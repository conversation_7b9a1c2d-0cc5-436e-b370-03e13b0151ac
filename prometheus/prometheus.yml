# Prometheus configuration for Aptio platform

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# Scrape configurations
scrape_configs:
  # Self monitoring
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # Backend API monitoring
  - job_name: "backend"
    metrics_path: /metrics
    scrape_interval: 10s
    static_configs:
      - targets: ["backend:3000"]

  # Frontend monitoring
  - job_name: "frontend"
    metrics_path: /metrics
    scrape_interval: 10s
    static_configs:
      - targets: ["frontend:3001"]

  # Node exporter for host metrics
  - job_name: "node"
    static_configs:
      - targets: ["node-exporter:9100"]

  # Nginx exporter
  - job_name: "nginx"
    static_configs:
      - targets: ["nginx-exporter:9113"]

  # Redis exporter
  - job_name: "redis"
    static_configs:
      - targets: ["redis-exporter:9121"]

  # PostgreSQL exporter
  - job_name: "postgres"
    static_configs:
      - targets: ["postgres-exporter:9187"]
