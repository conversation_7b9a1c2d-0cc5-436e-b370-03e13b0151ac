# Multi-stage build for B2B2B SaaS Platform for Contract Lifecycle Management
# Stage 1: Development/Build dependencies
FROM node:18-alpine AS deps
WORKDIR /app

# Install dependencies for backend and frontend
COPY package.json ./
COPY backend/package.json ./backend/
COPY frontend/package.json ./frontend/

# Install all dependencies, including dev dependencies
RUN npm install -g npm@latest && \
    npm install && \
    npm run setup

# Stage 2: Backend Builder
FROM node:18-alpine AS backend-builder
WORKDIR /app

# Copy backend source code and required configuration
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/backend/node_modules ./backend/node_modules
COPY package.json ./
COPY tsconfig.json ./
COPY backend ./backend
COPY src ./src

# Build backend
WORKDIR /app/backend
RUN npm run build

# Stage 3: Frontend Builder
FROM node:18-alpine AS frontend-builder
WORKDIR /app

# Copy frontend source code and required configuration
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules
COPY package.json ./
COPY tsconfig.json ./
COPY frontend ./frontend

# Build frontend
WORKDIR /app/frontend
RUN npm run build

# Stage 4: Production image
FROM node:18-alpine AS production
WORKDIR /app

# Set environment to production
ENV NODE_ENV=production

# Copy built artifacts
COPY package.json ./
COPY --from=backend-builder /app/backend/dist ./backend/dist
COPY --from=backend-builder /app/backend/package.json ./backend/
COPY --from=frontend-builder /app/frontend/.next ./frontend/.next
COPY --from=frontend-builder /app/frontend/package.json ./frontend/
COPY --from=frontend-builder /app/frontend/public ./frontend/public

# Install only production dependencies
RUN npm install -g npm@latest && \
    npm install --production && \
    cd backend && npm install --production && \
    cd ../frontend && npm install --production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app
USER nodejs

# Set health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD wget -qO- http://localhost:$PORT/api/health || exit 1

# Expose ports
EXPOSE 3000 3001

# Run application
CMD ["npm", "start"]