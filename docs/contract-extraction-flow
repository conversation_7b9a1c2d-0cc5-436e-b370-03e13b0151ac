Main Schema: ContractExtraction

Top-Level Header Fields

title: Contract title (string)

provider: Service or product provider (string)

client: Client or customer name (string)

contract_id: Unique identifier for the contract (string)

effective_date: When the contract starts (string)

end_date: When the contract expires (string)

total_amount: Total contract value (float)

currency: Currency code (string)

payment_terms: Payment terms summary (string)

governing_law: Jurisdiction (e.g., "Dutch", "US-NY") (string)

agreement_type: Type of agreement (string)

vendor_name: Optional alternate vendor name (string)

customer_name: Optional alternate customer name (string)

renewal_terms: List of renewal terms (array of RenewalTerm objects)

line_items: Contract line items (array of LineItem objects)

signatures: Signature information (array of SignatureBlock objects)

Nested Models

RenewalTerm

auto_renewal: "Yes", "No", or "N/A" (literal)

notice_period: Notice period for renewal (string)

renewal_term: Duration of renewal (string)

pricing_conditionality: Pricing conditions for renewal (string)

LineItem

description: Description of item/service (string)

amount: Cost (float)

details: Optional breakdown (array of strings)

SignatureBlock

party: "Provider", "Client", or "Other" (literal)

company: Company name (string)

name: Signatory name (string)

title: Signatory title (string)

date: Signature date (string)

Liability

capping: Liability cap description (string)

exclusions: Liability exclusions (string)

PaymentTermsDetailed

timeline: "<30 days", "30-60 days", or ">60 days" (literal)

delayed_payment_charges: Charges for late payment (string)

right_to_invoice: "Advance", "Upon completion", or "Others" (literal)

SLA (Service Level Agreement)

availability: ">99.5%", "95-99.5%", or "<95%" (literal)

response_time_p1: "<1 hr", "1-2 hrs", or ">2 hrs" (literal)

response_time_p2: "<2 hrs", "2-4 hrs", or ">4 hrs" (literal)

resolution_time_p1: "<4 hrs", "4-8 hrs", or ">8 hrs" (literal)

resolution_time_p2: "<2 hrs", "2-4 hrs", or ">4 hrs" (literal)

Termination

t4c: Termination for convenience: "Yes", "No", or "N/A" (literal)

notice_t4c: Notice period: "<=90 days", ">90 days", or "N/A" (literal)

ContractValue

acv: Annual contract value: "<100.000", "100.000-500.000", "500.000-2.000.000", or ">2.000.000" (literal)

Licensing

license_type: "Perpetual", "Subscription", or "Others" (literal)

Rights

transfer_rights: "Yes, affiliates included", "Yes, affiliates not included", "No", or "Others" (literal)

Indemnification

third_party_claims: Third-party claim provisions (string)

DataPrivacy

compliance: "GDPR", "Others", or "None" (literal)

TrueUp

frequency: "Once every year", "Once during the entire tenure", or "Others" (literal)

ServiceCredits

capping: "<=5%", "5%-10%", ">10%", or "Uncapped" (literal)

Governance

governance_type: "Defined" or "not defined" (literal)

ReportingObligations

consumption: "Yes" or "No" (literal)

IPR (Intellectual Property Rights)

custom_developments: "Vests with customer" or "Vests with supplier" (literal)

DataBreach

reporting_time: "upto 24 hrs", ">24 hrs", or "not defined" (literal)

Dashboard Schemas

DashboardRaw

auto_renewal: "N", "1Y", "2Y", "3Y", or "Other" (literal)

price_uplift: Price increase terms, e.g., "2%", "CPI" (optional string)

rebate_pct: Rebate percentage or "N" for none (optional string)

DashboardScorecard

price_benchmark: Price benchmark evaluation: "Green", "Amber", "Red", or empty (string)

consolidation: Consolidation evaluation: "Green", "Red", or empty (string)

consumption: Consumption evaluation: "Green", "Amber", "Red", or empty (string)

1. Contract Extraction Schema (Core Document Analysis)

Primary Data Structure

The ContractExtraction class is the main schema for extracted contract data, including:

1.1 Top-Level Metadata Fields

title: Official contract title

provider: Service/product provider company name

client: Customer/client company name

contract_id: Unique identifier or reference number

effective_date: Start date (YYYY-MM-DD format)

end_date: Expiration date (YYYY-MM-DD format)

total_amount: Numeric contract value without currency symbols

currency: Currency code (e.g., USD, EUR)

payment_terms: Plain text description of payment schedule

governing_law: Jurisdiction governing the agreement

agreement_type: Classification of contract type

vendor_name: Alternative vendor name if different from provider

customer_name: Alternative customer name if different from client

1.2 Document Classification Fields

These fields are added during processing:

contract_category: Category classification (e.g., "SW SAAS", "SI / Integration")

document_type: Type classification (e.g., "Master Subscription Agreement")

classification: Confidence and evidence data:

confidence: Confidence scores for categorization

category_confidence: 0.0-1.0 score for category classification

type_confidence: 0.0-1.0 score for document type classification

evidence: Textual justification for classification

category_evidence: Text explaining category classification

type_evidence: Text explaining document type classification

original_text: Extracted raw text from the document

date_added: Timestamp when the document was processed

source_filename: Original filename

1.3 Nested Data Structures

2.3 Scorecard Evaluation Rules

Price Benchmark Rule

Green: Price uplift ≤ 2%

Amber: Price uplift between 2-5%

Red: Price uplift > 5% or "CPI"

Empty: Not assessed

Consolidation Flag Rule

Green: Single vendor in category

Red: Multiple vendors in same category

Empty: Not assessed

Consumption Flag Rule

Green: Utilization ≥ 95%

Amber: Utilization between 80-95%

Red: Utilization < 80%

Empty: Not assessed

3. Document Processing Flow

Text Extraction: PDF, DOCX, or TXT files are processed using appropriate libraries (PyPDF2, etc.)

Document Classification: Documents are categorized by type and category with confidence scores

Structured Data Extraction: Gemini API extracts contract data according to schema definitions

Scorecard Analysis: Applied to extracted data based on evaluation rules

Storage: Saved as JSON files in the contracts directory

Retrieval & Display: Available through the web UI and API endpoints

This comprehensive schema definition supports the application's Agreement Analysis, search, and dashboard functionalities.

2.3 Scorecard Evaluation Rules

Price Benchmark Rule

Green: Price uplift ≤ 2%

Amber: Price uplift between 2-5%

Red: Price uplift > 5% or "CPI"

Empty: Not assessed

Consolidation Flag Rule

Green: Single vendor in category

Red: Multiple vendors in same category

Empty: Not assessed

Consumption Flag Rule

Green: Utilization ≥ 95%

Amber: Utilization between 80-95%

Red: Utilization < 80%

Empty: Not assessed

3. Document Processing Flow

Text Extraction: PDF, DOCX, or TXT files are processed using appropriate libraries (PyPDF2, etc.)

Document Classification: Documents are categorized by type and category with confidence scores

Structured Data Extraction: Gemini API extracts contract data according to schema definitions

Scorecard Analysis: Applied to extracted data based on evaluation rules

Storage: Saved as JSON files in the contracts directory

Retrieval & Display: Available through the web UI and API endpoints

This comprehensive schema definition supports the application's Agreement Analysis, search, and dashboard functionalities.

1. Document Type Taxonomy with Detailed Characteristics

The system uses a detailed taxonomy to classify contracts, with specific keywords and patterns for each document type:

Core Document Types

Master Service Agreement: Characterized by "governing law", "entire agreement", "term and termination", "liability cap", "force majeure", etc.

Statement of Work: Identified by "scope of work", "project milestones", "deliverables", "acceptance criteria", "project schedule", "roles and responsibilities".

Pricing Sheet: Contains "cost summary", "rate card", "fee schedule", "price per unit", "hourly rate", "unit cost", "extended price".

Order Form: Features "order number", "service order", "billing term", "quantity", "unit price", "initial term", "renewal term".

Purchase Order: Includes "po number", "po date", "vendor", "buyer", "ship to", "bill to", "payment terms".

Invoice: Characterized by "invoice number", "due date", "amount due", "balance", "tax id", "remit to", "bill to".

Price List: Contains "product code", "sku", "msrp", "public price", "tiered pricing", "volume discount".

Support Addendum: Features "service level", "response time", "resolution time", "support hours", "incident priority".

License Agreement: Includes "licensor", "licensee", "grant of license", "scope of license", "term of license".

Service Schedule: Contains "service tier", "service credits", "availability", "uptime", "response time", "maintenance window".

Generic Legal Forms

Data Processing Agreement: Identified by "dpa", "processor", "controller", "sub-processor", "gdpr", "annex", "standard contractual clauses".

End-User License Agreement: Features "eula", "license grant", "permitted uses", "restrictions", "software license".

Unlimited License Agreement: Contains "ula", "certification", "deployment report", "license audit", "support renewal".

Non-Disclosure Agreement: Characterized by "nda", "confidential information", "disclosing party", "receiving party", "carve-outs".

Publisher-Specific Forms

Microsoft Enterprise Agreement: Includes "microsoft ea", "enrollment", "true up", "server and cloud enrollment", "volume licensing".

Microsoft Products and Services Agreement: Features "mpsa", "purchasing account", "non-expiring agreement", "volume licensing".

SAP Subscription Order Form: Contains "cloud services", "s/4hana cloud", "sold-to party", "service description", "entitlement".

Oracle Master Agreement: Includes "oma", "ordering document", "five-year term", "technical support policies", "license definitions".

Oracle Unlimited License Agreement: Features "oracle ula", "ula certification", "deployment report", "ula exit", "license audit".

Adobe Enterprise Term License Agreement: Contains "etla", "adobe etla", "creative cloud", "standardize for 3-year period".

Salesforce Master Subscription Agreement: Includes "salesforce msa", "service cloud", "sales cloud", "trust.salesforce.com", "api limit".

2. Contract Category Taxonomy with Specific Indicators

The system classifies contracts into these broad categories, each with its own signature terminology:

Specialist: Identified by "subject matter expert", "consultant", "consultancy", "advisory", "assessment", "audit", "health check".

SI / Integration: Features "system integration", "implementation", "data migration", "cutover", "configuration", "rollout".

Development: Contains "custom development", "software build", "coding sprint", "feature enhancement", "agile backlog".

Support: Characterized by "maintenance", "ticket", "help desk", "incident", "service request", "knowledge base".

SW SAAS: Includes "software as a service", "subscription", "tenant", "instance", "usage fee", "api call", "monthly active", "seat license".

Cloud / Infra: Features "infrastructure as a service", "compute", "virtual machine", "instance hour", "storage bucket".

Break Fix: Contains "on-site repair", "hardware replacement", "incident restore", "field engineer", "dispatch".

Hardware: Characterized by "equipment", "appliance", "device", "hardware unit", "sku", "chassis", "rack", "blade".

3. Scorecard Evaluation Rules with Detailed Criteria

The system applies specific evaluation rules to generate performance scorecards:

Price Benchmark Rule (with thresholds)

Green: Price uplift ≤ 2% (0.02 decimal)

Amber: Price uplift between 2-5% (0.02-0.05 decimal)

Red: Price uplift > 5% (>0.05 decimal) or contains "CPI" (special trigger phrase)

Empty: Not assessed (if benchmark_active=False)

Consolidation Flag Rule (with methodology)

Green: Single vendor in a category (based on spend_map)

Red: Multiple vendors in the same category (vendor fragmentation indicator)

Empty: Not assessed (if assessed=False or spend_map=None)

Consumption Flag Rule (with specific utilization thresholds)

Green: Utilization ≥ 95% (0.95 decimal) - optimal usage

Amber: Utilization between 80-95% (0.80-0.95 decimal) - moderate underuse

Red: Utilization < 80% (< 0.80 decimal) - significant underuse

Empty: Not assessed (if assessed=False or utilization=None)

4. Detailed Classification Methodology

The system uses a sophisticated multi-step approach to classify documents:

Primary AI Classification: Uses Gemini API with a detailed prompt that analyzes document structure and content.

Confidence Scoring: Each classification includes confidence metrics (0.0-1.0) for transparency.

Evidence Collection: The system extracts specific text snippets that support the classification.

Fallback Mechanism: If AI classification fails, the system uses keyword frequency analysis:

Matches document text against predefined keyword lists

Scores each category/type based on keyword occurrences

Calculates confidence scores based on match strength (0.3-0.9 range)

Documents matching keywords that influenced the classification

1. Document Type Taxonomy with Detailed Characteristics

The system uses a detailed taxonomy to classify contracts, with specific keywords and patterns for each document type:

Core Document Types

Master Service Agreement: Characterized by "governing law", "entire agreement", "term and termination", "liability cap", "force majeure", etc.

Statement of Work: Identified by "scope of work", "project milestones", "deliverables", "acceptance criteria", "project schedule", "roles and responsibilities".

Pricing Sheet: Contains "cost summary", "rate card", "fee schedule", "price per unit", "hourly rate", "unit cost", "extended price".

Order Form: Features "order number", "service order", "billing term", "quantity", "unit price", "initial term", "renewal term".

Purchase Order: Includes "po number", "po date", "vendor", "buyer", "ship to", "bill to", "payment terms".

Invoice: Characterized by "invoice number", "due date", "amount due", "balance", "tax id", "remit to", "bill to".

Price List: Contains "product code", "sku", "msrp", "public price", "tiered pricing", "volume discount".

Support Addendum: Features "service level", "response time", "resolution time", "support hours", "incident priority".

License Agreement: Includes "licensor", "licensee", "grant of license", "scope of license", "term of license".

Service Schedule: Contains "service tier", "service credits", "availability", "uptime", "response time", "maintenance window".

Generic Legal Forms

Data Processing Agreement: Identified by "dpa", "processor", "controller", "sub-processor", "gdpr", "annex", "standard contractual clauses".

End-User License Agreement: Features "eula", "license grant", "permitted uses", "restrictions", "software license".

Unlimited License Agreement: Contains "ula", "certification", "deployment report", "license audit", "support renewal".

Non-Disclosure Agreement: Characterized by "nda", "confidential information", "disclosing party", "receiving party", "carve-outs".

Publisher-Specific Forms

Microsoft Enterprise Agreement: Includes "microsoft ea", "enrollment", "true up", "server and cloud enrollment", "volume licensing".

Microsoft Products and Services Agreement: Features "mpsa", "purchasing account", "non-expiring agreement", "volume licensing".

SAP Subscription Order Form: Contains "cloud services", "s/4hana cloud", "sold-to party", "service description", "entitlement".

Oracle Master Agreement: Includes "oma", "ordering document", "five-year term", "technical support policies", "license definitions".

Oracle Unlimited License Agreement: Features "oracle ula", "ula certification", "deployment report", "ula exit", "license audit".

Adobe Enterprise Term License Agreement: Contains "etla", "adobe etla", "creative cloud", "standardize for 3-year period".

Salesforce Master Subscription Agreement: Includes "salesforce msa", "service cloud", "sales cloud", "trust.salesforce.com", "api limit".

2. Contract Category Taxonomy with Specific Indicators

The system classifies contracts into these broad categories, each with its own signature terminology:

Specialist: Identified by "subject matter expert", "consultant", "consultancy", "advisory", "assessment", "audit", "health check".

SI / Integration: Features "system integration", "implementation", "data migration", "cutover", "configuration", "rollout".

Development: Contains "custom development", "software build", "coding sprint", "feature enhancement", "agile backlog".

Support: Characterized by "maintenance", "ticket", "help desk", "incident", "service request", "knowledge base".

SW SAAS: Includes "software as a service", "subscription", "tenant", "instance", "usage fee", "api call", "monthly active", "seat license".

Cloud / Infra: Features "infrastructure as a service", "compute", "virtual machine", "instance hour", "storage bucket".

Break Fix: Contains "on-site repair", "hardware replacement", "incident restore", "field engineer", "dispatch".

Hardware: Characterized by "equipment", "appliance", "device", "hardware unit", "sku", "chassis", "rack", "blade".

3. Scorecard Evaluation Rules with Detailed Criteria

The system applies specific evaluation rules to generate performance scorecards:

Price Benchmark Rule (with thresholds)

Green: Price uplift ≤ 2% (0.02 decimal)

Amber: Price uplift between 2-5% (0.02-0.05 decimal)

Red: Price uplift > 5% (>0.05 decimal) or contains "CPI" (special trigger phrase)

Empty: Not assessed (if benchmark_active=False)

Consolidation Flag Rule (with methodology)

Green: Single vendor in a category (based on spend_map)

Red: Multiple vendors in the same category (vendor fragmentation indicator)

Empty: Not assessed (if assessed=False or spend_map=None)

Consumption Flag Rule (with specific utilization thresholds)

Green: Utilization ≥ 95% (0.95 decimal) - optimal usage

Amber: Utilization between 80-95% (0.80-0.95 decimal) - moderate underuse

Red: Utilization < 80% (< 0.80 decimal) - significant underuse

Empty: Not assessed (if assessed=False or utilization=None)

4. Detailed Classification Methodology

The system uses a sophisticated multi-step approach to classify documents:

Primary AI Classification: Uses Gemini API with a detailed prompt that analyzes document structure and content.

Confidence Scoring: Each classification includes confidence metrics (0.0-1.0) for transparency.

Evidence Collection: The system extracts specific text snippets that support the classification.

Fallback Mechanism: If AI classification fails, the system uses keyword frequency analysis:

Matches document text against predefined keyword lists

Scores each category/type based on keyword occurrences

Calculates confidence scores based on match strength (0.3-0.9 range)

Documents matching keywords that influenced the classification

Amine Safi (External) was invited to the meeting.

Copy of Product Demo Video in Green Blue Cool Corporate Style (1).mp4

Copy of Product Demo Video in Green Blue Cool Corporate Style (1).mp4

Scipt which we used for now "Unlock the Future of Contract Management with MAIT

"Discover. Optimize. Renew — All in One Place."

Contract chaos is draining your resources. 71% of companies can't locate 10% or more of their contracts — and that's just the tip of the iceberg. Are you ready to take control?

Introducing MAIT, your 24/7 AI-powered contract concierge — designed to eliminate the inefficiencies and frustrations that come with traditional contract management.

Why MAIT?

Effortless Discovery: Automate contract discovery with advanced OCR and metadata extraction.

Real-Time Intelligence: Track performance with intelligent KPI dashboards that give you insights at a glance.

Always-On Assistant: Ask MAIT, and instantly discover, track, and take action on contracts across your entire portfolio.

Proactive Alerts: Receive renewal notifications before the risks catch up to you.

Zero Blind Spots: See everything — no more searching for missing contracts.

97% of contract professionals struggle to locate contracts. Don’t be part of that statistic. MAIT unifies all your contract insights, giving you the intelligence to make informed decisions, faster.

MAIT — Automate. Optimize. Renew.
Your AI-powered contract assistant, always one step ahead."

Himanshu Gautam was invited to the meeting.

12:18 PM Meeting ended: 51m 59s

import lexnlp.extract.en.dates
import lexnlp.extract.en.money
import lexnlp.extract.en.percents
import lexnlp.extract.en.citations
import lexnlp.extract.en.entities.nltk_re as lexnlp_entities # for companies, etc.

def extract_legal_metadata(text: str) -> dict:
metadata = {} # Dates
dates = list(lexnlp.extract.en.dates.get_dates(text))
metadata["dates"] = dates # Monetary amounts (returns tuples like (amount, currency) or just amounts)
money = list(lexnlp.extract.en.money.get_money(text))
metadata["money"] = money # Percentages
percents = list(lexnlp.extract.en.percents.get_percents(text))
metadata["percents"] = percents # Legal citations (court cases, statutes, etc.)
citations = list(lexnlp.extract.en.citations.get_citations(text))
metadata["citations"] = citations # Companies/Organizations (uses a regex-based NER)
companies = list(lexnlp_entities.get_companies(text))
metadata["companies"] = [comp[0] for comp in companies] # comp is a tuple (name, type)
return metadata

prompt: Your purpose is to help procurement and IT professionals analyze contracts, proposals, and related documents with precision and clarity. You operate with a deep understanding of legal terminology, procurement practices, and the specifics of IT vendor agreements.

Role and Domain Expertise

•  Domain Knowledge: You act as a virtual contract analyst with expertise in contract law, IT procurement, and vendor management. You understand typical contract structures (Master Service Agreements, SLAs, NDAs, etc.), legal clauses (termination, liability, data privacy, indemnification), and procurement concepts (RFPs, pricing models, service levels).

•  Legal & Procurement Language: You are fluent in legal and business terminology. You can interpret complex contract language and translate it into clear insights. This includes understanding obligations, performance metrics, compliance requirements, and industry benchmarks common in IT sourcing agreements.

•  Enterprise Context: You recognize the needs of enterprise users – focusing on risk, compliance, value for money, and adherence to company policies. Your responses take into account the perspective of procurement and IT roles, providing information that supports decision-making and contract governance.

Primary Capabilities

1.  Detailed Q&A on Contracts: Answer specific, detailed questions about contracts stored in the backend repository. Retrieve relevant clauses or data from the documents and provide precise answers. (For example, you can find what a termination clause says in Contract A, or identify the expiration date and renewal terms for Contract B.)

2.  Contract & Proposal Evaluation: Evaluate contracts or vendor proposals against standard templates, pricing benchmarks, and defined criteria. Identify where the document aligns with expectations and where it deviates. Highlight compliance with or deviations from company standards (e.g., missing required clauses, unusual pricing terms, or risks and liabilities that exceed benchmarks).

3.  Clause Summarization Across Documents: Summarize the content of specific clauses or topics across multiple documents for analytics and compliance. For instance, you can review all data privacy clauses across a set of vendor contracts and provide a summary of each or an overall analysis of how they compare, noting similarities, differences, and any outliers.

4.  Invoice Reconciliation: Cross-check and reconcile invoices against contract terms. Identify the pricing and commercial terms in the contract (rates, fees, discounts, payment schedules) and compare them to actual billing information from an invoice. Flag any discrepancies such as overcharges, missing discounts, or items not covered by the contract.

Tools & Resources

•  Access to Contract Repository (RAG): You have access to a backend repository of contracts, proposals, templates, and other relevant documents. Utilize a Retrieval-Augmented Generation system (SimpleRAG) to fetch the most relevant document sections needed to answer a query. Always ground your responses in this retrieved content to ensure accuracy and specificity.

•  Legal NLP (LexNLP): You are equipped with legal text processing capabilities (e.g., LexNLP) to extract structured data from contracts. You can identify and pull out key details such as party names, effective dates, contract values, pricing terms, renewal dates, termination notice periods, indemnity limits, and other structured elements. Leverage this to provide precise answers (for example, listing the key financial terms of a contract) or to support comparisons.

•  Multi-Document Analysis: You can handle and correlate information from multiple documents. For example, you may retrieve clauses from several contracts to compare their contents, or use content from both an invoice and a contract to perform reconciliation. You are capable of merging insights from various sources in a coherent answer.

•  Templates & Benchmarks: You have knowledge of standard contract templates, common industry benchmarks, and internal compliance checklists (when provided). This allows you to compare an actual contract or proposal to the expected standard. Use this knowledge to point out deviations (e.g., “the service credit percentage is lower than the typical benchmark”) and to ensure evaluations are contextually informed.

Communication Style

•  Formal and Professional Tone: Communicate in a polite, formal tone appropriate for business communications. Your audience is typically procurement officers, contract managers, or IT executives, so maintain professionalism and respect in all responses.

•  Clarity and Conciseness: Provide clear explanations and avoid unnecessary jargon. If legal or technical terms are used from the contract, explain them briefly in simpler terms if there’s any chance of misunderstanding. Keep sentences and paragraphs concise (avoid wall-of-text replies) – break complex information into smaller, digestible parts.

•  Structured Responses: Organize your answers for readability. Use headings, bullet points, or numbered lists to structure the information when addressing complex queries or multiple points. For example, if comparing a contract to a template across several criteria, you might list each criterion (Pricing, Termination, SLA, etc.) as a bullet point with findings. This structured approach helps users quickly grasp the key points.

•  Legally-Informed Wording: Use correct legal and contractual terminology (e.g., refer to sections as “clauses” or “Sections” with numbers if known, use terms like “governing law,” “force majeure,” etc. appropriately). Ensure that any paraphrasing of contract language remains true to the original meaning. When summarizing, preserve any important conditions or exceptions from the source text.

•  Neutral and Objective: Present information and analyses objectively, without personal bias. Even when evaluating or identifying issues, use a factual tone (e.g., “Clause 5.2 deviates from the standard template by omitting X…” rather than emotive language). Do not take sides or make judgments outside the provided criteria—stick to the facts and the content of the documents.

Interaction Guidelines

•  Clarify When Necessary: If a user’s request is ambiguous or lacks detail, ask polite clarifying questions. For example, if the user says “Compare this to the standard contract,” and it’s not clear which standard or which document to compare, you should ask for clarification. Only proceed once you are sure what the user needs.

•  Step-by-Step Reasoning: For complex tasks, approach them stepwise and transparently. If asked to perform a complex analysis (such as reconciling an invoice with a contract), it’s acceptable to break down the task: first identify relevant contract terms, then examine invoice items, then compare. You can even briefly outline your approach in the answer so the user follows your logic.

•  Focus on the User’s Query: Always tailor your output to exactly what the user asks. If the question is asking for a summary, do not veer into evaluation or vice versa. Address all parts of a multi-part question methodically. If the user asks for a comparison, ensure you cover both similarities and differences as requested.

•  Summarization: When summarizing documents or clauses, distill the content to the most important points. Include key facts such as dates, figures, obligations, and any unique conditions. Ensure that the summary is accurate and does not omit critical details that could change the meaning. For instance, if a limitation or exception is present in a clause, note it in the summary.

•  Evaluation & Comparison: When evaluating a contract or proposal against criteria (or a template/benchmark), structure your answer by each criterion. Clearly indicate whether each criterion is Met, Partially Met, or Not Met (if this applies), and provide a brief explanation with references to the document. For comparisons, you might say, “Pricing: The proposed rate is $X, which is higher than our benchmark of $Y. Termination: The contract’s termination clause requires 90 days notice, whereas our standard is 60 days,” and so on.

•  Invoice Reconciliation: When checking invoices against contracts, list findings clearly. For each discrepancy or match, explicitly reference the contract term and the invoice detail. For example: “- License Fee: Contract states $100/license, invoice charges $110/license (Discrepancy: overcharge of $10 per license).” Be thorough in checking all items, taxes, discounts, and terms like payment due dates if relevant. Highlight any compliance issues (e.g., billing for a service not covered by the contract).

•  Ground Answers in Documents: Always base your answers on the content you have retrieved or know from the user’s documents. If a user asks about a specific clause, your answer should reference that clause’s content. Use the retrieved text to back up your explanations (e.g., quote a key phrase or cite a section number). Do not speculate or fabricate information – if the answer is not found in the documents or your integrated knowledge base, inform the user of this.

•  Handling Uncertainty: If you cannot find the answer in the provided data or it falls outside your scope, respond transparently. It’s better to say, “The contract does not specify X,” or “I don’t have information on Y,” than to guess. You may also suggest the user provide additional information or confirm if you should proceed with available assumptions. Always aim to be helpful, but truthful and accurate above all.

# === MAIT – Agreement Analysis Assistant (System Prompt) ===

## Role & Expertise

You are **MAI**, MAIT’s AI assistant for enterprise IT-sourcing contracts.  
 – Expert in IT procurement and contract law: MSAs, SLAs, NDAs, DPAs, indemnities, pricing models, service-level metrics.  
 – Skilled in risk assessment and compliance; can compare agreements to templates or industry benchmarks.

## Tone & Style

Formal, professional, neutral.  
 Write clearly and concisely; avoid jargon unless needed and define legal terms briefly when first used.

## Response Rules

1.  **Ground everything in the provided <context>.** Quote or reference clauses/sections when relevant.
2.  **No hallucination.** If information is missing, say “Information not found.” Do not guess.
3.  **Structure answers.** Use headings, bullets, or numbered lists so users can scan key points quickly.
4.  **Accurate clause summaries.** Preserve conditions, exclusions, caps, timeframes, and obligations.
5.  **Objective comparisons.** When asked to compare vs. a template/benchmark, list each criterion → Met / Partially Met / Not Met, with concise facts.
6.  **Neutral tone.** State risks or deviations factually (e.g., “Clause 5.2 omits a liability cap, which increases exposure”). Avoid unsupported opinions.
7.  **Explain multi-step reasoning briefly.** Outline steps for tasks like invoice reconciliation, then present results.
8.  **Stay within scope.** Focus on IT-vendor contracts, proposals, invoices, and related documents.

Respond in professional English. Keep answers precise, well-structured, and grounded in the contract text.

# backend/schemas/contract_extraction.py

from pydantic import BaseModel, Field
from typing import Literal, Optional

class Liability(BaseModel):
capping: str = Field(..., description="Describe the liability capping (e.g. '100% of contract value of last 12 months').")
exclusions: str = Field(..., description="List any liability exclusions (e.g. 'Special, indirect and consequential damages').")

class PaymentTerms(BaseModel):
timeline: Literal["<30 days", "30-60 days", ">60 days"]
delayed_payment_charges: str # e.g. "<1% p.m", ">1% p.m", "None"
right_to_invoice: Literal["Advance", "Upon completion", "Others"]

class SLA(BaseModel):
availability: Literal[">99.5%", "95-99.5%", "<95%"]
response_time_p1: Literal["<1 hr", "1-2 hrs", ">2 hrs"]
response_time_p2: Literal["<2 hrs", "2-4 hrs", ">4 hrs"]
resolution_time_p1: Literal["<4 hrs", "4-8 hrs", ">8 hrs"]
resolution_time_p2: Literal["<2 hrs", "2-4 hrs", ">4 hrs"]

class Termination(BaseModel):
t4c: Literal["Yes", "No", "N/A"] = Field(..., description="Termination for convenience applicable?")
notice_t4c: Literal["<=90 days", ">90 days", "N/A"]

class ContractValue(BaseModel):
acv: Literal["<100.000", "100.000-500.000", "500.000-2.000.000", ">2.000.000"]

class Licensing(BaseModel):
license_type: Literal["Perpetual", "Subscription", "Others"]

class Rights(BaseModel):
transfer_rights: Literal["Yes, affiliates included",
"Yes, affiliates not included",
"No", "Others"]

class Indemnification(BaseModel):
third_party_claims: str # e.g. "Yes, uncapped", "Yes, capped", "No"

class DataPrivacy(BaseModel):
compliance: Literal["GDPR", "Others", "None"]

class TrueUp(BaseModel):
frequency: Literal["Once every year", "Once during the entire tenure", "Others"]

class ServiceCredits(BaseModel):
capping: Literal["<=5%", "5%-10%", ">10%", "Uncapped"]

class Governance(BaseModel):
governance_type: Literal["Defined", "not defined"]

class ReportingObligations(BaseModel):
consumption: Literal["Yes", "No"]

class IPR(BaseModel):
custom_developments: Literal["Vests with customer", "Vests with supplier"]

class DataBreach(BaseModel):
reporting_time: Literal["upto 24 hrs", ">24 hrs", "not defined"]

class ContractExtraction(BaseModel):
provider: str
liability: Liability
payment_terms: PaymentTerms
sla: SLA
governing_law: Literal["Yes", "No"]
termination: Termination
contract_value: ContractValue
licensing: Licensing
rights: Rights
indemnification: Indemnification
data_privacy: DataPrivacy
true_up_down: TrueUp
service_credits: ServiceCredits
governance: Governance
reporting_obligations: ReportingObligations
ipr: IPR
data_breach: DataBreach

{
"provider": "Acme Inc.",
"liability": {
"capping": "100% of contract value of last 12 months",
"exclusions": "Special, indirect and consequential damages"
},
"payment_terms": {
"timeline": "<30 days",
"delayed_payment_charges": "<1% p.m",
"right_to_invoice": "Advance"
},
"sla": {
"availability": ">99.5%",
"response_time_p1": "<1 hr",
"response_time_p2": "<2 hrs",
"resolution_time_p1": "<4 hrs",
"resolution_time_p2": "<2 hrs"
},
"governing_law": "Yes",
"termination": {
"t4c": "Yes",
"notice_t4c": "<=90 days"
},
"contract_value": { "acv": "<100.000" },
"licensing": { "license_type": "Perpetual" },
"rights": { "transfer_rights": "Yes, affiliates included" },
"indemnification": { "third_party_claims": "Yes, uncapped" },
"data_privacy": { "compliance": "GDPR" },
"true_up_down": { "frequency": "Once every year" },
"service_credits": { "capping": "<=5%" },
"governance": { "governance_type": "Defined" },
"reporting_obligations": { "consumption": "Yes" },
"ipr": { "custom_developments": "Vests with customer" },
"data_breach": { "reporting_time": "upto 24 hrs" }
}

# backend/schemas/contract_extraction.py

from pydantic import BaseModel, Field
from typing import Literal, Optional, List

# ---------- TOP-LEVEL HEADER FIELDS ----------

class RenewalTerm(BaseModel):
auto_renewal: Literal["Yes", "No", "N/A"]
notice_period: str
renewal_term: str
pricing_conditionality: str

class LineItem(BaseModel):
description: str
amount: float
details: Optional[List[str]] = Field(
default=None,
description="Optional sub-details or breakdowns for the item/service."
)

class SignatureBlock(BaseModel):
party: Literal["Provider", "Client", "Other"]
company: str
name: str
title: str
date: str

# ---------- EXISTING NESTED MODELS ----------

class Liability(BaseModel):
capping: str
exclusions: str

class PaymentTermsDetailed(BaseModel): # renamed to avoid clash with header field
timeline: Literal["<30 days", "30-60 days", ">60 days"]
delayed_payment_charges: str
right_to_invoice: Literal["Advance", "Upon completion", "Others"]

class SLA(BaseModel):
availability: Literal[">99.5%", "95-99.5%", "<95%"]
response_time_p1: Literal["<1 hr", "1-2 hrs", ">2 hrs"]
response_time_p2: Literal["<2 hrs", "2-4 hrs", ">4 hrs"]
resolution_time_p1: Literal["<4 hrs", "4-8 hrs", ">8 hrs"]
resolution_time_p2: Literal["<2 hrs", "2-4 hrs", ">4 hrs"]

class Termination(BaseModel):
t4c: Literal["Yes", "No", "N/A"]
notice_t4c: Literal["<=90 days", ">90 days", "N/A"]

class ContractValue(BaseModel):
acv: Literal["<100.000", "100.000-500.000", "500.000-2.000.000", ">2.000.000"]

class Licensing(BaseModel):
license_type: Literal["Perpetual", "Subscription", "Others"]

class Rights(BaseModel):
transfer_rights: Literal[
"Yes, affiliates included",
"Yes, affiliates not included",
"No",
"Others"
]

class Indemnification(BaseModel):
third_party_claims: str

class DataPrivacy(BaseModel):
compliance: Literal["GDPR", "Others", "None"]

class TrueUp(BaseModel):
frequency: Literal[
"Once every year",
"Once during the entire tenure",
"Others"
]

class ServiceCredits(BaseModel):
capping: Literal["<=5%", "5%-10%", ">10%", "Uncapped"]

class Governance(BaseModel):
governance_type: Literal["Defined", "not defined"]

class ReportingObligations(BaseModel):
consumption: Literal["Yes", "No"]

class IPR(BaseModel):
custom_developments: Literal["Vests with customer", "Vests with supplier"]

class DataBreach(BaseModel):
reporting_time: Literal["upto 24 hrs", ">24 hrs", "not defined"]

# ---------- ROOT MODEL ----------

class ContractExtraction(BaseModel): # Header / metadata
title: str
provider: str
client: str
contract_id: str
effective_date: str
end_date: str
total_amount: float
currency: str
payment_terms: str # header-level plain text
governing_law: str # e.g. "Dutch", "US-NY"
agreement_type: str
vendor_name: Optional[str] = None
customer_name: Optional[str] = None
renewal_terms: List[RenewalTerm]
line_items: List[LineItem]
signatures: List[SignatureBlock]

    # Detailed clause blocks (unchanged except rename)
     liability: Liability
     payment_terms_detailed: PaymentTermsDetailed
     sla: SLA
     termination: Termination
     contract_value: ContractValue
     licensing: Licensing
     rights: Rights
     indemnification: Indemnification
     data_privacy: DataPrivacy
     true_up_down: TrueUp
     service_credits: ServiceCredits
     governance: Governance
     reporting_obligations: ReportingObligations
     ipr: IPR
     data_breach: DataBreach

{
"title": "Master Service Agreement – Cloud Hosting",
"provider": "Acme Cloud Ltd.",
"client": "GlobalRetail BV",
"contract_id": "MSA-2025-001",
"effective_date": "2025-01-15",
"end_date": "2028-01-14",
"total_amount": 480000,
"currency": "EUR",
"payment_terms": "<30 days from invoice date",
"governing_law": "Dutch",
"agreement_type": "MSA",
"vendor_name": "Acme Cloud Ltd.",
"customer_name": "GlobalRetail BV",
"renewal_terms": [
{
"auto_renewal": "Yes",
"notice_period": "60 days",
"renewal_term": "12 months",
"pricing_conditionality": "Max 3% uplift CPI"
}
],
"line_items": [
{
"description": "Annual hosting fee",
"amount": 160000
},
{
"description": "Premium support",
"amount": 20000,
"details": ["24/7 coverage", "Dedicated TAM"]
}
],
"signatures": [
{
"party": "Provider",
"company": "Acme Cloud Ltd.",
"name": "Jane Doe",
"title": "VP Sales",
"date": "2025-01-10"
},
{
"party": "Client",
"company": "GlobalRetail BV",
"name": "John Smith",
"title": "Procurement Director",
"date": "2025-01-12"
}
],
"...": "remaining nested blocks identical to earlier example"
}

# backend/classification/taxonomy.py

TAXONOMY = [
"Specialist",
"SI / Integration",
"Development",
"Support",
"SW SAAS",
"Cloud / Infra",
"Break Fix",
"Hardware",
]

# Simple keyword seeds to bootstrap heuristics

SEED_KEYWORDS = {
"Specialist": ["expert services", "consultant", "advisory"],
"SI / Integration": ["system integration", "systems integrator", "implementation"],
"Development": ["custom development", "agile sprint", "software build"],
"Support": ["maintenance", "support hours", "ticket", "help desk"],
"SW SAAS": ["software as a service", "subscription fee", "SaaS"],
"Cloud / Infra": ["IaaS", "compute", "storage", "AWS", "Azure", "GCP"],
"Break Fix": ["break/fix", "on-site repair", "incident"],
"Hardware": ["equipment", "appliance", "device", "hardware"],
}

# backend/classification/classifier.py

from pathlib import Path
import re, os
from collections import Counter
from typing import Tuple
from .taxonomy import TAXONOMY, SEED_KEYWORDS
from ..llm import ask_claude # helper we built earlier

\_PROMPT = Path("prompts/system_prompt.txt").read_text() + """

You are now performing a single task:
Classify the entire contract into ONE of these categories:
{taxonomy_list}

Return ONLY the category name. If unsure, return "Unknown".
"""

def \_heuristic(text: str) -> Tuple[str, float]:
"""Very fast keyword count. Returns (candidate, confidence 0-1)."""
text_lc = text.lower()
scores = Counter()
for cat, words in SEED_KEYWORDS.items():
scores[cat] = sum(text_lc.count(w) for w in words)
if not scores:
return "Unknown", 0.0
best, hits = scores.most_common(1)[0]
total = sum(scores.values())
conf = hits / total if total else 0.0
return best if conf >= 0.4 else "Unknown", conf

def classify_contract(text: str) -> str: # 1) Heuristic first
cat, conf = \_heuristic(text)
if cat != "Unknown":
return cat

    # 2) Fallback to Claude for edge cases
     llm_prompt = _PROMPT.format(taxonomy_list=", ".join(TAXONOMY))
     response = ask_claude(
         question="Which category?",
         context=text[:12000],      # keep <=12k tokens
         max_tokens=10,
         system_override=llm_prompt  # ask_claude() supports override
     )
     # Sanitize
     response = re.sub(r"[^A-Za-z/ ]", "", response).strip()
     return response if response in TAXONOMY else "Unknown"

# backend/classification/taxonomy.py

# ------------------------------------------------------------------

# Axis 1 – Contract Category

TAXONOMY = [
"Specialist",
"SI / Integration",
"Development",
"Support",
"SW SAAS",
"Cloud / Infra",
"Break Fix",
"Hardware",
]

SEED_KEYWORDS = {
"Specialist": [
"subject matter expert", "consultant", "consultancy", "advisory",
"assessment", "audit", "health check", "optimization", "training",
"workshop", "knowledge transfer", "expert services", "professional services",
"managed advisory", "strategy review", "process mapping",
"business analysis", "gap analysis", "best practice review", "roadmap"
],
"SI / Integration": [
"system integration", "systems integrator", "implementation",
"data migration", "cutover", "configuration", "rollout",
"go live", "integration testing", "interface build", "api integration",
"middleware", "onboarding", "deployment", "devops automation",
"change management", "work package", "phase delivery",
"technical design", "blueprint", "fit gap"
],
"Development": [
"custom development", "software build", "coding sprint",
"feature enhancement", "agile backlog", "user story", "commit",
"database schema", "unit test", "ci/cd", "version control",
"code review", "prototype", "minimum viable product", "dev sandbox",
"acceptance criteria", "merge request", "release note",
"bug fix", "velocity"
],
"Support": [
"support services", "maintenance", "ticket", "help desk",
"incident", "service request", "knowledge base", "service window",
"priority response", "patching", "root cause", "sla response",
"hotfix", "change ticket", "problem management", "24/7 coverage",
"severity", "resolution time", "remote support", "technical assistance"
],
"SW SAAS": [
"software as a service", "subscription", "tenant", "instance",
"usage fee", "api call", "monthly active", "seat license",
"uptime", "multi-tenant", "cloud console", "version upgrade",
"release cycle", "sandbox", "renewal", "user licence",
"dashboard", "web application", "overage", "data retention"
],
"Cloud / Infra": [
"infrastructure as a service", "compute", "virtual machine",
"instance hour", "storage bucket", "network egress", "vpc",
"load balancer", "kubernetes", "managed service", "autoscaling",
"object storage", "snapshot", "region", "availability zone",
"terraform", "cloud console", "iam", "resource quota", "billing account"
],
"Break Fix": [
"break/fix", "on-site repair", "hardware replacement",
"incident restore", "field engineer", "dispatch", "call-out",
"troubleshoot", "diagnostic", "repair time", "service truck",
"parts swap", "return merchandise authorization", "downtime",
"fault isolation", "service center", "refurbish", "replacement unit",
"escalation", "after-hours"
],
"Hardware": [
"equipment", "appliance", "device", "hardware unit",
"sku", "chassis", "rack", "blade", "vendor part number",
"serial number", "warranty", "spare parts", "firmware",
"data sheet", "power supply", "network port", "ssd", "cpu",
"memory module", "service tag"
],
}

# ------------------------------------------------------------------

# Axis 2 – Document Type

DOC_TYPES = [
"Master Service Agreement",
"Statement of Work",
"Pricing Sheet",
"Order Form",
"Purchase Order",
"Invoice",
"Price List",
"Support Addendum",
"License Agreement",
"Service Schedule",
]

DOC_TYPE_KEYWORDS = {
"Master Service Agreement": [
"master service agreement", "this msa", "m s a", "agreement number",
"governing law", "entire agreement", "term and termination",
"liability cap", "force majeure", "confidentiality", "definitions",
"order form attached", "statement of work", "scope of services",
"relationship of the parties", "notices", "assignment",
"severability", "waiver", "counterparts", "signature block"
],
"Statement of Work": [
"statement of work", "sow", "scope of work", "project milestones",
"deliverables", "acceptance criteria", "project schedule",
"roles and responsibilities", "in scope", "out of scope",
"payment schedule", "resource plan", "change request",
"assumptions", "project manager", "kickoff", "timeline",
"go live", "project close", "location of services"
],
"Pricing Sheet": [
"pricing sheet", "cost summary", "rate card", "fee schedule",
"price per unit", "hourly rate", "unit cost", "extended price",
"subtotal", "discount", "tax", "one-time fee", "recurring fee",
"cost breakdown", "bill of materials", "bom", "volume tier",
"sku", "list price", "net price", "currency"
],
"Order Form": [
"order form", "order number", "service order", "order effective date",
"billing term", "quantity", "unit price", "initial term",
"renewal term", "customer signature", "provider signature",
"service description", "subscription plan", "support tier",
"payment due", "purchase order reference", "upfront charge",
"recurring charge", "implementation fee", "contract id"
],
"Purchase Order": [
"purchase order", "po number", "po date", "vendor", "buyer",
"ship to", "bill to", "payment terms", "net 30", "net 60",
"line item", "qty", "uom", "unit price", "extended amount",
"incoterms", "delivery date", "freight", "approval",
"purchase requisition", "receiving"
],
"Invoice": [
"invoice", "invoice number", "invoice date", "due date",
"amount due", "balance", "tax id", "remit to", "bill to",
"po reference", "account balance", "late fee", "subtotal",
"vat", "gst", "line item", "hours", "rate", "description",
"total", "terms"
],
"Price List": [
"price list", "list price", "product code", "sku", "msrp",
"public price", "tiered pricing", "volume discount",
"valid through", "effective date", "updated price",
"currency", "unit price", "support fee", "maintenance fee",
"upgrade price", "license fee", "subscription fee", "setup fee", "training fee"
],
"Support Addendum": [
"support addendum", "support agreement", "maintenance addendum",
"service level", "response time", "resolution time",
"support hours", "incident priority", "support window",
"contact method", "escalation", "technical support",
"software updates", "patches", "hotfixes", "support fee",
"support term", "knowledge base", "ticket", "sla"
],
"License Agreement": [
"license agreement", "licensor", "licensee", "grant of license",
"scope of license", "term of license", "perpetual license",
"subscription license", "royalty", "license fee", "intellectual property",
"restrictions", "audit rights", "termination", "confidential information",
"benchmarks", "export control", "governing law", "warranty disclaimer", "indemnification"
],
"Service Schedule": [
"service schedule", "services schedule", "service description",
"service tier", "service credits", "availability", "uptime",
"response time", "maintenance window", "service desk",
"escalation procedure", "scheduled downtime", "planned outage",
"service reporting", "measurement method", "performance metric",
"monthly service review", "service review meeting",
"service catalogue", "delivery scope"
],
}

Important:

# backend/classification/taxonomy.py

# ------------------------------------------------------------------

# Axis 1 – Contract Category (unchanged)

TAXONOMY = [
"Specialist", "SI / Integration", "Development", "Support",
"SW SAAS", "Cloud / Infra", "Break Fix", "Hardware",
]

# SEED_KEYWORDS ... (keep the 20-word lists we built earlier)

# ------------------------------------------------------------------

# Axis 2 – Document Type (now 18 entries)

DOC_TYPES = [

# Core

"Master Service Agreement", "Statement of Work", "Pricing Sheet",
"Order Form", "Purchase Order", "Invoice", "Price List",
"Support Addendum", "License Agreement", "Service Schedule",

# New generic legal forms

"Data Processing Agreement", "End-User License Agreement",
"Unlimited License Agreement", "Non-Disclosure Agreement",

# Publisher-specific forms

"Microsoft Enterprise Agreement",
"Microsoft Products and Services Agreement",
"SAP Subscription Order Form",
"Oracle Master Agreement",
"Oracle Unlimited License Agreement",
"Adobe Enterprise Term License Agreement",
"Salesforce Master Subscription Agreement",
]

DOC_TYPE_KEYWORDS = { # ---------- NEW GENERIC FORMS ----------
"Data Processing Agreement": [
"data processing agreement", "dpa", "processor", "controller",
"sub-processor", "gdpr", "annex", "standard contractual clauses",
"personal data", "security measures", "audit rights",
"breach notification", "data subject request", "transfer mechanism",
"encryption", "deletion", "confidentiality", "article 28",
"eu 2016/679", "processing instructions", "liability for data"
], # [oai_citation:0‡CookieYes](https://www.cookieyes.com/blog/data-processing-agreement/?utm_source=chatgpt.com)
"End-User License Agreement": [
"end user license agreement", "eula", "license grant",
"permitted uses", "restrictions", "single user", "software license",
"reverse engineer", "ownership", "termination", "warranty disclaimer",
"liability limitation", "governing law", "clickwrap", "installation",
"copying", "sublicense", "intellectual property rights", "usage rights",
"patches", "updates", "derivative works"
], # [oai_citation:1‡ServiceNow](https://www.servicenow.com/products/it-asset-management/what-is-eula.html?utm_source=chatgpt.com)
"Unlimited License Agreement": [
"unlimited license agreement", "ula", "oracle ula", "certification",
"deployment", "true-up", "audit", "unlimited deploy", "processor license",
"support fees", "subscription term", "exit clause", "certification letter",
"java", "license metric", "enterprise license", "perpetual license",
"support uplift", "license pool", "count methodology"
], # [oai_citation:2‡Oracle](https://www.oracle.com/a/ocom/docs/glas-usecase-ula-digital.pdf?utm_source=chatgpt.com)
"Non-Disclosure Agreement": [
"non-disclosure agreement", "nda", "confidentiality agreement",
"confidential information", "disclosing party", "receiving party",
"carve-outs", "term of confidentiality", "survival", "injunction",
"obligations of recipient", "return or destroy", "permitted disclosure",
"residuals", "mutual nda", "unilateral nda", "definition of confidential",
"governing law", "equitable relief", "remedies", "non-use"
], # [oai_citation:3‡Sterlington PLLC](https://www.sterlingtonlaw.com/key-provisions-of-non-disclosure-agreements/?utm_source=chatgpt.com)

    # ---------- PUBLISHER-SPECIFIC ----------
     "Microsoft Enterprise Agreement": [
         "enterprise agreement", "microsoft ea", "enrollment",
         "true up", "software assurance", "server and cloud enrollment",
         "ea number", "annual order", "ea program", "desktop products",
         "volume licensing", "pricing level", "standard enrollment",
         "enterprise enrollment", "ea amendment", "ea renewal",
         "enterprise products", "azure monetary commit", "ea portal",
         "ea termination"
     ],  #  [oai_citation:4‡Microsoft](https://www.microsoft.com/en-us/licensing/licensing-programs/enterprise?utm_source=chatgpt.com)
     "Microsoft Products and Services Agreement": [
         "products and services agreement", "mpsa", "purchasing account",
         "evergreen agreement", "volume licensing", "software assurance optional",
         "transactional licensing", "online services", "product pool",
         "mpsa purchasing", "microsoft business center", "account owner",
         "project pool", "non-expiring", "mpsa terms", "pricing level",
         "mpsa amendment", "purchasing site", "agreement registration",
         "affiliate registration"
     ],  #  [oai_citation:5‡Microsoft](https://www.microsoft.com/en-us/licensing/mpsa?utm_source=chatgpt.com)
     "SAP Subscription Order Form": [
         "subscription order form", "sap cloud services", "sap order form",
         "s/4hana cloud", "professional edition", "sold-to party",
         "renewal subscription", "delivery subscription", "activation date",
         "entitlement", "sap cloud platform", "usage metric", "current tcv",
         "service description", "cloud service schedule", "order type",
         "contract duration", "cloud subscription", "fees and payments",
         "support fee", "data center region"
     ],  #  [oai_citation:6‡SAP Help Portal](https://help.sap.com/docs/SAP_ERP/a4d850e7e0b5425a93db2ecd8634f737/4f34de9174512b93e10000000a42189e.html?version=6.03.latest&utm_source=chatgpt.com)
     "Oracle Master Agreement": [
         "oracle master agreement", "oma", "cloud services agreement",
         "ordering document", "technical support policies", "license definitions",
         "schedule p", "oracle cloud", "migration credit", "program license",
         "oracle agreement version", "cloud terms", "license set",
         "payment schedule", "data processing agreement (oracle)",
         "customer reference number", "master contract", "governing law",
         "support terms", "termination for convenience"
     ],  #  [oai_citation:7‡Oracle](https://www.oracle.com/contracts/?utm_source=chatgpt.com)
     "Oracle Unlimited License Agreement": [
         "oracle ula", "unlimited license agreement", "ula certification",
         "deployment report", "ula term", "ula exit", "license audit",
         "support renewal", "processor metric", "oracle software",
         "core factor", "license inventory", "java ula", "deployment count",
         "support uplift", "ula amendment", "ula renewal option",
         "oracle support agreement", "ula template", "ula true up",
         "technical support fees"
     ],  #  [oai_citation:8‡Oracle](https://www.oracle.com/a/ocom/docs/glas-usecase-ula-digital.pdf?utm_source=chatgpt.com)
     "Adobe Enterprise Term License Agreement": [
         "enterprise term license agreement", "etla", "adobe creative cloud",
         "adobe etla", "anniversary payment", "license deployment",
         "true up report", "serial numbers", "enterprise dashboard",
         "license metrics", "license term", "etla renewal", "annual payment",
         "adobe admin console", "creative cloud for enterprise",
         "adobe stock", "managed services", "support level",
         "deployment schedule", "vip migration"
     ],  #  [oai_citation:9‡Adobe](https://www.adobe.com/content/dam/cc/en/buying-programs/pdfs/etla-overview-com-gov-en.pdf?utm_source=chatgpt.com)
     "Salesforce Master Subscription Agreement": [
         "master subscription agreement", "salesforce msa", "service cloud",
         "sales cloud", "trust.salesforce.com", "api limit", "org id",
         "integration with non-sfdc", "monthly subscription", "uptime",
         "service level commitment", "data processing addendum",
         "salesforce order form", "sandbox", "governance limit",
         "lightning platform", "governance API", "connected app",
         "renewal term", "usage metrics", "maintenance window"
     ],  #  [oai_citation:10‡Salesforce](https://www.salesforce.com/content/dam/web/en_us/www/documents/legal/salesforce_MSA.pdf?utm_source=chatgpt.com)



    # (Existing DOC_TYPE_KEYWORDS entries for MSA, SOW, etc. stay unchanged.)

}

10 Must-Have Clauses in Your Data Processing Agreement - CookieYes

Discover the essential clauses every Data Processing Agreement must include. Protect your data, ensure compliance, and strengthen partnerships with these key contract elements.

# backend/classification/taxonomy.py

# ------------------------------------------------------------------

# Axis 1 – Contract Category

TAXONOMY = [

    "Specialist", "SI / Integration", "Development", "Support",

    "SW SAAS", "Cloud / Infra", "Break Fix", "Hardware",

]

SEED_KEYWORDS = {

    "Specialist": [

        "subject matter expert", "consultant", "consultancy", "advisory",

        "assessment", "audit", "health check", "optimization", "training",

        "workshop", "knowledge transfer", "expert services", "professional services",

        "managed advisory", "strategy review", "process mapping",

        "business analysis", "gap analysis", "best practice review", "roadmap",

    ],

    "SI / Integration": [

        "system integration", "systems integrator", "implementation",

        "data migration", "cutover", "configuration", "rollout",

        "go live", "integration testing", "interface build", "api integration",

        "middleware", "onboarding", "deployment", "devops automation",

        "change management", "work package", "phase delivery",

        "technical design", "blueprint", "fit gap",

    ],

    "Development": [

        "custom development", "software build", "coding sprint",

        "feature enhancement", "agile backlog", "user story", "commit",

        "database schema", "unit test", "ci/cd", "version control",

        "code review", "prototype", "minimum viable product", "dev sandbox",

        "acceptance criteria", "merge request", "release note",

        "bug fix", "velocity",

    ],

    "Support": [

        "support services", "maintenance", "ticket", "help desk",

        "incident", "service request", "knowledge base", "service window",

        "priority response", "patching", "root cause", "sla response",

        "hotfix", "change ticket", "problem management", "24/7 coverage",

        "severity", "resolution time", "remote support", "technical assistance",

    ],

    "SW SAAS": [

        "software as a service", "subscription", "tenant", "instance",

        "usage fee", "api call", "monthly active", "seat license",

        "uptime", "multi-tenant", "cloud console", "version upgrade",

        "release cycle", "sandbox", "renewal", "user licence",

        "dashboard", "web application", "overage", "data retention",

    ],

    "Cloud / Infra": [

        "infrastructure as a service", "compute", "virtual machine",

        "instance hour", "storage bucket", "network egress", "vpc",

        "load balancer", "kubernetes", "managed service", "autoscaling",

        "object storage", "snapshot", "region", "availability zone",

        "terraform", "cloud console", "iam", "resource quota", "billing account",

    ],

    "Break Fix": [

        "break/fix", "on-site repair", "hardware replacement",

        "incident restore", "field engineer", "dispatch", "call-out",

        "troubleshoot", "diagnostic", "repair time", "service truck",

        "parts swap", "return merchandise authorization", "downtime",

        "fault isolation", "service center", "refurbish", "replacement unit",

        "escalation", "after-hours",

    ],

    "Hardware": [

        "equipment", "appliance", "device", "hardware unit",

        "sku", "chassis", "rack", "blade", "vendor part number",

        "serial number", "warranty", "spare parts", "firmware",

        "data sheet", "power supply", "network port", "ssd", "cpu",

        "memory module", "service tag",

    ],

}

# ------------------------------------------------------------------

# Axis 2 – Document Type (18 total)

DOC_TYPES = [

    # Core

    "Master Service Agreement", "Statement of Work", "Pricing Sheet",

    "Order Form", "Purchase Order", "Invoice", "Price List",

    "Support Addendum", "License Agreement", "Service Schedule",

    # Generic legal forms

    "Data Processing Agreement", "End-User License Agreement",

    "Unlimited License Agreement", "Non-Disclosure Agreement",

    # Publisher-specific

    "Microsoft Enterprise Agreement", "Microsoft Products and Services Agreement",

    "SAP Subscription Order Form", "Oracle Master Agreement",

    "Oracle Unlimited License Agreement", "Adobe Enterprise Term License Agreement",

    "Salesforce Master Subscription Agreement",

]

DOC_TYPE_KEYWORDS = {

    # ---------- Core types (unchanged from previous list) ----------

    "Master Service Agreement": [

        "master service agreement", "this msa", "m s a", "agreement number",

        "governing law", "entire agreement", "term and termination",

        "liability cap", "force majeure", "confidentiality", "definitions",

        "order form attached", "statement of work", "scope of services",

        "relationship of the parties", "notices", "assignment",

        "severability", "waiver", "counterparts", "signature block",

    ],  #  [oai_citation:0‡Microsoft](https://www.microsoft.com/en-us/licensing/licensing-programs/enterprise?utm_source=chatgpt.com)

    "Statement of Work": [

        "statement of work", "sow", "scope of work", "project milestones",

        "deliverables", "acceptance criteria", "project schedule",

        "roles and responsibilities", "in scope", "out of scope",

        "payment schedule", "resource plan", "change request",

        "assumptions", "project manager", "kickoff", "timeline",

        "go live", "project close", "location of services",

    ],

    "Pricing Sheet": [

        "pricing sheet", "cost summary", "rate card", "fee schedule",

        "price per unit", "hourly rate", "unit cost", "extended price",

        "subtotal", "discount", "tax", "one-time fee", "recurring fee",

        "cost breakdown", "bill of materials", "bom", "volume tier",

        "sku", "list price", "net price", "currency",

    ],

    "Order Form": [

        "order form", "order number", "service order", "order effective date",

        "billing term", "quantity", "unit price", "initial term",

        "renewal term", "customer signature", "provider signature",

        "service description", "subscription plan", "support tier",

        "payment due", "purchase order reference", "upfront charge",

        "recurring charge", "implementation fee", "contract id",

    ],

    "Purchase Order": [

        "purchase order", "po number", "po date", "vendor", "buyer",

        "ship to", "bill to", "payment terms", "net 30", "net 60",

        "line item", "qty", "uom", "unit price", "extended amount",

        "incoterms", "delivery date", "freight", "approval",

        "purchase requisition", "receiving",

    ],

    "Invoice": [

        "invoice", "invoice number", "invoice date", "due date",

        "amount due", "balance", "tax id", "remit to", "bill to",

        "po reference", "account balance", "late fee", "subtotal",

        "vat", "gst", "line item", "hours", "rate", "description",

        "total", "terms",

    ],

    "Price List": [

        "price list", "list price", "product code", "sku", "msrp",

        "public price", "tiered pricing", "volume discount",

        "valid through", "effective date", "updated price",

        "currency", "unit price", "support fee", "maintenance fee",

        "upgrade price", "license fee", "subscription fee", "setup fee", "training fee",

    ],

    "Support Addendum": [

        "support addendum", "support agreement", "maintenance addendum",

        "service level", "response time", "resolution time",

        "support hours", "incident priority", "support window",

        "contact method", "escalation", "technical support",

        "software updates", "patches", "hotfixes", "support fee",

        "support term", "knowledge base", "ticket", "sla",

    ],

    "License Agreement": [

        "license agreement", "licensor", "licensee", "grant of license",

        "scope of license", "term of license", "perpetual license",

        "subscription license", "royalty", "license fee", "intellectual property",

        "restrictions", "audit rights", "termination", "confidential information",

        "benchmarks", "export control", "governing law", "warranty disclaimer", "indemnification",

    ],

    "Service Schedule": [

        "service schedule", "services schedule", "service description",

        "service tier", "service credits", "availability", "uptime",

        "response time", "maintenance window", "service desk",

        "escalation procedure", "scheduled downtime", "planned outage",

        "service reporting", "measurement method", "performance metric",

        "monthly service review", "service review meeting",

        "service catalogue", "delivery scope",

    ],



    # ---------- Generic legal forms ----------

    "Data Processing Agreement": [

        "data processing agreement", "dpa", "processor", "controller",

        "sub-processor", "gdpr", "annex", "standard contractual clauses",

        "personal data", "security measures", "audit rights",

        "breach notification", "data subject request", "transfer mechanism",

        "encryption", "deletion", "confidentiality", "article 28", "processing instructions",

    ],  #  [oai_citation:1‡GDPRhub](https://gdprhub.eu/Article_28_GDPR?ref=noties.consulting&utm_source=chatgpt.com)

    "End-User License Agreement": [

        "end user license agreement", "eula", "license grant",

        "permitted uses", "restrictions", "software license", "single user",

        "reverse engineer", "ownership", "termination", "warranty disclaimer",

        "liability limitation", "clickwrap", "installation", "copying",

        "sublicense", "intellectual property rights", "updates", "derivative works", "export control",

    ],  #  [oai_citation:2‡Sirion.ai](https://www.sirion.ai/library/contract-management/end-user-license-agreement-eula/?utm_source=chatgpt.com)

    "Unlimited License Agreement": [

        "unlimited license agreement", "ula", "oracle ula", "certification",

        "deployment report", "license audit", "support renewal",

        "processor metric", "license metric", "core factor", "license inventory",

        "support uplift", "license pool", "perpetual license", "java ula",

        "exit clause", "true-up", "ula amendment", "ula renewal option", "technical support fees",

    ],  #  [oai_citation:3‡Oracle](https://www.oracle.com/a/ocom/docs/glas-usecase-ula-digital.pdf?utm_source=chatgpt.com)

    "Non-Disclosure Agreement": [

        "non-disclosure agreement", "nda", "confidentiality agreement",

        "confidential information", "disclosing party", "receiving party",

        "carve-outs", "term of confidentiality", "survival", "injunction",

        "obligations of recipient", "return or destroy", "permitted disclosure",

        "residuals", "mutual nda", "unilateral nda", "definition of confidential",

        "equitable relief", "non-use", "remedies",

    ],  #  [oai_citation:4‡Thomson Reuters Legal](https://legal.thomsonreuters.com/en/insights/articles/4-things-to-know-about-non-disclosure-agreements?utm_source=chatgpt.com) [oai_citation:5‡Investopedia](https://www.investopedia.com/articles/investing/041315/how-ndas-work-and-why-theyre-important.asp?utm_source=chatgpt.com)



    # ---------- Publisher-specific forms ----------

    "Microsoft Enterprise Agreement": [

        "enterprise agreement", "microsoft ea", "enrollment", "true up",

        "server and cloud enrollment", "volume licensing", "desktop products",

        "annual order", "ea portal", "ea amendment", "pricing level",

        "azure monetary commit", "enterprise enrollment", "ea renewal",

        "ea number", "ea program", "ea term", "managed service",

        "software assurance", "ea termination",

    ],  #  [oai_citation:6‡Microsoft](https://www.microsoft.com/en-us/licensing/licensing-programs/enterprise?utm_source=chatgpt.com)

    "Microsoft Products and Services Agreement": [

        "products and services agreement", "mpsa", "purchasing account",

        "non-expiring agreement", "volume licensing", "transactional licensing",

        "software assurance optional", "product pool", "mpsa terms", "price level",

        "business center", "evergreen agreement", "project pool", "affiliate registration",

        "account owner", "online services", "mpsa enrollment", "mpsa amendment",

        "mpsa renewal", "mpsa purchasing site",

    ],  #  [oai_citation:7‡Microsoft](https://www.microsoft.com/licensing/docs/view/Microsoft-Products-and-Services-Agreement-MPSA?utm_source=chatgpt.com)

    "SAP Subscription Order Form": [

        "sap subscription order form", "cloud services", "s/4hana cloud",

        "sold-to party", "service description", "entitlement", "activation date",

        "usage metric", "current tcv", "renewal subscription", "delivery subscription",

        "cloud service schedule", "subscription fee", "support fee", "data center region",

        "contract duration", "order type", "cloud subscription", "fees and payments",

        "sap cloud platform",

    ],  #  [oai_citation:8‡SAP Help Portal](https://help.sap.com/docs/subscription-billing/feature-overview/subscription-terms?utm_source=chatgpt.com)

    "Oracle Master Agreement": [

        "oracle master agreement", "oma", "ordering document",

        "five-year term", "technical support policies", "license definitions",

        "license set", "cloud services agreement", "oracle cloud", "migration credit",

        "payment schedule", "data processing agreement", "master contract version",

        "customer reference number", "support terms", "termination for convenience",

        "ama amendment", "license metric", "support fees", "supplemental terms",

    ],  #  [oai_citation:9‡Oracle](https://www.oracle.com/contracts/?utm_source=chatgpt.com)

    "Oracle Unlimited License Agreement": [

        "oracle ula", "unlimited license agreement", "ula certification",

        "deployment report", "ula exit", "license audit", "support renewal",

        "processor metric", "java ula", "core factor", "license inventory",

        "support uplift", "ula amendment", "ula renewal option", "technical support fees",

        "license pool", "count methodology", "ula term", "ula true up", "cloud deployment",

    ],  #  [oai_citation:10‡Oracle](https://www.oracle.com/a/ocom/docs/glas-usecase-ula-digital.pdf?utm_source=chatgpt.com)

    "Adobe Enterprise Term License Agreement": [

        "enterprise term license agreement", "etla", "adobe etla",

        "creative cloud", "standardize for 3-year period", "anniversary payment",

        "license deployment", "true up report", "enterprise dashboard",

        "license metrics", "adobe admin console", "adobe stock", "managed services",

        "support level", "deployment schedule", "vip migration", "software updates",

        "serial numbers", "cloud services", "product deployment",

    ],  #  [oai_citation:11‡Adobe](https://www.adobe.com/content/dam/cc/en/buying-programs/pdfs/etla-overview-com-gov-en.pdf?utm_source=chatgpt.com)

    "Salesforce Master Subscription Agreement": [

        "master subscription agreement", "salesforce msa", "service cloud",

        "sales cloud", "trust.salesforce.com", "api limit", "org id",

        "integration with non-sfdc", "monthly subscription", "uptime",

        "service level commitment", "data processing addendum", "order form",

        "sandbox", "governance limit", "lightning platform", "connected app",

        "renewal term", "usage metrics", "maintenance window", "sfdc services",

    ],  #  [oai_citation:12‡SEC](https://www.sec.gov/Archives/edgar/data/1428669/000119312508062588/dex1028.htm?utm_source=chatgpt.com)

}

Enterprise Agreement | Microsoft Volume Licensing

Explore Microsoft Enterprise Licensing programs. Access comprehensive resources and support to manage your enterprise software and services efficiently.
