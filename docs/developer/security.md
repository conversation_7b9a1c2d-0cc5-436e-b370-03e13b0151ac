# Security Implementation

This document provides a comprehensive overview of the security implementation in the Aptio platform, including authentication, authorization, data protection, and audit logging.

## Table of Contents

- [Security Architecture Overview](#security-architecture-overview)
- [Authentication Implementation](#authentication-implementation)
- [Authorization and Access Control](#authorization-and-access-control)
- [Multi-Tenancy Security](#multi-tenancy-security)
- [Data Protection](#data-protection)
- [Secure AI Processing](#secure-ai-processing)
- [Audit Logging](#audit-logging)
- [Security Best Practices](#security-best-practices)
- [Compliance Considerations](#compliance-considerations)

## Security Architecture Overview

Aptio implements a multi-layered security approach to protect sensitive data and ensure compliance with regulations.

### Defense in Depth Strategy

The platform implements multiple layers of security controls:

```mermaid
flowchart TB
    subgraph "Network Layer"
        WAF["Web Application Firewall"]
        DDoS["DDoS Protection"]
        TLS["TLS 1.3 Encryption"]
    end
    
    subgraph "Application Layer"
        Auth["Authentication"]
        RBAC["Role-Based Access Control"]
        InputVal["Input Validation"]
        RateLimit["API Rate Limiting"]
    end
    
    subgraph "Data Layer"
        Encrypt["Data Encryption"]
        Masking["Data Masking"]
        Access["Access Controls"]
        Integrity["Data Integrity"]
    end
    
    subgraph "Infrastructure Layer"
        Container["Container Isolation"]
        Network["Network Segmentation"]
        Monitoring["Security Monitoring"]
        Updates["Security Updates"]
    end
    
    Client[Client] --> WAF
    WAF --> DDoS
    DDoS --> TLS
    TLS --> Auth
    Auth --> RBAC
    RBAC --> InputVal
    InputVal --> RateLimit
    RateLimit --> Encrypt
    Encrypt --> Masking
    Masking --> Access
    Access --> Integrity
    Integrity --> Container
    Container --> Network
    Network --> Monitoring
    Monitoring --> Updates
```

## Authentication Implementation

Aptio uses Auth.js (formerly NextAuth.js) for authentication, providing a secure and flexible authentication system.

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant AuthService
    participant JWTService
    participant UserRepository
    
    User->>Frontend: Login Request
    Frontend->>AuthService: Authenticate(email, password)
    AuthService->>UserRepository: FindUserByEmail(email)
    UserRepository-->>AuthService: User
    AuthService->>AuthService: VerifyPassword(password, user.passwordHash)
    AuthService->>JWTService: GenerateTokens(user)
    JWTService-->>AuthService: {accessToken, refreshToken}
    AuthService-->>Frontend: Authentication Response
    Frontend-->>User: Login Success + Tokens
```

### JWT Implementation

```typescript
export class JWTService {
  constructor(private configService: ConfigService) {}

  generateAccessToken(payload: JWTPayload): string {
    return jwt.sign(payload, this.configService.get('JWT_SECRET'), {
      expiresIn: '15m',
    });
  }

  generateRefreshToken(payload: JWTPayload): string {
    return jwt.sign(payload, this.configService.get('JWT_REFRESH_SECRET'), {
      expiresIn: '7d',
    });
  }

  verifyAccessToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.configService.get('JWT_SECRET')) as JWTPayload;
    } catch (error) {
      throw new UnauthorizedError('Invalid access token');
    }
  }

  verifyRefreshToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.configService.get('JWT_REFRESH_SECRET')) as JWTPayload;
    } catch (error) {
      throw new UnauthorizedError('Invalid refresh token');
    }
  }
}
```

### Authentication Middleware

```typescript
export function authMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('Missing or invalid authorization header');
    }

    const token = authHeader.split(' ')[1];
    
    // Verify token
    const jwtService = new JWTService(new ConfigService());
    const payload = jwtService.verifyAccessToken(token);
    
    // Attach user to request
    req.user = {
      id: payload.userId,
      email: payload.email,
      roles: payload.roles,
    };
    
    next();
  } catch (error) {
    next(error);
  }
}
```

## Authorization and Access Control

Aptio implements Role-Based Access Control (RBAC) to manage permissions and access control.

### Permission Model

The permission model is based on the following components:

- **Roles**: Collections of permissions (e.g., Admin, Manager, User)
- **Resources**: Objects that can be accessed (e.g., Contract, License, User)
- **Actions**: Operations that can be performed on resources (e.g., Create, Read, Update, Delete)
- **Permissions**: Combinations of resources and actions (e.g., Contract:Create, License:Read)

### RBAC Implementation

```typescript
export class RBACService {
  constructor(private userRepository: UserRepository) {}

  async hasPermission(userId: string, tenantId: string, permission: string): Promise<boolean> {
    // Get user roles for the tenant
    const tenantUser = await this.userRepository.findTenantUser(userId, tenantId);
    if (!tenantUser) {
      return false;
    }
    
    // Get role permissions
    const rolePermissions = await this.userRepository.getRolePermissions(tenantUser.tenantRole);
    
    // Check if the role has the required permission
    return rolePermissions.includes(permission);
  }

  async checkPermission(userId: string, tenantId: string, permission: string): Promise<void> {
    const hasPermission = await this.hasPermission(userId, tenantId, permission);
    if (!hasPermission) {
      throw new ForbiddenError(`User does not have permission: ${permission}`);
    }
  }
}
```

### Authorization Middleware

```typescript
export function authorizationMiddleware(permission: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const tenantId = req.headers['x-tenant-id'] as string;
      
      if (!userId || !tenantId) {
        throw new UnauthorizedError('Missing user or tenant context');
      }
      
      const rbacService = new RBACService(new UserRepository(new PrismaClient()));
      await rbacService.checkPermission(userId, tenantId, permission);
      
      next();
    } catch (error) {
      next(error);
    }
  };
}
```

## Multi-Tenancy Security

Aptio implements a robust multi-tenancy architecture to securely isolate data between tenants.

### Tenant Isolation Approaches

1. **Database-Level Isolation**:
   - Row-level security in PostgreSQL
   - Tenant ID as a mandatory filter on all queries
   - Tenant context in all repository methods

2. **Application-Level Isolation**:
   - Tenant context in all service methods
   - Middleware to extract and validate tenant context
   - Permission checks that include tenant context

3. **API-Level Isolation**:
   - Tenant-specific API keys
   - Rate limiting per tenant
   - Request validation that includes tenant context

### Tenant Context Middleware

```typescript
export function tenantContextMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const tenantId = req.headers['x-tenant-id'] as string;
    if (!tenantId) {
      throw new UnauthorizedError('Missing tenant context');
    }
    
    // Validate that the user has access to the tenant
    const userId = req.user?.id;
    if (!userId) {
      throw new UnauthorizedError('Missing user context');
    }
    
    const userRepository = new UserRepository(new PrismaClient());
    userRepository.findTenantUser(userId, tenantId)
      .then(tenantUser => {
        if (!tenantUser) {
          throw new ForbiddenError('User does not have access to this tenant');
        }
        
        // Attach tenant context to request
        req.tenantContext = new TenantContext(
          tenantId,
          userId,
          [tenantUser.tenantRole]
        );
        
        next();
      })
      .catch(error => next(error));
  } catch (error) {
    next(error);
  }
}
```

## Data Protection

Aptio implements comprehensive data protection measures to secure sensitive information.

### Data Encryption

The platform uses AES-256 encryption for sensitive data:

```typescript
export class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private key: Buffer;
  
  constructor(private configService: ConfigService) {
    // Convert hex key to Buffer
    this.key = Buffer.from(this.configService.get('ENCRYPTION_KEY'), 'hex');
  }
  
  encrypt(text: string): EncryptedData {
    // Generate initialization vector
    const iv = crypto.randomBytes(16);
    
    // Create cipher
    const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
    
    // Encrypt data
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Get auth tag
    const authTag = cipher.getAuthTag();
    
    return {
      iv: iv.toString('hex'),
      encrypted,
      authTag: authTag.toString('hex')
    };
  }
  
  decrypt(data: EncryptedData): string {
    // Convert hex to Buffer
    const iv = Buffer.from(data.iv, 'hex');
    const authTag = Buffer.from(data.authTag, 'hex');
    
    // Create decipher
    const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
    decipher.setAuthTag(authTag);
    
    // Decrypt data
    let decrypted = decipher.update(data.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### Data Classification

The platform implements data classification to identify and protect sensitive data:

| Classification | Description | Examples | Protection Measures |
|----------------|-------------|----------|---------------------|
| Public | Non-sensitive data | Public company information | No special protection |
| Internal | Internal-only data | Internal documents | Authentication required |
| Confidential | Sensitive business data | Contract details, financial data | Encryption, access controls |
| Restricted | Highly sensitive data | PII, payment information | Encryption, strict access controls, audit logging |

### Data Masking

Sensitive data is masked in logs and non-essential contexts:

```typescript
export class DataMaskingService {
  maskPII(text: string): string {
    // Mask email addresses
    text = text.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL]');
    
    // Mask phone numbers
    text = text.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]');
    
    // Mask credit card numbers
    text = text.replace(/\b(?:\d{4}[-\s]?){3}\d{4}\b/g, '[CREDIT_CARD]');
    
    // Mask SSNs
    text = text.replace(/\b\d{3}[-]?\d{2}[-]?\d{4}\b/g, '[SSN]');
    
    return text;
  }
  
  maskSensitiveData(data: any, sensitiveFields: string[]): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }
    
    const result = Array.isArray(data) ? [...data] : { ...data };
    
    for (const key in result) {
      if (sensitiveFields.includes(key)) {
        result[key] = '[REDACTED]';
      } else if (typeof result[key] === 'object' && result[key] !== null) {
        result[key] = this.maskSensitiveData(result[key], sensitiveFields);
      }
    }
    
    return result;
  }
}
```

## Secure AI Processing

Aptio implements secure AI processing to protect sensitive data during AI operations.

### Isolated AI Processing

AI processing is isolated from the main application:

```mermaid
sequenceDiagram
    participant App as Application
    participant Queue as Job Queue
    participant Worker as AI Worker
    participant Storage as Document Storage
    participant DB as Database
    
    App->>Queue: Submit AI Job
    Queue-->>App: Job Accepted
    
    Worker->>Queue: Poll for Jobs
    Queue-->>Worker: Job Details
    
    Worker->>Storage: Request Document (Read-Only)
    Storage-->>Worker: Encrypted Document
    
    Worker->>Worker: Process in Isolated Container
    
    Worker->>DB: Store Results
    DB-->>Worker: Results Stored
    
    Worker->>Queue: Mark Job Complete
    Queue-->>App: Job Complete Notification
```

### AI Security Measures

1. **Containerized Processing**: AI operations run in isolated containers
2. **Read-Only Access**: AI processors have read-only access to documents
3. **Ephemeral Processing**: No persistent storage of sensitive data
4. **Audit Logging**: All AI operations are logged for audit purposes
5. **Input Validation**: All inputs to AI services are validated
6. **Output Filtering**: All outputs from AI services are filtered

### Secure AI Processor Implementation

```typescript
export class SecureAIProcessor {
  constructor(
    private configService: ConfigService,
    private encryptionService: EncryptionService,
    private auditService: AuditService
  ) {}
  
  async processDocument(jobId: string, documentId: string, tenantId: string): Promise<void> {
    try {
      // Log processing start
      await this.auditService.log({
        action: 'AI_PROCESSING_START',
        resource: `document:${documentId}`,
        tenantId,
        details: { jobId }
      });
      
      // Get document (read-only)
      const document = await this.getDocument(documentId, tenantId);
      
      // Process in isolated environment
      const result = await this.processInIsolatedEnvironment(document.content, document.metadata);
      
      // Store results
      await this.storeResults(jobId, result);
      
      // Log processing complete
      await this.auditService.log({
        action: 'AI_PROCESSING_COMPLETE',
        resource: `document:${documentId}`,
        tenantId,
        details: { jobId }
      });
    } catch (error) {
      // Log processing error
      await this.auditService.log({
        action: 'AI_PROCESSING_ERROR',
        resource: `document:${documentId}`,
        tenantId,
        details: { jobId, error: error.message }
      });
      
      throw error;
    }
  }
  
  private async getDocument(documentId: string, tenantId: string): Promise<{ content: string, metadata: any }> {
    // Implementation details
  }
  
  private async processInIsolatedEnvironment(content: string, metadata: any): Promise<any> {
    // Implementation details
  }
  
  private async storeResults(jobId: string, result: any): Promise<void> {
    // Implementation details
  }
}
```

## Audit Logging

Aptio implements comprehensive audit logging to track all security-relevant events.

### Audit Log Schema

The audit log schema captures the following information:

- **Who**: User ID, IP address, user agent
- **What**: Action performed, resource affected
- **When**: Timestamp
- **Where**: Service, component
- **How**: Details of the operation
- **Result**: Success or failure, error details

### Audit Service Implementation

```typescript
export class AuditService {
  constructor(private prisma: PrismaClient) {}
  
  async log(auditEvent: AuditEvent): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          action: auditEvent.action,
          resource: auditEvent.resource,
          actorId: auditEvent.actorId,
          details: auditEvent.details || {},
          timestamp: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
      // Fail open - don't block operations if audit logging fails
    }
  }
  
  async getAuditLogs(
    tenantId: string,
    filters: AuditLogFilters,
    pagination: PaginationOptions
  ): Promise<PaginatedResult<AuditLog>> {
    // Implementation details
  }
}
```

### Audit Middleware

```typescript
export function auditMiddleware(action: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    // Capture the response
    res.send = function(body) {
      const auditService = new AuditService(new PrismaClient());
      
      // Log the action
      auditService.log({
        action,
        resource: req.path,
        actorId: req.user?.id,
        details: {
          method: req.method,
          path: req.path,
          query: req.query,
          body: req.body,
          statusCode: res.statusCode,
          responseTime: Date.now() - (req.startTime || Date.now()),
        },
      });
      
      return originalSend.call(this, body);
    };
    
    next();
  };
}
```

## Security Best Practices

Aptio follows security best practices throughout the development lifecycle:

### Secure Coding Practices

1. **Input Validation**: All user inputs are validated
2. **Output Encoding**: All outputs are properly encoded
3. **Parameterized Queries**: All database queries use parameterized statements
4. **Error Handling**: Errors are handled securely without leaking sensitive information
5. **Dependency Management**: Dependencies are regularly updated and scanned for vulnerabilities

### Security Testing

1. **Static Application Security Testing (SAST)**: Code is scanned for security issues
2. **Dynamic Application Security Testing (DAST)**: Running application is tested for vulnerabilities
3. **Dependency Scanning**: Dependencies are scanned for known vulnerabilities
4. **Penetration Testing**: Regular penetration testing is performed
5. **Security Code Reviews**: Code is reviewed for security issues

### Security Configuration

1. **Secure Headers**: HTTP security headers are configured
2. **TLS Configuration**: TLS is configured securely
3. **Cookie Security**: Cookies are configured with secure attributes
4. **CORS Configuration**: CORS is configured to restrict cross-origin requests
5. **CSP Configuration**: Content Security Policy is configured to prevent XSS

## Compliance Considerations

Aptio is designed to help organizations comply with various regulations:

### GDPR Compliance

1. **Data Minimization**: Only necessary data is collected
2. **Purpose Limitation**: Data is used only for specified purposes
3. **Storage Limitation**: Data is retained only as long as necessary
4. **Data Subject Rights**: Users can access, correct, and delete their data
5. **Data Protection**: Data is protected with appropriate security measures

### SOC 2 Compliance

1. **Security**: Controls to protect against unauthorized access
2. **Availability**: Controls to ensure system availability
3. **Processing Integrity**: Controls to ensure accurate processing
4. **Confidentiality**: Controls to protect confidential information
5. **Privacy**: Controls to protect personal information

### HIPAA Compliance

1. **Access Controls**: Controls to restrict access to PHI
2. **Audit Controls**: Controls to record and examine activity
3. **Integrity Controls**: Controls to prevent improper alteration
4. **Transmission Security**: Controls to protect data in transit
5. **Business Associate Agreements**: Agreements with third parties
