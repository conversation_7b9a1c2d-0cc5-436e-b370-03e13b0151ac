# Aptio Architecture Overview

This document provides a comprehensive overview of the Aptio platform architecture, designed for contract and license lifecycle management.

## Table of Contents

- [System Architecture](#system-architecture)
- [Domain-Driven Design Implementation](#domain-driven-design-implementation)
- [Key Design Patterns](#key-design-patterns)
- [Security Architecture](#security-architecture)
- [Multi-Tenancy Implementation](#multi-tenancy-implementation)
- [AI Integration Architecture](#ai-integration-architecture)

## System Architecture

Aptio follows a Modular Monolith architecture pattern with clear boundaries between modules, facilitating future decomposition into microservices if needed.

### High-Level Architecture Diagram

```mermaid
flowchart TB
    subgraph "Client Layer"
        WebApp["Web Application"]
        MobileApp["Mobile Applications"]
        ExternalSystems["External Systems"]
    end

    subgraph "API Gateway"
        APIGateway["API Gateway<br/>(Authentication, Rate Limiting)"]
    end

    subgraph "Modular Monolith"
        subgraph "Core Domain"
            ContractMgmt["Contract Management<br/>(Repository Pattern)"]
            UserMgmt["User Management<br/>(RBAC)"]
            TenantMgmt["Tenant Management<br/>(Multi-tenancy)"]
        end

        subgraph "Supporting Services"
            Notifications["Notification<br/>System"]
            IntegrationSvc["Integration<br/>Services"]
            AuditSvc["Audit & Compliance<br/>(Event Sourcing)"]
        end

        subgraph "Isolated AI Services"
            AIQueue["AI Job Queue<br/>(BullMQ)"]
            AIProcessor["Secure AI Processor<br/>(Containerized)"]
            AIResults["AI Results<br/>Repository"]
        end
    end

    subgraph "Data Storage"
        PostgreSQL["PostgreSQL<br/>(Tenant Isolation)"]
        Redis["Redis<br/>(Cache, Queues)"]
        DocStorage["Document Storage<br/>(Encrypted at Rest)"]
    end

    subgraph "External Integrations"
        CRM["CRM<br/>(Salesforce, Hubspot)"]
        ESign["E-Signature<br/>(DocuSign, AdobeSign)"]
        IdP["Identity Providers<br/>(Okta, Azure AD)"]
        Storage["Cloud Storage<br/>(OneDrive, G-Drive)"]
        Payment["Payment Processing<br/>(Stripe, Chargebee)"]
    end

    %% Client connections
    WebApp --> APIGateway
    MobileApp --> APIGateway
    ExternalSystems --> APIGateway

    %% API Gateway connections
    APIGateway --> ContractMgmt
    APIGateway --> UserMgmt
    APIGateway --> TenantMgmt
    APIGateway --> Notifications
    APIGateway --> IntegrationSvc

    %% Core Service interactions
    ContractMgmt <--> PostgreSQL
    ContractMgmt <--> DocStorage
    ContractMgmt --> AIQueue
    ContractMgmt --> AuditSvc
    UserMgmt <--> PostgreSQL
    UserMgmt --> AuditSvc
    TenantMgmt <--> PostgreSQL
    TenantMgmt --> AuditSvc

    %% AI Processing flow
    AIQueue <--> Redis
    AIQueue --> AIProcessor
    AIProcessor --> AIResults
    AIProcessor --> DocStorage
    AIResults --> PostgreSQL
    AIProcessor --> AuditSvc

    %% Supporting services
    Notifications <--> Redis
    Notifications <--> PostgreSQL
    IntegrationSvc <--> Redis
    IntegrationSvc <--> PostgreSQL
    AuditSvc <--> PostgreSQL

    %% External integration connections
    IntegrationSvc <--> CRM
    IntegrationSvc <--> ESign
    IntegrationSvc <--> Storage
    IntegrationSvc <--> Payment
    UserMgmt <--> IdP
```

### Module Structure

The codebase is organized into the following structure:

```
src/
├── core/ (domain entities and interfaces)
│   ├── contract/
│   ├── user/
│   ├── tenant/
│   └── shared/
├── modules/ (bounded context implementations)
│   ├── contract-management/
│   ├── user-management/
│   ├── tenant-management/
│   ├── notification-system/
│   ├── ai-processing/
│   ├── integration-services/
│   └── audit-compliance/
├── infrastructure/ (cross-cutting concerns)
│   ├── database/
│   ├── messaging/
│   ├── security/
│   ├── logging/
│   └── services/
└── api/ (application interfaces)
    ├── rest/
    ├── graphql/
    └── webhooks/
```

### Technology Stack

- **Frontend**: Next.js v14, Auth.js v5, Tailwind CSS v3, Tanstack Query v5, Zod v3, Shadcn/UI
- **Backend**: Express.js v4, PostgreSQL v16 with pgvector, Prisma v5, Redis v7, BullMQ v4
- **Infrastructure**: Docker, Docker Compose, Kubernetes (optional)
- **AI**: OpenAI API, LangChain, Vector Embeddings

## Domain-Driven Design Implementation

Aptio implements Domain-Driven Design (DDD) principles to manage complexity and align the software with the business domain.

### Bounded Contexts

The system is organized into the following bounded contexts:

1. **ContractManagement** - Core domain focused on contract lifecycle
2. **UserManagement** - User identity, authentication, and authorization
3. **TenantManagement** - Multi-tenant organization management
4. **NotificationSystem** - Communication and alerts
5. **AIProcessing** - Isolated AI/ML operations
6. **IntegrationServices** - Third-party service connections
7. **AuditAndCompliance** - Security and compliance logging

### Domain Entities

#### ContractManagement Context

| Entity           | Description                                | Key Attributes                                              |
| ---------------- | ------------------------------------------ | ----------------------------------------------------------- |
| Contract         | Core entity representing a legal agreement | id, title, status, startDate, endDate, renewalType, version |
| ContractVersion  | Tracks document versions                   | id, contractId, versionNumber, documentUri, metadata        |
| ContractTemplate | Reusable contract templates                | id, name, industryType, clauses                             |
| Clause           | Contract building blocks                   | id, name, text, category, riskLevel                         |
| Party            | Contract participant                       | id, name, type, contactInfo                                 |
| Obligation       | Contractual obligation                     | id, contractId, description, dueDate, status                |
| ApprovalWorkflow | Contract approval process                  | id, contractId, steps, currentStep, status                  |
| Comment          | Feedback on contract                       | id, contractId, authorId, text, timestamp                   |

#### LicenseManagement Context

| Entity             | Description                                 | Key Attributes                                            |
| ------------------ | ------------------------------------------- | --------------------------------------------------------- |
| License            | Core entity representing a software license | id, name, licenseType, status, vendor, startDate, endDate |
| LicenseEntitlement | Features included in a license              | id, licenseId, name, description, included, quantity      |
| LicenseDocument    | Documents related to a license              | id, licenseId, name, documentType, documentUri            |
| LicenseUsage       | Usage data for a license                    | id, licenseId, date, usageCount, utilizationPercentage    |

#### UserManagement Context

| Entity     | Description      | Key Attributes                |
| ---------- | ---------------- | ----------------------------- |
| User       | System user      | id, email, name, status, role |
| Role       | User permissions | id, name, permissions         |
| Permission | Access control   | id, name, resource, action    |

#### TenantManagement Context

| Entity         | Description            | Key Attributes                         |
| -------------- | ---------------------- | -------------------------------------- |
| Tenant         | B2B customer           | id, name, tier, status                 |
| Subscription   | Paid service plan      | id, tenantId, plan, startDate, endDate |
| TenantSettings | Customer configuration | id, tenantId, branding, preferences    |
| TenantUser     | User-tenant mapping    | id, userId, tenantId, tenantRole       |

### Domain Relationships

- **Contract** is the central aggregate root in the ContractManagement context
- **License** is the central aggregate root in the LicenseManagement context
- **User** can have multiple roles across multiple tenants
- **Tenant** contains multiple users, contracts, and licenses
- **ContractVersion** tracks changes to Agreement Documents
- **AIJob** processes Agreement Documents and produces AIResult
- **AuditLog** tracks actions across all contexts

## Key Design Patterns

Aptio implements several design patterns to ensure a maintainable and scalable architecture:

### Architectural Patterns

- **Hexagonal Architecture Pattern**: Separates domain logic from external concerns
- **Clean Architecture Pattern**: Enforces dependency rules pointing inward
- **CQRS Pattern**: Separates read and write operations for scalability
- **Repository Pattern**: Abstracts data access
- **Mediator Pattern**: Decouples modules through message passing

### Component Patterns

- **Domain Model**: Core business entities and logic
- **Repository**: Data access abstractions
- **Service**: Business operations coordination
- **Controller**: API endpoints
- **Factory**: Object creation
- **Gateway**: External system interfaces
- **Event Publisher**: Message broadcasting
- **Event Subscriber**: Message consumption
- **Validator**: Input validation

### Implementation Examples

#### Repository Pattern

```typescript
// Example of Repository Pattern implementation
export class ContractRepository implements IContractRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string, tenantId: string): Promise<Contract | null> {
    const contractData = await this.prisma.contract.findFirst({
      where: { id, tenantId },
      include: {
        contractVersions: true,
        contractMetadata: true,
        parties: {
          include: {
            party: true,
          },
        },
      },
    });

    if (!contractData) return null;

    return this.mapToDomainModel(contractData);
  }

  async create(contract: Contract): Promise<Contract> {
    // Implementation details
  }

  async update(contract: Contract): Promise<Contract> {
    // Implementation details
  }

  private mapToDomainModel(data: any): Contract {
    // Mapping logic
  }
}
```

#### Service Pattern

```typescript
// Example of Service Pattern implementation
export class ContractService {
  constructor(
    private contractRepository: IContractRepository,
    private documentService: DocumentService,
    private aiService: AIService,
    private auditService: AuditService
  ) {}

  async createContract(
    contractData: CreateContractDto,
    tenantId: string,
    userId: string
  ): Promise<Contract> {
    // Validate input
    // Create contract entity
    // Store contract
    // Process document if provided
    // Log audit event
    // Return created contract
  }

  async updateContract(
    id: string,
    contractData: UpdateContractDto,
    tenantId: string,
    userId: string
  ): Promise<Contract> {
    // Implementation details
  }
}
```

## Security Architecture

Aptio implements a multi-layered security approach to protect sensitive data and ensure compliance with regulations.

### Multi-Layered Security Approach

1. **Defense in Depth** with multiple security controls:

   - Network Layer: WAF, DDoS protection, TLS 1.3
   - Application Layer: RBAC, API rate limiting, input validation
   - Data Layer: Encryption, data masking, access controls
   - Infrastructure Layer: Container isolation, network segmentation

2. **Authentication and Authorization**:

   - Auth.js for authentication flows
   - JWT-based token management
   - RBAC for granular permissions
   - Tenant isolation via database schemas

3. **Data Protection**:

   - AES-256 encryption at rest
   - TLS 1.3 encryption in transit
   - Data classification and handling policies
   - Automatic data purging based on retention policies

4. **Isolated AI Processing**:
   - Isolated Subsystem Pattern for AI processing
   - Container-based isolation
   - Restricted network access
   - Ephemeral data processing
   - Read-only access to source documents
   - Comprehensive audit logging

### Security Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant APIGateway
    participant AuthService
    participant ResourceService
    participant AuditService

    User->>APIGateway: Request with JWT
    APIGateway->>AuthService: Validate JWT
    AuthService->>APIGateway: Token Valid + User/Tenant Context
    APIGateway->>ResourceService: Request with Context
    ResourceService->>ResourceService: Check Permissions
    ResourceService->>AuditService: Log Access Attempt
    ResourceService->>APIGateway: Response
    APIGateway->>User: Response
```

## Multi-Tenancy Implementation

Aptio implements a robust multi-tenancy architecture to securely isolate data between tenants.

### Tenant Isolation Approaches

1. **Database-Level Isolation**:

   - Row-level security in PostgreSQL
   - Tenant ID as a mandatory filter on all queries
   - Tenant context in all repository methods

2. **Application-Level Isolation**:

   - Tenant context in all service methods
   - Middleware to extract and validate tenant context
   - Permission checks that include tenant context

3. **API-Level Isolation**:
   - Tenant-specific API keys
   - Rate limiting per tenant
   - Request validation that includes tenant context

### Tenant Context Implementation

```typescript
// Tenant context implementation
export class TenantContext {
  constructor(
    public readonly tenantId: string,
    public readonly userId: string,
    public readonly roles: string[]
  ) {}

  hasRole(role: string): boolean {
    return this.roles.includes(role);
  }

  static fromRequest(req: Request): TenantContext {
    const tenantId = req.headers["x-tenant-id"] as string;
    const userId = req.user?.id as string;
    const roles = (req.user?.roles as string[]) || [];

    if (!tenantId || !userId) {
      throw new UnauthorizedError("Invalid tenant context");
    }

    return new TenantContext(tenantId, userId, roles);
  }
}
```

## AI Integration Architecture

Aptio integrates AI capabilities throughout the platform using a secure and scalable architecture.

### RAG Architecture

The platform implements Retrieval-Augmented Generation (RAG) to provide accurate, context-aware responses based on the organization's documents.

```mermaid
flowchart LR
    subgraph "Document Processing"
        Upload[Upload Document]
        Split[Split into Chunks]
        Embed[Generate Embeddings]
        Store[Store in Vector DB]
    end

    subgraph "Query Processing"
        Query[User Query]
        QueryEmbed[Generate Query Embedding]
        Retrieve[Retrieve Similar Chunks]
        Context[Build Context]
        Generate[Generate Response]
        Response[Return Response]
    end

    Upload --> Split
    Split --> Embed
    Embed --> Store

    Query --> QueryEmbed
    QueryEmbed --> Retrieve
    Store --> Retrieve
    Retrieve --> Context
    Context --> Generate
    Generate --> Response
```

### Isolated AI Processing

AI processing is isolated from the main application to ensure security and scalability:

1. **Job Queue**: AI processing jobs are queued in Redis using BullMQ
2. **Containerized Processing**: AI processing happens in isolated containers
3. **Secure Data Access**: AI processors have read-only access to documents
4. **Result Storage**: Results are stored in the database via secure channels
5. **Audit Logging**: All AI operations are logged for audit purposes

### AI Services

The platform provides several AI services:

- **RAGService**: Provides Retrieval-Augmented Generation capabilities
- **ChatService**: Manages chat conversations with context
- **ContractAnalysisService**: Provides AI-powered Agreement Analysis
- **LicenseManagementService**: Provides AI-powered license management
- **AnalyticsService**: Provides AI-powered analytics
- **NotificationService**: Provides AI-powered notifications
- **OnboardingService**: Provides AI-powered onboarding
