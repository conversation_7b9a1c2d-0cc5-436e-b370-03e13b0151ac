# Database Design

This document provides a comprehensive overview of the Aptio database design, including schema details, entity relationships, and the vector database implementation.

## Table of Contents

- [Database Technology](#database-technology)
- [Schema Overview](#schema-overview)
- [Entity Relationships](#entity-relationships)
- [Vector Database Implementation](#vector-database-implementation)
- [Data Migration Strategy](#data-migration-strategy)
- [Performance Optimization](#performance-optimization)
- [Security Considerations](#security-considerations)

## Database Technology

Aptio uses PostgreSQL v16 with the pgvector extension for vector similarity search capabilities. The database is managed using Prisma ORM.

### Key Technologies

- **PostgreSQL v16**: Relational database with JSONB support for flexible schema evolution
- **pgvector extension**: Enables vector similarity search for AI features
- **Prisma v5**: Type-safe ORM with migration support
- **Row-level security**: For tenant isolation

## Schema Overview

The database schema is organized around the core domain entities and bounded contexts:

### Contract Management Context

- **Contract**: Core entity representing a legal agreement
- **ContractVersion**: Tracks document versions
- **ContractMetadata**: Stores extracted and enriched metadata
- **ContractTemplate**: Reusable contract templates
- **ClauseTemplate**: Contract building blocks
- **Party**: Contract participant
- **ContractParty**: Relationship between contracts and parties
- **Obligation**: Contractual obligation
- **ApprovalWorkflow**: Contract approval process
- **Comment**: Feedback on contract
- **RiskAssessment**: Contract risk evaluation

### License Management Context

- **License**: Core entity representing a software license
- **LicenseEntitlement**: Features included in a license
- **LicenseDocument**: Documents related to a license
- **LicenseUsage**: Usage data for a license

### User Management Context

- **User**: System user
- **TenantUser**: User-tenant mapping

### Tenant Management Context

- **Tenant**: B2B customer
- **Subscription**: Paid service plan
- **TenantSettings**: Customer configuration

### AI Processing Context

- **AIJob**: Processing request
- **AIResult**: Processing outcome

### Audit and Compliance Context

- **AuditLog**: System event record
- **RiskAssessment**: Contract risk evaluation

### Notification System Context

- **Notification**: Communication and alerts
- **NotificationDelivery**: Delivery status of notifications
- **NotificationPreference**: User preferences for notifications

## Entity Relationships

The following diagram shows the key entity relationships in the database:

```mermaid
erDiagram
    Tenant ||--o{ Contract : "has many"
    Tenant ||--o{ License : "has many"
    Tenant ||--o{ Party : "has many"
    Tenant ||--o{ ContractTemplate : "has many"
    Tenant ||--o{ TenantUser : "has many"
    Tenant ||--o| Subscription : "has one"
    Tenant ||--o| TenantSettings : "has one"
    
    User ||--o{ TenantUser : "has many"
    User ||--o{ Comment : "has many"
    User ||--o{ AuditLog : "has many"
    User ||--o{ Notification : "has many"
    User ||--o{ NotificationPreference : "has many"
    
    Contract ||--o{ ContractVersion : "has many"
    Contract ||--o| ContractMetadata : "has one"
    Contract ||--o{ Obligation : "has many"
    Contract ||--o| ApprovalWorkflow : "has one"
    Contract ||--o{ Comment : "has many"
    Contract ||--o{ ContractParty : "has many"
    Contract ||--o| RiskAssessment : "has one"
    
    ContractTemplate ||--o{ ClauseTemplate : "has many"
    
    Party ||--o{ ContractParty : "has many"
    
    License ||--o{ LicenseEntitlement : "has many"
    License ||--o{ LicenseDocument : "has many"
    License ||--o{ LicenseUsage : "has many"
    
    AIJob ||--o{ AIResult : "has many"
    
    Notification ||--o{ NotificationDelivery : "has many"
```

## Vector Database Implementation

Aptio uses the pgvector extension to store and query vector embeddings for AI features.

### Vector Embeddings Table

The vector embeddings are stored in a dedicated table:

```sql
CREATE TABLE IF NOT EXISTS vector_embeddings (
  id UUID PRIMARY KEY,
  tenant_id UUID NOT NULL,
  document_id UUID NOT NULL,
  chunk_index INTEGER NOT NULL,
  content TEXT NOT NULL,
  embedding VECTOR(1536) NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(document_id, chunk_index)
);
```

### Indexing for Similarity Search

The vector embeddings table uses an IVFFlat index for efficient similarity search:

```sql
CREATE INDEX IF NOT EXISTS vector_embeddings_embedding_idx ON vector_embeddings
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
```

### Tenant Isolation

Tenant isolation is enforced at the database level:

```sql
CREATE INDEX IF NOT EXISTS vector_embeddings_tenant_id_idx ON vector_embeddings(tenant_id);
```

### Vector Repository Implementation

The VectorRepository class provides methods for storing and retrieving vector embeddings:

```typescript
export class VectorRepository {
  constructor(private prisma: PrismaClient) {}

  async storeEmbedding(
    tenantId: string,
    documentId: string,
    chunkIndex: number,
    content: string,
    embedding: number[],
    metadata?: any
  ): Promise<string> {
    const id = uuidv4();
    
    await this.prisma.$executeRaw`
      INSERT INTO vector_embeddings (
        id, tenant_id, document_id, chunk_index, content, embedding, metadata
      ) VALUES (
        ${id}::uuid, ${tenantId}::uuid, ${documentId}::uuid, ${chunkIndex}, 
        ${content}, ${embedding}::vector, ${JSON.stringify(metadata || {})}::jsonb
      )
    `;
    
    return id;
  }

  async findSimilar(
    tenantId: string,
    embedding: number[],
    limit: number = 5,
    metadata?: any
  ): Promise<VectorSearchResult[]> {
    let metadataFilter = '';
    if (metadata) {
      // Build metadata filter based on provided metadata
      // This is a simplified example
      metadataFilter = Object.entries(metadata)
        .map(([key, value]) => `metadata->>'${key}' = '${value}'`)
        .join(' AND ');
      
      if (metadataFilter) {
        metadataFilter = ` AND ${metadataFilter}`;
      }
    }
    
    const results = await this.prisma.$queryRaw<VectorSearchResult[]>`
      SELECT 
        id, 
        document_id as "documentId", 
        content, 
        metadata, 
        1 - (embedding <=> ${embedding}::vector) as similarity
      FROM 
        vector_embeddings
      WHERE 
        tenant_id = ${tenantId}::uuid
        ${metadataFilter ? this.prisma.$raw([metadataFilter]) : this.prisma.$raw([])}
      ORDER BY 
        embedding <=> ${embedding}::vector
      LIMIT 
        ${limit}
    `;
    
    return results;
  }

  async deleteEmbeddings(tenantId: string, documentId: string): Promise<number> {
    const result = await this.prisma.$executeRaw`
      DELETE FROM vector_embeddings 
      WHERE tenant_id = ${tenantId}::uuid AND document_id = ${documentId}::uuid
    `;
    
    return result;
  }
}
```

## Data Migration Strategy

Aptio uses Prisma Migrate for database migrations:

### Migration Workflow

1. **Development Migrations**:
   ```bash
   pnpm prisma migrate dev --name migration_name
   ```

2. **Production Migrations**:
   ```bash
   pnpm prisma migrate deploy
   ```

### Migration Best Practices

- Keep migrations small and focused
- Use descriptive names for migrations
- Test migrations in a staging environment before production
- Include rollback procedures for critical migrations
- Document breaking changes

## Performance Optimization

### Indexing Strategy

The database uses strategic indexing to optimize common query patterns:

```prisma
// Contract indexes
@@index([tenantId])
@@index([status])
@@index([contractType])
@@index([startDate, endDate])
@@index([title]) // For search performance

// License indexes
@@index([tenantId])
@@index([status])
@@index([licenseType])
@@index([vendor])
@@index([startDate, endDate])
@@index([name]) // For search performance

// Notification indexes
@@index([userId])
@@index([tenantId])
@@index([type])
@@index([isRead])
@@index([createdAt])
```

### Query Optimization

- Use of Prisma's include for efficient eager loading
- Pagination for large result sets
- Selective column fetching for large tables
- Caching for frequently accessed data

### Connection Pooling

The database connection is managed with connection pooling to optimize performance:

```typescript
// Database connection configuration
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Connection pooling configuration
  log: ['query', 'info', 'warn', 'error'],
  // Adjust pool size based on workload
  __internal: {
    engine: {
      connectionLimit: 5,
    },
  },
});
```

## Security Considerations

### Data Encryption

Sensitive data is encrypted at the application level before storage:

- Document content is encrypted with AES-256
- Financial data is encrypted
- Personal identifiable information (PII) is encrypted

### Tenant Isolation

Tenant isolation is enforced at multiple levels:

- Database queries always include tenant ID
- Row-level security policies in PostgreSQL
- Application-level checks for tenant context

### Audit Logging

All database operations are logged for audit purposes:

- Who performed the operation
- What operation was performed
- When the operation was performed
- Which data was affected

### Data Retention

Data retention policies are implemented to comply with regulations:

- Automatic data purging based on retention periods
- Soft deletion for recoverable data
- Hard deletion for sensitive data that must be removed
