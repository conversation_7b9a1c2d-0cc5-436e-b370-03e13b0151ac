# Deployment and DevOps

This document provides a comprehensive guide for deploying and managing the Aptio platform in different environments.

## Table of Contents

- [Deployment Options](#deployment-options)
- [Docker Deployment](#docker-deployment)
- [Kubernetes Deployment](#kubernetes-deployment)
- [Environment Configuration](#environment-configuration)
- [Scaling Considerations](#scaling-considerations)
- [Monitoring and Logging](#monitoring-and-logging)
- [Backup and Recovery](#backup-and-recovery)
- [CI/CD Pipeline](#cicd-pipeline)
- [Troubleshooting](#troubleshooting)

## Deployment Options

Aptio supports multiple deployment options to accommodate different environments and requirements:

1. **Docker Deployment**: Suitable for development and small production environments
2. **Kubernetes Deployment**: Recommended for larger production environments with high availability requirements
3. **Hybrid Deployment**: Combination of containerized and non-containerized components

### Deployment Architecture

```mermaid
flowchart TB
    subgraph "Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "Frontend"
        FE1[Frontend Instance 1]
        FE2[Frontend Instance 2]
        FE3[Frontend Instance 3]
    end
    
    subgraph "Backend API"
        BE1[Backend Instance 1]
        BE2[Backend Instance 2]
        BE3[Backend Instance 3]
    end
    
    subgraph "Database"
        PG1[(PostgreSQL Primary)]
        PG2[(PostgreSQL Replica)]
    end
    
    subgraph "Cache & Queue"
        RD1[(Redis Primary)]
        RD2[(Redis Replica)]
    end
    
    subgraph "AI Processing"
        AI1[AI Worker 1]
        AI2[AI Worker 2]
    end
    
    subgraph "Storage"
        S3[Object Storage]
    end
    
    subgraph "Monitoring"
        Prom[Prometheus]
        Graf[Grafana]
        Log[Log Aggregator]
    end
    
    LB --> FE1
    LB --> FE2
    LB --> FE3
    
    FE1 --> BE1
    FE2 --> BE2
    FE3 --> BE3
    
    BE1 --> PG1
    BE2 --> PG1
    BE3 --> PG1
    
    BE1 --> RD1
    BE2 --> RD1
    BE3 --> RD1
    
    BE1 --> S3
    BE2 --> S3
    BE3 --> S3
    
    RD1 --> AI1
    RD1 --> AI2
    
    AI1 --> S3
    AI2 --> S3
    
    AI1 --> PG1
    AI2 --> PG1
    
    PG1 --> PG2
    RD1 --> RD2
    
    BE1 --> Prom
    BE2 --> Prom
    BE3 --> Prom
    FE1 --> Prom
    FE2 --> Prom
    FE3 --> Prom
    AI1 --> Prom
    AI2 --> Prom
    
    Prom --> Graf
    
    BE1 --> Log
    BE2 --> Log
    BE3 --> Log
    FE1 --> Log
    FE2 --> Log
    FE3 --> Log
    AI1 --> Log
    AI2 --> Log
```

## Docker Deployment

Docker deployment is suitable for development and small production environments.

### Prerequisites

- Docker v20 or later
- Docker Compose v2 or later
- Domain name and SSL certificates (for production)
- OpenAI API key with sufficient quota
- SMTP server for email notifications
- Storage solution (local, S3, Azure Blob, etc.)

### Docker Compose Configuration

The platform uses Docker Compose for containerized deployment:

```yaml
version: '3.8'

services:
  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    image: aptio-backend:latest
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - backend-node-modules:/app/node_modules
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=********************************************/clm_dev
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend Next.js application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    image: aptio-frontend:latest
    ports:
      - "3001:3001"
    volumes:
      - ./frontend:/app
      - frontend-node-modules:/app/node_modules
    environment:
      - NODE_ENV=production
      - PORT=3001
      - NEXT_PUBLIC_API_URL=http://backend:3000
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL database with pgvector extension
  postgres:
    image: ankane/pgvector:latest
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-pgvector.sql:/docker-entrypoint-initdb.d/init-pgvector.sql
      - ./db/migrations:/docker-entrypoint-initdb.d/migrations
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=clm_dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  # Redis cache and message broker
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  # Nginx for reverse proxy and static file serving
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/www:/usr/share/nginx/html
      - frontend-build:/usr/share/nginx/html/app
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:
  backend-node-modules:
  frontend-node-modules:
  frontend-build:

networks:
  app-network:
    driver: bridge
```

### Deployment Steps

1. **Set Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Build and Start Containers**:
   ```bash
   docker-compose build
   docker-compose up -d
   ```

3. **Run Database Migrations**:
   ```bash
   docker-compose exec backend pnpm prisma migrate deploy
   ```

4. **Verify Deployment**:
   ```bash
   docker-compose ps
   ```

### SSL Configuration

For production, replace the self-signed certificates with valid SSL certificates:

1. Place your SSL certificate and key in `nginx/ssl/`:
   - `server.crt`: SSL certificate
   - `server.key`: SSL private key

2. Update Nginx configuration in `nginx/conf.d/default.conf`:
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name yourdomain.com;

       ssl_certificate /etc/nginx/ssl/server.crt;
       ssl_certificate_key /etc/nginx/ssl/server.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers HIGH:!aNULL:!MD5;
       ssl_prefer_server_ciphers on;

       location / {
           proxy_pass http://frontend:3001;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }

       location /api {
           proxy_pass http://backend:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

## Kubernetes Deployment

For larger deployments, Kubernetes is recommended. Kubernetes manifests are provided in the `kubernetes/` directory.

### Prerequisites

- Kubernetes cluster (v1.22 or later)
- kubectl configured to access the cluster
- Helm v3 or later
- Container registry for storing images
- Persistent storage provider
- Ingress controller

### Deployment Steps

1. **Create Namespace**:
   ```bash
   kubectl apply -f kubernetes/namespace.yaml
   ```

2. **Create ConfigMap and Secrets**:
   ```bash
   # Create ConfigMap
   kubectl apply -f kubernetes/configmap.yaml
   
   # Create Secrets
   kubectl apply -f kubernetes/secrets.yaml
   ```

3. **Deploy Database**:
   ```bash
   kubectl apply -f kubernetes/postgres.yaml
   ```

4. **Deploy Redis**:
   ```bash
   kubectl apply -f kubernetes/redis.yaml
   ```

5. **Deploy Backend**:
   ```bash
   kubectl apply -f kubernetes/backend.yaml
   ```

6. **Deploy Frontend**:
   ```bash
   kubectl apply -f kubernetes/frontend.yaml
   ```

7. **Deploy Ingress**:
   ```bash
   kubectl apply -f kubernetes/ingress.yaml
   ```

8. **Verify Deployment**:
   ```bash
   kubectl get pods -n aptio
   ```

### Kubernetes Manifests

Example backend deployment manifest:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: aptio
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: aptio-backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: production
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: redis-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: openai-api-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: jwt-secret
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: jwt-refresh-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: aptio-secrets
              key: encryption-key
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Environment Configuration

The platform uses environment variables for configuration. Create a `.env` file based on `.env.example`:

### Critical Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development, production) | `production` |
| `OPENAI_API_KEY` | OpenAI API key | `sk-...` |
| `JWT_SECRET` | Secret for JWT tokens | `random-secure-string` |
| `JWT_REFRESH_SECRET` | Secret for refresh tokens | `another-random-secure-string` |
| `ENCRYPTION_KEY` | 32-byte hex key for encryption | `32-byte-hex-string` |
| `DATABASE_URL` | PostgreSQL connection string | `********************************/db` |
| `REDIS_URL` | Redis connection string | `redis://host:6379` |

### Security Recommendations

- Use a secure password generator for secrets
- Store production secrets in a secure vault (HashiCorp Vault, AWS Secrets Manager, etc.)
- Rotate secrets regularly
- Use different secrets for different environments

## Scaling Considerations

### Horizontal Scaling

- **Backend API**: Scale horizontally by increasing replicas
- **Frontend**: Scale horizontally by increasing replicas
- **Database**: Consider read replicas for read-heavy workloads
- **Redis**: Consider Redis Cluster for high availability

### Vertical Scaling

- Increase CPU and memory resources for services
- Optimize database performance with proper indexing
- Configure connection pooling for database

### AI Processing Optimization

- Implement caching for AI responses
- Use batch processing for document analysis
- Configure rate limiting for OpenAI API calls
- Consider using smaller models for less complex tasks

## Monitoring and Logging

### Prometheus and Grafana

The platform includes Prometheus and Grafana for monitoring:

```bash
# Enable monitoring profile
docker-compose --profile monitoring up -d
```

Access Grafana at http://localhost:3000 (default credentials: admin/admin).

### Log Management

Logs are collected and formatted as JSON for easy integration with log management systems:

- **Docker**: Logs are available via `docker-compose logs`
- **Kubernetes**: Use a log aggregator like ELK, Loki, or CloudWatch

### Monitoring Dashboard

The Grafana dashboard provides the following metrics:

- **System Metrics**: CPU, memory, disk usage
- **Application Metrics**: Request rate, error rate, response time
- **Database Metrics**: Query performance, connection count
- **Redis Metrics**: Memory usage, operation count
- **AI Metrics**: Processing time, queue length, error rate

## Backup and Recovery

### Database Backup

```bash
# Backup PostgreSQL database
docker-compose exec postgres pg_dump -U postgres clm_dev > backup.sql

# Restore PostgreSQL database
cat backup.sql | docker-compose exec -T postgres psql -U postgres clm_dev
```

### Volume Backup

```bash
# Backup volumes
docker run --rm -v aptio_postgres-data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/postgres-data.tar.gz -C /source .

# Restore volumes
docker run --rm -v aptio_postgres-data:/target -v $(pwd)/backups:/backup alpine sh -c "rm -rf /target/* && tar -xzf /backup/postgres-data.tar.gz -C /target"
```

### Backup Strategy

1. **Regular Database Backups**: Schedule regular database backups
2. **Volume Backups**: Backup persistent volumes
3. **Configuration Backups**: Backup configuration files
4. **Offsite Storage**: Store backups in a secure offsite location
5. **Backup Testing**: Regularly test backup restoration

## CI/CD Pipeline

The platform uses GitHub Actions for CI/CD:

### CI Pipeline

```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install
      - name: Run linter
        run: pnpm lint
      - name: Run tests
        run: pnpm test

  build:
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install
      - name: Build
        run: pnpm build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build
          path: |
            backend/dist
            frontend/.next
```

### CD Pipeline

```yaml
name: CD

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm install
      - name: Build
        run: pnpm build
      - name: Build and push Docker images
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ghcr.io/yourusername/aptio-backend:latest
            ghcr.io/yourusername/aptio-backend:${{ github.ref_name }}
      - name: Deploy to production
        run: |
          # Deploy to production server
          echo "Deploying to production..."
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL environment variable
   - Verify PostgreSQL is running
   - Check network connectivity

2. **OpenAI API Errors**
   - Verify OPENAI_API_KEY is valid
   - Check API quota and limits
   - Implement retry logic for transient errors

3. **Container Startup Failures**
   - Check container logs: `docker-compose logs <service>`
   - Verify environment variables
   - Check for port conflicts

4. **Performance Issues**
   - Monitor resource usage with Prometheus
   - Check for slow database queries
   - Optimize AI processing with caching

### Debugging Tools

1. **Docker Logs**:
   ```bash
   docker-compose logs -f backend
   ```

2. **Kubernetes Logs**:
   ```bash
   kubectl logs -f deployment/backend -n aptio
   ```

3. **Database Debugging**:
   ```bash
   docker-compose exec postgres psql -U postgres clm_dev
   ```

4. **Redis Debugging**:
   ```bash
   docker-compose exec redis redis-cli
   ```

5. **Application Debugging**:
   ```bash
   docker-compose exec backend pnpm debug
   ```

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs: `docker-compose logs` or service-specific logs
2. Review the GitHub Issues
3. Contact <NAME_EMAIL>
