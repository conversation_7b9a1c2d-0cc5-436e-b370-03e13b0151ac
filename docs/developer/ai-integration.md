# AI Integration

This document provides a comprehensive overview of the AI integration in the Aptio platform, including the RAG architecture, document processing pipeline, and AI services.

## Table of Contents

- [AI Architecture Overview](#ai-architecture-overview)
- [RAG Implementation](#rag-implementation)
- [Document Processing Pipeline](#document-processing-pipeline)
- [AI Services](#ai-services)
- [Prompt Engineering](#prompt-engineering)
- [Vector Database Integration](#vector-database-integration)
- [Security Considerations](#security-considerations)
- [Performance Optimization](#performance-optimization)
- [Testing AI Components](#testing-ai-components)

## AI Architecture Overview

Aptio integrates AI throughout its architecture to provide intelligent features for contract and license management. The AI functionality is built on several key technologies:

- **OpenAI API**: For large language model capabilities
- **LangChain**: For building AI applications and chains
- **Vector Embeddings**: For semantic search and retrieval
- **PostgreSQL with pgvector**: For storing and querying vector embeddings

### High-Level AI Architecture

```mermaid
flowchart TB
    subgraph "User Interface"
        UI[Web Application]
    end

    subgraph "API Layer"
        API[API Gateway]
    end

    subgraph "Application Services"
        ContractService[Contract Service]
        LicenseService[License Service]
        AnalyticsService[Analytics Service]
    end

    subgraph "AI Services"
        RAGService[RAG Service]
        ChatService[Chat Service]
        DocumentAnalyzer[Document Analyzer]
    end

    subgraph "AI Processing"
        AIQueue[AI Job Queue]
        AIProcessor[Secure AI Processor]
        AIResults[AI Results Repository]
    end

    subgraph "Data Storage"
        VectorDB[Vector Database]
        DocumentDB[Document Storage]
    end

    UI --> API
    API --> ContractService
    API --> LicenseService
    API --> AnalyticsService

    ContractService --> RAGService
    ContractService --> DocumentAnalyzer
    LicenseService --> RAGService
    AnalyticsService --> RAGService

    RAGService --> VectorDB
    ChatService --> RAGService
    DocumentAnalyzer --> AIQueue

    AIQueue --> AIProcessor
    AIProcessor --> DocumentDB
    AIProcessor --> AIResults
    AIResults --> VectorDB
```

## RAG Implementation

The platform implements Retrieval-Augmented Generation (RAG) to provide accurate, context-aware responses based on the organization's documents.

### RAG Flow

1. **Document Processing**:

   - Documents are split into chunks
   - Chunks are converted to vector embeddings
   - Embeddings are stored in the vector database

2. **Query Processing**:

   - User query is converted to a vector embedding
   - Similar document chunks are retrieved from the vector database
   - Retrieved chunks are used as context for the language model

3. **Response Generation**:
   - Language model generates a response based on the query and retrieved context
   - Response is returned to the user with citations to source documents

### RAG Service Implementation

```typescript
export class RAGService {
  constructor(
    private configService: ConfigService,
    private embeddingService: EmbeddingService,
    private vectorRepository: VectorRepository
  ) {
    this.llm = new ChatOpenAI({
      modelName: "gpt-4",
      temperature: 0.2,
      apiKey: this.configService.get("OPENAI_API_KEY"),
    });
  }

  async query(
    query: string,
    options?: RAGQueryOptions
  ): Promise<RAGQueryResult> {
    // Convert query to embedding
    const queryEmbedding = await this.embeddingService.generateEmbedding(query);

    // Retrieve similar documents
    const similarDocuments = await this.vectorRepository.findSimilar(
      options?.tenantId || "default",
      queryEmbedding,
      options?.limit || 5,
      options?.metadata
    );

    // Generate prompt with context
    const prompt = this.generatePromptWithContext(query, similarDocuments);

    // Generate response using LLM
    const response = await this.llm.call(prompt);

    // Return result with citations
    return {
      query,
      answer: response,
      sources: this.extractSources(similarDocuments),
    };
  }

  private generatePromptWithContext(
    query: string,
    documents: VectorSearchResult[]
  ): string {
    const context = documents
      .map((doc, index) => `[Source ${index + 1}] ${doc.content}`)
      .join("\n\n");

    return `
      You are an AI assistant for a contract and license management platform.
      Answer the following question based on the provided context.
      
      Context:
      ${context}
      
      Question:
      ${query}
      
      Provide a comprehensive answer based only on the context provided.
      If the context doesn't contain the information needed to answer the question,
      say "I don't have enough information to answer this question."
      
      Include citations to the source documents in your answer using [Source X] notation.
    `;
  }

  private extractSources(documents: VectorSearchResult[]): RAGSource[] {
    return documents.map((doc, index) => ({
      sourceId: index + 1,
      documentId: doc.documentId,
      content: doc.content.substring(0, 200) + "...",
      metadata: doc.metadata,
      similarity: doc.similarity,
    }));
  }
}
```

## Document Processing Pipeline

The `DocumentProcessingPipeline` provides end-to-end document processing:

### Processing Flow

1. **Document Classification**: Identify the type of document
2. **Information Extraction**: Extract key information from the document
3. **RAG Processing**: Process the document for RAG
4. **Analysis**: Analyze the document based on its type
5. **Database Storage**: Store the document and its metadata

### Pipeline Implementation

```typescript
export class DocumentProcessingPipeline {
  constructor(
    private configService: ConfigService,
    private documentProcessingService: DocumentProcessingService,
    private contractAnalysisService: ContractAnalysisService,
    private licenseManagementService: LicenseManagementService,
    private prisma: PrismaClient
  ) {}

  async processDocument(
    tenantId: string,
    documentText: string,
    fileName?: string
  ): Promise<DocumentProcessingResult> {
    const startTime = Date.now();
    const documentId = uuidv4();

    // Classify document
    const classification = await this.classifyDocument(documentText, fileName);

    // Extract information
    const extraction = await this.extractDocumentInformation(
      documentText,
      classification.documentType
    );

    // Process for RAG
    await this.documentProcessingService.processDocument(
      tenantId,
      documentId,
      documentText,
      {
        isLegalDocument: true,
        metadata: {
          ...extraction,
          documentType: classification.documentType,
          fileName,
        },
      }
    );

    // Analyze document based on type
    let analysis: any;
    if (classification.documentType === DocumentType.CONTRACT) {
      analysis = await this.contractAnalysisService.analyzeContract(
        documentId,
        documentText
      );
    } else if (classification.documentType === DocumentType.LICENSE) {
      analysis = await this.licenseManagementService.analyzeLicense(
        documentId,
        documentText
      );
    }

    // Create document record in database
    await this.createDocumentRecord(
      documentId,
      tenantId,
      documentText,
      classification,
      extraction,
      analysis
    );

    return {
      documentId,
      tenantId,
      classification,
      extraction,
      analysis,
      processingTimeMs: Date.now() - startTime,
    };
  }

  private async classifyDocument(
    documentText: string,
    fileName?: string
  ): Promise<DocumentClassification> {
    // Implementation details
  }

  private async extractDocumentInformation(
    documentText: string,
    documentType: DocumentType
  ): Promise<DocumentExtraction> {
    // Implementation details
  }

  private async createDocumentRecord(
    documentId: string,
    tenantId: string,
    documentText: string,
    classification: DocumentClassification,
    extraction: DocumentExtraction,
    analysis: any
  ): Promise<void> {
    // Implementation details
  }
}
```

## AI Services

Aptio provides several AI services for different use cases:

### RAGService

The `RAGService` provides Retrieval-Augmented Generation capabilities:

- **Methods**:
  - `query(query, options)`: Query documents with RAG
  - `queryLegal(query, options)`: Specialized query for legal documents
  - `generatePromptWithContext(query, documents)`: Generate prompt with document context

### ChatService

The `ChatService` manages chat conversations with context:

- **Methods**:
  - `createConversation(userId, title, metadata)`: Create a new conversation
  - `addMessage(conversationId, content, role)`: Add a message to a conversation
  - `generateResponse(conversationId, message)`: Generate a response using RAG

### ContractAnalysisService

The `ContractAnalysisService` provides AI-powered Agreement Analysis:

- **Methods**:
  - `analyzeContract(contractId, contractText)`: Analyze a contract
  - `extractObligations(contractId)`: Extract obligations from a contract
  - `compareContracts(contractId1, contractId2)`: Compare two contracts
  - `generateContract(params)`: Generate a contract

### LicenseManagementService

The `LicenseManagementService` provides AI-powered license management:

- **Methods**:
  - `analyzeLicense(licenseId, licenseText)`: Analyze a license
  - `generateComplianceReport(licenseId, usageData)`: Generate a compliance report
  - `recommendOptimizations(licenseIds, usageData)`: Recommend license optimizations
  - `predictLicenseNeeds(tenantId, historicalData, forecastMonths)`: Predict future license needs

### AnalyticsService

The `AnalyticsService` provides AI-powered analytics:

- **Methods**:
  - `generateDashboardSummary(tenantId)`: Generate a dashboard summary
  - `generateReport(tenantId, reportType, timeframe)`: Generate a report
  - `answerBusinessQuestion(tenantId, question)`: Answer a business question

### NotificationService

The `AINotificationService` provides AI-powered notifications:

- **Methods**:
  - `generateContractExpirationNotifications(tenantId)`: Generate contract expiration notifications
  - `generateLicenseExpirationNotifications(tenantId)`: Generate license expiration notifications
  - `generateComplianceNotifications(tenantId)`: Generate compliance notifications
  - `generatePersonalizedDigest(userId, notifications)`: Generate a personalized notification digest

## Prompt Engineering

The platform uses carefully crafted prompts for different AI tasks:

### RAG Prompts

```typescript
const ragPrompt = PromptTemplate.fromTemplate(`
  You are an AI assistant for a contract and license management platform.
  Answer the following question based on the provided context.
  
  Context:
  {context}
  
  Question:
  {query}
  
  Provide a comprehensive answer based only on the context provided.
  If the context doesn't contain the information needed to answer the question,
  say "I don't have enough information to answer this question."
  
  Include citations to the source documents in your answer using [Source X] notation.
`);
```

### Legal Document Prompts

```typescript
const legalDocumentPrompt = PromptTemplate.fromTemplate(`
  You are an expert legal analyst specializing in contracts and licenses.
  Analyze the following legal document and extract key information.
  
  Document:
  {document}
  
  Extract the following information:
  - Document type (contract, license, amendment, etc.)
  - Parties involved
  - Effective date
  - Expiration date
  - Key terms and conditions
  - Obligations and responsibilities
  - Risk factors
  
  {format_instructions}
`);
```

## Vector Database Integration

The platform uses PostgreSQL with pgvector extension for storing and querying vector embeddings.

### Schema

The vector database schema is defined in `init-pgvector.sql`:

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create vector embeddings table
CREATE TABLE IF NOT EXISTS vector_embeddings (
  id UUID PRIMARY KEY,
  tenant_id UUID NOT NULL,
  document_id UUID NOT NULL,
  chunk_index INTEGER NOT NULL,
  content TEXT NOT NULL,
  embedding VECTOR(1536) NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(document_id, chunk_index)
);

-- Create index for similarity search
CREATE INDEX IF NOT EXISTS vector_embeddings_embedding_idx ON vector_embeddings
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create index for tenant_id
CREATE INDEX IF NOT EXISTS vector_embeddings_tenant_id_idx ON vector_embeddings(tenant_id);

-- Create index for document_id
CREATE INDEX IF NOT EXISTS vector_embeddings_document_id_idx ON vector_embeddings(document_id);
```

### VectorRepository

The `VectorRepository` provides methods for storing and retrieving vector embeddings:

- **Methods**:
  - `storeEmbedding(embedding, content, metadata)`: Store a vector embedding
  - `findSimilar(embedding, limit, metadata)`: Find similar documents
  - `deleteEmbeddings(documentId)`: Delete embeddings for a document
  - `deleteAllEmbeddings(tenantId)`: Delete all embeddings for a tenant

## Security Considerations

### Isolated AI Processing

AI processing is isolated from the main application to ensure security:

1. **Containerized Processing**: AI operations run in isolated containers
2. **Read-Only Access**: AI processors have read-only access to documents
3. **Ephemeral Processing**: No persistent storage of sensitive data
4. **Audit Logging**: All AI operations are logged for audit purposes

### Data Sanitization

Input and output data is sanitized to prevent security issues:

```typescript
export class DataSanitizer {
  static sanitizeInput(input: string): string {
    // Remove potential injection patterns
    return input.replace(/[^\w\s.,;:?!()[\]{}'"<>@#$%^&*+=\-\/\\]/g, "").trim();
  }

  static sanitizeOutput(output: string): string {
    // Remove potentially harmful content
    return output
      .replace(/[^\w\s.,;:?!()[\]{}'"<>@#$%^&*+=\-\/\\]/g, "")
      .trim();
  }
}
```

### Prompt Injection Prevention

The platform implements measures to prevent prompt injection attacks:

1. **Input Validation**: All user inputs are validated
2. **Prompt Separation**: Clear separation between instructions and user input
3. **Output Filtering**: Responses are filtered for potentially harmful content

## Performance Optimization

### Embedding Caching

The platform caches embeddings to avoid regenerating them for the same content:

```typescript
export class EmbeddingService {
  private cache: Map<string, number[]> = new Map();

  async generateEmbedding(text: string): Promise<number[]> {
    // Check cache
    const cacheKey = this.generateCacheKey(text);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Generate embedding
    const embedding = await this.openai.embeddings.create({
      model: "text-embedding-ada-002",
      input: text,
    });

    // Cache embedding
    this.cache.set(cacheKey, embedding.data[0].embedding);

    return embedding.data[0].embedding;
  }

  private generateCacheKey(text: string): string {
    return crypto.createHash("md5").update(text).digest("hex");
  }
}
```

### Batch Processing

The platform uses batch processing for generating embeddings:

```typescript
async generateEmbeddingsForChunks(chunks: string[]): Promise<number[][]> {
  // Process in batches of 20
  const batchSize = 20;
  const embeddings: number[][] = [];

  for (let i = 0; i < chunks.length; i += batchSize) {
    const batch = chunks.slice(i, i + batchSize);
    const batchEmbeddings = await Promise.all(
      batch.map(chunk => this.generateEmbedding(chunk))
    );
    embeddings.push(...batchEmbeddings);
  }

  return embeddings;
}
```

## Testing AI Components

### Unit Testing

Example of unit testing the `RAGService`:

```typescript
describe("RAGService", () => {
  let ragService: RAGService;
  let embeddingService: EmbeddingService;
  let vectorRepository: VectorRepository;
  let llm: ChatOpenAI;

  beforeEach(() => {
    embeddingService = mock<EmbeddingService>();
    vectorRepository = mock<VectorRepository>();
    llm = mock<ChatOpenAI>();

    ragService = new RAGService(
      { get: () => "test-api-key" } as ConfigService,
      embeddingService,
      vectorRepository
    );
  });

  it("should query documents and return results", async () => {
    // Arrange
    const query = "What is the contract expiration date?";
    const queryEmbedding = [0.1, 0.2, 0.3];
    const similarDocuments = [
      {
        id: "1",
        content: "The contract expires on December 31, 2023.",
        metadata: { documentId: "doc1" },
      },
    ];

    when(embeddingService.generateEmbedding)
      .calledWith(query)
      .mockResolvedValue(queryEmbedding);
    when(vectorRepository.findSimilar)
      .calledWith(queryEmbedding, 5, undefined)
      .mockResolvedValue(similarDocuments);
    when(llm.call).mockResolvedValue(
      "The contract expiration date is December 31, 2023. [Source 1]"
    );

    // Act
    const result = await ragService.query(query);

    // Assert
    expect(result.answer).toBe(
      "The contract expiration date is December 31, 2023. [Source 1]"
    );
    expect(result.sources).toHaveLength(1);
    expect(result.sources[0].documentId).toBe("doc1");
  });
});
```

### Integration Testing

Example of integration testing the document processing pipeline:

```typescript
describe("DocumentProcessingPipeline Integration", () => {
  let pipeline: DocumentProcessingPipeline;
  let prisma: PrismaClient;

  beforeAll(async () => {
    // Set up test database
    prisma = new PrismaClient();
    await prisma.$connect();

    // Initialize services
    const configService = new ConfigService();
    const embeddingService = new EmbeddingService(configService);
    const vectorRepository = new VectorRepository(configService);
    const documentProcessingService = new DocumentProcessingService(
      configService,
      embeddingService,
      vectorRepository
    );
    const contractAnalysisService = new ContractAnalysisService(
      configService,
      new RAGService(configService, embeddingService, vectorRepository),
      documentProcessingService
    );
    const licenseManagementService = new LicenseManagementService(
      configService,
      new RAGService(configService, embeddingService, vectorRepository),
      documentProcessingService
    );

    pipeline = new DocumentProcessingPipeline(
      configService,
      documentProcessingService,
      contractAnalysisService,
      licenseManagementService,
      prisma
    );
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  it("should process a Agreement Document end-to-end", async () => {
    // Arrange
    const tenantId = "test-tenant";
    const contractText = `
      CONTRACT AGREEMENT
      
      This agreement is made on January 1, 2023, between Company A and Company B.
      
      The term of this agreement is 12 months, ending on December 31, 2023.
      
      Company A agrees to provide services to Company B for $10,000 per month.
    `;

    // Act
    const result = await pipeline.processDocument(
      tenantId,
      contractText,
      "contract.txt"
    );

    // Assert
    expect(result.classification.documentType).toBe(DocumentType.CONTRACT);
    expect(result.extraction.parties).toContain("Company A");
    expect(result.extraction.parties).toContain("Company B");
    expect(result.extraction.effectiveDate).toBe("January 1, 2023");
    expect(result.extraction.expirationDate).toBe("December 31, 2023");

    // Verify document was stored in database
    const contract = await prisma.contract.findUnique({
      where: { id: result.documentId },
    });
    expect(contract).not.toBeNull();
    expect(contract!.name).toContain("contract");

    // Verify embeddings were created
    const embeddings = await prisma.vectorEmbedding.findMany({
      where: { documentId: result.documentId },
    });
    expect(embeddings.length).toBeGreaterThan(0);
  });
});
```
