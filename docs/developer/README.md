# Developer Documentation

This documentation provides comprehensive information for developers working on the Aptio platform.

## Table of Contents

- [Architecture Overview](./architecture.md)
- [Development Environment Setup](./setup.md)
- [Database Design](./database.md)
- [AI Integration](./ai-integration.md)
- [Security Implementation](./security.md)
- [Deployment and DevOps](./deployment.md)
- [AI Components](./ai-components.md)
- [API Reference](#api-reference)
- [Testing](#testing)
- [Contribution Guidelines](#contribution-guidelines)

## Architecture Overview

Aptio is built as a modular monolith following Domain-Driven Design (DDD) principles. The application is designed to be scalable and potentially extractable into microservices in the future.

### Key Components

- **Frontend**: Next.js application with React, TailwindCSS, and TypeScript
- **Backend**: Express.js API with TypeScript, Prisma ORM, and PostgreSQL
- **AI Layer**: LangChain, OpenAI, and vector embeddings for RAG capabilities
- **Infrastructure**: <PERSON><PERSON>, <PERSON>er Compose, Nginx, Redis, and PostgreSQL with pgvector

### Architecture Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │────▶│  Backend API    │────▶│  Database       │
│  (Next.js)      │     │  (Express)      │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │  ▲
                               │  │
                               ▼  │
                        ┌─────────────────┐     ┌─────────────────┐
                        │                 │     │                 │
                        │  AI Services    │────▶│  OpenAI API     │
                        │  (LangChain)    │     │                 │
                        │                 │     │                 │
                        └─────────────────┘     └─────────────────┘
                               │  ▲
                               │  │
                               ▼  │
                        ┌─────────────────┐
                        │                 │
                        │  Vector DB      │
                        │  (pgvector)     │
                        │                 │
                        └─────────────────┘
```

## Development Environment Setup

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- OpenAI API key

### Setup Steps

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/aptio.git
   cd aptio
   ```

2. **Configure environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your development configuration, including:
   - `OPENAI_API_KEY`: Your OpenAI API key
   - `DATABASE_URL`: PostgreSQL connection string
   - `JWT_SECRET` and `JWT_REFRESH_SECRET`: Secrets for JWT tokens

3. **Start the development environment**

   ```bash
   # Make the deployment script executable
   chmod +x deploy.sh

   # Deploy with development profile
   NODE_ENV=development ./deploy.sh
   ```

4. **Access the development environment**

   - Frontend: http://localhost:3001
   - Backend API: http://localhost:3000
   - PgAdmin: http://localhost:5050 (username: <EMAIL>, password: admin)

### Development Workflow

1. **Backend Development**

   The backend code is located in the `backend/` directory. The server will automatically reload when you make changes to the code.

   ```bash
   # Run backend tests
   cd backend
   npm test

   # Generate Prisma client after schema changes
   npx prisma generate

   # Run database migrations
   npx prisma migrate dev
   ```

2. **Frontend Development**

   The frontend code is located in the `frontend/` directory. The Next.js development server will automatically reload when you make changes to the code.

   ```bash
   # Run frontend tests
   cd frontend
   npm test

   # Build for production
   npm run build
   ```

## Project Structure

### Backend Structure

```
backend/
├── prisma/                # Database schema and migrations
├── src/
│   ├── api/               # API layer
│   │   ├── controllers/   # Request handlers
│   │   ├── middleware/    # Express middleware
│   │   └── routes/        # API routes
│   ├── domain/            # Domain layer (business logic)
│   │   ├── contracts/     # Contract domain
│   │   ├── licenses/      # License domain
│   │   ├── notifications/ # Notification domain
│   │   ├── tenants/       # Tenant domain
│   │   └── shared/        # Shared domain objects
│   ├── infrastructure/    # Infrastructure layer
│   │   ├── ai/            # AI services
│   │   ├── repositories/  # Data access
│   │   ├── services/      # External services
│   │   └── logging/       # Logging
│   ├── server.ts          # Server entry point
│   └── app.ts             # Express application
└── tests/                 # Tests
```

### Frontend Structure

```
frontend/
├── public/                # Static assets
├── src/
│   ├── components/        # React components
│   │   ├── auth/          # Authentication components
│   │   ├── chat/          # Chat interface components
│   │   ├── contracts/     # Contract-related components
│   │   ├── layouts/       # Layout components
│   │   └── ui/            # UI components
│   ├── contexts/          # React contexts
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility functions
│   ├── pages/             # Next.js pages
│   └── styles/            # CSS styles
└── tests/                 # Tests
```

## AI Components

The AI functionality is implemented in the `backend/src/infrastructure/ai/` directory:

- **RAGService**: Implements Retrieval-Augmented Generation for document Q&A
- **ChatService**: Manages chat conversations with context from documents
- **EmbeddingService**: Generates and manages vector embeddings
- **DocumentProcessingService**: Processes documents for RAG
- **ContractAnalysisService**: Analyzes contracts for insights and risks
- **LicenseManagementService**: Manages license analysis and optimization
- **AnalyticsService**: Generates insights and analytics
- **NotificationService**: Generates intelligent notifications
- **OnboardingService**: Provides personalized onboarding
- **DocumentProcessingPipeline**: End-to-end document processing

### Vector Database

The platform uses PostgreSQL with pgvector extension for storing and querying vector embeddings. The vector database schema is defined in `init-pgvector.sql`.

### LangChain Integration

The platform uses LangChain for building AI applications:

- **Text Splitting**: `RecursiveCharacterTextSplitter` for document chunking
- **Embeddings**: `OpenAIEmbeddings` for generating embeddings
- **Chains**: `RunnableSequence` for creating processing chains
- **Output Parsing**: `StructuredOutputParser` for structured outputs

## API Reference

The API documentation is available at `/api/docs` when running the development server.

### Key API Endpoints

- **Authentication**: `/api/auth/*`
- **Contracts**: `/api/contracts/*`
- **Licenses**: `/api/licenses/*`
- **RAG**: `/api/rag/*`
- **Chat**: `/api/chat/*`
- **AI**: `/api/ai/*`
- **Analytics**: `/api/analytics/*`

## Testing

### Backend Testing

```bash
cd backend
npm test                 # Run all tests
npm test -- --watch      # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
```

### Frontend Testing

```bash
cd frontend
npm test                 # Run all tests
npm test -- --watch      # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
```

### End-to-End Testing

```bash
npm run test:e2e         # Run end-to-end tests
```

## Contribution Guidelines

### Code Style

The project uses ESLint and Prettier for code formatting:

```bash
npm run lint             # Run linter
npm run format           # Format code
```

### Git Workflow

1. Create a feature branch from `main`
2. Make your changes
3. Write tests for your changes
4. Run linter and tests
5. Submit a pull request

### Pull Request Process

1. Ensure all tests pass
2. Update documentation if necessary
3. Get at least one code review
4. Squash commits before merging

### Commit Message Format

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
feat: add new feature
fix: fix bug
docs: update documentation
style: format code
refactor: refactor code
test: add tests
chore: update build scripts
```
