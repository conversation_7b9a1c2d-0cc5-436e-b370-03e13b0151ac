# Development Environment Setup

This guide provides instructions for setting up the Aptio development environment.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Docker Development Environment](#docker-development-environment)
- [Database Setup](#database-setup)
- [Environment Configuration](#environment-configuration)
- [Running the Application](#running-the-application)
- [Development Tools](#development-tools)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up the development environment, ensure you have the following installed:

- **Node.js**: v18 or later
- **npm** or **pnpm**: v8 or later (pnpm is preferred)
- **Docker**: v20 or later
- **Docker Compose**: v2 or later
- **Git**: v2 or later
- **PostgreSQL**: v16 or later (optional if using Docker)
- **Redis**: v7 or later (optional if using Docker)
- **OpenAI API Key**: Required for AI features

## Local Development Setup

### Clone the Repository

```bash
git clone https://github.com/yourusername/aptio.git
cd aptio
```

### Install Dependencies

```bash
# Install pnpm if not already installed
npm install -g pnpm

# Install backend dependencies
cd backend
pnpm install

# Install frontend dependencies
cd ../frontend
pnpm install

# Return to root directory
cd ..
```

### Set Up Environment Variables

Create `.env` files for both backend and frontend:

```bash
# Create backend .env file
cp backend/.env.example backend/.env

# Create frontend .env file
cp frontend/.env.example frontend/.env
```

Edit the `.env` files to set the required environment variables:

#### Backend `.env`

```
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/clm_dev
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_api_key
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
ENCRYPTION_KEY=your_32_byte_hex_encryption_key
```

#### Frontend `.env`

```
NEXT_PUBLIC_API_URL=http://localhost:3000
```

## Docker Development Environment

For a containerized development environment, use Docker Compose:

### Start the Development Environment

```bash
# Start all services
docker-compose up -d

# Start with development profile (includes pgAdmin)
docker-compose --profile dev up -d
```

### Stop the Development Environment

```bash
docker-compose down
```

### Rebuild Containers

```bash
docker-compose build
```

## Database Setup

### Using Local PostgreSQL

If you're using a local PostgreSQL installation:

1. Create the database:

```bash
createdb clm_dev
```

2. Enable the pgvector extension:

```bash
psql -d clm_dev -c "CREATE EXTENSION IF NOT EXISTS vector;"
```

3. Run database migrations:

```bash
cd backend
pnpm prisma migrate dev
```

### Using Docker PostgreSQL

If you're using the Docker PostgreSQL container:

1. The database and pgvector extension are automatically set up
2. Run database migrations:

```bash
docker-compose exec backend pnpm prisma migrate dev
```

## Environment Configuration

### Critical Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development, production) | `development` |
| `OPENAI_API_KEY` | OpenAI API key | `sk-...` |
| `JWT_SECRET` | Secret for JWT tokens | `random-secure-string` |
| `JWT_REFRESH_SECRET` | Secret for refresh tokens | `another-random-secure-string` |
| `ENCRYPTION_KEY` | 32-byte hex key for encryption | `32-byte-hex-string` |
| `DATABASE_URL` | PostgreSQL connection string | `********************************/db` |
| `REDIS_URL` | Redis connection string | `redis://host:6379` |

### Generating Secure Keys

Use the following commands to generate secure keys:

```bash
# Generate JWT secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Generate encryption key
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
```

## Running the Application

### Running the Backend

```bash
cd backend

# Development mode with hot reload
pnpm dev

# Production mode
pnpm build
pnpm start
```

### Running the Frontend

```bash
cd frontend

# Development mode with hot reload
pnpm dev

# Production mode
pnpm build
pnpm start
```

### Running Both Services

You can use the provided script to run both services:

```bash
# Make the script executable
chmod +x run-dev.sh

# Run the development environment
./run-dev.sh
```

## Development Tools

### Database Management

#### PgAdmin

If you're using Docker with the dev profile, PgAdmin is available at http://localhost:5050:

- **Email**: <EMAIL>
- **Password**: admin

To connect to the PostgreSQL database:

1. Add a new server
2. Set the connection details:
   - **Host**: postgres
   - **Port**: 5432
   - **Database**: clm_dev
   - **Username**: postgres
   - **Password**: postgres

#### Prisma Studio

Prisma Studio provides a visual interface for the database:

```bash
cd backend
pnpm prisma studio
```

Access Prisma Studio at http://localhost:5555

### API Testing

#### Postman

A Postman collection is available in the `docs/postman` directory. Import it into Postman to test the API endpoints.

#### Swagger UI

Swagger UI is available at http://localhost:3000/api-docs when running the backend in development mode.

### Monitoring

#### Prometheus and Grafana

If you're using Docker with the monitoring profile, Prometheus and Grafana are available:

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000
  - **Username**: admin
  - **Password**: admin

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL environment variable
   - Verify PostgreSQL is running
   - Check network connectivity

2. **OpenAI API Errors**
   - Verify OPENAI_API_KEY is valid
   - Check API quota and limits
   - Implement retry logic for transient errors

3. **Container Startup Failures**
   - Check container logs: `docker-compose logs <service>`
   - Verify environment variables
   - Check for port conflicts

4. **Prisma Migration Errors**
   - Reset the database: `pnpm prisma migrate reset`
   - Check for schema conflicts
   - Verify database connection

5. **Node.js Version Issues**
   - Use nvm to switch Node.js versions: `nvm use 18`
   - Check for compatibility issues with dependencies
   - Update Node.js to the latest LTS version

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs: `docker-compose logs` or service-specific logs
2. Review the GitHub Issues
3. Contact the development team
