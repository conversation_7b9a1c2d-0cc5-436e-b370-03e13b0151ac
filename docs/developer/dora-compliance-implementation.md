# DORA Compliance Analysis Implementation

## Overview

The DORA (Digital Operational Resilience Act) compliance analysis feature provides automated assessment of contract compliance with EU DORA regulations. This system uses AI-powered document analysis to identify and evaluate DORA-relevant clauses in contracts.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Database Schema](#database-schema)
3. [Backend Implementation](#backend-implementation)
4. [Frontend Implementation](#frontend-implementation)
5. [API Endpoints](#api-endpoints)
6. [Data Flow](#data-flow)
7. [AI Integration](#ai-integration)
8. [Configuration](#configuration)
9. [Troubleshooting](#troubleshooting)
10. [Future Enhancements](#future-enhancements)

## Architecture Overview

The DORA compliance system follows a multi-tier architecture:

```
Frontend (React/Next.js)
    ↓
API Layer (Express.js)
    ↓
Service Layer (ContractAIService)
    ↓
AI Provider (Google Gemini)
    ↓
Database (PostgreSQL with Prisma)
```

### Key Components

- **Frontend**: TabbedExtractionDisplay component with compliance tab
- **Backend**: ContractController with DORA-specific endpoints
- **AI Service**: ContractAIService with DORA analysis methods
- **Database**: ContractExtraction table with complianceAnalysis JSON field
- **AI Provider**: Google Gemini 2.0 Flash for document analysis

## Database Schema

### ContractExtraction Table

The DORA compliance data is stored in the `complianceAnalysis` JSON field:

```sql
model ContractExtraction {
  id                String   @id @default(uuid())
  contractId        String   @unique
  tenantId          String
  fixedFields       Json
  dynamicFields     Json
  specialFields     Json
  extractionDate    DateTime @default(now())
  extractionVersion String   @default("1.0")
  overallConfidence Float?
  processingTimeMs  Int?
  metadata          Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  folderId          String?
  analysisFields    Json?
  documentSummary   Json?
  complianceAnalysis Json?    // DORA compliance data stored here
}
```

### Compliance Analysis JSON Structure

```typescript
interface ComplianceAnalysis {
  dora?: DORACompliance;
  esg?: any; // Future implementation
  extractionDate?: Date;
  processingTimeMs?: number;
  overallRisk?: "Low" | "Medium" | "High";
}

interface DORACompliance {
  criticalFunctions: ComplianceClause;
  subcontracting: ComplianceClause;
  auditRights: ComplianceClause;
  terminationRights: ComplianceClause;
  exitStrategy: ComplianceClause;
  incidentNotification: ComplianceClause;
  dataLocation: ComplianceClause;
  businessContinuity: ComplianceClause;
  securityMeasures: ComplianceClause;
  regulatoryCooperation: ComplianceClause;
  liability: ComplianceClause;
  serviceLevelAgreements: ComplianceClause;
}

interface ComplianceClause {
  present: "Yes" | "No" | "Unclear";
  summary: string;
  complianceRisk: "Low" | "Medium" | "High";
}
```

## Backend Implementation

### ContractController

DORA compliance generation uses a single optimized endpoint:

#### DORA Compliance Generation (OCR-based)

```typescript
POST /api/contracts/:id/generate-dora-compliance
```

This endpoint automatically uses OCR text stored in the database for faster and more accurate analysis.

### ContractAIService

Key methods for DORA compliance:

#### generateDORAComplianceAnalysis()

```typescript
async generateDORAComplianceAnalysis(
  documentBuffer: Buffer,
  fileName: string,
  contractTitle: string
): Promise<any>
```

#### updateComplianceAnalysis()

```typescript
async updateComplianceAnalysis(
  contractId: string,
  tenantId: string,
  complianceAnalysis: any
): Promise<void>
```

### Route Configuration

```typescript
// OCR-based endpoint (uses stored OCR text from database)
router.post(
  "/:id/generate-dora-compliance",
  requirePermissions(["contracts:write"]),
  contractController.generateDORACompliance.bind(contractController)
);
```

## Frontend Implementation

### TabbedExtractionDisplay Component

The main UI component for displaying DORA compliance analysis:

#### Key Features:

- **Compliance Tab**: Dedicated tab for compliance analysis
- **Generate Button**: Triggers DORA compliance analysis
- **Summary Overview**: Visual statistics and compliance percentage
- **Detailed Clauses**: Individual clause analysis with risk indicators
- **Visual Indicators**: Color-coded status badges with icons

#### Component Structure:

```typescript
// Main compliance generation handler
const handleGenerateCompliance = async () => {
  const response = await contractService.generateDORACompliance(contractId);
  // Refresh data and show results
};

// Render DORA compliance results
const renderDORACompliance = (dora: any) => {
  // Calculate statistics
  // Render summary section
  // Render individual clauses
};
```

### ContractService

Frontend service handling API communication:

```typescript
async generateDORACompliance(contractId: string): Promise<{
  success: boolean;
  compliance: any;
  message: string;
}> {
  // Uses OCR text stored in database for fast and accurate analysis
  return await apiClient.post(`/api/contracts/${contractId}/generate-dora-compliance`);
}
```

## API Endpoints

### Generate DORA Compliance (OCR-based)

```http
POST /api/contracts/{contractId}/generate-dora-compliance
Authorization: Bearer {token}
Content-Type: application/json

Response:
{
  "success": true,
  "compliance": {
    "criticalFunctions": {
      "present": "Yes",
      "summary": "Contract includes critical function definitions...",
      "complianceRisk": "Low"
    },
    // ... other clauses
  },
  "message": "DORA compliance analysis generated successfully"
}
```

**Note**: This endpoint automatically uses OCR text stored in the database, eliminating the need for document uploads and providing faster, more consistent analysis.

## Data Flow

### 1. User Initiates Analysis

```
User clicks "Check Compliance" → Frontend triggers generateDORACompliance()
```

### 2. OCR Text Retrieval

```
Backend retrieves OCR text from database → No document processing needed
```

### 3. AI Processing

```
Backend uses OCR text → ContractAIService processes → Gemini AI analyzes
```

### 4. Data Storage

```
AI returns analysis → Backend stores in complianceAnalysis field → Returns to frontend
```

### 5. UI Update

```
Frontend receives response → Refreshes extraction data → Updates compliance tab
```

## AI Integration

### Google Gemini Configuration

The system uses Google Gemini 2.0 Flash for document analysis:

```typescript
// API Configuration
const apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent";

// Generation Config
generationConfig: {
  temperature: 0.1,    // Low temperature for deterministic output
  topP: 0.95,
  topK: 40,
  maxOutputTokens: 4096
}
```

### DORA Analysis Prompt

The AI prompt instructs Gemini to analyze 12 specific DORA compliance categories:

1. **Critical/Important Functions**
2. **Subcontracting**
3. **Audit, Access & Inspection Rights**
4. **Termination Rights**
5. **Exit Strategy/Transition Support**
6. **Incident Notification**
7. **Data Location & Processing**
8. **Business Continuity & Disaster Recovery**
9. **Security Measures**
10. **Regulatory Cooperation**
11. **Liability & Insurance**
12. **Service Level Agreements**

### Response Format

The AI returns structured JSON with each clause containing:

- `present`: "Yes" | "No" | "Unclear"
- `summary`: Detailed explanation of findings
- `complianceRisk`: "Low" | "Medium" | "High"

## Configuration

### Environment Variables

```bash
# Required for AI functionality
GEMINI_API_KEY=your_gemini_api_key_here

# Database configuration
DATABASE_URL=postgresql://user:password@localhost:5432/database_name
```

### File Upload Limits

```typescript
// Multer configuration in ContractController
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
    files: 20,
  },
  fileFilter: (_, file, cb) => {
    // Supported formats: PDF, DOCX, DOC, TXT, RTF, ODT, MD, HTML
  },
});
```

### Permissions

Users need `contracts:write` permission to generate DORA compliance analysis.

## Troubleshooting

### Common Issues

#### 1. Document Reading Errors

**Problem**: "Failed to retrieve contract document for analysis"
**Solution**: Use the document upload endpoint instead of database retrieval

#### 2. AI API Errors

**Problem**: Gemini API returns 400 status
**Cause**: Document format not supported or content unreadable
**Solution**: Ensure document is valid PDF/DOCX and under size limit

#### 3. Empty Analysis Results

**Problem**: AI returns empty or invalid JSON
**Cause**: Document content unclear or prompt issues
**Solution**: Check document quality and AI prompt configuration

#### 4. Permission Errors

**Problem**: 403 Forbidden when generating analysis
**Solution**: Ensure user has `contracts:write` permission

### Debugging

Enable detailed logging:

```typescript
logger.info(`Generating DORA compliance analysis for contract: ${contractId}`);
logger.info(`Document buffer size: ${documentBuffer.length} bytes`);
logger.info(`MIME type detected: ${mimeType}`);
```

## Future Enhancements

### Planned Features

1. **ESG Compliance Analysis**: Similar to DORA but for ESG regulations
2. **Batch Analysis**: Analyze multiple contracts simultaneously
3. **Custom Compliance Frameworks**: User-defined compliance categories
4. **Compliance Reporting**: Generate compliance reports across contract portfolio
5. **Risk Scoring**: Automated risk scoring based on compliance gaps
6. **Remediation Suggestions**: AI-powered suggestions for improving compliance

### Technical Improvements

1. **Caching**: Cache analysis results to avoid re-processing
2. **Background Processing**: Queue-based processing for large documents
3. **Version Control**: Track compliance analysis versions over time
4. **Integration**: Connect with external compliance management systems
5. **Analytics**: Compliance trends and portfolio-wide insights

---

## Support

For technical support or questions about the DORA compliance implementation:

1. Check the troubleshooting section above
2. Review application logs for detailed error messages
3. Verify environment configuration and API keys
4. Contact the development team for assistance

---

_Last Updated: December 2024_
_Version: 1.0_
