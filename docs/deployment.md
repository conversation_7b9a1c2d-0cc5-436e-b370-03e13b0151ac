# Deployment Guide

This guide provides detailed instructions for deploying the Aptio platform in different environments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Docker Deployment](#docker-deployment)
- [Kubernetes Deployment](#kubernetes-deployment)
- [Scaling Considerations](#scaling-considerations)
- [Monitoring and Logging](#monitoring-and-logging)
- [Backup and Recovery](#backup-and-recovery)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying the platform, ensure you have the following:

- Docker and Docker Compose (for Docker deployment)
- Kubernetes cluster (for Kubernetes deployment)
- OpenAI API key with sufficient quota
- Domain name and SSL certificates (for production)
- SMTP server for email notifications
- Storage solution (local, S3, Azure Blob, etc.)

## Environment Configuration

The platform uses environment variables for configuration. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

### Critical Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development, production) | `production` |
| `OPENAI_API_KEY` | OpenAI API key | `sk-...` |
| `JWT_SECRET` | Secret for JWT tokens | `random-secure-string` |
| `JWT_REFRESH_SECRET` | Secret for refresh tokens | `another-random-secure-string` |
| `ENCRYPTION_KEY` | 32-byte hex key for encryption | `32-byte-hex-string` |
| `DATABASE_URL` | PostgreSQL connection string | `********************************/db` |
| `REDIS_URL` | Redis connection string | `redis://host:6379` |

### Security Recommendations

- Use a secure password generator for secrets
- Store production secrets in a secure vault (HashiCorp Vault, AWS Secrets Manager, etc.)
- Rotate secrets regularly
- Use different secrets for different environments

## Docker Deployment

### Local Development

```bash
# Set NODE_ENV to development in .env
NODE_ENV=development

# Deploy with development profile
chmod +x deploy.sh
./deploy.sh
```

### Production Deployment

```bash
# Set NODE_ENV to production in .env
NODE_ENV=production

# Deploy
chmod +x deploy.sh
./deploy.sh
```

### Custom Configuration

You can customize the deployment by editing the `docker-compose.yml` file:

- Change ports
- Add or remove services
- Configure volume mounts
- Adjust resource limits

### SSL Configuration

For production, replace the self-signed certificates with valid SSL certificates:

1. Place your SSL certificate and key in `nginx/ssl/`:
   - `server.crt`: SSL certificate
   - `server.key`: SSL private key

2. Update Nginx configuration in `nginx/conf.d/default.conf` if needed.

## Kubernetes Deployment

For larger deployments, Kubernetes is recommended. Kubernetes manifests are provided in the `kubernetes/` directory.

```bash
# Apply Kubernetes manifests
kubectl apply -f kubernetes/namespace.yaml
kubectl apply -f kubernetes/secrets.yaml
kubectl apply -f kubernetes/configmap.yaml
kubectl apply -f kubernetes/postgres.yaml
kubectl apply -f kubernetes/redis.yaml
kubectl apply -f kubernetes/backend.yaml
kubectl apply -f kubernetes/frontend.yaml
kubectl apply -f kubernetes/ingress.yaml
```

## Scaling Considerations

### Horizontal Scaling

- **Backend API**: Scale horizontally by increasing replicas
- **Frontend**: Scale horizontally by increasing replicas
- **Database**: Consider read replicas for read-heavy workloads
- **Redis**: Consider Redis Cluster for high availability

### Vertical Scaling

- Increase CPU and memory resources for services
- Optimize database performance with proper indexing
- Configure connection pooling for database

### AI Processing Optimization

- Implement caching for AI responses
- Use batch processing for document analysis
- Configure rate limiting for OpenAI API calls
- Consider using smaller models for less complex tasks

## Monitoring and Logging

### Prometheus and Grafana

The platform includes Prometheus and Grafana for monitoring:

```bash
# Enable monitoring profile
docker-compose --profile monitoring up -d
```

Access Grafana at http://localhost:3000 (default credentials: admin/admin).

### Log Management

Logs are collected and formatted as JSON for easy integration with log management systems:

- **Docker**: Logs are available via `docker-compose logs`
- **Kubernetes**: Use a log aggregator like ELK, Loki, or CloudWatch

## Backup and Recovery

### Database Backup

```bash
# Backup PostgreSQL database
docker-compose exec postgres pg_dump -U postgres clm_dev > backup.sql

# Restore PostgreSQL database
cat backup.sql | docker-compose exec -T postgres psql -U postgres clm_dev
```

### Volume Backup

```bash
# Backup volumes
docker run --rm -v aptio_postgres-data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/postgres-data.tar.gz -C /source .

# Restore volumes
docker run --rm -v aptio_postgres-data:/target -v $(pwd)/backups:/backup alpine sh -c "rm -rf /target/* && tar -xzf /backup/postgres-data.tar.gz -C /target"
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL environment variable
   - Verify PostgreSQL is running
   - Check network connectivity

2. **OpenAI API Errors**
   - Verify OPENAI_API_KEY is valid
   - Check API quota and limits
   - Implement retry logic for transient errors

3. **Container Startup Failures**
   - Check container logs: `docker-compose logs <service>`
   - Verify environment variables
   - Check for port conflicts

4. **Performance Issues**
   - Monitor resource usage with Prometheus
   - Check for slow database queries
   - Optimize AI processing with caching

### Getting Help

If you encounter issues not covered in this guide:

1. Check the logs: `docker-compose logs`
2. Review the [GitHub Issues](https://github.com/yourusername/aptio/issues)
3. Contact <NAME_EMAIL>
