# Backend Environment Variables

# Application Settings
NODE_ENV=development
PORT=5000
API_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000

# Database Settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/clm_dev

# Security & Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
ENCRYPTION_KEY=your_32_byte_hex_encryption_key

# Email Service
EMAIL_FROM=<EMAIL>
EMAIL_SERVER=smtp://user:<EMAIL>:587

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Microsoft Computer Vision OCR
AZURE_COMPUTER_VISION_KEY=some_key_here_from_microsoft
AZURE_COMPUTER_VISION_ENDPOINT=some_endpoint_here_from_microsoft

# AI Processing
GEMINI_API_KEY=some_key_here_from_google
GEMINI_MODEL=some_model_here_from_google

# Super Admin Configuration
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=SuperSecurePassword123!
SUPER_ADMIN_NAME=Platform Administrator

