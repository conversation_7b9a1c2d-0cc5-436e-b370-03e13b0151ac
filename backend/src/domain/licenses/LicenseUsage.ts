/**
 * License Usage Entity
 * Represents usage data for a license over time
 */

/**
 * License Usage domain entity
 */
export class LicenseUsage {
  private _id: string;
  private _licenseId: string;
  private _date: Date;
  private _usageCount: number;
  private _totalAvailable: number;
  private _utilizationPercentage: number;
  private _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: {
    id: string;
    licenseId: string;
    date: Date;
    usageCount: number;
    totalAvailable: number;
    utilizationPercentage?: number;
    createdAt?: Date;
    updatedAt?: Date;
  }) {
    this._id = props.id;
    this._licenseId = props.licenseId;
    this._date = props.date;
    this._usageCount = props.usageCount;
    this._totalAvailable = props.totalAvailable;
    this._utilizationPercentage = props.utilizationPercentage || 
      (props.totalAvailable > 0 ? (props.usageCount / props.totalAvailable) * 100 : 0);
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  // Getters
  get id(): string { return this._id; }
  get licenseId(): string { return this._licenseId; }
  get date(): Date { return this._date; }
  get usageCount(): number { return this._usageCount; }
  get totalAvailable(): number { return this._totalAvailable; }
  get utilizationPercentage(): number { return this._utilizationPercentage; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }

  // Domain methods

  /**
   * Validates the license usage entity properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._id) {
      throw new Error('Usage ID is required');
    }

    if (!this._licenseId) {
      throw new Error('License ID is required');
    }

    if (!this._date) {
      throw new Error('Date is required');
    }

    if (this._usageCount < 0) {
      throw new Error('Usage count cannot be negative');
    }

    if (this._totalAvailable < 0) {
      throw new Error('Total available cannot be negative');
    }

    if (this._utilizationPercentage < 0 || this._utilizationPercentage > 100) {
      throw new Error('Utilization percentage must be between 0 and 100');
    }
  }

  /**
   * Updates usage data
   */
  updateUsage(params: {
    usageCount: number,
    totalAvailable: number
  }): void {
    if (params.usageCount < 0) {
      throw new Error('Usage count cannot be negative');
    }

    if (params.totalAvailable < 0) {
      throw new Error('Total available cannot be negative');
    }

    this._usageCount = params.usageCount;
    this._totalAvailable = params.totalAvailable;
    this._utilizationPercentage = params.totalAvailable > 0 ? 
      (params.usageCount / params.totalAvailable) * 100 : 0;
    this._updatedAt = new Date();
  }

  /**
   * Converts the domain entity to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      licenseId: this._licenseId,
      date: this._date,
      usageCount: this._usageCount,
      totalAvailable: this._totalAvailable,
      utilizationPercentage: this._utilizationPercentage,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }

  /**
   * Creates a LicenseUsage entity from persistence data
   */
  static fromPersistence(data: any): LicenseUsage {
    return new LicenseUsage({
      id: data.id,
      licenseId: data.licenseId,
      date: data.date,
      usageCount: data.usageCount,
      totalAvailable: data.totalAvailable,
      utilizationPercentage: data.utilizationPercentage,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}
