/**
 * License Entity - Core Domain Model
 * Represents a software license with its metadata and properties
 */

import { Decimal } from "@prisma/client/runtime/library";

export enum LicenseType {
  SOFTWARE = "SOFTWARE",
  SAAS = "SAAS",
  HARDWARE = "HARDWARE",
  SERVICE = "SERVICE",
  OTHER = "OTHER",
}

export enum LicenseStatus {
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  EXPIRING_SOON = "EXPIRING_SOON",
  INACTIVE = "INACTIVE",
  COMPLIANCE_ISSUE = "COMPLIANCE_ISSUE",
}

/**
 * License domain entity
 * Core entity for the License Management
 */
export class License {
  private _id: string;
  private _name: string;
  private _description: string | null;
  private _licenseNumber: string | null;
  private _licenseType: LicenseType;
  private _status: LicenseStatus;
  private _vendor: string;
  private _purchaseDate: Date | null;
  private _startDate: Date | null;
  private _endDate: Date | null;
  private _renewalType: string | null;
  private _renewalDate: Date | null;
  private _isAutoRenew: boolean;
  private _noticePeriodDays: number | null;
  private _totalValue: Decimal | null;
  private _currency: string | null;
  private _costPeriod: string | null;
  private _totalLicenses: number;
  private _assignedLicenses: number;
  private _availableLicenses: number;
  private _complianceStatus: string;
  private _lastComplianceCheck: Date | null;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _tenantId: string;
  private _createdById: string | null;

  constructor(props: {
    id: string;
    name: string;
    description?: string | null;
    licenseNumber?: string | null;
    licenseType: LicenseType;
    status: LicenseStatus;
    vendor: string;
    purchaseDate?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
    renewalType?: string | null;
    renewalDate?: Date | null;
    isAutoRenew?: boolean;
    noticePeriodDays?: number | null;
    totalValue?: Decimal | null;
    currency?: string | null;
    costPeriod?: string | null;
    totalLicenses: number;
    assignedLicenses?: number;
    availableLicenses?: number;
    complianceStatus?: string;
    lastComplianceCheck?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
    tenantId: string;
    createdById?: string | null;
  }) {
    this._id = props.id;
    this._name = props.name;
    this._description = props.description || null;
    this._licenseNumber = props.licenseNumber || null;
    this._licenseType = props.licenseType;
    this._status = props.status;
    this._vendor = props.vendor;
    this._purchaseDate = props.purchaseDate || null;
    this._startDate = props.startDate || null;
    this._endDate = props.endDate || null;
    this._renewalType = props.renewalType || null;
    this._renewalDate = props.renewalDate || null;
    this._isAutoRenew = props.isAutoRenew || false;
    this._noticePeriodDays = props.noticePeriodDays || null;
    this._totalValue = props.totalValue || null;
    this._currency = props.currency || null;
    this._costPeriod = props.costPeriod || null;
    this._totalLicenses = props.totalLicenses;
    this._assignedLicenses = props.assignedLicenses || 0;
    this._availableLicenses =
      props.availableLicenses ||
      props.totalLicenses - (props.assignedLicenses || 0);
    this._complianceStatus = props.complianceStatus || "Compliant";
    this._lastComplianceCheck = props.lastComplianceCheck || null;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();
    this._tenantId = props.tenantId;
    this._createdById = props.createdById || null;

    this.validate();
  }

  // Getters
  get id(): string {
    return this._id;
  }
  get name(): string {
    return this._name;
  }
  get description(): string | null {
    return this._description;
  }
  get licenseNumber(): string | null {
    return this._licenseNumber;
  }
  get licenseType(): LicenseType {
    return this._licenseType;
  }
  get status(): LicenseStatus {
    return this._status;
  }
  get vendor(): string {
    return this._vendor;
  }
  get purchaseDate(): Date | null {
    return this._purchaseDate;
  }
  get startDate(): Date | null {
    return this._startDate;
  }
  get endDate(): Date | null {
    return this._endDate;
  }
  get renewalType(): string | null {
    return this._renewalType;
  }
  get renewalDate(): Date | null {
    return this._renewalDate;
  }
  get isAutoRenew(): boolean {
    return this._isAutoRenew;
  }
  get noticePeriodDays(): number | null {
    return this._noticePeriodDays;
  }
  get totalValue(): Decimal | null {
    return this._totalValue;
  }
  get currency(): string | null {
    return this._currency;
  }
  get costPeriod(): string | null {
    return this._costPeriod;
  }
  get totalLicenses(): number {
    return this._totalLicenses;
  }
  get assignedLicenses(): number {
    return this._assignedLicenses;
  }
  get availableLicenses(): number {
    return this._availableLicenses;
  }
  get complianceStatus(): string {
    return this._complianceStatus;
  }
  get lastComplianceCheck(): Date | null {
    return this._lastComplianceCheck;
  }
  get createdAt(): Date {
    return this._createdAt;
  }
  get updatedAt(): Date {
    return this._updatedAt;
  }
  get tenantId(): string {
    return this._tenantId;
  }
  get createdById(): string | null {
    return this._createdById;
  }

  // Domain methods

  /**
   * Validates the license entity properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._name || this._name.trim().length === 0) {
      throw new Error("License name is required");
    }

    if (!this._vendor || this._vendor.trim().length === 0) {
      throw new Error("Vendor name is required");
    }

    if (!this._tenantId) {
      throw new Error("Tenant ID is required");
    }

    if (this._totalLicenses < 0) {
      throw new Error("Total licenses cannot be negative");
    }

    if (this._assignedLicenses < 0) {
      throw new Error("Assigned licenses cannot be negative");
    }

    if (this._assignedLicenses > this._totalLicenses) {
      throw new Error("Assigned licenses cannot exceed total licenses");
    }

    this.validateDates();
  }

  /**
   * Validates date relationships (purchase date, start date, end date, renewal date)
   * @throws Error if date validation fails
   */
  private validateDates(): void {
    if (this._startDate && this._endDate && this._startDate > this._endDate) {
      throw new Error("Start date cannot be after end date");
    }

    if (
      this._endDate &&
      this._renewalDate &&
      this._renewalDate > this._endDate
    ) {
      throw new Error("Renewal date cannot be after end date");
    }
  }

  /**
   * Updates the license status
   * @param newStatus The new license status
   */
  updateStatus(newStatus: LicenseStatus): void {
    this._status = newStatus;
    this._updatedAt = new Date();
  }

  /**
   * Updates license details
   */
  updateDetails(params: {
    name?: string;
    description?: string | null;
    licenseNumber?: string | null;
    licenseType?: LicenseType;
    vendor?: string;
    purchaseDate?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
    renewalType?: string | null;
    renewalDate?: Date | null;
    isAutoRenew?: boolean;
    noticePeriodDays?: number | null;
    totalValue?: Decimal | null;
    currency?: string | null;
    costPeriod?: string | null;
  }): void {
    if (params.name !== undefined) {
      if (!params.name || params.name.trim().length === 0) {
        throw new Error("License name is required");
      }
      this._name = params.name;
    }

    if (params.description !== undefined) {
      this._description = params.description;
    }

    if (params.licenseNumber !== undefined) {
      this._licenseNumber = params.licenseNumber;
    }

    if (params.licenseType !== undefined) {
      this._licenseType = params.licenseType;
    }

    if (params.vendor !== undefined) {
      if (!params.vendor || params.vendor.trim().length === 0) {
        throw new Error("Vendor name is required");
      }
      this._vendor = params.vendor;
    }

    if (params.purchaseDate !== undefined) {
      this._purchaseDate = params.purchaseDate;
    }

    if (params.startDate !== undefined) {
      this._startDate = params.startDate;
    }

    if (params.endDate !== undefined) {
      this._endDate = params.endDate;
    }

    if (params.renewalType !== undefined) {
      this._renewalType = params.renewalType;
    }

    if (params.renewalDate !== undefined) {
      this._renewalDate = params.renewalDate;
    }

    if (params.isAutoRenew !== undefined) {
      this._isAutoRenew = params.isAutoRenew;
    }

    if (params.noticePeriodDays !== undefined) {
      this._noticePeriodDays = params.noticePeriodDays;
    }

    if (params.totalValue !== undefined) {
      this._totalValue = params.totalValue;
    }

    if (params.currency !== undefined) {
      this._currency = params.currency;
    }

    if (params.costPeriod !== undefined) {
      this._costPeriod = params.costPeriod;
    }

    this._updatedAt = new Date();
    this.validateDates();
  }

  /**
   * Updates license allocation
   */
  updateAllocation(params: {
    totalLicenses?: number;
    assignedLicenses?: number;
  }): void {
    if (params.totalLicenses !== undefined) {
      if (params.totalLicenses < 0) {
        throw new Error("Total licenses cannot be negative");
      }
      this._totalLicenses = params.totalLicenses;
    }

    if (params.assignedLicenses !== undefined) {
      if (params.assignedLicenses < 0) {
        throw new Error("Assigned licenses cannot be negative");
      }
      this._assignedLicenses = params.assignedLicenses;
    }

    // Recalculate available licenses
    this._availableLicenses = this._totalLicenses - this._assignedLicenses;

    if (this._availableLicenses < 0) {
      throw new Error("Assigned licenses cannot exceed total licenses");
    }

    this._updatedAt = new Date();
  }

  /**
   * Updates compliance status
   */
  updateComplianceStatus(status: string): void {
    this._complianceStatus = status;
    this._lastComplianceCheck = new Date();
    this._updatedAt = new Date();

    // Update license status if there's a compliance issue
    if (
      status !== "Compliant" &&
      this._status !== LicenseStatus.COMPLIANCE_ISSUE
    ) {
      this._status = LicenseStatus.COMPLIANCE_ISSUE;
    } else if (
      status === "Compliant" &&
      this._status === LicenseStatus.COMPLIANCE_ISSUE
    ) {
      // Revert to ACTIVE status if compliance issue is resolved
      this._status = LicenseStatus.ACTIVE;
    }
  }

  /**
   * Checks if a license is expiring soon (within notice period)
   * @param daysThreshold Optional custom threshold in days
   * @returns boolean indicating if license is expiring soon
   */
  isExpiringSoon(daysThreshold?: number): boolean {
    if (!this._endDate) return false;

    const threshold = daysThreshold || this._noticePeriodDays || 30; // Default 30 days if not specified
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() + threshold);

    return this._endDate <= thresholdDate && this._endDate > new Date();
  }

  /**
   * Checks if a license is over-utilized
   * @returns boolean indicating if license is over-utilized
   */
  isOverUtilized(): boolean {
    return this._assignedLicenses > this._totalLicenses;
  }

  /**
   * Checks if a license is under-utilized
   * @param threshold Utilization threshold percentage (default: 70%)
   * @returns boolean indicating if license is under-utilized
   */
  isUnderUtilized(threshold: number = 70): boolean {
    if (this._totalLicenses === 0) return false;
    const utilizationPercentage =
      (this._assignedLicenses / this._totalLicenses) * 100;
    return utilizationPercentage < threshold;
  }

  /**
   * Renews the license with new end date
   * @param newEndDate New end date for the renewed license
   * @param updateRenewalDate Whether to update the renewal date as well
   */
  renew(newEndDate: Date, updateRenewalDate: boolean = true): void {
    if (newEndDate <= new Date()) {
      throw new Error("New end date must be in the future");
    }

    this._endDate = newEndDate;

    if (updateRenewalDate) {
      // Set renewal date to null or calculate based on business rules
      this._renewalDate = null;
    }

    // Update status to ACTIVE
    this._status = LicenseStatus.ACTIVE;
    this._updatedAt = new Date();
  }

  /**
   * Converts the domain entity to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      name: this._name,
      description: this._description,
      licenseNumber: this._licenseNumber,
      licenseType: this._licenseType,
      status: this._status,
      vendor: this._vendor,
      purchaseDate: this._purchaseDate,
      startDate: this._startDate,
      endDate: this._endDate,
      renewalType: this._renewalType,
      renewalDate: this._renewalDate,
      isAutoRenew: this._isAutoRenew,
      noticePeriodDays: this._noticePeriodDays,
      totalValue: this._totalValue,
      currency: this._currency,
      costPeriod: this._costPeriod,
      totalLicenses: this._totalLicenses,
      assignedLicenses: this._assignedLicenses,
      availableLicenses: this._availableLicenses,
      complianceStatus: this._complianceStatus,
      lastComplianceCheck: this._lastComplianceCheck,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      tenantId: this._tenantId,
      createdById: this._createdById,
    };
  }

  /**
   * Creates a License entity from persistence data
   */
  static fromPersistence(data: any): License {
    return new License({
      id: data.id,
      name: data.name,
      description: data.description,
      licenseNumber: data.licenseNumber,
      licenseType: data.licenseType,
      status: data.status,
      vendor: data.vendor,
      purchaseDate: data.purchaseDate,
      startDate: data.startDate,
      endDate: data.endDate,
      renewalType: data.renewalType,
      renewalDate: data.renewalDate,
      isAutoRenew: data.isAutoRenew,
      noticePeriodDays: data.noticePeriodDays,
      totalValue: data.totalValue,
      currency: data.currency,
      costPeriod: data.costPeriod,
      totalLicenses: data.totalLicenses,
      assignedLicenses: data.assignedLicenses,
      availableLicenses: data.availableLicenses,
      complianceStatus: data.complianceStatus,
      lastComplianceCheck: data.lastComplianceCheck,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      tenantId: data.tenantId,
      createdById: data.createdById,
    });
  }
}
