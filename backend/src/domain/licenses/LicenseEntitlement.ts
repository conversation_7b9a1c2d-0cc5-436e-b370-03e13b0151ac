/**
 * License Entitlement Entity
 * Represents a specific entitlement or feature included in a license
 */

/**
 * License Entitlement domain entity
 */
export class LicenseEntitlement {
  private _id: string;
  private _licenseId: string;
  private _name: string;
  private _description: string | null;
  private _included: boolean;
  private _quantity: number | null;
  private _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: {
    id: string;
    licenseId: string;
    name: string;
    description?: string | null;
    included: boolean;
    quantity?: number | null;
    createdAt?: Date;
    updatedAt?: Date;
  }) {
    this._id = props.id;
    this._licenseId = props.licenseId;
    this._name = props.name;
    this._description = props.description || null;
    this._included = props.included;
    this._quantity = props.quantity || null;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  // Getters
  get id(): string { return this._id; }
  get licenseId(): string { return this._licenseId; }
  get name(): string { return this._name; }
  get description(): string | null { return this._description; }
  get included(): boolean { return this._included; }
  get quantity(): number | null { return this._quantity; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }

  // Domain methods

  /**
   * Validates the license entitlement entity properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._id) {
      throw new Error('Entitlement ID is required');
    }

    if (!this._licenseId) {
      throw new Error('License ID is required');
    }

    if (!this._name || this._name.trim().length === 0) {
      throw new Error('Entitlement name is required');
    }

    if (this._quantity !== null && this._quantity < 0) {
      throw new Error('Quantity cannot be negative');
    }
  }

  /**
   * Updates entitlement details
   */
  updateDetails(params: {
    name?: string,
    description?: string | null,
    included?: boolean,
    quantity?: number | null
  }): void {
    if (params.name !== undefined) {
      if (!params.name || params.name.trim().length === 0) {
        throw new Error('Entitlement name is required');
      }
      this._name = params.name;
    }

    if (params.description !== undefined) {
      this._description = params.description;
    }

    if (params.included !== undefined) {
      this._included = params.included;
    }

    if (params.quantity !== undefined) {
      if (params.quantity !== null && params.quantity < 0) {
        throw new Error('Quantity cannot be negative');
      }
      this._quantity = params.quantity;
    }

    this._updatedAt = new Date();
  }

  /**
   * Converts the domain entity to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      licenseId: this._licenseId,
      name: this._name,
      description: this._description,
      included: this._included,
      quantity: this._quantity,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }

  /**
   * Creates a LicenseEntitlement entity from persistence data
   */
  static fromPersistence(data: any): LicenseEntitlement {
    return new LicenseEntitlement({
      id: data.id,
      licenseId: data.licenseId,
      name: data.name,
      description: data.description,
      included: data.included,
      quantity: data.quantity,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}
