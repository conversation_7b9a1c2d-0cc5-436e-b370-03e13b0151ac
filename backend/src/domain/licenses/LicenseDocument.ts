/**
 * License Document Entity
 * Represents a document associated with a license (contract, invoice, etc.)
 */

import { DocumentFormat } from '@prisma/client';

export enum LicenseDocumentType {
  CONTRACT = 'CONTRACT',
  INVOICE = 'INVOICE',
  AGREEMENT = 'AGREEMENT',
  TERMS = 'TERMS',
  CERTIFICATE = 'CERTIFICATE',
  OTHER = 'OTHER'
}

/**
 * License Document domain entity
 */
export class LicenseDocument {
  private _id: string;
  private _licenseId: string;
  private _name: string;
  private _documentType: LicenseDocumentType;
  private _documentFormat: DocumentFormat;
  private _documentUri: string;
  private _documentHash: string | null;
  private _isEncrypted: boolean;
  private _encryptionKeyId: string | null;
  private _uploadDate: Date;
  private _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: {
    id: string;
    licenseId: string;
    name: string;
    documentType: LicenseDocumentType;
    documentFormat: DocumentFormat;
    documentUri: string;
    documentHash?: string | null;
    isEncrypted?: boolean;
    encryptionKeyId?: string | null;
    uploadDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
  }) {
    this._id = props.id;
    this._licenseId = props.licenseId;
    this._name = props.name;
    this._documentType = props.documentType;
    this._documentFormat = props.documentFormat;
    this._documentUri = props.documentUri;
    this._documentHash = props.documentHash || null;
    this._isEncrypted = props.isEncrypted !== undefined ? props.isEncrypted : false;
    this._encryptionKeyId = props.encryptionKeyId || null;
    this._uploadDate = props.uploadDate || new Date();
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  // Getters
  get id(): string { return this._id; }
  get licenseId(): string { return this._licenseId; }
  get name(): string { return this._name; }
  get documentType(): LicenseDocumentType { return this._documentType; }
  get documentFormat(): DocumentFormat { return this._documentFormat; }
  get documentUri(): string { return this._documentUri; }
  get documentHash(): string | null { return this._documentHash; }
  get isEncrypted(): boolean { return this._isEncrypted; }
  get encryptionKeyId(): string | null { return this._encryptionKeyId; }
  get uploadDate(): Date { return this._uploadDate; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }

  // Domain methods

  /**
   * Validates the license document entity properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._id) {
      throw new Error('Document ID is required');
    }

    if (!this._licenseId) {
      throw new Error('License ID is required');
    }

    if (!this._name || this._name.trim().length === 0) {
      throw new Error('Document name is required');
    }

    if (!this._documentUri || this._documentUri.trim().length === 0) {
      throw new Error('Document URI is required');
    }

    // If the document is encrypted, it should have an encryption key ID
    if (this._isEncrypted && !this._encryptionKeyId) {
      throw new Error('Encryption key ID is required for encrypted documents');
    }
  }

  /**
   * Updates document details
   */
  updateDetails(params: {
    name?: string,
    documentType?: LicenseDocumentType,
    documentFormat?: DocumentFormat
  }): void {
    if (params.name !== undefined) {
      if (!params.name || params.name.trim().length === 0) {
        throw new Error('Document name is required');
      }
      this._name = params.name;
    }

    if (params.documentType !== undefined) {
      this._documentType = params.documentType;
    }

    if (params.documentFormat !== undefined) {
      this._documentFormat = params.documentFormat;
    }

    this._updatedAt = new Date();
  }

  /**
   * Updates document encryption status
   */
  updateEncryption(params: {
    isEncrypted: boolean,
    encryptionKeyId?: string | null
  }): void {
    this._isEncrypted = params.isEncrypted;
    
    if (params.isEncrypted) {
      if (!params.encryptionKeyId) {
        throw new Error('Encryption key ID is required for encrypted documents');
      }
      this._encryptionKeyId = params.encryptionKeyId;
    } else {
      this._encryptionKeyId = null;
    }

    this._updatedAt = new Date();
  }

  /**
   * Converts the domain entity to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      licenseId: this._licenseId,
      name: this._name,
      documentType: this._documentType,
      documentFormat: this._documentFormat,
      documentUri: this._documentUri,
      documentHash: this._documentHash,
      isEncrypted: this._isEncrypted,
      encryptionKeyId: this._encryptionKeyId,
      uploadDate: this._uploadDate,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }

  /**
   * Creates a LicenseDocument entity from persistence data
   */
  static fromPersistence(data: any): LicenseDocument {
    return new LicenseDocument({
      id: data.id,
      licenseId: data.licenseId,
      name: data.name,
      documentType: data.documentType,
      documentFormat: data.documentFormat,
      documentUri: data.documentUri,
      documentHash: data.documentHash,
      isEncrypted: data.isEncrypted,
      encryptionKeyId: data.encryptionKeyId,
      uploadDate: data.uploadDate,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}
