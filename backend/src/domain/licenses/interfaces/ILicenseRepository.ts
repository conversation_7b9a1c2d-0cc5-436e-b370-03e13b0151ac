/**
 * License Repository Interface
 * Defines the contract for license repository implementations
 */

import { License, LicenseStatus, LicenseType } from "../License";
import { LicenseEntitlement } from "../LicenseEntitlement";
import { LicenseDocument } from "../LicenseDocument";
import { LicenseUsage } from "../LicenseUsage";

/**
 * Search parameters for finding licenses
 * Note: tenantId is automatically applied from the current tenant context
 */
export interface LicenseSearchParams {
  name?: string;
  vendor?: string;
  licenseType?: LicenseType;
  status?: LicenseStatus;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  page?: number;
  limit?: number;
}

/**
 * Interface for License Repository
 */
export interface ILicenseRepository {
  /**
   * Creates a new license
   * @param license License entity to create
   * @returns Created license with generated ID
   */
  create(license: License): Promise<License>;

  /**
   * Finds a license by ID
   * @param id License ID
   * @returns License entity or null if not found
   */
  findById(id: string): Promise<License | null>;

  /**
   * Updates an existing license
   * @param license License entity to update
   * @returns Updated license
   */
  update(license: License): Promise<License>;

  /**
   * Deletes a license by ID
   * @param id License ID
   * @returns true if deleted, false if not found
   */
  delete(id: string): Promise<boolean>;

  /**
   * Searches for licenses based on various parameters
   * @param params Search parameters
   * @returns Array of licenses matching the search criteria and total count
   */
  search(
    params: LicenseSearchParams
  ): Promise<{ licenses: License[]; total: number }>;

  /**
   * Gets all licenses for a tenant
   * @param tenantId Tenant ID
   * @param page Page number (optional, default: 1)
   * @param limit Items per page (optional, default: 20)
   * @returns Array of licenses and total count
   */
  getByTenant(
    tenantId: string,
    page?: number,
    limit?: number
  ): Promise<{ licenses: License[]; total: number }>;

  /**
   * Gets licenses expiring within a specified period
   * @param tenantId Tenant ID
   * @param days Number of days from now
   * @returns Array of expiring licenses
   */
  getExpiring(tenantId: string, days: number): Promise<License[]>;

  /**
   * Gets licenses with compliance issues
   * @param tenantId Tenant ID
   * @returns Array of licenses with compliance issues
   */
  getWithComplianceIssues(tenantId: string): Promise<License[]>;

  /**
   * Gets licenses with utilization issues (over or under-utilized)
   * @param tenantId Tenant ID
   * @param threshold Utilization threshold percentage (default: 70%)
   * @returns Array of licenses with utilization issues
   */
  getWithUtilizationIssues(
    tenantId: string,
    threshold?: number
  ): Promise<License[]>;

  // License Entitlement methods

  /**
   * Creates a new license entitlement
   * @param entitlement License entitlement to create
   * @returns Created entitlement
   */
  createEntitlement(
    entitlement: LicenseEntitlement
  ): Promise<LicenseEntitlement>;

  /**
   * Gets all entitlements for a license
   * @param licenseId License ID
   * @returns Array of entitlements
   */
  getEntitlements(licenseId: string): Promise<LicenseEntitlement[]>;

  /**
   * Finds an entitlement by ID
   * @param id Entitlement ID
   * @returns Entitlement entity or null if not found
   */
  findEntitlementById(id: string): Promise<LicenseEntitlement | null>;

  /**
   * Updates a license entitlement
   * @param entitlement License entitlement to update
   * @returns Updated entitlement
   */
  updateEntitlement(
    entitlement: LicenseEntitlement
  ): Promise<LicenseEntitlement>;

  /**
   * Deletes a license entitlement
   * @param id Entitlement ID
   * @returns true if deleted, false if not found
   */
  deleteEntitlement(id: string): Promise<boolean>;

  // License Document methods

  /**
   * Creates a new license document
   * @param document License document to create
   * @returns Created document
   */
  createDocument(document: LicenseDocument): Promise<LicenseDocument>;

  /**
   * Gets all documents for a license
   * @param licenseId License ID
   * @returns Array of documents
   */
  getDocuments(licenseId: string): Promise<LicenseDocument[]>;

  /**
   * Finds a document by ID
   * @param id Document ID
   * @returns Document entity or null if not found
   */
  findDocumentById(id: string): Promise<LicenseDocument | null>;

  /**
   * Updates a license document
   * @param document License document to update
   * @returns Updated document
   */
  updateDocument(document: LicenseDocument): Promise<LicenseDocument>;

  /**
   * Deletes a license document
   * @param id Document ID
   * @returns true if deleted, false if not found
   */
  deleteDocument(id: string): Promise<boolean>;

  // License Usage methods

  /**
   * Creates a new license usage record
   * @param usage License usage to create
   * @returns Created usage record
   */
  createUsage(usage: LicenseUsage): Promise<LicenseUsage>;

  /**
   * Gets usage history for a license
   * @param licenseId License ID
   * @param startDate Start date for usage history
   * @param endDate End date for usage history
   * @returns Array of usage records
   */
  getUsageHistory(
    licenseId: string,
    startDate: Date,
    endDate: Date
  ): Promise<LicenseUsage[]>;

  /**
   * Gets the latest usage record for a license
   * @param licenseId License ID
   * @returns Latest usage record or null if none exists
   */
  getLatestUsage(licenseId: string): Promise<LicenseUsage | null>;
}
