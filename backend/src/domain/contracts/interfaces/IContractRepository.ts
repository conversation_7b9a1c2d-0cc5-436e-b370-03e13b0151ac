/**
 * Contract Repository Interface
 * Defines the contract for repository operations according to the Repository Pattern
 */

import { Contract } from "../Contract";
import { ContractVersion } from "../ContractVersion";
import { ContractMetadata } from "../ContractMetadata";
import {
  ContractStatus,
  AgreementType,
  SecurityClassification,
} from "@prisma/client";

/**
 * Search parameters for contract repository
 */
export interface ContractSearchParams {
  title?: string;
  agreementType?: AgreementType;
  status?: ContractStatus;
  securityClassification?: SecurityClassification;
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  tenantId: string;
  page?: number;
  limit?: number;
  folderId?: string | null; // Add folderId parameter for folder-based filtering
}

/**
 * Contract Repository Interface
 */
export interface IContractRepository {
  /**
   * Creates a new contract
   * @param contract Contract entity to create
   * @returns Created contract
   */
  create(contract: Contract): Promise<Contract>;

  /**
   * Finds a contract by ID
   * @param id Contract ID
   * @returns Contract entity or null if not found
   */
  findById(id: string): Promise<Contract | null>;

  /**
   * Updates an existing contract
   * @param contract Contract entity with updated values
   * @returns Updated contract
   */
  update(contract: Contract): Promise<Contract>;

  /**
   * Deletes a contract by ID
   * @param id Contract ID
   * @returns True if deleted, false if not found
   */
  delete(id: string): Promise<boolean>;

  /**
   * Checks if a contract with the given title already exists for the tenant
   * @param title Contract title to check
   * @param tenantId Tenant ID
   * @returns True if a contract with this title exists, false otherwise
   */
  existsByTitle(title: string, tenantId: string): Promise<boolean>;

  /**
   * Searches for contracts based on various parameters
   * @param params Search parameters
   * @returns Array of contracts matching the search criteria
   */
  search(
    params: ContractSearchParams
  ): Promise<{ contracts: Contract[]; total: number }>;

  /**
   * Creates a new contract version
   * @param contractVersion Contract version to create
   * @param documentBuffer Optional document buffer to store
   * @returns Created contract version
   */
  createVersion(
    contractVersion: ContractVersion,
    documentBuffer?: Buffer
  ): Promise<ContractVersion>;

  /**
   * Gets all versions of a contract
   * @param contractId Contract ID
   * @returns Array of contract versions
   */
  getVersionHistory(contractId: string): Promise<ContractVersion[]>;

  /**
   * Gets a specific version of a contract
   * @param contractId Contract ID
   * @param versionNumber Version number
   * @returns Contract version or null if not found
   */
  getVersion(
    contractId: string,
    versionNumber: number
  ): Promise<ContractVersion | null>;

  /**
   * Creates or updates contract metadata
   * @param metadata Contract metadata to create or update
   * @returns Created or updated contract metadata
   */
  saveMetadata(metadata: ContractMetadata): Promise<ContractMetadata>;

  /**
   * Gets metadata for a contract
   * @param contractId Contract ID
   * @returns Contract metadata or null if not found
   */
  getMetadata(contractId: string): Promise<ContractMetadata | null>;

  /**
   * Gets all contracts for a tenant (without pagination)
   * @param tenantId Tenant ID
   * @returns Array of all contracts for the tenant
   */
  findByTenantId(tenantId: string): Promise<Contract[]>;
}
