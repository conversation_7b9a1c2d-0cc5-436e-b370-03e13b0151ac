/**
 * Contract Extraction Repository Interface
 * Defines the contract for data access operations related to contract extractions
 */

import {
  ContractExtraction,
  FixedFields,
  DynamicFields,
  DynamicCategoricalFields,
  SpecialFields,
  DocumentSummary,
  ComplianceAnalysis,
  AnalysisFields,
} from "../ContractExtraction";

export interface CreateContractExtractionParams {
  contractId: string;
  tenantId: string;
  fixedFields: FixedFields;
  dynamicFields: DynamicCategoricalFields;
  specialFields: SpecialFields;
  documentSummary?: DocumentSummary;
  complianceAnalysis?: ComplianceAnalysis;
  analysisFields?: AnalysisFields;
  extractionVersion: string;
  processingTimeMs?: number;
  modelUsed?: string;
  folderId?: string;
  ocrUsedForExtraction?: boolean;
}

export interface UpdateContractExtractionParams {
  fixedFields?: FixedFields;
  dynamicFields?: DynamicCategoricalFields;
  specialFields?: SpecialFields;
  documentSummary?: DocumentSummary;
  complianceAnalysis?: ComplianceAnalysis;
  integrityAnalysis?: any;
  analysisFields?: AnalysisFields;
  extractionVersion?: string;
  processingTimeMs?: number;
  modelUsed?: string;
}

export interface IContractExtractionRepository {
  /**
   * Creates a new contract extraction record
   */
  create(params: CreateContractExtractionParams): Promise<ContractExtraction>;

  /**
   * Gets a contract extraction by contract ID
   */
  getByContractId(
    contractId: string,
    tenantId: string
  ): Promise<ContractExtraction | null>;

  /**
   * Gets a contract extraction by ID
   */
  getById(id: string, tenantId: string): Promise<ContractExtraction | null>;

  /**
   * Updates an existing contract extraction
   */
  update(
    id: string,
    tenantId: string,
    params: UpdateContractExtractionParams
  ): Promise<ContractExtraction>;

  /**
   * Deletes a contract extraction
   */
  delete(id: string, tenantId: string): Promise<void>;

  /**
   * Gets all contract extractions for a tenant with pagination
   */
  getByTenant(
    tenantId: string,
    page?: number,
    limit?: number
  ): Promise<{ extractions: ContractExtraction[]; total: number }>;

  /**
   * Gets all contract extractions for a tenant (without pagination)
   */
  getByTenantId(tenantId: string): Promise<ContractExtraction[]>;

  /**
   * Gets contract extractions for a tenant with filtering and pagination
   */
  getByTenantWithFilters(
    tenantId: string,
    filters: {
      title?: string;
      agreementType?: string;
      status?: string;
      securityClassification?: string;
      startDateFrom?: string;
      startDateTo?: string;
      endDateFrom?: string;
      endDateTo?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{ extractions: ContractExtraction[]; total: number }>;

  /**
   * Gets contract extractions by confidence threshold
   */
  getByConfidenceThreshold(
    tenantId: string,
    minConfidence: number,
    maxConfidence?: number
  ): Promise<ContractExtraction[]>;

  /**
   * Gets contract extractions by extraction version
   */
  getByExtractionVersion(
    tenantId: string,
    version: string
  ): Promise<ContractExtraction[]>;

  /**
   * Checks if extraction exists for a contract
   */
  existsForContract(contractId: string, tenantId: string): Promise<boolean>;

  /**
   * Gets extraction statistics for a tenant
   */
  getExtractionStats(tenantId: string): Promise<{
    totalExtractions: number;
    averageConfidence: number;
    highConfidenceCount: number;
    lowConfidenceCount: number;
    latestExtractionDate: Date | null;
  }>;
}
