/**
 * ContractExtraction Entity - Domain Model
 * Represents the three-tier AI extraction results for a contract
 */

export interface FieldValue {
  value: string;
  confidence: number;
}

export interface DynamicField {
  value: string;
  description: string;
  confidence: number;
}

export interface FixedFields {
  agreement_type: FieldValue;
  provider: FieldValue;
  client: FieldValue;
  product: FieldValue;
  total_amount: FieldValue;
  annually_amount: FieldValue; // Year-by-year breakdown of contract value
  start_date: FieldValue;
  end_date: FieldValue;
  contract_id: FieldValue;
  contract_classification: FieldValue;
  // Contract status and term (LLM-extracted)
  contract_status: FieldValue; // Active/Inactive/Unknown - extracted by LLM
  contract_term: FieldValue; // Duration extracted by LLM or calculated from dates
  // Renewal fields
  auto_renewal: FieldValue;
  renewal_notice_period: FieldValue;
  // Document relationships
  relationships: FieldValue; // Array of document references as comma-separated string
  // File metadata
  original_filename: FieldValue;
  // Calculated fields
  safe_auto_renewal?: FieldValue;
  intervention_opportunity?: FieldValue;
}

export interface DynamicFields {
  [key: string]: DynamicField;
}

/**
 * Category-based fields for dynamic field organization
 */
export interface CategoryFields {
  [fieldName: string]: DynamicField;
}

/**
 * Categorized dynamic fields structure
 */
export interface DynamicCategoricalFields {
  "Use rights & restrictions"?: CategoryFields;
  "General"?: CategoryFields;
  "Legal terms"?: CategoryFields;
  "Commercial terms"?: CategoryFields;
  "Data protection"?: CategoryFields;
  "Others"?: CategoryFields;
}

export interface SpecialFields {
  [supplierName: string]: { [key: string]: FieldValue };
}

export interface DocumentSummary {
  value: string;
  confidence: number;
  extractionDate: Date;
  processingTimeMs?: number;
}

export interface ComplianceClause {
  present: "Yes" | "No" | "Unclear";
  summary: string;
  complianceRisk: "Low" | "Medium" | "High";
}

export interface DORACompliance {
  criticalFunctions: ComplianceClause;
  subcontracting: ComplianceClause;
  auditRights: ComplianceClause;
  terminationRights: ComplianceClause;
  exitStrategy: ComplianceClause;
  incidentNotification: ComplianceClause;
  dataLocation: ComplianceClause;
  businessContinuity: ComplianceClause;
  securityMeasures: ComplianceClause;
  regulatoryCooperation: ComplianceClause;
  liability: ComplianceClause;
  serviceLevelAgreements: ComplianceClause;
}

export interface ComplianceAnalysis {
  dora?: DORACompliance;
  esg?: any; // Will be defined when ESG compliance is implemented
  extractionDate?: Date;
  processingTimeMs?: number;
  overallRisk?: "Low" | "Medium" | "High";
}

export interface IntegrityClauseResult {
  clauseName: string;
  found: boolean;
  extractedValue: string | null;
  matchedValue: string | null;
  riskScore: number;
  maxScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  confidence: number;
  sourceField?: string | null;
  reasoning?: string;
}

export interface IntegrityAnalysis {
  configurationUsed: {
    id: string;
    name: string;
    totalClauses: number;
  };
  overallRiskScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  maxPossibleScore: number;
  clauses: Record<string, IntegrityClauseResult>;
  extractionDate: string;
  processingTimeMs: number;
}

export interface AnalysisFields {
  publisher?: FieldValue;
  reseller?: FieldValue;
  entitled_entity?: FieldValue;
  entitled_entity_country?: FieldValue;
  product_name?: FieldValue;
  total_quantity?: FieldValue;
  metric?: FieldValue;
  metric_definition?: FieldValue;
  term?: FieldValue;
  level?: FieldValue;
  limitations?: FieldValue;
  included_rights?: FieldValue;
  csi?: FieldValue;
  purchase_date?: FieldValue;
  governing_agreement?: FieldValue;
  support_contract_number?: FieldValue;
  support_start_date?: FieldValue;
  support_end_date?: FieldValue;
  original_document_name?: FieldValue;
  document_type?: FieldValue;
  license_value?: FieldValue;
  license_value_per_unit?: FieldValue;
  contractual_support_value?: FieldValue;
  support_value_per_year?: FieldValue;
  support_value_per_year_per_unit?: FieldValue;
  currency?: FieldValue;
  index?: FieldValue;
  delta?: FieldValue;
  complianceAnalysis?: ComplianceAnalysis;
  extractionDate?: Date;
  processingTimeMs?: number;
}

export interface ContractExtractionData {
  id: string;
  contractId: string;
  tenantId: string;
  fixedFields: FixedFields;
  dynamicFields: DynamicCategoricalFields; // Always categorized format
  specialFields: SpecialFields;
  documentSummary?: DocumentSummary;
  complianceAnalysis?: ComplianceAnalysis;
  integrityAnalysis?: IntegrityAnalysis;
  analysisFields?: AnalysisFields;
  extractionDate: Date;
  extractionVersion: string;
  overallConfidence?: number;
  processingTimeMs?: number;
  modelUsed?: string;
  folderId?: string; // Auto-assigned folder based on extraction data
  reportingTo?: string; // Contract ID this contract reports to
  createdAt: Date;
  updatedAt: Date;
}

export class ContractExtraction {
  constructor(
    public readonly id: string,
    public readonly contractId: string,
    public readonly tenantId: string,
    public readonly fixedFields: FixedFields,
    public readonly dynamicFields: DynamicCategoricalFields,
    public readonly specialFields: SpecialFields,
    public readonly extractionDate: Date,
    public readonly extractionVersion: string,
    public readonly documentSummary?: DocumentSummary,
    public readonly complianceAnalysis?: ComplianceAnalysis,
    public readonly integrityAnalysis?: IntegrityAnalysis,
    public readonly analysisFields?: AnalysisFields,
    public readonly overallConfidence?: number,
    public readonly processingTimeMs?: number,
    public readonly modelUsed?: string,
    public readonly folderId?: string,
    public readonly reportingTo?: string,
    public readonly createdAt?: Date,
    public readonly updatedAt?: Date
  ) { }

  /**
   * Creates a ContractExtraction instance from persistence data
   */
  static fromPersistence(data: any): ContractExtraction {
    return new ContractExtraction(
      data.id,
      data.contractId,
      data.tenantId,
      data.fixedFields as FixedFields,
      data.dynamicFields as DynamicFields,
      data.specialFields as SpecialFields,
      data.extractionDate,
      data.extractionVersion,
      data.documentSummary as DocumentSummary,
      data.complianceAnalysis as ComplianceAnalysis,
      data.integrityAnalysis as IntegrityAnalysis,
      data.analysisFields as AnalysisFields,
      data.overallConfidence,
      data.processingTimeMs,
      data.modelUsed,
      data.folderId,
      data.reportingTo,
      data.createdAt,
      data.updatedAt
    );
  }

  /**
   * Converts the domain entity to a DTO for API responses
   */
  toDTO(): ContractExtractionData {
    return {
      id: this.id,
      contractId: this.contractId,
      tenantId: this.tenantId,
      fixedFields: this.fixedFields,
      dynamicFields: this.dynamicFields,
      specialFields: this.specialFields,
      documentSummary: this.documentSummary,
      complianceAnalysis: this.complianceAnalysis,
      integrityAnalysis: this.integrityAnalysis,
      analysisFields: this.analysisFields,
      extractionDate: this.extractionDate,
      extractionVersion: this.extractionVersion,
      overallConfidence: this.overallConfidence,
      processingTimeMs: this.processingTimeMs,
      modelUsed: this.modelUsed,
      folderId: this.folderId,
      reportingTo: this.reportingTo,
      createdAt: this.createdAt || new Date(),
      updatedAt: this.updatedAt || new Date(),
    };
  }

  /**
   * Calculates the overall confidence score from all fields
   */
  calculateOverallConfidence(): number {
    const allConfidences: number[] = [];

    // Add fixed field confidences
    Object.values(this.fixedFields).forEach((field) => {
      if (field.confidence > 0.1) {
        // Only include meaningful confidences
        allConfidences.push(field.confidence);
      }
    });

    // Add dynamic field confidences (flat structure)
    Object.values(this.dynamicFields).forEach((field) => {
      if (field.confidence > 0.1) {
        allConfidences.push(field.confidence);
      }
    });

    // Dynamic fields are in categorized format
    Object.values(this.dynamicFields).forEach((categoryFields) => {
      if (categoryFields && typeof categoryFields === "object") {
        Object.values(categoryFields).forEach((field) => {
          const dynamicField = field as DynamicField;
          if (dynamicField.confidence > 0.1) {
            allConfidences.push(dynamicField.confidence);
          }
        });
      }
    });

    // Add special field confidences
    Object.values(this.specialFields).forEach((vendorFields) => {
      Object.values(vendorFields).forEach((field: any) => {
        if (
          field &&
          typeof field.confidence === "number" &&
          field.confidence > 0.1
        ) {
          allConfidences.push(field.confidence);
        }
      });
    });

    if (allConfidences.length === 0) {
      return 0;
    }

    return (
      allConfidences.reduce((sum, conf) => sum + conf, 0) /
      allConfidences.length
    );
  }

  /**
   * Gets high confidence fields (>= 0.8)
   */
  getHighConfidenceFields(): Array<{
    field: string;
    confidence: number;
    value: string;
  }> {
    const highConfFields: Array<{
      field: string;
      confidence: number;
      value: string;
    }> = [];

    // Check fixed fields
    Object.entries(this.fixedFields).forEach(([key, field]) => {
      if (field.confidence >= 0.8) {
        highConfFields.push({
          field: key,
          confidence: field.confidence,
          value: field.value,
        });
      }
    });

    // Check dynamic fields
    Object.entries(this.dynamicFields).forEach(([key, field]) => {
      if (field.confidence >= 0.8) {
        highConfFields.push({
          field: key,
          confidence: field.confidence,
          value: field.value,
        });
      }
    });

    // Check categorized dynamic fields
    Object.entries(this.dynamicFields).forEach(([category, categoryFields]) => {
      if (categoryFields && typeof categoryFields === "object") {
        Object.entries(categoryFields).forEach(([key, field]) => {
          const dynamicField = field as DynamicField;
          if (dynamicField.confidence >= 0.8) {
            highConfFields.push({
              field: `${category}:${key}`,
              confidence: dynamicField.confidence,
              value: dynamicField.value,
            });
          }
        });
      }
    });

    return highConfFields;
  }

  /**
   * Gets low confidence fields (< 0.5) that may need review
   */
  getLowConfidenceFields(): Array<{
    field: string;
    confidence: number;
    value: string;
  }> {
    const lowConfFields: Array<{
      field: string;
      confidence: number;
      value: string;
    }> = [];

    // Check fixed fields
    Object.entries(this.fixedFields).forEach(([key, field]) => {
      if (field.confidence < 0.5 && field.value !== "N/A") {
        lowConfFields.push({
          field: key,
          confidence: field.confidence,
          value: field.value,
        });
      }
    });

    // Check dynamic fields
    Object.entries(this.dynamicFields).forEach(([key, field]) => {
      if (field.confidence < 0.5 && field.value !== "N/A") {
        lowConfFields.push({
          field: key,
          confidence: field.confidence,
          value: field.value,
        });
      }
    });

    // Check categorized dynamic fields
    Object.entries(this.dynamicFields).forEach(([category, categoryFields]) => {
      if (categoryFields && typeof categoryFields === "object") {
        Object.entries(categoryFields).forEach(([key, field]) => {
          const dynamicField = field as DynamicField;
          if (dynamicField.confidence < 0.5 && dynamicField.value !== "N/A") {
            lowConfFields.push({
              field: `${category}:${key}`,
              confidence: dynamicField.confidence,
              value: dynamicField.value,
            });
          }
        });
      }
    });

    return lowConfFields;
  }
}
