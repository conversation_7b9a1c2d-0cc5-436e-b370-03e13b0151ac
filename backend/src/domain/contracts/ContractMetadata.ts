/**
 * ContractMetadata Value Object
 * Represents extracted and enriched metadata about a contract
 */

import { Decimal } from "@prisma/client/runtime/library";

/**
 * Contract Metadata value object
 * Stores metadata and extracted data from Agreement Documents
 */
export class ContractMetadata {
  private _id: string;
  private _contractId: string;
  private _totalValue: Decimal | null;
  private _currency: string | null;
  private _paymentTerms: string | null;
  private _autoExtractedFields: Record<string, any> | null;
  private _customMetadata: Record<string, any> | null;
  private _confidenceScores: Record<string, number> | null;
  private _encryptedFields: string | null;
  private _lastExtractedAt: Date | null;
  private _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: {
    id: string;
    contractId: string;
    totalValue?: Decimal | null;
    currency?: string | null;
    paymentTerms?: string | null;
    autoExtractedFields?: Record<string, any> | null;
    customMetadata?: Record<string, any> | null;
    confidenceScores?: Record<string, number> | null;
    encryptedFields?: string | null;
    lastExtractedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
  }) {
    this._id = props.id;
    this._contractId = props.contractId;
    this._totalValue = props.totalValue || null;
    this._currency = props.currency || null;
    this._paymentTerms = props.paymentTerms || null;
    this._autoExtractedFields = props.autoExtractedFields || null;
    this._customMetadata = props.customMetadata || null;
    this._confidenceScores = props.confidenceScores || null;
    this._encryptedFields = props.encryptedFields || null;
    this._lastExtractedAt = props.lastExtractedAt || null;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  // Getters
  get id(): string {
    return this._id;
  }
  get contractId(): string {
    return this._contractId;
  }
  get totalValue(): Decimal | null {
    return this._totalValue;
  }
  get currency(): string | null {
    return this._currency;
  }
  get paymentTerms(): string | null {
    return this._paymentTerms;
  }
  get autoExtractedFields(): Record<string, any> | null {
    return this._autoExtractedFields;
  }
  get customMetadata(): Record<string, any> | null {
    return this._customMetadata;
  }
  get confidenceScores(): Record<string, number> | null {
    return this._confidenceScores;
  }
  get encryptedFields(): string | null {
    return this._encryptedFields;
  }
  get lastExtractedAt(): Date | null {
    return this._lastExtractedAt;
  }

  get createdAt(): Date {
    return this._createdAt;
  }
  get updatedAt(): Date {
    return this._updatedAt;
  }

  /**
   * Validates the contract metadata properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._contractId) {
      throw new Error("Contract ID is required");
    }

    // Validate currency code if provided
    if (this._currency && !this.isValidCurrencyCode(this._currency)) {
      throw new Error("Invalid currency code");
    }

    // Validate total value is positive if provided
    if (this._totalValue && this._totalValue.isNegative()) {
      throw new Error("Total value cannot be negative");
    }

    this.validateDates();
  }

  /**
   * Validates currency code format (ISO 4217)
   * @param code Currency code to validate
   * @returns boolean indicating if the currency code is valid
   */
  private isValidCurrencyCode(code: string): boolean {
    // Basic validation for ISO 4217 currency codes (3 uppercase letters)
    return /^[A-Z]{3}$/.test(code);
  }

  /**
   * Validates date relationships - method kept for backward compatibility but no longer validates dates
   * @throws Error if date validation fails
   */
  private validateDates(): void {
    // This method is kept for backward compatibility but no longer validates any dates
    // since effective_date and execution_date have been removed
  }

  /**
   * Checks if the metadata has sensitive information that may need encryption
   * @returns boolean indicating presence of potentially sensitive data
   */
  hasSensitiveInformation(): boolean {
    return (
      !!this._totalValue ||
      !!this._encryptedFields ||
      (!!this._autoExtractedFields &&
        Object.keys(this._autoExtractedFields).length > 0)
    );
  }

  /**
   * Updates financial information
   */
  updateFinancialInfo(params: {
    totalValue?: Decimal | null;
    currency?: string | null;
    paymentTerms?: string | null;
  }): void {
    if (params.totalValue !== undefined) {
      if (params.totalValue && params.totalValue.isNegative()) {
        throw new Error("Total value cannot be negative");
      }
      this._totalValue = params.totalValue;
    }

    if (params.currency !== undefined) {
      if (params.currency && !this.isValidCurrencyCode(params.currency)) {
        throw new Error("Invalid currency code");
      }
      this._currency = params.currency;
    }

    if (params.paymentTerms !== undefined) {
      this._paymentTerms = params.paymentTerms;
    }

    this._updatedAt = new Date();
  }

  /**
   * Updates contract dates - method kept for backward compatibility but no longer used
   */
  updateDates(params: {}): void {
    // This method is kept for backward compatibility but no longer updates any dates
    this._updatedAt = new Date();
  }

  /**
   * Updates auto-extracted fields (typically from AI processing)
   * @param fields Fields extracted by automated processing
   */
  updateAutoExtractedFields(fields: Record<string, any>): void {
    this._autoExtractedFields = fields;
    this._lastExtractedAt = new Date();
    this._updatedAt = new Date();
  }

  /**
   * Updates custom metadata fields
   * @param metadata Custom metadata to store
   */
  updateCustomMetadata(metadata: Record<string, any>): void {
    this._customMetadata = metadata;
    this._updatedAt = new Date();
  }

  /**
   * Updates encrypted fields
   * @param encryptedData Encrypted sensitive metadata (as a string)
   */
  updateEncryptedFields(encryptedData: string): void {
    this._encryptedFields = encryptedData;
    this._updatedAt = new Date();
  }

  /**
   * Updates confidence scores for AI-extracted fields
   * @param scores Confidence scores for extracted fields
   */
  updateConfidenceScores(scores: Record<string, number>): void {
    this._confidenceScores = scores;
    this._updatedAt = new Date();
  }

  /**
   * Converts the value object to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      contractId: this._contractId,
      totalValue: this._totalValue,
      currency: this._currency,
      paymentTerms: this._paymentTerms,
      autoExtractedFields: this._autoExtractedFields,
      customMetadata: this._customMetadata,
      encryptedFields: this._encryptedFields,
      lastExtractedAt: this._lastExtractedAt,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      confidence_scores: this._confidenceScores,
    };
  }

  /**
   * Creates a ContractMetadata value object from persistence data
   */
  static fromPersistence(data: any): ContractMetadata {
    return new ContractMetadata({
      id: data.id,
      contractId: data.contractId,
      totalValue: data.totalValue,
      currency: data.currency,
      paymentTerms: data.paymentTerms,
      autoExtractedFields: data.autoExtractedFields,
      customMetadata: data.customMetadata,
      encryptedFields: data.encryptedFields,
      lastExtractedAt: data.lastExtractedAt,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      confidenceScores: data.confidence_scores,
    });
  }
}
