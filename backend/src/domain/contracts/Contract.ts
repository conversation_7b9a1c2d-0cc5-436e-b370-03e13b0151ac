/**
 * Contract Entity - Core Domain Model
 * Represents a legal Agreement Document with its metadata and properties
 */

import {
  ContractStatus,
  SecurityClassification,
  ContractClassification,
  AgreementType,
} from "@prisma/client";
import { ContractExtraction } from "./ContractExtraction";

/**
 * Contract domain entity
 * Core entity for the Contract Repository
 */
export class Contract {
  private _id: string;
  private _title: string;
  private _description: string | null;
  private _contractNumber: string | null;
  private _classification: ContractClassification | null;
  private _agreementType: AgreementType;
  private _status: ContractStatus;
  private _startDate: Date | null;
  private _endDate: Date | null;
  private _renewalType: string | null;
  private _renewalDate: Date | null;
  private _isAutoRenew: boolean;
  private _noticePeriodDays: number | null;
  private _version: number;
  private _securityClassification: SecurityClassification;
  private _isEncrypted: boolean;
  private _currentVersionId: string | null;
  private _counterparty: string;
  private _value: string;
  private _provider: string | null;
  private _assessmentCompleted: boolean;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _tenantId: string;
  private _createdById: string | null;
  private _folderId: string | null;
  private _folderName: string | null;

  constructor(props: {
    id: string;
    title: string;
    description?: string | null;
    contractNumber?: string | null;
    classification?: ContractClassification | null;
    agreementType: AgreementType;
    status: ContractStatus;
    startDate?: Date | null;
    endDate?: Date | null;
    renewalType?: string | null;
    renewalDate?: Date | null;
    isAutoRenew?: boolean;
    noticePeriodDays?: number | null;
    version?: number;
    securityClassification?: SecurityClassification;
    isEncrypted?: boolean;
    currentVersionId?: string | null;
    counterparty?: string | null;
    value?: string | null;
    provider?: string | null;
    assessmentCompleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    tenantId: string;
    createdById?: string | null;
    folderId?: string | null;
    folderName?: string | null;
  }) {
    this._id = props.id;
    this._title = props.title;
    this._description = props.description || null;
    this._contractNumber = props.contractNumber || null;
    this._classification = props.classification || null;
    this._agreementType = props.agreementType;
    this._status = props.status;
    this._startDate = props.startDate || null;
    this._endDate = props.endDate || null;
    this._renewalType = props.renewalType || null;
    this._renewalDate = props.renewalDate || null;
    this._isAutoRenew = props.isAutoRenew || false;
    this._noticePeriodDays = props.noticePeriodDays || null;
    this._version = props.version || 1;
    this._securityClassification =
      props.securityClassification || SecurityClassification.CONFIDENTIAL;
    this._isEncrypted =
      props.isEncrypted !== undefined ? props.isEncrypted : true;
    this._currentVersionId = props.currentVersionId || null;
    this._counterparty =
      props.counterparty !== undefined && props.counterparty !== null
        ? props.counterparty
        : "";
    this._value =
      props.value !== undefined && props.value !== null ? props.value : "";
    this._provider = props.provider || null;
    this._assessmentCompleted = props.assessmentCompleted || false;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();
    this._tenantId = props.tenantId;
    this._createdById = props.createdById || null;
    this._folderId = props.folderId || null;
    this._folderName = props.folderName || null;

    this.validate();
  }

  // Getters
  get id(): string {
    return this._id;
  }
  get title(): string {
    return this._title;
  }
  get description(): string | null {
    return this._description;
  }
  get contractNumber(): string | null {
    return this._contractNumber;
  }
  get classification(): ContractClassification | null {
    return this._classification;
  }
  get agreementType(): AgreementType {
    return this._agreementType;
  }
  get status(): ContractStatus {
    return this._status;
  }
  get startDate(): Date | null {
    return this._startDate;
  }
  get endDate(): Date | null {
    return this._endDate;
  }
  get renewalType(): string | null {
    return this._renewalType;
  }
  get renewalDate(): Date | null {
    return this._renewalDate;
  }
  get isAutoRenew(): boolean {
    return this._isAutoRenew;
  }
  get noticePeriodDays(): number | null {
    return this._noticePeriodDays;
  }
  get version(): number {
    return this._version;
  }
  get securityClassification(): SecurityClassification {
    return this._securityClassification;
  }
  get isEncrypted(): boolean {
    return this._isEncrypted;
  }
  get currentVersionId(): string | null {
    return this._currentVersionId;
  }
  get counterparty(): string {
    return this._counterparty;
  }
  get value(): string {
    return this._value;
  }
  get provider(): string | null {
    return this._provider;
  }
  get assessmentCompleted(): boolean {
    return this._assessmentCompleted;
  }
  get createdAt(): Date {
    return this._createdAt;
  }
  get updatedAt(): Date {
    return this._updatedAt;
  }
  get tenantId(): string {
    return this._tenantId;
  }
  get createdById(): string | null {
    return this._createdById;
  }
  get folderId(): string | null {
    return this._folderId;
  }
  get folderName(): string | null {
    return this._folderName;
  }

  // Domain methods

  /**
   * Validates the contract entity properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._title || this._title.trim().length === 0) {
      throw new Error("Contract title is required");
    }

    if (!this._tenantId) {
      throw new Error("Tenant ID is required");
    }

    this.validateDates();
  }

  /**
   * Validates date relationships (start date, end date, renewal date)
   * @throws Error if date validation fails
   */
  private validateDates(): void {
    if (this._startDate && this._endDate && this._startDate > this._endDate) {
      throw new Error("Start date cannot be after end date");
    }

    if (
      this._endDate &&
      this._renewalDate &&
      this._renewalDate > this._endDate
    ) {
      throw new Error("Renewal date cannot be after end date");
    }
  }

  /**
   * Updates the contract status - deprecated, status is now calculated dynamically
   * @param newStatus The new contract status (ignored)
   * @deprecated Status is now calculated dynamically based on dates
   */
  updateStatus(newStatus: ContractStatus): void {
    // Status is now calculated dynamically, this method is kept for backward compatibility
    this._updatedAt = new Date();
  }

  /**
   * Updates the contract version
   * @param versionId ID of the new version
   * @param versionNumber New version number (incremented)
   */
  updateVersion(versionId: string, versionNumber: number): void {
    if (versionNumber <= this._version) {
      throw new Error(
        "New version number must be greater than current version"
      );
    }

    this._currentVersionId = versionId;
    this._version = versionNumber;
    this._updatedAt = new Date();
  }

  /**
   * Updates contract dates (start, end, renewal)
   */
  updateDates(params: {
    startDate?: Date | null;
    endDate?: Date | null;
    renewalDate?: Date | null;
  }): void {
    if (params.startDate !== undefined) this._startDate = params.startDate;
    if (params.endDate !== undefined) this._endDate = params.endDate;
    if (params.renewalDate !== undefined)
      this._renewalDate = params.renewalDate;

    this.validateDates();
    this._updatedAt = new Date();
  }

  /**
   * Checks if a contract is expired
   * @returns boolean indicating if contract is expired
   */
  isExpired(): boolean {
    if (!this._endDate) return false;
    return this._endDate < new Date();
  }

  /**
   * Calculates the current status of the contract based on dates
   * @returns Calculated contract status
   */
  calculateStatus(): "Active" | "Inactive" | "Unknown" {
    // If either start date or end date is missing, status is unknown
    if (!this._startDate || !this._endDate) {
      return "Unknown";
    }

    const currentDate = new Date();
    const startDate = new Date(this._startDate);
    const endDate = new Date(this._endDate);

    // Set time to start of day for accurate date comparison
    currentDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);

    // Active: current date is between start and end date (inclusive)
    if (currentDate >= startDate && currentDate <= endDate) {
      return "Active";
    }

    // Inactive: current date is outside the contract period
    return "Inactive";
  }

  /**
   * Checks if a contract is currently active
   * @returns boolean indicating if contract is currently active
   */
  isActive(): boolean {
    return this.calculateStatus() === "Active";
  }

  /**
   * Checks if a contract is expiring soon (within notice period)
   * @param daysThreshold Optional custom threshold in days
   * @returns boolean indicating if contract is expiring soon
   */
  isExpiringSoon(daysThreshold?: number): boolean {
    if (!this._endDate) return false;

    const threshold = daysThreshold || this._noticePeriodDays || 30; // Default 30 days if not specified
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() + threshold);

    return this._endDate <= thresholdDate && this._endDate > new Date();
  }

  /**
   * Updates security classification
   */
  updateSecurityClassification(classification: SecurityClassification): void {
    this._securityClassification = classification;
    this._updatedAt = new Date();
  }

  /**
   * Converts the domain entity to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      title: this._title,
      description: this._description,
      contractNumber: this._contractNumber,
      agreementType: this._agreementType, // Use agreementType as the primary type field
      classification: this._classification,
      status: this.calculateStatus(), // Use calculated status as the main status
      startDate: this._startDate,
      endDate: this._endDate,
      renewalType: this._renewalType,
      renewalDate: this._renewalDate,
      isAutoRenew: this._isAutoRenew,
      noticePeriodDays: this._noticePeriodDays,
      version: this._version,
      securityClassification: this._securityClassification,
      isEncrypted: this._isEncrypted,
      currentVersionId: this._currentVersionId,
      counterparty: this._counterparty,
      value: this._value,
      provider: this._provider,
      assessmentCompleted: this._assessmentCompleted,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      tenantId: this._tenantId,
      createdById: this._createdById,
      folderId: this._folderId,
      folderName: this._folderName,
    };
  }

  /**
   * Creates a Contract entity from persistence data
   */
  static fromPersistence(data: any): Contract {
    return new Contract({
      id: data.id,
      title: data.title,
      description: data.description,
      contractNumber: data.contractNumber,
      classification: data.classification,
      agreementType: data.agreementType,
      status: data.status,
      startDate: data.startDate,
      endDate: data.endDate,
      renewalType: data.renewalType,
      renewalDate: data.renewalDate,
      isAutoRenew: data.isAutoRenew,
      noticePeriodDays: data.noticePeriodDays,
      version: data.version,
      securityClassification: data.securityClassification,
      isEncrypted: data.isEncrypted,
      currentVersionId: data.currentVersionId,
      counterparty: data.counterparty,
      value: data.value,
      provider: data.provider,
      assessmentCompleted: data.assessmentCompleted,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      tenantId: data.tenantId,
      createdById: data.createdById,
      folderId: data.folderId,
      folderName: data.folder?.name,
    });
  }
}
