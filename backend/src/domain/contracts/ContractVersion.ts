/**
 * ContractVersion Value Object
 * Represents a specific version of a Agreement Document
 */

import { DocumentFormat } from "@prisma/client";

/**
 * Contract Version value object
 * Stores information about a specific version of a Agreement Document
 */
export class ContractVersion {
  private _id: string;
  private _contractId: string;
  private _versionNumber: number;
  private _documentUri: string;
  private _documentHash: string | null;
  private _documentFormat: DocumentFormat;
  private _documentSize: number;
  private _documentName: string;
  private _documentContent: Buffer | null;
  private _mimeType: string | null;
  private _encryptionKeyId: string | null;
  private _versionComment: string | null;
  private _changelog: Record<string, any> | null;
  private _metadata: Record<string, any> | null;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _createdBy: string | null;

  constructor(props: {
    id: string;
    contractId: string;
    versionNumber: number;
    documentUri: string;
    documentHash?: string | null;
    documentFormat: DocumentFormat;
    documentSize: number;
    documentName: string;
    documentContent?: Buffer | null;
    mimeType?: string | null;
    encryptionKeyId?: string | null;
    versionComment?: string | null;
    changelog?: Record<string, any> | null;
    metadata?: Record<string, any> | null;
    createdAt?: Date;
    updatedAt?: Date;
    createdBy?: string | null;
  }) {
    this._id = props.id;
    this._contractId = props.contractId;
    this._versionNumber = props.versionNumber;
    this._documentUri = props.documentUri;
    this._documentHash = props.documentHash || null;
    this._documentFormat = props.documentFormat;
    this._documentSize = props.documentSize;
    this._documentName = props.documentName;
    this._documentContent = props.documentContent || null;
    this._mimeType = props.mimeType || null;
    this._encryptionKeyId = props.encryptionKeyId || null;
    this._versionComment = props.versionComment || null;
    this._changelog = props.changelog || null;
    this._metadata = props.metadata || null;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();
    this._createdBy = props.createdBy || null;

    this.validate();
  }

  // Getters
  get id(): string {
    return this._id;
  }
  get contractId(): string {
    return this._contractId;
  }
  get versionNumber(): number {
    return this._versionNumber;
  }
  get documentUri(): string {
    return this._documentUri;
  }
  get documentHash(): string | null {
    return this._documentHash;
  }
  get documentFormat(): DocumentFormat {
    return this._documentFormat;
  }
  get documentSize(): number {
    return this._documentSize;
  }
  get documentName(): string {
    return this._documentName;
  }
  get documentContent(): Buffer | null {
    return this._documentContent;
  }
  get mimeType(): string | null {
    return this._mimeType;
  }
  get encryptionKeyId(): string | null {
    return this._encryptionKeyId;
  }
  get versionComment(): string | null {
    return this._versionComment;
  }
  get changelog(): Record<string, any> | null {
    return this._changelog;
  }
  get metadata(): Record<string, any> | null {
    return this._metadata;
  }
  get createdAt(): Date {
    return this._createdAt;
  }
  get updatedAt(): Date {
    return this._updatedAt;
  }
  get createdBy(): string | null {
    return this._createdBy;
  }

  /**
   * Validates the contract version properties
   * @throws Error if validation fails
   */
  private validate(): void {
    if (!this._contractId) {
      throw new Error("Contract ID is required");
    }

    if (this._versionNumber <= 0) {
      throw new Error("Version number must be greater than 0");
    }

    if (!this._documentUri || this._documentUri.trim().length === 0) {
      throw new Error("Document URI is required");
    }

    if (this._documentSize <= 0) {
      throw new Error("Document size must be greater than 0");
    }

    if (!this._documentName || this._documentName.trim().length === 0) {
      throw new Error("Document name is required");
    }
  }

  /**
   * Checks if the document is encrypted
   * @returns boolean indicating if the document is encrypted
   */
  isEncrypted(): boolean {
    return !!this._encryptionKeyId;
  }

  /**
   * Verifies document integrity using stored hash
   * @param calculatedHash Hash calculated from the document
   * @returns boolean indicating if the document hash matches
   */
  verifyIntegrity(calculatedHash: string): boolean {
    if (!this._documentHash) {
      throw new Error("No document hash available for verification");
    }
    return this._documentHash === calculatedHash;
  }

  /**
   * Updates document hash after verification or recalculation
   * @param newHash The new document hash to store
   */
  updateDocumentHash(newHash: string): void {
    this._documentHash = newHash;
    this._updatedAt = new Date();
  }

  /**
   * Updates version metadata
   * @param metadata The metadata object to store
   */
  updateMetadata(metadata: Record<string, any>): void {
    this._metadata = metadata;
    this._updatedAt = new Date();
  }

  /**
   * Converts the value object to a data transfer object for persistence/presentation
   */
  toDTO() {
    return {
      id: this._id,
      contractId: this._contractId,
      versionNumber: this._versionNumber,
      documentUri: this._documentUri,
      documentHash: this._documentHash,
      documentFormat: this._documentFormat,
      documentSize: this._documentSize,
      documentName: this._documentName,
      documentContent: this._documentContent,
      mimeType: this._mimeType,
      encryptionKeyId: this._encryptionKeyId,
      versionComment: this._versionComment,
      changelog: this._changelog,
      metadata: this._metadata,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      createdBy: this._createdBy,
    };
  }

  /**
   * Creates a ContractVersion value object from persistence data
   */
  static fromPersistence(data: any): ContractVersion {
    return new ContractVersion({
      id: data.id,
      contractId: data.contractId,
      versionNumber: data.versionNumber,
      documentUri: data.documentUri,
      documentHash: data.documentHash,
      documentFormat: data.documentFormat,
      documentSize: data.documentSize,
      documentName: data.documentName,
      documentContent: data.documentContent,
      mimeType: data.mimeType,
      encryptionKeyId: data.encryptionKeyId,
      versionComment: data.versionComment,
      changelog: data.changelog,
      metadata: data.metadata,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      createdBy: data.createdBy,
    });
  }
}
