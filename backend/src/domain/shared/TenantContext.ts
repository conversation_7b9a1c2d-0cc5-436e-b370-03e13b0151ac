/**
 * Tenant Context
 * Provides a thread-local storage for the current tenant ID
 * This ensures proper multi-tenancy isolation at the application level
 */

import { AsyncLocalStorage } from 'async_hooks';
import { logger } from '../../infrastructure/logging/logger';

/**
 * TenantContext class for managing tenant context throughout request lifecycle
 */
export class TenantContext {
  private static asyncLocalStorage = new AsyncLocalStorage<Map<string, any>>();

  /**
   * Sets the current tenant ID for the execution context
   * @param tenantId Tenant ID
   */
  static setCurrentTenant(tenantId: string): void {
    const store = TenantContext.asyncLocalStorage.getStore();
    
    if (store) {
      store.set('tenantId', tenantId);
      logger.debug('Tenant context set', { tenantId });
    } else {
      logger.warn('Failed to set tenant context - no store available');
    }
  }

  /**
   * Gets the current tenant ID from the execution context
   * @returns Current tenant ID or undefined if not set
   */
  static getCurrentTenant(): string | undefined {
    const store = TenantContext.asyncLocalStorage.getStore();
    
    if (!store) {
      logger.warn('No tenant context available');
      return undefined;
    }
    
    return store.get('tenantId');
  }

  /**
   * Runs a callback with a tenant context
   * @param tenantId Tenant ID
   * @param callback Function to run within the tenant context
   * @returns Result of the callback
   */
  static runWithTenant<T>(tenantId: string, callback: () => T): T {
    return TenantContext.asyncLocalStorage.run(new Map([['tenantId', tenantId]]), callback);
  }

  /**
   * Middleware to initialize tenant context for each request
   * This should be applied before any other middleware that needs tenant context
   */
  static middleware() {
    return (req: any, res: any, next: any) => {
      TenantContext.asyncLocalStorage.run(new Map(), () => {
        // Tenant ID will be set by auth middleware when token is verified
        next();
      });
    };
  }

  /**
   * Ensures that a tenant ID is available in the current context
   * @throws Error if no tenant ID is available
   */
  static ensureTenantContext(): string {
    const tenantId = TenantContext.getCurrentTenant();
    
    if (!tenantId) {
      const error = new Error('No tenant context available');
      logger.error('Tenant context missing', { error });
      throw error;
    }
    
    return tenantId;
  }
}
