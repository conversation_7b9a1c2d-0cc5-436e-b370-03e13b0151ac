/**
 * Folder Domain Entity
 * Represents a folder for organizing contracts
 */

export interface FolderProps {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  tenantId: string;
  createdById?: string;
}

export class Folder {
  private _id: string;
  private _name: string;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _tenantId: string;
  private _createdById?: string;

  constructor(props: FolderProps) {
    this._id = props.id;
    this._name = props.name;
    this._createdAt = props.createdAt;
    this._updatedAt = props.updatedAt;
    this._tenantId = props.tenantId;
    this._createdById = props.createdById;
  }

  // Getters
  get id(): string {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get tenantId(): string {
    return this._tenantId;
  }

  get createdById(): string | undefined {
    return this._createdById;
  }

  // Business methods
  updateName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error("Folder name cannot be empty");
    }
    if (name.length > 255) {
      throw new Error("Folder name cannot exceed 255 characters");
    }
    this._name = name.trim();
    this._updatedAt = new Date();
  }

  // Validation
  static validateName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error("Folder name is required");
    }
    if (name.length > 255) {
      throw new Error("Folder name cannot exceed 255 characters");
    }
  }

  // DTO conversion
  toDTO() {
    return {
      id: this._id,
      name: this._name,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      tenantId: this._tenantId,
      createdById: this._createdById,
    };
  }

  // Factory method
  static create(props: Omit<FolderProps, 'id' | 'createdAt' | 'updatedAt'>): Folder {
    const now = new Date();
    return new Folder({
      ...props,
      id: '', // Will be set by repository
      createdAt: now,
      updatedAt: now,
    });
  }
}
