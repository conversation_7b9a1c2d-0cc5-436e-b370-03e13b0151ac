/**
 * Folder Repository Interface
 * Defines the contract for folder data access
 */

import { Folder } from "../Folder";

export interface FolderSearchParams {
  name?: string;
  tenantId: string;
  createdById?: string;
  page?: number;
  limit?: number;
}

export interface FolderWithContractCount {
  folder: Folder;
  contractCount: number;
  contracts: any[]; // Contract data from Prisma
}

export interface IFolderRepository {
  /**
   * Creates a new folder
   * @param folder Folder entity to create
   * @returns Created folder with generated ID
   */
  create(folder: Folder): Promise<Folder>;

  /**
   * Finds a folder by ID
   * @param id Folder ID
   * @returns Folder or null if not found
   */
  findById(id: string): Promise<Folder | null>;

  /**
   * Finds folders by search parameters
   * @param params Search parameters
   * @returns Array of folders
   */
  findMany(params: FolderSearchParams): Promise<Folder[]>;

  /**
   * Finds folders with contract counts
   * @param params Search parameters
   * @returns Array of folders with contract counts
   */
  findManyWithContractCount(
    params: FolderSearchParams
  ): Promise<FolderWithContractCount[]>;

  /**
   * Updates a folder
   * @param id Folder ID
   * @param folder Updated folder data
   * @returns Updated folder or null if not found
   */
  update(id: string, folder: Partial<Folder>): Promise<Folder | null>;

  /**
   * Deletes a folder
   * @param id Folder ID
   * @returns True if deleted, false if not found
   */
  delete(id: string): Promise<boolean>;

  /**
   * Checks if a folder name exists for a tenant
   * @param name Folder name
   * @param tenantId Tenant ID
   * @param excludeId Optional folder ID to exclude from check
   * @returns True if name exists, false otherwise
   */
  nameExists(
    name: string,
    tenantId: string,
    excludeId?: string
  ): Promise<boolean>;

  /**
   * Gets the total count of folders for search parameters
   * @param params Search parameters
   * @returns Total count
   */
  count(params: FolderSearchParams): Promise<number>;

  /**
   * Moves contracts to a folder
   * @param contractIds Array of contract IDs
   * @param folderId Folder ID (null to remove from folder)
   * @param tenantId Tenant ID for validation
   * @returns Number of contracts moved
   */
  moveContractsToFolder(
    contractIds: string[],
    folderId: string | null,
    tenantId: string
  ): Promise<number>;
}
