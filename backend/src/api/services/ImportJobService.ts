/**
 * Import Job Service
 * Handles background processing and status tracking for contract imports
 */

import { logger } from "../../infrastructure/logging/logger";
import { NotificationService } from "./NotificationService";
import { NotificationType, NotificationPriority, PrismaClient } from "@prisma/client";

export interface ImportJob {
  id: string;
  tenantId: string;
  userId: string;
  status: "pending" | "processing" | "completed" | "failed";
  totalFiles: number;
  processedFiles: number;
  successfulFiles: number;
  failedFiles: number;
  errors: Array<{
    file?: string;
    row?: number;
    error: string;
    data?: any;
  }>;
  contracts: Array<{
    id: string;
    title: string;
    status: string;
    metadata?: any;
  }>;
  startTime: Date;
  endTime?: Date;
  estimatedTimeSeconds?: number;
  batchInfo?: {
    totalFiles: number;
    concurrencyLimit: number;
    estimatedBatches: number;
  };
}

export interface ImportJobResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    file?: string;
    row?: number;
    error: string;
    data?: any;
  }>;
  contracts: Array<{
    id: string;
    title: string;
    status: string;
    metadata?: any;
  }>;
}

/**
 * Service for managing import jobs and their status
 */
export class ImportJobService {
  private jobs: Map<string, ImportJob> = new Map();
  private readonly JOB_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24 hours
  private notificationService: NotificationService;

  constructor() {
    // Initialize notification service
    const prisma = new PrismaClient();
    this.notificationService = new NotificationService(prisma);

    // Clean up old jobs periodically
    setInterval(() => {
      this.cleanupOldJobs();
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Creates a new import job
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param totalFiles Total number of files to process
   * @param estimatedTimeSeconds Estimated processing time
   * @param batchInfo Batch processing information
   * @returns Job ID
   */
  createJob(
    tenantId: string,
    userId: string,
    totalFiles: number,
    estimatedTimeSeconds?: number,
    batchInfo?: {
      totalFiles: number;
      concurrencyLimit: number;
      estimatedBatches: number;
    }
  ): string {
    const jobId = this.generateJobId();

    const job: ImportJob = {
      id: jobId,
      tenantId,
      userId,
      status: "pending",
      totalFiles,
      processedFiles: 0,
      successfulFiles: 0,
      failedFiles: 0,
      errors: [],
      contracts: [],
      startTime: new Date(),
      estimatedTimeSeconds,
      batchInfo,
    };

    this.jobs.set(jobId, job);
    logger.info(
      `Created import job ${jobId} for tenant ${tenantId} with ${totalFiles} files`
    );

    // Create notification for job start
    this.createJobStartNotification(job).catch((error) => {
      logger.error("Failed to create job start notification:", error);
    });

    return jobId;
  }

  /**
   * Updates job status
   * @param jobId Job ID
   * @param status New status
   */
  updateJobStatus(jobId: string, status: ImportJob["status"]): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = status;
      if (status === "completed" || status === "failed") {
        job.endTime = new Date();
      }
      logger.info(`Updated job ${jobId} status to ${status}`);
    }
  }

  /**
   * Updates job progress
   * @param jobId Job ID
   * @param processedFiles Number of processed files
   * @param successfulFiles Number of successful files
   * @param failedFiles Number of failed files
   */
  updateJobProgress(
    jobId: string,
    processedFiles: number,
    successfulFiles: number,
    failedFiles: number
  ): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.processedFiles = processedFiles;
      job.successfulFiles = successfulFiles;
      job.failedFiles = failedFiles;
      logger.debug(
        `Updated job ${jobId} progress: ${processedFiles}/${job.totalFiles} files processed`
      );
    }
  }

  /**
   * Adds an error to the job
   * @param jobId Job ID
   * @param error Error information
   */
  addJobError(
    jobId: string,
    error: { file?: string; row?: number; error: string; data?: any }
  ): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.errors.push(error);
      logger.warn(
        `Added error to job ${jobId} for file ${error.file}: ${error.error}`
      );
    }
  }

  /**
   * Adds a successful contract to the job
   * @param jobId Job ID
   * @param contract Contract information
   */
  addJobContract(
    jobId: string,
    contract: { id: string; title: string; status: string; metadata?: any }
  ): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.contracts.push(contract);
      logger.debug(`Added contract ${contract.id} to job ${jobId}`);
    }
  }

  /**
   * Completes a job with final results
   * @param jobId Job ID
   * @param result Final import result
   */
  completeJob(jobId: string, result: ImportJobResult): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = "completed";
      job.endTime = new Date();
      job.processedFiles = result.totalProcessed;
      job.successfulFiles = result.successful;
      job.failedFiles = result.failed;
      job.errors = result.errors;
      job.contracts = result.contracts;

      logger.info(
        `Completed job ${jobId}: ${result.successful} successful, ${result.failed} failed`
      );

      // Create notification for job completion
      this.createJobCompleteNotification(job).catch((error) => {
        logger.error("Failed to create job completion notification:", error);
      });
    }
  }

  /**
   * Fails a job with error information
   * @param jobId Job ID
   * @param error Error message
   */
  failJob(jobId: string, error: string): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = "failed";
      job.endTime = new Date();
      job.errors.push({
        file: "SYSTEM",
        error,
      });

      logger.error(`Failed job ${jobId}: ${error}`);

      // Create notification for job failure
      this.createJobFailureNotification(job).catch((notificationError) => {
        logger.error("Failed to create job failure notification:", notificationError);
      });
    }
  }

  /**
   * Gets job status
   * @param jobId Job ID
   * @returns Job information or null if not found
   */
  getJob(jobId: string): ImportJob | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Gets jobs for a specific tenant
   * @param tenantId Tenant ID
   * @returns Array of jobs for the tenant
   */
  getJobsForTenant(tenantId: string): ImportJob[] {
    return Array.from(this.jobs.values()).filter(
      (job) => job.tenantId === tenantId
    );
  }

  /**
   * Generates a unique job ID
   * @returns Unique job ID
   */
  private generateJobId(): string {
    return `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleans up old completed jobs
   */
  private cleanupOldJobs(): void {
    const now = Date.now();
    const jobsToDelete: string[] = [];

    for (const [jobId, job] of this.jobs.entries()) {
      if (
        (job.status === "completed" || job.status === "failed") &&
        job.endTime &&
        now - job.endTime.getTime() > this.JOB_RETENTION_TIME
      ) {
        jobsToDelete.push(jobId);
      }
    }

    jobsToDelete.forEach((jobId) => {
      this.jobs.delete(jobId);
      logger.debug(`Cleaned up old job ${jobId}`);
    });

    if (jobsToDelete.length > 0) {
      logger.info(`Cleaned up ${jobsToDelete.length} old import jobs`);
    }
  }



  /**
   * Creates a notification when a job starts
   * @param job Import job
   */
  private async createJobStartNotification(job: ImportJob): Promise<void> {
    try {
      await this.notificationService.createContractNotification({
        title: "Contract Import Started",
        content: `Processing ${job.totalFiles} contract${job.totalFiles > 1 ? 's' : ''} in the background. You'll be notified when complete.`,
        userId: job.userId,
        tenantId: job.tenantId,
        contractId: "", // No specific contract ID for batch jobs
        priority: NotificationPriority.MEDIUM,
        actionUrl: `/contract-management/contracts`,
        metadata: {
          jobId: job.id,
          totalFiles: job.totalFiles,
          estimatedTimeSeconds: job.estimatedTimeSeconds,
          batchInfo: job.batchInfo,
        },
      });
    } catch (error) {
      logger.error("Error creating job start notification:", error);
    }
  }

  /**
   * Creates a notification when a job completes successfully
   * @param job Import job
   */
  private async createJobCompleteNotification(job: ImportJob): Promise<void> {
    try {
      const successMessage = job.successfulFiles > 0
        ? `Successfully imported ${job.successfulFiles} contract${job.successfulFiles > 1 ? 's' : ''}.`
        : "Import completed.";

      const failureMessage = job.failedFiles > 0
        ? ` ${job.failedFiles} file${job.failedFiles > 1 ? 's' : ''} failed to import.`
        : "";

      await this.notificationService.createContractNotification({
        title: "Contract Import Complete",
        content: successMessage + failureMessage,
        userId: job.userId,
        tenantId: job.tenantId,
        contractId: "", // No specific contract ID for batch jobs
        priority: job.failedFiles > 0 ? NotificationPriority.HIGH : NotificationPriority.MEDIUM,
        actionUrl: `/contract-management/contracts`,
        metadata: {
          jobId: job.id,
          totalFiles: job.totalFiles,
          successfulFiles: job.successfulFiles,
          failedFiles: job.failedFiles,
          processingTime: job.endTime && job.startTime
            ? Math.round((job.endTime.getTime() - job.startTime.getTime()) / 1000)
            : undefined,
          contracts: job.contracts,
        },
      });
    } catch (error) {
      logger.error("Error creating job completion notification:", error);
    }
  }

  /**
   * Creates a notification when a job fails
   * @param job Import job
   */
  private async createJobFailureNotification(job: ImportJob): Promise<void> {
    try {
      const errorMessage = job.errors.length > 0
        ? job.errors[job.errors.length - 1].error // Get the last error
        : "Unknown error occurred during import.";

      await this.notificationService.createContractNotification({
        title: "Contract Import Failed",
        content: `Failed to import contracts: ${errorMessage}`,
        userId: job.userId,
        tenantId: job.tenantId,
        contractId: "", // No specific contract ID for batch jobs
        priority: NotificationPriority.HIGH,
        actionUrl: `/contract-management/contracts`,
        metadata: {
          jobId: job.id,
          totalFiles: job.totalFiles,
          errors: job.errors,
          processingTime: job.endTime && job.startTime
            ? Math.round((job.endTime.getTime() - job.startTime.getTime()) / 1000)
            : undefined,
        },
      });
    } catch (error) {
      logger.error("Error creating job failure notification:", error);
    }
  }
}

// Create a singleton instance
export const importJobService = new ImportJobService();
