/**
 * License Service
 * Implements business logic for license management
 */

import { DocumentFormat, PrismaClient } from "@prisma/client";
import { LicenseType, LicenseStatus } from "../../domain/licenses/License";
import { LicenseDocumentType } from "../../domain/licenses/LicenseDocument";
import { License } from "../../domain/licenses/License";
import { LicenseEntitlement } from "../../domain/licenses/LicenseEntitlement";
import { LicenseDocument } from "../../domain/licenses/LicenseDocument";
import { LicenseUsage } from "../../domain/licenses/LicenseUsage";
import {
  ILicenseRepository,
  LicenseSearchParams,
} from "../../domain/licenses/interfaces/ILicenseRepository";
import { EncryptionService } from "../../infrastructure/services/EncryptionService";
import { v4 as uuidv4 } from "uuid";
import { Decimal } from "@prisma/client/runtime/library";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Service for managing licenses
 */
export class LicenseService {
  private licenseRepository: ILicenseRepository;
  private encryptionService: EncryptionService;

  constructor(
    licenseRepository: ILicenseRepository,
    encryptionService: EncryptionService
  ) {
    this.licenseRepository = licenseRepository;
    this.encryptionService = encryptionService;
  }

  /**
   * Creates a new license
   * @param licenseData License data for creation
   * @returns Created license
   */
  async createLicense(licenseData: {
    name: string;
    description?: string;
    licenseNumber?: string;
    licenseType: LicenseType;
    status?: LicenseStatus;
    vendor: string;
    purchaseDate?: Date;
    startDate?: Date;
    endDate?: Date;
    renewalType?: string;
    renewalDate?: Date;
    isAutoRenew?: boolean;
    noticePeriodDays?: number;
    totalValue?: number | string;
    currency?: string;
    costPeriod?: string;
    totalLicenses: number;
    assignedLicenses?: number;
    tenantId: string;
    createdById?: string;
  }): Promise<License> {
    try {
      // Set default values where needed
      const status = licenseData.status || LicenseStatus.ACTIVE;
      const assignedLicenses = licenseData.assignedLicenses || 0;
      const availableLicenses = licenseData.totalLicenses - assignedLicenses;

      // Convert totalValue to Decimal if provided
      let totalValueDecimal: Decimal | null = null;
      if (licenseData.totalValue !== undefined) {
        totalValueDecimal = new Decimal(licenseData.totalValue.toString());
      }

      // Create a new license entity
      const license = new License({
        id: uuidv4(),
        name: licenseData.name,
        description: licenseData.description,
        licenseNumber: licenseData.licenseNumber,
        licenseType: licenseData.licenseType,
        status,
        vendor: licenseData.vendor,
        purchaseDate: licenseData.purchaseDate,
        startDate: licenseData.startDate,
        endDate: licenseData.endDate,
        renewalType: licenseData.renewalType,
        renewalDate: licenseData.renewalDate,
        isAutoRenew: licenseData.isAutoRenew,
        noticePeriodDays: licenseData.noticePeriodDays,
        totalValue: totalValueDecimal,
        currency: licenseData.currency,
        costPeriod: licenseData.costPeriod,
        totalLicenses: licenseData.totalLicenses,
        assignedLicenses,
        availableLicenses,
        tenantId: licenseData.tenantId,
        createdById: licenseData.createdById,
      });

      // Save the license
      return await this.licenseRepository.create(license);
    } catch (error) {
      logger.error("Error creating license:", { error });
      throw new Error(`Failed to create license: ${(error as Error).message}`);
    }
  }

  /**
   * Gets a license by ID
   * @param id License ID
   * @returns License entity or null if not found
   */
  async getLicenseById(id: string): Promise<License | null> {
    try {
      return await this.licenseRepository.findById(id);
    } catch (error) {
      logger.error("Error getting license by ID:", { error });
      throw new Error(`Failed to get license: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an existing license
   * @param id License ID
   * @param licenseData License data for update
   * @returns Updated license
   */
  async updateLicense(
    id: string,
    licenseData: {
      name?: string;
      description?: string;
      licenseNumber?: string;
      licenseType?: LicenseType;
      status?: LicenseStatus;
      vendor?: string;
      purchaseDate?: Date;
      startDate?: Date;
      endDate?: Date;
      renewalType?: string;
      renewalDate?: Date;
      isAutoRenew?: boolean;
      noticePeriodDays?: number;
      totalValue?: number | string;
      currency?: string;
      costPeriod?: string;
      totalLicenses?: number;
      assignedLicenses?: number;
    }
  ): Promise<License> {
    try {
      // Retrieve existing license
      const existingLicense = await this.licenseRepository.findById(id);

      if (!existingLicense) {
        throw new Error(`License with ID ${id} not found`);
      }

      // Convert totalValue to Decimal if provided
      let totalValueDecimal: Decimal | null = null;
      if (licenseData.totalValue !== undefined) {
        totalValueDecimal = new Decimal(licenseData.totalValue.toString());
      }

      // Update license details
      existingLicense.updateDetails({
        name: licenseData.name,
        description: licenseData.description,
        licenseNumber: licenseData.licenseNumber,
        licenseType: licenseData.licenseType,
        vendor: licenseData.vendor,
        purchaseDate: licenseData.purchaseDate,
        startDate: licenseData.startDate,
        endDate: licenseData.endDate,
        renewalType: licenseData.renewalType,
        renewalDate: licenseData.renewalDate,
        isAutoRenew: licenseData.isAutoRenew,
        noticePeriodDays: licenseData.noticePeriodDays,
        totalValue: totalValueDecimal,
        currency: licenseData.currency,
        costPeriod: licenseData.costPeriod,
      });

      // Update license allocation if provided
      if (
        licenseData.totalLicenses !== undefined ||
        licenseData.assignedLicenses !== undefined
      ) {
        existingLicense.updateAllocation({
          totalLicenses: licenseData.totalLicenses,
          assignedLicenses: licenseData.assignedLicenses,
        });
      }

      // Update license status if provided
      if (licenseData.status !== undefined) {
        existingLicense.updateStatus(licenseData.status);
      }

      // Save changes
      return await this.licenseRepository.update(existingLicense);
    } catch (error) {
      logger.error("Error updating license:", { error });
      throw new Error(`Failed to update license: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes a license by ID
   * @param id License ID
   * @returns true if deleted, false if not found
   */
  async deleteLicense(id: string): Promise<boolean> {
    try {
      return await this.licenseRepository.delete(id);
    } catch (error) {
      logger.error("Error deleting license:", { error });
      throw new Error(`Failed to delete license: ${(error as Error).message}`);
    }
  }

  /**
   * Searches for licenses based on various parameters
   * @param params Search parameters
   * @returns Array of licenses matching the search criteria and total count
   */
  async searchLicenses(
    params: LicenseSearchParams
  ): Promise<{ licenses: License[]; total: number }> {
    try {
      return await this.licenseRepository.search(params);
    } catch (error) {
      logger.error("Error searching licenses:", { error });
      throw new Error(`Failed to search licenses: ${(error as Error).message}`);
    }
  }

  /**
   * Gets all licenses for a tenant
   * @param tenantId Tenant ID
   * @param page Page number (optional, default: 1)
   * @param limit Items per page (optional, default: 20)
   * @returns Array of licenses and total count
   */
  async getLicensesByTenant(
    tenantId: string,
    page?: number,
    limit?: number
  ): Promise<{ licenses: License[]; total: number }> {
    try {
      return await this.licenseRepository.getByTenant(tenantId, page, limit);
    } catch (error) {
      logger.error("Error getting licenses by tenant:", { error });
      throw new Error(
        `Failed to get licenses by tenant: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets licenses expiring within a specified period
   * @param tenantId Tenant ID
   * @param days Number of days from now
   * @returns Array of expiring licenses
   */
  async getExpiringLicenses(
    tenantId: string,
    days: number
  ): Promise<License[]> {
    try {
      return await this.licenseRepository.getExpiring(tenantId, days);
    } catch (error) {
      logger.error("Error getting expiring licenses:", { error });
      throw new Error(
        `Failed to get expiring licenses: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets licenses with compliance issues
   * @param tenantId Tenant ID
   * @returns Array of licenses with compliance issues
   */
  async getLicensesWithComplianceIssues(tenantId: string): Promise<License[]> {
    try {
      return await this.licenseRepository.getWithComplianceIssues(tenantId);
    } catch (error) {
      logger.error("Error getting licenses with compliance issues:", { error });
      throw new Error(
        `Failed to get licenses with compliance issues: ${
          (error as Error).message
        }`
      );
    }
  }

  /**
   * Gets licenses with utilization issues (over or under-utilized)
   * @param tenantId Tenant ID
   * @param threshold Utilization threshold percentage (default: 70%)
   * @returns Array of licenses with utilization issues
   */
  async getLicensesWithUtilizationIssues(
    tenantId: string,
    threshold?: number
  ): Promise<License[]> {
    try {
      return await this.licenseRepository.getWithUtilizationIssues(
        tenantId,
        threshold
      );
    } catch (error) {
      logger.error("Error getting licenses with utilization issues:", {
        error,
      });
      throw new Error(
        `Failed to get licenses with utilization issues: ${
          (error as Error).message
        }`
      );
    }
  }

  /**
   * Renews a license with a new end date
   * @param licenseId License ID
   * @param newEndDate New end date for the renewed license
   * @returns Updated license
   */
  async renewLicense(licenseId: string, newEndDate: Date): Promise<License> {
    try {
      const license = await this.licenseRepository.findById(licenseId);

      if (!license) {
        throw new Error(`License with ID ${licenseId} not found`);
      }

      // Renew the license
      license.renew(newEndDate);

      // Save changes
      return await this.licenseRepository.update(license);
    } catch (error) {
      logger.error("Error renewing license:", { error });
      throw new Error(`Failed to renew license: ${(error as Error).message}`);
    }
  }

  /**
   * Updates the compliance status of a license
   * @param licenseId License ID
   * @param complianceStatus New compliance status
   * @returns Updated license
   */
  async updateComplianceStatus(
    licenseId: string,
    complianceStatus: string
  ): Promise<License> {
    try {
      const license = await this.licenseRepository.findById(licenseId);

      if (!license) {
        throw new Error(`License with ID ${licenseId} not found`);
      }

      // Update compliance status
      license.updateComplianceStatus(complianceStatus);

      // Save changes
      return await this.licenseRepository.update(license);
    } catch (error) {
      logger.error("Error updating license compliance status:", { error });
      throw new Error(
        `Failed to update license compliance status: ${
          (error as Error).message
        }`
      );
    }
  }

  // License Entitlement methods

  /**
   * Creates a new license entitlement
   * @param entitlementData Entitlement data for creation
   * @returns Created entitlement
   */
  async createEntitlement(entitlementData: {
    licenseId: string;
    name: string;
    description?: string;
    included?: boolean;
    quantity?: number;
  }): Promise<LicenseEntitlement> {
    try {
      // Create a new entitlement entity
      const entitlement = new LicenseEntitlement({
        id: uuidv4(),
        licenseId: entitlementData.licenseId,
        name: entitlementData.name,
        description: entitlementData.description,
        included:
          entitlementData.included !== undefined
            ? entitlementData.included
            : true,
        quantity: entitlementData.quantity,
      });

      // Save the entitlement
      return await this.licenseRepository.createEntitlement(entitlement);
    } catch (error) {
      logger.error("Error creating license entitlement:", { error });
      throw new Error(
        `Failed to create license entitlement: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all entitlements for a license
   * @param licenseId License ID
   * @returns Array of entitlements
   */
  async getEntitlements(licenseId: string): Promise<LicenseEntitlement[]> {
    try {
      return await this.licenseRepository.getEntitlements(licenseId);
    } catch (error) {
      logger.error("Error getting license entitlements:", { error });
      throw new Error(
        `Failed to get license entitlements: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates a license entitlement
   * @param id Entitlement ID
   * @param entitlementData Entitlement data for update
   * @returns Updated entitlement
   */
  async updateEntitlement(
    id: string,
    entitlementData: {
      name?: string;
      description?: string;
      included?: boolean;
      quantity?: number;
    }
  ): Promise<LicenseEntitlement> {
    try {
      // First find the entitlement to get its licenseId
      const entitlement = await this.licenseRepository.findEntitlementById(id);
      if (!entitlement) {
        throw new Error(`Entitlement with ID ${id} not found`);
      }

      // Get all entitlements for the license
      const entitlements = await this.licenseRepository.getEntitlements(
        entitlement.licenseId
      );
      const existingEntitlement = entitlements.find((e) => e.id === id);

      if (!existingEntitlement) {
        throw new Error(`Entitlement with ID ${id} not found`);
      }

      // Update entitlement details
      existingEntitlement.updateDetails({
        name: entitlementData.name,
        description: entitlementData.description,
        included: entitlementData.included,
        quantity: entitlementData.quantity,
      });

      // Save changes
      return await this.licenseRepository.updateEntitlement(
        existingEntitlement
      );
    } catch (error) {
      logger.error("Error updating license entitlement:", { error });
      throw new Error(
        `Failed to update license entitlement: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes a license entitlement
   * @param id Entitlement ID
   * @returns true if deleted, false if not found
   */
  async deleteEntitlement(id: string): Promise<boolean> {
    try {
      return await this.licenseRepository.deleteEntitlement(id);
    } catch (error) {
      logger.error("Error deleting license entitlement:", { error });
      throw new Error(
        `Failed to delete license entitlement: ${(error as Error).message}`
      );
    }
  }

  // License Document methods

  /**
   * Creates a new license document
   * @param documentData Document data for creation
   * @returns Created document
   */
  async createDocument(documentData: {
    licenseId: string;
    name: string;
    documentType: LicenseDocumentType;
    documentFormat: DocumentFormat;
    documentUri: string;
    documentHash?: string;
    isEncrypted?: boolean;
  }): Promise<LicenseDocument> {
    try {
      // Calculate document hash if not provided
      let documentHash = documentData.documentHash;
      if (!documentHash) {
        // In a real implementation, we'd calculate the hash from the actual document
        // For demonstration, we'll use a simple hash method
        documentHash = this.encryptionService.createHash(
          documentData.name + Date.now()
        );
      }

      // Set up encryption if document is encrypted
      let encryptionKeyId = null;
      if (documentData.isEncrypted) {
        // In a real implementation, we would get or generate a key
        // For demonstration, we'll use a simple ID
        encryptionKeyId = "key-" + Date.now();
      }

      // Create a new document entity
      const document = new LicenseDocument({
        id: uuidv4(),
        licenseId: documentData.licenseId,
        name: documentData.name,
        documentType: documentData.documentType,
        documentFormat: documentData.documentFormat,
        documentUri: documentData.documentUri,
        documentHash,
        isEncrypted: documentData.isEncrypted || false,
        encryptionKeyId,
        uploadDate: new Date(),
      });

      // Save the document
      return await this.licenseRepository.createDocument(document);
    } catch (error) {
      logger.error("Error creating license document:", { error });
      throw new Error(
        `Failed to create license document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all documents for a license
   * @param licenseId License ID
   * @returns Array of documents
   */
  async getDocuments(licenseId: string): Promise<LicenseDocument[]> {
    try {
      return await this.licenseRepository.getDocuments(licenseId);
    } catch (error) {
      logger.error("Error getting license documents:", { error });
      throw new Error(
        `Failed to get license documents: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates a license document
   * @param id Document ID
   * @param documentData Document data for update
   * @returns Updated document
   */
  async updateDocument(
    id: string,
    documentData: {
      name?: string;
      documentType?: LicenseDocumentType;
      documentFormat?: DocumentFormat;
      isEncrypted?: boolean;
    }
  ): Promise<LicenseDocument> {
    try {
      // First find the document to get its licenseId
      const document = await this.licenseRepository.findDocumentById(id);
      if (!document) {
        throw new Error(`Document with ID ${id} not found`);
      }

      // Get all documents for the license
      const documents = await this.licenseRepository.getDocuments(
        document.licenseId
      );
      const existingDocument = documents.find((d) => d.id === id);

      if (!existingDocument) {
        throw new Error(`Document with ID ${id} not found`);
      }

      // Update document details
      existingDocument.updateDetails({
        name: documentData.name,
        documentType: documentData.documentType,
        documentFormat: documentData.documentFormat,
      });

      // Update encryption status if provided
      if (documentData.isEncrypted !== undefined) {
        let encryptionKeyId = null;
        if (documentData.isEncrypted) {
          // In a real implementation, we would get or generate a key
          // For demonstration, we'll use a simple ID
          encryptionKeyId = "key-" + Date.now();
        }

        existingDocument.updateEncryption({
          isEncrypted: documentData.isEncrypted,
          encryptionKeyId,
        });
      }

      // Save changes
      return await this.licenseRepository.updateDocument(existingDocument);
    } catch (error) {
      logger.error("Error updating license document:", { error });
      throw new Error(
        `Failed to update license document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes a license document
   * @param id Document ID
   * @returns true if deleted, false if not found
   */
  async deleteDocument(id: string): Promise<boolean> {
    try {
      return await this.licenseRepository.deleteDocument(id);
    } catch (error) {
      logger.error("Error deleting license document:", { error });
      throw new Error(
        `Failed to delete license document: ${(error as Error).message}`
      );
    }
  }

  // License Usage methods

  /**
   * Creates a new license usage record
   * @param usageData Usage data for creation
   * @returns Created usage record
   */
  async createUsageRecord(usageData: {
    licenseId: string;
    date: Date;
    usageCount: number;
    totalAvailable: number;
  }): Promise<LicenseUsage> {
    try {
      // Calculate utilization percentage
      const utilizationPercentage =
        usageData.totalAvailable > 0
          ? (usageData.usageCount / usageData.totalAvailable) * 100
          : 0;

      // Create a new usage entity
      const usage = new LicenseUsage({
        id: uuidv4(),
        licenseId: usageData.licenseId,
        date: usageData.date,
        usageCount: usageData.usageCount,
        totalAvailable: usageData.totalAvailable,
        utilizationPercentage,
      });

      // Save the usage record
      return await this.licenseRepository.createUsage(usage);
    } catch (error) {
      logger.error("Error creating license usage record:", { error });
      throw new Error(
        `Failed to create license usage record: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets usage history for a license
   * @param licenseId License ID
   * @param startDate Start date for usage history
   * @param endDate End date for usage history
   * @returns Array of usage records
   */
  async getUsageHistory(
    licenseId: string,
    startDate: Date,
    endDate: Date
  ): Promise<LicenseUsage[]> {
    try {
      return await this.licenseRepository.getUsageHistory(
        licenseId,
        startDate,
        endDate
      );
    } catch (error) {
      logger.error("Error getting license usage history:", { error });
      throw new Error(
        `Failed to get license usage history: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets the latest usage record for a license
   * @param licenseId License ID
   * @returns Latest usage record or null if none exists
   */
  async getLatestUsage(licenseId: string): Promise<LicenseUsage | null> {
    try {
      return await this.licenseRepository.getLatestUsage(licenseId);
    } catch (error) {
      logger.error("Error getting latest license usage:", { error });
      throw new Error(
        `Failed to get latest license usage: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates license allocation based on usage data
   * @param licenseId License ID
   * @returns Updated license
   */
  async updateLicenseAllocation(licenseId: string): Promise<License> {
    try {
      // Get the license
      const license = await this.licenseRepository.findById(licenseId);

      if (!license) {
        throw new Error(`License with ID ${licenseId} not found`);
      }

      // Get the latest usage record
      const latestUsage = await this.licenseRepository.getLatestUsage(
        licenseId
      );

      if (!latestUsage) {
        // No usage data available, return the license as is
        return license;
      }

      // Update license allocation based on usage data
      license.updateAllocation({
        assignedLicenses: latestUsage.usageCount,
      });

      // Save changes
      return await this.licenseRepository.update(license);
    } catch (error) {
      logger.error("Error updating license allocation:", { error });
      throw new Error(
        `Failed to update license allocation: ${(error as Error).message}`
      );
    }
  }
}
