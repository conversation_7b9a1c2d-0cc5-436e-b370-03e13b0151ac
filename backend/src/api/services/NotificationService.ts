/**
 * Notification Service
 * Implements business logic for notification management
 */

import {
  PrismaClient,
  NotificationType,
  NotificationPriority,
  NotificationChannel,
} from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Service for managing notifications
 */
export class NotificationService {
  private prisma: PrismaClient;
  private readonly NOTIFICATION_RETENTION_DAYS = 30;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;

    // Start automatic cleanup process
    this.startAutoCleanup();
  }

  /**
   * Creates a new notification
   * @param notificationData Notification data for creation
   * @returns Created notification
   */
  async createNotification(notificationData: {
    title: string;
    content: string;
    type: NotificationType;
    priority?: NotificationPriority;
    userId: string;
    tenantId: string;
    resourceId?: string;
    resourceType?: string;
    actionUrl?: string;
    metadata?: any;
  }): Promise<any> {
    try {
      // Set default values where needed
      const priority = notificationData.priority || NotificationPriority.MEDIUM;

      // Create the notification
      const notification = await this.prisma.notification.create({
        data: {
          id: uuidv4(),
          title: notificationData.title,
          content: notificationData.content,
          type: notificationData.type,
          priority,
          isRead: false,
          userId: notificationData.userId,
          tenantId: notificationData.tenantId,
          resourceId: notificationData.resourceId,
          resourceType: notificationData.resourceType,
          actionUrl: notificationData.actionUrl,
          metadata: notificationData.metadata
            ? JSON.stringify(notificationData.metadata)
            : undefined,
        },
      });

      // Get user notification preferences
      const preferences = await this.prisma.notificationPreference.findMany({
        where: {
          userId: notificationData.userId,
          tenantId: notificationData.tenantId,
          type: notificationData.type,
          enabled: true,
        },
      });

      // Create notification deliveries based on preferences
      const deliveryPromises = preferences.map((pref) =>
        this.prisma.notificationDelivery.create({
          data: {
            id: uuidv4(),
            notificationId: notification.id,
            channel: pref.channel,
            status: "PENDING",
          },
        })
      );

      // If no preferences found, default to IN_APP
      if (preferences.length === 0) {
        deliveryPromises.push(
          this.prisma.notificationDelivery.create({
            data: {
              id: uuidv4(),
              notificationId: notification.id,
              channel: NotificationChannel.IN_APP,
              status: "PENDING",
            },
          })
        );
      }

      // Wait for all deliveries to be created
      await Promise.all(deliveryPromises);

      // Return the created notification with deliveries
      return await this.prisma.notification.findUnique({
        where: { id: notification.id },
        include: { deliveries: true },
      });
    } catch (error) {
      logger.error("Error creating notification:", { error });
      throw new Error(
        `Failed to create notification: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets notifications for a user
   * @param userId User ID
   * @param page Page number (optional, default: 1)
   * @param limit Items per page (optional, default: 20)
   * @returns Array of notifications and total count
   */
  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ notifications: any[]; total: number }> {
    try {
      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get total count
      const total = await this.prisma.notification.count({
        where: { userId },
      });

      // Get notifications
      const notifications = await this.prisma.notification.findMany({
        where: { userId },
        include: { deliveries: true },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      });

      return { notifications, total };
    } catch (error) {
      logger.error("Error getting user notifications:", { error });
      throw new Error(
        `Failed to get user notifications: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets unread notifications for a user
   * @param userId User ID
   * @returns Array of unread notifications
   */
  async getUnreadNotifications(userId: string): Promise<any[]> {
    try {
      return await this.prisma.notification.findMany({
        where: {
          userId,
          isRead: false,
        },
        include: { deliveries: true },
        orderBy: { createdAt: "desc" },
      });
    } catch (error) {
      logger.error("Error getting unread notifications:", { error });
      throw new Error(
        `Failed to get unread notifications: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets live notifications for a user (optimized for real-time updates)
   * @param userId User ID
   * @param since Optional date to get notifications since
   * @param limit Maximum number of notifications to return
   * @returns Object with notifications and metadata
   */
  async getLiveNotifications(
    userId: string,
    since?: Date,
    limit: number = 10
  ): Promise<{
    notifications: any[];
    unreadCount: number;
    hasMore: boolean;
    lastUpdated: Date;
  }> {
    try {
      // Build where clause
      const whereClause: any = {
        userId,
      };

      if (since) {
        whereClause.createdAt = {
          gt: since,
        };
      }

      // Get notifications
      const notifications = await this.prisma.notification.findMany({
        where: whereClause,
        orderBy: {
          createdAt: "desc",
        },
        take: limit + 1, // Take one extra to check if there are more
        include: {
          deliveries: true,
        },
      });

      // Check if there are more notifications
      const hasMore = notifications.length > limit;
      const actualNotifications = hasMore ? notifications.slice(0, limit) : notifications;

      // Get unread count
      const unreadCount = await this.prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      });

      return {
        notifications: actualNotifications,
        unreadCount,
        hasMore,
        lastUpdated: new Date(),
      };
    } catch (error) {
      logger.error("Error getting live notifications:", { error });
      throw new Error("Failed to get live notifications");
    }
  }

  /**
   * Marks a notification as read
   * @param notificationId Notification ID
   * @returns Updated notification
   */
  async markAsRead(notificationId: string): Promise<any> {
    try {
      return await this.prisma.notification.update({
        where: { id: notificationId },
        data: {
          isRead: true,
          readAt: new Date(),
        },
        include: { deliveries: true },
      });
    } catch (error) {
      logger.error("Error marking notification as read:", { error });
      throw new Error(
        `Failed to mark notification as read: ${(error as Error).message}`
      );
    }
  }

  /**
   * Marks all notifications for a user as read
   * @param userId User ID
   * @returns Number of notifications marked as read
   */
  async markAllAsRead(userId: string): Promise<number> {
    try {
      const result = await this.prisma.notification.updateMany({
        where: {
          userId,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });

      return result.count;
    } catch (error) {
      logger.error("Error marking all notifications as read:", { error });
      throw new Error(
        `Failed to mark all notifications as read: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes a notification
   * @param notificationId Notification ID
   * @returns true if deleted, false if not found
   */
  async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      // Delete notification deliveries first
      await this.prisma.notificationDelivery.deleteMany({
        where: { notificationId },
      });

      // Then delete the notification
      const result = await this.prisma.notification.delete({
        where: { id: notificationId },
      });

      return !!result;
    } catch (error) {
      // If the notification doesn't exist, return false
      if ((error as any).code === "P2025") {
        return false;
      }

      logger.error("Error deleting notification:", { error });
      throw new Error(
        `Failed to delete notification: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets notification preferences for a user
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Array of notification preferences
   */
  async getUserPreferences(userId: string, tenantId: string): Promise<any[]> {
    try {
      return await this.prisma.notificationPreference.findMany({
        where: {
          userId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error getting user notification preferences:", { error });
      throw new Error(
        `Failed to get user notification preferences: ${(error as Error).message
        }`
      );
    }
  }

  /**
   * Updates notification preferences for a user
   * @param userId User ID
   * @param tenantId Tenant ID
   * @param preferences Array of preference updates
   * @returns Updated preferences
   */
  async updateUserPreferences(
    userId: string,
    tenantId: string,
    preferences: {
      type: NotificationType;
      channel: NotificationChannel;
      enabled: boolean;
    }[]
  ): Promise<any[]> {
    try {
      const updatePromises = preferences.map((pref) =>
        this.prisma.notificationPreference.upsert({
          where: {
            userId_type_channel_tenantId: {
              userId,
              type: pref.type,
              channel: pref.channel,
              tenantId,
            },
          },
          update: {
            enabled: pref.enabled,
          },
          create: {
            id: uuidv4(),
            userId,
            tenantId,
            type: pref.type,
            channel: pref.channel,
            enabled: pref.enabled,
          },
        })
      );

      await Promise.all(updatePromises);

      return await this.prisma.notificationPreference.findMany({
        where: {
          userId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error updating user notification preferences:", { error });
      throw new Error(
        `Failed to update user notification preferences: ${(error as Error).message
        }`
      );
    }
  }

  /**
   * Updates notification delivery status
   * @param deliveryId Delivery ID
   * @param status New status
   * @param errorMessage Error message (if status is FAILED)
   * @returns Updated delivery
   */
  async updateDeliveryStatus(
    deliveryId: string,
    status: string,
    errorMessage?: string
  ): Promise<any> {
    try {
      const updateData: any = { status };

      if (status === "SENT") {
        updateData.sentAt = new Date();
      } else if (status === "DELIVERED") {
        updateData.deliveredAt = new Date();
      } else if (status === "FAILED") {
        updateData.errorMessage = errorMessage || "Unknown error";
      }

      return await this.prisma.notificationDelivery.update({
        where: { id: deliveryId },
        data: updateData,
      });
    } catch (error) {
      logger.error("Error updating notification delivery status:", { error });
      throw new Error(
        `Failed to update notification delivery status: ${(error as Error).message
        }`
      );
    }
  }

  /**
   * Gets pending notification deliveries for a specific channel
   * @param channel Notification channel
   * @returns Array of pending deliveries
   */
  async getPendingDeliveries(channel: NotificationChannel): Promise<any[]> {
    try {
      return await this.prisma.notificationDelivery.findMany({
        where: {
          channel,
          status: "PENDING",
        },
        include: { notification: true },
      });
    } catch (error) {
      logger.error("Error getting pending notification deliveries:", { error });
      throw new Error(
        `Failed to get pending notification deliveries: ${(error as Error).message
        }`
      );
    }
  }

  /**
   * Creates a contract notification
   * @param data Contract notification data
   * @returns Created notification
   */
  async createContractNotification(data: {
    title: string;
    content: string;
    userId: string;
    tenantId: string;
    contractId: string;
    priority?: NotificationPriority;
    actionUrl?: string;
    metadata?: any;
  }): Promise<any> {
    return this.createNotification({
      title: data.title,
      content: data.content,
      type: NotificationType.CONTRACT,
      priority: data.priority,
      userId: data.userId,
      tenantId: data.tenantId,
      resourceId: data.contractId,
      resourceType: "Contract",
      actionUrl: data.actionUrl || `/repository`, // Default to repository page
      metadata: data.metadata,
    });
  }

  /**
   * Creates a license notification
   * @param data License notification data
   * @returns Created notification
   */
  async createLicenseNotification(data: {
    title: string;
    content: string;
    userId: string;
    tenantId: string;
    licenseId: string;
    priority?: NotificationPriority;
    metadata?: any;
  }): Promise<any> {
    return this.createNotification({
      title: data.title,
      content: data.content,
      type: NotificationType.LICENSE,
      priority: data.priority,
      userId: data.userId,
      tenantId: data.tenantId,
      resourceId: data.licenseId,
      resourceType: "License",
      metadata: data.metadata,
    });
  }

  /**
   * Creates a system notification
   * @param data System notification data
   * @returns Created notification
   */
  async createSystemNotification(data: {
    title: string;
    content: string;
    userId: string;
    tenantId: string;
    priority?: NotificationPriority;
    metadata?: any;
  }): Promise<any> {
    return this.createNotification({
      title: data.title,
      content: data.content,
      type: NotificationType.SYSTEM,
      priority: data.priority,
      userId: data.userId,
      tenantId: data.tenantId,
      metadata: data.metadata,
    });
  }

  /**
   * Creates a security notification
   * @param data Security notification data
   * @returns Created notification
   */
  async createSecurityNotification(data: {
    title: string;
    content: string;
    userId: string;
    tenantId: string;
    priority?: NotificationPriority;
    metadata?: any;
  }): Promise<any> {
    return this.createNotification({
      title: data.title,
      content: data.content,
      type: NotificationType.SECURITY,
      priority: data.priority || NotificationPriority.HIGH,
      userId: data.userId,
      tenantId: data.tenantId,
      metadata: data.metadata,
    });
  }

  /**
   * Starts the automatic cleanup process for old notifications
   */
  private startAutoCleanup(): void {
    // Run cleanup every 24 hours
    setInterval(() => {
      this.cleanupOldNotifications().catch((error) => {
        logger.error("Error during automatic notification cleanup:", error);
      });
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Run initial cleanup after 1 minute
    setTimeout(() => {
      this.cleanupOldNotifications().catch((error) => {
        logger.error("Error during initial notification cleanup:", error);
      });
    }, 60 * 1000); // 1 minute
  }

  /**
   * Cleans up notifications older than the retention period
   * @returns Number of notifications deleted
   */
  async cleanupOldNotifications(): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.NOTIFICATION_RETENTION_DAYS);

      // Delete old notifications and their deliveries (cascade delete should handle deliveries)
      const result = await this.prisma.notification.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      if (result.count > 0) {
        logger.info(`Cleaned up ${result.count} old notifications older than ${this.NOTIFICATION_RETENTION_DAYS} days`);
      }

      return result.count;
    } catch (error) {
      logger.error("Error cleaning up old notifications:", error);
      throw new Error("Failed to cleanup old notifications");
    }
  }

  /**
   * Creates default notification preferences for a user
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Created preferences
   */
  async createDefaultPreferences(userId: string, tenantId: string): Promise<any[]> {
    try {
      const defaultPreferences = [
        // Contract notifications - enabled for in-app
        {
          type: NotificationType.CONTRACT,
          channel: NotificationChannel.IN_APP,
          enabled: true,
        },
        // License notifications - enabled for in-app
        {
          type: NotificationType.LICENSE,
          channel: NotificationChannel.IN_APP,
          enabled: true,
        },
        // System notifications - enabled for in-app
        {
          type: NotificationType.SYSTEM,
          channel: NotificationChannel.IN_APP,
          enabled: true,
        },
        // Security notifications - enabled for in-app and email
        {
          type: NotificationType.SECURITY,
          channel: NotificationChannel.IN_APP,
          enabled: true,
        },
        {
          type: NotificationType.SECURITY,
          channel: NotificationChannel.EMAIL,
          enabled: true,
        },
        // User notifications - enabled for in-app
        {
          type: NotificationType.USER,
          channel: NotificationChannel.IN_APP,
          enabled: true,
        },
      ];

      const createPromises = defaultPreferences.map((pref) =>
        this.prisma.notificationPreference.upsert({
          where: {
            userId_type_channel_tenantId: {
              userId,
              type: pref.type,
              channel: pref.channel,
              tenantId,
            },
          },
          update: {
            enabled: pref.enabled,
          },
          create: {
            id: uuidv4(),
            userId,
            tenantId,
            type: pref.type,
            channel: pref.channel,
            enabled: pref.enabled,
          },
        })
      );

      const preferences = await Promise.all(createPromises);
      logger.info(`Created default notification preferences for user ${userId}`);

      return preferences;
    } catch (error) {
      logger.error("Error creating default notification preferences:", error);
      throw new Error("Failed to create default notification preferences");
    }
  }

  /**
   * Gets notification statistics for a user
   * @param userId User ID
   * @returns Notification statistics
   */
  async getNotificationStats(userId: string): Promise<{
    total: number;
    unread: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    try {
      const [total, unread, byType, byPriority] = await Promise.all([
        // Total notifications
        this.prisma.notification.count({
          where: { userId },
        }),
        // Unread notifications
        this.prisma.notification.count({
          where: { userId, isRead: false },
        }),
        // By type
        this.prisma.notification.groupBy({
          by: ['type'],
          where: { userId },
          _count: { type: true },
        }),
        // By priority
        this.prisma.notification.groupBy({
          by: ['priority'],
          where: { userId },
          _count: { priority: true },
        }),
      ]);

      const typeStats: Record<string, number> = {};
      byType.forEach((item) => {
        typeStats[item.type] = item._count.type;
      });

      const priorityStats: Record<string, number> = {};
      byPriority.forEach((item) => {
        priorityStats[item.priority] = item._count.priority;
      });

      return {
        total,
        unread,
        byType: typeStats,
        byPriority: priorityStats,
      };
    } catch (error) {
      logger.error("Error getting notification statistics:", error);
      throw new Error("Failed to get notification statistics");
    }
  }
}
