/**
 * Contract Reporting Service
 * Manages reporting relationships between contracts
 */

import { ContractExtraction } from "../../domain/contracts/ContractExtraction";
import { ContractHierarchyService } from "./ContractHierarchyService";
import { logger } from "../../infrastructure/logging/logger";

export interface ReportingRelationship {
  childContractId: string;
  parentContractId: string;
  relationshipType: "AUTO" | "MANUAL";
  createdAt: Date;
  updatedAt: Date;
}

export interface ReportingValidationResult {
  isValid: boolean;
  reason?: string;
  suggestedParent?: string;
  isHierarchyWarning?: boolean; // Indicates if this is just a hierarchy warning
}

export class ContractReportingService {
  constructor(private contractHierarchyService: ContractHierarchyService) {}

  /**
   * Auto-determines reporting relationship based on agreement type hierarchy
   */
  async determineAutoReporting(
    contract: ContractExtraction,
    allContracts: ContractExtraction[]
  ): Promise<string | null> {
    try {
      const contractAgreementType = contract.fixedFields.agreement_type?.value;
      if (!contractAgreementType) {
        logger.warn(
          `No agreement type found for contract ${contract.contractId}`
        );
        return null;
      }

      // Get hierarchy info for this agreement type
      const hierarchyInfo = this.contractHierarchyService.getHierarchyInfo(
        contractAgreementType
      );
      if (!hierarchyInfo || hierarchyInfo.level === 1) {
        // Top-level contracts (like MSA) don't report to anyone
        return null;
      }

      // Find potential parent contracts based on hierarchy rules
      const potentialParents = allContracts.filter((parentContract) => {
        const parentAgreementType =
          parentContract.fixedFields.agreement_type?.value;
        if (!parentAgreementType) return false;

        // Check if parent type can be a parent of child type
        return this.contractHierarchyService.canBeParent(
          parentAgreementType,
          contractAgreementType
        );
      });

      if (potentialParents.length === 0) {
        logger.info(
          `No potential parents found for contract ${contract.contractId} of type ${contractAgreementType}`
        );
        return null;
      }

      // Apply additional matching criteria to find the best parent
      const bestParent = this.findBestParentMatch(contract, potentialParents);
      return bestParent?.contractId || null;
    } catch (error) {
      logger.error("Error determining auto reporting relationship:", error);
      return null;
    }
  }

  /**
   * Finds the best parent match using multiple criteria
   */
  private findBestParentMatch(
    contract: ContractExtraction,
    potentialParents: ContractExtraction[]
  ): ContractExtraction | null {
    let bestMatch: ContractExtraction | null = null;
    let bestScore = 0;

    for (const parent of potentialParents) {
      let score = 0;

      // 1. Same provider/supplier (highest priority)
      if (
        contract.fixedFields.provider?.value ===
        parent.fixedFields.provider?.value
      ) {
        score += 50;
      }

      // 2. Contract ID similarity (e.g., MSA-001 and SOW-001-A)
      const contractId = contract.fixedFields.contract_id?.value || "";
      const parentId = parent.fixedFields.contract_id?.value || "";
      if (
        contractId &&
        parentId &&
        this.areContractIdsRelated(contractId, parentId)
      ) {
        score += 30;
      }

      // 3. Date proximity (contracts created around the same time)
      const contractDate = contract.fixedFields.start_date?.value;
      const parentDate = parent.fixedFields.start_date?.value;
      if (contractDate && parentDate) {
        const daysDiff = Math.abs(
          (new Date(contractDate).getTime() - new Date(parentDate).getTime()) /
            (1000 * 60 * 60 * 24)
        );
        if (daysDiff <= 90) {
          // Within 3 months
          score += Math.max(0, 20 - (daysDiff / 90) * 20);
        }
      }

      // 4. Referenced documents
      const relationships = contract.fixedFields.relationships?.value;
      if (relationships && typeof relationships === "string") {
        const referencedDocs = relationships
          .split(",")
          .map((ref) => ref.trim());
        if (referencedDocs.some((ref) => ref.includes(parentId))) {
          score += 25;
        }
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = parent;
      }
    }

    // Only return a match if the score is above a threshold
    return bestScore >= 30 ? bestMatch : null;
  }

  /**
   * Checks if two contract IDs are related (e.g., MSA-001 and SOW-001-A)
   */
  private areContractIdsRelated(
    contractId1: string,
    contractId2: string
  ): boolean {
    // Remove common prefixes and suffixes to find base numbers
    const extractBaseNumber = (id: string): string => {
      const match = id.match(/(\d+)/);
      return match ? match[1] : "";
    };

    const base1 = extractBaseNumber(contractId1);
    const base2 = extractBaseNumber(contractId2);

    return Boolean(base1 && base2 && base1 === base2);
  }

  /**
   * Validates if a reporting relationship is valid
   */
  async validateReportingRelationship(
    childContractId: string,
    parentContractId: string,
    allContracts: ContractExtraction[],
    allowManualOverride: boolean = false
  ): Promise<ReportingValidationResult> {
    try {
      // Find the contracts
      const childContract = allContracts.find(
        (c) => c.contractId === childContractId
      );
      const parentContract = allContracts.find(
        (c) => c.contractId === parentContractId
      );

      if (!childContract || !parentContract) {
        return {
          isValid: false,
          reason: "One or both contracts not found",
        };
      }

      // Check if they're the same contract
      if (childContractId === parentContractId) {
        return {
          isValid: false,
          reason: "A contract cannot report to itself",
        };
      }

      // Check hierarchy rules
      const childType = childContract.fixedFields.agreement_type?.value;
      const parentType = parentContract.fixedFields.agreement_type?.value;

      if (!childType || !parentType) {
        return {
          isValid: false,
          reason: "Agreement type missing for one or both contracts",
        };
      }

      // Check for circular dependencies (always block this)
      if (
        await this.wouldCreateCircularDependency(
          childContractId,
          parentContractId,
          allContracts
        )
      ) {
        return {
          isValid: false,
          reason: "This relationship would create a circular dependency",
        };
      }

      // Check hierarchy rules
      if (!this.contractHierarchyService.canBeParent(parentType, childType)) {
        if (allowManualOverride) {
          // Return as warning but allow the relationship
          return {
            isValid: true,
            reason: `${parentType} cannot be a parent of ${childType} according to hierarchy rules`,
            isHierarchyWarning: true,
          };
        } else {
          return {
            isValid: false,
            reason: `${parentType} cannot be a parent of ${childType} according to hierarchy rules`,
            isHierarchyWarning: true,
          };
        }
      }

      return { isValid: true };
    } catch (error) {
      logger.error("Error validating reporting relationship:", error);
      return {
        isValid: false,
        reason: "Validation error occurred",
      };
    }
  }

  /**
   * Checks if setting a reporting relationship would create a circular dependency
   */
  private async wouldCreateCircularDependency(
    childContractId: string,
    parentContractId: string,
    allContracts: ContractExtraction[]
  ): Promise<boolean> {
    const visited = new Set<string>();

    const checkCircular = (currentId: string): boolean => {
      if (visited.has(currentId)) {
        return true; // Found a cycle
      }

      if (currentId === childContractId) {
        return true; // Would create a cycle
      }

      visited.add(currentId);

      // Find what this contract reports to
      const contract = allContracts.find((c) => c.contractId === currentId);
      if (contract?.reportingTo) {
        return checkCircular(contract.reportingTo);
      }

      return false;
    };

    return checkCircular(parentContractId);
  }

  /**
   * Gets all contracts that report to a specific contract
   */
  getChildContracts(
    parentContractId: string,
    allContracts: ContractExtraction[]
  ): ContractExtraction[] {
    return allContracts.filter(
      (contract) => contract.reportingTo === parentContractId
    );
  }

  /**
   * Gets the reporting chain for a contract (all ancestors)
   */
  getReportingChain(
    contractId: string,
    allContracts: ContractExtraction[]
  ): ContractExtraction[] {
    const chain: ContractExtraction[] = [];
    let currentId = contractId;

    while (currentId) {
      const contract = allContracts.find((c) => c.contractId === currentId);
      if (
        !contract ||
        chain.some((c) => c.contractId === contract.contractId)
      ) {
        break; // Prevent infinite loops
      }

      chain.push(contract);
      currentId = contract.reportingTo || "";
    }

    return chain.slice(1); // Remove the original contract, return only ancestors
  }
}
