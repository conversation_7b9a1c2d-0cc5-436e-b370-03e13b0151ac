/**
 * Supplier Mapping Service
 * Manages supplier-specific field mappings for contract extraction
 */

import * as fs from "fs";
import * as path from "path";
import { logger } from "../../infrastructure/logging/logger";

export interface CategoryMapping {
  [categoryName: string]: {
    [standardFieldName: string]: string;
  };
}

export interface SupplierMapping {
  [supplierName: string]: CategoryMapping;
}

export class SupplierMappingService {
  private mappingData: any = null;
  private readonly mappingFilePath: string;

  constructor() {
    this.mappingFilePath = path.join(__dirname, "../../data/mapping.json");
  }

  /**
   * Loads the mapping data from the JSON file
   */
  private async loadMappingData(): Promise<void> {
    if (this.mappingData) {
      return; // Already loaded
    }

    try {
      const fileContent = fs.readFileSync(this.mappingFilePath, "utf-8");
      this.mappingData = JSON.parse(fileContent);
      logger.info("Successfully loaded supplier mapping data");
    } catch (error) {
      logger.error("Failed to load supplier mapping data:", error);
      throw new Error("Failed to load supplier mapping configuration");
    }
  }

  /**
   * Normalizes supplier name to match mapping keys
   * @param supplierName - Raw supplier name from contract
   * @returns Normalized supplier name or "general" as fallback
   */
  public normalizeSupplierName(supplierName: string): string | null {
    if (!supplierName) {
      return null;
    }

    const normalized = supplierName.toLowerCase().trim();

    // Check for Oracle variations
    if (normalized.includes("oracle")) {
      return "oracle";
    }

    // Check for Microsoft variations
    if (normalized.includes("microsoft") || normalized.includes("msft")) {
      return "microsoft";
    }

    // Check for SAP variations
    if (normalized.includes("sap")) {
      return "sap";
    }

    // Check for Red Hat variations
    if (normalized.includes("red hat") || normalized.includes("redhat")) {
      return "red-hat";
    }

    // Check for Salesforce variations
    if (normalized.includes("salesforce")) {
      return "salesforce";
    }

    // Check for ServiceNow variations
    if (normalized.includes("servicenow")) {
      return "servicenow";
    }

    // Fallback to general mapping for unknown suppliers
    return "general";
  }

  /**
   * Gets the list of standardized field names for a specific supplier across all categories
   * @param supplierName - Normalized supplier name
   * @returns Array of standardized field names
   */
  public async getSupplierFields(supplierName: string): Promise<string[]> {
    await this.loadMappingData();

    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    if (!normalizedSupplier) {
      logger.warn(`No mapping found for supplier: ${supplierName}`);
      return [];
    }

    const supplierMapping = this.mappingData[normalizedSupplier];
    if (!supplierMapping || typeof supplierMapping !== "object") {
      logger.warn(`Invalid field mapping for supplier: ${normalizedSupplier}`);
      return [];
    }

    // Extract field names from all categories
    const allFields: string[] = [];
    for (const category in supplierMapping) {
      if (
        supplierMapping[category] &&
        typeof supplierMapping[category] === "object"
      ) {
        allFields.push(...Object.keys(supplierMapping[category]));
      }
    }

    return allFields;
  }

  /**
   * Gets the detailed mapping (standardized field name to supplier-specific terminology)
   * Flattens the categorical structure into a single mapping object
   * @param supplierName - Normalized supplier name
   * @returns Object mapping standardized field names to supplier-specific terms
   */
  public async getDetailedMapping(
    supplierName: string
  ): Promise<Record<string, string>> {
    await this.loadMappingData();

    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    if (!normalizedSupplier) {
      return {};
    }

    const supplierMapping = this.mappingData[normalizedSupplier];

    if (!supplierMapping || typeof supplierMapping !== "object") {
      logger.warn(
        `No detailed mapping found for supplier: ${normalizedSupplier}`
      );
      return {};
    }

    // Flatten the categorical structure into a single mapping
    const flatMapping: Record<string, string> = {};
    for (const category in supplierMapping) {
      if (
        supplierMapping[category] &&
        typeof supplierMapping[category] === "object"
      ) {
        Object.assign(flatMapping, supplierMapping[category]);
      }
    }

    return flatMapping;
  }

  /**
   * Gets the categorical mapping for a supplier
   * @param supplierName - Normalized supplier name
   * @returns Object with categories containing field mappings
   */
  public async getCategoricalMapping(
    supplierName: string
  ): Promise<CategoryMapping> {
    await this.loadMappingData();

    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    if (!normalizedSupplier) {
      return {};
    }

    const supplierMapping = this.mappingData[normalizedSupplier];

    if (!supplierMapping || typeof supplierMapping !== "object") {
      logger.warn(
        `No categorical mapping found for supplier: ${normalizedSupplier}`
      );
      return {};
    }

    return supplierMapping;
  }

  /**
   * Gets all supported suppliers
   * @returns Array of supported supplier names
   */
  public async getSupportedSuppliers(): Promise<string[]> {
    await this.loadMappingData();

    const suppliers: string[] = [];
    for (const key in this.mappingData) {
      if (
        this.mappingData[key] &&
        typeof this.mappingData[key] === "object" &&
        !Array.isArray(this.mappingData[key])
      ) {
        suppliers.push(key);
      }
    }

    return suppliers;
  }

  /**
   * Checks if a supplier is supported
   * @param supplierName - Supplier name to check
   * @returns True if supplier is supported (always true now with general fallback)
   */
  public async isSupplierSupported(supplierName: string): Promise<boolean> {
    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    if (!normalizedSupplier) {
      return false;
    }

    // Since we now fallback to "general" for unknown suppliers, all suppliers are supported
    return true;
  }

  /**
   * Generates a supplier-specific AI prompt for field extraction with categorical output
   * @param supplierName - Supplier name
   * @param supplierFields - Array of standardized field names
   * @param detailedMapping - Mapping of standardized names to supplier-specific terms
   * @returns AI prompt string
   */
  public generateSupplierSpecificPrompt(
    supplierName: string,
    supplierFields: string[],
    detailedMapping: Record<string, string>
  ): string {
    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    const supplierDisplayName = normalizedSupplier
      ? normalizedSupplier.charAt(0).toUpperCase() + normalizedSupplier.slice(1)
      : supplierName;

    let prompt = `You are an expert contract analyst specializing in ${supplierDisplayName} contracts. Extract the following supplier-specific fields from this ${supplierDisplayName} contract document.

**CRITICAL INSTRUCTIONS FOR RELEVANT EXTRACTION:**

1. **EXTRACT ACTUAL INFORMATION WITH REFERENCES:** Do NOT provide ONLY basic references like "Article 5 covers confidentiality" or "Yes, an annex is provided on pages 30-32". Instead, extract the ACTUAL relevant information, key terms, and specific details from those sections, and include the source reference at the end (e.g., "specific requirements here (Article 5)").

2. **CONCISE BUT COMPREHENSIVE EXTRACTION:** When you find relevant information:
   - Extract the key terms, conditions, and specific requirements (not full clause text)
   - Include specific numbers, percentages, timeframes, and thresholds
   - Provide the essential information from annexes, schedules, and appendices
   - Extract key definitions and important details, not just references to where they are located
   - Include specific obligations, rights, restrictions, and procedures in concise form

3. **RELEVANT VALUE EXTRACTION:** For each field:
   - Extract the key terms and important conditions (summarized, not full text)
   - Include specific requirements and important details
   - Provide actual numbers, percentages, timeframes, and thresholds
   - Extract essential definitions and key explanations
   - Include important procedures and requirements in concise form

4. **SUPPLIER-SPECIFIC TERMINOLOGY:** Understand that ${supplierDisplayName} may use different terminology in their documents
5. **STANDARDIZED OUTPUT:** Return results using the standardized field names provided below, organized by categories
6. **CONFIDENCE SCORING:** Use confidence scores: 0.9-1.0 (explicit), 0.7-0.9 (clear), 0.5-0.7 (moderate), 0.3-0.5 (unclear), 0.1-0.3 (missing/uncertain)
7. **MISSING FIELDS:** If a field is not found or not applicable, use "N/A" with confidence 0.1
8. **FINANCIAL AMOUNTS:** For any financial/monetary values, ALWAYS use the format "CURRENCY:AMOUNT" (e.g., "USD:50000", "EUR:25000.50"). Extract both currency and amount together.

**STANDARDIZED FIELDS TO EXTRACT (organized by categories):**
`;

    // Define the categories in the order we want them
    const categories = [
      "Use rights & restrictions",
      "General",
      "Legal terms",
      "Commercial terms",
      "Data protection",
      "Others",
    ];

    categories.forEach((category) => {
      prompt += `\n**${category.toUpperCase()}:**\n`;

      // Show a sample of fields for this category
      supplierFields.slice(0, 8).forEach((field) => {
        const supplierTerm = detailedMapping[field];
        prompt += `- **${field}**`;
        if (supplierTerm && supplierTerm !== field) {
          prompt += ` (${supplierDisplayName} typically calls this: "${supplierTerm}")`;
        }
        prompt += "\n";
      });
    });

    prompt += `
**OUTPUT FORMAT:**
Return a valid JSON object with this exact categorical structure:

{
  "special_fields": {
    "Use rights & restrictions": {
`;

    // Add sample fields for each category
    const sampleFieldsPerCategory = Math.ceil(supplierFields.length / 6);
    let fieldIndex = 0;

    categories.forEach((category, categoryIndex) => {
      if (categoryIndex > 0) {
        prompt += `    },
    "${category}": {
`;
      }

      const categoryFields = supplierFields.slice(
        fieldIndex,
        fieldIndex + sampleFieldsPerCategory
      );
      categoryFields.forEach((field, index) => {
        const fieldKey = field.toLowerCase().replace(/[^a-z0-9]/g, "_");
        prompt += `      "${fieldKey}": {"value": "extracted value or N/A", "confidence": 0.85}`;
        if (index < categoryFields.length - 1) {
          prompt += ",";
        }
        prompt += "\n";
      });

      fieldIndex += sampleFieldsPerCategory;
    });

    prompt += `    }
  }
}

**CRITICAL EXTRACTION REQUIREMENTS:**
- Extract ACTUAL RELEVANT INFORMATION, not basic references or article numbers
- Include key terms, conditions, requirements, and important details (concise, not full clause text)
- Provide essential definitions, obligations, restrictions, and specifications from the document
- Extract actual numbers, percentages, timeframes, thresholds, and specific requirements
- Include important information from annexes, schedules, appendices, and referenced sections
- Your response MUST be a valid JSON object matching the exact categorical structure shown above
- Organize extracted fields under the appropriate categories: "Use rights & restrictions", "General", "Legal terms", "Commercial terms", "Data protection", "Others"
- Do not include explanatory text outside the JSON
- Each field should have both "value" (with relevant actual information) and "confidence" properties

**EXAMPLES OF GOOD vs BAD EXTRACTION:**
❌ BAD: "Article 5 covers confidentiality in detail"
✅ GOOD: "AES-256 encryption required, access limited to authorized personnel, no third-party disclosure without written consent, 5-year post-termination obligation (Article 5)"

❌ BAD: "Yes, an annex of definitions is provided on pages 30-32"
✅ GOOD: "Includes technical data, trade secrets, research, product plans, customer lists, software, developments, marketing and financial information (Annex A, pages 30-32)"`;

    return prompt;
  }

  /**
   * Generates a supplier-specific AI prompt using the actual categorical mapping
   * @param supplierName - Supplier name
   * @returns AI prompt string with proper categorical organization
   */
  public async generateCategoricalPrompt(
    supplierName: string
  ): Promise<string> {
    const normalizedSupplier = this.normalizeSupplierName(supplierName);
    const supplierDisplayName = normalizedSupplier
      ? normalizedSupplier.charAt(0).toUpperCase() + normalizedSupplier.slice(1)
      : supplierName;

    const categoricalMapping = await this.getCategoricalMapping(supplierName);

    if (!categoricalMapping || Object.keys(categoricalMapping).length === 0) {
      // Fallback to the original method
      const supplierFields = await this.getSupplierFields(supplierName);
      const detailedMapping = await this.getDetailedMapping(supplierName);
      return this.generateSupplierSpecificPrompt(
        supplierName,
        supplierFields,
        detailedMapping
      );
    }

    let prompt = `You are an expert contract analyst specializing in ${supplierDisplayName} contracts. Extract the following supplier-specific fields from this ${supplierDisplayName} contract document.

**CRITICAL INSTRUCTIONS FOR RELEVANT EXTRACTION:**

1. **EXTRACT ACTUAL INFORMATION WITH REFERENCES:** Do NOT provide ONLY basic references like "Article 5 covers confidentiality" or "Yes, an annex is provided on pages 30-32". Instead, extract the ACTUAL relevant information, key terms, and specific details from those sections, and include the source reference at the end (e.g., "specific requirements here (Article 5)").

2. **CONCISE BUT COMPREHENSIVE EXTRACTION:** When you find relevant information:
   - Extract the key terms, conditions, and specific requirements (not full clause text)
   - Include specific numbers, percentages, timeframes, and thresholds
   - Provide the essential information from annexes, schedules, and appendices
   - Extract key definitions and important details, not just references to where they are located
   - Include specific obligations, rights, restrictions, and procedures in concise form

3. **RELEVANT VALUE EXTRACTION:** For each field:
   - Extract the key terms and important conditions (summarized, not full text)
   - Include specific requirements and important details
   - Provide actual numbers, percentages, timeframes, and thresholds
   - Extract essential definitions and key explanations
   - Include important procedures and requirements in concise form

4. **AVOID BASIC REFERENCES ONLY:** Never respond with ONLY basic references like:
   - "Article X covers this topic" (without the actual information)
   - "Section Y provides details" (without the actual details)
   - "An annex is provided on pages Z" (without the actual content)
   - "Covered in detail in the agreement" (without the actual details)
   - Basic yes/no answers without supporting details

5. **PROVIDE RELEVANT DETAILS WITH REFERENCES:** Always include:
   - Key information and important conditions from relevant clauses
   - Specific requirements, obligations, and restrictions
   - Important procedures and processes
   - Essential definitions and explanations
   - Actual terms, conditions, and specifications
   - Include source references (e.g., "Article 5", "Section 3.2") AFTER the actual information

6. **SUPPLIER-SPECIFIC TERMINOLOGY:** Understand that ${supplierDisplayName} may use different terminology in their documents
7. **STANDARDIZED OUTPUT:** Return results using the standardized field names provided below, organized by categories
8. **CONFIDENCE SCORING:** Use confidence scores: 0.9-1.0 (explicit), 0.7-0.9 (clear), 0.5-0.7 (moderate), 0.3-0.5 (unclear), 0.1-0.3 (missing/uncertain)
9. **MISSING FIELDS:** If a field is not found or not applicable, use "N/A" with confidence 0.1
10. **FINANCIAL AMOUNTS:** For any financial/monetary values, ALWAYS use the format "CURRENCY:AMOUNT" (e.g., "USD:50000", "EUR:25000.50"). Extract both currency and amount together.

**STANDARDIZED FIELDS TO EXTRACT (organized by categories):**
`;

    // Use the actual categorical mapping
    for (const [category, fields] of Object.entries(categoricalMapping)) {
      prompt += `\n**${category.toUpperCase()}:**\n`;

      for (const [fieldName, supplierTerm] of Object.entries(fields)) {
        prompt += `- **${fieldName}**`;
        if (
          supplierTerm &&
          supplierTerm !== fieldName &&
          supplierTerm !== "—" &&
          supplierTerm !== ""
        ) {
          prompt += ` (${supplierDisplayName} typically calls this: "${supplierTerm}")`;
        }
        prompt += "\n";
      }
    }

    prompt += `
**OUTPUT FORMAT:**
Return a valid JSON object with this exact categorical structure:

{
  "special_fields": {
`;

    // Generate the output format based on actual categories
    const categoryNames = Object.keys(categoricalMapping);
    categoryNames.forEach((category, categoryIndex) => {
      if (categoryIndex > 0) {
        prompt += `    },
`;
      }
      prompt += `    "${category}": {
`;

      const fields = Object.keys(categoricalMapping[category]);
      fields.forEach((field, fieldIndex) => {
        // Use the original field name as the key, not a transformed version
        prompt += `      "${field}": {"value": "extracted value or N/A", "confidence": 0.85}`;
        if (fieldIndex < fields.length - 1) {
          prompt += ",";
        }
        prompt += "\n";
      });
    });

    prompt += `    }
  }
}

**CRITICAL EXTRACTION REQUIREMENTS:**
- Extract ACTUAL RELEVANT INFORMATION, not basic references or article numbers
- Include key terms, conditions, requirements, and important details (concise, not full clause text)
- Provide essential definitions, obligations, restrictions, and specifications from the document
- Extract actual numbers, percentages, timeframes, thresholds, and specific requirements
- Include important information from annexes, schedules, appendices, and referenced sections
- Your response MUST be a valid JSON object matching the exact categorical structure shown above
- Organize extracted fields under the appropriate categories as shown
- Do not include explanatory text outside the JSON
- Each field should have both "value" (with relevant actual information) and "confidence" properties

**EXAMPLES OF GOOD vs BAD EXTRACTION:**
❌ BAD: "Article 5 covers confidentiality in detail"
✅ GOOD: "AES-256 encryption required, access limited to authorized personnel, no third-party disclosure without written consent, 5-year post-termination obligation (Article 5)"

❌ BAD: "Yes, an annex of definitions is provided on pages 30-32"
✅ GOOD: "Includes technical data, trade secrets, research, product plans, customer lists, software, developments, marketing and financial information (Annex A, pages 30-32)"

**FINANCIAL FIELD EXAMPLES:**
❌ BAD: "€25,000.50 setup fee"
✅ GOOD: "EUR:25000.50" (for setup costs)`;

    return prompt;
  }
}
