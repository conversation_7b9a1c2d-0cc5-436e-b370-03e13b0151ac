/**
 * Tenant Settings Service
 * Implements business logic for tenant settings and white-labeling
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../infrastructure/logging/logger';

/**
 * Service for managing tenant settings and white-labeling
 */
export class TenantSettingsService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Gets tenant settings
   * @param tenantId Tenant ID
   * @returns Tenant settings or null if not found
   */
  async getTenantSettings(tenantId: string): Promise<any | null> {
    try {
      return await this.prisma.tenantSettings.findUnique({
        where: { tenantId }
      });
    } catch (error) {
      logger.error('Error getting tenant settings:', { error });
      throw new Error(`Failed to get tenant settings: ${(error as Error).message}`);
    }
  }

  /**
   * Creates or updates tenant settings
   * @param tenantId Tenant ID
   * @param settings Settings data
   * @returns Updated tenant settings
   */
  async updateTenantSettings(tenantId: string, settings: {
    branding?: any;
    preferences?: any;
    whiteLabeling?: any;
    customDomain?: string;
    domainVerified?: boolean;
    emailConfiguration?: any;
    emailTemplates?: any;
    themeSettings?: any;
    loginPageSettings?: any;
  }): Promise<any> {
    try {
      // Check if tenant exists
      const tenant = await this.prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        throw new Error(`Tenant with ID ${tenantId} not found`);
      }

      // Prepare data for upsert
      const data: any = {};

      if (settings.branding !== undefined) {
        data.branding = settings.branding;
      }

      if (settings.preferences !== undefined) {
        data.preferences = settings.preferences;
      }

      if (settings.whiteLabeling !== undefined) {
        data.whiteLabeling = settings.whiteLabeling;
      }

      if (settings.customDomain !== undefined) {
        data.customDomain = settings.customDomain;
      }

      if (settings.domainVerified !== undefined) {
        data.domainVerified = settings.domainVerified;
      }

      if (settings.emailConfiguration !== undefined) {
        data.emailConfiguration = settings.emailConfiguration;
      }

      if (settings.emailTemplates !== undefined) {
        data.emailTemplates = settings.emailTemplates;
      }

      if (settings.themeSettings !== undefined) {
        data.themeSettings = settings.themeSettings;
      }

      if (settings.loginPageSettings !== undefined) {
        data.loginPageSettings = settings.loginPageSettings;
      }

      // Update or create tenant settings
      return await this.prisma.tenantSettings.upsert({
        where: { tenantId },
        update: data,
        create: {
          id: uuidv4(),
          tenantId,
          ...data
        }
      });
    } catch (error) {
      logger.error('Error updating tenant settings:', { error });
      throw new Error(`Failed to update tenant settings: ${(error as Error).message}`);
    }
  }

  /**
   * Updates branding settings
   * @param tenantId Tenant ID
   * @param branding Branding data
   * @returns Updated tenant settings
   */
  async updateBranding(tenantId: string, branding: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { branding });
    } catch (error) {
      logger.error('Error updating branding settings:', { error });
      throw new Error(`Failed to update branding settings: ${(error as Error).message}`);
    }
  }

  /**
   * Updates white-labeling settings
   * @param tenantId Tenant ID
   * @param whiteLabeling White-labeling data
   * @returns Updated tenant settings
   */
  async updateWhiteLabeling(tenantId: string, whiteLabeling: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { whiteLabeling });
    } catch (error) {
      logger.error('Error updating white-labeling settings:', { error });
      throw new Error(`Failed to update white-labeling settings: ${(error as Error).message}`);
    }
  }

  /**
   * Updates theme settings
   * @param tenantId Tenant ID
   * @param themeSettings Theme settings data
   * @returns Updated tenant settings
   */
  async updateThemeSettings(tenantId: string, themeSettings: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { themeSettings });
    } catch (error) {
      logger.error('Error updating theme settings:', { error });
      throw new Error(`Failed to update theme settings: ${(error as Error).message}`);
    }
  }

  /**
   * Updates login page settings
   * @param tenantId Tenant ID
   * @param loginPageSettings Login page settings data
   * @returns Updated tenant settings
   */
  async updateLoginPageSettings(tenantId: string, loginPageSettings: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { loginPageSettings });
    } catch (error) {
      logger.error('Error updating login page settings:', { error });
      throw new Error(`Failed to update login page settings: ${(error as Error).message}`);
    }
  }

  /**
   * Updates email configuration
   * @param tenantId Tenant ID
   * @param emailConfiguration Email configuration data
   * @returns Updated tenant settings
   */
  async updateEmailConfiguration(tenantId: string, emailConfiguration: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { emailConfiguration });
    } catch (error) {
      logger.error('Error updating email configuration:', { error });
      throw new Error(`Failed to update email configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Updates email templates
   * @param tenantId Tenant ID
   * @param emailTemplates Email templates data
   * @returns Updated tenant settings
   */
  async updateEmailTemplates(tenantId: string, emailTemplates: any): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { emailTemplates });
    } catch (error) {
      logger.error('Error updating email templates:', { error });
      throw new Error(`Failed to update email templates: ${(error as Error).message}`);
    }
  }

  /**
   * Updates custom domain settings
   * @param tenantId Tenant ID
   * @param customDomain Custom domain
   * @param domainVerified Whether the domain is verified
   * @returns Updated tenant settings
   */
  async updateCustomDomain(tenantId: string, customDomain: string, domainVerified: boolean = false): Promise<any> {
    try {
      return await this.updateTenantSettings(tenantId, { 
        customDomain,
        domainVerified
      });
    } catch (error) {
      logger.error('Error updating custom domain settings:', { error });
      throw new Error(`Failed to update custom domain settings: ${(error as Error).message}`);
    }
  }

  /**
   * Verifies a custom domain
   * @param tenantId Tenant ID
   * @returns Updated tenant settings
   */
  async verifyCustomDomain(tenantId: string): Promise<any> {
    try {
      // In a real implementation, this would perform actual DNS verification
      // For demonstration, we'll just mark the domain as verified
      return await this.updateTenantSettings(tenantId, { domainVerified: true });
    } catch (error) {
      logger.error('Error verifying custom domain:', { error });
      throw new Error(`Failed to verify custom domain: ${(error as Error).message}`);
    }
  }

  /**
   * Gets white-labeling settings
   * @param tenantId Tenant ID
   * @returns White-labeling settings or null if not found
   */
  async getWhiteLabelingSettings(tenantId: string): Promise<any | null> {
    try {
      const settings = await this.getTenantSettings(tenantId);
      
      if (!settings) {
        return null;
      }

      return {
        branding: settings.branding,
        whiteLabeling: settings.whiteLabeling,
        customDomain: settings.customDomain,
        domainVerified: settings.domainVerified,
        themeSettings: settings.themeSettings,
        loginPageSettings: settings.loginPageSettings
      };
    } catch (error) {
      logger.error('Error getting white-labeling settings:', { error });
      throw new Error(`Failed to get white-labeling settings: ${(error as Error).message}`);
    }
  }

  /**
   * Gets email settings
   * @param tenantId Tenant ID
   * @returns Email settings or null if not found
   */
  async getEmailSettings(tenantId: string): Promise<any | null> {
    try {
      const settings = await this.getTenantSettings(tenantId);
      
      if (!settings) {
        return null;
      }

      return {
        emailConfiguration: settings.emailConfiguration,
        emailTemplates: settings.emailTemplates
      };
    } catch (error) {
      logger.error('Error getting email settings:', { error });
      throw new Error(`Failed to get email settings: ${(error as Error).message}`);
    }
  }
}
