/**
 * Chat Cleanup Service
 * Handles automatic deletion of chat data after 30 days
 */

import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";

export class ChatCleanupService {
  private prisma: PrismaClient;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private readonly CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly RETENTION_DAYS = 30;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Starts the cleanup service
   */
  start(): void {
    logger.info("Starting chat cleanup service");

    // Schedule periodic cleanup (don't run immediately on startup)
    this.cleanupInterval = setInterval(() => {
      this.runCleanup().catch((error) => {
        logger.error("Error in scheduled chat cleanup:", error);
      });
    }, this.CLEANUP_INTERVAL_MS);

    logger.info(
      `Chat cleanup service started with ${this.RETENTION_DAYS} day retention`
    );

    // Run initial cleanup after a delay to allow server to fully start
    setTimeout(() => {
      this.runCleanup().catch((error) => {
        logger.error("Error in initial chat cleanup:", error);
      });
    }, 5000); // 5 second delay
  }

  /**
   * Stops the cleanup service
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      logger.info("Chat cleanup service stopped");
    }
  }

  /**
   * Runs the cleanup process
   */
  private async runCleanup(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.RETENTION_DAYS);

      logger.info(
        `Running chat cleanup for data older than ${cutoffDate.toISOString()}`
      );

      // Find conversations older than retention period
      const oldConversations = await this.prisma.conversation.findMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
        select: {
          id: true,
          tenantId: true,
          userId: true,
          createdAt: true,
        },
      });

      if (oldConversations.length === 0) {
        logger.info("No old conversations found for cleanup");
        return;
      }

      logger.info(`Found ${oldConversations.length} conversations to delete`);

      // Delete messages first (should cascade, but being explicit)
      const conversationIds = oldConversations.map((c) => c.id);

      const deletedMessages = await this.prisma.message.deleteMany({
        where: {
          conversationId: {
            in: conversationIds,
          },
        },
      });

      // Delete conversations
      const deletedConversations = await this.prisma.conversation.deleteMany({
        where: {
          id: {
            in: conversationIds,
          },
        },
      });

      logger.info(
        `Chat cleanup completed: deleted ${deletedMessages.count} messages and ${deletedConversations.count} conversations`
      );

      // Log cleanup statistics by tenant for audit purposes
      const tenantStats = oldConversations.reduce((acc, conv) => {
        acc[conv.tenantId] = (acc[conv.tenantId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      logger.info("Chat cleanup statistics by tenant:", tenantStats);
    } catch (error) {
      logger.error("Error during chat cleanup:", error);
      throw error;
    }
  }

  /**
   * Manually triggers cleanup (for testing or admin purposes)
   */
  async manualCleanup(): Promise<{
    deletedMessages: number;
    deletedConversations: number;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.RETENTION_DAYS);

      // Find conversations older than retention period
      const oldConversations = await this.prisma.conversation.findMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
        select: {
          id: true,
        },
      });

      if (oldConversations.length === 0) {
        return { deletedMessages: 0, deletedConversations: 0 };
      }

      const conversationIds = oldConversations.map((c) => c.id);

      // Delete messages first
      const deletedMessages = await this.prisma.message.deleteMany({
        where: {
          conversationId: {
            in: conversationIds,
          },
        },
      });

      // Delete conversations
      const deletedConversations = await this.prisma.conversation.deleteMany({
        where: {
          id: {
            in: conversationIds,
          },
        },
      });

      return {
        deletedMessages: deletedMessages.count,
        deletedConversations: deletedConversations.count,
      };
    } catch (error) {
      logger.error("Error during manual chat cleanup:", error);
      throw error;
    }
  }

  /**
   * Gets cleanup statistics without performing cleanup
   */
  async getCleanupStats(): Promise<{
    totalConversations: number;
    totalMessages: number;
    oldConversations: number;
    oldMessages: number;
    cutoffDate: Date;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.RETENTION_DAYS);

      const [totalConversations, totalMessages, oldConversations] =
        await Promise.all([
          this.prisma.conversation.count(),
          this.prisma.message.count(),
          this.prisma.conversation.findMany({
            where: {
              createdAt: {
                lt: cutoffDate,
              },
            },
            select: {
              id: true,
            },
          }),
        ]);

      const oldConversationIds = oldConversations.map((c) => c.id);
      const oldMessages =
        oldConversationIds.length > 0
          ? await this.prisma.message.count({
              where: {
                conversationId: {
                  in: oldConversationIds,
                },
              },
            })
          : 0;

      return {
        totalConversations,
        totalMessages,
        oldConversations: oldConversations.length,
        oldMessages,
        cutoffDate,
      };
    } catch (error) {
      logger.error("Error getting cleanup stats:", error);
      throw error;
    }
  }
}
