import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { IntegrityConfigurationService, IntegrityClause, IntegrityConfigurationData } from "./IntegrityConfigurationService";
import { GoogleGenAI } from "@google/genai";

export interface IntegrityAnalysisResult {
  configurationUsed: {
    id: string;
    name: string;
    totalClauses: number;
  };
  overallRiskScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  maxPossibleScore: number;
  clauses: Record<string, IntegrityClauseResult>;
  risks: DocumentRisk[];
  extractionDate: string;
  processingTimeMs: number;
}

export interface DocumentRisk {
  id: string;
  title: string;
  description: string;
  category: 'Legal' | 'Financial' | 'Operational' | 'Compliance' | 'Security' | 'Commercial';
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  impact: string;
  mitigation: string;
  confidence: number;
}

export interface IntegrityClauseResult {
  clauseName: string;
  value: string; // Either the found value or "N/A"
  description: string; // Description of what this value means
  riskScore: number;
  maxScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  confidence: number;
  sourceField?: string | null;
  reasoning?: string;
}

/**
 * Service for performing integrity analysis on contracts
 */
export class IntegrityAnalysisService {
  private prisma: PrismaClient;
  private contractExtractionRepository: ContractExtractionRepository;
  private integrityConfigService: IntegrityConfigurationService;
  private googleAI: GoogleGenAI;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.contractExtractionRepository = new ContractExtractionRepository(prisma);
    this.integrityConfigService = new IntegrityConfigurationService(prisma);

    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("GEMINI_API_KEY environment variable is required");
    }
    this.googleAI = new GoogleGenAI({ apiKey });
  }

  /**
   * Perform integrity analysis on a contract
   */
  async analyzeContract(
    contractId: string,
    userId: string,
    tenantId: string
  ): Promise<IntegrityAnalysisResult> {
    const startTime = Date.now();

    try {


      // Get contract extraction data
      const extraction = await this.contractExtractionRepository.findByContractId(contractId);
      if (!extraction) {
        throw new Error("Contract extraction data not found");
      }

      // Get OCR text for more comprehensive analysis
      const ocrText = await this.getOCRTextForContract(contractId);

      // Get user's active integrity configuration
      const activeConfig = await this.integrityConfigService.getActiveConfiguration(userId, tenantId);
      const configData = activeConfig.clauses as unknown as IntegrityConfigurationData;

      if (!configData || !configData.clauses || !Array.isArray(configData.clauses)) {
        throw new Error("Invalid configuration data structure - clauses array not found");
      }



      // Analyze clauses and generate risks using AI with both extracted fields and OCR text
      const analysisResult = await this.analyzeClausesAndRisksWithAI(
        extraction.fixedFields,
        extraction.dynamicFields,
        extraction.specialFields,
        configData.clauses,
        ocrText
      );

      // Calculate overall risk score
      const overallRiskScore = this.calculateOverallRiskScore(analysisResult.clauseResults);
      const maxPossibleScore = this.integrityConfigService.calculateMaxScore(configData);
      const riskLevel = this.determineRiskLevel(overallRiskScore, maxPossibleScore);

      const result: IntegrityAnalysisResult = {
        configurationUsed: {
          id: activeConfig.id,
          name: activeConfig.configurationName,
          totalClauses: configData.clauses.length,
        },
        overallRiskScore,
        riskLevel,
        maxPossibleScore,
        clauses: analysisResult.clauseResults,
        risks: analysisResult.risks,
        extractionDate: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime,
      };

      // Store the analysis result
      await this.storeAnalysisResult(contractId, tenantId, result);


      return result;

    } catch (error) {
      logger.error(`Error in integrity analysis for contract ${contractId}:`, error);
      throw error;
    }
  }

  /**
   * Get OCR text for a contract
   */
  private async getOCRTextForContract(contractId: string): Promise<string | null> {
    try {
      const contractVersion = await this.prisma.contractVersion.findFirst({
        where: { contractId },
        orderBy: { createdAt: 'desc' },
        select: { ocrText: true }
      });

      return contractVersion?.ocrText || null;
    } catch (error) {
      logger.error(`Error getting OCR text for contract ${contractId}:`, error);
      return null;
    }
  }

  /**
   * Analyze clauses and generate risks using AI with both extracted fields and OCR text
   */
  private async analyzeClausesAndRisksWithAI(
    fixedFields: any,
    dynamicFields: any,
    specialFields: any,
    clauses: IntegrityClause[],
    ocrText?: string | null
  ): Promise<{ clauseResults: Record<string, IntegrityClauseResult>; risks: DocumentRisk[] }> {
    try {


      // Prepare contract data for AI analysis
      const contractData = {
        fixedFields,
        dynamicFields,
        specialFields,
      };

      // Create AI prompt for clause analysis and risk generation (enhanced with OCR text if available)
      const prompt = this.createAnalysisAndRiskPrompt(contractData, clauses, ocrText);
      // Call Gemini AI
      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.1,
          maxOutputTokens: 8192,
        },
        contents: [
          {
            role: "user",
            parts: [{ text: prompt }],
          },
        ],
      });

      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        logger.error("Invalid response structure from Gemini API");
        throw new Error("Invalid response from AI service");
      }

      const analysisText = response.candidates[0].content.parts?.[0]?.text;
      if (!analysisText) {
        logger.error("No analysis text in response");
        throw new Error("No analysis text received from AI service");
      }



      // Parse AI response - try to extract JSON from the response
      let aiAnalysis;
      try {
        // First try direct JSON parsing
        aiAnalysis = JSON.parse(analysisText);
      } catch (parseError) {
        logger.warn("Direct JSON parsing failed, attempting to extract JSON from response");

        // Try to extract JSON from markdown code blocks or other formatting
        const jsonMatch = analysisText.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
        if (jsonMatch) {
          try {
            aiAnalysis = JSON.parse(jsonMatch[1]);
            logger.info("Successfully extracted JSON from code block");
          } catch (extractError) {
            logger.error("Failed to parse extracted JSON:", extractError);
            logger.error("Extracted text:", jsonMatch[1]);
            throw new Error(`Failed to parse extracted JSON: ${(extractError as Error).message}`);
          }
        } else {
          // Try to find JSON object in the response
          const jsonStart = analysisText.indexOf('{');
          const jsonEnd = analysisText.lastIndexOf('}');
          if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            const jsonText = analysisText.substring(jsonStart, jsonEnd + 1);
            try {
              aiAnalysis = JSON.parse(jsonText);
              logger.info("Successfully extracted JSON from response text");
            } catch (extractError) {
              logger.error("Failed to parse AI response as JSON:", parseError);
              logger.error("Raw AI response:", analysisText);
              throw new Error(`Failed to parse AI response as JSON: ${(parseError as Error).message}`);
            }
          } else {
            logger.error("Failed to parse AI response as JSON:", parseError);
            logger.error("Raw AI response:", analysisText);
            throw new Error(`Failed to parse AI response as JSON: ${(parseError as Error).message}`);
          }
        }
      }

      if (!aiAnalysis || !aiAnalysis.clauses) {
        logger.error("Invalid AI analysis structure:", JSON.stringify(aiAnalysis, null, 2));
        throw new Error("Invalid AI analysis structure - missing clauses object");
      }

      // Process clause results and match with configuration
      const clauseResults: Record<string, IntegrityClauseResult> = {};

      for (const clause of clauses) {
        const aiResult = aiAnalysis.clauses[clause.id];

        let value: string;
        let description: string;
        let riskScore: number;
        let riskLevel: 'Low' | 'Medium' | 'High';

        if (aiResult?.found && aiResult?.extractedValue) {
          // Find the best matching possible value
          const matchedValue = this.findBestMatch(aiResult.extractedValue, clause.possibleValues);

          // Check if the matched value indicates "not mentioned" or similar
          if (matchedValue?.value &&
            (matchedValue.value.toLowerCase().includes('not mentioned') ||
              matchedValue.value.toLowerCase().includes('not found') ||
              matchedValue.value.toLowerCase().includes('not specified'))) {
            // Treat as not found
            value = "N/A";
            description = `${clause.description} This clause is explicitly mentioned as "${matchedValue.value}" in the contract, which indicates it was not properly defined. Consider adding specific terms to reduce risk.`;
            riskScore = 0;
            riskLevel = 'High';
          } else {
            // Actually found with a meaningful value
            value = matchedValue?.value || aiResult.extractedValue;
            description = matchedValue?.description || `Found: "${aiResult.extractedValue}" - This clause was identified in the contract with the specified terms.`;
            riskScore = matchedValue?.weightage || 0;
            riskLevel = matchedValue?.riskLevel || 'High';
          }
        } else {
          // Not found - use "N/A" and lowest risk score
          value = "N/A";
          description = `This clause was not found in the contract. ${clause.description} Consider adding explicit terms for this clause to reduce risk.`;
          riskScore = 0;
          riskLevel = 'High';
        }

        clauseResults[clause.id] = {
          clauseName: clause.name,
          value,
          description,
          riskScore,
          maxScore: Math.max(...clause.possibleValues.map(v => v.weightage)),
          riskLevel,
          confidence: aiResult?.confidence || 0,
        };
      }

      // Process risks from AI analysis
      const risks: DocumentRisk[] = aiAnalysis.risks || [];

      return { clauseResults, risks };

    } catch (error) {
      logger.error("Error in AI clause analysis:", error);
      throw new Error("Failed to analyze clauses with AI");
    }
  }

  /**
   * Create AI prompt for integrity analysis and risk generation with optional OCR text
   */
  private createAnalysisAndRiskPrompt(contractData: any, clauses: IntegrityClause[], ocrText?: string | null): string {
    const clauseDescriptions = clauses.map(clause => {
      const possibleValues = clause.possibleValues.map(v => `"${v.value}"`).join(', ');
      return `${clause.id}: "${clause.name}" - ${clause.description}. Possible values: ${possibleValues}`;
    }).join('\n');

    let prompt = `You are an expert contract analysis system. Analyze the following contract data and extract information about specific integrity clauses.

CONTRACT DATA (Extracted Fields):
${JSON.stringify(contractData, null, 2)}`;

    // Add OCR text if available for more comprehensive analysis
    if (ocrText) {
      prompt += `

FULL CONTRACT TEXT (OCR):
${ocrText}`;
    }

    prompt += `

CLAUSES TO ANALYZE:
${clauseDescriptions}

INSTRUCTIONS:
1. Search through ALL available data sources (extracted fields${ocrText ? ' and full contract text' : ''}) for information about each clause
2. For each clause, determine if it's present and identify which specific value from the possible values list best matches what you found
3. If found, set found=true and provide the exact matching value from the possible values list
4. If not found or unclear, set found=false and extractedValue=null
5. Provide confidence scores (0.0-1.0) based on how clearly the information is stated
${ocrText ? '6. Use the full contract text for more accurate analysis when extracted fields are insufficient' : ''}
7. Additionally, analyze the entire contract to identify potential risks that could impact the organization
8. Generate 3-8 key risks with detailed descriptions, impact assessment, and mitigation suggestions

OUTPUT FORMAT:
Return a valid JSON object with this structure:
{
  "clauses": {
    "clause_id": {
      "found": boolean,
      "extractedValue": "exact matching value from possible values list or null if not found",
      "confidence": number,
      "sourceField": "field where found or 'OCR_TEXT' if found in full text or null",
      "reasoning": "brief explanation"
    }
  },
  "risks": [
    {
      "id": "unique_risk_id",
      "title": "Risk Title",
      "description": "Detailed description of the risk",
      "category": "Legal|Financial|Operational|Compliance|Security|Commercial",
      "severity": "Low|Medium|High|Critical",
      "impact": "Description of potential impact on the organization",
      "mitigation": "Suggested mitigation strategies",
      "confidence": 0.0-1.0
    }
  ]
}

Be thorough in your search and provide accurate confidence scores. Look for semantic matches, not just exact text matches. For risks, focus on contractual terms that could negatively impact the organization.`;

    return prompt;
  }

  /**
   * Find best matching value from possible values
   */
  private findBestMatch(extractedValue: string | null, possibleValues: any[]): any | null {
    if (!extractedValue) {
      // Return "Not mentioned" or lowest scoring option
      return possibleValues.find(v => v.value.toLowerCase().includes('not mentioned')) ||
        possibleValues.reduce((min, current) => current.weightage < min.weightage ? current : min);
    }

    // Try exact match first
    const exactMatch = possibleValues.find(v =>
      v.value.toLowerCase() === extractedValue.toLowerCase()
    );
    if (exactMatch) return exactMatch;

    // Try partial match
    const partialMatch = possibleValues.find(v =>
      extractedValue.toLowerCase().includes(v.value.toLowerCase()) ||
      v.value.toLowerCase().includes(extractedValue.toLowerCase())
    );
    if (partialMatch) return partialMatch;

    // Semantic matching for common patterns
    const lowerExtracted = extractedValue.toLowerCase();

    for (const value of possibleValues) {
      const lowerValue = value.value.toLowerCase();

      // Check for semantic matches
      if (this.isSemanticMatch(lowerExtracted, lowerValue)) {
        return value;
      }
    }

    // Default to "Not mentioned" or lowest scoring option
    return possibleValues.find(v => v.value.toLowerCase().includes('not mentioned')) ||
      possibleValues.reduce((min, current) => current.weightage < min.weightage ? current : min);
  }

  /**
   * Check for semantic matches between extracted and possible values
   */
  private isSemanticMatch(extracted: string, possible: string): boolean {
    // Define semantic equivalents
    const semanticMaps = [
      ['yes', 'allowed', 'available', 'present', 'mentioned'],
      ['no', 'not allowed', 'not available', 'not present', 'not mentioned'],
      ['both', 'mutual', 'either party', 'both parties'],
      ['customer', 'client', 'buyer'],
      ['supplier', 'vendor', 'provider'],
      ['unlimited', 'uncapped', 'no limit'],
      ['capped', 'limited', 'maximum'],
    ];

    for (const synonyms of semanticMaps) {
      const extractedMatch = synonyms.some(syn => extracted.includes(syn));
      const possibleMatch = synonyms.some(syn => possible.includes(syn));

      if (extractedMatch && possibleMatch) {
        return true;
      }
    }

    return false;
  }

  /**
   * Calculate overall risk score
   */
  private calculateOverallRiskScore(clauseResults: Record<string, IntegrityClauseResult>): number {
    return Object.values(clauseResults).reduce((total, result) => total + result.riskScore, 0);
  }

  /**
   * Determine risk level based on score
   * Higher scores mean higher integrity and lower risk
   */
  private determineRiskLevel(score: number, maxScore: number): 'Low' | 'Medium' | 'High' {
    const percentage = (score / maxScore) * 100;

    if (percentage >= 70) return 'Low';
    if (percentage >= 40) return 'Medium';
    return 'High';
  }

  /**
   * Store analysis result in database
   */
  private async storeAnalysisResult(
    contractId: string,
    tenantId: string,
    result: IntegrityAnalysisResult
  ): Promise<void> {
    try {
      await this.contractExtractionRepository.updateIntegrityAnalysis(
        contractId,
        tenantId,
        result
      );
    } catch (error) {
      logger.error("Error storing integrity analysis result:", error);
      throw error;
    }
  }
}
