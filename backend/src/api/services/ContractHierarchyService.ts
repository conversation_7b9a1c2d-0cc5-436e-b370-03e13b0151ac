/**
 * Contract Hierarchy Service
 * Handles contract hierarchy mapping and relationship detection
 */

import { logger } from "../../infrastructure/logging/logger";

/**
 * Contract hierarchy levels based on typical contract structure
 */
export enum ContractHierarchyLevel {
  LEVEL_1 = 1, // Master agreements (MSA, GMSA, OMA, GTC)
  LEVEL_2 = 2, // Schedules (SLA, DPA, Pricing, Governance)
  LEVEL_3 = 3, // Program Charter, Support Terms, Cloud Addenda
  LEVEL_4 = 4, // Statements of Work (SOW), Ordering Documents
  LEVEL_5 = 5, // Order Forms, Engagement Letters, Product Use Rights
  LEVEL_6 = 6, // Purchase Orders (PO)
  LEVEL_7 = 7, // Invoices
}

/**
 * Contract hierarchy relationship information
 */
export interface ContractHierarchyInfo {
  level: ContractHierarchyLevel;
  parentTypes: string[];
  childTypes: string[];
  description: string;
}

/**
 * Contract with hierarchy information
 */
export interface ContractWithHierarchy {
  id: string;
  title: string;
  agreementType: string;
  startDate?: Date;
  endDate?: Date;
  hierarchyLevel?: ContractHierarchyLevel | null;
  parentContracts?: string[];
  childContracts?: string[];
  groupId?: string;
}

/**
 * Contract hierarchy relationship
 */
export interface ContractRelationship {
  parentId: string;
  childId: string;
  relationshipType: "GOVERNS" | "REFERENCES" | "SUPPLEMENTS";
}

/**
 * Contract Hierarchy Service
 */
export class ContractHierarchyService {
  /**
   * Document type to hierarchy level mapping
   */
  private readonly hierarchyMapping: Map<string, ContractHierarchyInfo> =
    new Map([
      // Level 1 - Master Agreements
      [
        "MSA",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: ["SLA", "DPA", "SOW", "PO"],
          description: "Master Services Agreement",
        },
      ],
      [
        "GMSA",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: ["SLA", "DPA", "SOW", "PO"],
          description: "Global Master Services Agreement",
        },
      ],
      [
        "OMA",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: ["SLA", "DPA", "SOW", "PO"],
          description: "Oracle Master Agreement",
        },
      ],
      [
        "GTC",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: ["SLA", "DPA", "SOW", "PO"],
          description: "General Terms and Conditions",
        },
      ],

      // Level 2 - Schedules and Policies
      [
        "SLA",
        {
          level: ContractHierarchyLevel.LEVEL_2,
          parentTypes: ["MSA", "GMSA", "OMA", "GTC"],
          childTypes: ["TSA", "SOW", "PO"],
          description: "Service Level Agreement",
        },
      ],
      [
        "DPA",
        {
          level: ContractHierarchyLevel.LEVEL_2,
          parentTypes: ["MSA", "GMSA", "OMA", "GTC"],
          childTypes: ["TSA", "SOW", "PO"],
          description: "Data Processing Agreement",
        },
      ],

      // Level 3 - Support and Governance
      [
        "TSA",
        {
          level: ContractHierarchyLevel.LEVEL_3,
          parentTypes: ["SLA", "DPA"],
          childTypes: ["SOW", "PO"],
          description: "Transition Services Agreement",
        },
      ],

      // Level 4 - Work Statements and Ordering
      [
        "SOW",
        {
          level: ContractHierarchyLevel.LEVEL_4,
          parentTypes: ["MSA", "GMSA", "SLA", "DPA", "TSA"],
          childTypes: ["PO"],
          description: "Statement of Work",
        },
      ],

      // Level 5 - Order Forms and Engagement
      [
        "LOI",
        {
          level: ContractHierarchyLevel.LEVEL_5,
          parentTypes: ["MSA", "SOW"],
          childTypes: ["PO"],
          description: "Letter of Intent",
        },
      ],

      // Level 6 - Purchase Orders
      [
        "PO",
        {
          level: ContractHierarchyLevel.LEVEL_6,
          parentTypes: ["MSA", "SOW", "SLA", "DPA", "TSA"],
          childTypes: [],
          description: "Purchase Order",
        },
      ],

      // Level 2 - Schedules (additional)
      [
        "SCHEDULE",
        {
          level: ContractHierarchyLevel.LEVEL_2,
          parentTypes: ["MSA", "GMSA", "OMA", "GTC"],
          childTypes: ["TSA", "SOW", "PO"],
          description: "Schedule Agreement",
        },
      ],

      // Level 7 - Invoices (if tracked as contracts)
      [
        "INVOICE",
        {
          level: ContractHierarchyLevel.LEVEL_7,
          parentTypes: ["PO"],
          childTypes: [],
          description: "Invoice",
        },
      ],

      // Standalone agreements (Level 1 by default)
      [
        "NDA",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: [],
          description: "Non-Disclosure Agreement",
        },
      ],
      [
        "EULA",
        {
          level: ContractHierarchyLevel.LEVEL_1,
          parentTypes: [],
          childTypes: [],
          description: "End User License Agreement",
        },
      ],
    ]);

  /**
   * Gets hierarchy information for a document type
   * @param agreementType Agreement type
   * @returns Hierarchy information or null if not found
   */
  getHierarchyInfo(agreementType: string): ContractHierarchyInfo | null {
    const normalizedType = agreementType?.toUpperCase().trim();
    return this.hierarchyMapping.get(normalizedType) || null;
  }

  /**
   * Gets hierarchy level for a document type
   * @param agreementType Agreement type
   * @returns Hierarchy level or null if not found
   */
  getHierarchyLevel(agreementType: string): ContractHierarchyLevel | null {
    const info = this.getHierarchyInfo(agreementType);
    return info?.level || null;
  }

  /**
   * Determines if one contract type can be a parent of another
   * @param parentType Parent agreement type
   * @param childType Child agreement type
   * @returns True if parent-child relationship is valid
   */
  canBeParent(parentType: string, childType: string): boolean {
    const childInfo = this.getHierarchyInfo(childType);
    if (!childInfo) return false;

    const normalizedParentType = parentType?.toUpperCase().trim();
    return childInfo.parentTypes.includes(normalizedParentType);
  }

  /**
   * Analyzes contracts within a group and determines hierarchy relationships
   * @param contracts Array of contracts in the same group
   * @returns Array of contracts with hierarchy information
   */
  analyzeContractHierarchy(
    contracts: ContractWithHierarchy[]
  ): ContractWithHierarchy[] {
    logger.info(`Analyzing hierarchy for ${contracts.length} contracts`);

    // Add hierarchy levels to contracts
    const contractsWithLevels = contracts.map((contract) => ({
      ...contract,
      hierarchyLevel: this.getHierarchyLevel(contract.agreementType),
    }));

    // Sort by hierarchy level and then by date
    contractsWithLevels.sort((a, b) => {
      // First sort by hierarchy level
      const levelA = a.hierarchyLevel || 999;
      const levelB = b.hierarchyLevel || 999;
      if (levelA !== levelB) {
        return levelA - levelB;
      }

      // Then sort by start date
      const dateA = a.startDate?.getTime() || 0;
      const dateB = b.startDate?.getTime() || 0;
      return dateA - dateB;
    });

    // Determine parent-child relationships
    const relationships = this.determineRelationships(contractsWithLevels);

    // Apply relationships to contracts
    return this.applyRelationships(contractsWithLevels, relationships);
  }

  /**
   * Determines parent-child relationships between contracts
   * @param contracts Sorted contracts with hierarchy levels
   * @returns Array of relationships
   */
  private determineRelationships(
    contracts: ContractWithHierarchy[]
  ): ContractRelationship[] {
    const relationships: ContractRelationship[] = [];

    for (let i = 0; i < contracts.length; i++) {
      const contract = contracts[i];

      // Find potential parents (contracts with lower hierarchy levels)
      for (let j = 0; j < i; j++) {
        const potentialParent = contracts[j];

        if (
          this.canBeParent(
            potentialParent.agreementType,
            contract.agreementType
          )
        ) {
          // Check if this is the most appropriate parent (closest in hierarchy and time)
          const isClosestParent = this.isClosestParent(
            potentialParent,
            contract,
            contracts.slice(0, i)
          );

          if (isClosestParent) {
            relationships.push({
              parentId: potentialParent.id,
              childId: contract.id,
              relationshipType: "GOVERNS",
            });
            break; // Only assign one direct parent
          }
        }
      }
    }

    return relationships;
  }

  /**
   * Determines if a potential parent is the closest/most appropriate parent
   * @param potentialParent Potential parent contract
   * @param child Child contract
   * @param previousContracts All contracts that come before the child
   * @returns True if this is the closest parent
   */
  private isClosestParent(
    potentialParent: ContractWithHierarchy,
    child: ContractWithHierarchy,
    previousContracts: ContractWithHierarchy[]
  ): boolean {
    const parentLevel = potentialParent.hierarchyLevel || 999;
    const childLevel = child.hierarchyLevel || 999;

    // Find other potential parents
    const otherPotentialParents = previousContracts.filter(
      (contract) =>
        contract.id !== potentialParent.id &&
        this.canBeParent(contract.agreementType, child.agreementType)
    );

    // If no other potential parents, this is the closest
    if (otherPotentialParents.length === 0) {
      return true;
    }

    // Find the parent with the closest hierarchy level
    const closestLevel = Math.min(
      ...otherPotentialParents.map((p) => p.hierarchyLevel || 999),
      parentLevel
    );

    if (parentLevel !== closestLevel) {
      return false;
    }

    // Among parents at the same level, choose the most recent one
    const sameLevel = otherPotentialParents.filter(
      (p) => (p.hierarchyLevel || 999) === closestLevel
    );
    sameLevel.push(potentialParent);

    const mostRecent = sameLevel.reduce((latest, current) => {
      const latestDate = latest.startDate?.getTime() || 0;
      const currentDate = current.startDate?.getTime() || 0;
      return currentDate > latestDate ? current : latest;
    });

    return mostRecent.id === potentialParent.id;
  }

  /**
   * Applies relationships to contracts
   * @param contracts Contracts array
   * @param relationships Relationships array
   * @returns Contracts with parent/child information
   */
  private applyRelationships(
    contracts: ContractWithHierarchy[],
    relationships: ContractRelationship[]
  ): ContractWithHierarchy[] {
    return contracts.map((contract) => {
      const parentRelationships = relationships.filter(
        (r) => r.childId === contract.id
      );
      const childRelationships = relationships.filter(
        (r) => r.parentId === contract.id
      );

      return {
        ...contract,
        parentContracts: parentRelationships.map((r) => r.parentId),
        childContracts: childRelationships.map((r) => r.childId),
      };
    });
  }

  /**
   * Gets all supported document types with their hierarchy information
   * @returns Map of document types to hierarchy info
   */
  getAllHierarchyTypes(): Map<string, ContractHierarchyInfo> {
    return new Map(this.hierarchyMapping);
  }
}
