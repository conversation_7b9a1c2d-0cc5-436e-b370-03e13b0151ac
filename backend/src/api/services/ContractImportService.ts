/**
 * Contract Import Service
 * Handles importing contracts from CSV, Excel, and document files
 */

import { ContractService } from "./ContractService";
import {
  ContractStatus,
  ContractClassification,
  AgreementType,
  SecurityClassification,
  PrismaClient,
  NotificationType,
  NotificationPriority,
} from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import { DocumentFormat } from "../../infrastructure/ai/interfaces";
import { parse as csvParse } from "csv-parse/sync";
import * as XLSX from "xlsx";

import { logger } from "../../infrastructure/logging/logger";
import {
  ContractAIService,
  ContractDocumentAnalysisResult,
} from "./ContractAIService";
import { importJobService, ImportJobResult } from "./ImportJobService";
import { AppError } from "../../infrastructure/middleware/errorHandler";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { NotificationService } from "./NotificationService";
import * as path from "path";

/**
 * Contract import result interface
 */
export interface ContractImportResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    file?: string;
    row?: number;
    error: string;
    data?: any;
  }>;
  contracts: Array<{
    id: string;
    title: string;
    status: string;
    metadata?: any;
  }>;
  autoGroupingSuggestions?: any; // Auto-grouping suggestions for imported contracts
}

/**
 * Contract import service
 */
export class ContractImportService {
  private contractService: ContractService;
  private contractAIService: ContractAIService;
  private contractExtractionRepository: ContractExtractionRepository;
  private notificationService: NotificationService;

  constructor(contractService: ContractService, prisma?: PrismaClient) {
    this.contractService = contractService;
    const prismaClient = prisma || new PrismaClient();
    this.contractAIService = new ContractAIService(prismaClient);
    this.contractExtractionRepository = new ContractExtractionRepository(
      prismaClient
    );
    this.notificationService = new NotificationService(prismaClient);
  }

  /**
   * Gets document format from file name
   * @param fileName File name
   * @returns Document format enum
   */
  private getDocumentFormatFromFileName(fileName: string): DocumentFormat {
    const ext = path.extname(fileName).toLowerCase();

    switch (ext) {
      case ".pdf":
        return DocumentFormat.PDF;
      case ".docx":
      case ".doc":
        return DocumentFormat.DOCX;
      case ".txt":
      case ".rtf":
      case ".odt":
      case ".md":
      case ".html":
      case ".htm":
      case ".csv":
        return DocumentFormat.TEXT; // Use TEXT as fallback for text-based formats
      case ".xlsx":
      case ".xls":
        return DocumentFormat.XLSX;
      default:
        // For formats not explicitly defined in DocumentFormat enum,
        // default to TEXT as a fallback
        return DocumentFormat.TEXT;
    }
  }

  /**
   * Imports contracts from a CSV buffer
   * @param buffer CSV file buffer
   * @param tenantId Tenant ID
   * @returns Import result
   */
  async importFromCsv(
    buffer: Buffer,
    tenantId: string
  ): Promise<ContractImportResult> {
    try {
      // Parse CSV
      const records = csvParse(buffer, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      return await this.processImportRecords(records, tenantId);
    } catch (error) {
      logger.error("Error importing contracts from CSV:", error);
      throw new Error(
        `Failed to import contracts from CSV: ${(error as Error).message}`
      );
    }
  }

  /**
   * Imports contracts from an Excel buffer
   * @param buffer Excel file buffer
   * @param tenantId Tenant ID
   * @returns Import result
   */
  async importFromExcel(
    buffer: Buffer,
    tenantId: string
  ): Promise<ContractImportResult> {
    try {
      // Parse Excel
      const workbook = XLSX.read(buffer, { type: "buffer" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const records = XLSX.utils.sheet_to_json(worksheet);

      return await this.processImportRecords(records, tenantId);
    } catch (error) {
      logger.error("Error importing contracts from Excel:", error);
      throw new Error(
        `Failed to import contracts from Excel: ${(error as Error).message}`
      );
    }
  }

  /**
   * Imports contracts from document files with job tracking (for background processing)
   * @param files Array of file objects with buffer and originalname
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param jobId Optional job ID for tracking
   * @returns Import result
   */
  async importFromDocumentsWithJobTracking(
    files: Array<{ buffer: Buffer; originalname: string }>,
    tenantId: string,
    userId: string,
    jobId?: string
  ): Promise<ContractImportResult> {
    try {
      // Create job if not provided
      if (!jobId) {
        const concurrencyLimit = Math.min(3, files.length);
        const estimatedBatches = Math.ceil(files.length / concurrencyLimit);
        const estimatedTimeSeconds = estimatedBatches * 120; // 2 minutes per batch

        jobId = importJobService.createJob(
          tenantId,
          userId,
          files.length,
          estimatedTimeSeconds,
          {
            totalFiles: files.length,
            concurrencyLimit,
            estimatedBatches,
          }
        );
      }

      // Update job status to processing
      importJobService.updateJobStatus(jobId, "processing");

      // Process documents with job tracking
      const result = await this.importFromDocuments(files, tenantId, userId, jobId);

      // Complete the job
      const jobResult: ImportJobResult = {
        totalProcessed: result.totalProcessed,
        successful: result.successful,
        failed: result.failed,
        errors: result.errors,
        contracts: result.contracts,
      };

      importJobService.completeJob(jobId, jobResult);

      return result;
    } catch (error) {
      // Fail the job if provided
      if (jobId) {
        importJobService.failJob(jobId, (error as Error).message);
      }
      throw error;
    }
  }

  /**
   * Imports contracts from document files (PDF, DOCX, etc.)
   * @param files Array of file objects with buffer and originalname
   * @param tenantId Tenant ID
   * @param userId User ID for notifications
   * @param jobId Optional job ID for progress tracking
   * @returns Import result
   */
  async importFromDocuments(
    files: Array<{ buffer: Buffer; originalname: string }>,
    tenantId: string,
    userId: string,
    jobId?: string
  ): Promise<ContractImportResult> {
    try {
      const result: ContractImportResult = {
        totalProcessed: files.length,
        successful: 0,
        failed: 0,
        errors: [],
        contracts: [],
      };

      // Validate files before processing
      const validFiles = files.filter((file) => {
        const ext = path.extname(file.originalname).toLowerCase();
        const validExtensions = [
          ".pdf",
          ".docx",
          ".doc",
          ".txt",
          ".rtf",
          ".odt",
          ".md",
          ".html",
          ".htm",
          ".csv",
          ".xlsx",
          ".xls",
        ];

        if (!validExtensions.includes(ext)) {
          result.failed++;
          result.errors.push({
            file: file.originalname,
            error: `Unsupported file format: ${ext}. Supported formats are: PDF, Word, Excel, CSV, TXT, RTF, ODT, Markdown, HTML.`,
          });
          return false;
        }

        if (file.buffer.length === 0) {
          result.failed++;
          result.errors.push({
            file: file.originalname,
            error: "Empty file",
          });
          return false;
        }

        return true;
      });

      // Check for duplicate filenames before processing
      const duplicateFiles: string[] = [];
      for (const file of validFiles) {
        // Extract the title that would be used for the contract (filename without extension)
        const contractTitle = path.basename(
          file.originalname,
          path.extname(file.originalname)
        );

        try {
          // Check for existing contracts in the ContractExtraction table by looking for matching original_filename
          const existingExtractions =
            await this.contractExtractionRepository.getByTenantWithFilters(
              tenantId,
              {
                page: 1,
                limit: 50, // Get more to check for matches since we need to search through fixedFields
              }
            );

          // Check if any extraction has a matching original_filename in fixed fields
          const hasMatchingFilename = existingExtractions.extractions.some(
            (extraction) => {
              const fixedFields = extraction.fixedFields as any;
              const originalFilename = fixedFields?.original_filename?.value;
              if (originalFilename) {
                // Extract filename without extension for comparison
                const existingTitle = path.basename(
                  originalFilename,
                  path.extname(originalFilename)
                );
                return existingTitle === contractTitle;
              }
              return false;
            }
          );

          if (hasMatchingFilename) {
            duplicateFiles.push(file.originalname);
            result.failed++;
            result.errors.push({
              file: file.originalname,
              error: `A contract with the name "${contractTitle}" already exists. Please rename the file or use a different filename.`,
            });
          }
        } catch (error) {
          logger.error(
            `Error checking for duplicate filename ${file.originalname}:`,
            error
          );
          // Continue processing if the duplicate check fails - don't block the entire import
        }
      }

      // If there are duplicate files, throw a validation error
      if (duplicateFiles.length > 0) {
        const duplicateFileNames = duplicateFiles.join(", ");
        throw new AppError(
          `Duplicate filenames detected: ${duplicateFileNames}. Please rename the files or use different filenames.`,
          422, // 422 Unprocessable Entity for validation errors
          true
        );
      }

      // Filter out duplicate files from processing (this should be empty now, but keeping for safety)
      const filesToProcess = validFiles.filter(
        (file) => !duplicateFiles.includes(file.originalname)
      );

      if (filesToProcess.length === 0) {
        logger.warn(
          "No files to process after validation and duplicate checks"
        );
        return result;
      }

      // Process files with parallel processing and concurrency limits
      const concurrencyLimit = Math.min(3, filesToProcess.length); // Max 3 concurrent processes
      const batches = this.createBatches(filesToProcess, concurrencyLimit);

      logger.info(
        `Processing ${filesToProcess.length} files in ${batches.length} batches with concurrency limit of ${concurrencyLimit}`
      );

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        logger.info(
          `Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length
          } files`
        );

        // Process batch in parallel
        const batchPromises = batch.map(async (file) => {
          try {
            logger.info(`Processing document file: ${file.originalname}`);

            // Add processing status notification
            logger.info(`Analyzing document with AI: ${file.originalname}`);

            // Try to analyze document with retries
            let analysisResult: ContractDocumentAnalysisResult = {};
            let retryCount = 0;
            const maxRetries = 2;

            while (retryCount <= maxRetries) {
              try {
                // Analyze document using AI
                analysisResult =
                  await this.contractAIService.analyzeContractDocument(
                    file.buffer,
                    file.originalname
                  );

                // If we got a valid result with at least some metadata, break the retry loop
                if (
                  analysisResult &&
                  (analysisResult.title ||
                    analysisResult.agreementType ||
                    analysisResult.contractType ||
                    (analysisResult.value &&
                      (typeof analysisResult.value === "string" ||
                        (typeof analysisResult.value === "object" &&
                          analysisResult.value.amount))))
                ) {
                  break;
                }

                // If we got an empty or incomplete result, retry
                logger.warn(
                  `Incomplete analysis result for ${file.originalname
                  }, retrying (${retryCount + 1}/${maxRetries})`
                );
                retryCount++;

                // Wait before retrying (exponential backoff)
                await new Promise((resolve) =>
                  setTimeout(resolve, 1000 * Math.pow(2, retryCount))
                );
              } catch (analysisError) {
                logger.error(
                  `Error analyzing document ${file.originalname} (attempt ${retryCount + 1
                  }/${maxRetries + 1}):`,
                  analysisError
                );
                retryCount++;

                // If we've exhausted all retries, rethrow the error
                if (retryCount > maxRetries) {
                  throw analysisError;
                }

                // Wait before retrying (exponential backoff)
                await new Promise((resolve) =>
                  setTimeout(resolve, 1000 * Math.pow(2, retryCount))
                );
              }
            }

            // Always use the filename as the contract title for consistency
            // This ensures the contract name matches the uploaded file name
            analysisResult.title = path.basename(
              file.originalname,
              path.extname(file.originalname)
            );

            // Create contract from analysis result
            logger.info(
              `Creating contract from analysis result for ${file.originalname}`
            );
            const contract = await this.createContractFromAnalysis(
              analysisResult,
              file.originalname,
              tenantId,
              userId,
              file.buffer
            );

            logger.info(
              `Successfully imported contract: ${contract.title} (ID: ${contract.id})`
            );

            return {
              success: true,
              contract: {
                id: contract.id,
                title: contract.title,
                status: contract.status.toString(),
                metadata: analysisResult,
              },
            };
          } catch (error) {
            // Return error information for this file
            logger.error(
              `Failed to import contract from ${file.originalname}:`,
              error
            );

            // Create error notification for failed contract upload
            try {
              await this.notificationService.createContractNotification({
                title: "Contract Upload Failed",
                content: `Failed to upload contract "${file.originalname}": ${(error as Error).message}`,
                userId: userId,
                tenantId: tenantId,
                contractId: "", // No contract ID since creation failed
                priority: NotificationPriority.HIGH,
                actionUrl: `/repository`,
                metadata: {
                  fileName: file.originalname,
                  errorMessage: (error as Error).message,
                  uploadDate: new Date().toISOString(),
                },
              });
            } catch (notificationError) {
              logger.error("Failed to create contract upload error notification:", notificationError);
            }

            return {
              success: false,
              error: {
                file: file.originalname,
                error: (error as Error).message,
                data: {
                  errorType:
                    error instanceof Error ? error.constructor.name : "Unknown",
                  errorStack: (error as Error).stack,
                  fileName: file.originalname,
                  fileSize: file.buffer.length,
                  fileType: path.extname(file.originalname),
                },
              },
            };
          }
        });

        // Wait for all files in the batch to complete
        const batchResults = await Promise.allSettled(batchPromises);

        // Process batch results
        batchResults.forEach((batchResult, index) => {
          if (batchResult.status === "fulfilled") {
            const fileResult = batchResult.value;
            if (fileResult.success && fileResult.contract) {
              result.successful++;
              result.contracts.push(fileResult.contract);

              // Update job progress if tracking
              if (jobId) {
                importJobService.addJobContract(jobId, fileResult.contract);
              }
            } else if (!fileResult.success && fileResult.error) {
              result.failed++;
              result.errors.push(fileResult.error);

              // Update job progress if tracking
              if (jobId) {
                importJobService.addJobError(jobId, fileResult.error);
              }
            }
          } else {
            // Handle promise rejection
            const file = batch[index];
            const error = {
              file: file.originalname,
              error: `Promise rejected: ${batchResult.reason?.message || "Unknown error"
                }`,
              data: {
                errorType: "PromiseRejection",
                fileName: file.originalname,
                fileSize: file.buffer.length,
                fileType: path.extname(file.originalname),
              },
            };

            logger.error(
              `Promise rejected for file ${file.originalname}:`,
              batchResult.reason
            );
            result.failed++;
            result.errors.push(error);

            // Update job progress if tracking
            if (jobId) {
              importJobService.addJobError(jobId, error);
            }
          }
        });

        // Update job progress after each batch
        if (jobId) {
          const processedFiles = (batchIndex + 1) * concurrencyLimit;
          importJobService.updateJobProgress(
            jobId,
            Math.min(processedFiles, filesToProcess.length),
            result.successful,
            result.failed
          );
        }

        logger.info(`Batch ${batchIndex + 1}/${batches.length} completed`);
      }

      return result;
    } catch (error) {
      logger.error("Error importing contracts from documents:", error);
      throw new Error(
        `Failed to import contracts from documents: ${(error as Error).message}`
      );
    }
  }

  /**
   * Creates batches of files for parallel processing
   * @param files Array of files to process
   * @param batchSize Size of each batch
   * @returns Array of file batches
   */
  private createBatches<T>(files: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < files.length; i += batchSize) {
      batches.push(files.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Creates a contract from AI analysis result
   * @param analysis Analysis result
   * @param fileName Original file name
   * @param tenantId Tenant ID
   * @param userId User ID for notifications
   * @returns Created contract
   */
  private async createContractFromAnalysis(
    analysis: ContractDocumentAnalysisResult,
    fileName: string,
    tenantId: string,
    userId: string,
    fileBuffer?: Buffer
  ) {
    // Extract contract data from analysis
    // Always use the filename as the contract title for consistency
    const title = path.basename(fileName, path.extname(fileName));

    // Parse dates with improved handling
    let startDate: Date | undefined;
    let endDate: Date | undefined;
    let renewalDate: Date | undefined;
    let effectiveDate: Date | undefined;
    let executionDate: Date | undefined;

    // Helper function to safely parse dates
    const safelyParseDate = (dateStr?: string): Date | undefined => {
      if (!dateStr) return undefined;

      try {
        // Try standard date parsing
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) return date;

        // Try different date formats
        const formats = [
          // ISO format
          /(\d{4})-(\d{2})-(\d{2})/,
          // MM/DD/YYYY
          /(\d{1,2})\/(\d{1,2})\/(\d{4})/,
          // DD/MM/YYYY
          /(\d{1,2})\/(\d{1,2})\/(\d{4})/,
          // Month DD, YYYY
          /([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})/,
        ];

        for (const format of formats) {
          const match = dateStr.match(format);
          if (match) {
            const parsedDate = new Date(dateStr);
            if (!isNaN(parsedDate.getTime())) return parsedDate;
          }
        }
      } catch (e) {
        logger.warn(`Failed to parse date: ${dateStr}`, e);
      }

      return undefined;
    };

    // Parse all dates
    if (analysis.dates) {
      startDate = safelyParseDate(analysis.dates.startDate);
      endDate = safelyParseDate(analysis.dates.endDate);
      renewalDate = safelyParseDate(analysis.dates.renewalDate);
      effectiveDate = safelyParseDate(analysis.dates.effectiveDate);
      executionDate = safelyParseDate(analysis.dates.executionDate);
    }

    // Determine appropriate status based on dates
    let status: ContractStatus = ContractStatus.DRAFT; // Default
    if (startDate && endDate) {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(0, 0, 0, 0);

      if (currentDate < start) {
        status = ContractStatus.PENDING_APPROVAL; // Future contract
      } else if (currentDate > end) {
        status = ContractStatus.EXPIRED; // Past contract
      } else {
        status = ContractStatus.ACTIVE; // Current contract
      }
    }

    // Determine security classification
    const securityClassification = this.determineSecurityClassification(
      analysis.confidentiality
    );

    // Extract counterparty with improved logic
    let counterparty = "";
    if (analysis.parties && analysis.parties.length > 0) {
      // Find the first party that's not the company
      const counterpartyObj = analysis.parties.find(
        (p) =>
          p.role &&
          !p.role.toLowerCase().includes("company") &&
          !p.role.toLowerCase().includes("client") &&
          !p.role.toLowerCase().includes("customer") &&
          !p.role.toLowerCase().includes("provider") &&
          !p.role.toLowerCase().includes("vendor")
      );

      if (counterpartyObj) {
        counterparty = counterpartyObj.name;
      } else if (analysis.parties.length > 1) {
        // If we couldn't identify a clear counterparty but have multiple parties,
        // use the second party as the counterparty
        counterparty = analysis.parties[1].name;
      } else if (analysis.parties.length === 1) {
        // If there's only one party, use it
        counterparty = analysis.parties[0].name;
      }
    }

    // Extract contract value with improved handling - support new combined format
    let contractValue = "";
    if (analysis.value) {
      if (typeof analysis.value === "string") {
        // New format: "CURRENCY:AMOUNT"
        contractValue = analysis.value;
      } else if (analysis.value.amount) {
        // Old format: create combined format from separate fields
        const currency =
          this.sanitizeCurrency(analysis.value.currency) || "USD";
        const amount = this.extractNumericValue(analysis.value.amount);
        if (amount !== undefined) {
          contractValue = `${currency}:${amount}`;
        } else {
          contractValue = analysis.value.amount.toString();
        }
      } else if (analysis.value.totalValue) {
        // Old format: create combined format from totalValue
        const currency =
          this.sanitizeCurrency(analysis.value.currency) || "USD";
        const amount = this.extractNumericValue(analysis.value.totalValue);
        if (amount !== undefined) {
          contractValue = `${currency}:${amount}`;
        } else {
          contractValue = analysis.value.totalValue.toString();
        }
      }
    }

    // Map classification and agreement type from AI analysis
    const classification = this.mapContractClassification(
      analysis.classification
    );
    const agreementType =
      this.mapAgreementType(analysis.agreementType) || AgreementType.OTHER;

    // Extract provider name using comprehensive logic
    const provider = this.extractProviderFromAnalysis(analysis);

    // Extract contract number/ID from AI analysis
    const contractNumber = this.extractContractNumberFromAnalysis(analysis);

    // Create contract with enhanced metadata
    const contract = await this.contractService.createContract({
      title,
      description: `Imported from ${fileName}`,
      contractNumber, // Use extracted contract number instead of empty string
      classification,
      agreementType,
      status,
      startDate,
      endDate,
      renewalType: analysis.renewalTerms?.renewalPeriod || "",
      renewalDate,
      isAutoRenew: analysis.renewalTerms?.isAutoRenew || false,
      noticePeriodDays: analysis.renewalTerms?.noticePeriodDays,
      securityClassification,
      isEncrypted: securityClassification !== SecurityClassification.PUBLIC,
      counterparty,
      value: contractValue,
      provider, // Add provider field to contract
      tenantId,
    } as any);

    // Create ContractVersion first (required for OCR processing)
    if (fileBuffer) {
      try {
        logger.info(`Storing document for contract ${contract.id}`);

        // Get document format from file name
        const aiDocumentFormat = this.getDocumentFormatFromFileName(fileName);

        // Convert AI DocumentFormat to Prisma enum
        // This is needed because they are different enums
        let prismaDocFormat;
        if (aiDocumentFormat === DocumentFormat.PDF) {
          prismaDocFormat = "PDF";
        } else if (aiDocumentFormat === DocumentFormat.DOCX) {
          prismaDocFormat = "DOCX";
        } else {
          prismaDocFormat = "OTHER";
        }

        await this.contractService.createContractVersion({
          contractId: contract.id,
          versionNumber: 1,
          documentName: fileName,
          documentFormat: prismaDocFormat as any, // Cast to any to bypass type checking
          documentBuffer: fileBuffer,
          versionComment: "Initial document import",
        });
        logger.info(`Stored document for contract ${contract.id}`);
      } catch (error) {
        logger.error(
          `Error storing document for contract ${contract.id}:`,
          error
        );
        // Continue even if document storage fails
      }
    }

    // Create comprehensive metadata
    if (Object.keys(analysis).length > 0) {
      // Extract confidence scores from analysis if available
      const confidenceScores = (analysis as any).confidence_scores || {};

      // Generate three-tier extraction for the contract (now that ContractVersion exists)
      try {
        logger.info(
          `Starting three-tier extraction for contract: ${contract.title}`
        );

        await this.contractAIService.extractAndSaveThreeTierData(
          fileBuffer!,
          fileName,
          contract.id,
          tenantId
        );

        logger.info(
          `Successfully generated three-tier extraction for contract: ${contract.title}`
        );

        // Note: Folder assignment removed - folders are now calculated at runtime based on provider
      } catch (error) {
        logger.error(
          "Failed to generate three-tier extraction during import:",
          {
            contractTitle: contract.title,
            fileName,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          }
        );
        // Continue without three-tier extraction - it's not critical for import
      }

      // Prepare metadata with all extracted information in flattened structure
      const metadataToStore: any = {
        contractId: contract.id,
        effectiveDate,
        executionDate,
        totalValue: this.extractTotalValueFromCombinedFormat(contractValue),
        currency: this.extractCurrencyFromCombinedFormat(contractValue),
        paymentTerms:
          typeof analysis.paymentTerms === "object"
            ? JSON.stringify(analysis.paymentTerms)
            : analysis.paymentTerms,
        autoExtractedFields: analysis,
        // Flatten all metadata fields to root level (removing customMetadata nesting)
        governingLaw: analysis.governingLaw,
        disputeResolution: analysis.disputeResolution,
        liabilityLimits: analysis.liabilityLimits,
        warranties: analysis.warranties
          ? Array.isArray(analysis.warranties)
            ? analysis.warranties.join("; ")
            : analysis.warranties
          : undefined,
        amendments: analysis.amendments,
        signatures: analysis.signatures
          ? JSON.stringify(analysis.signatures)
          : undefined,
        // Include original filename and import metadata
        originalFileName: fileName,
        importDate: new Date().toISOString(),
        // Add confidence scores at root level
        confidence_scores: confidenceScores,
      };

      // Add Oracle-specific fields at root level if this is an Oracle contract
      const isOracleContract = this.isOracleContract(analysis as any);
      if (isOracleContract) {
        // Extract Oracle fields from analysis and add them to root level
        const oracleFields = [
          "publisher",
          "reseller",
          "entitled_entity",
          "entitled_entity_country",
          "product_name",
          "raw_product_name",
          "total_quantity",
          "metric",
          "metric_definition",
          "term",
          "level",
          "limitations",
          "included_rights",
          "csi",
          "purchase_date",
          "governing_agreement",
          "support_contract_number",
          "support_start_date",
          "support_end_date",
          "original_document_name",
          "document_type",
          "license_value",
          "license_value_per_unit",
          "contractual_support_value",
          "support_value_per_year",
          "support_value_per_year_per_unit",
          "oracle_currency",
          "index_field",
          "delta",
        ];

        // Create nested oracleFields object
        const oracleFieldsData: Record<string, any> = {};
        oracleFields.forEach((field) => {
          if ((analysis as any)[field] !== undefined) {
            oracleFieldsData[field] = (analysis as any)[field];
          }
        });

        // Store Oracle fields in nested structure
        if (Object.keys(oracleFieldsData).length > 0) {
          metadataToStore.oracleFields = oracleFieldsData;
        }
      }

      // Store the metadata
      await this.contractService.createOrUpdateContractMetadata(
        metadataToStore
      );

      // Mark assessment as completed since we have AI-extracted data
      try {
        const prisma = new PrismaClient();
        await prisma.contract.update({
          where: { id: contract.id },
          data: { assessmentCompleted: true },
        });
        await prisma.$disconnect();
      } catch (prismaError) {
        logger.error(
          `Error updating assessment status for contract ${contract.id}:`,
          prismaError
        );
        // Continue even if this update fails
      }
    }

    // ContractVersion already created earlier (before OCR processing)

    // Create success notification for contract upload
    try {
      await this.notificationService.createContractNotification({
        title: "Contract Upload Successful",
        content: `Contract "${contract.title}" has been successfully uploaded and processed.`,
        userId: userId,
        tenantId: tenantId,
        contractId: contract.id,
        priority: NotificationPriority.MEDIUM,
        actionUrl: `/contract-management/contracts`,
        metadata: {
          contractTitle: contract.title,
          fileName: fileName,
          uploadDate: new Date().toISOString(),
        },
      });
    } catch (notificationError) {
      logger.error("Failed to create contract upload notification:", notificationError);
      // Don't fail the contract creation if notification fails
    }

    return contract;
  }

  /**
   * Extracts numeric value from a string
   * @param valueString Value string that may contain currency symbols, commas, etc.
   * @returns Prisma Decimal value or undefined if not a valid number
   */
  private extractNumericValue(valueString?: string): Decimal | undefined {
    if (!valueString) return undefined;

    // Remove currency symbols, commas, spaces, and other non-numeric characters
    // Keep decimal points and negative signs
    const cleanedValue = valueString.replace(/[^0-9.-]/g, "");

    // Parse as float
    const numericValue = parseFloat(cleanedValue);

    // Return undefined if not a valid number
    if (isNaN(numericValue)) return undefined;

    // Convert to Prisma Decimal type
    try {
      return new Decimal(numericValue);
    } catch (error) {
      logger.error(`Error converting value to Decimal: ${valueString}`, error);
      return undefined;
    }
  }

  /**
   * Determines security classification from confidentiality string
   * @param confidentiality Confidentiality string from AI
   * @returns Security classification enum
   */
  private determineSecurityClassification(
    confidentiality?: string
  ): SecurityClassification {
    if (!confidentiality) return SecurityClassification.CONFIDENTIAL;

    const confUpper = confidentiality.toUpperCase();

    if (confUpper.includes("PUBLIC")) return SecurityClassification.PUBLIC;
    if (confUpper.includes("INTERNAL")) return SecurityClassification.INTERNAL;
    if (confUpper.includes("CONFIDENTIAL"))
      return SecurityClassification.CONFIDENTIAL;
    if (
      confUpper.includes("RESTRICTED") ||
      confUpper.includes("HIGHLY CONFIDENTIAL")
    )
      return SecurityClassification.RESTRICTED;

    return SecurityClassification.CONFIDENTIAL;
  }

  /**
   * Processes import records and creates contracts
   * @param records Records to process
   * @param tenantId Tenant ID
   * @returns Import result
   */
  private async processImportRecords(
    records: any[],
    tenantId: string
  ): Promise<ContractImportResult> {
    const result: ContractImportResult = {
      totalProcessed: records.length,
      successful: 0,
      failed: 0,
      errors: [],
      contracts: [],
    };

    // Process each record
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const rowNumber = i + 2; // +2 because of header row and 0-indexing

      try {
        // Validate required fields
        if (!record.title) {
          throw new Error("Title is required");
        }

        // Map agreement type
        const agreementType =
          this.mapAgreementType(record.agreementType) || AgreementType.OTHER;

        // Map security classification
        let securityClassification: SecurityClassification;
        try {
          securityClassification = this.mapSecurityClassification(
            record.securityClassification
          );
        } catch (error) {
          throw new Error(
            `Invalid security classification: ${record.securityClassification}`
          );
        }

        // Map status
        let status: ContractStatus;
        try {
          status = this.mapContractStatus(record.status);
        } catch (error) {
          throw new Error(`Invalid status: ${record.status}`);
        }

        // Parse dates
        let startDate: Date | undefined;
        let endDate: Date | undefined;
        let renewalDate: Date | undefined;

        if (record.startDate) {
          startDate = new Date(record.startDate);
          if (isNaN(startDate.getTime())) {
            throw new Error(`Invalid start date: ${record.startDate}`);
          }
        }

        if (record.endDate) {
          endDate = new Date(record.endDate);
          if (isNaN(endDate.getTime())) {
            throw new Error(`Invalid end date: ${record.endDate}`);
          }
        }

        if (record.renewalDate) {
          renewalDate = new Date(record.renewalDate);
          if (isNaN(renewalDate.getTime())) {
            throw new Error(`Invalid renewal date: ${record.renewalDate}`);
          }
        }

        // Create contract
        const contract = await this.contractService.createContract({
          // Remove the id field as it's generated by the service
          title: record.title,
          description: record.description || "",
          contractNumber: record.contractNumber || "",
          agreementType,
          status,
          startDate,
          endDate,
          renewalType: record.renewalType || "",
          renewalDate,
          isAutoRenew:
            record.isAutoRenew === "true" || record.isAutoRenew === true,
          noticePeriodDays: record.noticePeriodDays
            ? parseInt(record.noticePeriodDays)
            : undefined,
          securityClassification,
          isEncrypted:
            record.isEncrypted === "true" || record.isEncrypted === true,
          counterparty: record.counterparty || "",
          value: record.value || "",
          tenantId,
        });

        // Add to successful contracts
        result.successful++;
        result.contracts.push({
          id: contract.id,
          title: contract.title,
          status: contract.status,
        });
      } catch (error) {
        // Add to failed contracts
        result.failed++;
        result.errors.push({
          row: rowNumber,
          error: (error as Error).message,
          data: record,
        });
      }
    }

    return result;
  }

  /**
   * Maps security classification string to enum
   * @param classification Security classification string
   * @returns Security classification enum
   */
  private mapSecurityClassification(
    classification: string
  ): SecurityClassification {
    const classUpper = (classification || "").toUpperCase();

    switch (classUpper) {
      case "PUBLIC":
        return SecurityClassification.PUBLIC;
      case "INTERNAL":
        return SecurityClassification.INTERNAL;
      case "CONFIDENTIAL":
        return SecurityClassification.CONFIDENTIAL;
      case "RESTRICTED":
        return SecurityClassification.RESTRICTED;
      case "HIGHLY_RESTRICTED":
        return SecurityClassification.RESTRICTED; // Map to RESTRICTED as per our fix
      default:
        return SecurityClassification.CONFIDENTIAL; // Default to CONFIDENTIAL
    }
  }

  /**
   * Maps contract status string to enum
   * @param status Contract status string
   * @returns Contract status enum
   */
  private mapContractStatus(status: string): ContractStatus {
    const statusUpper = (status || "").toUpperCase();

    switch (statusUpper) {
      case "DRAFT":
        return ContractStatus.DRAFT;
      case "REVIEW":
        return ContractStatus.REVIEW;
      case "PENDING":
      case "PENDING_APPROVAL":
        return ContractStatus.PENDING_APPROVAL;
      case "APPROVED":
        return ContractStatus.APPROVED;
      case "ACTIVE":
        return ContractStatus.ACTIVE;
      case "EXPIRING":
        return ContractStatus.EXPIRING;
      case "EXPIRED":
        return ContractStatus.EXPIRED;
      case "TERMINATED":
        return ContractStatus.TERMINATED;
      case "RENEWED":
        return ContractStatus.RENEWED;
      default:
        return ContractStatus.DRAFT; // Default to DRAFT
    }
  }

  /**
   * Extracts currency from combined format "CURRENCY:AMOUNT"
   * @param combinedValue Combined value string
   * @returns Currency code or undefined
   */
  private extractCurrencyFromCombinedFormat(
    combinedValue?: string
  ): string | undefined {
    if (!combinedValue || !combinedValue.includes(":")) {
      return undefined;
    }

    const [currency] = combinedValue.split(":");
    return this.sanitizeCurrency(currency) || undefined;
  }

  /**
   * Extracts total value from combined format "CURRENCY:AMOUNT"
   * @param combinedValue Combined value string
   * @returns Decimal value or undefined
   */
  private extractTotalValueFromCombinedFormat(
    combinedValue?: string
  ): Decimal | undefined {
    if (!combinedValue) {
      return undefined;
    }

    if (combinedValue.includes(":")) {
      // New format: "CURRENCY:AMOUNT"
      const [, amount] = combinedValue.split(":");
      return this.extractNumericValue(amount);
    } else {
      // Old format: just the amount
      return this.extractNumericValue(combinedValue);
    }
  }

  /**
   * Extracts provider name from AI analysis result using comprehensive logic
   * @param analysis AI analysis result
   * @returns Provider name or empty string if not found
   */
  private extractProviderFromAnalysis(analysis: any): string {
    if (!analysis) return "";

    // First check top-level fields
    let providerName =
      analysis.providerName ||
      analysis.provider ||
      analysis.publisher ||
      analysis.counterparty ||
      "";

    // If not found at top level, check nested structures
    if (!providerName || providerName === "N/A") {
      // Check parties array for provider role
      if (analysis.parties && Array.isArray(analysis.parties)) {
        const providerParty = analysis.parties.find(
          (party: any) => party.role && party.role.toLowerCase() === "provider"
        );
        if (providerParty?.name && providerParty.name !== "N/A") {
          providerName = providerParty.name;
        }
      }
    }

    return providerName && providerName !== "N/A" ? providerName : "";
  }

  /**
   * Gets the ContractAIService instance for external use
   * @returns ContractAIService instance
   */
  getContractAIService(): ContractAIService {
    return this.contractAIService;
  }

  /**
   * Sanitizes currency code to ensure it's valid for ISO 4217 format
   * @param currency Raw currency code from AI analysis
   * @returns Valid currency code or null
   */
  private sanitizeCurrency(currency?: string): string | null {
    if (!currency) {
      return null;
    }

    // Convert to uppercase and trim
    const cleanCurrency = currency.toUpperCase().trim();

    // Handle common AI responses that aren't valid currency codes
    if (
      cleanCurrency === "NOT SPECIFIED" ||
      cleanCurrency === "UNKNOWN" ||
      cleanCurrency === "N/A" ||
      cleanCurrency === "NULL" ||
      cleanCurrency === "UNDEFINED" ||
      cleanCurrency === ""
    ) {
      return null;
    }

    // Map common currency names/symbols to ISO codes
    const currencyMap: Record<string, string> = {
      DOLLAR: "USD",
      DOLLARS: "USD",
      "US DOLLAR": "USD",
      "US DOLLARS": "USD",
      "AMERICAN DOLLAR": "USD",
      $: "USD",
      EURO: "EUR",
      EUROS: "EUR",
      "€": "EUR",
      POUND: "GBP",
      POUNDS: "GBP",
      "BRITISH POUND": "GBP",
      "£": "GBP",
      YEN: "JPY",
      "JAPANESE YEN": "JPY",
      "¥": "JPY",
      "CANADIAN DOLLAR": "CAD",
      "CANADIAN DOLLARS": "CAD",
      "AUSTRALIAN DOLLAR": "AUD",
      "AUSTRALIAN DOLLARS": "AUD",
      "SWISS FRANC": "CHF",
      "SWISS FRANCS": "CHF",
      YUAN: "CNY",
      "CHINESE YUAN": "CNY",
      RUPEE: "INR",
      "INDIAN RUPEE": "INR",
      RUPEES: "INR",
    };

    // Check if it's a mapped currency name
    if (currencyMap[cleanCurrency]) {
      return currencyMap[cleanCurrency];
    }

    // Validate ISO 4217 format (3 uppercase letters)
    if (/^[A-Z]{3}$/.test(cleanCurrency)) {
      return cleanCurrency;
    }

    // If we can't determine a valid currency, return null
    logger.warn(
      `Invalid currency code from AI analysis: ${currency}, defaulting to null`
    );
    return null;
  }

  /**
   * Maps contract classification string to enum
   * @param classification Contract classification string
   * @returns Contract classification enum or undefined
   */
  private mapContractClassification(
    classification?: string
  ): ContractClassification | undefined {
    if (!classification) {
      return undefined;
    }

    const classUpper = classification.toUpperCase().trim();

    switch (classUpper) {
      case "SW_SAAS":
      case "SOFTWARE":
      case "SAAS":
      case "SOFTWARE_AS_A_SERVICE":
        return ContractClassification.SW_SAAS;
      case "IAAS":
      case "INFRASTRUCTURE_AS_A_SERVICE":
      case "INFRASTRUCTURE":
        return ContractClassification.IAAS;
      case "PAAS":
      case "PLATFORM_AS_A_SERVICE":
      case "PLATFORM":
        return ContractClassification.PAAS;
      case "PROFESSIONAL_SERVICES":
      case "CONSULTING":
      case "IMPLEMENTATION":
        return ContractClassification.PROFESSIONAL_SERVICES;
      case "MANAGED_SERVICES":
      case "OUTSOURCED":
      case "MANAGED":
        return ContractClassification.MANAGED_SERVICES;
      case "HARDWARE":
      case "EQUIPMENT":
      case "PHYSICAL":
        return ContractClassification.HARDWARE;
      case "RESELLER":
      case "RESELLER_SERVICES":
      case "VALUE_ADDED_RESELLER":
        return ContractClassification.RESELLER;
      case "NETWORK":
      case "NETWORKING":
      case "TELECOM":
        return ContractClassification.NETWORK;
      case "OTHER":
      default:
        return ContractClassification.OTHER;
    }
  }

  /**
   * Maps agreement type string to enum
   * @param agreementType Agreement type string
   * @returns Agreement type enum or undefined
   */
  private mapAgreementType(agreementType?: string): AgreementType | undefined {
    if (!agreementType) {
      return undefined;
    }

    const typeUpper = agreementType.toUpperCase().trim();

    switch (typeUpper) {
      case "MSA":
      case "MASTER_SERVICES_AGREEMENT":
        return AgreementType.MSA;
      case "NDA":
      case "NON_DISCLOSURE_AGREEMENT":
        return AgreementType.NDA;
      case "SOW":
      case "STATEMENT_OF_WORK":
        return AgreementType.SOW;
      case "PO":
      case "PURCHASE_ORDER":
        return AgreementType.PO;
      case "SLA":
      case "SERVICE_LEVEL_AGREEMENT":
        return AgreementType.SLA;
      case "DPA":
      case "DATA_PROCESSING_AGREEMENT":
        return AgreementType.DPA;
      case "BAA":
      case "BUSINESS_ASSOCIATE_AGREEMENT":
        return AgreementType.BAA;
      case "EULA":
      case "END_USER_LICENSE_AGREEMENT":
        return AgreementType.EULA;
      case "LOI":
      case "LETTER_OF_INTENT":
        return AgreementType.LOI;
      case "MOA":
      case "MEMORANDUM_OF_AGREEMENT":
        return AgreementType.MOA;
      case "MOU":
      case "MEMORANDUM_OF_UNDERSTANDING":
        return AgreementType.MOU;
      case "JV":
      case "JOINT_VENTURE_AGREEMENT":
        return AgreementType.JV;
      case "CA":
      case "CONFIDENTIALITY_AGREEMENT":
        return AgreementType.CA;
      case "LPA":
      case "LIMITED_PARTNERSHIP_AGREEMENT":
        return AgreementType.LPA;
      case "SSA":
      case "SOFTWARE_SUBSCRIPTION_AGREEMENT":
        return AgreementType.SSA;
      case "ESA":
      case "EQUIPMENT_SUPPLY_AGREEMENT":
        return AgreementType.ESA;
      case "PSA":
      case "PROFESSIONAL_SERVICES_AGREEMENT":
        return AgreementType.PSA;
      case "TOS":
      case "TERMS_OF_SERVICE":
        return AgreementType.TOS;
      case "DUA":
      case "DATA_USE_AGREEMENT":
        return AgreementType.DUA;
      case "OEM":
      case "ORIGINAL_EQUIPMENT_MANUFACTURER_AGREEMENT":
        return AgreementType.OEM;
      case "RFP":
      case "REQUEST_FOR_PROPOSAL":
        return AgreementType.RFP;
      case "RFQ":
      case "REQUEST_FOR_QUOTATION":
        return AgreementType.RFQ;
      case "BPA":
      case "BLANKET_PURCHASE_AGREEMENT":
        return AgreementType.BPA;
      case "PPA":
      case "POWER_PURCHASE_AGREEMENT":
        return AgreementType.PPA;
      case "LSA":
      case "LICENSE_AND_SERVICES_AGREEMENT":
        return AgreementType.LSA;
      case "ISA":
      case "INDEPENDENT_SOFTWARE_VENDOR_AGREEMENT":
        return AgreementType.ISA;
      case "SPA":
      case "SHARE_PURCHASE_AGREEMENT":
        return AgreementType.SPA;
      case "APA":
      case "ASSET_PURCHASE_AGREEMENT":
        return AgreementType.APA;
      case "TPA":
      case "THIRD_PARTY_AGREEMENT":
        return AgreementType.TPA;
      case "IP":
      case "INTELLECTUAL_PROPERTY_ASSIGNMENT_AGREEMENT":
        return AgreementType.IP;
      case "RSA":
      case "RESELLER_AGREEMENT":
        return AgreementType.RSA;
      case "VARA":
      case "VALUE_ADDED_RESELLER_AGREEMENT":
        return AgreementType.VARA;
      case "DDA":
      case "DATA_DISCLOSURE_AGREEMENT":
        return AgreementType.DDA;
      case "TSA":
      case "TRANSITION_SERVICES_AGREEMENT":
        return AgreementType.TSA;
      case "IA":
      case "INTERCONNECTION_AGREEMENT":
        return AgreementType.IA;
      case "OTHER":
      default:
        return AgreementType.OTHER;
    }
  }

  /**
   * Determines if a contract is an Oracle contract based on provider information
   * @param analysis - Contract analysis data
   * @returns True if this is an Oracle contract
   */
  private isOracleContract(analysis: any): boolean {
    const provider = analysis.provider || analysis.publisher || "";
    const providerLower = provider.toLowerCase();

    return (
      providerLower.includes("oracle corporation") ||
      providerLower.includes("oracle america") ||
      providerLower.includes("oracle international") ||
      providerLower.includes("oracle israel") ||
      providerLower === "oracle" ||
      (providerLower.includes("oracle") && providerLower.includes("inc")) ||
      (providerLower.includes("oracle") && providerLower.includes("ltd"))
    );
  }

  /**
   * Extracts contract number/ID from AI analysis result
   * @param analysis - Contract analysis data
   * @returns Contract number/ID or null if not found
   */
  private extractContractNumberFromAnalysis(analysis: any): string | null {
    // Helper function to check if a value is meaningful
    const isMeaningfulValue = (value: any): boolean => {
      if (value === null || value === undefined) return false;
      if (typeof value === "string") {
        const trimmed = value.trim().toLowerCase();
        return (
          trimmed !== "" &&
          trimmed !== "n/a" &&
          trimmed !== "unknown" &&
          trimmed !== "not specified" &&
          trimmed !== "null" &&
          trimmed !== "undefined" &&
          trimmed !== "not available" &&
          trimmed !== "none"
        );
      }
      return true;
    };

    // First, check for contract_id in the raw result (from the AI analysis)
    const rawData = analysis.rawResult || analysis;

    if (isMeaningfulValue(rawData.contract_id)) {
      return rawData.contract_id.trim();
    }

    // Fallback: check for contract_id in the main analysis object
    if (isMeaningfulValue(analysis.contract_id)) {
      return analysis.contract_id.trim();
    }

    // Additional fallback: check for Oracle-specific contract numbers
    if (rawData.oracle_fields) {
      if (isMeaningfulValue(rawData.oracle_fields.support_contract_number)) {
        return rawData.oracle_fields.support_contract_number.trim();
      }
      if (isMeaningfulValue(rawData.oracle_fields.governing_agreement)) {
        return rawData.oracle_fields.governing_agreement.trim();
      }
    }

    // Final fallback: check for support_contract_number at root level
    if (isMeaningfulValue(rawData.support_contract_number)) {
      return rawData.support_contract_number.trim();
    }

    // Return null if no meaningful contract number found
    return null;
  }
}
