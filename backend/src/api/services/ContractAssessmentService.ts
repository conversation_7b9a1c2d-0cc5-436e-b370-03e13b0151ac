/**
 * Contract Assessment Service
 * Handles business logic for contract assessments
 */

import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Contract assessment data interface
 */
export interface ContractAssessmentData {
  contractId: string;
  tenantId: string;
  annualContractValue?: number;
  totalContractValue?: number;
  currency?: string;
  terminationForConvenience?: boolean;
  terminationNoticeDays?: number;
  autoRenewal?: boolean;
  licenseItems?: Array<{
    name: string;
    quantity: number;
    unitPrice: number;
    discount?: number;
  }>;
  geographicLimitations?: string;
  customerDefinition?: string;
  consumptionReporting?: boolean;
  auditRequirements?: string;
  activeUsersPercentage?: number;
  featureUtilization?: number;
  usageFrequency?: string;
  volumeChangeForecast?: string;
  additionalProducts?: string;
  redundantProducts?: string;
  downgradePotential?: boolean;
  preferredContractLength?: string;
  paymentFlexibility?: boolean;
  vendorSwitchWillingness?: boolean;
  satisfactionRating?: number;
  impactRating?: number;
  isNicheOffering?: boolean;
}

/**
 * Contract Assessment Service
 */
export class ContractAssessmentService {
  private prisma: PrismaClient;

  /**
   * Constructor
   * @param prisma Prisma client
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Creates a new contract assessment
   * @param data Assessment data
   * @returns Created assessment
   */
  async createAssessment(data: ContractAssessmentData) {
    try {
      // Check if contract exists and belongs to tenant
      const contract = await this.prisma.contract.findFirst({
        where: {
          id: data.contractId,
          tenantId: data.tenantId,
        },
      });

      if (!contract) {
        throw new Error(`Contract not found: ${data.contractId}`);
      }

      // Check if assessment already exists
      const existingAssessment =
        await this.prisma.contractAssessment.findUnique({
          where: {
            contractId: data.contractId,
          },
        });

      if (existingAssessment) {
        // Update existing assessment and mark contract as having completed assessment
        // Update the contract to mark assessment as completed
        await this.prisma.$executeRaw`
          UPDATE "Contract"
          SET "assessmentCompleted" = true
          WHERE "id" = ${data.contractId}
        `;

        return await this.prisma.contractAssessment.update({
          where: {
            contractId: data.contractId,
          },
          data: {
            annualContractValue: data.annualContractValue
              ? parseFloat(data.annualContractValue.toString())
              : null,
            totalContractValue: data.totalContractValue
              ? parseFloat(data.totalContractValue.toString())
              : null,
            currency: data.currency,
            terminationForConvenience: data.terminationForConvenience,
            terminationNoticeDays: data.terminationNoticeDays,
            autoRenewal: data.autoRenewal,
            licenseItems: data.licenseItems
              ? JSON.stringify(data.licenseItems)
              : undefined,
            geographicLimitations: data.geographicLimitations,
            customerDefinition: data.customerDefinition,
            consumptionReporting: data.consumptionReporting,
            auditRequirements: data.auditRequirements,
            activeUsersPercentage: data.activeUsersPercentage,
            featureUtilization: data.featureUtilization,
            usageFrequency: data.usageFrequency,
            volumeChangeForecast: data.volumeChangeForecast,
            additionalProducts: data.additionalProducts,
            redundantProducts: data.redundantProducts,
            downgradePotential: data.downgradePotential,
            preferredContractLength: data.preferredContractLength,
            paymentFlexibility: data.paymentFlexibility?.toString(),
            vendorSwitchWillingness: data.vendorSwitchWillingness?.toString(),
            satisfactionRating: data.satisfactionRating,
            impactRating: data.impactRating,
            isNicheOffering: data.isNicheOffering,
            completedAt: new Date(),
            updatedAt: new Date(),
          },
        });
      }

      // Update contract to mark assessment as completed
      await this.prisma.$executeRaw`
        UPDATE "Contract"
        SET "assessmentCompleted" = true
        WHERE "id" = ${data.contractId}
      `;

      // Create new assessment
      return await this.prisma.contractAssessment.create({
        data: {
          id: uuidv4(),
          contractId: data.contractId,
          tenantId: data.tenantId,
          annualContractValue: data.annualContractValue
            ? parseFloat(data.annualContractValue.toString())
            : null,
          totalContractValue: data.totalContractValue
            ? parseFloat(data.totalContractValue.toString())
            : null,
          currency: data.currency,
          terminationForConvenience: data.terminationForConvenience,
          terminationNoticeDays: data.terminationNoticeDays,
          autoRenewal: data.autoRenewal,
          licenseItems: data.licenseItems
            ? JSON.stringify(data.licenseItems)
            : undefined,
          geographicLimitations: data.geographicLimitations,
          customerDefinition: data.customerDefinition,
          consumptionReporting: data.consumptionReporting,
          auditRequirements: data.auditRequirements,
          activeUsersPercentage: data.activeUsersPercentage,
          featureUtilization: data.featureUtilization,
          usageFrequency: data.usageFrequency,
          volumeChangeForecast: data.volumeChangeForecast,
          additionalProducts: data.additionalProducts,
          redundantProducts: data.redundantProducts,
          downgradePotential: data.downgradePotential,
          preferredContractLength: data.preferredContractLength,
          paymentFlexibility: data.paymentFlexibility?.toString(),
          vendorSwitchWillingness: data.vendorSwitchWillingness?.toString(),
          satisfactionRating: data.satisfactionRating,
          impactRating: data.impactRating,
          isNicheOffering: data.isNicheOffering,
          completedAt: new Date(),
        },
      });
    } catch (error) {
      logger.error("Error creating contract assessment:", error);
      throw new Error(
        `Failed to create contract assessment: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets a contract assessment by contract ID
   * @param contractId Contract ID
   * @param tenantId Tenant ID
   * @returns Contract assessment or null if not found
   */
  async getAssessment(contractId: string, tenantId: string) {
    try {
      return await this.prisma.contractAssessment.findFirst({
        where: {
          contractId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error getting contract assessment:", error);
      throw new Error(
        `Failed to get contract assessment: ${(error as Error).message}`
      );
    }
  }
}
