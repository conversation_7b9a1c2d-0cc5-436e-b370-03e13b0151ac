/**
 * Contract AI Service
 * Handles AI processing of Agreement Documents for metadata extraction
 */

import { GoogleGenAI } from "@google/genai";
import { Decimal } from "@prisma/client/runtime/library";
import { logger } from "../../infrastructure/logging/logger";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import {
  FixedFields,
  DynamicCategoricalFields,
  SpecialFields,
  DocumentSummary,
  AnalysisFields,
} from "../../domain/contracts/ContractExtraction";
import { PrismaClient } from "@prisma/client";
import path from "path";
import { ContractCalculationService } from "./ContractCalculationService";
import { FolderService } from "./FolderService";
import * as fs from 'fs';
import { FolderRepository } from "../../infrastructure/repositories/FolderRepository";
import { SupplierMappingService } from "./SupplierMappingService";
import { AzureOCRService, OCRResult } from "./AzureOCRService";

/**
 * Agreement Document analysis result
 */
export interface ContractDocumentAnalysisResult {
  title?: string;
  contractType?: string;
  classification?: string;
  agreementType?: string;
  parties?: Array<{
    name: string;
    role?: string;
    address?: string;
    contactInfo?: string;
  }>;
  dates?: {
    startDate?: string;
    endDate?: string;
    executionDate?: string;
    effectiveDate?: string;
    renewalDate?: string;
    terminationDate?: string;
  };
  value?:
  | string
  | {
    amount?: string;
    currency?: string;
    paymentSchedule?: string;
    totalValue?: string;
  };
  paymentTerms?: {
    dueDays?: number;
    latePaymentPenalty?: string;
    paymentMethod?: string;
    description?: string;
  };
  renewalTerms?: {
    isAutoRenew?: boolean;
    renewalPeriod?: string;
    noticePeriodDays?: number;
    renewalConditions?: string;
  };
  confidentiality?: string;
  terminationClauses?: string[];
  governingLaw?: string;
  disputeResolution?: string;
  liabilityLimits?: string;
  warranties?: string[];
  amendments?: string;
  signatures?: Array<{
    name?: string;
    title?: string;
    date?: string;
    party?: string;
  }>;
  otherMetadata?: Record<string, any>;
}

/**
 * Contract AI Service
 */
export class ContractAIService {
  private contractExtractionRepository: ContractExtractionRepository;
  private contractCalculationService: ContractCalculationService;
  private folderService: FolderService;
  private folderRepository: FolderRepository;
  private supplierMappingService: SupplierMappingService;
  private googleAI: GoogleGenAI;
  private azureOCRService: AzureOCRService;
  private prisma: PrismaClient;
  private clauseMapping: any;
  private readonly mappingFilePath: string;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.contractExtractionRepository = new ContractExtractionRepository(
      prisma
    );
    this.contractCalculationService = new ContractCalculationService();
    this.folderRepository = new FolderRepository(prisma);
    this.folderService = new FolderService(this.folderRepository);
    this.supplierMappingService = new SupplierMappingService();
    this.mappingFilePath = path.join(__dirname, "../../data/mapping.json");

    // Initialize Google AI client
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("GEMINI_API_KEY is not defined in environment variables");
    }
    this.googleAI = new GoogleGenAI({ apiKey });

    // Initialize Azure OCR service
    this.azureOCRService = new AzureOCRService();
  }

  /**
   * Load clause mapping from mapping.json file
   */
  private async loadClauseMapping(): Promise<void> {
    if (this.clauseMapping) {
      return; // Already loaded
    }

    try {
      const fileContent = fs.readFileSync(this.mappingFilePath, "utf-8");
      const mappingData = JSON.parse(fileContent);
      this.clauseMapping = mappingData.general;
      logger.info("Successfully loaded clause mapping data");
    } catch (error) {
      logger.error('Failed to load clause mapping:', error);
      // Fallback to empty mapping
      this.clauseMapping = {};
    }
  }

  /**
   * Get formatted clause mapping structure for AI prompt
   */
  private async getClauseMappingStructure(): Promise<string> {
    await this.loadClauseMapping();

    if (!this.clauseMapping) {
      return 'Clause mapping not available';
    }

    let structure = '';
    Object.entries(this.clauseMapping).forEach(([categoryName, clauses]: [string, any]) => {
      structure += `\n**${categoryName}:**\n`;
      const clauseNames = Object.keys(clauses);
      structure += clauseNames.join(', ') + '\n';
    });

    return structure;
  }

  /**
   * Parses JSON response from Gemini API using multiple strategies
   * @param response - Gemini API response
   * @returns Parsed JSON object
   */
  private parseGeminiResponse(response: any): any {
    // Extract response text
    let responseText = "";
    if (response && response.text) {
      responseText = response.text;
    } else if (
      response &&
      response.candidates &&
      response.candidates[0] &&
      response.candidates[0].content &&
      response.candidates[0].content.parts &&
      response.candidates[0].content.parts[0] &&
      response.candidates[0].content.parts[0].text
    ) {
      responseText = response.candidates[0].content.parts[0].text;
    } else {
      logger.warn("Unexpected response structure from Gemini API");
      logger.debug("Raw response:", JSON.stringify(response));
      throw new Error("Invalid response structure from Gemini API");
    }

    if (!responseText) {
      logger.warn("Empty response from Gemini API");
      throw new Error("Empty response from Gemini API");
    }

    // Extract JSON from response using multiple strategies
    let jsonObject = null;

    // Strategy 1: Try to parse the entire response as JSON directly
    try {
      jsonObject = JSON.parse(responseText.trim());
      logger.info("Successfully parsed entire response as JSON");
    } catch (directParseError) {
      // Strategy 2: Find JSON between triple backticks (markdown code blocks)
      const tripleBacktickMatch = responseText.match(
        /```(?:json)?\s*(\{[\s\S]*?\})\s*```/
      );
      if (tripleBacktickMatch) {
        try {
          jsonObject = JSON.parse(tripleBacktickMatch[1].trim());
          logger.info("Successfully extracted JSON from triple backticks");
        } catch (tripleBacktickError) {
          logger.warn(
            "Found JSON-like content in triple backticks but failed to parse",
            {
              error: tripleBacktickError,
              content: tripleBacktickMatch[1],
            }
          );
        }
      }

      // Strategy 3: Find JSON between single backticks
      if (!jsonObject) {
        const singleBacktickMatch = responseText.match(
          /`\s*(\{[\s\S]*?\})\s*`/
        );
        if (singleBacktickMatch) {
          try {
            jsonObject = JSON.parse(singleBacktickMatch[1].trim());
            logger.info(
              "Successfully extracted JSON from single backticks"
            );
          } catch (singleBacktickError) {
            logger.warn(
              "Found JSON-like content in single backticks but failed to parse",
              {
                error: singleBacktickError,
                content: singleBacktickMatch[1],
              }
            );
          }
        }
      }

      // Strategy 4: Find any JSON-like object in the text
      if (!jsonObject) {
        const jsonMatch = responseText.match(/(\{[\s\S]*\})/);
        if (jsonMatch) {
          try {
            jsonObject = JSON.parse(jsonMatch[0].trim());
            logger.info("Successfully extracted JSON from text");
          } catch (jsonMatchError) {
            logger.warn("Found JSON-like content but failed to parse", {
              error: jsonMatchError,
              content: jsonMatch[0],
            });
          }
        }
      }
    }

    // If we still don't have valid JSON, try one last approach - look for the first { and last }
    if (!jsonObject) {
      const firstBrace = responseText.indexOf("{");
      const lastBrace = responseText.lastIndexOf("}");

      if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
        const potentialJson = responseText.substring(
          firstBrace,
          lastBrace + 1
        );
        try {
          jsonObject = JSON.parse(potentialJson);
          logger.info("Successfully extracted JSON using brace positions");
        } catch (braceError) {
          logger.warn("Failed to parse JSON using brace positions", {
            error: braceError,
            content: potentialJson,
          });
        }
      }
    }

    // If we still don't have valid JSON, give up
    if (!jsonObject) {
      logger.error("Could not extract valid JSON from Gemini response");
      logger.debug("Raw response:", responseText);
      throw new Error("Failed to extract valid JSON from AI response");
    }

    return jsonObject;
  }

  /**
   * Extracts only fixed fields from a contract document to identify supplier
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @returns Fixed fields extraction result
   */
  async extractFixedFieldsOnly(
    documentBuffer: Buffer,
    fileName: string,
    contractId?: string
  ): Promise<FixedFields> {
    try {
      logger.info(`Extracting fixed fields only from: ${fileName}`);

      // Check if OCR text is available for this contract
      let useOCRText = false;
      let ocrText = '';

      if (contractId) {
        const ocrTextResult = await this.getOCRTextForContract(contractId);
        if (ocrTextResult) {
          useOCRText = true;
          ocrText = ocrTextResult;
          logger.info(`Using OCR text for fixed fields extraction: ${fileName}`);
        }
      }

      if (!useOCRText) {
        // Fallback to document buffer
        logger.info(`Using document buffer for fixed fields extraction: ${fileName}`);
      }

      // Convert document to base64 (only if not using OCR)
      const base64Content = useOCRText ? '' : documentBuffer.toString("base64");
      const mimeType = useOCRText ? '' : this.getMimeType(fileName);

      // Focused prompt for fixed fields only
      const promptText = `You are an expert contract analysis system. Extract ONLY the following 16 fixed fields from this contract document. Do not extract any other information.

**FIXED FIELDS TO EXTRACT:**

1. **agreement_type**: Use standardized abbreviations (MSA, NDA, SOW, PO, SLA, DPA, BAA, EULA, SCHEDULE, INVOICE, ORDER, etc.)
2. **provider**: Service/product provider company name (the supplier/vendor)
3. **client**: Customer/client company name
4. **product**: Primary product or service being contracted
5. **total_amount**: Format as "CURRENCY_CODE:AMOUNT" (e.g., "USD:1250000.00", "EUR:808668.96")
6. **annually_amount**: Year-by-year breakdown of contract value. If explicitly mentioned in contract (e.g., "Year 1: $50,000, Year 2: $60,000"), extract as-is. If not mentioned, calculate as follows:
   - Convert contract_term from months to years (divide by 12)
   - If contract_term >= 12 months: Divide total_amount by years (e.g., 18 months = 1.5 years, so USD:150000.00 ÷ 1.5 = USD:100000.00 per year)
   - If contract_term < 12 months: Calculate proportional annual value by multiplying (e.g., 6 months with USD:50000.00 = USD:100000.00 annual rate)
   - Format examples:
     * 24 months, USD:120000.00 → "Year 1: USD:60000.00, Year 2: USD:60000.00"
     * 18 months, USD:150000.00 → "Year 1: USD:100000.00, Year 2 (6 months): USD:50000.00"
     * 6 months, USD:50000.00 → "Annual rate: USD:100000.00 (6 months actual: USD:50000.00)"
   Use "N/A" if total_amount or contract_term cannot be determined.
7. **start_date**: Contract start date in YYYY-MM-DD format
8. **end_date**: Contract expiration date in YYYY-MM-DD format
9. **contract_id**: Any unique identifier (contract number, reference number, agreement ID)
10. **contract_classification**: Use only these values: SW_SAAS|IAAS|PAAS|PROFESSIONAL_SERVICES|MANAGED_SERVICES|HARDWARE|RESELLER|NETWORK|OTHER
11. **contract_status**: Determine current contract status ("Active" if currently in effect, "Inactive" if expired or not yet started, "Unknown" if dates are unclear or missing)
12. **contract_term**: Extract contract duration from document text (e.g., "24 months", "3 years", "36 months"). If not explicitly stated, calculate from start_date and end_date and format as months (e.g., "17 months"). Use "N/A" if cannot be determined.
13. **auto_renewal**: Whether contract automatically renews ("Yes" or "No" - must be determined from contract text, default to "No" if unclear)
14. **renewal_notice_period**: Notice period required to prevent renewal (ALWAYS format as "X months" only, e.g., "1 month", "3 months", "6 months", "12 months". Convert days to months: 30 days = "1 month", 60 days = "2 months", 90 days = "3 months". Use "N/A" if not specified)
15. **relationships**: Any references to other documents mentioned in this contract (comma-separated string of document names, contract IDs, file names, or any document references found in the text; capture exactly as mentioned; "N/A" if no references found)
16. **original_filename**: The original filename of the uploaded document

**CONFIDENCE SCORING:**
- 0.9-1.0: Explicitly stated with clear language
- 0.7-0.9: Clearly implied or stated with minor ambiguity
- 0.5-0.7: Moderately ambiguous, requires interpretation
- 0.3-0.5: Unclear or weakly implied
- 0.1-0.3: Uncertain, missing, or highly speculative

**OUTPUT FORMAT:**
Return a valid JSON object with this exact structure:

{
  "fixed_fields": {
    "agreement_type": {"value": "MSA", "confidence": 0.95},
    "provider": {"value": "Company Name", "confidence": 0.90},
    "client": {"value": "Customer Name", "confidence": 0.85},
    "product": {"value": "Cloud Software Platform", "confidence": 0.88},
    "total_amount": {"value": "USD:150000.00", "confidence": 0.90},
    "annually_amount": {"value": "Year 1: USD:100000.00, Year 2 (6 months): USD:50000.00", "confidence": 0.85},
    "start_date": {"value": "2024-01-01", "confidence": 0.95},
    "end_date": {"value": "2025-06-30", "confidence": 0.90},
    "contract_id": {"value": "MSA-2024-001", "confidence": 0.85},
    "contract_classification": {"value": "SW_SAAS", "confidence": 0.88},
    "contract_status": {"value": "Active", "confidence": 0.90},
    "contract_term": {"value": "18 months", "confidence": 0.85},
    "auto_renewal": {"value": "No", "confidence": 0.90},
    "renewal_notice_period": {"value": "3 months", "confidence": 0.85},
    "relationships": {"value": "Master Agreement dated Jan 2024,Data Processing Addendum", "confidence": 0.80},
    "original_filename": {"value": "${fileName}", "confidence": 1.0}
  }
}

**IMPORTANT:**
- Extract ONLY these 16 fixed fields, nothing else
- Focus on identifying the provider/supplier accurately as this will be used for subsequent targeted extraction
- For annually_amount calculation: Always convert contract_term to years first (months ÷ 12), then calculate annual values
- Your response MUST be a valid JSON object matching the exact structure shown above
- Do not include explanatory text outside the JSON`;

      try {
        // Call Gemini API with either OCR text or document buffer
        const parts: any[] = [{ text: promptText }];

        if (useOCRText) {
          // Add OCR text to the prompt
          parts[0].text += `\n\nContract Text:\n${ocrText}`;
        } else {
          // Add document as inline data
          parts.push({
            inlineData: {
              mimeType,
              data: base64Content,
            },
          });
        }

        const response = await this.googleAI.models.generateContent({
          model: "gemini-2.5-pro",
          config: {
            temperature: 0.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 4096,
          },
          contents: {
            role: "user",
            parts,
          },
        });

        // Extract and parse response
        const jsonObject = this.parseGeminiResponse(response);

        if (!jsonObject || !jsonObject.fixed_fields) {
          throw new Error("Invalid fixed fields extraction result structure");
        }

        // Add original filename with high confidence
        const fixedFields = {
          ...jsonObject.fixed_fields,
          original_filename: {
            value: fileName,
            confidence: 1.0,
          },
        } as FixedFields;

        logger.info(`Successfully extracted fixed fields from: ${fileName}`);
        return fixedFields;

      } catch (error) {
        logger.error("Error processing fixed fields extraction", { error });
        throw error;
      }
    } catch (error) {
      logger.error("Error extracting fixed fields", { error });
      throw error;
    }
  }

  /**
   * Extracts dynamic fields while excluding supplier-specific fields to avoid duplication
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param supplierName - Identified supplier name (optional)
   * @param supplierSpecificFields - Already extracted supplier-specific fields to exclude
   * @returns Dynamic fields extraction result
   */
  async extractDynamicFieldsWithExclusion(
    documentBuffer: Buffer,
    fileName: string,
    supplierName?: string,
    supplierSpecificFields?: Record<string, any>,
    contractId?: string
  ): Promise<DynamicCategoricalFields> {
    try {
      logger.info(`Extracting dynamic fields with exclusion from: ${fileName}`);

      // Check if OCR text is available for this contract
      let useOCRText = false;
      let ocrText = '';

      if (contractId) {
        const ocrTextResult = await this.getOCRTextForContract(contractId);
        if (ocrTextResult) {
          useOCRText = true;
          ocrText = ocrTextResult;
          logger.info(`Using OCR text for dynamic fields extraction: ${fileName}`);
        }
      }

      if (!useOCRText) {
        logger.info(`Using document buffer for dynamic fields extraction: ${fileName}`);
      }

      // Convert document to base64 (only if not using OCR)
      const base64Content = useOCRText ? '' : documentBuffer.toString("base64");
      const mimeType = useOCRText ? '' : this.getMimeType(fileName);

      // Build exclusion list from supplier-specific fields
      let exclusionList: string[] = [];
      if (supplierSpecificFields) {
        for (const category of Object.values(supplierSpecificFields)) {
          if (typeof category === "object" && category !== null) {
            exclusionList.push(...Object.keys(category));
          }
        }
      }

      const exclusionText = exclusionList.length > 0
        ? `\n\n**IMPORTANT EXCLUSIONS:**\nDo NOT extract the following fields as they have already been extracted in the supplier-specific step:\n${exclusionList.map(field => `- ${field}`).join('\n')}\n\nAvoid any fields that are semantically similar to these excluded fields.`
        : '';

      // Focused prompt for dynamic fields only
      const promptText = `You are an expert contract analysis system. Extract ONLY dynamic contract-specific fields from this contract document and organize them into the specified categories. Do not extract fixed fields or supplier-specific fields.

**DYNAMIC FIELDS TO EXTRACT:**

Extract EVERY relevant contract-specific field found in the document and organize them into the following categories. Perform exhaustive analysis to capture ALL contractual terms, conditions, clauses, and metadata:

**Use rights & restrictions:** Usage limitations, access restrictions, permitted uses, prohibited activities, user limitations, capacity constraints, geographic restrictions, time-based limitations, scope of use, operational boundaries, service limitations, feature restrictions, and ALL usage-related terms and constraints.

**General:** General contract terms, basic provisions, standard clauses, administrative details, general obligations, miscellaneous provisions, definitions, interpretations, general conditions, standard terms, boilerplate clauses, general requirements, and ALL other general contractual provisions.

**Legal terms:** Liability limitations, indemnification clauses, confidentiality periods, data privacy compliance requirements, audit rights, regulatory compliance obligations, legal protections, governing law, jurisdiction, dispute resolution procedures, arbitration clauses, mediation requirements, legal notices, compliance certifications, regulatory reporting, intellectual property rights, warranties, representations, and ALL legal and compliance terms.

**Commercial terms:** Payment schedules, billing frequencies, late fees, currency provisions, tax responsibilities, pricing models, cost escalation clauses, financial penalties, discounts, rebates, credits, adjustments, true-up provisions, budget caps, spending limits, invoice procedures, payment methods, banking details, financial reporting requirements, audit rights, service level credits, performance bonuses, and ALL other monetary obligations and financial arrangements.

**Data protection:** Data privacy requirements, data security measures, data retention policies, data processing terms, data transfer restrictions, data subject rights, GDPR compliance, data breach notification procedures, data encryption requirements, data backup procedures, data deletion obligations, data access controls, and ALL data protection and privacy-related terms.

**Others:** Service level agreements, performance metrics, uptime guarantees, response times, support levels, maintenance schedules, delivery timelines, quality standards, operational commitments, availability requirements, capacity guarantees, throughput specifications, error rates, resolution times, escalation procedures, performance penalties, technical specifications, training provisions, implementation requirements, operational constraints, system requirements, integration specifications, API limitations, bandwidth requirements, security standards, backup procedures, disaster recovery plans, insurance requirements, risk allocation clauses, force majeure provisions, business continuity requirements, security audits, penetration testing, vulnerability assessments, auto-renewal provisions, notice periods, termination rights, cancellation procedures, post-termination obligations, transition requirements, contract continuation terms, renewal pricing, termination fees, wind-down procedures, data return obligations, and ALL other contractual terms not covered in the above categories.

Use descriptive field names that clearly indicate the nature of each extracted term (e.g., "renewal_notice_period", "liability_cap", "support_response_time", "data_retention_period", "security_audit_frequency", "ip_ownership_rights"). Each dynamic field must include:
- value: Extracted value from the contract
- description: Brief explanation of what this field represents in business context
- confidence: Confidence score (0.0-1.0)

**MANDATORY DYNAMIC FIELD - Contract Description:**
Always include a "contract_description" field in the "General" category with:
- value: Comprehensive description of the contract including its purpose, scope, key obligations, deliverables, and business context. Include specific document section references, calculations that justify the contract value, and detailed supporting information that explains what the contract covers and why it has the stated value.
- description: "Detailed contract description with supporting information and value justification"
- confidence: Confidence score based on how well the description can be extracted from the document${exclusionText}

**CONFIDENCE SCORING:**
- 0.9-1.0: Explicitly stated with clear language
- 0.7-0.9: Clearly implied or stated with minor ambiguity
- 0.5-0.7: Moderately ambiguous, requires interpretation
- 0.3-0.5: Unclear or weakly implied
- 0.1-0.3: Uncertain, missing, or highly speculative

**FINANCIAL AMOUNTS:**
For any financial/monetary values in the Financial category, ALWAYS use the format "CURRENCY:AMOUNT" (e.g., "USD:50000", "EUR:25000.50"). Extract both currency and amount together.

**OUTPUT FORMAT:**
Return a valid JSON object with this exact structure:

{
  "dynamic_fields": {
    "Use rights & restrictions": {
      "user_limitations": {
        "value": "Maximum 500 concurrent users",
        "description": "Limit on number of simultaneous users",
        "confidence": 0.92
      },
      "geographic_restrictions": {
        "value": "Service available only in North America",
        "description": "Geographic limitations on service availability",
        "confidence": 0.88
      }
    },
    "General": {
      "contract_description": {
        "value": "This Master Service Agreement establishes a comprehensive cloud software platform engagement...",
        "description": "Detailed contract description with supporting information and value justification",
        "confidence": 0.88
      },
      "effective_date": {
        "value": "January 1, 2024",
        "description": "Date when contract becomes effective",
        "confidence": 0.90
      }
    },
    "Legal terms": {
      "governing_law": {
        "value": "State of California",
        "description": "Legal jurisdiction governing the contract",
        "confidence": 0.85
      }
    },
    "Commercial terms": {
      "payment_terms": {
        "value": "Net 30 days from invoice date",
        "description": "Payment due within 30 days of invoice receipt",
        "confidence": 0.90
      },
      "annual_fee": {
        "value": "USD:120000",
        "description": "Annual subscription fee",
        "confidence": 0.95
      }
    },
    "Data protection": {
      "data_retention_period": {
        "value": "7 years after contract termination",
        "description": "Duration for retaining customer data",
        "confidence": 0.88
      }
    },
    "Others": {
      "service_level_agreement": {
        "value": "99.9% uptime guarantee",
        "description": "Minimum service availability commitment",
        "confidence": 0.92
      }
    }
  }
}

**IMPORTANT:**
- Extract ONLY dynamic fields, not fixed fields or supplier-specific fields
- Focus on comprehensive extraction while avoiding duplication with already extracted supplier-specific fields
- Your response MUST be a valid JSON object matching the exact structure shown above
- Do not include explanatory text outside the JSON`;

      try {
        // Call Gemini API with either OCR text or document buffer
        const parts: any[] = [{ text: promptText }];

        if (useOCRText) {
          // Add OCR text to the prompt
          parts[0].text += `\n\nContract Text:\n${ocrText}`;
        } else {
          // Add document as inline data
          parts.push({
            inlineData: {
              mimeType,
              data: base64Content,
            },
          });
        }

        const response = await this.googleAI.models.generateContent({
          model: "gemini-2.5-pro",
          config: {
            temperature: 0.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192,
          },
          contents: {
            role: "user",
            parts,
          },
        });

        // Extract and parse response
        const jsonObject = this.parseGeminiResponse(response);

        if (!jsonObject || !jsonObject.dynamic_fields) {
          logger.warn("Invalid dynamic fields extraction result structure, returning empty fields");
          return {};
        }

        logger.info(`Successfully extracted dynamic fields from: ${fileName}`);
        return jsonObject.dynamic_fields as DynamicCategoricalFields;

      } catch (error) {
        logger.error("Error processing dynamic fields extraction", { error });
        logger.warn("Returning empty dynamic fields due to extraction error");
        return {};
      }
    } catch (error) {
      logger.error("Error extracting dynamic fields", { error });
      logger.warn("Returning empty dynamic fields due to extraction error");
      return {};
    }
  }

  /**
   * Analyzes a Agreement Document using Gemini AI
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @returns Analysis result
   */
  async analyzeContractDocument(
    documentBuffer: Buffer,
    fileName: string
  ): Promise<ContractDocumentAnalysisResult> {
    try {
      logger.info(`Analyzing Agreement Document: ${fileName}`);

      // Convert document to base64
      const base64Content = documentBuffer.toString("base64");
      const mimeType = this.getMimeType(fileName);

      // Prepare prompt for Gemini API
      const promptText = `You are an expert contract analysis system designed to perform COMPREHENSIVE document analysis and extract ALL structured data from contract documents using a three-tier categorization approach.

**CRITICAL INSTRUCTION: ANALYZE EVERY SECTION, CLAUSE, AND DETAIL**

You must thoroughly examine the ENTIRE contract document, reading every paragraph, section, clause, subsection, appendix, schedule, exhibit, and attachment. Leave no stone unturned. Extract EVERY piece of structured information, contractual term, condition, obligation, right, restriction, and metadata present in the document.

**COMPREHENSIVE ANALYSIS REQUIREMENTS:**

- Read and analyze EVERY page of the document from beginning to end
- Extract information from headers, footers, signatures, and metadata sections
- Analyze all appendices, schedules, exhibits, and attachments
- Identify and extract ALL financial terms, amounts, percentages, and calculations
- Capture ALL dates, deadlines, milestones, and time-based obligations
- Extract ALL legal terms, clauses, conditions, and contractual language
- Identify ALL parties, entities, roles, and relationships mentioned
- Capture ALL performance metrics, service levels, and quality standards
- Extract ALL compliance requirements, regulatory obligations, and standards
- Identify ALL intellectual property, licensing, and usage rights
- Capture ALL termination, renewal, and modification provisions
- Extract ALL risk allocation, liability, and indemnification terms
- Identify ALL data protection, privacy, and security requirements
- Capture ALL operational procedures, processes, and workflows
- Extract ALL technical specifications, requirements, and constraints

**EXTRACTION CATEGORIES:**

1. **Fixed Fields** (mandatory extraction for all contracts - 15 total fields):
   - agreement_type: Use standardized abbreviations (MSA, NDA, SOW, PO, SLA, DPA, BAA, EULA, SCHEDULE, INVOICE, etc.)
   - provider: Service/product provider company name
   - client: Customer/client company name
   - contract_classification: Contract category classification
   - total_amount: Format as "CURRENCY_CODE:AMOUNT" (e.g., "USD:1250000.00", "EUR:808668.96")
   - start_date: Contract start date in YYYY-MM-DD format
   - end_date: Contract expiration date in YYYY-MM-DD format
   - contract_id: Any unique identifier (contract number, reference number, agreement ID)
   - product: Primary product or service being contracted
   - contract_status: Determine current contract status based on dates and contract text ("Active" if currently in effect, "Inactive" if expired or not yet started, "Unknown" if dates are unclear or missing)
   - contract_term: Extract contract duration from document text (e.g., "24 months", "3 years", "36 months"). If not explicitly stated, calculate from start_date and end_date and format as months (e.g., "17 months"). Use "N/A" if cannot be determined.
   - auto_renewal: Whether contract automatically renews ("Yes" or "No" - must be determined from contract text, default to "No" if unclear)
   - renewal_notice_period: Notice period required to prevent renewal (CRITICAL: ALWAYS format as "X months" ONLY. Convert: 30 days = "1 month", 60 days = "2 months", 90 days = "3 months", 180 days = "6 months", 365 days = "12 months", 1 year = "12 months". Use "N/A" if not specified)
   - relationships: Any references to other documents mentioned in this contract (comma-separated string of document names, contract IDs, file names, or any document references found in the text; capture exactly as mentioned; "N/A" if no references found)
   - original_filename: The original filename of the uploaded document

2. **Dynamic Fields** (COMPREHENSIVE contract-specific metadata organized by categories):
   Extract EVERY relevant contract-specific field found in the document and organize them into the following categories. Perform exhaustive analysis to capture ALL contractual terms, conditions, clauses, and metadata:

   **Use rights & restrictions:** Usage limitations, access restrictions, permitted uses, prohibited activities, user limitations, capacity constraints, geographic restrictions, time-based limitations, scope of use, operational boundaries, service limitations, feature restrictions, and ALL usage-related terms and constraints.

   **General:** General contract terms, basic provisions, standard clauses, administrative details, general obligations, miscellaneous provisions, definitions, interpretations, general conditions, standard terms, boilerplate clauses, general requirements, and ALL other general contractual provisions.

   **Legal terms:** Liability limitations, indemnification clauses, confidentiality periods, data privacy compliance requirements, audit rights, regulatory compliance obligations, legal protections, governing law, jurisdiction, dispute resolution procedures, arbitration clauses, mediation requirements, legal notices, compliance certifications, regulatory reporting, intellectual property rights, warranties, representations, and ALL legal and compliance terms.

   **Commercial terms:** Payment schedules, billing frequencies, late fees, currency provisions, tax responsibilities, pricing models, cost escalation clauses, financial penalties, discounts, rebates, credits, adjustments, true-up provisions, budget caps, spending limits, invoice procedures, payment methods, banking details, financial reporting requirements, audit rights, service level credits, performance bonuses, and ALL other monetary obligations and financial arrangements.

   **Data protection:** Data privacy requirements, data security measures, data retention policies, data processing terms, data transfer restrictions, data subject rights, GDPR compliance, data breach notification procedures, data encryption requirements, data backup procedures, data deletion obligations, data access controls, and ALL data protection and privacy-related terms.

   **Others:** Service level agreements, performance metrics, uptime guarantees, response times, support levels, maintenance schedules, delivery timelines, quality standards, operational commitments, availability requirements, capacity guarantees, throughput specifications, error rates, resolution times, escalation procedures, performance penalties, technical specifications, training provisions, implementation requirements, operational constraints, system requirements, integration specifications, API limitations, bandwidth requirements, security standards, backup procedures, disaster recovery plans, insurance requirements, risk allocation clauses, force majeure provisions, business continuity requirements, security audits, penetration testing, vulnerability assessments, auto-renewal provisions, notice periods, termination rights, cancellation procedures, post-termination obligations, transition requirements, contract continuation terms, renewal pricing, termination fees, wind-down procedures, data return obligations, and ALL other contractual terms not covered in the above categories.

   Use descriptive field names that clearly indicate the nature of each extracted term (e.g., "renewal_notice_period", "liability_cap", "support_response_time", "data_retention_period", "security_audit_frequency", "ip_ownership_rights"). Each dynamic field must include:
   - value: Extracted value from the contract
   - description: Brief explanation of what this field represents in business context
   - confidence: Confidence score (0.0-1.0)

   **MANDATORY DYNAMIC FIELD - Contract Description:**
   Always include a "contract_description" field with:
   - value: Comprehensive description of the contract including its purpose, scope, key obligations, deliverables, and business context. Include specific document section references, calculations that justify the contract value, and detailed supporting information that explains what the contract covers and why it has the stated value.
   - description: "Detailed contract description with supporting information and value justification"
   - confidence: Confidence score based on how well the description can be extracted from the document

3. **Special Fields** (vendor-specific fields - will be populated in a separate extraction step):

   Leave this as an empty object for now. Vendor-specific fields will be extracted in a targeted second pass after supplier identification.

**CONFIDENCE SCORING:**
- 0.9-1.0: Explicitly stated with clear language
- 0.7-0.9: Clearly implied or stated with minor ambiguity
- 0.5-0.7: Moderately ambiguous, requires interpretation
- 0.3-0.5: Unclear or weakly implied
- 0.1-0.3: Uncertain, missing, or highly speculative

**OUTPUT FORMAT:**
Return a valid JSON object with this exact structure:

{
  "fixed_fields": {
    "agreement_type": {"value": "MSA", "confidence": 0.95},
    "provider": {"value": "Company Name", "confidence": 0.90},
    "client": {"value": "Customer Name", "confidence": 0.85},
    "product": {"value": "Cloud Software Platform", "confidence": 0.88},
    "total_amount": {"value": "USD:1250000.00", "confidence": 0.90},
    "start_date": {"value": "2024-01-01", "confidence": 0.95},
    "end_date": {"value": "2026-12-31", "confidence": 0.90},
    "contract_id": {"value": "MSA-2024-001", "confidence": 0.85},
    "contract_classification": {"value": "SW_SAAS", "confidence": 0.88},
    "contract_status": {"value": "Active", "confidence": 0.90},
    "contract_term": {"value": "36 months", "confidence": 0.85},
    "auto_renewal": {"value": "No", "confidence": 0.90},
    "renewal_notice_period": {"value": "3 months", "confidence": 0.85},
    "relationships": {"value": "Master Agreement dated Jan 2024,Data Processing Addendum,Schedule A", "confidence": 0.80},
    "original_filename": {"value": "MSA_CompanyName_2024.pdf", "confidence": 1.0}
  },
  "dynamic_fields": {
    "Use rights & restrictions": {
      "user_limitations": {
        "value": "Maximum 500 concurrent users",
        "description": "Limit on number of simultaneous users",
        "confidence": 0.92
      },
      "geographic_restrictions": {
        "value": "Service available only in North America",
        "description": "Geographic limitations on service availability",
        "confidence": 0.88
      },
      "usage_limitations": {
        "value": "10TB monthly data processing limit",
        "description": "Monthly data processing capacity constraint",
        "confidence": 0.85
      }
    },
    "General": {
      "contract_description": {
        "value": "This Master Service Agreement establishes a comprehensive cloud software platform engagement between Company Name and Customer Name. The contract covers enterprise-grade SaaS services including data processing, analytics, and reporting capabilities as detailed in Section 2.1. The total contract value of $1,250,000 is justified by the scope including 500 user licenses at $2,500 per license annually, premium support services valued at $150,000 per year, and professional implementation services totaling $100,000 as outlined in Exhibit A. The agreement encompasses data migration, system integration, ongoing maintenance, and 24/7 technical support with guaranteed 99.9% uptime per Section 4.2.",
        "description": "Detailed contract description with supporting information and value justification",
        "confidence": 0.88
      },
      "effective_date": {
        "value": "January 1, 2024",
        "description": "Date when contract becomes effective",
        "confidence": 0.90
      },
      "contract_purpose": {
        "value": "Provision of cloud-based software services",
        "description": "Primary purpose of the agreement",
        "confidence": 0.89
      }
    },
    "Legal terms": {
      "governing_law": {
        "value": "State of California",
        "description": "Legal jurisdiction governing the contract",
        "confidence": 0.85
      },
      "dispute_resolution": {
        "value": "Binding arbitration in San Francisco, CA",
        "description": "Method and location for resolving disputes",
        "confidence": 0.86
      },
      "confidentiality_period": {
        "value": "5 years after contract termination",
        "description": "Duration of confidentiality obligations",
        "confidence": 0.89
      },
      "liability_limitation": {
        "value": "Limited to 12 months of fees paid",
        "description": "Maximum liability cap for damages",
        "confidence": 0.83
      },
      "intellectual_property_ownership": {
        "value": "Customer retains ownership of all data and content",
        "description": "IP ownership rights and restrictions",
        "confidence": 0.90
      }
    },
    "Commercial terms": {
      "payment_terms": {
        "value": "Net 30 days from invoice date",
        "description": "Payment due within 30 days of invoice receipt",
        "confidence": 0.90
      },
      "late_payment_fee": {
        "value": "1.5% per month",
        "description": "Interest rate charged on overdue payments",
        "confidence": 0.87
      },
      "billing_frequency": {
        "value": "Monthly in advance",
        "description": "How often invoices are generated",
        "confidence": 0.89
      }
    },
    "Data protection": {
      "data_retention_period": {
        "value": "7 years after contract termination",
        "description": "Duration for retaining customer data",
        "confidence": 0.88
      },
      "data_encryption_requirements": {
        "value": "AES-256 encryption for data at rest and in transit",
        "description": "Required encryption standards for data protection",
        "confidence": 0.85
      },
      "gdpr_compliance": {
        "value": "Full GDPR compliance including data subject rights",
        "description": "GDPR compliance obligations and requirements",
        "confidence": 0.87
      }
    },
    "Others": {
      "service_level_agreement": {
        "value": "99.9% uptime guarantee",
        "description": "Minimum service availability commitment",
        "confidence": 0.92
      },
      "response_time_requirement": {
        "value": "4 hours for critical issues",
        "description": "Maximum response time for critical support issues",
        "confidence": 0.88
      },
      "maintenance_window": {
        "value": "Sundays 2-6 AM EST",
        "description": "Scheduled maintenance window",
        "confidence": 0.85
      },
      "implementation_timeline": {
        "value": "90 days from contract execution",
        "description": "Time allowed for system implementation",
        "confidence": 0.83
      },
      "auto_renewal_terms": {
        "value": "Automatically renews for 1-year terms unless terminated",
        "description": "Contract renewal mechanism and duration",
        "confidence": 0.91
      },
      "renewal_notice_period": {
        "value": "3 months written notice",
        "description": "Required notice period to prevent auto-renewal",
        "confidence": 0.87
      },
      "termination_for_cause": {
        "value": "30 days written notice for material breach",
        "description": "Termination procedure for contract violations",
        "confidence": 0.85
      },
      "force_majeure_clause": {
        "value": "Performance excused for acts of God, war, natural disasters",
        "description": "Circumstances that excuse contract performance",
        "confidence": 0.82
      }
    }
  },
  "special_fields": {}
}

**COMPREHENSIVE EXTRACTION RULES:**

1. **THOROUGHNESS REQUIREMENT:** Extract EVERY piece of structured information present in the document. Read every section, paragraph, clause, subsection, appendix, schedule, exhibit, and attachment. Do not skip any content.

2. **DYNAMIC FIELDS CATEGORIZATION:** For dynamic_fields, extract EVERY contract-specific term, condition, clause, and metadata element found in the document and organize them into the 6 predefined categories. This should result in 30-100+ dynamic fields for comprehensive contracts, distributed across categories:

   **Use rights & restrictions Category:**
   - ALL usage limitations and access restrictions
   - ALL permitted uses and prohibited activities
   - ALL user limitations and capacity constraints
   - ALL geographic and time-based restrictions
   - ALL scope of use and operational boundaries

   **General Category:**
   - ALL general contract terms and basic provisions
   - ALL standard clauses and administrative details
   - ALL general obligations and miscellaneous provisions
   - ALL definitions, interpretations, and general conditions
   - Contract description (MANDATORY field in this category)

   **Legal terms Category:**
   - ALL legal clauses and provisions
   - ALL compliance and regulatory obligations
   - ALL governing law and jurisdiction terms
   - ALL dispute resolution and legal procedures
   - ALL intellectual property and warranty terms

   **Commercial terms Category:**
   - ALL financial terms and conditions beyond the basic total amount
   - ALL payment schedules, billing terms, and invoicing procedures
   - ALL pricing models, cost structures, and fee arrangements
   - ALL financial penalties, discounts, and adjustments

   **Data protection Category:**
   - ALL data privacy and security requirements
   - ALL data retention and processing terms
   - ALL data transfer restrictions and subject rights
   - ALL GDPR compliance and breach notification procedures
   - ALL data encryption and access control requirements

   **Others Category:**
   - ALL service level agreements and performance standards
   - ALL technical specifications and operational requirements
   - ALL risk allocation and liability terms
   - ALL insurance requirements and security measures
   - ALL termination and renewal conditions
   - ALL other contractual terms not covered in above categories

3. **INDUSTRY-SPECIFIC TERMINOLOGY:** Identify and extract industry-specific language, technical terms, and specialized contractual provisions relevant to the business domain (software, healthcare, finance, manufacturing, etc.).

4. **COMPREHENSIVE CLAUSE ANALYSIS:** Analyze and extract information from ALL types of contract clauses including but not limited to:
   - Force majeure clauses
   - Indemnification provisions
   - Limitation of liability clauses
   - Confidentiality and non-disclosure terms
   - Intellectual property ownership and licensing
   - Data processing and privacy terms
   - Compliance and regulatory requirements
   - Performance standards and service levels
   - Payment terms and financial obligations
   - Termination and renewal provisions
   - Dispute resolution procedures
   - Governing law and jurisdiction
   - Amendment and modification procedures
   - Assignment and transfer restrictions
   - Severability and enforceability terms

5. **METADATA EXTRACTION:** Extract ALL document metadata including version numbers, revision dates, approval signatures, reference numbers, and administrative information.

6. **FORMATTING RULES:**
   - Use "N/A" for missing fields with appropriate low confidence scores (0.1-0.3)
   - For currency amounts, always use "CURRENCY_CODE:AMOUNT" format
   - For dates, always use YYYY-MM-DD format
   - For agreement types, use standardized abbreviations: MSA|NDA|SOW|PO|SLA|DPA|BAA|EULA|LOI|MOA|MOU|JV|CA|LPA|SSA|ESA|PSA|TOS|DUA|OEM|RFP|RFQ|BPA|PPA|LSA|ISA|SPA|APA|TPA|IP|RSA|VARA|DDA|TSA|IA|INVOICE|SCHEDULE|ORDER|OTHER
   - For contract_classification, use only these values: SW_SAAS|IAAS|PAAS|PROFESSIONAL_SERVICES|MANAGED_SERVICES|HARDWARE|RESELLER|NETWORK|OTHER

7. **ACCURACY AND COMPLETENESS:** Extract only information explicitly stated or strongly implied in the document. Do not hallucinate or guess information not present. However, prioritize COMPLETENESS - extract EVERY available piece of information.

8. **SPECIAL FIELDS:** Leave special_fields as an empty object {} - vendor-specific fields will be extracted in a separate targeted extraction step.

9. **COMPREHENSIVE COVERAGE:** Your extraction should capture the full depth and breadth of information contained within the contract document, providing a complete digital representation of all contractual terms, conditions, and metadata.

**IMPORTANT:** Your response MUST be a valid JSON object matching the exact structure shown above. Do not include explanatory text outside the JSON.`;

      try {
        // Call Gemini API using @google/genai SDK
        const response = await this.googleAI.models.generateContent({
          model: "gemini-2.5-pro",
          config: {
            temperature: 0.1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 16384,
          },
          contents: {
            role: "user",
            parts: [
              {
                text: promptText,
              },
              {
                inlineData: {
                  mimeType,
                  data: base64Content,
                },
              },
            ],
          },
        });

        // Extract response text
        let responseText = "";
        if (response && response.text) {
          responseText = response.text;
        } else if (
          response &&
          response.candidates &&
          response.candidates[0] &&
          response.candidates[0].content &&
          response.candidates[0].content.parts &&
          response.candidates[0].content.parts[0] &&
          response.candidates[0].content.parts[0].text
        ) {
          responseText = response.candidates[0].content.parts[0].text;
        } else {
          logger.warn("Unexpected response structure from Gemini API");
          logger.debug("Raw response:", JSON.stringify(response));
          throw new Error("Invalid response structure from Gemini API");
        }

        if (!responseText) {
          logger.warn("Empty response from Gemini API");
          throw new Error("Empty response from Gemini API");
        }

        // Extract JSON from response using multiple strategies
        let jsonObject = null;

        // Strategy 1: Try to parse the entire response as JSON directly
        try {
          jsonObject = JSON.parse(responseText.trim());
          logger.info("Successfully parsed entire response as JSON");
        } catch (directParseError) {
          // Strategy 2: Find JSON between triple backticks (markdown code blocks)
          const tripleBacktickMatch = responseText.match(
            /```(?:json)?\s*(\{[\s\S]*?\})\s*```/
          );
          if (tripleBacktickMatch) {
            try {
              jsonObject = JSON.parse(tripleBacktickMatch[1].trim());
              logger.info("Successfully extracted JSON from triple backticks");
            } catch (tripleBacktickError) {
              logger.warn(
                "Found JSON-like content in triple backticks but failed to parse",
                {
                  error: tripleBacktickError,
                  content: tripleBacktickMatch[1],
                }
              );
            }
          }

          // Strategy 3: Find JSON between single backticks
          if (!jsonObject) {
            const singleBacktickMatch = responseText.match(
              /`\s*(\{[\s\S]*?\})\s*`/
            );
            if (singleBacktickMatch) {
              try {
                jsonObject = JSON.parse(singleBacktickMatch[1].trim());
                logger.info(
                  "Successfully extracted JSON from single backticks"
                );
              } catch (singleBacktickError) {
                logger.warn(
                  "Found JSON-like content in single backticks but failed to parse",
                  {
                    error: singleBacktickError,
                    content: singleBacktickMatch[1],
                  }
                );
              }
            }
          }

          // Strategy 4: Find any JSON-like object in the text
          if (!jsonObject) {
            const jsonMatch = responseText.match(/(\{[\s\S]*\})/);
            if (jsonMatch) {
              try {
                jsonObject = JSON.parse(jsonMatch[0].trim());
                logger.info("Successfully extracted JSON from text");
              } catch (jsonMatchError) {
                logger.warn("Found JSON-like content but failed to parse", {
                  error: jsonMatchError,
                  content: jsonMatch[0],
                });
              }
            }
          }
        }

        // If we still don't have valid JSON, try one last approach - look for the first { and last }
        if (!jsonObject) {
          const firstBrace = responseText.indexOf("{");
          const lastBrace = responseText.lastIndexOf("}");

          if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
            const potentialJson = responseText.substring(
              firstBrace,
              lastBrace + 1
            );
            try {
              jsonObject = JSON.parse(potentialJson);
              logger.info("Successfully extracted JSON using brace positions");
            } catch (braceError) {
              logger.warn("Failed to parse JSON using brace positions", {
                error: braceError,
                content: potentialJson,
              });
            }
          }
        }

        // If we still don't have valid JSON, give up
        if (!jsonObject) {
          logger.error("Could not extract valid JSON from Gemini response");
          logger.debug("Raw response:", responseText);
          throw new Error("Failed to extract valid JSON from AI response");
        }

        // Normalize the result structure
        const result = this.normalizeContractAnalysisResult(jsonObject);

        // Extract confidence scores and restructure the response
        const confidenceScores = this.extractConfidenceScores(jsonObject);

        // Store the raw result for comprehensive metadata extraction
        (result as any).rawResult = jsonObject;

        // Add confidence scores to the result
        (result as any).confidence_scores = confidenceScores;

        logger.info(
          "Successfully extracted and normalized metadata from Agreement Document"
        );

        return result;
      } catch (error) {
        logger.error("Error processing Gemini API response", { error });

        // For timeout errors, try a simplified extraction without clause analysis
        if (
          (error as any)?.code === "ECONNABORTED" ||
          (error as any)?.message?.includes("timeout")
        ) {
          logger.warn(
            "Request timed out, attempting simplified extraction without clause analysis"
          );
          try {
            // Use the same method but with a simplified prompt (handled by reduced clause count)
            logger.info(
              "Falling back to basic contract analysis without detailed clause extraction"
            );
          } catch (fallbackError) {
            logger.error("Fallback extraction also failed", { fallbackError });
          }
        }

        // Return a minimal valid result rather than an empty object
        return {
          title: path.basename(fileName, path.extname(fileName)),
          value: {
            amount: "Not specified",
            currency: "Not specified",
          },
        };
      }
    } catch (error) {
      logger.error("Error analyzing Agreement Document", { error });
      return {};
    }
  }

  /**
   * Extracts metadata from a Agreement Document using Gemini AI
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @returns Extracted metadata in key-value format
   */
  async extractContractMetadata(
    documentBuffer: Buffer,
    fileName: string
  ): Promise<Record<string, any>> {
    try {
      logger.info(`Extracting metadata from Agreement Document: ${fileName}`);

      // Use the enhanced analyzeContractDocument method and convert to a simple key-value format
      const analysisResult = await this.analyzeContractDocument(
        documentBuffer,
        fileName
      );

      // Convert the structured analysis result to a flattened key-value format
      const metadata: Record<string, any> = {};

      // Define all expected fields that AI should attempt to extract
      const expectedFields = [
        "title",
        "contractType",
        "provider",
        "client",
        "contract_id",
        "effective_date",
        "end_date",
        "total_amount",
        "payment_terms",
        "governing_law",
        "agreement_type",
        "contract_classification",
        "renewal_terms",
        "line_items",
        "signatures",
        "liability",
        "payment_terms_detailed",
        "sla",
        "termination",
        "contract_value",
        "contract_description",
        "licensing",
        "rights",
        "indemnification",
        "data_privacy",
        "true_up_down",
        "service_credits",
        "governance",
        "reporting_obligations",
        "ipr",
        "data_breach",
        "clauses_analysis",
      ];

      // Define Oracle-specific fields
      const oracleFields = [
        "publisher",
        "reseller",
        "entitled_entity",
        "entitled_entity_country",
        "product_name",
        "raw_product_name",
        "total_quantity",
        "metric",
        "metric_definition",
        "term",
        "level",
        "limitations",
        "included_rights",
        "csi",
        "purchase_date",
        "governing_agreement",
        "support_contract_number",
        "support_start_date",
        "support_end_date",
        "original_document_name",
        "document_type",
        "license_value",
        "license_value_per_unit",
        "contractual_support_value",
        "support_value_per_year",
        "support_value_per_year_per_unit",
        "oracle_currency",
        "index_field",
        "delta",
      ];

      // Helper function to check if a value is meaningful
      const isMeaningfulValue = (value: any): boolean => {
        if (value === null || value === undefined) return false;
        if (typeof value === "string") {
          const trimmed = value.trim().toLowerCase();
          return (
            trimmed !== "" &&
            trimmed !== "n/a" &&
            trimmed !== "unknown" &&
            trimmed !== "not specified" &&
            trimmed !== "null" &&
            trimmed !== "undefined" &&
            trimmed !== "not available" &&
            trimmed !== "none"
          );
        }
        if (Array.isArray(value)) {
          return (
            value.length > 0 && value.some((item) => isMeaningfulValue(item))
          );
        }
        if (typeof value === "object") {
          return Object.values(value).some((val) => isMeaningfulValue(val));
        }
        return true;
      };

      // Add basic fields with N/A handling
      metadata.title = isMeaningfulValue(analysisResult.title)
        ? analysisResult.title
        : "N/A";
      metadata.contractType = isMeaningfulValue(analysisResult.contractType)
        ? analysisResult.contractType
        : "N/A";

      // Add parties
      if (analysisResult.parties && analysisResult.parties.length > 0) {
        metadata.parties = analysisResult.parties;

        // Also add a simplified counterparty field for backward compatibility
        const counterparty = analysisResult.parties.find(
          (p) =>
            p.role &&
            !p.role.toLowerCase().includes("company") &&
            !p.role.toLowerCase().includes("client")
        );
        if (counterparty) {
          metadata.counterparty = counterparty.name;
        }
      }

      // Add dates
      if (analysisResult.dates) {
        Object.entries(analysisResult.dates).forEach(([key, value]) => {
          if (value) metadata[key] = value;
        });
      }

      // Add value information - handle both old and new formats
      if (analysisResult.value) {
        // Check if we have the new combined format (CURRENCY:AMOUNT)
        if (
          typeof analysisResult.value === "string" &&
          analysisResult.value.includes(":")
        ) {
          // New format: "CURRENCY:AMOUNT"
          const [currency, amount] = analysisResult.value.split(":");
          if (currency && amount) {
            metadata.contractValue = analysisResult.value; // Store the combined format
            metadata.currency = this.sanitizeCurrency(currency) || undefined;

            try {
              const numericValue = this.extractNumericValue(amount);
              if (numericValue !== undefined) {
                metadata.totalValue = new Decimal(numericValue);
              }
            } catch (error) {
              logger.warn(
                `Could not convert contract value to number: ${amount}`,
                error
              );
            }
          }
        } else if (typeof analysisResult.value === "object") {
          // Old format: object with separate currency and amount fields
          if (analysisResult.value.amount) {
            try {
              const numericValue = this.extractNumericValue(
                analysisResult.value.amount
              );
              const currency =
                this.sanitizeCurrency(analysisResult.value.currency) || "USD";

              if (numericValue !== undefined) {
                // Create combined format
                metadata.contractValue = `${currency}:${numericValue}`;
                metadata.currency = currency;
                metadata.totalValue = new Decimal(numericValue);
              } else {
                metadata.contractValue = analysisResult.value.amount;
              }
            } catch (error) {
              logger.warn(
                `Could not convert contract value to number: ${analysisResult.value.amount}`,
                error
              );
              metadata.contractValue = analysisResult.value.amount;
            }
          }

          if (analysisResult.value.currency && !metadata.currency) {
            metadata.currency =
              this.sanitizeCurrency(analysisResult.value.currency) || undefined;
          }

          if (analysisResult.value.totalValue && !metadata.totalValue) {
            try {
              const numericValue = this.extractNumericValue(
                analysisResult.value.totalValue
              );
              if (numericValue !== undefined) {
                metadata.totalValue = new Decimal(numericValue);
              }
            } catch (error) {
              logger.warn(
                `Could not convert total value to number: ${analysisResult.value.totalValue}`,
                error
              );
            }
          }

          if (analysisResult.value.paymentSchedule)
            metadata.paymentSchedule = analysisResult.value.paymentSchedule;
        }
      }

      // Add payment terms
      if (analysisResult.paymentTerms) {
        metadata.paymentTerms = analysisResult.paymentTerms;
      }

      // Add renewal terms
      if (analysisResult.renewalTerms) {
        metadata.renewalTerms = analysisResult.renewalTerms;

        // Also add simplified fields for backward compatibility
        if (analysisResult.renewalTerms.isAutoRenew !== undefined) {
          metadata.isAutoRenew = analysisResult.renewalTerms.isAutoRenew;
        }
        if (analysisResult.renewalTerms.noticePeriodDays !== undefined) {
          metadata.noticePeriodDays =
            analysisResult.renewalTerms.noticePeriodDays;
        }
      }

      // Add other fields
      if (analysisResult.confidentiality)
        metadata.confidentiality = analysisResult.confidentiality;
      if (analysisResult.terminationClauses)
        metadata.terminationClauses = analysisResult.terminationClauses;
      if (analysisResult.governingLaw)
        metadata.governingLaw = analysisResult.governingLaw;
      if (analysisResult.disputeResolution)
        metadata.disputeResolution = analysisResult.disputeResolution;
      if (analysisResult.liabilityLimits)
        metadata.liabilityLimits = analysisResult.liabilityLimits;
      if (analysisResult.warranties)
        metadata.warranties = analysisResult.warranties;
      if (analysisResult.amendments)
        metadata.amendments = analysisResult.amendments;
      if (analysisResult.signatures)
        metadata.signatures = analysisResult.signatures;

      // Add any other metadata
      if (analysisResult.otherMetadata) {
        Object.entries(analysisResult.otherMetadata).forEach(([key, value]) => {
          metadata[key] = value;
        });
      }

      // Store the original analysis result for reference
      metadata.analysisResult = analysisResult;

      // Add comprehensive metadata from new schema with N/A handling
      // Access the raw result from the analysis result
      const rawData = (analysisResult as any).rawResult || analysisResult;

      // Apply N/A handling to all expected fields
      metadata.contractId = isMeaningfulValue(rawData.contract_id)
        ? rawData.contract_id
        : "N/A";
      metadata.contractNumber = isMeaningfulValue(rawData.contract_id)
        ? rawData.contract_id
        : "N/A";
      metadata.provider = isMeaningfulValue(rawData.provider)
        ? rawData.provider
        : "N/A";
      metadata.providerName = isMeaningfulValue(rawData.provider)
        ? rawData.provider
        : "N/A";
      metadata.client = isMeaningfulValue(rawData.client)
        ? rawData.client
        : "N/A";
      metadata.agreementType = isMeaningfulValue(rawData.agreement_type)
        ? this.validateAgreementType(rawData.agreement_type)
        : "OTHER";
      metadata.contractClassification = isMeaningfulValue(
        rawData.contract_classification
      )
        ? rawData.contract_classification
        : "N/A";
      metadata.governingLaw = isMeaningfulValue(rawData.governing_law)
        ? rawData.governing_law
        : "N/A";

      // Add detailed Agreement Analysis fields with N/A handling
      metadata.renewalTermsDetailed = isMeaningfulValue(rawData.renewal_terms)
        ? rawData.renewal_terms
        : "N/A";
      metadata.lineItems = isMeaningfulValue(rawData.line_items)
        ? rawData.line_items
        : "N/A";
      metadata.liability = isMeaningfulValue(rawData.liability)
        ? rawData.liability
        : "N/A";
      metadata.paymentTermsDetailed = isMeaningfulValue(
        rawData.payment_terms_detailed
      )
        ? rawData.payment_terms_detailed
        : "N/A";
      metadata.sla = isMeaningfulValue(rawData.sla) ? rawData.sla : "N/A";
      metadata.termination = isMeaningfulValue(rawData.termination)
        ? rawData.termination
        : "N/A";
      metadata.contractValueCategory = isMeaningfulValue(rawData.contract_value)
        ? rawData.contract_value
        : "N/A";

      // Extract contract description from dynamic fields
      const dynamicFields = rawData.dynamic_fields || {};
      metadata.contractDescription = isMeaningfulValue(
        dynamicFields.contract_description?.value
      )
        ? dynamicFields.contract_description.value
        : "N/A";

      metadata.licensing = isMeaningfulValue(rawData.licensing)
        ? rawData.licensing
        : "N/A";
      metadata.rights = isMeaningfulValue(rawData.rights)
        ? rawData.rights
        : "N/A";
      metadata.indemnification = isMeaningfulValue(rawData.indemnification)
        ? rawData.indemnification
        : "N/A";
      metadata.dataPrivacy = isMeaningfulValue(rawData.data_privacy)
        ? rawData.data_privacy
        : "N/A";
      metadata.trueUpDown = isMeaningfulValue(rawData.true_up_down)
        ? rawData.true_up_down
        : "N/A";
      metadata.serviceCredits = isMeaningfulValue(rawData.service_credits)
        ? rawData.service_credits
        : "N/A";
      metadata.governance = isMeaningfulValue(rawData.governance)
        ? rawData.governance
        : "N/A";
      metadata.reportingObligations = isMeaningfulValue(
        rawData.reporting_obligations
      )
        ? rawData.reporting_obligations
        : "N/A";
      metadata.ipr = isMeaningfulValue(rawData.ipr) ? rawData.ipr : "N/A";
      metadata.dataBreach = isMeaningfulValue(rawData.data_breach)
        ? rawData.data_breach
        : "N/A";

      // Check if this is an Oracle contract and extract Oracle-specific fields
      const isOracleContract = this.isOracleContract(rawData);
      if (isOracleContract) {
        // Extract Oracle-specific fields from oracle_fields object or root level
        const oracleData = rawData.oracle_fields || rawData;

        // Store Oracle fields in nested oracleFields structure
        const oracleFields = [
          "publisher",
          "reseller",
          "entitled_entity",
          "entitled_entity_country",
          "product_name",
          "raw_product_name",
          "total_quantity",
          "metric",
          "metric_definition",
          "term",
          "level",
          "limitations",
          "included_rights",
          "csi",
          "purchase_date",
          "governing_agreement",
          "support_contract_number",
          "support_start_date",
          "support_end_date",
          "original_document_name",
          "document_type",
          "license_value",
          "license_value_per_unit",
          "contractual_support_value",
          "support_value_per_year",
          "support_value_per_year_per_unit",
          "oracle_currency",
          "index_field",
          "delta",
        ];

        // Create nested oracleFields object
        const oracleFieldsData: Record<string, any> = {};
        oracleFields.forEach((field) => {
          const value = oracleData[field];
          oracleFieldsData[field] = isMeaningfulValue(value) ? value : "N/A";
        });

        // Store Oracle fields in nested structure
        metadata.oracleFields = oracleFieldsData;

        // Smart mapping: Use Oracle fields to populate standard fields if they're more meaningful
        if (
          isMeaningfulValue(oracleData.publisher) &&
          !isMeaningfulValue(metadata.provider)
        ) {
          metadata.provider = oracleData.publisher;
        }
        if (
          isMeaningfulValue(oracleData.entitled_entity) &&
          !isMeaningfulValue(metadata.client)
        ) {
          metadata.client = oracleData.entitled_entity;
        }
        if (
          isMeaningfulValue(oracleData.support_start_date) &&
          !isMeaningfulValue(rawData.effective_date)
        ) {
          metadata.effectiveDate = oracleData.support_start_date;
        }
        if (
          isMeaningfulValue(oracleData.support_end_date) &&
          !isMeaningfulValue(rawData.end_date)
        ) {
          metadata.endDate = oracleData.support_end_date;
        }
        if (
          isMeaningfulValue(oracleData.document_type) &&
          !isMeaningfulValue(metadata.agreementType)
        ) {
          metadata.agreementType = this.validateAgreementType(
            oracleData.document_type
          );
        }
      }

      // Add confidence scores if available
      if ((analysisResult as any).confidence_scores) {
        metadata.confidence_scores = (analysisResult as any).confidence_scores;
      }

      // Post-process to ensure all "not defined" values are converted to "N/A"
      const normalizedMetadata = this.normalizeNotDefinedValues(metadata);

      return normalizedMetadata;
    } catch (error) {
      logger.error("Error extracting contract metadata:", error);
      // Return a minimal metadata object rather than throwing
      return {
        title: path.basename(fileName, path.extname(fileName)),
        contractValue: "Not specified",
        currency: "USD",
        extractionError: (error as Error).message,
      };
    }
  }

  /**
   * Process OCR for a document and store results in ContractVersion
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param contractId - Contract ID
   * @returns OCR result and whether OCR was successful
   */
  async processAndStoreOCR(
    documentBuffer: Buffer,
    fileName: string,
    contractId: string
  ): Promise<{ ocrResult: OCRResult | null; ocrSuccess: boolean }> {
    try {
      logger.info(`Starting OCR processing for contract: ${contractId}, file: ${fileName}`);

      // Extract text using Azure OCR
      const ocrResult = await this.azureOCRService.extractTextFromDocument(
        documentBuffer,
        fileName
      );

      // Find the current contract version to update with OCR data
      const contractVersion = await this.prisma.contractVersion.findFirst({
        where: { contractId },
        orderBy: { versionNumber: 'desc' }
      });

      if (!contractVersion) {
        logger.error(`No contract version found for contract: ${contractId}. OCR processing cannot proceed without a ContractVersion record.`);
        return { ocrResult: null, ocrSuccess: false };
      }

      // Update contract version with OCR results
      await this.prisma.contractVersion.update({
        where: { id: contractVersion.id },
        data: {
          ocrText: ocrResult.text,
          ocrProcessedAt: new Date(),
          ocrConfidence: ocrResult.confidence,
          ocrStatus: 'SUCCESS',
          ocrMetadata: {
            pageCount: ocrResult.pageCount,
            processingTimeMs: ocrResult.processingTimeMs,
            pages: ocrResult.metadata.pages
          }
        }
      });

      logger.info(
        `OCR processing completed for contract: ${contractId}. Extracted ${ocrResult.text.length} characters from ${ocrResult.pageCount} pages`
      );

      return { ocrResult, ocrSuccess: true };
    } catch (error) {
      logger.error(`OCR processing failed for contract: ${contractId}`, error);

      // Update contract version with failure status
      try {
        const contractVersion = await this.prisma.contractVersion.findFirst({
          where: { contractId },
          orderBy: { versionNumber: 'desc' }
        });

        if (contractVersion) {
          await this.prisma.contractVersion.update({
            where: { id: contractVersion.id },
            data: {
              ocrStatus: 'FAILED',
              ocrProcessedAt: new Date(),
              ocrMetadata: {
                error: (error as Error).message
              }
            }
          });
        }
      } catch (updateError) {
        logger.error(`Failed to update OCR failure status for contract: ${contractId}`, updateError);
      }

      // According to user preference: full dependency on OCR, fail if OCR fails
      throw new Error(`OCR processing failed: ${(error as Error).message}`);
    }
  }

  /**
   * Extracts contract data using sequential three-tier system to avoid duplication and saves to database
   * Step 0: Process OCR to extract text from document
   * Step 1: Extract fixed fields to identify supplier (using OCR text)
   * Step 2: Extract supplier-specific special fields based on identified supplier (using OCR text)
   * Step 3: Extract dynamic fields while excluding already extracted supplier-specific fields (using OCR text)
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param contractId - Contract ID to associate with extraction
   * @param tenantId - Tenant ID
   * @returns Three-tier extraction result
   */
  async extractAndSaveThreeTierData(
    documentBuffer: Buffer,
    fileName: string,
    contractId: string,
    tenantId: string
  ): Promise<{
    fixedFields: FixedFields;
    dynamicFields: DynamicCategoricalFields;
    specialFields: SpecialFields;
    documentSummary?: DocumentSummary;
    analysisFields?: AnalysisFields;
    extractionId: string;
  }> {
    const startTime = Date.now();

    try {
      logger.info(
        `Starting sequential three-tier extraction for contract: ${contractId}`
      );

      // STEP 0: Process OCR and store in ContractVersion (if not already done)
      logger.info(`Step 0: OCR processing for contract: ${contractId}`);
      try {
        await this.processAndStoreOCR(
          documentBuffer,
          fileName,
          contractId
        );
        logger.info(`OCR processing completed for contract: ${contractId}`);
      } catch (ocrError) {
        logger.warn(`OCR processing failed for contract: ${contractId}, continuing with document buffer fallback:`, ocrError);
        // Continue with extraction - functions will fall back to document buffer
      }

      // STEP 1: Extract only fixed fields to identify supplier (will use OCR text automatically)
      logger.info(`Step 1: Fixed fields extraction for contract: ${contractId}`);
      const fixedFields = await this.extractFixedFieldsOnly(
        documentBuffer,
        fileName,
        contractId
      );

      if (!fixedFields) {
        throw new Error("Failed to extract fixed fields");
      }

      // Generate detailed document summary in parallel (don't await to keep it parallel)
      let documentSummary: any = undefined;
      const summaryPromise = this.generateContractSummaryFromDocument(
        documentBuffer,
        fileName,
        undefined,
        contractId
      )
        .then((summaryText) => {
          return {
            value: summaryText,
            confidence: 0.85, // Default confidence for generated summaries
            extractionDate: new Date(),
            processingTimeMs: Date.now() - startTime,
          };
        })
        .catch((error) => {
          logger.warn(
            `Failed to generate document summary for contract ${contractId}:`,
            error
          );
          // Return undefined if summary generation fails
          return undefined;
        });

      // STEP 2: Identify supplier and perform targeted special fields extraction
      logger.info(
        `Step 2: Supplier identification and targeted extraction for contract: ${contractId}`
      );
      const providerValue = fixedFields.provider?.value;
      let supplierSpecificFields: Record<string, any> = {};

      if (providerValue && providerValue !== "N/A") {
        logger.info(
          `Identified provider: ${providerValue} for contract: ${contractId}`
        );

        // Check if this supplier is supported for special field extraction
        const isSupported =
          await this.supplierMappingService.isSupplierSupported(providerValue);

        if (isSupported) {
          logger.info(
            `Performing targeted extraction for supported supplier: ${providerValue}`
          );

          // Make a separate API call to Gemini for supplier-specific fields (will use OCR if available)
          const extractedFields = await this.extractSupplierSpecificFields(
            documentBuffer,
            fileName,
            providerValue,
            contractId
          );

          // Store the extracted fields under the normalized supplier name
          const normalizedSupplier =
            this.supplierMappingService.normalizeSupplierName(providerValue);
          if (normalizedSupplier && Object.keys(extractedFields).length > 0) {
            supplierSpecificFields[normalizedSupplier] = extractedFields;

            // Count total fields across all categories
            let totalFieldCount = 0;
            for (const category of Object.values(extractedFields)) {
              if (typeof category === "object" && category !== null) {
                totalFieldCount += Object.keys(category).length;
              }
            }

            logger.info(
              `Successfully extracted ${totalFieldCount} special fields across ${Object.keys(extractedFields).length
              } categories for ${normalizedSupplier}`
            );
          }
        } else {
          logger.info(
            `Supplier ${providerValue} not supported for special field extraction`
          );
        }
      } else {
        logger.warn(
          `No provider identified for contract: ${contractId}, skipping special field extraction`
        );
      }

      // STEP 3: Extract dynamic fields while excluding supplier-specific fields
      logger.info(
        `Step 3: Dynamic fields extraction with exclusion for contract: ${contractId}`
      );
      let dynamicFields: DynamicCategoricalFields;

      try {
        dynamicFields = await this.extractDynamicFieldsWithExclusion(
          documentBuffer,
          fileName,
          providerValue,
          supplierSpecificFields,
          contractId
        );
        logger.info(
          `Successfully extracted dynamic fields for contract: ${contractId}`
        );
      } catch (error) {
        logger.warn(
          `Failed to extract dynamic fields for contract ${contractId}:`,
          error
        );
        // Fallback to empty dynamic fields
        dynamicFields = {};
      }

      const processingTime = Date.now() - startTime;

      // Add fallback logic for contract_status and contract_term if not extracted by LLM
      const fixedFieldsWithFallbacks = this.addContractStatusAndTermFallbacks(
        fixedFields
      );

      // Calculate derived fields and add them to fixed fields
      // Note: Calculation service primarily uses fixed fields, dynamic fields are rarely needed
      const enhancedFixedFields =
        this.contractCalculationService.calculateAndAddDerivedFields(
          fixedFieldsWithFallbacks,
          {} // Empty dynamic fields - calculations use fixed fields
        );

      // Skip auto-assign folder - let users create folders manually via smart grouping or suggestions
      const folderId = null;

      // Wait for the detailed summary to complete
      documentSummary = await summaryPromise;

      // Save to database with categorized structure
      const extraction = await this.contractExtractionRepository.create({
        contractId,
        tenantId,
        fixedFields: enhancedFixedFields,
        dynamicFields: dynamicFields,
        specialFields: supplierSpecificFields as SpecialFields,
        documentSummary,
        extractionVersion: "1.0",
        processingTimeMs: processingTime,
        modelUsed: "gemini-2.5-pro",
        folderId: folderId || undefined,
        ocrUsedForExtraction: true, // Mark that OCR was used for this extraction
      });

      // Background processing for analysis fields (for all contracts)
      logger.info(
        `Contract ${contractId} starting analysis fields background processing.`
      );

      // Run analysis fields extraction in background for all contracts (don't await)
      this.processAnalysisFieldsInBackground(
        documentBuffer,
        fileName,
        contractId,
        tenantId
      ).catch((error: any) => {
        logger.error(
          `Analysis fields background processing failed for contract ${contractId}:`,
          error
        );
      });

      logger.info(
        `Successfully saved three-tier extraction for contract: ${contractId}`
      );

      return {
        fixedFields: extraction.fixedFields,
        dynamicFields: extraction.dynamicFields,
        specialFields: extraction.specialFields,
        documentSummary: extraction.documentSummary,
        analysisFields: extraction.analysisFields,
        extractionId: extraction.id,
      };
    } catch (error) {
      logger.error(
        `Error in three-tier extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Extracts analysis fields for entitlement analysis from any software contract
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param contractId - Contract ID (optional, for OCR text lookup)
   * @returns Extracted analysis fields
   */
  async extractAnalysisFields(
    documentBuffer: Buffer,
    fileName: string,
    contractId?: string
  ): Promise<Record<string, any>> {
    try {
      logger.info(
        `Starting analysis fields extraction for: ${fileName} `
      );

      // Check if OCR text is available for this contract
      let useOCRText = false;
      let ocrText = '';

      if (contractId) {
        const ocrTextResult = await this.getOCRTextForContract(contractId);
        if (ocrTextResult) {
          useOCRText = true;
          ocrText = ocrTextResult;
          logger.info(`Using OCR text for analysis fields extraction: ${fileName}`);
        }
      }

      if (!useOCRText) {
        // Fallback to document buffer
        logger.info(`Using document buffer for analysis fields extraction: ${fileName}`);
      }

      // Convert document to base64 (only if not using OCR)
      const base64Content = useOCRText ? '' : documentBuffer.toString("base64");
      const mimeType = useOCRText ? '' : this.getMimeType(fileName);

      logger.debug(`Detected MIME type: ${mimeType} `);
      logger.debug(`Base64 content length: ${base64Content.length} characters`);

      // Create provider-agnostic analysis prompt
      const prompt = `You are an expert software contract analysis system designed to extract specific entitlement analysis fields from software licensing documents.

**CRITICAL INSTRUCTION: EXTRACT SOFTWARE ENTITLEMENT DATA**

Extract the following entitlement analysis fields with SHORT, CRISP, TO-THE-POINT values:

1. **Publisher** - Software vendor/publisher (e.g., Oracle, Microsoft, SAP, IBM, etc.)
2. **Reseller** - Authorized reseller/partner (if applicable)
3. **Entitled Entity** - Customer/licensee organization name
4. **Entitled Entity Country** - Country where entitled entity is located
5. **Product Name** - Specific software product/service name
6. **Purchasing** - Array of all license/product line items from tables (see structure below)
7. **Total Quantity** - Sum of all quantities from purchasing array
8. **Metric** - Licensing metric (Named User, Processor, Device, Concurrent User, etc.)
9. **Metric Definition** - Definition of the licensing metric
10. **Term** - License term duration
11. **Level** - Support level or service tier
12. **Limitations** - Usage limitations or restrictions
13. **Included Rights** - Rights included with the license
14. **CSI** - Customer Support Identifier (or equivalent vendor ID)
15. **Purchase Date** - Date of purchase/order
16. **Governing Agreement** - Master agreement reference
17. **Support Contract Number** - Support contract identifier
18. **Support Start Date** - Support coverage start date
19. **Support End Date** - Support coverage end date
20. **Original Document Name** - Source document name
21. **Document Type** - Type of software document (License, Support, Maintenance, etc.)
22. **License Value** - Total license cost
23. **License Value per Unit** - Cost per license unit
24. **Contractual Support Value** - Total support cost
25. **Support Value per Year** - Annual support cost
26. **Support Value per Year per Unit** - Annual support cost per unit
27. **Currency** - Currency code (USD, EUR, etc.)
28. **Index** - Document index or reference number
29. **Delta** - Changes from previous version/agreement

**TABLE EXTRACTION RULES:**
- SCAN the entire document for licensing/purchasing tables
- EXTRACT ALL ROWS from tables (not just the first row)
        - Each table row should become one item in the purchasing array
          - Look for tables with columns like: Product, SKU, Quantity, Price, License Type, Part Number, etc.
- Include all line items, even if table formatting is inconsistent
  - When extracting from tables, apply unit price calculation rules if any pricing component is missing
    - Be smart about identifying unit prices vs total prices based on column headers and context

**DEDUPLICATION RULES:**
- If the same license_type and license_value combination appears multiple times (even across different categories, countries, or sections), consolidate them into a SINGLE line item
          - Sum up the quantities for duplicate license_type + license_value combinations
            - Sum up the total prices for duplicate license_type + license_value combinations
- This prevents duplicate entries when the same license is mentioned in multiple contexts (e.g., different geographical regions, categories, or document sections)

**DEDUPLICATION EXAMPLE:**
                  If you find these entries in different sections:
- Section A: "Oracle Database Enterprise", "USD:50000", quantity "5", price "USD:250000"
  - Section B: "Oracle Database Enterprise", "USD:50000", quantity "3", price "USD:150000"
    - Section C: "Oracle WebLogic Server", "USD:30000", quantity "2", price "USD:60000"

Consolidate to:
- "Oracle Database Enterprise", "USD:50000", quantity "8", price "USD:400000"
  - "Oracle WebLogic Server", "USD:30000", quantity "2", price "USD:60000"

**UNIT PRICE CALCULATION EXAMPLE:**
      If you find in the document:
- "Microsoft Office 365", quantity "100", total price "USD:120000" (but no unit price mentioned)
Calculate: license_value = "USD:1200" (120000 ÷ 100)

If you find:
- "Adobe Creative Suite", unit price "USD:600", quantity "50" (but no total price mentioned)
Calculate: price = "USD:30000" (600 × 50)

**PURCHASING STRUCTURE:**
The purchasing field should be an object organized by contract years. Look for:
- Multi-year contracts with different quantities/prices per year
- Annual breakdowns, year-specific pricing tables
          - Renewal schedules with changing terms

PREFERRED FORMAT (when year-wise data is available):
{
  "purchasing": {
    "YEAR 1": [
      {
        "license_type": "Software Product Name/Edition",
        "license_value": "USD:50000",
        "quantity": "5",
        "price": "USD:250000"
      }
    ],
      "YEAR 2": [
        {
          "license_type": "Software Product Name/Edition",
          "license_value": "USD:30000",
          "quantity": "10",
          "price": "USD:300000"
        }
      ]
  }
}

FALLBACK FORMAT (when no year-wise breakdown is found):
{
  "purchasing": {
    "TOTAL": [
      {
        "license_type": "Software Product Name/Edition",
        "license_value": "USD:36667",
        "quantity": "15",
        "price": "USD:550000"
      }
    ]
  }
}

**EXTRACTION RULES:**
- Values must be SHORT and CRISP (1-10 words maximum)
    - Extract only explicitly stated information
      - Use "N/A" for missing fields
- For currency amounts, use format "CURRENCY:AMOUNT" (e.g., "USD:50000")
- For dates, use YYYY-MM-DD format

**UNIT PRICE CALCULATION RULES:**
              - If you find quantity and total price but no unit price, CALCULATE the unit price automatically
- Unit price = Total price ÷ Quantity ÷ Contract term (in months)
- Example: If quantity is "10", price is "USD:50000", and contract term is "24 months", then license_value should be "USD:208.33" (50000 ÷ 10 ÷ 24)
                    - If you find unit price and quantity but no total price, CALCULATE the total price automatically
- Total price = Unit price × Quantity × Contract term (in months)
                        - Example: If license_value is "USD:208.33", quantity is "10", and contract term is "24 months", then price should be "USD:50000"
- Always ensure mathematical consistency between quantity, license_value (monthly unit price), price (total price), and contract term
- Extract contract term from document (e.g., "24 months", "3 years") and convert to months for calculations
                              - If all values are explicitly stated in the document, verify they are mathematically consistent
                                - Round calculated values to 2 decimal places for currency amounts

**OUTPUT FORMAT:**
                                    Return a valid JSON object with this exact structure:

{
  "publisher": {"value": "Software Vendor Corp", "confidence": 0.95},
  "reseller": {"value": "Authorized Partner Inc", "confidence": 0.90},
  "entitled_entity": {"value": "Customer Corp", "confidence": 0.95},
  "entitled_entity_country": {"value": "United States", "confidence": 0.90},
  "product_name": {"value": "Software Product Enterprise Edition", "confidence": 0.95},
  "purchasing": {
    "YEAR 1": [
      {
        "license_type": "Software Product Enterprise Edition",
        "license_value": "USD:50000",
        "quantity": "5",
        "price": "USD:250000"
      }
    ],
      "YEAR 2": [
        {
          "license_type": "Additional Module/Component",
          "license_value": "USD:30000",
          "quantity": "3",
          "price": "USD:90000"
        }
      ]
  },
  "total_quantity": {"value": "8", "confidence": 0.90},
  "metric": {"value": "Named User", "confidence": 0.95},
  "metric_definition": {"value": "Individual authorized to use software", "confidence": 0.85},
  "term": {"value": "Perpetual", "confidence": 0.90},
  "level": {"value": "Premium Support", "confidence": 0.85},
  "limitations": {"value": "Internal business operations only", "confidence": 0.80},
  "included_rights": {"value": "Use, backup, disaster recovery", "confidence": 0.85},
  "csi": {"value": "12345678", "confidence": 0.90},
  "purchase_date": {"value": "2024-01-15", "confidence": 0.95},
  "governing_agreement": {"value": "Master Software Agreement", "confidence": 0.85},
  "support_contract_number": {"value": "SUP-2024-001", "confidence": 0.90},
  "support_start_date": {"value": "2024-01-15", "confidence": 0.95},
  "support_end_date": {"value": "2027-01-14", "confidence": 0.95},
  "original_document_name": {"value": "${fileName}", "confidence": 1.0},
  "document_type": {"value": "License Agreement", "confidence": 0.90},
  "license_value": {"value": "USD:500000", "confidence": 0.85},
  "license_value_per_unit": {"value": "USD:50000", "confidence": 0.85},
  "contractual_support_value": {"value": "USD:110000", "confidence": 0.85},
  "support_value_per_year": {"value": "USD:110000", "confidence": 0.85},
  "support_value_per_year_per_unit": {"value": "USD:11000", "confidence": 0.85},
  "currency": {"value": "USD", "confidence": 0.95},
  "index": {"value": "DOC-001", "confidence": 0.80},
  "delta": {"value": "New agreement", "confidence": 0.75}
}

**NOTE:** If no year-wise breakdown is found in the document, use the TOTAL format instead:
"purchasing": {
  "TOTAL": [
    {
      "license_type": "Software Product Enterprise Edition",
      "license_value": "USD:80000",
      "quantity": "8",
      "price": "USD:340000"
    }
  ]
}

**IMPORTANT:** Your response MUST be a valid JSON object matching the exact structure shown above. Do not include explanatory text outside the JSON.`;

      logger.info(
        `Starting analysis fields extraction for: ${fileName}`
      );

      // Call Gemini API using @google/genai SDK with either OCR text or document buffer
      const startTime = Date.now();
      const parts: any[] = [{ text: prompt }];

      if (useOCRText) {
        // Add OCR text to the prompt
        parts[0].text += `\n\nContract Text:\n${ocrText}`;
      } else {
        // Add document as inline data
        parts.push({
          inlineData: {
            mimeType,
            data: base64Content,
          },
        });
      }

      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.1,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 16384,
        },
        contents: {
          role: "user",
          parts,
        },
      });

      // Extract response text
      let responseText = "";
      if (response && response.text) {
        responseText = response.text;
      } else if (
        response &&
        response.candidates &&
        response.candidates[0] &&
        response.candidates[0].content
      ) {
        // Handle different response structure
        const content = response.candidates[0].content;
        if (content.parts && content.parts[0] && content.parts[0].text) {
          responseText = content.parts[0].text;
        }
      } else {
        logger.warn(
          "Unexpected response structure from Gemini API for Oracle analysis"
        );
        return {};
      }

      if (!responseText) {
        logger.warn("Empty response from Gemini API for Oracle analysis");
        return {};
      }

      // Parse JSON response
      let jsonObject = null;

      try {
        // Clean up the response text more thoroughly
        let cleanedResponse = responseText.trim();

        // Remove markdown code blocks if present
        cleanedResponse = cleanedResponse
          .replace(/^```json\s*/i, "")
          .replace(/\s*```$/, "");
        cleanedResponse = cleanedResponse
          .replace(/^```\s*/, "")
          .replace(/\s*```$/, "");

        // Remove any explanatory text before or after JSON
        const jsonStart = cleanedResponse.indexOf("{");
        const jsonEnd = cleanedResponse.lastIndexOf("}");

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          cleanedResponse = cleanedResponse.substring(jsonStart, jsonEnd + 1);
        }

        jsonObject = JSON.parse(cleanedResponse);
        logger.info("Successfully parsed Oracle analysis fields JSON");
      } catch (parseError) {
        logger.warn(
          "Initial JSON parse failed, attempting to extract JSON from response"
        );

        // Try multiple approaches to extract JSON from response
        let extractedJson = null;

        // Approach 1: Look for JSON object in the response
        const jsonMatch = responseText.match(/(\{[\s\S]*\})/);
        if (jsonMatch) {
          extractedJson = jsonMatch[0].trim();
        }

        // Approach 2: If no JSON found, try looking for content between specific markers
        if (!extractedJson) {
          const lines = responseText.split("\n");
          let jsonLines = [];
          let inJson = false;

          for (const line of lines) {
            if (line.trim().startsWith("{")) {
              inJson = true;
            }
            if (inJson) {
              jsonLines.push(line);
            }
            if (line.trim().endsWith("}") && inJson) {
              break;
            }
          }

          if (jsonLines.length > 0) {
            extractedJson = jsonLines.join("\n").trim();
          }
        }

        if (extractedJson) {
          try {
            jsonObject = JSON.parse(extractedJson);
            logger.info(
              "Successfully extracted Oracle analysis fields JSON from text"
            );
          } catch (extractError) {
            logger.warn("Failed to parse Oracle analysis fields JSON");
            return {};
          }
        } else {
          logger.warn("No JSON found in Oracle analysis response");
          return {};
        }
      }

      if (!jsonObject) {
        logger.error(
          "Could not extract valid JSON from analysis response, returning basic fallback structure"
        );
        // Return a basic structure with some extracted information if possible
        return {
          publisher: { value: "Unknown Publisher", confidence: 0.5 },
          original_document_name: { value: fileName, confidence: 1.0 },
          document_type: { value: "Software License Agreement", confidence: 0.5 },
          extractionDate: new Date().toISOString(),
          processingTimeMs: Date.now() - startTime,
          fallback: true,
        };
      }

      logger.info(
        `Successfully extracted ${Object.keys(jsonObject).length
        } analysis fields`
      );

      return jsonObject;
    } catch (error) {
      logger.error("Error extracting Oracle analysis fields:", {
        error: (error as Error).message,
        stack: (error as Error).stack,
        fileName,
        errorDetails: error
      });

      // Log more specific error information
      if ((error as any).response) {
        logger.error("API Response Error:", {
          status: (error as any).response.status,
          statusText: (error as any).response.statusText,
          data: (error as any).response.data
        });
      }

      return {};
    }
  }

  /**
   * Performs targeted extraction of supplier-specific special fields
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param supplierName - Identified supplier name
   * @returns Extracted special fields for the supplier
   */
  async extractSupplierSpecificFields(
    documentBuffer: Buffer,
    fileName: string,
    supplierName: string,
    contractId?: string
  ): Promise<Record<string, any>> {
    try {
      logger.info(`Starting supplier-specific extraction for: ${supplierName}`);

      // Check if OCR text is available for this contract
      let useOCRText = false;
      let ocrText = '';

      if (contractId) {
        const ocrTextResult = await this.getOCRTextForContract(contractId);
        if (ocrTextResult) {
          useOCRText = true;
          ocrText = ocrTextResult;
          logger.info(`Using OCR text for supplier-specific extraction: ${fileName}`);
        }
      }

      if (!useOCRText) {
        logger.info(`Using document buffer for supplier-specific extraction: ${fileName}`);
      }

      // Check if supplier is supported
      const isSupported = await this.supplierMappingService.isSupplierSupported(
        supplierName
      );
      if (!isSupported) {
        logger.warn(
          `Supplier ${supplierName} is not supported for special field extraction`
        );
        return {};
      }

      // Get supplier-specific fields and detailed mapping
      const supplierFields =
        await this.supplierMappingService.getSupplierFields(supplierName);

      if (supplierFields.length === 0) {
        logger.warn(`No fields defined for supplier: ${supplierName}`);
        return {};
      }

      // Generate supplier-specific prompt using the new categorical format
      const prompt =
        await this.supplierMappingService.generateCategoricalPrompt(
          supplierName
        );

      // Convert document to base64
      const base64Content = documentBuffer.toString("base64");
      const mimeType = this.getMimeType(fileName);

      logger.info(
        `Making targeted extraction API call for supplier: ${supplierName}`
      );

      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.05,
          topP: 0.7,
          topK: 15,
          maxOutputTokens: 16384,
        },
        contents: {
          role: "user",
          parts: [
            {
              text: prompt,
            },
            {
              inlineData: {
                mimeType,
                data: base64Content,
              },
            },
          ],
        },
      });

      // Extract response text
      let responseText = "";
      if (response && response.text) {
        responseText = response.text;
      } else {
        logger.warn(
          "Unexpected response structure from Gemini API for supplier extraction"
        );
        return {};
      }

      if (!responseText) {
        logger.warn("Empty response from Gemini API for supplier extraction");
        return {};
      }

      // Parse JSON response
      let jsonObject = null;
      try {
        jsonObject = JSON.parse(responseText.trim());
      } catch (directParseError) {
        // Try to extract JSON from markdown code blocks
        const jsonMatch = responseText.match(
          /```(?:json)?\s*(\{[\s\S]*?\})\s*```/
        );
        if (jsonMatch) {
          try {
            jsonObject = JSON.parse(jsonMatch[1].trim());
          } catch (markdownParseError) {
            logger.warn(
              "Failed to parse JSON from markdown in supplier extraction response"
            );
          }
        }
      }

      if (!jsonObject || !jsonObject.special_fields) {
        logger.warn("Invalid JSON structure in supplier extraction response");
        return {};
      }

      // Count total fields across all categories
      let totalFieldCount = 0;
      const categories = Object.keys(jsonObject.special_fields);
      for (const category of Object.values(jsonObject.special_fields)) {
        if (typeof category === "object" && category !== null) {
          totalFieldCount += Object.keys(category).length;
        }
      }

      logger.info(
        `Successfully extracted ${totalFieldCount} special fields across ${categories.length} categories for supplier: ${supplierName}`
      );
      return jsonObject.special_fields;
    } catch (error) {
      logger.error(
        `Error in supplier-specific extraction for ${supplierName}:`,
        error
      );
      return {};
    }
  }

  /**
   * Gets three-tier extraction data for a contract
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @returns Three-tier extraction data or null if not found
   */
  async getThreeTierExtraction(contractId: string, tenantId: string) {
    try {
      const extraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );
      return extraction ? extraction.toDTO() : null;
    } catch (error) {
      logger.error(
        `Error getting three-tier extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Add fallback logic for contract_status and contract_term if not extracted by LLM
   * @param fixedFields - Fixed fields from extraction
   * @returns Fixed fields with fallback values for status and term
   */
  private addContractStatusAndTermFallbacks(
    fixedFields: FixedFields
  ): FixedFields {
    const updatedFields = { ...fixedFields };

    // Add fallback for contract_status if not present or has low confidence
    if (
      !updatedFields.contract_status ||
      !updatedFields.contract_status.value ||
      updatedFields.contract_status.confidence < 0.3
    ) {
      const startDate = updatedFields.start_date?.value;
      const endDate = updatedFields.end_date?.value;

      let status = "Unknown";
      let confidence = 0.5;

      if (startDate && endDate) {
        try {
          const currentDate = new Date();
          const start = new Date(startDate);
          const end = new Date(endDate);

          if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
            // Set time to start of day for accurate comparison
            currentDate.setHours(0, 0, 0, 0);
            start.setHours(0, 0, 0, 0);
            end.setHours(0, 0, 0, 0);

            if (currentDate >= start && currentDate <= end) {
              status = "Active";
            } else {
              status = "Inactive";
            }
            confidence = 0.9; // High confidence in calculated status
          }
        } catch (error) {
          logger.warn("Error calculating contract status fallback:", error);
        }
      }

      updatedFields.contract_status = {
        value: status,
        confidence: confidence,
      };
    }

    // Add fallback for contract_term if not present or has low confidence
    if (
      !updatedFields.contract_term ||
      !updatedFields.contract_term.value ||
      updatedFields.contract_term.confidence < 0.3
    ) {
      const startDate = updatedFields.start_date?.value;
      const endDate = updatedFields.end_date?.value;

      let term = "N/A";
      let confidence = 0.5;

      if (startDate && endDate) {
        try {
          const start = new Date(startDate);
          const end = new Date(endDate);

          if (!isNaN(start.getTime()) && !isNaN(end.getTime()) && end > start) {
            const diffTime = Math.abs(end.getTime() - start.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const totalMonths = Math.floor(diffDays / 30);

            if (totalMonths > 0) {
              term = `${totalMonths} month${totalMonths > 1 ? "s" : ""}`;
            } else {
              term = `${diffDays} day${diffDays > 1 ? "s" : ""}`;
            }
            confidence = 0.8; // Good confidence in calculated term
          }
        } catch (error) {
          logger.warn("Error calculating contract term fallback:", error);
        }
      }

      updatedFields.contract_term = {
        value: term,
        confidence: confidence,
      };
    }

    return updatedFields;
  }

  /**
   * DISABLED: Automatically assigns a folder to a contract based on extraction data
   * Folder creation is now handled manually by users through smart grouping or suggestions
   * @param contractId Contract ID
   * @param tenantId Tenant ID
   * @param fixedFields Extracted fixed fields
   * @returns Always returns null - no automatic folder assignment
   */
  async autoAssignFolder(
    contractId: string,
    tenantId: string,
    fixedFields: FixedFields
  ): Promise<string | null> {
    // Automatic folder creation is disabled
    // Users should create folders manually via smart grouping or suggestion API
    logger.info(
      `Auto-folder assignment disabled for contract ${contractId}. Use smart grouping or suggestions instead.`
    );
    return null;
  }

  /**
   * Gets extraction data for auto-grouping purposes
   * @param contractId Contract ID
   * @param tenantId Tenant ID
   * @returns Extraction data with provider, contract number, and agreement type
   */
  async getExtractionForGrouping(
    contractId: string,
    tenantId: string
  ): Promise<{
    provider: string | null;
    contractNumber: string | null;
    agreementType: string | null;
    client: string | null;
    folderId?: string | null;
  } | null> {
    try {
      const extraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );
      if (!extraction) {
        return null;
      }

      const fixedFields = extraction.fixedFields;

      return {
        provider: fixedFields.provider?.value || null,
        contractNumber: fixedFields.contract_id?.value || null,
        agreementType: fixedFields.agreement_type?.value || null,
        client: fixedFields.client?.value || null,
        folderId: extraction.folderId || null,
      };
    } catch (error) {
      logger.error(
        `Error getting extraction for grouping for contract ${contractId}:`,
        error
      );
      return null;
    }
  }

  /**
   * Gets all extractions for a tenant for auto-grouping purposes
   * @param tenantId Tenant ID
   * @param limit Maximum number of extractions to return
   * @returns Array of extraction data for grouping
   */
  async getAllExtractionsForTenant(
    tenantId: string,
    limit: number = 1000
  ): Promise<
    {
      contractId: string;
      provider: string | null;
      contractNumber: string | null;
      agreementType: string | null;
      client: string | null;
      folderId?: string | null;
    }[]
  > {
    try {
      const result = await this.contractExtractionRepository.getByTenant(
        tenantId,
        1,
        limit
      );

      return result.extractions.map((extraction) => ({
        contractId: extraction.contractId,
        provider: extraction.fixedFields?.provider?.value || null,
        contractNumber: extraction.fixedFields?.contract_id?.value || null,
        agreementType: extraction.fixedFields?.agreement_type?.value || null,
        client: extraction.fixedFields?.client?.value || null,
        folderId: extraction.folderId,
      }));
    } catch (error) {
      logger.error(
        `Error getting all extractions for tenant ${tenantId}:`,
        error
      );
      return [];
    }
  }

  /**
   * Gets basic contract information needed for the analysis pages
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @returns Basic contract information
   */
  async getBasicContractInfo(contractId: string, tenantId: string) {
    try {
      // Get extraction data which contains the essential contract info
      const extraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );

      if (!extraction) {
        return null;
      }

      // Extract basic info from the fixed fields and dynamic fields
      const fixedFields = extraction.fixedFields;
      const dynamicFields = extraction.dynamicFields;

      // Get contract description from dynamic fields, fallback to template if not available
      let contractDescription = `${fixedFields.agreement_type?.value || "Agreement"
        } between ${fixedFields.provider?.value || "Provider"} and ${fixedFields.client?.value || "Client"
        }`;

      // Use the extracted contract description if available and meaningful
      // Contract description is typically in "General" category
      const generalFields = dynamicFields["General"];
      if (
        generalFields?.contract_description?.value &&
        generalFields.contract_description.value !== "N/A" &&
        generalFields.contract_description.value.trim() !== "" &&
        !generalFields.contract_description.value
          .toLowerCase()
          .includes("not specified") &&
        !generalFields.contract_description.value
          .toLowerCase()
          .includes("not available")
      ) {
        contractDescription = generalFields.contract_description.value;
      }

      // Get LLM-extracted status or calculate fallback
      const extractedStatus = fixedFields.contract_status?.value;
      let status = "Unknown";

      if (
        extractedStatus &&
        (extractedStatus === "Active" ||
          extractedStatus === "Inactive" ||
          extractedStatus === "Unknown")
      ) {
        status = extractedStatus;
      } else {
        // Fallback: calculate status from dates if new fields don't exist yet
        const startDate = fixedFields.start_date?.value;
        const endDate = fixedFields.end_date?.value;

        if (startDate && endDate) {
          try {
            const currentDate = new Date();
            const start = new Date(startDate);
            const end = new Date(endDate);

            if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
              // Set time to start of day for accurate comparison
              currentDate.setHours(0, 0, 0, 0);
              start.setHours(0, 0, 0, 0);
              end.setHours(0, 0, 0, 0);

              if (currentDate >= start && currentDate <= end) {
                status = "Active";
              } else {
                status = "Inactive";
              }
            }
          } catch (error) {
            logger.warn("Error calculating contract status fallback:", error);
          }
        }
      }

      // Return essential contract information needed for analysis pages
      return {
        id: contractId,
        title:
          fixedFields.original_filename?.value ||
          fixedFields.contract_id?.value ||
          `Contract ${contractId}`,
        description: contractDescription,
        fileName:
          fixedFields.original_filename?.value ||
          `${fixedFields.agreement_type?.value || "Contract"
          }_${contractId}.pdf`,
        status: status,
        createdAt: extraction.createdAt?.toISOString(),
        updatedAt: extraction.updatedAt?.toISOString(),
      };
    } catch (error) {
      logger.error(
        `Error getting basic contract info for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Determines if a contract is an Oracle contract based on provider information
   * @param rawData - Raw contract data from AI analysis
   * @returns True if this is an Oracle contract
   */
  private isOracleContract(rawData: any): boolean {
    const provider = rawData.provider || rawData.publisher || "";
    const providerLower = provider.toLowerCase();

    return (
      providerLower.includes("oracle corporation") ||
      providerLower.includes("oracle america") ||
      providerLower.includes("oracle international") ||
      providerLower === "oracle" ||
      (providerLower.includes("oracle") && providerLower.includes("inc"))
    );
  }

  /**
   * Normalizes "not defined" values to "N/A" throughout the metadata object
   * @param metadata - Metadata object to normalize
   * @returns Normalized metadata object
   */
  private normalizeNotDefinedValues(
    metadata: Record<string, any>
  ): Record<string, any> {
    const normalize = (obj: any): any => {
      if (obj === null || obj === undefined) {
        return obj;
      }

      if (typeof obj === "string") {
        const trimmed = obj.trim().toLowerCase();
        if (
          trimmed === "not defined" ||
          trimmed === "not available" ||
          trimmed === "undefined" ||
          trimmed === "null"
        ) {
          return "N/A";
        }
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(normalize);
      }

      if (typeof obj === "object") {
        const normalized: Record<string, any> = {};
        for (const [key, value] of Object.entries(obj)) {
          normalized[key] = normalize(value);
        }
        return normalized;
      }

      return obj;
    };

    return normalize(metadata);
  }

  /**
   * Extracts confidence scores from the raw AI result
   * @param rawResult - Raw result from AI analysis
   * @returns Object containing confidence scores for all fields
   */
  private extractConfidenceScores(rawResult: any): Record<string, number> {
    const confidenceScores: Record<string, number> = {};

    // Define all expected fields that should have confidence scores
    const expectedFields = [
      "title",
      "provider",
      "client",
      "contract_id",
      "effective_date",
      "end_date",
      "total_amount",
      "payment_terms",
      "governing_law",
      "agreement_type",
      "contract_classification",
      "renewal_terms",
      "line_items",
      "signatures",
      "liability",
      "payment_terms_detailed",
      "sla",
      "termination",
      "contract_value",
      "contract_description",
      "licensing",
      "rights",
      "indemnification",
      "data_privacy",
      "true_up_down",
      "service_credits",
      "governance",
      "reporting_obligations",
      "ipr",
      "data_breach",
    ];

    // Define Oracle-specific fields that should have confidence scores
    const oracleFields = [
      "publisher",
      "reseller",
      "entitled_entity",
      "entitled_entity_country",
      "product_name",
      "raw_product_name",
      "total_quantity",
      "metric",
      "metric_definition",
      "term",
      "level",
      "limitations",
      "included_rights",
      "csi",
      "purchase_date",
      "governing_agreement",
      "support_contract_number",
      "support_start_date",
      "support_end_date",
      "original_document_name",
      "document_type",
      "license_value",
      "license_value_per_unit",
      "contractual_support_value",
      "support_value_per_year",
      "support_value_per_year_per_unit",
      "oracle_currency",
      "index_field",
      "delta",
    ];

    // Combine all fields for confidence score processing
    const allFields = [...expectedFields, ...oracleFields];

    // First, check if we have the new centralized confidence_scores object
    if (
      rawResult.confidence_scores &&
      typeof rawResult.confidence_scores === "object"
    ) {
      // Use the centralized confidence scores
      Object.entries(rawResult.confidence_scores).forEach(([key, value]) => {
        if (typeof value === "number") {
          let confidence = value;
          // Ensure confidence is between 0 and 1
          if (confidence > 1) confidence = confidence / 100;
          if (confidence < 0) confidence = 0;
          if (confidence > 1) confidence = 1;
          confidenceScores[key] = confidence;
        }
      });
    } else {
      // Fallback to old format for backward compatibility
      allFields.forEach((field) => {
        const confidenceField = `${field}_confidence`;
        if (rawResult[confidenceField] !== undefined) {
          let confidence = rawResult[confidenceField];
          // Ensure confidence is between 0 and 1
          if (confidence > 1) confidence = confidence / 100;
          if (confidence < 0) confidence = 0;
          if (confidence > 1) confidence = 1;
          confidenceScores[`${field}_score`] = confidence;
        }
      });
    }

    // Ensure all expected fields have confidence scores
    allFields.forEach((field) => {
      const scoreKey = `${field}_score`;

      // If we don't have a confidence score for this field, generate one based on the field value
      if (!confidenceScores[scoreKey]) {
        const fieldValue = rawResult[field];
        let defaultConfidence = 0.2; // Default for missing/undefined fields

        if (fieldValue !== undefined && fieldValue !== null) {
          if (typeof fieldValue === "string") {
            if (
              fieldValue.toLowerCase().includes("n/a") ||
              fieldValue.toLowerCase().includes("unknown") ||
              fieldValue.toLowerCase().includes("not specified") ||
              fieldValue.toLowerCase().includes("not available") ||
              fieldValue.trim() === ""
            ) {
              defaultConfidence = 0.2;
            } else {
              defaultConfidence = 0.7; // Medium confidence for extracted text
            }
          } else if (typeof fieldValue === "object" && fieldValue !== null) {
            defaultConfidence = 0.6; // Medium-low confidence for complex objects
          } else {
            defaultConfidence = 0.7; // Medium confidence for other types
          }
        }

        confidenceScores[scoreKey] = defaultConfidence;
      }
    });

    return confidenceScores;
  }

  /**
   * Validates and normalizes agreement type to match predefined enum values
   * @param rawAgreementType - Raw agreement type from AI analysis
   * @returns Valid AgreementType enum value or "OTHER"
   */
  private validateAgreementType(rawAgreementType: string): string {
    if (!rawAgreementType) {
      return "OTHER";
    }

    // Define the valid AgreementType enum values from Prisma schema
    const validAgreementTypes = [
      "MSA",
      "NDA",
      "SOW",
      "PO",
      "SLA",
      "DPA",
      "BAA",
      "EULA",
      "LOI",
      "MOA",
      "MOU",
      "JV",
      "CA",
      "LPA",
      "SSA",
      "ESA",
      "PSA",
      "TOS",
      "DUA",
      "OEM",
      "RFP",
      "RFQ",
      "BPA",
      "PPA",
      "LSA",
      "ISA",
      "SPA",
      "APA",
      "TPA",
      "IP",
      "RSA",
      "VARA",
      "DDA",
      "TSA",
      "IA",
      "INVOICE",
      "SCHEDULE",
      "OTHER",
      "ORDER",
    ];

    // Clean and normalize the input
    const cleanType = rawAgreementType.toString().trim().toUpperCase();

    // Direct match check
    if (validAgreementTypes.includes(cleanType)) {
      return cleanType;
    }

    // Mapping for common AI responses to valid enum values
    const agreementTypeMapping: Record<string, string> = {
      // Master agreements
      "MASTER SERVICE AGREEMENT": "MSA",
      "MASTER SERVICES AGREEMENT": "MSA",
      "MASTER AGREEMENT": "MSA",
      "FRAMEWORK AGREEMENT": "MSA",
      "UMBRELLA AGREEMENT": "MSA",
      "ENTERPRISE AGREEMENT": "MSA", // Map Enterprise Agreement to MSA

      // Non-disclosure agreements
      "NON-DISCLOSURE AGREEMENT": "NDA",
      "CONFIDENTIALITY AGREEMENT": "NDA",
      "SECRECY AGREEMENT": "NDA",

      // Statements of work
      "STATEMENT OF WORK": "SOW",
      "WORK ORDER": "SOW",
      "PROJECT AGREEMENT": "SOW",

      // Purchase orders
      "PURCHASE ORDER": "PO",
      "PROCUREMENT ORDER": "PO",
      "BUYING AGREEMENT": "PO",

      // Orders
      ORDER: "ORDER",
      "ORDER AGREEMENT": "ORDER",
      "ORDERING AGREEMENT": "ORDER",

      // Service level agreements
      "SERVICE LEVEL AGREEMENT": "SLA",
      "SERVICE AGREEMENT": "SLA",

      // Data processing agreements
      "DATA PROCESSING AGREEMENT": "DPA",
      "DATA PROTECTION AGREEMENT": "DPA",

      // Business associate agreements
      "BUSINESS ASSOCIATE AGREEMENT": "BAA",

      // End user license agreements
      "END USER LICENSE AGREEMENT": "EULA",
      "SOFTWARE LICENSE AGREEMENT": "EULA",
      "LICENSE AGREEMENT": "EULA",

      // Letters of intent
      "LETTER OF INTENT": "LOI",

      // Memorandums
      "MEMORANDUM OF AGREEMENT": "MOA",
      "MEMORANDUM OF UNDERSTANDING": "MOU",

      // Joint ventures
      "JOINT VENTURE": "JV",
      "JOINT VENTURE AGREEMENT": "JV",

      // Consulting agreements
      "CONSULTING AGREEMENT": "CA",
      "CONSULTANT AGREEMENT": "CA",

      // License and partnership agreements
      "LICENSE AND PARTNERSHIP AGREEMENT": "LPA",
      "LICENSING AGREEMENT": "LPA",

      // Software and service agreements
      "SOFTWARE SUPPORT AGREEMENT": "SSA",
      "SUPPORT AGREEMENT": "SSA",
      "ENTERPRISE SUPPORT AGREEMENT": "ESA",
      "PROFESSIONAL SERVICES AGREEMENT": "PSA",
      "PROFESSIONAL SERVICE AGREEMENT": "PSA",

      // Terms of service
      "TERMS OF SERVICE": "TOS",
      "TERMS AND CONDITIONS": "TOS",

      // Data use agreements
      "DATA USE AGREEMENT": "DUA",
      "DATA USAGE AGREEMENT": "DUA",

      // OEM agreements
      "OEM AGREEMENT": "OEM",
      "ORIGINAL EQUIPMENT MANUFACTURER": "OEM",

      // Request for proposals/quotes
      "REQUEST FOR PROPOSAL": "RFP",
      "REQUEST FOR QUOTE": "RFQ",
      "REQUEST FOR QUOTATION": "RFQ",

      // Blanket purchase agreements
      "BLANKET PURCHASE AGREEMENT": "BPA",

      // Power purchase agreements
      "POWER PURCHASE AGREEMENT": "PPA",

      // Lease and supply agreements
      "LEASE AGREEMENT": "LSA",
      "LEASE SERVICE AGREEMENT": "LSA",
      "INTEGRATION SERVICES AGREEMENT": "ISA",
      "SUPPLY AGREEMENT": "SPA",
      "ASSET PURCHASE AGREEMENT": "APA",
      "THIRD PARTY AGREEMENT": "TPA",

      // Intellectual property
      "INTELLECTUAL PROPERTY": "IP",
      "IP AGREEMENT": "IP",

      // Reseller and vendor agreements
      "RESELLER AGREEMENT": "RSA",
      "VENDOR AGREEMENT": "VARA",
      "DISTRIBUTION AGREEMENT": "DDA",

      // Technical support agreements
      "TECHNICAL SUPPORT AGREEMENT": "TSA",
      "TECHNOLOGY SUPPORT AGREEMENT": "TSA",

      // Implementation agreements
      "IMPLEMENTATION AGREEMENT": "IA",
      "INTEGRATION AGREEMENT": "IA",

      // Invoice documents
      INVOICE: "INVOICE",
      "BILLING DOCUMENT": "INVOICE",
      "PAYMENT REQUEST": "INVOICE",

      // Schedule agreements
      SCHEDULE: "SCHEDULE",
      "SCHEDULE AGREEMENT": "SCHEDULE",
      "PRICING SCHEDULE": "SCHEDULE",
      "DELIVERY SCHEDULE": "SCHEDULE",
      "PAYMENT SCHEDULE": "SCHEDULE",
      "SERVICE SCHEDULE": "SCHEDULE",
      "FINANCIAL AGREEMENT": "SCHEDULE",
      "FINANCIAL SCHEDULE": "SCHEDULE",
      "SCHEDULE DOCUMENT": "SCHEDULE",
    };

    // Check for mapped values
    if (agreementTypeMapping[cleanType]) {
      logger.info(
        `Mapped agreement type "${rawAgreementType}" to "${agreementTypeMapping[cleanType]}"`
      );
      return agreementTypeMapping[cleanType];
    }

    // Check for partial matches (contains key phrases)
    for (const [key, value] of Object.entries(agreementTypeMapping)) {
      if (cleanType.includes(key) || key.includes(cleanType)) {
        logger.info(
          `Partial match: mapped agreement type "${rawAgreementType}" to "${value}"`
        );
        return value;
      }
    }

    // If no match found, log the unknown type and default to OTHER
    logger.warn(
      `Unknown agreement type "${rawAgreementType}", defaulting to "OTHER"`
    );
    return "OTHER";
  }

  /**
   * Normalizes the Agreement Analysis result to ensure consistent structure
   * @param rawResult - Raw result from AI analysis
   * @returns Normalized Agreement Analysis result
   */
  private normalizeContractAnalysisResult(
    rawResult: any
  ): ContractDocumentAnalysisResult {
    const result: ContractDocumentAnalysisResult = {};

    // Extract title
    result.title =
      rawResult.title || rawResult.contractTitle || rawResult.name || undefined;

    // Extract contract type - map from new schema
    result.contractType =
      rawResult.agreement_type ||
      rawResult.contractType ||
      rawResult.type ||
      undefined;

    // Extract contract classification
    result.classification =
      rawResult.contract_classification ||
      rawResult.classification ||
      undefined;

    // Extract and validate agreement type
    const rawAgreementType =
      rawResult.agreement_type || rawResult.agreementType;
    result.agreementType = rawAgreementType
      ? this.validateAgreementType(rawAgreementType)
      : undefined;

    // Extract parties - handle both old and new schema
    if (rawResult.parties) {
      if (Array.isArray(rawResult.parties)) {
        result.parties = rawResult.parties.map((party: any) => ({
          name: party.name || party.partyName || "Unknown",
          role: party.role || party.partyRole || undefined,
          address: party.address || undefined,
          contactInfo: party.contactInfo || party.contact || undefined,
        }));
      } else if (typeof rawResult.parties === "object") {
        // Handle case where parties might be an object instead of array
        result.parties = Object.entries(rawResult.parties).map(
          ([key, value]: [string, any]) => ({
            name: value.name || key,
            role: value.role || undefined,
            address: value.address || undefined,
            contactInfo: value.contactInfo || value.contact || undefined,
          })
        );
      }
    } else {
      // Extract from new schema format
      const parties = [];
      if (rawResult.provider) {
        parties.push({
          name: rawResult.provider,
          role: "Provider",
          address: undefined,
          contactInfo: undefined,
        });
      }
      if (rawResult.client) {
        parties.push({
          name: rawResult.client,
          role: "Client",
          address: undefined,
          contactInfo: undefined,
        });
      }

      if (parties.length > 0) {
        result.parties = parties;
      }
    }

    // Extract dates - handle both old and new schema
    result.dates = {
      startDate: this.extractDate(rawResult, [
        "effective_date", // New schema
        "startDate",
        "start_date",
        "effectiveDate",
        "effective_date",
      ]),
      endDate: this.extractDate(rawResult, [
        "end_date", // New schema
        "endDate",
        "end_date",
        "terminationDate",
        "termination_date",
      ]),
      executionDate: this.extractDate(rawResult, [
        "executionDate",
        "execution_date",
        "signedDate",
        "signed_date",
      ]),
      effectiveDate: this.extractDate(rawResult, [
        "effective_date", // New schema
        "effectiveDate",
        "effective_date",
      ]),
      renewalDate: this.extractDate(rawResult, ["renewalDate", "renewal_date"]),
      terminationDate: this.extractDate(rawResult, [
        "end_date", // New schema can also be termination
        "terminationDate",
        "termination_date",
      ]),
    };

    // Extract value - handle both old and new schema
    if (rawResult.value) {
      result.value = {
        amount:
          rawResult.value.amount ||
          rawResult.value.value ||
          rawResult.value.toString(),
        currency: rawResult.value.currency || "USD",
        paymentSchedule:
          rawResult.value.paymentSchedule ||
          rawResult.value.schedule ||
          undefined,
        totalValue:
          rawResult.value.totalValue || rawResult.value.total || undefined,
      };
    } else if (rawResult.contractValue) {
      // Handle alternative structure
      result.value = {
        amount:
          rawResult.contractValue.amount || rawResult.contractValue.toString(),
        currency: rawResult.contractValue.currency || "USD",
        paymentSchedule: rawResult.contractValue.paymentSchedule || undefined,
        totalValue: rawResult.contractValue.totalValue || undefined,
      };
    } else if (rawResult.total_amount) {
      // Handle new schema format - check if it's combined format
      if (
        typeof rawResult.total_amount === "string" &&
        rawResult.total_amount.includes(":")
      ) {
        // New combined format: "CURRENCY:AMOUNT"
        result.value = rawResult.total_amount;
      } else {
        // Old format: separate fields
        result.value = {
          amount: rawResult.total_amount?.toString() || "Not specified",
          currency: rawResult.currency || "USD",
          paymentSchedule: rawResult.payment_terms || undefined,
          totalValue: rawResult.total_amount || undefined,
        };
      }
    } else {
      // Ensure value is always present
      result.value = {
        amount: "Not specified",
        currency: "USD",
      };
    }

    // Extract payment terms
    if (rawResult.paymentTerms) {
      if (typeof rawResult.paymentTerms === "object") {
        result.paymentTerms = {
          dueDays: this.extractNumericValue(
            rawResult.paymentTerms.dueDays || rawResult.paymentTerms.due
          ),
          latePaymentPenalty:
            rawResult.paymentTerms.latePaymentPenalty ||
            rawResult.paymentTerms.penalty,
          paymentMethod:
            rawResult.paymentTerms.paymentMethod ||
            rawResult.paymentTerms.method,
          description:
            rawResult.paymentTerms.description ||
            rawResult.paymentTerms.toString(),
        };
      } else {
        result.paymentTerms = {
          description: rawResult.paymentTerms.toString(),
        };
      }
    }

    // Extract renewal terms
    if (rawResult.renewalTerms) {
      if (typeof rawResult.renewalTerms === "object") {
        result.renewalTerms = {
          isAutoRenew:
            rawResult.renewalTerms.isAutoRenew ||
            rawResult.renewalTerms.autoRenew ||
            false,
          renewalPeriod:
            rawResult.renewalTerms.renewalPeriod ||
            rawResult.renewalTerms.period,
          noticePeriodDays: this.extractNumericValue(
            rawResult.renewalTerms.noticePeriodDays ||
            rawResult.renewalTerms.noticePeriod
          ),
          renewalConditions:
            rawResult.renewalTerms.renewalConditions ||
            rawResult.renewalTerms.conditions,
        };
      } else {
        result.renewalTerms = {
          renewalPeriod: rawResult.renewalTerms.toString(),
        };
      }
    }

    // Extract other fields
    result.confidentiality =
      rawResult.confidentiality || rawResult.confidentialityClassification;
    result.terminationClauses = Array.isArray(rawResult.terminationClauses)
      ? rawResult.terminationClauses
      : rawResult.terminationClauses
        ? [rawResult.terminationClauses.toString()]
        : undefined;
    result.governingLaw =
      rawResult.governingLaw ||
      rawResult.governing_law ||
      rawResult.jurisdiction;
    result.disputeResolution =
      rawResult.disputeResolution || rawResult.dispute_resolution;
    result.liabilityLimits =
      rawResult.liabilityLimits || rawResult.liability_limits;

    // Extract warranties
    if (rawResult.warranties) {
      result.warranties = Array.isArray(rawResult.warranties)
        ? rawResult.warranties
        : [rawResult.warranties.toString()];
    }

    result.amendments = rawResult.amendments || rawResult.amendmentProvisions;

    // Extract signatures
    if (rawResult.signatures) {
      if (Array.isArray(rawResult.signatures)) {
        result.signatures = rawResult.signatures.map((sig: any) => ({
          name: sig.name || sig.signatoryName,
          title: sig.title || sig.signatoryTitle,
          date: sig.date || sig.signatureDate,
          party: sig.party || sig.representingParty,
        }));
      } else if (typeof rawResult.signatures === "object") {
        result.signatures = Object.entries(rawResult.signatures).map(
          ([key, value]: [string, any]) => ({
            name: value.name || key,
            title: value.title,
            date: value.date,
            party: value.party,
          })
        );
      }
    }

    // Include any other metadata
    result.otherMetadata = rawResult.otherMetadata || rawResult.other || {};

    return result;
  }

  /**
   * Extracts a date from various possible fields in the raw result
   * @param rawResult - Raw result object
   * @param possibleFields - Array of possible field names
   * @returns Extracted date string or undefined
   */
  private extractDate(
    rawResult: any,
    possibleFields: string[]
  ): string | undefined {
    for (const field of possibleFields) {
      // Check direct field
      if (rawResult[field]) {
        return rawResult[field];
      }

      // Check in dates object
      if (rawResult.dates && rawResult.dates[field]) {
        return rawResult.dates[field];
      }
    }
    return undefined;
  }

  /**
   * Extracts a numeric value from a string or number
   * @param value - Value to extract number from
   * @returns Numeric value or undefined
   */
  private extractNumericValue(value: any): number | undefined {
    if (value === undefined || value === null) {
      return undefined;
    }

    if (typeof value === "number") {
      return value;
    }

    if (typeof value === "string") {
      // For currency values like "$50,000.00" or "50,000 USD"
      if (value.match(/[$€£¥]|USD|EUR|GBP|JPY/i)) {
        // Remove currency symbols, commas, spaces, and other non-numeric characters
        // Keep decimal points and negative signs
        const cleanedValue = value.replace(/[^0-9.-]/g, "");
        const numericValue = parseFloat(cleanedValue);
        if (!isNaN(numericValue)) {
          return numericValue;
        }
      }

      // For simple numeric strings with units (e.g., "30 days")
      const match = value.match(/(\d+(\.\d+)?)/);
      if (match) {
        return parseFloat(match[1]);
      }
    }

    return undefined;
  }

  /**
   * Gets the MIME type for a file based on its extension
   * @param fileName - File name
   * @returns MIME type
   */
  private getMimeType(fileName: string): string {
    const ext = fileName.split(".").pop()?.toLowerCase();

    switch (ext) {
      case "pdf":
        return "application/pdf";
      case "docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case "doc":
        return "application/msword";
      case "txt":
        return "text/plain";
      case "rtf":
        return "application/rtf";
      case "html":
      case "htm":
        return "text/html";
      default:
        return "application/octet-stream";
    }
  }

  /**
   * Sanitizes currency code to ensure it's valid for ISO 4217 format
   * @param currency Raw currency code from AI analysis
   * @returns Valid currency code or null
   */
  private sanitizeCurrency(currency?: string): string | null {
    if (!currency) {
      return null;
    }

    // Convert to uppercase and trim
    const cleanCurrency = currency.toUpperCase().trim();

    // Handle common AI responses that aren't valid currency codes
    if (
      cleanCurrency === "NOT SPECIFIED" ||
      cleanCurrency === "UNKNOWN" ||
      cleanCurrency === "N/A" ||
      cleanCurrency === "NULL" ||
      cleanCurrency === "UNDEFINED" ||
      cleanCurrency === ""
    ) {
      return null;
    }

    // Map common currency names/symbols to ISO codes
    const currencyMap: Record<string, string> = {
      DOLLAR: "USD",
      DOLLARS: "USD",
      "US DOLLAR": "USD",
      "US DOLLARS": "USD",
      "AMERICAN DOLLAR": "USD",
      $: "USD",
      EURO: "EUR",
      EUROS: "EUR",
      "€": "EUR",
      POUND: "GBP",
      POUNDS: "GBP",
      "BRITISH POUND": "GBP",
      "£": "GBP",
      YEN: "JPY",
      "JAPANESE YEN": "JPY",
      "¥": "JPY",
      "CANADIAN DOLLAR": "CAD",
      "CANADIAN DOLLARS": "CAD",
      "AUSTRALIAN DOLLAR": "AUD",
      "AUSTRALIAN DOLLARS": "AUD",
      "SWISS FRANC": "CHF",
      "SWISS FRANCS": "CHF",
      YUAN: "CNY",
      "CHINESE YUAN": "CNY",
      RUPEE: "INR",
      "INDIAN RUPEE": "INR",
      RUPEES: "INR",
    };

    // Check if it's a mapped currency name
    if (currencyMap[cleanCurrency]) {
      return currencyMap[cleanCurrency];
    }

    // Validate ISO 4217 format (3 uppercase letters)
    if (/^[A-Z]{3}$/.test(cleanCurrency)) {
      return cleanCurrency;
    }

    // If we can't determine a valid currency, return null
    logger.warn(
      `Invalid currency code from AI analysis: ${currency}, defaulting to null`
    );
    return null;
  }

  /**
   * Generates both narrative and tabular AI summaries directly from the contract document
   * @param documentBuffer - The original contract document buffer
   * @param fileName - Original file name
   * @param contractData - Basic contract data for context
   * @returns Object containing both narrative and tabular summaries
   */
  async generateContractSummariesFromDocument(
    documentBuffer: Buffer,
    fileName: string,
    contractData?: any
  ): Promise<{ narrative: string; tabular: Record<string, any>[] }> {
    logger.info(
      `[DUAL_SUMMARY] Starting comprehensive AI summaries generation from document: ${fileName}`
    );

    try {
      // Generate both summaries sequentially for better error tracking
      logger.info(`[DUAL_SUMMARY] Step 1: Generating narrative summary...`);
      const narrative = await this.generateContractSummaryFromDocument(
        documentBuffer,
        fileName,
        contractData
      );
      logger.info(
        `[DUAL_SUMMARY] Step 1 completed: Narrative summary generated (${narrative.length} characters)`
      );

      logger.info(`[DUAL_SUMMARY] Step 2: Generating tabular summary...`);
      let tabular: Record<string, any>[] = [];
      try {
        tabular = await this.generateTabularSummaryFromDocument(
          documentBuffer,
          fileName,
          contractData
        );
        logger.info(
          `[DUAL_SUMMARY] Step 2 completed: Tabular summary generated (${tabular.length} aspects)`
        );
      } catch (tabularError) {
        logger.error(
          `[DUAL_SUMMARY] Step 2 FAILED: Tabular summary generation failed:`,
          tabularError
        );
        // Continue with empty tabular summary rather than failing completely
        logger.info(
          `[DUAL_SUMMARY] Step 2 FALLBACK: Using empty tabular summary`
        );
      }

      logger.info(
        `[DUAL_SUMMARY] SUCCESS: Both summaries generated successfully`
      );
      return { narrative, tabular };
    } catch (error) {
      logger.error(`[DUAL_SUMMARY] ERROR in primary generation:`, error);

      // Fallback to individual generation with detailed logging
      try {
        logger.info(
          `[DUAL_SUMMARY] FALLBACK: Attempting individual generation...`
        );

        let narrative: string;
        let tabular: Record<string, any>[];

        try {
          narrative = await this.generateContractSummaryFromDocument(
            documentBuffer,
            fileName,
            contractData
          );
          logger.info(
            `[DUAL_SUMMARY] FALLBACK: Narrative summary generated successfully`
          );
        } catch (narrativeError) {
          logger.error(
            `[DUAL_SUMMARY] FALLBACK: Narrative generation failed:`,
            narrativeError
          );
          narrative = contractData
            ? await this.generateContractSummary(contractData)
            : this.generateBasicFallbackSummary(fileName);
          logger.info(
            `[DUAL_SUMMARY] FALLBACK: Using metadata-based narrative summary`
          );
        }

        try {
          tabular = await this.generateTabularSummaryFromDocument(
            documentBuffer,
            fileName,
            contractData
          );
          logger.info(
            `[DUAL_SUMMARY] FALLBACK: Tabular summary generated successfully`
          );
        } catch (tabularError) {
          logger.error(
            `[DUAL_SUMMARY] FALLBACK: Tabular generation failed:`,
            tabularError
          );
          tabular = contractData
            ? this.generateTabularSummaryFromMetadata(contractData)
            : this.generateFallbackTabularSummary(contractData, fileName);
          logger.info(
            `[DUAL_SUMMARY] FALLBACK: Using metadata-based tabular summary (${tabular.length} aspects)`
          );
        }

        logger.info(
          `[DUAL_SUMMARY] FALLBACK SUCCESS: Both summaries available`
        );
        return { narrative, tabular };
      } catch (fallbackError) {
        logger.error(`[DUAL_SUMMARY] FALLBACK FAILED:`, fallbackError);

        // Final fallback with guaranteed results
        logger.info(
          `[DUAL_SUMMARY] FINAL FALLBACK: Using basic fallback summaries`
        );
        const fallbackNarrative = contractData
          ? await this.generateContractSummary(contractData)
          : this.generateBasicFallbackSummary(fileName);
        const fallbackTabular = this.generateFallbackTabularSummary(
          contractData,
          fileName
        );

        logger.info(
          `[DUAL_SUMMARY] FINAL FALLBACK SUCCESS: Basic summaries generated`
        );
        return {
          narrative: fallbackNarrative,
          tabular: fallbackTabular,
        };
      }
    }
  }

  /**
   * Generates a comprehensive AI summary directly from the contract document
   * @param documentBuffer - The original contract document buffer
   * @param fileName - Original file name
   * @param contractData - Basic contract data for context
   * @returns Comprehensive narrative summary
   */
  async generateContractSummaryFromDocument(
    documentBuffer: Buffer,
    fileName: string,
    contractData?: any,
    contractId?: string
  ): Promise<string> {
    try {
      logger.info(
        `Generating comprehensive AI summary from document: ${fileName}`
      );

      // Check if OCR text is available for this contract
      let useOCRText = false;
      let ocrText = '';

      if (contractId) {
        const ocrTextResult = await this.getOCRTextForContract(contractId);
        if (ocrTextResult) {
          useOCRText = true;
          ocrText = ocrTextResult;
          logger.info(`Using OCR text for summary generation: ${fileName}`);
        }
      }

      if (!useOCRText) {
        logger.info(`Using document buffer for summary generation: ${fileName}`);
      }

      // Convert document to base64 for Gemini API (only if not using OCR)
      const base64Document = useOCRText ? '' : documentBuffer.toString("base64");

      // Determine MIME type based on file extension (only if not using OCR)
      const mimeType = useOCRText ? '' : this.getMimeTypeFromFileName(fileName);

      // Create a comprehensive prompt for document-based summary generation
      const summaryPrompt = `
You are an expert contract analyst. Analyze the attached contract document and generate a comprehensive, professional narrative summary. The summary should be detailed, well-structured, and written in flowing paragraphs (not bullet points).

Please create a comprehensive summary that includes:

1. **Contract Overview and Parties**: Complete contract overview with full context, all parties involved (including subsidiaries and affiliates), agreement type, current status, and contract value WITH DETAILED JUSTIFICATION.

2. **Financial Terms and Value Justification**: For the contract value, provide detailed supporting information that explains the basis for the amount. Include specific document sections, line items, calculations, pricing breakdowns, or references that justify how the contract value was determined. For example: "Value based on 12 months of technical support services at $8,333 per month as specified in Section 3.2" or "Total amount derived from hardware costs ($75,000) plus installation services ($25,000) as itemized in Appendix A". Always reference specific document sections, schedules, or appendices where the value components are detailed.

3. **Payment Schedules and Pricing Details**: All financial terms, payment schedules, comprehensive date information (effective, execution, expiration, renewal, notice periods), and contract duration.

4. **Scope and Service Requirements**: Detailed scope of work, services, deliverables, performance requirements, SLA requirements, service levels, performance metrics, and contractual obligations.

5. **Risk Management and Governance**: Risk factors, liability terms, indemnification, compliance requirements, termination conditions, renewal terms, notice requirements, dispute resolution, and governing law.

6. **Key Clauses and Special Provisions**: Important contract clauses, special provisions, amendments, appendices, and any unique terms or conditions.

7. **Relationships and Dependencies**: Any references to other contracts, master agreements, appendices, or related documents.

Requirements:
- Write in natural, flowing language as a professional contract analysis report
- Include specific values, dates, and terms rather than generic statements
- For contract value, ALWAYS provide detailed justification with specific document references (sections, schedules, appendices, line items)
- Handle missing information gracefully without mentioning empty fields
- Aim for 4-5 comprehensive paragraphs
- Focus on the most important and meaningful contract elements
- Ensure the summary reads like a professional legal document analysis
- Extract information directly from the document content, not from metadata
- When describing contract value, explain HOW the value was calculated or determined, not just WHAT the value is

Generate the comprehensive summary now:`;

      // Call Gemini API with either OCR text or document buffer
      const parts: any[] = [{ text: summaryPrompt }];

      if (useOCRText) {
        // Add OCR text to the prompt
        parts[0].text += `\n\nContract Text:\n${ocrText}`;
      } else {
        // Add document as inline data
        parts.push({
          inlineData: {
            mimeType: mimeType,
            data: base64Document,
          },
        });
      }

      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.3,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 3072,
        },
        contents: {
          role: "user",
          parts,
        },
      });

      // Extract response text
      let summaryText = "";
      if (response && response.text) {
        summaryText = response.text;
      } else {
        throw new Error("Invalid response structure from Gemini API");
      }

      if (!summaryText || summaryText.trim().length === 0) {
        throw new Error("AI generated empty summary");
      }

      logger.info(
        "Successfully generated comprehensive AI contract summary from document"
      );
      return summaryText.trim();
    } catch (error) {
      logger.error(
        "Error generating AI contract summary from document:",
        error
      );

      // Fallback to metadata-based summary if document analysis fails
      if (contractData) {
        logger.info("Falling back to metadata-based summary generation");
        return this.generateContractSummary(contractData);
      }

      // Final fallback to basic summary
      const fallbackSummary = this.generateBasicFallbackSummary(fileName);
      logger.info("Using basic fallback summary generation");
      return fallbackSummary;
    }
  }



  /**
   * Gets OCR text for a contract if available
   * @param contractId - Contract ID
   * @returns OCR text if available, null otherwise
   */
  async getOCRTextForContract(contractId: string): Promise<string | null> {
    try {
      logger.info(`Fetching OCR text for contract: ${contractId}`);

      // Get the OCR text from the contract version (simplified - just check if OCR text exists)
      const contractVersion = await this.prisma.contractVersion.findFirst({
        where: { contractId },
        orderBy: { versionNumber: 'desc' },
        select: {
          id: true,
          ocrText: true,
          ocrStatus: true,
          ocrProcessedAt: true,
          ocrConfidence: true
        }
      });

      logger.info(`Contract version query result for ${contractId}:`, {
        found: !!contractVersion,
        id: contractVersion?.id,
        ocrStatus: contractVersion?.ocrStatus,
        ocrProcessedAt: contractVersion?.ocrProcessedAt,
        ocrConfidence: contractVersion?.ocrConfidence,
        hasOcrText: !!contractVersion?.ocrText,
        ocrTextLength: contractVersion?.ocrText?.length || 0
      });

      if (contractVersion?.ocrText && contractVersion.ocrText.trim().length > 0) {
        logger.info(`Found OCR text for contract ${contractId}: ${contractVersion.ocrText.length} characters`);
        return contractVersion.ocrText;
      }

      logger.warn(`No OCR text found for contract ${contractId}. ContractVersion exists: ${!!contractVersion}, OCR Status: ${contractVersion?.ocrStatus}`);
      return null;
    } catch (error) {
      logger.error(`Error getting OCR text for contract ${contractId}:`, error);
      return null;
    }
  }

  /**
   * Generates DORA compliance analysis for a contract document
   * @param documentBuffer - The original contract document buffer
   * @param fileName - Original file name
   * @param contractTitle - Contract title for context
   * @returns DORA compliance analysis
   */
  async generateDORAComplianceAnalysis(
    documentBuffer: Buffer,
    fileName: string,
    contractTitle: string
  ): Promise<any> {
    try {
      logger.info(
        `Generating DORA compliance analysis from document: ${fileName} using proven extraction method`
      );

      // Use the same document analysis method that works during initial upload
      // This method has proven to work with the same documents that are causing issues
      logger.info(
        `Validating document using analyzeContractDocument method first`
      );

      try {
        // First, let's try to analyze the document using the same method that works during upload
        // This will validate that the document is readable and processable
        const documentAnalysis = await this.analyzeContractDocument(
          documentBuffer,
          fileName
        );

        // Validate that we got meaningful results from the analysis
        if (!documentAnalysis || Object.keys(documentAnalysis).length === 0) {
          throw new Error("Document analysis returned empty results");
        }

        logger.info(
          `Document validation successful with meaningful results, proceeding with DORA compliance analysis`
        );
      } catch (validationError) {
        logger.error(`Document validation failed:`, validationError);
        throw new Error(
          `Document cannot be processed for compliance analysis. This indicates the stored document is corrupted or unreadable. Please re-upload the document. Error: ${(validationError as Error).message
          }`
        );
      }

      logger.info(`Document buffer size: ${documentBuffer.length} bytes`);
      logger.info(`Contract title: ${contractTitle}`);

      // Convert document to base64
      const base64Document = documentBuffer.toString("base64");
      const mimeType = this.getMimeType(fileName);

      logger.info(`MIME type detected: ${mimeType}`);
      logger.info(
        `Base64 document length: ${base64Document.length} characters`
      );

      // Document validation already done above using the proven analyzeContractDocument method
      // No need for additional PDF header validation since the proven method handles this

      // Create comprehensive DORA compliance prompt
      const compliancePrompt = `You are reviewing a contract to assess compliance with the EU Digital Operational Resilience Act (DORA).

For this contract titled "${contractTitle}", assess the presence of DORA-relevant clauses. For each category below, respond in this exact JSON format:

{
  "criticalFunctions": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "subcontracting": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "auditRights": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "terminationRights": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "exitStrategy": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "incidentNotification": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "dataLocation": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "businessContinuity": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "securityMeasures": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "regulatoryCooperation": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "liability": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "serviceLevelAgreements": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  }
}

### DORA Clause Assessment Categories:

1. **Critical / Important Functions** - Does the agreement support critical or important functions under EBA/ESMA guidance?

2. **Subcontracting** - Are subcontractors permitted? Is prior notification or approval required? Can critical functions be subcontracted?

3. **Audit, Access & Inspection Rights** - Are unrestricted audit and inspection rights granted (incl. access to logs, data centers, personnel)? Are there frequency or scope limitations?

4. **Termination Rights** - Can the customer terminate for: Material breach? Regulatory breach or noncompliance? Without penalty for disruption of critical service?

5. **Exit Strategy / Transition Support** - Is there a defined exit or transition plan? Are system and data handovers addressed? Is support for transitioning to a new provider included?

6. **Incident Notification** - Are ICT-related incidents reported within defined timeframes? Are timelines and escalation paths defined? Are obligations aligned with DORA?

7. **Data Location & Processing** - Is the data location (EU/EEA or third country) specified? Are there data transfer safeguards and GDPR compliance terms?

8. **Business Continuity & Disaster Recovery (BC/DR)** - Is there a BCP/DRP? Are RTO (Recovery Time Objective) and RPO (Recovery Point Objective) defined?

9. **Security Measures** - Are security obligations aligned with ISO 27001, NIST, or NIS2? Are obligations for penetration testing, encryption, vulnerability management, and access control defined?

10. **Regulatory Cooperation** - Are providers obligated to cooperate with EU supervisory authorities? Do clauses support regulator access to records and systems?

11. **Liability & Insurance** - Is liability for operational risk clearly defined? Is cyber insurance coverage mentioned or required?

12. **Service Level Agreements (SLAs)** - Are SLAs defined for uptime, support, and service recovery? Are monitoring and enforcement mechanisms in place?

**IMPORTANT:** Your response MUST be a valid JSON object matching the exact structure shown above. Do not include explanatory text outside the JSON.`;

      // Call Gemini API using @google/genai SDK
      const contents = {
        role: "user",
        parts: [
          {
            text: compliancePrompt,
          },
          {
            inlineData: {
              mimeType: mimeType,
              data: base64Document,
            },
          },
        ],
      };

      logger.info(
        `Making API call to Gemini with content size: ${JSON.stringify(contents).length
        } characters`
      );

      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.1,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 4096,
        },
        contents,
      });

      logger.info(`Gemini API response received successfully`);

      // Extract response text
      let complianceText = "";
      if (response && response.text) {
        complianceText = response.text;
      } else {
        throw new Error("Invalid response structure from Gemini API");
      }

      if (!complianceText || complianceText.trim().length === 0) {
        throw new Error("AI generated empty compliance analysis");
      }

      // Parse JSON response
      let complianceAnalysis;
      try {
        // Clean the response text to extract JSON
        const jsonMatch = complianceText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error("No JSON found in response");
        }
        complianceAnalysis = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        logger.error("Failed to parse compliance analysis JSON:", parseError);
        throw new Error("Failed to parse compliance analysis from AI response");
      }

      logger.info(
        "Successfully generated DORA compliance analysis from document"
      );
      return complianceAnalysis;
    } catch (error: any) {
      logger.error(
        "Error generating DORA compliance analysis from document:",
        error
      );

      // Log more specific error details
      if (error.response) {
        logger.error(`API Error Status: ${error.response.status}`);
        logger.error(`API Error Headers:`, error.response.headers);
        logger.error(
          `API Error Data:`,
          JSON.stringify(error.response.data, null, 2)
        );

        // Check if it's a specific Gemini API error
        if (error.response.status === 400) {
          logger.error(
            "Gemini API rejected the request - likely due to document format or content issues"
          );
          throw new Error(
            "The document format is not supported or the document content cannot be processed by the AI service. Please ensure you have uploaded a valid, readable PDF document."
          );
        }
      } else if (error.request) {
        logger.error("No response received from API");
        logger.error("Request details:", error.request);
      } else {
        logger.error(`Error setting up request: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Generates DORA compliance analysis from text content (fallback for corrupted PDFs)
   * @param textContent - The contract text content
   * @param contractTitle - Contract title for context
   * @returns DORA compliance analysis
   */
  async generateDORAComplianceFromText(
    textContent: string,
    contractTitle: string
  ): Promise<any> {
    try {
      logger.info(
        `Generating DORA compliance analysis from text content for: ${contractTitle}`
      );

      // Create comprehensive DORA compliance prompt with text content
      const compliancePrompt = `You are reviewing a contract to assess compliance with the EU Digital Operational Resilience Act (DORA).

For this contract titled "${contractTitle}", assess the presence of DORA-relevant clauses. For each category below, respond in this exact JSON format:

{
  "criticalFunctions": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "subcontracting": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "auditRights": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "terminationRights": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "exitStrategy": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "incidentNotification": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "dataLocation": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "businessContinuity": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "securityMeasures": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "regulatoryCooperation": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "liability": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  },
  "serviceLevelAgreements": {
    "present": "Yes|No|Unclear",
    "summary": "Summarize the clause if present, or state 'Not found'",
    "complianceRisk": "Low|Medium|High"
  }
}

### DORA Clause Assessment Categories:

1. **Critical / Important Functions** - Does the agreement support critical or important functions under EBA/ESMA guidance?

2. **Subcontracting** - Are subcontractors permitted? Is prior notification or approval required? Can critical functions be subcontracted?

3. **Audit, Access & Inspection Rights** - Are unrestricted audit and inspection rights granted (incl. access to logs, data centers, personnel)? Are there frequency or scope limitations?

4. **Termination Rights** - Can the customer terminate for: Material breach? Regulatory breach or noncompliance? Without penalty for disruption of critical service?

5. **Exit Strategy / Transition Support** - Is there a defined exit or transition plan? Are system and data handovers addressed? Is support for transitioning to a new provider included?

6. **Incident Notification** - Are ICT-related incidents reported within defined timeframes? Are timelines and escalation paths defined? Are obligations aligned with DORA?

7. **Data Location & Processing** - Is the data location (EU/EEA or third country) specified? Are there data transfer safeguards and GDPR compliance terms?

8. **Business Continuity & Disaster Recovery (BC/DR)** - Is there a BCP/DRP? Are RTO (Recovery Time Objective) and RPO (Recovery Point Objective) defined?

9. **Security Measures** - Are security obligations aligned with ISO 27001, NIST, or NIS2? Are obligations for penetration testing, encryption, vulnerability management, and access control defined?

10. **Regulatory Cooperation** - Are providers obligated to cooperate with EU supervisory authorities? Do clauses support regulator access to records and systems?

11. **Liability & Insurance** - Is liability for operational risk clearly defined? Is cyber insurance coverage mentioned or required?

12. **Service Level Agreements (SLAs)** - Are SLAs defined for uptime, support, and service recovery? Are monitoring and enforcement mechanisms in place?

**IMPORTANT:** Your response MUST be a valid JSON object matching the exact structure shown above. Do not include explanatory text outside the JSON.

Here is the contract text to analyze:

${textContent}`;

      // Call Gemini API using @google/genai SDK
      const contents = {
        role: "user",
        parts: [
          {
            text: compliancePrompt,
          },
        ],
      };

      logger.info(
        `Making API call to Gemini with text-based content size: ${JSON.stringify(contents).length
        } characters`
      );

      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.1,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 4096,
        },
        contents,
      });

      logger.info(`Gemini API response received successfully`);

      // Extract response text
      let complianceText = "";
      if (response && response.text) {
        complianceText = response.text;
      } else {
        throw new Error("Invalid response structure from Gemini API");
      }

      if (!complianceText || complianceText.trim().length === 0) {
        throw new Error("AI generated empty compliance analysis");
      }

      // Parse JSON response
      let complianceAnalysis;
      try {
        // Clean the response text to extract JSON
        const jsonMatch = complianceText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error("No JSON found in response");
        }
        complianceAnalysis = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        logger.error("Failed to parse compliance analysis JSON:", parseError);
        throw new Error("Failed to parse compliance analysis from AI response");
      }

      logger.info(
        "Successfully generated DORA compliance analysis from text content"
      );
      return complianceAnalysis;
    } catch (error: any) {
      logger.error(
        "Error generating DORA compliance analysis from text content:",
        error
      );

      // Log more specific error details
      if (error.response) {
        logger.error(`API Error Status: ${error.response.status}`);
        logger.error(
          `API Error Data:`,
          JSON.stringify(error.response.data, null, 2)
        );
      } else if (error.request) {
        logger.error("No response received from API");
      } else {
        logger.error(`Error setting up request: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Updates compliance analysis for a contract extraction
   * @param contractId Contract ID
   * @param tenantId Tenant ID
   * @param complianceAnalysis Compliance analysis data
   */
  async updateComplianceAnalysis(
    contractId: string,
    tenantId: string,
    complianceAnalysis: any
  ): Promise<void> {
    try {
      logger.info(`Updating compliance analysis for contract: ${contractId}`);

      // Get existing extraction
      const existingExtraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );

      if (!existingExtraction) {
        throw new Error("Contract extraction not found");
      }

      // Structure the compliance analysis properly
      const structuredComplianceAnalysis = {
        dora: complianceAnalysis,
        extractionDate: new Date(),
        processingTimeMs: Date.now() - Date.now(), // This would be calculated properly in real implementation
      };

      // Update the extraction record with compliance analysis
      await this.contractExtractionRepository.update(
        existingExtraction.id,
        tenantId,
        {
          complianceAnalysis: structuredComplianceAnalysis,
        }
      );

      logger.info(
        `Successfully updated compliance analysis for contract: ${contractId}`
      );
    } catch (error) {
      logger.error(
        `Error updating compliance analysis for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Updates integrity analysis for a contract extraction
   * @param contractId Contract ID
   * @param tenantId Tenant ID
   * @param integrityAnalysis Integrity analysis data
   */
  async updateIntegrityAnalysis(
    contractId: string,
    tenantId: string,
    integrityAnalysis: any
  ): Promise<void> {
    try {
      logger.info(
        `Updating integrity analysis for contract: ${contractId}`
      );

      // Get existing extraction record
      const existingExtraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );

      if (!existingExtraction) {
        throw new Error("Contract extraction not found");
      }

      // Update the extraction record with integrity analysis
      await this.contractExtractionRepository.update(
        existingExtraction.id,
        tenantId,
        {
          integrityAnalysis: integrityAnalysis,
        }
      );

      logger.info(
        `Successfully updated integrity analysis for contract: ${contractId}`
      );
    } catch (error) {
      logger.error(
        `Error updating integrity analysis for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Generates a comprehensive AI summary of a contract based on extracted metadata
   * @param contractData - Contract data including metadata and extracted fields
   * @returns Comprehensive narrative summary
   */
  async generateContractSummary(contractData: any): Promise<string> {
    try {
      logger.info(
        `Generating comprehensive AI summary for contract: ${contractData.title}`
      );

      // Prepare the contract information for AI analysis
      const contractInfo = {
        title: contractData.title,
        description: contractData.description,
        agreementType: contractData.agreementType,
        status: contractData.status,
        value: contractData.value,
        counterparty: contractData.counterparty,
        startDate: contractData.startDate,
        endDate: contractData.endDate,
        renewalDate: contractData.renewalDate,
        metadata: contractData.metadata?.autoExtractedFields || {},
      };

      // Create a comprehensive prompt for AI summary generation
      const summaryPrompt = `
You are an expert contract analyst. Generate a comprehensive, professional narrative summary of the following contract. The summary should be detailed, well-structured, and written in flowing paragraphs (not bullet points).

Contract Information:
${JSON.stringify(contractInfo, null, 2)}

Please create a comprehensive summary that includes:

1. **Contract Overview and Parties**: Complete contract overview with full context, all parties involved (including subsidiaries and affiliates), agreement type, current status, and contract value WITH DETAILED JUSTIFICATION.

2. **Financial Terms and Value Justification**: For the contract value, provide detailed supporting information that explains the basis for the amount. Analyze the metadata to identify pricing components, service breakdowns, payment schedules, or cost structures that justify how the contract value was determined. Look for patterns like monthly/annual fees, unit pricing, service tiers, or cost categories. Provide specific explanations such as "Value based on monthly service fees" or "Total derived from multiple service components" with any available details from the extracted metadata.

3. **Payment Schedules and Timeline**: All financial terms, payment schedules, pricing details, comprehensive date information (effective, execution, expiration, renewal, notice periods), and contract duration.

4. **Scope and Service Requirements**: Detailed scope of work, services, deliverables, performance requirements, SLA requirements, service levels, performance metrics, and contractual obligations.

5. **Risk Management and Governance**: Risk factors, liability terms, indemnification, compliance requirements, termination conditions, renewal terms, notice requirements, dispute resolution, and governing law.

Requirements:
- Write in natural, flowing language as a professional contract analysis report
- Include specific values, dates, and terms rather than generic statements
- For contract value, ALWAYS provide detailed justification explaining HOW the value was calculated or determined, not just WHAT the value is
- Analyze the metadata for pricing components, service breakdowns, or cost structures
- Handle missing information gracefully without mentioning empty fields
- Aim for 3-4 comprehensive paragraphs
- Focus on the most important and meaningful contract elements
- Ensure the summary reads like a professional legal document analysis

Generate the summary now:`;

      // Use Gemini AI to generate the summary
      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.3,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 2048,
        },
        contents: {
          role: "user",
          parts: [
            {
              text: summaryPrompt,
            },
          ],
        },
      });

      // Extract response text
      let summaryText = "";
      if (response && response.text) {
        summaryText = response.text;
      } else {
        throw new Error("Invalid response structure from Gemini API");
      }

      if (!summaryText || summaryText.trim().length === 0) {
        throw new Error("AI generated empty summary");
      }

      logger.info("Successfully generated comprehensive AI contract summary");
      return summaryText.trim();
    } catch (error) {
      logger.error("Error generating AI contract summary:", error);

      // Fallback to a basic summary if AI generation fails
      const fallbackSummary = this.generateFallbackSummary(contractData);
      logger.info("Using fallback summary generation");
      return fallbackSummary;
    }
  }

  /**
   * Generates a fallback summary when AI generation fails
   * @param contractData - Contract data
   * @returns Basic summary
   */
  private generateFallbackSummary(contractData: any): string {
    const metadata = contractData.metadata?.autoExtractedFields || {};

    let summary = `This ${contractData.agreementType || "contract"} titled "${contractData.title
      }" `;

    if (contractData.counterparty) {
      summary += `involves ${contractData.counterparty} as the counterparty. `;
    }

    summary += `The agreement is currently ${contractData.status?.toLowerCase() || "active"
      }`;

    if (contractData.value) {
      summary += ` with a contract value of ${contractData.value}`;
    }

    if (contractData.startDate) {
      summary += ` and became effective on ${new Date(
        contractData.startDate
      ).toLocaleDateString()}`;
    }

    if (contractData.endDate) {
      summary += `, extending until ${new Date(
        contractData.endDate
      ).toLocaleDateString()}`;
    }

    summary += ". ";

    if (
      metadata.keyTerms &&
      Array.isArray(metadata.keyTerms) &&
      metadata.keyTerms.length > 0
    ) {
      summary += `\n\nKey contractual terms include ${metadata.keyTerms
        .slice(0, 5)
        .join(", ")}. `;
    }

    summary += `\n\nThis contract was processed and analyzed by the system on ${new Date().toLocaleDateString()}.`;

    return summary;
  }

  /**
   * Generates a tabular AI summary directly from the contract document
   * @param documentBuffer - The original contract document buffer
   * @param fileName - Original file name
   * @param contractData - Basic contract data for context
   * @returns Structured tabular summary as array of objects
   */
  async generateTabularSummaryFromDocument(
    documentBuffer: Buffer,
    fileName: string,
    contractData?: any
  ): Promise<Record<string, any>[]> {
    try {
      logger.info(
        `[TABULAR_SUMMARY] Starting tabular AI summary generation from document: ${fileName}`
      );

      logger.info(
        `[TABULAR_SUMMARY] API key found, proceeding with generation`
      );

      // Convert document to base64 for Gemini API
      logger.info(
        `[TABULAR_SUMMARY] Converting document to base64 (size: ${documentBuffer.length} bytes)`
      );
      const base64Document = documentBuffer.toString("base64");
      logger.info(
        `[TABULAR_SUMMARY] Document converted to base64 (length: ${base64Document.length} characters)`
      );

      // Determine MIME type based on file extension
      const mimeType = this.getMimeTypeFromFileName(fileName);
      logger.info(
        `[TABULAR_SUMMARY] Determined MIME type: ${mimeType} for file: ${fileName}`
      );

      // Create a comprehensive prompt for tabular summary generation
      const tabularPrompt = `
You are an expert contract analyst with deep expertise in legal document analysis. Analyze the attached contract document and extract ALL relevant contract information in a comprehensive structured tabular format.

Please extract information across ALL the following categories and return it as a JSON array where each object has "Aspect" and "Details" keys:

**CORE CONTRACT INFORMATION:**
- Document Type/Classification
- Document Number/ID/Reference
- Document Version/Revision
- Issue/Creation Date
- Effective/Start Date
- Expiration/End Date
- Signature Date
- Amendment/Modification Dates
- Document Status

**ALL PARTIES AND ENTITIES:**
- Primary Contractor/Provider
- Primary Client/Customer
- Secondary Parties
- Subsidiaries Mentioned
- Affiliates Referenced
- Third-Party Vendors
- Guarantors/Sureties
- Authorized Signatories (all parties)
- Legal Representatives
- Contact Information

**COMPREHENSIVE FINANCIAL TERMS:**
- Total Contract Value (with detailed breakdown and justification)
- Contract Value Calculation Basis (how the total was derived)
- Cost Components and Line Items (itemized breakdown)
- Service-Based Pricing (monthly/annual fees, per-unit costs)
- Hardware vs Software vs Services Breakdown
- Currency
- Payment Schedule/Terms
- Payment Methods
- Pricing Model/Structure
- Unit Prices
- Milestone Payments
- Advance Payments
- Retention Amounts
- Late Payment Penalties
- Interest Rates
- Cost Escalation Clauses
- Budget Allocations
- Expense Reimbursements
- Document Section References for Financial Terms

**DETAILED TIMELINE INFORMATION:**
- Project Start Date
- Project End Date
- Milestone Dates
- Delivery Deadlines
- Review Periods
- Notice Periods
- Renewal Dates
- Option Exercise Deadlines
- Warranty Periods
- Maintenance Periods

**RELATIONSHIPS AND REFERENCES:**
- Master Agreement References
- Related Contract Numbers
- Appendices/Exhibits
- Amendment References
- Incorporated Documents
- Cross-References
- Superseded Agreements
- Dependent Contracts

**COMPREHENSIVE CLAUSE ANALYSIS:**
- Payment Terms and Conditions
- Termination Conditions and Notice Periods
- Renewal and Auto-Renewal Terms
- Liability Limitations and Caps
- Indemnification Provisions
- Confidentiality and Non-Disclosure Terms
- Intellectual Property Rights and Ownership
- Non-Compete and Non-Solicitation Clauses
- Force Majeure Provisions and Exceptions
- Change Order and Amendment Procedures
- Dispute Resolution and Arbitration Mechanisms
- Warranty and Guarantee Terms
- Service Level Agreements and Performance Standards
- Compliance and Regulatory Requirements
- Insurance and Bonding Requirements
- Assignment and Transfer Rights
- Notice Requirements and Communication Protocols
- Governing Law and Jurisdiction
- Severability and Enforceability Clauses
- Integration and Entire Agreement Clauses

**RISK FACTORS AND COMPLIANCE:**
- Compliance Requirements
- Regulatory Standards
- Audit Rights
- Inspection Rights
- Penalties/Liquidated Damages
- Breach Conditions
- Default Provisions
- Insurance Requirements
- Bonding Requirements
- Security Clearance Needs

**OPERATIONAL DETAILS:**
- Scope of Work/Services
- Deliverables
- Performance Standards
- Service Level Agreements
- Quality Standards
- Acceptance Criteria
- Geographic Limitations
- Operational Constraints
- Resource Requirements
- Staffing Requirements

**LEGAL FRAMEWORK:**
- Governing Law
- Jurisdiction
- Venue for Disputes
- Applicable Regulations
- Compliance Standards
- Legal Precedence
- Conflict Resolution
- Arbitration Clauses

Requirements:
- Return ONLY a valid JSON array
- Each object must have exactly two keys: "Aspect" and "Details"
- Extract SPECIFIC values from the document, not generic placeholders
- If information is not available, omit that aspect entirely
- Format monetary values with currency symbols (e.g., "$14,191.56")
- Format dates in readable format (e.g., "January 3, 2018")
- Keep details concise but comprehensive
- Extract 50-75 aspects for comprehensive coverage
- Prioritize the most critical and specific contract elements
- Include ALL parties, dates, financial terms, and relationships mentioned
- For contract value, ALWAYS include detailed justification explaining HOW the value was calculated
- Reference specific document sections, schedules, appendices, or line items that support financial terms
- Break down total contract value into components (services, hardware, software, etc.) when available
- Include pricing models, payment schedules, and cost structures with document references
- Capture specific clause details and risk factors
- For each significant clause, include: clause type, key provisions, risk level, and specific terms
- Extract clause-specific information such as notice periods, liability caps, termination conditions
- Include warranty terms, service levels, compliance requirements, and performance standards

Example format:
[
  {"Aspect": "Document Type", "Details": "Master Service Agreement"},
  {"Aspect": "Master Agreement Reference", "Details": "MSA-2024-001 dated January 15, 2024"},
  {"Aspect": "Primary Contractor", "Details": "TechCorp Solutions Inc. (Delaware Corporation)"},
  {"Aspect": "Subsidiary Involved", "Details": "TechCorp International Ltd. (UK Subsidiary)"},
  {"Aspect": "Total Contract Value", "Details": "$2,500,000 USD over 3 years"},
  {"Aspect": "Contract Value Calculation Basis", "Details": "Based on monthly technical support services ($69,444/month) as detailed in Schedule A, Section 2.1"},
  {"Aspect": "Cost Components Breakdown", "Details": "Professional services ($1,800,000), software licenses ($500,000), training ($200,000) per Appendix B"},
  {"Aspect": "Service-Based Pricing", "Details": "Technical support: $69,444/month, premium support: $15,000/month as specified in Section 3.2"},
  {"Aspect": "Payment Schedule", "Details": "Monthly payments of $69,444 due by 15th of each month"},
  {"Aspect": "Document Section References for Financial Terms", "Details": "Pricing detailed in Schedule A, payment terms in Section 4.1, cost breakdown in Appendix B"},
  {"Aspect": "Termination for Convenience", "Details": "Either party with 90 days written notice"},
  {"Aspect": "Liability Cap", "Details": "Limited to 12 months of fees, excluding willful misconduct"},
  {"Aspect": "Confidentiality Clause", "Details": "5-year confidentiality period, includes trade secrets and proprietary information"},
  {"Aspect": "Service Level Agreement", "Details": "99.9% uptime requirement with penalties for non-compliance"},
  {"Aspect": "Intellectual Property Rights", "Details": "Client retains ownership, contractor grants perpetual license"},
  {"Aspect": "Force Majeure Provision", "Details": "Standard force majeure with 30-day notice requirement"},
  {"Aspect": "Dispute Resolution", "Details": "Mandatory arbitration under AAA Commercial Rules in Delaware"},
  {"Aspect": "Related SOW Reference", "Details": "Statement of Work SOW-2024-TechCorp-001"}
]

Analyze the document comprehensively and extract ALL relevant contract information now:`;

      // Call Gemini API using @google/genai SDK
      const contents = {
        role: "user",
        parts: [
          {
            text: tabularPrompt,
          },
          {
            inlineData: {
              mimeType: mimeType,
              data: base64Document,
            },
          },
        ],
      };

      logger.info(
        `[TABULAR_SUMMARY] Making API call to Gemini with content size: ${JSON.stringify(contents).length
        } characters`
      );

      let response;
      try {
        response = await this.googleAI.models.generateContent({
          model: "gemini-2.5-pro",
          config: {
            temperature: 0.05,
            topP: 0.7,
            topK: 15,
            maxOutputTokens: 4096,
          },
          contents,
        });
      } catch (apiError) {
        logger.error(`[TABULAR_SUMMARY] Gemini API call failed:`, {
          message:
            apiError instanceof Error ? apiError.message : String(apiError),
          status: (apiError as any)?.response?.status,
          statusText: (apiError as any)?.response?.statusText,
          data: (apiError as any)?.response?.data,
        });
        throw new Error(
          `Gemini API call failed: ${apiError instanceof Error ? apiError.message : String(apiError)
          }`
        );
      }

      logger.info(
        `[TABULAR_SUMMARY] Received response from Gemini API successfully`
      );
      logger.info(
        `[TABULAR_SUMMARY] Response structure: ${JSON.stringify(
          response,
          null,
          2
        ).substring(0, 1000)}...`
      );

      // Extract response text
      logger.info(
        `[TABULAR_SUMMARY] Extracting response text from API response...`
      );
      let responseText = "";
      if (response && response.text) {
        responseText = response.text;
        logger.info(
          `[TABULAR_SUMMARY] Successfully extracted response text (length: ${responseText.length} characters)`
        );
      } else {
        logger.error(
          `[TABULAR_SUMMARY] Invalid response structure from Gemini API`
        );
        logger.error(
          `[TABULAR_SUMMARY] Response structure: ${JSON.stringify(
            response,
            null,
            2
          )}`
        );
        throw new Error("Invalid response structure from Gemini API");
      }

      if (!responseText || responseText.trim().length === 0) {
        logger.error(`[TABULAR_SUMMARY] AI generated empty tabular summary`);
        throw new Error("AI generated empty tabular summary");
      }

      logger.info(
        `[TABULAR_SUMMARY] Response text preview: ${responseText.substring(
          0,
          200
        )}...`
      );

      // Parse JSON response
      try {
        logger.info(
          `[TABULAR_SUMMARY] Raw AI response preview: ${responseText.substring(
            0,
            500
          )}...`
        );

        // Clean the response text to extract JSON
        let jsonMatch = responseText.match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          logger.warn(
            "[TABULAR_SUMMARY] No JSON array found with standard regex, trying alternative patterns"
          );
          // Try to find JSON with code blocks
          jsonMatch = responseText.match(/```json\s*(\[[\s\S]*?\])\s*```/);
          if (jsonMatch) {
            jsonMatch[0] = jsonMatch[1]; // Use the captured group
          } else {
            // Try to find any array-like structure
            jsonMatch = responseText.match(/(\[[\s\S]*?\])/);
          }
        }

        if (!jsonMatch) {
          logger.error("[TABULAR_SUMMARY] No JSON array found in AI response");
          logger.error("[TABULAR_SUMMARY] Full response text:", responseText);
          throw new Error("No JSON array found in response");
        }

        logger.info(
          `[TABULAR_SUMMARY] Extracted JSON string: ${jsonMatch[0].substring(
            0,
            300
          )}...`
        );

        let tabularData;
        try {
          tabularData = JSON.parse(jsonMatch[0]);
        } catch (jsonError) {
          logger.error("[TABULAR_SUMMARY] JSON parsing failed:", jsonError);
          logger.error("[TABULAR_SUMMARY] Attempted to parse:", jsonMatch[0]);
          throw new Error(
            `JSON parsing failed: ${jsonError instanceof Error ? jsonError.message : String(jsonError)
            }`
          );
        }

        // Validate the structure
        if (!Array.isArray(tabularData)) {
          logger.error(
            "[TABULAR_SUMMARY] AI response is not an array:",
            typeof tabularData
          );
          throw new Error("Response is not an array");
        }

        logger.info(
          `[TABULAR_SUMMARY] Parsed ${tabularData.length} items from AI response`
        );

        // Validate each item has the required structure
        const validatedData = tabularData.filter((item, index) => {
          const isValid =
            item &&
            typeof item === "object" &&
            typeof item.Aspect === "string" &&
            typeof item.Details === "string" &&
            item.Aspect.trim() !== "" &&
            item.Details.trim() !== "";

          if (!isValid) {
            logger.warn(
              `[TABULAR_SUMMARY] Invalid item at index ${index}:`,
              item
            );
          }

          return isValid;
        });

        if (validatedData.length === 0) {
          logger.error(
            "[TABULAR_SUMMARY] No valid tabular data extracted after validation"
          );
          logger.error("[TABULAR_SUMMARY] Original data:", tabularData);
          throw new Error("No valid tabular data extracted");
        }

        logger.info(
          `[TABULAR_SUMMARY] Successfully generated tabular AI contract summary from document with ${validatedData.length} aspects`
        );
        return validatedData;
      } catch (parseError) {
        logger.error(
          "[TABULAR_SUMMARY] Error parsing tabular summary JSON:",
          parseError
        );
        logger.error(
          "[TABULAR_SUMMARY] Response text that failed to parse:",
          responseText
        );
        throw new Error(
          `Failed to parse AI-generated tabular summary: ${parseError instanceof Error
            ? parseError.message
            : String(parseError)
          }`
        );
      }
    } catch (error) {
      logger.error(
        `[TABULAR_SUMMARY] ERROR generating tabular AI contract summary from document:`,
        error
      );
      logger.error(`[TABULAR_SUMMARY] Error details:`, {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        fileName,
        contractDataAvailable: !!contractData,
      });

      // Fallback to metadata-based tabular summary if document analysis fails
      if (contractData) {
        logger.info(
          `[TABULAR_SUMMARY] FALLBACK: Attempting metadata-based tabular summary generation`
        );
        try {
          const metadataTabular =
            this.generateTabularSummaryFromMetadata(contractData);
          logger.info(
            `[TABULAR_SUMMARY] FALLBACK SUCCESS: Generated ${metadataTabular.length} aspects from metadata`
          );
          return metadataTabular;
        } catch (metadataError) {
          logger.error(
            `[TABULAR_SUMMARY] FALLBACK FAILED: Metadata-based generation failed:`,
            metadataError
          );
        }
      }

      // Final fallback to basic tabular summary
      logger.info(
        `[TABULAR_SUMMARY] FINAL FALLBACK: Using basic fallback tabular summary generation`
      );
      const fallbackTabular = this.generateFallbackTabularSummary(
        contractData,
        fileName
      );
      logger.info(
        `[TABULAR_SUMMARY] FINAL FALLBACK SUCCESS: Generated ${fallbackTabular.length} basic aspects`
      );
      return fallbackTabular;
    }
  }

  /**
   * Generates a comprehensive tabular summary from contract metadata
   * @param contractData - Contract data including metadata and extracted fields
   * @returns Structured tabular summary as array of objects
   */
  generateTabularSummaryFromMetadata(contractData: any): Record<string, any>[] {
    logger.info(
      `[METADATA_TABULAR] Starting comprehensive metadata-based tabular summary generation`
    );
    logger.info(`[METADATA_TABULAR] Contract data available:`, {
      hasTitle: !!contractData?.title,
      hasAgreementType: !!contractData?.agreementType,
      hasStatus: !!contractData?.status,
      hasValue: !!contractData?.value,
      hasCounterparty: !!contractData?.counterparty,
      hasMetadata: !!contractData?.metadata,
      metadataFields: contractData?.metadata
        ? Object.keys(contractData.metadata)
        : [],
    });

    const tabularData: Record<string, any>[] = [];
    const metadata = contractData.metadata?.autoExtractedFields || {};

    logger.info(
      `[METADATA_TABULAR] Extracted metadata fields:`,
      Object.keys(metadata)
    );

    // CORE CONTRACT INFORMATION
    if (contractData.title) {
      tabularData.push({
        Aspect: "Contract Title",
        Details: contractData.title,
      });
    }

    if (contractData.agreementType) {
      tabularData.push({
        Aspect: "Agreement Type",
        Details: contractData.agreementType,
      });
    }

    if (contractData.status) {
      tabularData.push({
        Aspect: "Contract Status",
        Details: contractData.status,
      });
    }

    if (contractData.description) {
      tabularData.push({
        Aspect: "Description",
        Details: contractData.description,
      });
    }

    // PARTIES AND ENTITIES
    if (contractData.counterparty) {
      tabularData.push({
        Aspect: "Primary Counterparty",
        Details: contractData.counterparty,
      });
    }

    if (contractData.provider) {
      tabularData.push({
        Aspect: "Service Provider",
        Details: contractData.provider,
      });
    }

    // FINANCIAL INFORMATION
    if (contractData.value) {
      tabularData.push({
        Aspect: "Total Contract Value",
        Details: contractData.value,
      });
    }

    // TIMELINE INFORMATION
    if (contractData.startDate) {
      tabularData.push({
        Aspect: "Effective Date",
        Details: new Date(contractData.startDate).toLocaleDateString(),
      });
    }

    if (contractData.endDate) {
      tabularData.push({
        Aspect: "Expiration Date",
        Details: new Date(contractData.endDate).toLocaleDateString(),
      });
    }

    if (contractData.renewalDate) {
      tabularData.push({
        Aspect: "Renewal Date",
        Details: new Date(contractData.renewalDate).toLocaleDateString(),
      });
    }

    // COMPREHENSIVE METADATA EXTRACTION
    const metadataMapping: Record<string, string> = {
      // Document Information
      contract_number: "Contract Number",
      document_type: "Document Type",
      document_version: "Document Version",
      issue_date: "Issue Date",
      signature_date: "Signature Date",

      // Parties
      supplier: "Supplier",
      vendor: "Vendor",
      client: "Client",
      customer: "Customer",
      contractor: "Contractor",
      subcontractor: "Subcontractor",
      authorized_signatory: "Authorized Signatory",

      // Financial Terms
      currency: "Currency",
      payment_terms: "Payment Terms",
      payment_schedule: "Payment Schedule",
      payment_method: "Payment Method",
      total_value: "Total Value",
      unit_price: "Unit Price",
      pricing_model: "Pricing Model",

      // Legal and Compliance
      governing_law: "Governing Law",
      jurisdiction: "Jurisdiction",
      dispute_resolution: "Dispute Resolution",
      compliance_requirements: "Compliance Requirements",

      // Operational Details
      scope_of_work: "Scope of Work",
      deliverables: "Deliverables",
      service_levels: "Service Level Requirements",
      performance_metrics: "Performance Metrics",
      geographic_scope: "Geographic Scope",

      // Terms and Conditions
      termination_clause: "Termination Conditions",
      termination_notice: "Termination Notice Period",
      renewal_terms: "Renewal Terms",
      auto_renewal: "Auto-Renewal",
      notice_period: "Notice Period",
      liability_limit: "Liability Limitation",
      indemnification: "Indemnification Terms",
      confidentiality: "Confidentiality Provisions",
      intellectual_property: "Intellectual Property Rights",

      // Risk and Insurance
      insurance_requirements: "Insurance Requirements",
      bonding_requirements: "Bonding Requirements",
      penalties: "Penalties",
      liquidated_damages: "Liquidated Damages",
      force_majeure: "Force Majeure",

      // Relationships
      master_agreement: "Master Agreement Reference",
      related_contracts: "Related Contracts",
      amendments: "Amendments",
      appendices: "Appendices",
      exhibits: "Exhibits",

      // Milestones and Deadlines
      project_start: "Project Start Date",
      project_end: "Project End Date",
      milestone_dates: "Milestone Dates",
      delivery_deadlines: "Delivery Deadlines",
      review_periods: "Review Periods",

      // Quality and Standards
      quality_standards: "Quality Standards",
      acceptance_criteria: "Acceptance Criteria",
      inspection_rights: "Inspection Rights",
      audit_rights: "Audit Rights",
    };

    // Process all metadata fields with enhanced mapping
    Object.entries(metadata).forEach(([key, value]) => {
      if (value && typeof value === "string" && value.trim() !== "") {
        const aspectName =
          metadataMapping[key] ||
          key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        tabularData.push({ Aspect: aspectName, Details: value });
      }
    });

    // Add any additional contract-specific fields
    if (contractData.contractNumber) {
      tabularData.push({
        Aspect: "Contract Number",
        Details: contractData.contractNumber,
      });
    }

    if (contractData.version) {
      tabularData.push({
        Aspect: "Contract Version",
        Details: contractData.version.toString(),
      });
    }

    if (contractData.renewalType) {
      tabularData.push({
        Aspect: "Renewal Type",
        Details: contractData.renewalType,
      });
    }

    if (contractData.noticePeriodDays) {
      tabularData.push({
        Aspect: "Notice Period",
        Details: `${contractData.noticePeriodDays} days`,
      });
    }

    if (contractData.isAutoRenew !== undefined) {
      tabularData.push({
        Aspect: "Auto-Renewal",
        Details: contractData.isAutoRenew ? "Yes" : "No",
      });
    }

    logger.info(
      `[METADATA_TABULAR] Generated ${tabularData.length} comprehensive tabular aspects from metadata`
    );

    if (tabularData.length > 0) {
      logger.info(
        `[METADATA_TABULAR] SUCCESS: Returning ${tabularData.length} aspects`
      );
      return tabularData;
    } else {
      logger.info(
        `[METADATA_TABULAR] No aspects generated, falling back to basic fallback`
      );
      return this.generateFallbackTabularSummary(contractData, "Unknown");
    }
  }

  /**
   * Generates a comprehensive fallback tabular summary when other methods fail
   * @param contractData - Contract data (may be null)
   * @param fileName - Original file name
   * @returns Enhanced basic tabular summary
   */
  generateFallbackTabularSummary(
    contractData: any,
    fileName: string
  ): Record<string, any>[] {
    const tabularData: Record<string, any>[] = [];

    // Document Information
    tabularData.push({ Aspect: "Document Name", Details: fileName });
    tabularData.push({ Aspect: "Processing Status", Details: "Processed" });
    tabularData.push({
      Aspect: "Analysis Date",
      Details: new Date().toLocaleDateString(),
    });

    // Core Contract Information (if available)
    if (contractData?.title) {
      tabularData.unshift({
        Aspect: "Contract Title",
        Details: contractData.title,
      });
    }

    if (contractData?.agreementType) {
      tabularData.push({
        Aspect: "Agreement Type",
        Details: contractData.agreementType,
      });
    }

    if (contractData?.status) {
      tabularData.push({
        Aspect: "Contract Status",
        Details: contractData.status,
      });
    }

    if (contractData?.counterparty) {
      tabularData.push({
        Aspect: "Counterparty",
        Details: contractData.counterparty,
      });
    }

    if (contractData?.value) {
      tabularData.push({
        Aspect: "Contract Value",
        Details: contractData.value,
      });
    }

    // Timeline Information (if available)
    if (contractData?.startDate) {
      tabularData.push({
        Aspect: "Start Date",
        Details: new Date(contractData.startDate).toLocaleDateString(),
      });
    }

    if (contractData?.endDate) {
      tabularData.push({
        Aspect: "End Date",
        Details: new Date(contractData.endDate).toLocaleDateString(),
      });
    }

    // Additional Contract Details (if available)
    if (contractData?.contractNumber) {
      tabularData.push({
        Aspect: "Contract Number",
        Details: contractData.contractNumber,
      });
    }

    if (contractData?.version) {
      tabularData.push({
        Aspect: "Version",
        Details: contractData.version.toString(),
      });
    }

    if (contractData?.renewalType) {
      tabularData.push({
        Aspect: "Renewal Type",
        Details: contractData.renewalType,
      });
    }

    if (contractData?.noticePeriodDays) {
      tabularData.push({
        Aspect: "Notice Period",
        Details: `${contractData.noticePeriodDays} days`,
      });
    }

    // Processing Information
    tabularData.push({
      Aspect: "Extraction Method",
      Details: "Fallback Processing",
    });
    tabularData.push({
      Aspect: "Data Source",
      Details: contractData ? "Contract Metadata" : "Document Only",
    });

    return tabularData;
  }

  /**
   * Generates a basic fallback summary when all other methods fail
   * @param fileName - Original file name
   * @returns Basic summary
   */
  private generateBasicFallbackSummary(fileName: string): string {
    return `This contract document "${fileName}" has been uploaded and processed by the system. A comprehensive analysis could not be completed at this time, but the document has been stored and is available for review. The system will continue to process and analyze the contract content to provide detailed insights and metadata extraction.`;
  }

  /**
   * Determines MIME type based on file extension
   * @param fileName - Original file name
   * @returns MIME type string
   */
  private getMimeTypeFromFileName(fileName: string): string {
    const extension = fileName.toLowerCase().split(".").pop();

    switch (extension) {
      case "pdf":
        return "application/pdf";
      case "doc":
        return "application/msword";
      case "docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case "txt":
        return "text/plain";
      case "rtf":
        return "application/rtf";
      default:
        return "application/octet-stream";
    }
  }



  /**
   * Processes analysis fields in background for any software contract
   * @param documentBuffer - Document buffer
   * @param fileName - Original file name
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   */
  private async processAnalysisFieldsInBackground(
    documentBuffer: Buffer,
    fileName: string,
    contractId: string,
    tenantId: string
  ): Promise<void> {
    try {
      logger.info(
        `Starting analysis fields background processing for contract: ${contractId}`
      );

      // Extract analysis fields
      const analysisFields = await this.extractAnalysisFields(
        documentBuffer,
        fileName
      );

      if (Object.keys(analysisFields).length === 0) {
        logger.warn(
          `No analysis fields extracted for contract: ${contractId}. This could be due to:`,
          {
            possibleCauses: [
              "AI model returned invalid JSON",
              "Document format not supported",
              "Document content not recognizable as software contract",
              "API timeout or error",
            ],
            fileName,
            contractId,
          }
        );
        return;
      }

      // Check if this is a fallback response
      if (analysisFields.fallback) {
        logger.info(
          `Using fallback analysis fields for contract: ${contractId}`
        );
      }

      // Convert to AnalysisFields format
      const formattedAnalysisFields: Record<string, any> = {};
      Object.entries(analysisFields).forEach(([key, value]: [string, any]) => {
        if (key === "purchasing") {
          // Handle purchasing data (both new object format and legacy array)
          formattedAnalysisFields[key] = value;
        } else if (
          value &&
          typeof value === "object" &&
          value.value !== undefined
        ) {
          formattedAnalysisFields[key] = {
            value: value.value,
            confidence: value.confidence || 0.5,
          };
        }
      });

      // Add metadata
      formattedAnalysisFields.extractionDate = new Date();
      formattedAnalysisFields.processingTimeMs = Date.now();

      // Update the extraction record with analysis fields
      const existingExtraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );

      if (existingExtraction) {
        await this.contractExtractionRepository.update(
          existingExtraction.id,
          tenantId,
          {
            analysisFields: formattedAnalysisFields,
          }
        );

        logger.info(
          `Successfully updated contract ${contractId} with ${Object.keys(formattedAnalysisFields).length
          } analysis fields`
        );
      } else {
        logger.warn(
          `Could not find extraction record for contract ${contractId} to update with analysis fields`
        );
      }
    } catch (error) {
      logger.error(
        `Error in analysis fields background processing for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Generates bundle interconnection analysis for a group of contracts using Gemini AI
   * @param contractsData - Array of contract data with extraction information
   * @param folderName - Name of the folder/provider group
   * @returns Bundle interconnection analysis result
   */
  async generateBundleInterconnectionAnalysis(
    contractsData: any[],
    folderName: string
  ): Promise<any> {
    try {
      logger.info(`Starting bundle interconnection analysis for folder: ${folderName} with ${contractsData.length} contracts`);

      // Get clause mapping structure
      const clauseMappingStructure = await this.getClauseMappingStructure();

      // Prepare the prompt for Gemini
      const promptText = `You are a contract intelligence engine performing comprehensive bundle interconnection analysis for related contract documents.

**ANALYSIS SCOPE:**
Analyze all contracts in this provider folder to:
✅ **Group related documents** into logical contract bundles (separate hierarchies)
✅ **Establish document hierarchy** using predefined AgreementType classifications
✅ **Identify explicit relationships** via direct references (e.g., "pursuant to MSA dated...")
✅ **Detect inferred relationships** via shared parties, projects, timelines, or values
✅ **Perform comprehensive clause analysis** SEPARATELY for each contract group
✅ **Determine clause precedence** and detect conflicts ONLY within each group
✅ **Provide business implications** for each contract group independently

**CRITICAL**: Documents from different contract groups should NOT be compared for conflicts or precedence. Each group represents a separate contract family.

**DOCUMENT CLASSIFICATION:**
Use only these predefined AgreementType values: MSA|NDA|SOW|PO|SLA|DPA|BAA|EULA|LOI|MOA|MOU|JV|CA|LPA|SSA|ESA|PSA|TOS|DUA|OEM|RFP|RFQ|BPA|PPA|LSA|ISA|SPA|APA|TPA|IP|RSA|VARA|DDA|TSA|IA|INVOICE|SCHEDULE|ORDER|OTHER

**COMPREHENSIVE CLAUSE ANALYSIS:**
Analyze clauses using the EXACT structure from the mapping.json file. Use the categories and clause keys from the "general" section:

${clauseMappingStructure}

**IMPORTANT**:
- Use these EXACT category names and clause names as keys in your JSON response
- Only analyze clauses that exist in the contract data
- **CRITICAL**: Perform clause analysis SEPARATELY for each contract group
- Do NOT compare clauses between different contract groups
- Each group's "comprehensive_clause_analysis" should only include documents from that specific group

**CLAUSE STATUS CLASSIFICATION:**
- **SAME**: Identical or equivalent clauses across documents
- **CONFLICT**: Contradictory terms that create legal ambiguity
- **SUPERSEDING**: Later/more specific document overrides general terms
- **UNIQUE**: Clause exists in only one document
- **MISSING**: Critical clause absent from document where expected

**DATA SOURCE RULES:**
Use all available extracted data:
✅ Contract metadata (document name, agreementType, supplier, project name, reference IDs, etc.)
✅ Extracted clauses and values from supplier-specific fields
✅ Dynamic fields organized by categories
✅ Document relationships and references

**OUTPUT FORMAT:**
Return a JSON object with the following structure:
{
  "contract_groups": [
    {
      "group_id": "bundle_001",
      "documents": [
        {
          "name": "MSA_GlobalCloud.pdf",
          "agreementType": "MSA",
          "is_primary": true,
          "hierarchy_level": 1
        },
        {
          "name": "SOW_InfraEU.pdf",
          "agreementType": "SOW",
          "relationship_type": "explicit",
          "linked_by": ["references MSA by date and title", "same supplier"],
          "hierarchy_level": 2,
          "confidence": 0.95
        },
        {
          "name": "Invoice_00458.pdf",
          "agreementType": "INVOICE",
          "relationship_type": "inferred",
          "linked_by": ["same project", "invoice date matches SOW timeline"],
          "hierarchy_level": 3,
          "confidence": 0.78
        }
      ],
      "hierarchy": {
        "primary_document": "MSA_GlobalCloud.pdf",
        "document_chain": ["MSA_GlobalCloud.pdf", "SOW_InfraEU.pdf", "Invoice_00458.pdf"]
      },
      "comprehensive_clause_analysis": {
        "Use rights & restrictions": {
          "category_risk_assessment": {"risk_level": "HIGH", "conflicts": 1, "total_clauses": 2},
          "Grant of licence": {
            "MSA_GlobalCloud.pdf": {"status": "SAME", "value": "Non-exclusive license", "confidence": 0.9},
            "SOW_InfraEU.pdf": {"status": "SAME", "value": "Non-exclusive license", "confidence": 0.9},
            "analysis": "Consistent licensing terms across documents"
          },
          "Territorial scope": {
            "MSA_GlobalCloud.pdf": {"status": "CONFLICT", "value": "Worldwide", "confidence": 0.85},
            "SOW_InfraEU.pdf": {"status": "CONFLICT", "value": "EU only", "confidence": 0.8},
            "analysis": "Conflicting territorial restrictions"
          }
        },
        "General": {
          "category_risk_assessment": {"risk_level": "LOW", "conflicts": 0, "total_clauses": 2},
          "Order-document precedence": {
            "MSA_GlobalCloud.pdf": {"status": "UNIQUE", "value": "MSA governs", "confidence": 0.9},
            "analysis": "Clear precedence established"
          },
          "Auto-renewal": {
            "MSA_GlobalCloud.pdf": {"status": "SAME", "value": "No auto-renewal", "confidence": 0.9},
            "SOW_InfraEU.pdf": {"status": "SAME", "value": "No auto-renewal", "confidence": 0.9},
            "analysis": "Consistent renewal terms"
          }
        },
        "Commercial terms": {
          "category_risk_assessment": {"risk_level": "MEDIUM", "conflicts": 1, "total_clauses": 1},
          "Fees / payment terms": {
            "MSA_GlobalCloud.pdf": {"status": "SUPERSEDING", "value": "60 days", "confidence": 0.9},
            "SOW_InfraEU.pdf": {"status": "SUPERSEDING", "value": "45 days", "confidence": 0.95},
            "precedence_order": ["SOW_InfraEU.pdf", "MSA_GlobalCloud.pdf"],
            "analysis": "SOW overrides MSA payment terms"
          }
        }
      },
      "business_implications": {
        "critical_conflicts": ["Payment terms create enforcement risk", "Use restrictions may limit project scope"],
        "missing_clauses": ["Data protection terms absent from SOW", "Liability cap not defined in Invoice"],
        "recommendations": ["Align payment terms across documents", "Add DPA reference to SOW"]
      }
    }
  ],
  "standalone_documents": [
    {
      "name": "PO_Adhoc.pdf",
      "agreementType": "PO",
      "linked_group_id": null,
      "is_orphan": true
    }
  ],
  "overall_analysis": {
    "total_bundles": 1,
    "standalone_documents": 0,
    "high_risk_bundles": 1,
    "confidence_score": 0.87,
    "analysis_date": "${new Date().toISOString()}",
    "folder_name": "${folderName}"
  }
}

**ANALYSIS INSTRUCTIONS:**
- **Step 1 - Group Detection**: First, identify separate contract families/hierarchies based on:
  - **Explicit relationships**: Direct references (e.g., "pursuant to Master Agreement dated…")
  - **Inferred relationships**: Shared parties, matching project/service names, overlapping timelines, similar clause values
- **Step 2 - Per-Group Analysis**: For EACH contract group independently:
  - **Clause mapping**: Map supplier-specific clause values to standardized clause keys (only for documents in THIS group)
  - **Precedence analysis**: Determine which document governs when clauses differ (only within THIS group)
  - **Risk assessment**: Evaluate conflicts by category (only within THIS group)
  - **Business implications**: Provide recommendations specific to THIS contract family
- **Step 3 - Isolation**: Ensure NO cross-group clause comparisons or conflicts
- **Focus on existing data**: Only analyze clauses that are actually present in the contract data

**CRITICAL**:
1. Return ONLY valid JSON - no explanatory text before or after
2. Use the exact JSON structure shown in the example above
3. Each category MUST include a "category_risk_assessment" object as the first key
4. Keep clause analysis focused on clauses that actually exist in the provided contract data
5. Use the exact clause names from mapping.json as keys within each category
6. If no conflicts exist, still provide the structure but mark everything as "SAME" or "UNIQUE"
7. **MOST IMPORTANT**: Each contract group's "comprehensive_clause_analysis" must ONLY analyze documents within that specific group
8. **NO CROSS-GROUP ANALYSIS**: Never compare clauses between different contract groups - they are separate contract families

Here is the detailed contract data for analysis:

${this.formatContractsDataForPrompt(contractsData)}

Analyze these contracts and return the comprehensive bundle interconnection analysis in the specified JSON format.`;

      // Call Gemini API
      const response = await this.googleAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
          temperature: 0.1,
          maxOutputTokens: 16384,
        },
        contents: [
          {
            role: "user",
            parts: [{ text: promptText }],
          },
        ],
      });
      const responseText = response.text || '';

      logger.info("Received bundle analysis response from Gemini");

      if (!responseText) {
        throw new Error("Empty response from Gemini API");
      }

      // Parse the JSON response
      let analysisResult;
      try {
        // Try to extract JSON from the response
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("No JSON found in response");
        }
      } catch (parseError) {
        const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
        logger.error("Failed to parse Gemini response as JSON:", {
          parseError: errorMessage,
          responseLength: responseText.length,
          responsePreview: responseText.substring(0, 500),
          responseSuffix: responseText.substring(Math.max(0, responseText.length - 500))
        });

        // Create a fallback response structure
        logger.info("Creating fallback bundle analysis response");
        analysisResult = {
          contract_groups: [{
            group_id: "bundle_001",
            documents: contractsData.map((contract, index) => ({
              name: contract.documentName || `Document_${index + 1}`,
              agreementType: contract.agreementType || "OTHER",
              is_primary: index === 0,
              hierarchy_level: index + 1,
              confidence: 0.5
            })),
            hierarchy: {
              primary_document: contractsData[0]?.documentName || "Document_1",
              document_chain: contractsData.map(c => c.documentName || "Unknown")
            },
            comprehensive_clause_analysis: {
              general: {
                "Document analysis": {
                  analysis: "Unable to perform detailed analysis due to parsing error. Manual review recommended."
                }
              }
            },
            category_risk_assessment: {
              general: { risk_level: "MEDIUM", conflicts: 0, total_clauses: 1 }
            },
            business_implications: {
              critical_conflicts: ["Analysis parsing failed - manual review required"],
              missing_clauses: [],
              recommendations: ["Retry analysis or perform manual contract review"]
            }
          }],
          standalone_documents: [],
          overall_analysis: {
            total_bundles: 1,
            standalone_documents: 0,
            high_risk_bundles: 1,
            confidence_score: 0.3,
            analysis_date: new Date().toISOString(),
            folder_name: folderName
          }
        };
      }

      // Add metadata to overall_analysis if it exists, otherwise create it
      if (!analysisResult.overall_analysis) {
        analysisResult.overall_analysis = {};
      }
      analysisResult.overall_analysis.processing_date = new Date().toISOString();
      analysisResult.overall_analysis.model_used = "gemini-2.5-pro";
      analysisResult.overall_analysis.contract_count = contractsData.length;
      analysisResult.overall_analysis.folder_name = folderName;

      logger.info(`Successfully generated bundle interconnection analysis for folder: ${folderName}`);

      return analysisResult;
    } catch (error) {
      logger.error("Error generating bundle interconnection analysis:", { error, folderName });
      throw error;
    }
  }

  /**
   * Formats contracts data for the bundle analysis prompt
   * @param contractsData Array of contract data
   * @returns Formatted string for the prompt
   */
  private formatContractsDataForPrompt(contractsData: any[]): string {
    return contractsData.map((contract, index) => {
      const sections = [];

      sections.push(`=== CONTRACT ${index + 1}: ${contract.documentName} ===`);
      sections.push(`Contract ID: ${contract.contractId}`);
      sections.push(`Agreement Type: ${contract.agreementType}`);
      sections.push(`Provider: ${contract.provider || 'N/A'}`);
      sections.push(`Start Date: ${contract.startDate || 'N/A'}`);
      sections.push(`End Date: ${contract.endDate || 'N/A'}`);
      sections.push(`Value: ${contract.value || 'N/A'}`);

      // Add fixed fields
      if (contract.fixedFields) {
        sections.push('\n--- FIXED FIELDS ---');
        Object.entries(contract.fixedFields).forEach(([key, value]: [string, any]) => {
          if (value && typeof value === 'object' && value.value !== undefined) {
            sections.push(`${key}: ${value.value} (confidence: ${value.confidence || 'N/A'})`);
          }
        });
      }

      // Add dynamic fields
      if (contract.dynamicFields) {
        sections.push('\n--- DYNAMIC FIELDS ---');
        Object.entries(contract.dynamicFields).forEach(([category, fields]: [string, any]) => {
          if (fields && typeof fields === 'object') {
            sections.push(`\n[${category.toUpperCase()}]`);
            Object.entries(fields).forEach(([key, value]: [string, any]) => {
              if (value && typeof value === 'object' && value.value !== undefined) {
                sections.push(`  ${key}: ${value.value} (confidence: ${value.confidence || 'N/A'})`);
              }
            });
          }
        });
      }

      // Add special fields
      if (contract.specialFields) {
        sections.push('\n--- SPECIAL FIELDS ---');
        Object.entries(contract.specialFields).forEach(([key, value]: [string, any]) => {
          if (value && typeof value === 'object' && value.value !== undefined) {
            sections.push(`${key}: ${value.value} (confidence: ${value.confidence || 'N/A'})`);
          }
        });
      }

      // Add confidence information
      if (contract.overallConfidence) {
        sections.push(`\nOverall Extraction Confidence: ${contract.overallConfidence}%`);
      }

      sections.push('\n' + '='.repeat(80) + '\n');

      return sections.join('\n');
    }).join('\n');
  }
}
