/**
 * ContractCalculationService
 * Service for calculating derived contract fields based on extracted data
 */

import {
  FieldValue,
  FixedFields,
  DynamicFields,
} from "../../domain/contracts/ContractExtraction";
import { logger } from "../../infrastructure/logging/logger";

export class ContractCalculationService {
  /**
   * Calculate safe auto renewal status
   * @param fixedFields - Fixed fields from extraction
   * @param dynamicFields - Dynamic fields from extraction
   * @returns FieldValue for safe auto renewal
   */
  calculateSafeAutoRenewal(
    fixedFields: FixedFields,
    dynamicFields: DynamicFields
  ): FieldValue {
    try {
      // Extract auto renewal information - prioritize fixed fields, fallback to dynamic fields
      const autoRenewal = this.getAutoRenewalStatus(fixedFields, dynamicFields);
      const renewalNotice = this.getRenewalNoticePeriod(
        fixedFields,
        dynamicFields
      );

      // Calculate safe auto renewal
      // Consider it "safe" if auto-renewal is disabled or has adequate notice period (30+ days)
      let isSafe = false;
      let confidence = 0.9; // High confidence in calculation logic

      if (autoRenewal === "No") {
        isSafe = true; // No auto renewal is always safe
      } else if (autoRenewal === "Yes") {
        if (renewalNotice !== null && renewalNotice >= 30) {
          isSafe = true; // Auto renewal with adequate notice is safe
        } else {
          isSafe = false; // Auto renewal with short/no notice is not safe
        }
      } else {
        // Unknown auto renewal status - default to not safe
        isSafe = false;
        confidence = 0.5; // Lower confidence when auto renewal status is unknown
      }

      return {
        value: isSafe ? "Yes" : "No",
        confidence: confidence,
      };
    } catch (error) {
      logger.error("Error calculating safe auto renewal:", error);
      return {
        value: "Unknown",
        confidence: 0.0,
      };
    }
  }

  /**
   * Calculate intervention opportunity level
   * @param fixedFields - Fixed fields from extraction
   * @param dynamicFields - Dynamic fields from extraction
   * @returns FieldValue for intervention opportunity (1-5 scale)
   */
  calculateInterventionOpportunity(
    fixedFields: FixedFields,
    dynamicFields: DynamicFields
  ): FieldValue {
    try {
      const autoRenewal = this.getAutoRenewalStatus(fixedFields, dynamicFields);
      const renewalNotice = this.getRenewalNoticePeriod(
        fixedFields,
        dynamicFields
      );

      let interventionLevel = 3; // Default medium
      let confidence = 0.9; // High confidence in calculation logic

      if (autoRenewal === "No") {
        interventionLevel = 5; // High - no auto renewal means full control
      } else if (autoRenewal === "Yes") {
        if (renewalNotice === null) {
          interventionLevel = 1; // Low - auto renewal with no notice
        } else {
          if (renewalNotice >= 60) {
            interventionLevel = 4; // High - good notice period
          } else if (renewalNotice >= 30) {
            interventionLevel = 3; // Medium - adequate notice
          } else {
            interventionLevel = 2; // Low-medium - short notice
          }
        }
      } else {
        // Unknown auto renewal status
        interventionLevel = 3; // Default to medium
        confidence = 0.5; // Lower confidence when auto renewal status is unknown
      }

      return {
        value: interventionLevel.toString(),
        confidence: confidence,
      };
    } catch (error) {
      logger.error("Error calculating intervention opportunity:", error);
      return {
        value: "3",
        confidence: 0.0,
      };
    }
  }

  /**
   * Extract auto renewal status from fixed fields first, then dynamic fields
   * @param fixedFields - Fixed fields from extraction
   * @param dynamicFields - Dynamic fields from extraction
   * @returns "Yes", "No", or null if unknown
   */
  private getAutoRenewalStatus(
    fixedFields: FixedFields,
    dynamicFields: DynamicFields
  ): string | null {
    // First check fixed fields (prioritized)
    if (fixedFields.auto_renewal && fixedFields.auto_renewal.value) {
      const value = fixedFields.auto_renewal.value.toLowerCase().trim();

      // Fixed fields should only contain "Yes" or "No"
      if (value === "yes") {
        return "Yes";
      }
      if (value === "no") {
        return "No";
      }

      // Legacy support for other formats (but normalize to Yes/No)
      if (
        value.includes("yes") ||
        value.includes("automatic") ||
        value.includes("auto") ||
        value.includes("true") ||
        value === "1"
      ) {
        return "Yes";
      }

      // Default to "No" for any unclear values
      return "No";
    }

    // Fallback to dynamic fields if fixed field is not available or unclear
    const autoRenewalFields = [
      "auto_renewal",
      "automatic_renewal",
      "auto_renew",
      "renewal_automatic",
      "is_auto_renew",
      "auto_renewal_clause",
    ];

    for (const fieldName of autoRenewalFields) {
      const field = dynamicFields[fieldName];
      if (field && field.value) {
        const value = field.value.toLowerCase().trim();

        // Check for positive indicators
        if (
          value.includes("yes") ||
          value.includes("automatic") ||
          value.includes("auto") ||
          value.includes("true") ||
          value === "1"
        ) {
          return "Yes";
        }

        // Check for negative indicators
        if (
          value.includes("no") ||
          value.includes("false") ||
          value.includes("manual") ||
          value.includes("none") ||
          value === "0"
        ) {
          return "No";
        }
      }
    }

    return null; // Unknown
  }

  /**
   * Extract renewal notice period from fixed fields first, then dynamic fields
   * @param fixedFields - Fixed fields from extraction
   * @param dynamicFields - Dynamic fields from extraction
   * @returns Number of days or null if not found
   */
  private getRenewalNoticePeriod(
    fixedFields: FixedFields,
    dynamicFields: DynamicFields
  ): number | null {
    // First check fixed fields (prioritized)
    if (
      fixedFields.renewal_notice_period &&
      fixedFields.renewal_notice_period.value
    ) {
      const value = fixedFields.renewal_notice_period.value
        .toLowerCase()
        .trim();

      // Extract number of days from various formats
      const days = this.extractDaysFromText(value);
      if (days !== null) {
        return days;
      }
    }

    // Fallback to dynamic fields if fixed field is not available
    const noticePeriodFields = [
      "notice_period",
      "renewal_notice",
      "notice_period_days",
      "termination_notice",
      "cancellation_notice",
      "notice_required",
      "renewal_notice_period",
    ];

    for (const fieldName of noticePeriodFields) {
      const field = dynamicFields[fieldName];
      if (field && field.value) {
        const value = field.value.toLowerCase().trim();

        // Extract number of days from various formats
        const days = this.extractDaysFromText(value);
        if (days !== null) {
          return days;
        }
      }
    }

    return null; // Not found
  }

  /**
   * Extract number of days from text
   * @param text - Text containing time period
   * @returns Number of days or null if not found
   */
  private extractDaysFromText(text: string): number | null {
    // Remove common words and normalize
    const normalized = text
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ")
      .trim();

    // Look for patterns like "30 days", "60 day", "3 months", "1 year", etc.
    const patterns = [
      // Days
      /(\d+)\s*days?/i,
      /(\d+)\s*d\b/i,

      // Weeks (convert to days)
      /(\d+)\s*weeks?/i,
      /(\d+)\s*w\b/i,

      // Months (convert to days, assuming 30 days per month)
      /(\d+)\s*months?/i,
      /(\d+)\s*m\b/i,

      // Years (convert to days)
      /(\d+)\s*years?/i,
      /(\d+)\s*y\b/i,
    ];

    for (const pattern of patterns) {
      const match = normalized.match(pattern);
      if (match) {
        const number = parseInt(match[1], 10);
        if (!isNaN(number)) {
          // Convert to days based on unit
          if (pattern.source.includes("week")) {
            return number * 7;
          } else if (pattern.source.includes("month")) {
            return number * 30;
          } else if (pattern.source.includes("year")) {
            return number * 365;
          } else {
            return number; // Already in days
          }
        }
      }
    }

    // Look for just numbers (assume days)
    const numberMatch = normalized.match(/\b(\d+)\b/);
    if (numberMatch) {
      const number = parseInt(numberMatch[1], 10);
      if (!isNaN(number) && number > 0 && number <= 365) {
        // Only accept reasonable day values
        return number;
      }
    }

    return null;
  }

  /**
   * Calculate all derived fields and add them to fixed fields
   * @param fixedFields - Original fixed fields
   * @param dynamicFields - Dynamic fields from extraction
   * @returns Updated fixed fields with calculated values
   */
  calculateAndAddDerivedFields(
    fixedFields: FixedFields,
    dynamicFields: DynamicFields
  ): FixedFields {
    const updatedFields = { ...fixedFields };

    // Calculate safe auto renewal
    updatedFields.safe_auto_renewal = this.calculateSafeAutoRenewal(
      fixedFields,
      dynamicFields
    );

    // Calculate intervention opportunity
    updatedFields.intervention_opportunity =
      this.calculateInterventionOpportunity(fixedFields, dynamicFields);

    logger.info("Calculated derived fields:", {
      safeAutoRenewal: updatedFields.safe_auto_renewal?.value,
      interventionOpportunity: updatedFields.intervention_opportunity?.value,
    });

    return updatedFields;
  }
}
