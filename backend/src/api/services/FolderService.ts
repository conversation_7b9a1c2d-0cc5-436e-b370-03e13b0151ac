/**
 * Folder Service
 * Implements business logic for folder management
 */

import { v4 as uuidv4 } from "uuid";
import { Folder } from "../../domain/folders/Folder";
import {
  IFolderRepository,
  FolderSearchParams,
  FolderWithContractCount,
} from "../../domain/folders/interfaces/IFolderRepository";
import { logger } from "../../infrastructure/logging/logger";

export interface CreateFolderData {
  name: string;
  tenantId: string;
  createdById?: string;
}

export interface UpdateFolderData {
  name?: string;
}

export interface FolderListResult {
  folders: FolderWithContractCount[];
  total: number;
  page: number;
  limit: number;
}

export interface MoveContractsToFolderData {
  contractIds: string[];
  folderId: string | null;
  tenantId: string;
}

export class FolderService {
  constructor(private folderRepository: IFolderRepository) {}

  /**
   * Creates a new folder
   * @param folderData Folder creation data
   * @returns Created folder
   */
  async createFolder(folderData: CreateFolderData): Promise<Folder> {
    try {
      // Validate folder name
      Folder.validateName(folderData.name);

      const trimmedName = folderData.name.trim();

      logger.info("Creating folder:", {
        name: trimmedName,
        tenantId: folderData.tenantId,
        createdById: folderData.createdById,
      });

      // Check if folder name already exists for this tenant
      const nameExists = await this.folderRepository.nameExists(
        trimmedName,
        folderData.tenantId
      );

      if (nameExists) {
        logger.warn(
          `Folder with name "${trimmedName}" already exists for tenant ${folderData.tenantId}`
        );
        throw new Error(`Folder with name "${trimmedName}" already exists`);
      }

      // Create folder entity
      const folder = new Folder({
        id: uuidv4(),
        name: trimmedName,
        createdAt: new Date(),
        updatedAt: new Date(),
        tenantId: folderData.tenantId,
        createdById: folderData.createdById,
      });

      // Save to repository
      const createdFolder = await this.folderRepository.create(folder);

      logger.info("Folder created successfully", {
        folderId: createdFolder.id,
        name: createdFolder.name,
        tenantId: createdFolder.tenantId,
      });

      return createdFolder;
    } catch (error) {
      logger.error("Error creating folder:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        folderData: {
          name: folderData.name,
          tenantId: folderData.tenantId,
          createdById: folderData.createdById,
        },
      });

      // Re-throw with more context if it's a generic error
      if (error instanceof Error) {
        if (
          error.message.includes("duplicate") ||
          error.message.includes("unique")
        ) {
          throw new Error(
            `Folder with name "${folderData.name.trim()}" already exists`
          );
        }
      }

      throw error;
    }
  }

  /**
   * Gets a folder by ID
   * @param id Folder ID
   * @param tenantId Tenant ID for validation
   * @returns Folder or null if not found
   */
  async getFolder(id: string, tenantId: string): Promise<Folder | null> {
    try {
      const folder = await this.folderRepository.findById(id);

      // Validate tenant ownership
      if (folder && folder.tenantId !== tenantId) {
        return null; // Return null if folder doesn't belong to tenant
      }

      return folder;
    } catch (error) {
      logger.error("Error getting folder:", { error, id, tenantId });
      throw new Error(`Failed to get folder: ${(error as Error).message}`);
    }
  }

  /**
   * Gets folders with contract counts
   * @param params Search parameters
   * @returns Folder list result
   */
  async getFolders(params: FolderSearchParams): Promise<FolderListResult> {
    try {
      const page = params.page || 0;
      const limit = params.limit || 50;

      // Get folders with contract counts
      const folders = await this.folderRepository.findManyWithContractCount({
        ...params,
        page,
        limit,
      });

      // Get total count
      const total = await this.folderRepository.count(params);

      return {
        folders,
        total,
        page,
        limit,
      };
    } catch (error) {
      logger.error("Error getting folders:", { error, params });
      throw new Error(`Failed to get folders: ${(error as Error).message}`);
    }
  }

  /**
   * Gets folders for autocomplete (lightweight)
   * @param tenantId Tenant ID
   * @param search Optional search term
   * @returns Array of folders
   */
  async getFoldersForAutocomplete(
    tenantId: string,
    search?: string
  ): Promise<Folder[]> {
    try {
      const folders = await this.folderRepository.findMany({
        tenantId,
        name: search,
        limit: 20, // Limit for autocomplete
      });

      return folders;
    } catch (error) {
      logger.error("Error getting folders for autocomplete:", {
        error,
        tenantId,
        search,
      });
      throw new Error(
        `Failed to get folders for autocomplete: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates a folder
   * @param id Folder ID
   * @param folderData Update data
   * @param tenantId Tenant ID for validation
   * @returns Updated folder or null if not found
   */
  async updateFolder(
    id: string,
    folderData: UpdateFolderData,
    tenantId: string
  ): Promise<Folder | null> {
    try {
      // Get existing folder to validate tenant ownership
      const existingFolder = await this.getFolder(id, tenantId);
      if (!existingFolder) {
        return null;
      }

      // Validate name if provided
      if (folderData.name !== undefined) {
        Folder.validateName(folderData.name);

        // Check if new name already exists for this tenant (excluding current folder)
        const nameExists = await this.folderRepository.nameExists(
          folderData.name,
          tenantId,
          id
        );

        if (nameExists) {
          throw new Error(
            `Folder with name "${folderData.name}" already exists`
          );
        }
      }

      // Update folder
      const updatedFolder = await this.folderRepository.update(id, {
        name: folderData.name?.trim(),
      } as Partial<Folder>);

      if (updatedFolder) {
        logger.info("Folder updated successfully", {
          folderId: updatedFolder.id,
          name: updatedFolder.name,
          tenantId: updatedFolder.tenantId,
        });
      }

      return updatedFolder;
    } catch (error) {
      logger.error("Error updating folder:", {
        error,
        id,
        folderData,
        tenantId,
      });
      throw error;
    }
  }

  /**
   * Deletes a folder
   * @param id Folder ID
   * @param tenantId Tenant ID for validation
   * @returns True if deleted, false if not found
   */
  async deleteFolder(id: string, tenantId: string): Promise<boolean> {
    try {
      // Get existing folder to validate tenant ownership
      const existingFolder = await this.getFolder(id, tenantId);
      if (!existingFolder) {
        return false;
      }

      // Delete folder (contracts will be moved to null folder_id due to onDelete: SetNull)
      const deleted = await this.folderRepository.delete(id);

      if (deleted) {
        logger.info("Folder deleted successfully", {
          folderId: id,
          tenantId,
        });
      }

      return deleted;
    } catch (error) {
      logger.error("Error deleting folder:", { error, id, tenantId });
      throw new Error(`Failed to delete folder: ${(error as Error).message}`);
    }
  }

  /**
   * Moves contracts to a folder
   * @param data Move contracts data
   * @returns Number of contracts moved
   */
  async moveContractsToFolder(
    data: MoveContractsToFolderData
  ): Promise<number> {
    try {
      // Validate folder exists if folderId is provided
      if (data.folderId) {
        const folder = await this.getFolder(data.folderId, data.tenantId);
        if (!folder) {
          throw new Error("Folder not found");
        }
      }

      // Move contracts
      const movedCount = await this.folderRepository.moveContractsToFolder(
        data.contractIds,
        data.folderId,
        data.tenantId
      );

      logger.info("Contracts moved to folder", {
        contractIds: data.contractIds,
        folderId: data.folderId,
        movedCount,
        tenantId: data.tenantId,
      });

      return movedCount;
    } catch (error) {
      logger.error("Error moving contracts to folder:", { error, data });
      throw error;
    }
  }
}
