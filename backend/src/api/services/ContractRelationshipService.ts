/**
 * Contract Relationship Service
 * Analyzes relationships between contracts based on multiple criteria
 */

import {
  ContractExtraction,
  FixedFields,
} from "../../domain/contracts/ContractExtraction";
import {
  ContractHierarchyService,
  ContractWithHierarchy,
} from "./ContractHierarchyService";
import { logger } from "../../infrastructure/logging/logger";

export interface ContractRelationshipCriteria {
  contractIdMatching: boolean;
  startDateProximity: boolean;
  executionDateCorrelation: boolean;
  signatureDateAlignment: boolean;
  signatureNameMatching: boolean;
  referencedDocuments: boolean;
  agreementTypeHierarchy: boolean;
}

export interface ContractRelationship {
  fromContractId: string;
  toContractId: string;
  relationshipType:
    | "HIERARCHY"
    | "REFERENCE"
    | "TEMPORAL"
    | "SIGNATURE"
    | "CONTRACT_ID";
  strength: number; // 0-1 confidence score
  criteria: string[]; // Which criteria matched
  details?: string; // Additional details about the relationship
}

export interface ContractNode {
  id: string;
  title: string;
  agreementType: string;
  provider?: string;
  startDate?: Date;
  endDate?: Date;
  executionDate?: Date;
  signatureDate?: Date;
  signatoryName?: string;
  contractNumber?: string;
  referencedDocuments?: string[];
  hierarchyLevel?: number;
  value?: string;
  status?: string;
}

export interface ContractRelationshipAnalysis {
  nodes: ContractNode[];
  relationships: ContractRelationship[];
  clusters: ContractCluster[];
}

export interface ContractCluster {
  id: string;
  name: string;
  contractIds: string[];
  clusterType: "HIERARCHY" | "TEMPORAL" | "REFERENCE" | "MIXED";
  strength: number;
}

export class ContractRelationshipService {
  private hierarchyService: ContractHierarchyService;

  constructor() {
    this.hierarchyService = new ContractHierarchyService();
  }

  /**
   * Analyzes relationships between contracts for a specific supplier
   */
  async analyzeSupplierContractRelationships(
    extractions: ContractExtraction[],
    criteria: Partial<ContractRelationshipCriteria> = {}
  ): Promise<ContractRelationshipAnalysis> {
    try {
      // Set default criteria
      const activeCriteria: ContractRelationshipCriteria = {
        contractIdMatching: true,
        startDateProximity: true,
        executionDateCorrelation: true,
        signatureDateAlignment: true,
        signatureNameMatching: true,
        referencedDocuments: true,
        agreementTypeHierarchy: true,
        ...criteria,
      };

      // Convert extractions to nodes
      const nodes = this.extractionsToNodes(extractions);

      // Find relationships based on criteria
      const relationships = await this.findRelationships(nodes, activeCriteria);

      // Create clusters based on relationships
      const clusters = this.createClusters(nodes, relationships);

      return {
        nodes,
        relationships,
        clusters,
      };
    } catch (error) {
      logger.error("Error analyzing contract relationships:", error);
      throw new Error("Failed to analyze contract relationships");
    }
  }

  /**
   * Converts contract extractions to relationship nodes
   */
  private extractionsToNodes(
    extractions: ContractExtraction[]
  ): ContractNode[] {
    const nodes = extractions.map((extraction) => {
      const fixed = extraction.fixedFields;
      const dynamic = extraction.dynamicFields;

      return {
        id: extraction.contractId,
        title:
          this.getFieldValue(fixed.original_filename) ||
          this.getFieldValue(fixed.contract_id) ||
          `Contract ${extraction.contractId.slice(-8)}`,
        agreementType: this.getFieldValue(fixed.agreement_type) || "UNKNOWN",
        provider:
          this.getFieldValue(fixed.provider) ||
          this.getFieldValue(fixed.client),
        startDate: this.parseDate(this.getFieldValue(fixed.start_date)),
        endDate: this.parseDate(this.getFieldValue(fixed.end_date)),
        executionDate: this.parseDate(
          this.extractFromDynamicFields(dynamic, [
            "execution_date",
            "executionDate",
            "signed_date",
            "signedDate",
          ])
        ),
        signatureDate: this.parseDate(
          this.extractFromDynamicFields(dynamic, [
            "signature_date",
            "signatureDate",
            "signed_date",
            "signedDate",
          ])
        ),
        signatoryName: this.extractFromDynamicFields(dynamic, [
          "signatory_name",
          "signatoryName",
          "signer_name",
          "signerName",
          "signed_by",
          "signedBy",
        ]),
        contractNumber: this.getFieldValue(fixed.contract_id),
        referencedDocuments: this.parseReferencedDocuments(fixed.relationships),
        hierarchyLevel:
          this.hierarchyService.getHierarchyLevel(
            this.getFieldValue(fixed.agreement_type) || "UNKNOWN"
          ) || 1,
        value: this.getFieldValue(fixed.total_amount),
        status: this.getFieldValue(fixed.contract_status),
      };
    });

    return nodes;
  }

  /**
   * Finds relationships between contract nodes based on criteria
   */
  private async findRelationships(
    nodes: ContractNode[],
    criteria: ContractRelationshipCriteria
  ): Promise<ContractRelationship[]> {
    const relationships: ContractRelationship[] = [];
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const nodeA = nodes[i];
        const nodeB = nodes[j];

        const relationship = await this.analyzeNodeRelationship(
          nodeA,
          nodeB,
          criteria
        );
        if (relationship) {
          relationships.push(relationship);
        }
      }
    }

    return relationships;
  }

  /**
   * Analyzes relationship between two contract nodes
   * Only creates relationships if they follow hierarchy AND match criteria
   */
  private async analyzeNodeRelationship(
    nodeA: ContractNode,
    nodeB: ContractNode,
    criteria: ContractRelationshipCriteria
  ): Promise<ContractRelationship | null> {
    const matchedCriteria: string[] = [];
    let totalStrength = 0;
    let relationshipType: ContractRelationship["relationshipType"] =
      "REFERENCE";

    // First check: Must follow hierarchy (MSA → Schedule, etc.)
    const hierarchyCheck = await this.checkHierarchyRelationship(nodeA, nodeB);
    if (!hierarchyCheck.isRelated) {
      return null; // No relationship if not hierarchically related
    }

    // If hierarchically related, check other criteria
    matchedCriteria.push("HIERARCHY_MATCH");
    totalStrength += hierarchyCheck.strength;
    relationshipType = "HIERARCHY";

    // 1. Contract ID matching (highest priority for same contract family)
    if (
      criteria.contractIdMatching &&
      this.checkContractIdMatching(nodeA, nodeB)
    ) {
      matchedCriteria.push("CONTRACT_ID_MATCH");
      totalStrength += 0.9;
      relationshipType = "CONTRACT_ID";
    }

    // 2. Agreement type hierarchy
    if (criteria.agreementTypeHierarchy) {
      const hierarchyMatch = await this.checkHierarchyRelationship(
        nodeA,
        nodeB
      );
      if (hierarchyMatch.isRelated) {
        matchedCriteria.push("HIERARCHY");
        totalStrength += hierarchyMatch.strength;
        relationshipType = "HIERARCHY";
      }
    }

    // 3. Referenced documents
    if (
      criteria.referencedDocuments &&
      this.checkReferencedDocuments(nodeA, nodeB)
    ) {
      matchedCriteria.push("REFERENCED_DOCUMENTS");
      totalStrength += 0.7;
      if (relationshipType === "HIERARCHY") relationshipType = "REFERENCE";
    }

    // 4. Start date proximity
    if (
      criteria.startDateProximity &&
      this.checkDateProximity(nodeA.startDate, nodeB.startDate, 30)
    ) {
      matchedCriteria.push("START_DATE_PROXIMITY");
      totalStrength += 0.4;
      if (relationshipType === "REFERENCE") relationshipType = "TEMPORAL";
    }

    // 5. Execution date correlation
    if (
      criteria.executionDateCorrelation &&
      this.checkDateProximity(nodeA.executionDate, nodeB.executionDate, 7)
    ) {
      matchedCriteria.push("EXECUTION_DATE_CORRELATION");
      totalStrength += 0.5;
      if (relationshipType === "REFERENCE") relationshipType = "TEMPORAL";
    }

    // 6. Signature date alignment
    if (
      criteria.signatureDateAlignment &&
      this.checkDateProximity(nodeA.signatureDate, nodeB.signatureDate, 7)
    ) {
      matchedCriteria.push("SIGNATURE_DATE_ALIGNMENT");
      totalStrength += 0.5;
      if (relationshipType === "REFERENCE") relationshipType = "SIGNATURE";
    }

    // 7. Signature name matching
    if (
      criteria.signatureNameMatching &&
      this.checkSignatureNameMatching(nodeA, nodeB)
    ) {
      matchedCriteria.push("SIGNATURE_NAME_MATCH");
      totalStrength += 0.3;
      if (relationshipType === "REFERENCE") relationshipType = "SIGNATURE";
    }

    // Only return relationship if we have matches and sufficient strength
    if (matchedCriteria.length > 0 && totalStrength > 0.2) {
      const normalizedStrength = Math.min(totalStrength, 1.0);

      // For hierarchy relationships, ensure parent -> child direction
      let fromId = nodeA.id;
      let toId = nodeB.id;

      if (relationshipType === "HIERARCHY") {
        const canABeParentOfB = this.hierarchyService.canBeParent(
          nodeA.agreementType,
          nodeB.agreementType
        );
        const canBBeParentOfA = this.hierarchyService.canBeParent(
          nodeB.agreementType,
          nodeA.agreementType
        );

        // If B can be parent of A, reverse the direction (parent -> child)
        if (canBBeParentOfA && !canABeParentOfB) {
          fromId = nodeB.id;
          toId = nodeA.id;
        }
      }

      return {
        fromContractId: fromId,
        toContractId: toId,
        relationshipType,
        strength: normalizedStrength,
        criteria: matchedCriteria,
        details: this.generateRelationshipDetails(
          matchedCriteria,
          nodeA,
          nodeB
        ),
      };
    }

    return null;
  }

  /**
   * Helper methods for relationship analysis
   */
  private getFieldValue(field: any): string | undefined {
    if (!field) return undefined;
    return typeof field === "object" ? field.value : field;
  }

  /**
   * Extracts value from dynamic fields by trying multiple field name variations
   */
  private extractFromDynamicFields(
    dynamicFields: any,
    fieldNames: string[]
  ): string | undefined {
    if (!dynamicFields) return undefined;

    // Try each field name variation
    for (const fieldName of fieldNames) {
      // Check in each category of dynamic fields
      for (const category of Object.values(dynamicFields)) {
        if (category && typeof category === "object") {
          const field = (category as any)[fieldName];
          if (field?.value) {
            return field.value;
          }
        }
      }

      // Also check at root level in case it's not categorized
      const rootField = (dynamicFields as any)[fieldName];
      if (rootField?.value) {
        return rootField.value;
      }
    }

    return undefined;
  }

  private parseDate(dateStr: string | undefined): Date | undefined {
    if (!dateStr) return undefined;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? undefined : date;
  }

  private parseReferencedDocuments(relationships: any): string[] {
    if (!relationships) return [];
    if (Array.isArray(relationships)) return relationships;
    if (typeof relationships === "object" && relationships.value) {
      return Array.isArray(relationships.value) ? relationships.value : [];
    }
    return [];
  }

  /**
   * Relationship checking methods
   */
  private checkContractIdMatching(
    nodeA: ContractNode,
    nodeB: ContractNode
  ): boolean {
    if (!nodeA.contractNumber || !nodeB.contractNumber) return false;

    // Normalize contract numbers for comparison
    const normalizeContractNumber = (num: string) =>
      num.replace(/[-\s_]/g, "").toLowerCase();

    return (
      normalizeContractNumber(nodeA.contractNumber) ===
      normalizeContractNumber(nodeB.contractNumber)
    );
  }

  private async checkHierarchyRelationship(
    nodeA: ContractNode,
    nodeB: ContractNode
  ): Promise<{ isRelated: boolean; strength: number }> {
    const canBeParent = this.hierarchyService.canBeParent(
      nodeA.agreementType,
      nodeB.agreementType
    );
    const canBeChild = this.hierarchyService.canBeParent(
      nodeB.agreementType,
      nodeA.agreementType
    );

    if (canBeParent || canBeChild) {
      // Higher strength for closer hierarchy levels
      const levelA =
        this.hierarchyService.getHierarchyLevel(nodeA.agreementType) || 999;
      const levelB =
        this.hierarchyService.getHierarchyLevel(nodeB.agreementType) || 999;
      const levelDiff = Math.abs(levelA - levelB);
      const strength = Math.max(0.3, 0.8 - levelDiff * 0.1);

      return { isRelated: true, strength };
    }
    return { isRelated: false, strength: 0 };
  }

  private checkReferencedDocuments(
    nodeA: ContractNode,
    nodeB: ContractNode
  ): boolean {
    const refsA = nodeA.referencedDocuments || [];
    const refsB = nodeB.referencedDocuments || [];

    // Check if either contract references the other
    const titleA = nodeA.title.toLowerCase();
    const titleB = nodeB.title.toLowerCase();
    const contractNumA = nodeA.contractNumber?.toLowerCase();
    const contractNumB = nodeB.contractNumber?.toLowerCase();

    return (
      refsA.some((ref) => {
        const refLower = ref.toLowerCase();
        return (
          refLower.includes(titleB) ||
          (contractNumB && refLower.includes(contractNumB))
        );
      }) ||
      refsB.some((ref) => {
        const refLower = ref.toLowerCase();
        return (
          refLower.includes(titleA) ||
          (contractNumA && refLower.includes(contractNumA))
        );
      })
    );
  }

  private checkDateProximity(
    dateA: Date | undefined,
    dateB: Date | undefined,
    maxDaysDiff: number
  ): boolean {
    if (!dateA || !dateB) return false;

    const diffMs = Math.abs(dateA.getTime() - dateB.getTime());
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    return diffDays <= maxDaysDiff;
  }

  private checkSignatureNameMatching(
    nodeA: ContractNode,
    nodeB: ContractNode
  ): boolean {
    if (!nodeA.signatoryName || !nodeB.signatoryName) return false;

    const normalizeSignature = (name: string) =>
      name
        .toLowerCase()
        .replace(/[^a-z\s]/g, "")
        .trim();

    return (
      normalizeSignature(nodeA.signatoryName) ===
      normalizeSignature(nodeB.signatoryName)
    );
  }

  private generateRelationshipDetails(
    criteria: string[],
    nodeA: ContractNode,
    nodeB: ContractNode
  ): string {
    const details: string[] = [];

    if (criteria.includes("CONTRACT_ID_MATCH")) {
      details.push(`Same contract ID: ${nodeA.contractNumber}`);
    }
    if (criteria.includes("HIERARCHY")) {
      details.push(
        `Hierarchy: ${nodeA.agreementType} ↔ ${nodeB.agreementType}`
      );
    }
    if (criteria.includes("REFERENCED_DOCUMENTS")) {
      details.push("Cross-referenced in documents");
    }
    if (criteria.includes("START_DATE_PROXIMITY")) {
      details.push("Similar start dates");
    }
    if (criteria.includes("EXECUTION_DATE_CORRELATION")) {
      details.push("Similar execution dates");
    }
    if (criteria.includes("SIGNATURE_DATE_ALIGNMENT")) {
      details.push("Similar signature dates");
    }
    if (criteria.includes("SIGNATURE_NAME_MATCH")) {
      details.push(`Same signatory: ${nodeA.signatoryName}`);
    }

    return details.join("; ");
  }

  /**
   * Creates clusters of related contracts
   */
  private createClusters(
    nodes: ContractNode[],
    relationships: ContractRelationship[]
  ): ContractCluster[] {
    const clusters: ContractCluster[] = [];
    const processedNodes = new Set<string>();

    // Create clusters based on connected components
    for (const node of nodes) {
      if (processedNodes.has(node.id)) continue;

      const cluster = this.findConnectedComponent(
        node.id,
        nodes,
        relationships
      );
      if (cluster.contractIds.length > 1) {
        cluster.contractIds.forEach((id) => processedNodes.add(id));
        clusters.push(cluster);
      }
    }

    return clusters;
  }

  private findConnectedComponent(
    startNodeId: string,
    nodes: ContractNode[],
    relationships: ContractRelationship[]
  ): ContractCluster {
    const visited = new Set<string>();
    const queue = [startNodeId];
    const clusterNodes: string[] = [];
    const clusterRelationships: ContractRelationship[] = [];

    while (queue.length > 0) {
      const currentId = queue.shift()!;
      if (visited.has(currentId)) continue;

      visited.add(currentId);
      clusterNodes.push(currentId);

      // Find connected nodes
      const connectedRels = relationships.filter(
        (rel) =>
          rel.fromContractId === currentId || rel.toContractId === currentId
      );

      clusterRelationships.push(...connectedRels);

      for (const rel of connectedRels) {
        const nextId =
          rel.fromContractId === currentId
            ? rel.toContractId
            : rel.fromContractId;
        if (!visited.has(nextId)) {
          queue.push(nextId);
        }
      }
    }

    // Determine cluster type and strength
    const relationshipTypes = clusterRelationships.map(
      (r) => r.relationshipType
    );
    const avgStrength =
      clusterRelationships.length > 0
        ? clusterRelationships.reduce((sum, r) => sum + r.strength, 0) /
          clusterRelationships.length
        : 0;

    let clusterType: ContractCluster["clusterType"] = "MIXED";
    if (relationshipTypes.every((t) => t === "HIERARCHY"))
      clusterType = "HIERARCHY";
    else if (relationshipTypes.every((t) => t === "TEMPORAL"))
      clusterType = "TEMPORAL";
    else if (relationshipTypes.every((t) => t === "REFERENCE"))
      clusterType = "REFERENCE";

    // Generate cluster name
    const clusterName = this.generateClusterName(
      clusterNodes,
      nodes,
      clusterType
    );

    return {
      id: `cluster_${clusterNodes.join("_")}`,
      name: clusterName,
      contractIds: clusterNodes,
      clusterType,
      strength: avgStrength,
    };
  }

  private generateClusterName(
    contractIds: string[],
    nodes: ContractNode[],
    clusterType: ContractCluster["clusterType"]
  ): string {
    const clusterNodes = nodes.filter((n) => contractIds.includes(n.id));
    const agreementTypes = [
      ...new Set(clusterNodes.map((n) => n.agreementType)),
    ];

    if (clusterType === "HIERARCHY" && agreementTypes.length > 1) {
      const sortedTypes = agreementTypes.sort((a, b) => {
        const levelA = this.hierarchyService.getHierarchyLevel(a) || 999;
        const levelB = this.hierarchyService.getHierarchyLevel(b) || 999;
        return levelA - levelB;
      });
      return `${sortedTypes[0]} Hierarchy`;
    }

    return `${agreementTypes[0]} Group (${contractIds.length} contracts)`;
  }
}
