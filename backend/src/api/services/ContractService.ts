/**
 * Contract Service
 * Implements business logic for contract management
 */

import {
  ContractStatus,
  SecurityClassification,
  DocumentFormat,
  ContractClassification,
  AgreementType,
} from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";
import { Contract } from "../../domain/contracts/Contract";
import { ContractVersion } from "../../domain/contracts/ContractVersion";
import { ContractMetadata } from "../../domain/contracts/ContractMetadata";
import {
  IContractRepository,
  ContractSearchParams,
} from "../../domain/contracts/interfaces/IContractRepository";
import { IContractExtractionRepository } from "../../domain/contracts/interfaces/IContractExtractionRepository";
import { EncryptionService } from "../../infrastructure/services/EncryptionService";
import {
  ContractHierarchyService,
  ContractWithHierarchy,
} from "./ContractHierarchyService";
import { v4 as uuidv4 } from "uuid";

/**
 * ContractService implements business logic for contract operations
 */
export class ContractService {
  private contractRepository: IContractRepository;
  private contractExtractionRepository: IContractExtractionRepository;
  private encryptionService: EncryptionService;
  private hierarchyService: ContractHierarchyService;

  constructor(
    contractRepository: IContractRepository,
    contractExtractionRepository: IContractExtractionRepository,
    encryptionService: EncryptionService
  ) {
    this.contractRepository = contractRepository;
    this.contractExtractionRepository = contractExtractionRepository;
    this.encryptionService = encryptionService;
    this.hierarchyService = new ContractHierarchyService();
  }

  /**
   * Creates a new contract
   * @param contractData Contract data for creation
   * @returns Created contract
   */
  async createContract(contractData: {
    title: string;
    description?: string;
    contractNumber?: string;
    classification?: ContractClassification;
    agreementType: AgreementType;
    status?: ContractStatus;
    startDate?: Date;
    endDate?: Date;
    renewalType?: string;
    renewalDate?: Date;
    isAutoRenew?: boolean;
    noticePeriodDays?: number;
    securityClassification?: SecurityClassification;
    isEncrypted?: boolean;
    counterparty?: string;
    value?: string;
    tenantId: string;
    createdById?: string;
  }): Promise<Contract> {
    try {
      // Set default values where needed
      const status = contractData.status || ContractStatus.DRAFT;
      const securityClassification =
        contractData.securityClassification ||
        SecurityClassification.CONFIDENTIAL;
      const isEncrypted =
        contractData.isEncrypted !== undefined
          ? contractData.isEncrypted
          : true;

      // Create a contract entity
      const contract = new Contract({
        id: uuidv4(),
        title: contractData.title,
        description: contractData.description,
        contractNumber: contractData.contractNumber,
        classification: contractData.classification,
        agreementType: contractData.agreementType,
        status,
        startDate: contractData.startDate,
        endDate: contractData.endDate,
        renewalType: contractData.renewalType,
        renewalDate: contractData.renewalDate,
        isAutoRenew: contractData.isAutoRenew,
        noticePeriodDays: contractData.noticePeriodDays,
        securityClassification,
        isEncrypted,
        counterparty: contractData.counterparty,
        value: contractData.value,
        tenantId: contractData.tenantId,
        createdById: contractData.createdById,
      });

      // Save to repository
      return await this.contractRepository.create(contract);
    } catch (error) {
      console.error("Error creating contract:", error);
      throw new Error(`Failed to create contract: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an existing contract
   * @param id Contract ID
   * @param contractData Contract data for update
   * @param tenantId Tenant ID for validation
   * @returns Updated contract or null if not found or not authorized
   */
  async updateContract(
    id: string,
    contractData: {
      title?: string;
      description?: string;
      contractNumber?: string;
      classification?: ContractClassification;
      agreementType?: AgreementType;
      status?: ContractStatus;
      startDate?: Date;
      endDate?: Date;
      renewalType?: string;
      renewalDate?: Date;
      isAutoRenew?: boolean;
      noticePeriodDays?: number;
      securityClassification?: SecurityClassification;
      isEncrypted?: boolean;
      counterparty?: string;
      value?: string;
    },
    tenantId: string
  ): Promise<Contract | null> {
    try {
      // Retrieve existing contract
      const existingContract = await this.contractRepository.findById(id);

      if (!existingContract) {
        return null;
      }

      // Validate tenant ownership
      if (existingContract.tenantId !== tenantId) {
        return null; // Don't update if contract doesn't belong to tenant
      }

      // Create updated contract entity
      const updatedContract = new Contract({
        id: existingContract.id,
        title: contractData.title || existingContract.title,
        description:
          contractData.description !== undefined
            ? contractData.description
            : existingContract.description,
        contractNumber:
          contractData.contractNumber !== undefined
            ? contractData.contractNumber
            : existingContract.contractNumber,

        classification:
          contractData.classification !== undefined
            ? contractData.classification
            : existingContract.classification,
        agreementType:
          contractData.agreementType !== undefined
            ? contractData.agreementType
            : existingContract.agreementType,
        status: contractData.status || existingContract.status,
        startDate:
          contractData.startDate !== undefined
            ? contractData.startDate
            : existingContract.startDate,
        endDate:
          contractData.endDate !== undefined
            ? contractData.endDate
            : existingContract.endDate,
        renewalType:
          contractData.renewalType !== undefined
            ? contractData.renewalType
            : existingContract.renewalType,
        renewalDate:
          contractData.renewalDate !== undefined
            ? contractData.renewalDate
            : existingContract.renewalDate,
        isAutoRenew:
          contractData.isAutoRenew !== undefined
            ? contractData.isAutoRenew
            : existingContract.isAutoRenew,
        noticePeriodDays:
          contractData.noticePeriodDays !== undefined
            ? contractData.noticePeriodDays
            : existingContract.noticePeriodDays,
        securityClassification:
          contractData.securityClassification ||
          existingContract.securityClassification,
        isEncrypted:
          contractData.isEncrypted !== undefined
            ? contractData.isEncrypted
            : existingContract.isEncrypted,
        counterparty:
          contractData.counterparty !== undefined
            ? contractData.counterparty
            : existingContract.counterparty,
        value:
          contractData.value !== undefined
            ? contractData.value
            : existingContract.value,
        currentVersionId: existingContract.currentVersionId,
        tenantId: existingContract.tenantId,
        createdAt: existingContract.createdAt,
        updatedAt: new Date(),
      });

      // Save to repository
      return await this.contractRepository.update(updatedContract);
    } catch (error) {
      console.error("Error updating contract:", error);
      throw new Error(`Failed to update contract: ${(error as Error).message}`);
    }
  }

  /**
   * Gets a contract by ID
   * @param id Contract ID
   * @param tenantId Tenant ID for validation
   * @returns Contract or null if not found
   */
  async getContract(id: string, tenantId: string): Promise<Contract | null> {
    try {
      const contract = await this.contractRepository.findById(id);

      // Validate tenant ownership
      if (contract && contract.tenantId !== tenantId) {
        return null; // Return null if contract doesn't belong to tenant
      }

      return contract;
    } catch (error) {
      console.error("Error getting contract:", error);
      throw new Error(`Failed to get contract: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes a contract by ID
   * @param id Contract ID
   * @param tenantId Tenant ID for validation
   * @returns True if deleted, false if not found
   */
  async deleteContract(id: string, tenantId: string): Promise<boolean> {
    try {
      // Validate contract belongs to tenant
      const contract = await this.contractRepository.findById(id);

      if (!contract) {
        return false;
      }

      if (contract.tenantId !== tenantId) {
        return false; // Don't delete if contract doesn't belong to tenant
      }

      return await this.contractRepository.delete(id);
    } catch (error) {
      console.error("Error deleting contract:", error);
      throw new Error(`Failed to delete contract: ${(error as Error).message}`);
    }
  }

  /**
   * Searches contracts based on criteria
   * @param params Search parameters
   * @returns Contracts matching the criteria and total count
   */
  async searchContracts(
    params: ContractSearchParams
  ): Promise<{ contracts: Contract[]; total: number }> {
    try {
      return await this.contractRepository.search(params);
    } catch (error) {
      console.error("Error searching contracts:", error);
      throw new Error(
        `Failed to search contracts: ${(error as Error).message}`
      );
    }
  }

  /**
   * Creates a new contract version (legacy method - use the other implementation)
   * @deprecated Use the other createContractVersion method with Buffer parameter
   * @param contractId Contract ID
   * @param versionData Version data
   * @returns Created version
   */
  async createContractVersionLegacy(
    contractId: string,
    versionData: {
      documentUri: string;
      documentName: string;
      documentFormat: DocumentFormat;
      documentSize: number;
      documentHash?: string;
      versionComment?: string;
      createdBy?: string;
    }
  ): Promise<ContractVersion> {
    try {
      // Get current contract
      const contract = await this.contractRepository.findById(contractId);

      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      // Determine new version number
      const newVersionNumber = contract.version + 1;

      // Calculate document hash if not provided
      let documentHash = versionData.documentHash;
      if (!documentHash) {
        // In a real implementation, we'd calculate the hash from the actual document
        // For demonstration, we'll use a simple hash method
        documentHash = this.encryptionService.createHash(
          versionData.documentName + Date.now()
        );
      }

      // Set up encryption if contract is encrypted
      let encryptionKeyId = null;
      if (contract.isEncrypted) {
        // In a real implementation, we would get or generate a key
        // For demonstration, we'll use a simple ID
        encryptionKeyId = "key-" + Date.now();
      }

      // Create version entity
      const contractVersion = new ContractVersion({
        id: uuidv4(),
        contractId,
        versionNumber: newVersionNumber,
        documentUri: versionData.documentUri,
        documentHash,
        documentFormat: versionData.documentFormat,
        documentSize: versionData.documentSize,
        documentName: versionData.documentName,
        encryptionKeyId,
        versionComment: versionData.versionComment,
        changelog: this.generateChangelog(newVersionNumber),
        createdBy: versionData.createdBy,
      });

      // Save to repository
      return await this.contractRepository.createVersion(contractVersion);
    } catch (error) {
      console.error("Error creating contract version:", error);
      throw new Error(
        `Failed to create contract version: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets version history for a contract
   * @param contractId Contract ID
   * @returns Array of versions
   */
  async getVersionHistory(contractId: string): Promise<ContractVersion[]> {
    try {
      return await this.contractRepository.getVersionHistory(contractId);
    } catch (error) {
      console.error("Error getting version history:", error);
      throw new Error(
        `Failed to get version history: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets a specific version of a contract
   * @param contractId Contract ID
   * @param versionNumber Version number
   * @returns Contract version or null if not found
   */
  async getVersion(
    contractId: string,
    versionNumber: number
  ): Promise<ContractVersion | null> {
    try {
      return await this.contractRepository.getVersion(
        contractId,
        versionNumber
      );
    } catch (error) {
      console.error("Error getting specific version:", error);
      throw new Error(`Failed to get version: ${(error as Error).message}`);
    }
  }

  /**
   * Gets the latest version of a contract
   * @param contractId Contract ID
   * @param tenantId Tenant ID for validation
   * @returns Latest contract version or null if not found
   */
  async getLatestVersion(
    contractId: string,
    tenantId: string
  ): Promise<ContractVersion | null> {
    try {
      // First validate that the contract belongs to the tenant
      const contract = await this.getContract(contractId, tenantId);
      if (!contract) {
        return null;
      }

      // Get the latest version
      // In a real implementation, you would use a repository method to get the latest version
      // For now, return null as a placeholder
      return null;
    } catch (error) {
      console.error("Error getting latest version:", error);
      throw new Error(
        `Failed to get latest version: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets document content from a document URI
   * @param documentUri Document URI
   * @returns Document content as Buffer or null if not found
   */
  async getDocumentContent(documentUri: string): Promise<Buffer | null> {
    try {
      // Check if it's a memory URI (for documents stored in the database)
      if (documentUri.startsWith("memory://")) {
        const documentId = documentUri.replace("memory://", "");
        // Find the version by ID and get its document content
        // This is a placeholder - in a real implementation, you would retrieve the document content
        return Buffer.from("Document content placeholder");
      }

      // For other URI types (e.g., external storage), implement appropriate retrieval logic
      console.error("Unsupported document URI type:", documentUri);
      return null;
    } catch (error) {
      console.error("Error getting document content:", error);
      throw new Error(
        `Failed to get document content: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets Agreement Document for download
   * @param contractId Contract ID
   * @param tenantId Tenant ID for security
   * @returns Document data with content and metadata
   */
  async getContractDocument(
    contractId: string,
    tenantId: string
  ): Promise<{
    content: Buffer;
    fileName: string;
    mimeType: string;
    size: number;
  } | null> {
    try {
      // Get contract to verify access
      const contract = await this.contractRepository.findById(contractId);
      if (!contract || contract.tenantId !== tenantId) {
        return null;
      }

      // Get the latest version
      const versions = await this.contractRepository.getVersionHistory(
        contractId
      );
      if (!versions || versions.length === 0) {
        return null;
      }

      // Get the latest version (highest version number)
      const latestVersion = versions.reduce(
        (latest: ContractVersion, current: ContractVersion) =>
          current.versionNumber > latest.versionNumber ? current : latest
      );

      // Check if document content exists
      if (!latestVersion.documentContent || latestVersion.documentContent.length === 0) {
        return null;
      }

      // Return document data
      return {
        content: latestVersion.documentContent,
        fileName: latestVersion.documentName,
        mimeType: latestVersion.mimeType || "application/octet-stream",
        size: latestVersion.documentSize,
      };
    } catch (error) {
      console.error("Error getting Agreement Document:", error);
      throw new Error(
        `Failed to get Agreement Document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets Agreement Document metadata without content
   * @param contractId Contract ID
   * @param tenantId Tenant ID for security
   * @returns Document metadata
   */
  async getContractDocumentMetadata(
    contractId: string,
    tenantId: string
  ): Promise<{
    fileName: string;
    mimeType: string;
    size: number;
    uploadDate: Date;
    versionNumber: number;
    hasDocument: boolean;
  } | null> {
    try {
      // Get contract to verify access
      const contract = await this.contractRepository.findById(contractId);
      if (!contract || contract.tenantId !== tenantId) {
        return null;
      }

      // Get the latest version
      const versions = await this.contractRepository.getVersionHistory(
        contractId
      );
      if (!versions || versions.length === 0) {
        return {
          fileName: "",
          mimeType: "",
          size: 0,
          uploadDate: new Date(),
          versionNumber: 0,
          hasDocument: false,
        };
      }

      // Get the latest version (highest version number)
      const latestVersion = versions.reduce(
        (latest: ContractVersion, current: ContractVersion) =>
          current.versionNumber > latest.versionNumber ? current : latest
      );

      // Return document metadata
      return {
        fileName: latestVersion.documentName,
        mimeType: latestVersion.mimeType || "application/octet-stream",
        size: latestVersion.documentSize,
        uploadDate: latestVersion.createdAt,
        versionNumber: latestVersion.versionNumber,
        hasDocument: !!latestVersion.documentContent,
      };
    } catch (error) {
      console.error("Error getting Agreement Document metadata:", error);
      throw new Error(
        `Failed to get Agreement Document metadata: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates or creates contract metadata
   * @param contractId Contract ID
   * @param metadataData Metadata data
   * @returns Updated or created metadata
   */
  async saveContractMetadata(
    contractId: string,
    metadataData: {
      totalValue?: number | Decimal;
      currency?: string;
      paymentTerms?: string;
      customMetadata?: Record<string, any>;
    }
  ): Promise<ContractMetadata> {
    try {
      // Check if contract exists
      const contract = await this.contractRepository.findById(contractId);

      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      // Get existing metadata if any
      let metadata = await this.contractRepository.getMetadata(contractId);

      if (metadata) {
        // Update existing metadata
        const updatedMetadata = new ContractMetadata({
          id: metadata.id,
          contractId,

          totalValue:
            metadataData.totalValue !== undefined
              ? (metadataData.totalValue as any)
              : metadata.totalValue,
          currency:
            metadataData.currency !== undefined
              ? metadataData.currency
              : metadata.currency,
          paymentTerms:
            metadataData.paymentTerms !== undefined
              ? metadataData.paymentTerms
              : metadata.paymentTerms,
          customMetadata:
            metadataData.customMetadata !== undefined
              ? metadataData.customMetadata
              : metadata.customMetadata,
          autoExtractedFields: metadata.autoExtractedFields,
          encryptedFields: metadata.encryptedFields,
          lastExtractedAt: metadata.lastExtractedAt,
          createdAt: metadata.createdAt,
          updatedAt: new Date(),
        });

        return await this.contractRepository.saveMetadata(updatedMetadata);
      } else {
        // Create new metadata
        const newMetadata = new ContractMetadata({
          id: uuidv4(),
          contractId,
          totalValue: metadataData.totalValue as any,
          currency: metadataData.currency,
          paymentTerms: metadataData.paymentTerms,
          customMetadata: metadataData.customMetadata,
        });

        return await this.contractRepository.saveMetadata(newMetadata);
      }
    } catch (error) {
      console.error("Error saving contract metadata:", error);
      throw new Error(
        `Failed to save contract metadata: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets metadata for a contract
   * @param contractId Contract ID
   * @param tenantId Tenant ID for validation
   * @returns Contract metadata or null if not found
   */
  async getContractMetadata(
    contractId: string,
    tenantId: string
  ): Promise<ContractMetadata | null> {
    try {
      // Validate contract belongs to tenant
      const contract = await this.contractRepository.findById(contractId);

      if (!contract) {
        return null;
      }

      if (contract.tenantId !== tenantId) {
        return null; // Don't return metadata if contract doesn't belong to tenant
      }

      return await this.contractRepository.getMetadata(contractId);
    } catch (error) {
      console.error("Error getting contract metadata:", error);
      throw new Error(
        `Failed to get contract metadata: ${(error as Error).message}`
      );
    }
  }

  /**
   * Creates or updates contract metadata
   * @param metadataData Metadata data
   * @returns Created or updated metadata
   */
  async createOrUpdateContractMetadata(metadataData: {
    contractId: string;
    totalValue?: number | Decimal;
    currency?: string;
    paymentTerms?: string;
    autoExtractedFields?: Record<string, any>;
    customMetadata?: Record<string, any>;
  }): Promise<ContractMetadata> {
    try {
      // Check if contract exists
      const contract = await this.contractRepository.findById(
        metadataData.contractId
      );

      if (!contract) {
        throw new Error(
          `Contract with ID ${metadataData.contractId} not found`
        );
      }

      // Get existing metadata if any
      let metadata = await this.contractRepository.getMetadata(
        metadataData.contractId
      );

      if (metadata) {
        // Update existing metadata
        const updatedMetadata = new ContractMetadata({
          id: metadata.id,
          contractId: metadataData.contractId,
          totalValue:
            metadataData.totalValue !== undefined
              ? (metadataData.totalValue as any)
              : metadata.totalValue,
          currency:
            metadataData.currency !== undefined
              ? metadataData.currency
              : metadata.currency,
          paymentTerms:
            metadataData.paymentTerms !== undefined
              ? metadataData.paymentTerms
              : metadata.paymentTerms,
          autoExtractedFields:
            metadataData.autoExtractedFields !== undefined
              ? metadataData.autoExtractedFields
              : metadata.autoExtractedFields,
          customMetadata:
            metadataData.customMetadata !== undefined
              ? metadataData.customMetadata
              : metadata.customMetadata,
          encryptedFields: metadata.encryptedFields,
          lastExtractedAt: new Date(),
          createdAt: metadata.createdAt,
          updatedAt: new Date(),
        });

        return await this.contractRepository.saveMetadata(updatedMetadata);
      } else {
        // Create new metadata
        const newMetadata = new ContractMetadata({
          id: uuidv4(),
          contractId: metadataData.contractId,
          totalValue: metadataData.totalValue as any,
          currency: metadataData.currency,
          paymentTerms: metadataData.paymentTerms,
          autoExtractedFields: metadataData.autoExtractedFields,
          customMetadata: metadataData.customMetadata,
          lastExtractedAt: new Date(),
        });

        return await this.contractRepository.saveMetadata(newMetadata);
      }
    } catch (error) {
      console.error("Error creating or updating contract metadata:", error);
      throw new Error(
        `Failed to create or update contract metadata: ${(error as Error).message
        }`
      );
    }
  }

  /**
   * Updates contract status
   * @param contractId Contract ID
   * @param status New status
   * @returns Updated contract
   */
  async updateContractStatus(
    contractId: string,
    status: ContractStatus
  ): Promise<Contract> {
    try {
      const contract = await this.contractRepository.findById(contractId);

      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      // Update status using domain method
      contract.updateStatus(status);

      // Save changes
      return await this.contractRepository.update(contract);
    } catch (error) {
      console.error("Error updating contract status:", error);
      throw new Error(
        `Failed to update contract status: ${(error as Error).message}`
      );
    }
  }

  /**
   * Generates a changelog for version history
   * @param versionNumber The version number
   * @returns Changelog object
   */
  private generateChangelog(versionNumber: number): Record<string, any> {
    return {
      version: versionNumber,
      timestamp: new Date().toISOString(),
      changes: [
        {
          type: "VERSION_CREATED",
          message: `Created version ${versionNumber}`,
        },
      ],
    };
  }

  /**
   * Creates a new contract version with document
   * @param versionData Version data
   * @returns Created contract version
   */
  async createContractVersion(versionData: {
    contractId: string;
    versionNumber: number;
    documentName: string;
    documentFormat: DocumentFormat;
    documentBuffer: Buffer;
    versionComment?: string;
  }): Promise<any> {
    try {
      // Extract data from input
      const contractId = versionData.contractId;
      const versionNumber = versionData.versionNumber;
      const documentName = versionData.documentName;
      const documentFormat = versionData.documentFormat;
      const documentBuffer = versionData.documentBuffer;
      const versionComment = versionData.versionComment;

      // Check if contract exists
      const contract = await this.contractRepository.findById(contractId);
      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      // Generate a unique ID for the document
      const versionId = uuidv4();

      // Store the document in the database
      // In a production environment, you might want to store the file in a blob storage
      // and only keep the reference in the database
      const documentUri = `memory://${versionId}`;

      // Determine MIME type based on file extension
      const mimeType = this.getMimeTypeFromFileName(documentName);

      // Create version data object
      const versionEntityData = {
        id: versionId,
        contractId,
        versionNumber,
        documentUri,
        documentFormat,
        documentSize: documentBuffer.length,
        documentName,
        documentContent: documentBuffer, // Store actual document content
        mimeType, // Store MIME type for proper file handling
        versionComment: versionComment || null,
        changelog: this.generateChangelog(versionNumber),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Create version in the database
      // Create a ContractVersion instance to satisfy type checking
      const contractVersionEntity = new ContractVersion(versionEntityData);
      const contractVersion = await this.contractRepository.createVersion(
        contractVersionEntity,
        documentBuffer
      );

      // Update contract's current version ID
      // Create a copy of the contract with the updated field
      const contractData = contract.toDTO();
      const updatedContract = new Contract({
        ...contractData,
        status: contract.status, // Use the original status field from the contract
        currentVersionId: versionId,
      });
      await this.contractRepository.update(updatedContract);

      return contractVersion;
    } catch (error) {
      console.error("Error creating contract version:", error);
      throw new Error(
        `Failed to create contract version: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets contracts for autocomplete (lightweight) using ContractExtraction
   * @param tenantId Tenant ID
   * @param search Optional search term
   * @returns Array of contract extractions with minimal data for autocomplete
   */
  async getContractsForAutocomplete(
    tenantId: string,
    search?: string
  ): Promise<any[]> {
    try {
      const filters: any = {
        page: 1,
        limit: 50, // Limit to 50 results for performance
      };

      if (search && search.trim()) {
        filters.title = search.trim();
      }

      const result = await this.contractExtractionRepository.getByTenantWithFilters(
        tenantId,
        filters
      );

      // Transform ContractExtraction data to autocomplete format
      return result.extractions.map((extraction) => {
        const fixedFields = extraction.fixedFields || {};
        const contractName = fixedFields.original_filename?.value ||
          'Unnamed Contract';
        const supplier = fixedFields.provider?.value ||
          'Unknown Supplier';

        return {
          id: extraction.contractId,
          title: contractName,
          contractNumber: fixedFields.contract_id?.value || null,
          supplier: supplier,
        };
      });
    } catch (error) {
      console.error("Error getting contracts for autocomplete:", error);
      throw new Error(
        `Failed to get contracts for autocomplete: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets MIME type from file name extension
   * @param fileName File name
   * @returns MIME type
   */
  private getMimeTypeFromFileName(fileName: string): string {
    const extension = fileName.toLowerCase().split(".").pop();

    const mimeTypes: { [key: string]: string } = {
      pdf: "application/pdf",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      txt: "text/plain",
      rtf: "application/rtf",
      odt: "application/vnd.oasis.opendocument.text",
      html: "text/html",
      htm: "text/html",
      md: "text/markdown",
      csv: "text/csv",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      xls: "application/vnd.ms-excel",
    };

    return mimeTypes[extension || ""] || "application/octet-stream";
  }

  /**
   * Extracts metadata from a document (placeholder for AI extraction)
   * @param contractId Contract ID
   * @param versionId Version ID
   * @returns Updated metadata
   */
  async extractMetadata(
    contractId: string,
    versionId: string
  ): Promise<ContractMetadata> {
    try {
      // Get contract and version
      const contract = await this.contractRepository.findById(contractId);
      if (!contract) {
        throw new Error(`Contract with ID ${contractId} not found`);
      }

      // Get existing metadata
      let metadata = await this.contractRepository.getMetadata(contractId);

      // In a real implementation, this would trigger an AI process to extract data
      // For demonstration, we'll create sample extracted data
      const extractedFields = {
        extractedAt: new Date().toISOString(),
        keyTerms: ["payment", "termination", "confidentiality"],
        parties: ["Company A", "Company B"],
        effectiveDate: new Date().toISOString(),
        expirationDate: new Date(
          Date.now() + 365 * 24 * 60 * 60 * 1000
        ).toISOString(),
        automatedRiskScore: 0.35,
      };

      if (metadata) {
        // Update metadata with extracted fields
        metadata.updateAutoExtractedFields(extractedFields);
        return await this.contractRepository.saveMetadata(metadata);
      } else {
        // Create new metadata with extracted fields
        const newMetadata = new ContractMetadata({
          id: uuidv4(),
          contractId,
          autoExtractedFields: extractedFields,
          lastExtractedAt: new Date(),
        });

        return await this.contractRepository.saveMetadata(newMetadata);
      }
    } catch (error) {
      console.error("Error extracting metadata:", error);
      throw new Error(
        `Failed to extract metadata: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets contracts with hierarchy information for a specific group
   * @param groupId Group identifier (e.g., "SupplierName:ContractNumber")
   * @param tenantId Tenant ID for validation
   * @returns Contracts with hierarchy relationships
   */
  async getContractsWithHierarchy(
    groupId: string,
    tenantId: string
  ): Promise<ContractWithHierarchy[]> {
    try {
      // Search for contracts in the group
      const searchResult = await this.contractRepository.search({
        tenantId,
        limit: 100, // Reasonable limit for a group
        page: 1,
      });

      // Filter contracts that belong to the specified group
      // This would need to be enhanced based on how you store group information
      const groupContracts = searchResult.contracts.filter((contract) => {
        // For now, we'll use a simple matching logic
        // In a real implementation, you'd have a proper group field
        return true; // Placeholder - implement actual group filtering
      });

      // Convert to ContractWithHierarchy format
      const contractsWithHierarchy: ContractWithHierarchy[] =
        groupContracts.map((contract) => ({
          id: contract.id,
          title: contract.title,
          agreementType: contract.agreementType,
          startDate: contract.startDate || undefined,
          endDate: contract.endDate || undefined,
          groupId,
        }));

      // Analyze hierarchy relationships
      return this.hierarchyService.analyzeContractHierarchy(
        contractsWithHierarchy
      );
    } catch (error) {
      console.error("Error getting contracts with hierarchy:", error);
      throw new Error(
        `Failed to get contracts with hierarchy: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets hierarchy information for a specific agreement type
   * @param agreementType Agreement type
   * @returns Hierarchy information or null if not found
   */
  getHierarchyInfo(agreementType: string) {
    return this.hierarchyService.getHierarchyInfo(agreementType);
  }

  /**
   * Gets all supported hierarchy types
   * @returns Map of document types to hierarchy info
   */
  getAllHierarchyTypes() {
    return this.hierarchyService.getAllHierarchyTypes();
  }

  /**
   * Determines if one contract type can be a parent of another
   * @param parentType Parent agreement type
   * @param childType Child agreement type
   * @returns True if parent-child relationship is valid
   */
  canBeParent(parentType: string, childType: string): boolean {
    return this.hierarchyService.canBeParent(parentType, childType);
  }

  /**
   * Gets repository statistics for all contracts in a tenant
   * @param tenantId Tenant ID
   * @returns Repository statistics including counts by status, agreement types, etc.
   */
  async getRepositoryStatistics(tenantId: string): Promise<{
    totalContracts: number;
    activeContracts: number;
    inactiveContracts: number;
    unknownStatusContracts: number;
    agreementTypesCount: number;
    recentlyAddedCount: number;
    agreementTypeBreakdown: { [key: string]: number };
  }> {
    try {
      // Get all contract extractions for the tenant (same data source as frontend)
      const allExtractions =
        await this.contractExtractionRepository.getByTenantId(tenantId);

      // Calculate current date for status determination
      const now = new Date();

      // Calculate statistics
      let activeCount = 0;
      let inactiveCount = 0;
      let unknownCount = 0;
      let recentlyAddedCount = 0;
      const agreementTypeBreakdown: { [key: string]: number } = {};

      // 30 days ago for "recently added" calculation
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(now.getDate() - 30);

      allExtractions.forEach((extraction) => {
        // Calculate status based on dates from fixedFields (same logic as frontend utility)
        let status = "Unknown";
        const fixedFields = extraction.fixedFields || {};
        const startDateValue = (fixedFields as any).start_date?.value;
        const endDateValue = (fixedFields as any).end_date?.value;

        // Use the same logic as frontend calculateContractStatus utility
        if (!startDateValue || !endDateValue) {
          status = "Unknown";
        } else if (
          (typeof startDateValue === "string" &&
            (startDateValue === "N/A" || startDateValue.trim() === "")) ||
          (typeof endDateValue === "string" &&
            (endDateValue === "N/A" || endDateValue.trim() === ""))
        ) {
          status = "Unknown";
        } else {
          try {
            const startDate = new Date(startDateValue);
            const endDate = new Date(endDateValue);

            // Check if dates are valid
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
              status = "Unknown";
            } else {
              // Set time to start of day for accurate date comparison (same as frontend)
              const currentDate = new Date(now);
              currentDate.setHours(0, 0, 0, 0);
              startDate.setHours(0, 0, 0, 0);
              endDate.setHours(0, 0, 0, 0);

              // Active: current date is between start and end date (inclusive)
              if (currentDate >= startDate && currentDate <= endDate) {
                status = "Active";
              } else {
                // Inactive: current date is outside the contract period
                status = "Inactive";
              }
            }
          } catch {
            status = "Unknown";
          }
        }

        // Count by status
        if (status === "Active") {
          activeCount++;
        } else if (status === "Inactive") {
          inactiveCount++;
        } else {
          unknownCount++;
        }

        // Count recently added (within last 30 days)
        if (extraction.createdAt && extraction.createdAt >= thirtyDaysAgo) {
          recentlyAddedCount++;
        }

        // Count by agreement type from fixedFields
        const agreementTypeValue =
          (fixedFields as any).agreement_type?.value || "Unknown";
        agreementTypeBreakdown[agreementTypeValue] =
          (agreementTypeBreakdown[agreementTypeValue] || 0) + 1;
      });

      return {
        totalContracts: allExtractions.length,
        activeContracts: activeCount,
        inactiveContracts: inactiveCount,
        unknownStatusContracts: unknownCount,
        agreementTypesCount: Object.keys(agreementTypeBreakdown).length,
        recentlyAddedCount,
        agreementTypeBreakdown,
      };
    } catch (error) {
      console.error("Error getting repository statistics:", error);
      throw new Error(
        `Failed to get repository statistics: ${(error as Error).message}`
      );
    }
  }
}
