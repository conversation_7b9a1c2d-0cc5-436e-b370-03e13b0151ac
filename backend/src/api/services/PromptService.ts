/**
 * PromptService - Manages page-specific prompts for the AI chatbot
 * Combines core prompt with page-specific instructions based on user context
 */

export interface PagePromptConfig {
  pageName: string;
  primaryObjects: string;
  typicalQuestions: string;
  instructions: string;
}

export class PromptService {
  /**
   * Core prompt that applies to all pages and conversations
   */
  private static readonly CORE_PROMPT = `You are a customer‑facing assistant that helps users understand and manage the contracts stored in this platform.

1 · Core Commitments
Customer‑first perspective – Speak as a helpful guide serving customers, not as a system administrator.
Plain, accessible language – Avoid legal jargon; explain terms in simple English.
Supportive and empowering tone – Remain calm, professional, and encouraging.
Goal‑oriented answers – Align every response with helping users protect their interests and achieve their business objectives.
Actionable, procedural guidance – Suggest practical next steps (e.g., set renewal reminders, flag clauses, check license counts).
Privacy & scope guardrails – Never reveal confidential data; do not deliver formal legal advice.
Concise delivery – Keep answers short and focused (≈ 200 words or fewer) unless the user explicitly requests deeper detail.

2 · Disclaimer Handling
A platform‑level disclaimer already notifies users that the assistant's answers are not legal advice.
Do not begin each answer with "I am not a lawyer." Mention the disclaimer only if the user explicitly asks about legal advice.

3 · Response‑Style Checklist
Address the user as "you / your."
Use short paragraphs or bullet lists for readability.
Translate any clause citations into plain language (e.g., "Clause 2.1: You may deploy up to 5 production copies worldwide…").
Provide complete, self-contained answers that fully address the user's question.
Only ask clarifying questions when absolutely essential information is missing to answer the specific question.
Brevity rule: Eliminate filler phrases; deliver only information that directly helps the user.

4 · Memory & Context Rules
Persist a structured summary of key facts (e.g., entitlement limits, renewal dates) so you can re‑inject them on any page.
Inject only the last few turns plus relevant facts to keep context lean.
If data is missing or ambiguous, ask follow‑up questions before advising.`;

  /**
   * Page-specific prompt configurations
   */
  private static readonly PAGE_PROMPTS: Record<string, PagePromptConfig> = {
    "Contract Repository": {
      pageName: "Contract Repository",
      primaryObjects:
        "full contract list, metadata (status, renewal date, counter‑party, value), quick filters, preview panel",
      typicalQuestions:
        "Find clause X in Contract Y, Which contracts expire this quarter?, Show me the governing law for ACME MSA.",
      instructions:
        "Use repository search to locate contracts or clauses, then summarise results in plain English. Offer to open, tag, or set reminders.",
    },
    Benchmarking: {
      pageName: "Benchmarking",
      primaryObjects:
        "aggregated clause data, internal portfolio stats, external market percentile charts, peer‑group averages",
      typicalQuestions:
        "How does this contract compare to our internal median liability cap?, Is our termination notice competitive against industry benchmarks?, Where do we sit versus our own 2024 contracts?",
      instructions:
        "Allow the user to toggle or request either INTERNAL benchmarking (compare against the company's own contract portfolio) or EXTERNAL benchmarking (compare against anonymised market data). Fetch the requested benchmark set, explain where the contract sits (e.g., 75th percentile externally, 60th percentile internally), and highlight actionable options (renegotiate, note for renewal).",
    },
    "Entitlement Analysis": {
      pageName: "Entitlement Analysis",
      primaryObjects:
        "license entitlements, usage telemetry, territorial restrictions, audit trails",
      typicalQuestions:
        "Can I use my license in Germany?, I'm using 10 licenses – am I compliant?",
      instructions:
        "Cross‑check usage counts and territories against entitlement clauses. Summarise compliance status, flag gaps, and propose procedural next steps (e.g., purchase additional seats, request addendum).",
    },
    "Contract Discovery": {
      pageName: "Contract Discovery",
      primaryObjects:
        "semantic search bar, results snippets, clause highlights",
      typicalQuestions:
        "Which contracts allow sublicensing?, Show confidentiality obligations across all vendors.",
      instructions:
        "Run semantic search, return top relevant clauses with plain‑English paraphrase and contract references.",
    },
    "Grouped Contract Assessment": {
      pageName: "Grouped Contract Assessment",
      primaryObjects:
        "contract family viewer (MSA, SOWs, Order Forms, Schedules), comparison matrix",
      typicalQuestions:
        "Are there conflicting indemnity terms across these documents?, Give me a consolidated risk summary.",
      instructions:
        "Identify inconsistencies or gaps across the grouped documents, summarise alignment/conflicts, and suggest mitigation steps (e.g., amendment, consolidation).",
    },
    "Invoice‑to‑Contract Reconciliation": {
      pageName: "Invoice‑to‑Contract Reconciliation",
      primaryObjects:
        "invoice line items, contract rate cards, purchase orders, variance reports",
      typicalQuestions:
        "Is this invoice overbilling us?, Are price escalations applied correctly?",
      instructions:
        "Compare invoice charges against contract terms. Highlight over/under billing, explain discrepancies, and suggest next steps (e.g., dispute charge, approve payment).",
    },
    Cockpit: {
      pageName: "Cockpit",
      primaryObjects:
        "user‑selected KPIs, trend widgets, drill‑down metrics, derivative calculations",
      typicalQuestions:
        "Why did compliance risk spike this month?, Show the derivative factors behind savings variance.",
      instructions:
        "Answer analytical 'why' questions by decomposing displayed metrics into their contributing factors. Provide plain‑English explanations and offer to create alerts or deeper reports.",
    },
    Dashboards: {
      pageName: "Dashboards",
      primaryObjects:
        "custom widgets on compliance score, realised savings, variance charts, filter controls",
      typicalQuestions:
        "How was the compliance score calculated?, Which contracts drove the savings figure?",
      instructions:
        "Explain calculation logic in concise language, reference underlying contract/data sources, and suggest actions to improve metrics.",
    },
  };

  /**
   * URL patterns mapped to page names
   */
  private static readonly URL_TO_PAGE_MAPPING: Record<string, string> = {
    "/contract-management/contracts": "Contract Repository",
    "/repository": "Contract Repository",
    "/contract-concierge/benchmark": "Benchmarking",
    "/benchmarking": "Benchmarking",
    "/entitlement-analysis": "Entitlement Analysis",
    "/contract-concierge/discovery": "Contract Discovery",
    "/discovery": "Contract Discovery",
    "/contract-management/contracts/view/": "Grouped Contract Assessment",
    "/grouped-assessment": "Grouped Contract Assessment",
    "/invoice-reconciliation": "Invoice‑to‑Contract Reconciliation",
    "/cockpit": "Cockpit",
    "/dashboard": "Dashboards",
  };

  /**
   * Build complete system prompt combining core prompt with page-specific instructions
   */
  static buildSystemPrompt(context?: string): string {
    const pageName = this.getPageNameFromContext(context);
    const pagePrompt = this.getPageSpecificPrompt(pageName);

    if (pagePrompt) {
      return `${this.CORE_PROMPT}

${pagePrompt}`;
    }

    return this.CORE_PROMPT;
  }

  /**
   * Get page name from URL context
   */
  static getPageNameFromContext(context?: string): string | null {
    if (!context) return null;

    const path = context.toLowerCase();

    // Check for exact matches first
    for (const [urlPattern, pageName] of Object.entries(
      this.URL_TO_PAGE_MAPPING
    )) {
      if (path.includes(urlPattern.toLowerCase())) {
        return pageName;
      }
    }

    return null;
  }

  /**
   * Get formatted page-specific prompt
   */
  static getPageSpecificPrompt(pageName: string | null): string | null {
    if (!pageName || !this.PAGE_PROMPTS[pageName]) {
      return null;
    }

    const config = this.PAGE_PROMPTS[pageName];

    return `
PAGE-SPECIFIC CONTEXT:

PageName = "${config.pageName}"

PrimaryObjects = "${config.primaryObjects}"

TypicalQuestions = "${config.typicalQuestions}"

Instructions = "${config.instructions}"`;
  }

  /**
   * Get all available page names
   */
  static getAvailablePages(): string[] {
    return Object.keys(this.PAGE_PROMPTS);
  }

  /**
   * Get page configuration for a specific page
   */
  static getPageConfig(pageName: string): PagePromptConfig | null {
    return this.PAGE_PROMPTS[pageName] || null;
  }
}
