/**
 * QuestionAnalysisService - Analyzes user questions and generates contextual follow-up suggestions
 */

import { GoogleGenAI } from "@google/genai";
import { logger } from "../../infrastructure/logging/logger";

export interface QuestionAnalysis {
  questionType: string;
  entities: string[];
  intent: string;
  subjectMatter: string;
  complexity: "simple" | "moderate" | "complex";
  keyTerms: string[];
  specificFocus: string;
}

export interface FollowUpSuggestion {
  id: string;
  text: string;
  category: string;
  reasoning?: string;
}

export class QuestionAnalysisService {
  // Initialize Google AI client
  private static getGoogleAI() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("GEMINI_API_KEY is not defined in environment variables");
    }
    return new GoogleGenAI({ apiKey });
  }
  /**
   * Analyze a user question to understand its type, intent, and key entities
   */
  static async analyzeQuestion(
    question: string
  ): Promise<QuestionAnalysis | null> {
    try {
      // Simple keyword-based analysis that's much more reliable than AI
      const lowerQuestion = question.toLowerCase();

      let subjectMatter = "general";
      let keyTerms: string[] = [];
      let specificFocus = question;

      // Determine subject matter based on keywords
      if (
        lowerQuestion.includes("payment") ||
        lowerQuestion.includes("pay") ||
        lowerQuestion.includes("invoice") ||
        lowerQuestion.includes("bill")
      ) {
        subjectMatter = "payment";
        keyTerms = ["payment", "pay", "invoice", "bill"];
      } else if (
        lowerQuestion.includes("terminat") ||
        lowerQuestion.includes("end") ||
        lowerQuestion.includes("cancel")
      ) {
        subjectMatter = "termination";
        keyTerms = ["terminate", "end", "cancel"];
      } else if (
        lowerQuestion.includes("renew") ||
        lowerQuestion.includes("extend") ||
        lowerQuestion.includes("continue")
      ) {
        subjectMatter = "renewal";
        keyTerms = ["renew", "extend", "continue"];
      } else if (
        lowerQuestion.includes("liabilit") ||
        lowerQuestion.includes("risk") ||
        lowerQuestion.includes("insurance")
      ) {
        subjectMatter = "liability";
        keyTerms = ["liability", "risk", "insurance"];
      } else if (
        lowerQuestion.includes("complian") ||
        lowerQuestion.includes("obligat") ||
        lowerQuestion.includes("requirement")
      ) {
        subjectMatter = "compliance";
        keyTerms = ["compliance", "obligation", "requirement"];
      } else if (
        lowerQuestion.includes("date") ||
        lowerQuestion.includes("deadline") ||
        lowerQuestion.includes("expire")
      ) {
        subjectMatter = "dates";
        keyTerms = ["date", "deadline", "expire"];
      } else if (
        lowerQuestion.includes("license") ||
        lowerQuestion.includes("licensing")
      ) {
        subjectMatter = "licensing";
        keyTerms = ["license", "licensing"];
      }

      // Determine question type
      let questionType = "factual";
      if (
        lowerQuestion.includes("how") ||
        lowerQuestion.includes("process") ||
        lowerQuestion.includes("procedure")
      ) {
        questionType = "procedural";
      } else if (
        lowerQuestion.includes("compare") ||
        lowerQuestion.includes("versus") ||
        lowerQuestion.includes("vs")
      ) {
        questionType = "comparative";
      } else if (
        lowerQuestion.includes("why") ||
        lowerQuestion.includes("analyze") ||
        lowerQuestion.includes("assess")
      ) {
        questionType = "analytical";
      }

      // Determine intent
      let intent = "understand";
      if (
        lowerQuestion.includes("do") ||
        lowerQuestion.includes("action") ||
        lowerQuestion.includes("next step")
      ) {
        intent = "take_action";
      } else if (
        lowerQuestion.includes("compliant") ||
        lowerQuestion.includes("check") ||
        lowerQuestion.includes("verify")
      ) {
        intent = "check_compliance";
      }

      const analysis: QuestionAnalysis = {
        questionType,
        entities: keyTerms,
        intent,
        subjectMatter,
        complexity: "simple",
        keyTerms,
        specificFocus: specificFocus.substring(0, 100),
      };

      logger.info("Question analysis completed", {
        question: question.substring(0, 100),
        analysis,
      });

      return analysis;
    } catch (error) {
      logger.error("Error analyzing question", { error, question });
      return null;
    }
  }

  /**
   * Generate contextual follow-up questions based on the conversation
   */
  static async generateContextualFollowUps(
    userQuestion: string,
    aiResponse: string,
    questionAnalysis: QuestionAnalysis | null,
    pageContext: string,
    contractContext?: any
  ): Promise<FollowUpSuggestion[]> {
    try {
      const apiKey = process.env.GEMINI_API_KEY;
      if (!apiKey) {
        logger.error("GEMINI_API_KEY not found for follow-up generation");
        return this.getFallbackFollowUps(questionAnalysis, pageContext);
      }

      logger.info("Starting follow-up generation", {
        userQuestion: userQuestion.substring(0, 100),
        aiResponseLength: aiResponse.length,
        hasQuestionAnalysis: !!questionAnalysis,
      });

      // Build context for follow-up generation
      let analysisContext = "";
      if (questionAnalysis) {
        analysisContext = `
QUESTION ANALYSIS:
- Type: ${questionAnalysis.questionType}
- Subject: ${questionAnalysis.subjectMatter}
- Intent: ${questionAnalysis.intent}
- Entities: ${questionAnalysis.entities.join(", ")}
- Key Terms: ${questionAnalysis.keyTerms?.join(", ") || "none"}
- Specific Focus: ${questionAnalysis.specificFocus || "general inquiry"}
- Complexity: ${questionAnalysis.complexity}`;
      }

      let contractInfo = "";
      if (contractContext) {
        contractInfo = `
CONTRACT CONTEXT: User is discussing specific contract(s) with relevant details available.`;
      }

      // Enhanced follow-up generation that analyzes the specific conversation
      const followUpPrompt = `You are generating follow-up questions for a contract management conversation.

CONVERSATION:
User asked: "${userQuestion}"
AI responded: "${aiResponse}"

TASK: Generate 3 follow-up questions that a user would naturally want to ask after receiving this specific response.

INSTRUCTIONS:
1. Carefully read what the AI actually said in the response
2. Identify specific details, terms, numbers, dates, or processes mentioned
3. Generate questions that dig deeper into those specific details
4. Focus on practical next steps or clarifications the user would need
5. Keep questions under 60 characters
6. Make questions feel like a natural continuation of THIS conversation

EXAMPLES:
- If AI said "payment due in 30 days" → "What happens if I pay late?"
- If AI said "liability cap of $1M" → "What's excluded from this cap?"
- If AI said "requires 60 days notice" → "How do I give proper notice?"
- If AI said "auto-renewal clause" → "How do I prevent auto-renewal?"

Return ONLY this JSON format:
[
  {"text": "specific question about the response", "category": "followup"},
  {"text": "another specific question", "category": "followup"},
  {"text": "third specific question", "category": "followup"}
]`;

      logger.info("Calling Gemini API for follow-ups", {
        promptLength: followUpPrompt.length,
        timeout: 20000,
      });

      // Call Gemini API using @google/genai SDK
      const googleAI = this.getGoogleAI();
      const response = await googleAI.models.generateContent({
        model: "gemini-2.0-flash",
        config: {
          temperature: 0.7,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 1024,
        },
        contents: {
          role: "user",
          parts: [
            {
              text: followUpPrompt,
            },
          ],
        },
      });

      logger.info("Gemini API response received", {
        hasResponse: !!response,
        hasText: !!response?.text,
      });

      if (response && response.text) {
        const responseText = response.text;

        logger.info("Parsing Gemini response for follow-ups", {
          responseText: responseText.substring(0, 500),
          responseLength: responseText.length,
          fullResponse: responseText, // Log full response to debug JSON issues
        });

        try {
          // Extract JSON from response text (Gemini sometimes adds extra text)
          const jsonText = this.extractJsonFromText(responseText);
          const suggestions = JSON.parse(jsonText);

          // Validate and format suggestions
          const validSuggestions = suggestions
            .filter((s: any) => s && s.text && s.text.length <= 80)
            .slice(0, 4) // Limit to 4 suggestions
            .map((s: any, index: number) => ({
              id: `followup-${Date.now()}-${index}`,
              text: s.text.trim(),
              category: s.category || "followup",
              reasoning: s.reasoning || undefined,
            }));

          logger.info("Generated contextual follow-ups", {
            count: validSuggestions.length,
            userQuestion: userQuestion.substring(0, 100),
            aiResponseLength: aiResponse.length,
            suggestions: validSuggestions.map((s: any) => s.text),
            questionAnalysis: questionAnalysis
              ? {
                  type: questionAnalysis.questionType,
                  subject: questionAnalysis.subjectMatter,
                  focus: questionAnalysis.specificFocus,
                }
              : null,
          });

          return validSuggestions;
        } catch (parseError) {
          logger.error("Failed to parse follow-up suggestions JSON", {
            responseText: responseText.substring(0, 1000),
            extractedJson: this.extractJsonFromText(responseText),
            parseError:
              parseError instanceof Error
                ? parseError.message
                : String(parseError),
          });
          // Use rule-based generation instead of generic fallbacks
          return this.generateSimpleRuleBasedFollowUps(
            userQuestion,
            aiResponse,
            questionAnalysis
          );
        }
      }

      // If AI generation fails, try simple rule-based generation
      return this.generateSimpleRuleBasedFollowUps(
        userQuestion,
        aiResponse,
        questionAnalysis
      );
    } catch (error) {
      logger.error("Error generating contextual follow-ups", {
        error: error instanceof Error ? error.message : String(error),
        userQuestion: userQuestion.substring(0, 100),
      });
      // Try simple rule-based generation before falling back to generic
      return this.generateSimpleRuleBasedFollowUps(
        userQuestion,
        aiResponse,
        questionAnalysis
      );
    }
  }

  /**
   * Generate simple rule-based follow-ups based on keywords in the conversation
   */
  private static generateSimpleRuleBasedFollowUps(
    userQuestion: string,
    aiResponse: string,
    questionAnalysis: QuestionAnalysis | null
  ): FollowUpSuggestion[] {
    const baseId = `rule-${Date.now()}`;
    const lowerQuestion = userQuestion.toLowerCase();
    const lowerResponse = aiResponse.toLowerCase();

    logger.info("Generating rule-based follow-ups", {
      userQuestion: userQuestion.substring(0, 100),
      aiResponseLength: aiResponse.length,
      questionAnalysis: questionAnalysis?.subjectMatter || "unknown",
    });

    // Analyze what the user specifically asked about and what the AI mentioned

    // Payment-related questions and responses
    if (
      lowerQuestion.includes("payment") ||
      lowerResponse.includes("payment") ||
      lowerQuestion.includes("pay") ||
      lowerResponse.includes("pay")
    ) {
      // More specific follow-ups based on what was mentioned in the response
      if (
        lowerResponse.includes("30 days") ||
        lowerResponse.includes("net 30")
      ) {
        return [
          {
            id: `${baseId}-1`,
            text: "What happens if I pay late?",
            category: "followup",
          },
          {
            id: `${baseId}-2`,
            text: "Are there early payment discounts?",
            category: "followup",
          },
          {
            id: `${baseId}-3`,
            text: "How do I submit invoices?",
            category: "followup",
          },
        ];
      } else if (
        lowerResponse.includes("discount") ||
        lowerResponse.includes("early")
      ) {
        return [
          {
            id: `${baseId}-1`,
            text: "How do I claim the discount?",
            category: "followup",
          },
          {
            id: `${baseId}-2`,
            text: "What's the discount deadline?",
            category: "followup",
          },
          {
            id: `${baseId}-3`,
            text: "Are there other discounts available?",
            category: "followup",
          },
        ];
      } else {
        return [
          {
            id: `${baseId}-1`,
            text: "What are late payment penalties?",
            category: "followup",
          },
          {
            id: `${baseId}-2`,
            text: "Are there early payment discounts?",
            category: "followup",
          },
          {
            id: `${baseId}-3`,
            text: "How do I dispute a charge?",
            category: "followup",
          },
        ];
      }
    }

    // Termination-related questions and responses
    if (
      lowerQuestion.includes("terminat") ||
      lowerResponse.includes("terminat") ||
      lowerQuestion.includes("end") ||
      lowerResponse.includes("end") ||
      lowerQuestion.includes("cancel") ||
      lowerResponse.includes("cancel")
    ) {
      return [
        {
          id: `${baseId}-1`,
          text: "What's the notice period required?",
          category: "followup",
        },
        {
          id: `${baseId}-2`,
          text: "Are there termination fees?",
          category: "followup",
        },
        {
          id: `${baseId}-3`,
          text: "What happens to my data?",
          category: "followup",
        },
      ];
    }

    // Date/time-related questions and responses
    if (
      lowerQuestion.includes("date") ||
      lowerResponse.includes("date") ||
      lowerQuestion.includes("deadline") ||
      lowerResponse.includes("deadline") ||
      lowerQuestion.includes("expire") ||
      lowerResponse.includes("expire")
    ) {
      return [
        {
          id: `${baseId}-1`,
          text: "What notifications are required?",
          category: "followup",
        },
        {
          id: `${baseId}-2`,
          text: "Are there any grace periods?",
          category: "followup",
        },
        {
          id: `${baseId}-3`,
          text: "How do I set up reminders?",
          category: "followup",
        },
      ];
    }

    // Liability/risk-related questions and responses
    if (
      lowerQuestion.includes("liabilit") ||
      lowerResponse.includes("liabilit") ||
      lowerQuestion.includes("risk") ||
      lowerResponse.includes("risk") ||
      lowerQuestion.includes("insurance") ||
      lowerResponse.includes("insurance")
    ) {
      return [
        {
          id: `${baseId}-1`,
          text: "What are the liability caps?",
          category: "followup",
        },
        {
          id: `${baseId}-2`,
          text: "What's excluded from coverage?",
          category: "followup",
        },
        {
          id: `${baseId}-3`,
          text: "Who handles insurance claims?",
          category: "followup",
        },
      ];
    }

    // Renewal-related questions and responses
    if (
      lowerQuestion.includes("renew") ||
      lowerResponse.includes("renew") ||
      lowerQuestion.includes("extend") ||
      lowerResponse.includes("extend") ||
      lowerQuestion.includes("continue") ||
      lowerResponse.includes("continue")
    ) {
      return [
        {
          id: `${baseId}-1`,
          text: "When do I need to give notice?",
          category: "followup",
        },
        {
          id: `${baseId}-2`,
          text: "Will prices change on renewal?",
          category: "followup",
        },
        {
          id: `${baseId}-3`,
          text: "Can I modify terms?",
          category: "followup",
        },
      ];
    }

    // Compliance/obligation questions and responses
    if (
      lowerQuestion.includes("complian") ||
      lowerResponse.includes("complian") ||
      lowerQuestion.includes("obligat") ||
      lowerResponse.includes("obligat") ||
      lowerQuestion.includes("requirement") ||
      lowerResponse.includes("requirement")
    ) {
      return [
        {
          id: `${baseId}-1`,
          text: "How do I track compliance?",
          category: "followup",
        },
        {
          id: `${baseId}-2`,
          text: "What are the key deadlines?",
          category: "followup",
        },
        {
          id: `${baseId}-3`,
          text: "What happens if I'm non-compliant?",
          category: "followup",
        },
      ];
    }

    // Default contextual follow-ups
    return this.getFallbackFollowUps(questionAnalysis, "");
  }

  /**
   * Generate fallback follow-up suggestions when AI generation fails
   */
  private static getFallbackFollowUps(
    questionAnalysis: QuestionAnalysis | null,
    pageContext: string
  ): FollowUpSuggestion[] {
    const baseId = `fallback-${Date.now()}`;

    // Subject-matter specific fallbacks
    if (questionAnalysis?.subjectMatter) {
      switch (questionAnalysis.subjectMatter) {
        case "payment":
          return [
            {
              id: `${baseId}-1`,
              text: "What are the late payment penalties?",
              category: "followup",
            },
            {
              id: `${baseId}-2`,
              text: "Are there early payment discounts?",
              category: "followup",
            },
            {
              id: `${baseId}-3`,
              text: "Who handles payment disputes?",
              category: "followup",
            },
          ];
        case "termination":
          return [
            {
              id: `${baseId}-1`,
              text: "What's the required notice period?",
              category: "followup",
            },
            {
              id: `${baseId}-2`,
              text: "Are there termination fees?",
              category: "followup",
            },
            {
              id: `${baseId}-3`,
              text: "What happens to my data?",
              category: "followup",
            },
          ];
        case "liability":
          return [
            {
              id: `${baseId}-1`,
              text: "What are the liability caps?",
              category: "followup",
            },
            {
              id: `${baseId}-2`,
              text: "Who handles insurance?",
              category: "followup",
            },
            {
              id: `${baseId}-3`,
              text: "What's excluded from coverage?",
              category: "followup",
            },
          ];
        case "renewal":
          return [
            {
              id: `${baseId}-1`,
              text: "When do I need to give notice?",
              category: "followup",
            },
            {
              id: `${baseId}-2`,
              text: "Will prices change on renewal?",
              category: "followup",
            },
            {
              id: `${baseId}-3`,
              text: "Can I modify terms during renewal?",
              category: "followup",
            },
          ];
      }
    }

    // Smart generic fallbacks based on common contract questions
    return [
      {
        id: `${baseId}-1`,
        text: "What are the key deadlines?",
        category: "followup",
      },
      {
        id: `${baseId}-2`,
        text: "How do I ensure compliance?",
        category: "followup",
      },
      {
        id: `${baseId}-3`,
        text: "What are my obligations?",
        category: "followup",
      },
    ];
  }

  /**
   * Extract JSON from text that might contain extra content
   */
  private static extractJsonFromText(text: string): string {
    // Remove common prefixes and suffixes
    let cleanText = text.trim();

    // Remove markdown code blocks
    cleanText = cleanText.replace(/```json\s*/g, "").replace(/```\s*/g, "");

    // Find JSON array or object
    const arrayMatch = cleanText.match(/\[[\s\S]*\]/);
    if (arrayMatch) {
      return arrayMatch[0];
    }

    const objectMatch = cleanText.match(/\{[\s\S]*\}/);
    if (objectMatch) {
      return objectMatch[0];
    }

    // If no JSON found, return original text
    return cleanText;
  }
}
