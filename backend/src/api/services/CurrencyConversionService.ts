/**
 * Currency Conversion Service
 * Handles currency conversion for dashboard analytics
 */

export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  lastUpdated: Date;
}

export interface CurrencyConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  targetCurrency: string;
  exchangeRate: number;
  conversionDate: Date;
}

export class CurrencyConversionService {
  private static instance: CurrencyConversionService;
  private exchangeRates: Map<string, ExchangeRate> = new Map();
  private baseCurrency = "USD"; // Default base currency for dashboard
  private lastUpdateTime: Date | null = null;
  private updateInterval = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  // Fallback exchange rates (updated periodically)
  private fallbackRates: Record<string, number> = {
    "EUR": 0.85,
    "GBP": 0.73,
    "JPY": 110.0,
    "CAD": 1.25,
    "AUD": 1.35,
    "CHF": 0.92,
    "CNY": 6.45,
    "INR": 74.5,
    "BRL": 5.2,
    "MXN": 20.1,
    "SGD": 1.35,
    "HKD": 7.8,
    "NOK": 8.5,
    "SEK": 8.7,
    "DKK": 6.3,
    "PLN": 3.9,
    "CZK": 21.5,
    "HUF": 295.0,
    "RON": 4.1,
    "BGN": 1.66,
    "HRK": 6.4,
    "RUB": 73.5,
    "TRY": 8.5,
    "ZAR": 14.2,
    "KRW": 1180.0,
    "THB": 31.5,
    "MYR": 4.1,
    "IDR": 14250.0,
    "PHP": 49.5,
    "VND": 23000.0
  };

  private constructor() {
    this.initializeFallbackRates();
  }

  public static getInstance(): CurrencyConversionService {
    if (!CurrencyConversionService.instance) {
      CurrencyConversionService.instance = new CurrencyConversionService();
    }
    return CurrencyConversionService.instance;
  }

  /**
   * Initialize fallback exchange rates
   */
  private initializeFallbackRates(): void {
    const now = new Date();
    
    // Add USD to USD rate
    this.exchangeRates.set("USD-USD", {
      from: "USD",
      to: "USD",
      rate: 1.0,
      lastUpdated: now
    });

    // Add fallback rates
    Object.entries(this.fallbackRates).forEach(([currency, rate]) => {
      // From currency to USD
      this.exchangeRates.set(`${currency}-USD`, {
        from: currency,
        to: "USD",
        rate: 1 / rate,
        lastUpdated: now
      });

      // From USD to currency
      this.exchangeRates.set(`USD-${currency}`, {
        from: "USD",
        to: currency,
        rate: rate,
        lastUpdated: now
      });
    });

    this.lastUpdateTime = now;
  }

  /**
   * Parse currency value in format "CURRENCY:AMOUNT"
   */
  public parseCurrencyValue(currencyValue: string | null | undefined): {
    currency: string;
    amount: number;
  } {
    if (!currencyValue || typeof currencyValue !== "string") {
      return { currency: "USD", amount: 0 };
    }

    // Handle format like "USD:58926.00"
    if (currencyValue.includes(":")) {
      const parts = currencyValue.split(":");
      if (parts.length === 2) {
        const currency = parts[0].toUpperCase();
        const amount = parseFloat(parts[1]);
        return {
          currency: currency || "USD",
          amount: isNaN(amount) ? 0 : amount
        };
      }
    }

    // Handle direct number format (assume USD)
    const amount = parseFloat(currencyValue);
    return {
      currency: "USD",
      amount: isNaN(amount) ? 0 : amount
    };
  }

  /**
   * Convert amount from one currency to another
   */
  public convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string = this.baseCurrency
  ): CurrencyConversionResult {
    const from = fromCurrency.toUpperCase();
    const to = toCurrency.toUpperCase();

    // If same currency, no conversion needed
    if (from === to) {
      return {
        originalAmount: amount,
        originalCurrency: from,
        convertedAmount: amount,
        targetCurrency: to,
        exchangeRate: 1.0,
        conversionDate: new Date()
      };
    }

    // Get exchange rate
    const rateKey = `${from}-${to}`;
    let exchangeRate = this.exchangeRates.get(rateKey);

    // If direct rate not found, try reverse rate
    if (!exchangeRate) {
      const reverseKey = `${to}-${from}`;
      const reverseRate = this.exchangeRates.get(reverseKey);
      if (reverseRate) {
        exchangeRate = {
          from: from,
          to: to,
          rate: 1 / reverseRate.rate,
          lastUpdated: reverseRate.lastUpdated
        };
      }
    }

    // If still no rate found, use fallback
    if (!exchangeRate) {
      console.warn(`Exchange rate not found for ${from} to ${to}, using fallback`);
      const fallbackRate = this.getFallbackRate(from, to);
      exchangeRate = {
        from: from,
        to: to,
        rate: fallbackRate,
        lastUpdated: new Date()
      };
    }

    const convertedAmount = amount * exchangeRate.rate;

    return {
      originalAmount: amount,
      originalCurrency: from,
      convertedAmount: convertedAmount,
      targetCurrency: to,
      exchangeRate: exchangeRate.rate,
      conversionDate: new Date()
    };
  }

  /**
   * Convert currency value string to base currency
   */
  public convertToBaseCurrency(currencyValue: string | null | undefined): number {
    const parsed = this.parseCurrencyValue(currencyValue);
    if (parsed.amount === 0) return 0;

    const conversion = this.convertCurrency(parsed.amount, parsed.currency, this.baseCurrency);
    return conversion.convertedAmount;
  }

  /**
   * Get fallback exchange rate
   */
  private getFallbackRate(from: string, to: string): number {
    if (from === to) return 1.0;
    
    if (from === "USD" && this.fallbackRates[to]) {
      return this.fallbackRates[to];
    }
    
    if (to === "USD" && this.fallbackRates[from]) {
      return 1 / this.fallbackRates[from];
    }
    
    // Cross-currency conversion via USD
    if (this.fallbackRates[from] && this.fallbackRates[to]) {
      return this.fallbackRates[to] / this.fallbackRates[from];
    }
    
    // Default fallback
    console.warn(`No exchange rate available for ${from} to ${to}, using 1.0`);
    return 1.0;
  }

  /**
   * Set base currency for dashboard
   */
  public setBaseCurrency(currency: string): void {
    this.baseCurrency = currency.toUpperCase();
  }

  /**
   * Get current base currency
   */
  public getBaseCurrency(): string {
    return this.baseCurrency;
  }

  /**
   * Update exchange rates (placeholder for future API integration)
   */
  public async updateExchangeRates(): Promise<void> {
    // TODO: Integrate with external exchange rate API
    // For now, we'll use the fallback rates
    console.log("Exchange rates updated with fallback values");
    this.lastUpdateTime = new Date();
  }

  /**
   * Check if exchange rates need updating
   */
  public needsUpdate(): boolean {
    if (!this.lastUpdateTime) return true;
    const now = new Date();
    return (now.getTime() - this.lastUpdateTime.getTime()) > this.updateInterval;
  }
}
