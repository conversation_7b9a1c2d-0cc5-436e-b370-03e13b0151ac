import { PrismaClient, IntegrityConfiguration } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import {
  IntegrityConfigurationRepository,
  CreateIntegrityConfigurationInput,
  UpdateIntegrityConfigurationInput
} from "../../infrastructure/repositories/IntegrityConfigurationRepository";
import * as fs from 'fs';
import * as path from 'path';

export interface IntegrityClause {
  id: string;
  name: string;
  description: string;
  category: string;
  possibleValues: IntegrityClauseValue[];
}

export interface IntegrityClauseValue {
  value: string;
  weightage: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  description: string;
}

export interface IntegrityConfigurationData {
  configurationName: string;
  description?: string;
  version?: string;
  clauses: IntegrityClause[];
}

/**
 * Service for managing integrity configurations
 */
export class IntegrityConfigurationService {
  private repository: IntegrityConfigurationRepository;

  constructor(prisma: PrismaClient) {
    this.repository = new IntegrityConfigurationRepository(prisma);
  }

  /**
   * Get default integrity template
   */
  getDefaultTemplate(): IntegrityConfigurationData {
    try {
      const templatePath = path.join(__dirname, '../../data/defaultIntegrityTemplate.json');
      const templateData = fs.readFileSync(templatePath, 'utf8');
      return JSON.parse(templateData);
    } catch (error) {
      logger.error("Error loading default integrity template:", error);
      throw new Error("Failed to load default integrity template");
    }
  }

  /**
   * Create integrity configuration for user
   */
  async createConfiguration(
    userId: string,
    tenantId: string,
    configurationData: IntegrityConfigurationData
  ): Promise<IntegrityConfiguration> {
    try {
      // Validate configuration data
      this.validateConfigurationData(configurationData);

      // Delete all existing configurations for this user (since we only allow one)
      await this.repository.deleteAllForUser(userId, tenantId);

      const input: CreateIntegrityConfigurationInput = {
        userId,
        tenantId,
        configurationName: configurationData.configurationName,
        clauses: configurationData,
        isActive: true, // New configurations are active by default
      };

      return await this.repository.create(input);
    } catch (error) {
      logger.error("Error creating integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Get user's active configuration or create default if none exists
   */
  async getActiveConfiguration(userId: string, tenantId: string): Promise<IntegrityConfiguration> {
    try {
      // Try to get user's active configuration
      let activeConfig = await this.repository.findActiveByUser(userId, tenantId);

      if (!activeConfig) {
        // Check if there's a tenant default
        const tenantDefault = await this.repository.findDefaultByTenant(tenantId);

        if (tenantDefault) {
          // Clone tenant default for this user
          const defaultTemplate = tenantDefault.clauses as unknown as IntegrityConfigurationData;
          activeConfig = await this.createConfiguration(userId, tenantId, {
            ...defaultTemplate,
            configurationName: `${defaultTemplate.configurationName} (Copy)`,
          });
        } else {
          // Create from system default template
          const defaultTemplate = this.getDefaultTemplate();
          activeConfig = await this.createConfiguration(userId, tenantId, defaultTemplate);
        }
      }

      return activeConfig;
    } catch (error) {
      logger.error("Error getting active integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Get all configurations for user
   */
  async getUserConfigurations(userId: string, tenantId: string): Promise<IntegrityConfiguration[]> {
    try {
      return await this.repository.findByUser(userId, tenantId);
    } catch (error) {
      logger.error("Error getting user integrity configurations:", error);
      throw error;
    }
  }

  /**
   * Update configuration
   */
  async updateConfiguration(
    configId: string,
    userId: string,
    tenantId: string,
    configurationData: IntegrityConfigurationData
  ): Promise<IntegrityConfiguration> {
    try {
      // Check if new name conflicts with existing configurations
      if (configurationData.configurationName) {
        const exists = await this.repository.existsByName(
          userId,
          tenantId,
          configurationData.configurationName,
          configId
        );

        if (exists) {
          throw new Error(`Configuration with name "${configurationData.configurationName}" already exists`);
        }
      }

      // Validate configuration data
      this.validateConfigurationData(configurationData);

      const updateInput: UpdateIntegrityConfigurationInput = {
        configurationName: configurationData.configurationName,
        clauses: configurationData,
      };

      return await this.repository.update(configId, tenantId, updateInput);
    } catch (error) {
      logger.error("Error updating integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Delete configuration
   */
  async deleteConfiguration(configId: string, tenantId: string): Promise<void> {
    try {
      await this.repository.delete(configId, tenantId);
    } catch (error) {
      logger.error("Error deleting integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Set configuration as active
   */
  async setActiveConfiguration(
    configId: string,
    userId: string,
    tenantId: string
  ): Promise<IntegrityConfiguration> {
    try {
      return await this.repository.setActive(configId, userId, tenantId);
    } catch (error) {
      logger.error("Error setting active integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Delete user's configuration to force reset to template
   */
  async deleteUserConfiguration(userId: string, tenantId: string): Promise<void> {
    try {
      await this.repository.deleteAllForUser(userId, tenantId);
    } catch (error) {
      logger.error("Error deleting user integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Create default configuration for tenant
   */
  async createTenantDefault(tenantId: string, adminUserId: string): Promise<IntegrityConfiguration> {
    try {
      const defaultTemplate = this.getDefaultTemplate();

      const input: CreateIntegrityConfigurationInput = {
        userId: adminUserId,
        tenantId,
        configurationName: "Tenant Default",
        clauses: defaultTemplate,
        isActive: true,
        isDefault: true,
      };

      return await this.repository.create(input);
    } catch (error) {
      logger.error("Error creating tenant default integrity configuration:", error);
      throw error;
    }
  }

  /**
   * Validate configuration data structure
   */
  private validateConfigurationData(configData: IntegrityConfigurationData): void {
    if (!configData.configurationName || configData.configurationName.trim() === '') {
      throw new Error("Configuration name is required");
    }

    if (!configData.clauses || !Array.isArray(configData.clauses)) {
      throw new Error("Clauses array is required");
    }

    if (configData.clauses.length === 0) {
      throw new Error("At least one clause is required");
    }

    // Validate each clause
    for (const clause of configData.clauses) {
      if (!clause.id || !clause.name) {
        throw new Error("Each clause must have an id and name");
      }

      if (!clause.possibleValues || !Array.isArray(clause.possibleValues)) {
        throw new Error(`Clause "${clause.name}" must have possibleValues array`);
      }

      if (clause.possibleValues.length === 0) {
        throw new Error(`Clause "${clause.name}" must have at least one possible value`);
      }

      // Validate possible values
      for (const value of clause.possibleValues) {
        if (!value.value || typeof value.weightage !== 'number') {
          throw new Error(`Invalid possible value in clause "${clause.name}"`);
        }

        if (!['Low', 'Medium', 'High'].includes(value.riskLevel)) {
          throw new Error(`Invalid risk level in clause "${clause.name}"`);
        }
      }
    }

    // Check for duplicate clause IDs
    const clauseIds = configData.clauses.map(c => c.id);
    const uniqueIds = new Set(clauseIds);
    if (clauseIds.length !== uniqueIds.size) {
      throw new Error("Duplicate clause IDs found");
    }
  }

  /**
   * Calculate maximum possible score for a configuration
   */
  calculateMaxScore(configData: IntegrityConfigurationData): number {
    return configData.clauses.reduce((total, clause) => {
      const maxWeightage = Math.max(...clause.possibleValues.map(v => v.weightage));
      return total + maxWeightage;
    }, 0);
  }
}
