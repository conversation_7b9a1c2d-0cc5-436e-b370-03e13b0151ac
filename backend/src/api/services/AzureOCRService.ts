/**
 * Azure Computer Vision OCR Service
 * Implements Microsoft Azure Read API for document text extraction
 */

import axios, { AxiosResponse } from 'axios';
import { logger } from '../../infrastructure/logging/logger';

export interface OCRResult {
  text: string;
  confidence: number;
  pageCount: number;
  processingTimeMs: number;
  metadata: {
    pages: Array<{
      pageNumber: number;
      lines: Array<{
        text: string;
        boundingBox: number[];
      }>;
    }>;
  };
}

export interface OCRError {
  error: string;
  details?: string;
}

export class AzureOCRService {
  private readonly endpoint: string;
  private readonly subscriptionKey: string;
  private readonly maxRetries: number = 30; // Maximum polling attempts
  private readonly retryDelayMs: number = 2000; // 2 seconds between polls

  constructor() {
    this.endpoint = process.env.AZURE_COMPUTER_VISION_ENDPOINT!;
    this.subscriptionKey = process.env.AZURE_COMPUTER_VISION_KEY!;

    if (!this.endpoint || !this.subscriptionKey) {
      throw new Error(
        'Azure Computer Vision credentials not configured. Please set AZURE_COMPUTER_VISION_ENDPOINT and AZURE_COMPUTER_VISION_KEY environment variables.'
      );
    }

    // Ensure endpoint ends with /
    this.endpoint = this.endpoint.endsWith('/') ? this.endpoint : `${this.endpoint}/`;

    // logger.info(`Azure OCR Service initialized with endpoint: ${this.endpoint}`);
  }

  /**
   * Extract text from document using Azure Computer Vision Read API
   * @param documentBuffer - Document buffer (PDF, image, etc.)
   * @param fileName - Original file name for logging
   * @returns OCR result with extracted text and metadata
   */
  async extractTextFromDocument(
    documentBuffer: Buffer,
    fileName: string
  ): Promise<OCRResult> {
    const startTime = Date.now();

    try {
      logger.info(`Starting OCR processing for document: ${fileName}`);

      // Step 1: Submit document for OCR analysis
      const operationUrl = await this.submitDocumentForOCR(documentBuffer, fileName);

      // Step 2: Poll for results
      const ocrResponse = await this.pollForOCRResults(operationUrl, fileName);

      // Step 3: Extract and format text
      const result = this.extractTextFromResponse(ocrResponse, startTime);

      logger.info(
        `OCR processing completed for ${fileName}. Extracted ${result.text.length} characters from ${result.pageCount} pages in ${result.processingTimeMs}ms`
      );

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`OCR processing failed for ${fileName} after ${processingTime}ms:`, error);
      throw error;
    }
  }

  /**
   * Submit document to Azure Read API for OCR processing
   * @param documentBuffer - Document buffer
   * @param fileName - File name for logging
   * @returns Operation URL for polling results
   */
  private async submitDocumentForOCR(
    documentBuffer: Buffer,
    fileName: string
  ): Promise<string> {
    try {
      const url = `${this.endpoint}vision/v3.2/read/analyze`;

      logger.debug(`Submitting document ${fileName} to Azure OCR API: ${url}`);

      const response: AxiosResponse = await axios.post(url, documentBuffer, {
        headers: {
          'Ocp-Apim-Subscription-Key': this.subscriptionKey,
          'Content-Type': 'application/octet-stream',
        },
        timeout: 60000, // 60 seconds timeout for upload
      });

      // Azure returns 202 Accepted with operation-location header
      if (response.status !== 202) {
        throw new Error(`Unexpected response status: ${response.status}`);
      }

      const operationUrl = response.headers['operation-location'];
      if (!operationUrl) {
        throw new Error('No operation-location header received from Azure OCR API');
      }

      logger.debug(`OCR operation submitted successfully for ${fileName}. Operation URL: ${operationUrl}`);
      return operationUrl;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || error.message;
        throw new Error(`Failed to submit document for OCR: ${errorMessage}`);
      }
      throw error;
    }
  }

  /**
   * Poll Azure Read API for OCR results
   * @param operationUrl - Operation URL from submit response
   * @param fileName - File name for logging
   * @returns OCR analysis result
   */
  private async pollForOCRResults(operationUrl: string, fileName: string): Promise<any> {
    let attempts = 0;

    while (attempts < this.maxRetries) {
      try {
        logger.debug(`Polling OCR results for ${fileName} (attempt ${attempts + 1}/${this.maxRetries})`);

        const response: AxiosResponse = await axios.get(operationUrl, {
          headers: {
            'Ocp-Apim-Subscription-Key': this.subscriptionKey,
          },
          timeout: 30000, // 30 seconds timeout for polling
        });

        const result = response.data;

        if (result.status === 'succeeded') {
          logger.debug(`OCR processing succeeded for ${fileName} after ${attempts + 1} attempts`);
          return result;
        } else if (result.status === 'failed') {
          throw new Error(`OCR processing failed: ${result.message || 'Unknown error'}`);
        } else if (result.status === 'running' || result.status === 'notStarted') {
          // Continue polling
          attempts++;
          if (attempts < this.maxRetries) {
            await this.delay(this.retryDelayMs);
          }
        } else {
          throw new Error(`Unexpected OCR status: ${result.status}`);
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          const errorMessage = error.response?.data?.message || error.message;
          throw new Error(`Failed to poll OCR results: ${errorMessage}`);
        }
        throw error;
      }
    }

    throw new Error(`OCR processing timed out after ${this.maxRetries} attempts (${this.maxRetries * this.retryDelayMs / 1000} seconds)`);
  }

  /**
   * Extract text and metadata from Azure OCR response
   * @param ocrResponse - Azure OCR API response
   * @param startTime - Processing start time for duration calculation
   * @returns Formatted OCR result
   */
  private extractTextFromResponse(ocrResponse: any, startTime: number): OCRResult {
    const processingTimeMs = Date.now() - startTime;
    const readResults = ocrResponse.analyzeResult?.readResults || [];

    if (!readResults.length) {
      return {
        text: '',
        confidence: 0,
        pageCount: 0,
        processingTimeMs,
        metadata: { pages: [] },
      };
    }

    let allText = '';
    let totalConfidence = 0;
    let lineCount = 0;
    const pages: any[] = [];

    // Process each page
    readResults.forEach((page: any, pageIndex: number) => {
      const pageLines: any[] = [];

      if (page.lines && page.lines.length > 0) {
        page.lines.forEach((line: any) => {
          if (line.text) {
            allText += line.text + '\n';

            // Calculate confidence (Azure doesn't provide line-level confidence in v3.2)
            // We'll use a default confidence of 0.85 for successful extractions
            totalConfidence += 0.85;
            lineCount++;

            pageLines.push({
              text: line.text,
              boundingBox: line.boundingBox || [],
            });
          }
        });
      }

      pages.push({
        pageNumber: pageIndex + 1,
        lines: pageLines,
      });
    });

    // Calculate average confidence
    const averageConfidence = lineCount > 0 ? totalConfidence / lineCount : 0;

    return {
      text: allText.trim(),
      confidence: averageConfidence,
      pageCount: readResults.length,
      processingTimeMs,
      metadata: { pages },
    };
  }

  /**
   * Utility method to add delay between polling attempts
   * @param ms - Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate if OCR service is properly configured
   * @returns True if service is configured correctly
   */
  isConfigured(): boolean {
    return !!(this.endpoint && this.subscriptionKey);
  }


}
