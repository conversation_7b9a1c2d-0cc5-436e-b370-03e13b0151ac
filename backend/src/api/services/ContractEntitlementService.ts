/**
 * Contract Entitlement Service
 * Aggregates entitlement data from analysis fields and supplier fields
 */

import { logger } from "../../infrastructure/logging/logger";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { PrismaClient } from "@prisma/client";

export interface ContractValueCard {
  value: string;
  currency: string;
}

export interface ContractTermCard {
  term: string;
}

export interface RenewalNoticeCard {
  noticePeriod: string;
  autoRenewal: string;
}

export interface DiscountCard {
  discountLevel: string;
  discountPercentage: string;
  discountFieldKey: string;
}

export interface SKUItem {
  [key: string]: string;
}

export interface UseRightsLegal {
  limitations: string;
  includedRights: string;
  metricDefinition: string;
  governingAgreement: string;
  supportTerms: string;
  entitledEntity: string;
  // Special fields from mapping.json
  territorialScope: string;
  thirdPartyUsage: string;
  licenceMobility: string;
  ipOwnership: string;
  rampedPricingConcession: string;
  warrantyDisclaimer: string;
}

export interface YearWisePurchasing {
  [year: string]: SKUItem[];
}

export interface ContractEntitlements {
  contractValue: ContractValueCard;
  contractTerm: ContractTermCard;
  renewalNotice: RenewalNoticeCard;
  discount: DiscountCard;
  skuBreakdown: SKUItem[];
  yearWisePurchasing: YearWisePurchasing;
  useRightsLegal: UseRightsLegal;
}

export class ContractEntitlementService {
  private contractExtractionRepository: ContractExtractionRepository;

  constructor(prisma: PrismaClient) {
    this.contractExtractionRepository = new ContractExtractionRepository(prisma);
  }

  /**
   * Get comprehensive entitlement data for a contract
   */
  async getContractEntitlements(
    contractId: string,
    tenantId: string
  ): Promise<ContractEntitlements> {
    try {
      // Get contract extraction data
      const extraction = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );

      if (!extraction) {
        logger.warn(`No extraction data found for contract ${contractId}`);
        return this.getEmptyEntitlements();
      }

      const analysisFields = extraction.analysisFields as any;
      const fixedFields = extraction.fixedFields as any;
      const dynamicFields = extraction.dynamicFields as any;
      const specialFields = extraction.specialFields as any;

      return {
        contractValue: this.extractContractValue(analysisFields, fixedFields),
        contractTerm: this.extractContractTerm(analysisFields, fixedFields),
        renewalNotice: this.extractRenewalNotice(fixedFields),
        discount: this.extractDiscount(analysisFields, dynamicFields),
        skuBreakdown: this.extractSKUData(analysisFields),
        yearWisePurchasing: this.extractYearWisePurchasing(analysisFields),
        useRightsLegal: this.extractLegalTerms(analysisFields, specialFields),
      };
    } catch (error) {
      logger.error("Error getting contract entitlements:", error);
      throw new Error("Failed to retrieve contract entitlements");
    }
  }

  /**
   * Extract contract value card data
   */
  private extractContractValue(analysisFields: any, fixedFields: any): ContractValueCard {
    // Use fixed fields primarily
    let value = this.getFieldValue(fixedFields, "total_amount");

    // Fallback to analysis fields if not in fixed fields
    if (!value || value === "N/A") {
      value = this.getFieldValue(analysisFields, "license_value");
    }

    const currency = this.getFieldValue(fixedFields, "currency") || "USD";

    return {
      value: value || "N/A",
      currency,
    };
  }

  /**
   * Extract contract term card data
   */
  private extractContractTerm(analysisFields: any, fixedFields: any): ContractTermCard {
    // Use fixed fields primarily
    let term = this.getFieldValue(fixedFields, "contract_term");

    // Fallback to analysis fields if not in fixed fields
    if (!term || term === "N/A") {
      term = this.getFieldValue(analysisFields, "term");
    }

    return {
      term: term || "N/A",
    };
  }

  /**
   * Extract renewal notice card data
   */
  private extractRenewalNotice(fixedFields: any): RenewalNoticeCard {
    const noticePeriod = this.getFieldValue(fixedFields, "renewal_notice_period") || "N/A";
    const autoRenewal = this.getFieldValue(fixedFields, "auto_renewal") || "N/A";

    return {
      noticePeriod,
      autoRenewal,
    };
  }

  /**
   * Extract discount card data
   */
  private extractDiscount(analysisFields: any, dynamicFields: any): DiscountCard {
    // Initialize discount data
    let discountLevel = "N/A";
    let discountPercentage = "N/A";
    let discountFieldKey = "N/A";

    // Check dynamic fields for discount information
    if (dynamicFields) {
      const financialFields = dynamicFields["Commercial terms"] || {};
      const generalFields = dynamicFields["General"] || {};

      // Look for discount-related fields in dynamic fields
      for (const [key, field] of Object.entries({ ...financialFields, ...generalFields })) {
        if (key.toLowerCase().includes("discount") && field && typeof field === "object") {
          const fieldData = field as any;
          if (fieldData.value && fieldData.value !== "N/A") {
            discountPercentage = fieldData.value;
            discountFieldKey = key;

            // If the field has a description, use it as discount level
            if (fieldData.description && fieldData.description !== "N/A") {
              discountLevel = fieldData.description;
            }
            break;
          }
        }
      }
    }

    return {
      discountLevel,
      discountPercentage,
      discountFieldKey,
    };
  }

  /**
   * Extract SKU-level data from purchasing data (new object format or legacy array)
   */
  private extractSKUData(analysisFields: any): SKUItem[] {
    const purchasing = analysisFields?.purchasing;
    if (!purchasing) {
      return [];
    }

    let allItems: any[] = [];

    if (Array.isArray(purchasing)) {
      // Legacy array format
      allItems = purchasing;
    } else if (typeof purchasing === 'object') {
      // New object format - flatten all years
      Object.entries(purchasing).forEach(([year, items]) => {
        if (Array.isArray(items)) {
          items.forEach((item: any) => {
            // Add year information to each item
            allItems.push({
              ...item,
              year: year // Add year as a field
            });
          });
        }
      });
    }

    // Return the items, ensuring all values are strings
    return allItems.map((item: any) => {
      const skuItem: SKUItem = {};

      // Convert all properties to strings and sort alphabetically
      Object.keys(item)
        .sort()
        .forEach(key => {
          skuItem[key] = String(item[key] || "N/A");
        });

      return skuItem;
    });
  }

  /**
   * Extract year-wise purchasing data from analysis fields
   */
  private extractYearWisePurchasing(analysisFields: any): YearWisePurchasing {
    const purchasing = analysisFields?.purchasing;
    if (!purchasing) {
      return {};
    }

    const yearWiseData: YearWisePurchasing = {};

    if (Array.isArray(purchasing)) {
      // Legacy array format - treat as TOTAL
      yearWiseData["TOTAL"] = purchasing.map((item: any) => {
        const skuItem: SKUItem = {};
        Object.keys(item)
          .sort()
          .forEach(key => {
            skuItem[key] = String(item[key] || "N/A");
          });
        return skuItem;
      });
    } else if (typeof purchasing === 'object') {
      // New object format - process by year
      Object.entries(purchasing).forEach(([year, items]) => {
        if (Array.isArray(items)) {
          yearWiseData[year] = items.map((item: any) => {
            const skuItem: SKUItem = {};
            Object.keys(item)
              .sort()
              .forEach(key => {
                skuItem[key] = String(item[key] || "N/A");
              });
            return skuItem;
          });
        }
      });
    }

    return yearWiseData;
  }

  /**
   * Extract legal terms from analysis fields and special fields
   */
  private extractLegalTerms(analysisFields: any, specialFields: any): UseRightsLegal {
    return {
      limitations: this.getFieldValue(analysisFields, "limitations") || "N/A",
      includedRights: this.getFieldValue(analysisFields, "included_rights") || "N/A",
      metricDefinition: this.getFieldValue(analysisFields, "metric_definition") || "N/A",
      governingAgreement: this.getFieldValue(analysisFields, "governing_agreement") || "N/A",
      supportTerms: this.getFieldValue(analysisFields, "support_contract_number") || "N/A",
      entitledEntity: this.getFieldValue(analysisFields, "entitled_entity") || "N/A",
      // Extract special fields using actual field names from data
      territorialScope: this.getSpecialFieldValue(specialFields, "Use rights & restrictions", "territorial_scope") || "N/A",
      thirdPartyUsage: this.getSpecialFieldValue(specialFields, "Use rights & restrictions", "third_party_usage") || "N/A",
      licenceMobility: this.getSpecialFieldValue(specialFields, "Use rights & restrictions", "licence_mobility_sa") || "N/A",
      ipOwnership: this.getSpecialFieldValue(specialFields, "Legal terms", "ip_ownership") || "N/A",
      rampedPricingConcession: this.getSpecialFieldValue(specialFields, "Commercial terms", "ramped_pricing_concession") || "N/A",
      warrantyDisclaimer: this.getSpecialFieldValue(specialFields, "Legal terms", "warranty_disclaimer") || "N/A",
    };
  }



  /**
   * Helper to get field value from analysis fields
   */
  private getFieldValue(analysisFields: any, fieldName: string): string {
    const field = analysisFields?.[fieldName];
    if (field && typeof field === "object" && field.value !== undefined) {
      return field.value || "N/A";
    }
    return field || "N/A";
  }

  /**
   * Helper to get special field value from special fields structure
   */
  private getSpecialFieldValue(specialFields: any, category: string, fieldName: string): string {
    if (!specialFields) return "N/A";

    // Check all suppliers for the field
    for (const supplierName of Object.keys(specialFields)) {
      const supplierData = specialFields[supplierName];
      if (supplierData && supplierData[category] && supplierData[category][fieldName]) {
        const field = supplierData[category][fieldName];
        if (field && typeof field === "object" && field.value !== undefined) {
          return field.value || "N/A";
        }
        return field || "N/A";
      }
    }

    return "N/A";
  }

  /**
   * Helper to get supplier field value
   */
  private getSupplierFieldValue(
    supplierFields: any,
    category: string,
    fieldName: string
  ): string {
    const categoryFields = supplierFields?.[category];
    const field = categoryFields?.[fieldName];

    if (field && typeof field === "object" && field.value !== undefined) {
      return field.value || "N/A";
    }
    return field || "N/A";
  }

  /**
   * Extract currency from currency:amount format
   */
  private extractCurrency(value: string): string | null {
    const match = value.match(/^([A-Z]{3}):/);
    return match ? match[1] : null;
  }

  /**
   * Extract amount from currency:amount format
   */
  private extractCurrencyAmount(value: string): number {
    const match = value.match(/:(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  }

  /**
   * Return empty entitlements structure
   */
  private getEmptyEntitlements(): ContractEntitlements {
    return {
      contractValue: {
        value: "N/A",
        currency: "USD",
      },
      contractTerm: {
        term: "N/A",
      },
      renewalNotice: {
        noticePeriod: "N/A",
        autoRenewal: "N/A",
      },
      discount: {
        discountLevel: "N/A",
        discountPercentage: "N/A",
        discountFieldKey: "N/A",
      },
      skuBreakdown: [],
      yearWisePurchasing: {},
      useRightsLegal: {
        limitations: "N/A",
        includedRights: "N/A",
        metricDefinition: "N/A",
        governingAgreement: "N/A",
        supportTerms: "N/A",
        entitledEntity: "N/A",
        territorialScope: "N/A",
        thirdPartyUsage: "N/A",
        licenceMobility: "N/A",
        ipOwnership: "N/A",
        rampedPricingConcession: "N/A",
        warrantyDisclaimer: "N/A",
      },
    };
  }
}
