/**
 * Reporting Migration Service
 * Handles migration and auto-population of reporting relationships for existing contracts
 */

import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { ContractReportingService } from "./ContractReportingService";
import { ContractHierarchyService } from "./ContractHierarchyService";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";

export interface MigrationResult {
  totalProcessed: number;
  successfulUpdates: number;
  skipped: number;
  errors: number;
  details: {
    contractId: string;
    status: "success" | "skipped" | "error";
    reportingTo?: string;
    reason?: string;
  }[];
}

export class ReportingMigrationService {
  private contractExtractionRepository: ContractExtractionRepository;
  private contractReportingService: ContractReportingService;
  private contractHierarchyService: ContractHierarchyService;

  constructor(prisma: PrismaClient) {
    this.contractExtractionRepository = new ContractExtractionRepository(prisma);
    this.contractHierarchyService = new ContractHierarchyService();
    this.contractReportingService = new ContractReportingService(this.contractHierarchyService);
  }

  /**
   * Migrates all existing contracts to populate reportingTo field
   */
  async migrateAllContracts(): Promise<MigrationResult> {
    const result: MigrationResult = {
      totalProcessed: 0,
      successfulUpdates: 0,
      skipped: 0,
      errors: 0,
      details: [],
    };

    try {
      logger.info("Starting reporting relationship migration for all contracts");

      // Get all tenants and process them separately
      const tenants = await this.getAllTenants();
      
      for (const tenantId of tenants) {
        const tenantResult = await this.migrateTenantContracts(tenantId);
        
        // Merge results
        result.totalProcessed += tenantResult.totalProcessed;
        result.successfulUpdates += tenantResult.successfulUpdates;
        result.skipped += tenantResult.skipped;
        result.errors += tenantResult.errors;
        result.details.push(...tenantResult.details);
      }

      logger.info("Completed reporting relationship migration", {
        totalProcessed: result.totalProcessed,
        successfulUpdates: result.successfulUpdates,
        skipped: result.skipped,
        errors: result.errors,
      });

      return result;

    } catch (error) {
      logger.error("Error during reporting relationship migration:", error);
      throw new Error(`Migration failed: ${(error as Error).message}`);
    }
  }

  /**
   * Migrates contracts for a specific tenant
   */
  async migrateTenantContracts(tenantId: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      totalProcessed: 0,
      successfulUpdates: 0,
      skipped: 0,
      errors: 0,
      details: [],
    };

    try {
      logger.info(`Starting migration for tenant: ${tenantId}`);

      // Get all contracts for this tenant
      const allContracts = await this.contractExtractionRepository.findByTenantId(tenantId);
      result.totalProcessed = allContracts.length;

      if (allContracts.length === 0) {
        logger.info(`No contracts found for tenant: ${tenantId}`);
        return result;
      }

      // Group contracts by provider for better relationship detection
      const contractsByProvider = this.groupContractsByProvider(allContracts);

      for (const [provider, contracts] of contractsByProvider.entries()) {
        logger.info(`Processing ${contracts.length} contracts for provider: ${provider}`);

        for (const contract of contracts) {
          try {
            // Skip if already has reportingTo set
            if (contract.reportingTo) {
              result.skipped++;
              result.details.push({
                contractId: contract.contractId,
                status: "skipped",
                reason: "Already has reporting relationship",
              });
              continue;
            }

            // Determine auto reporting relationship
            const reportingTo = await this.contractReportingService.determineAutoReporting(
              contract,
              contracts // Only consider contracts from same provider
            );

            if (reportingTo) {
              // Update the contract
              await this.contractExtractionRepository.updateReportingTo(
                contract.contractId,
                reportingTo
              );

              result.successfulUpdates++;
              result.details.push({
                contractId: contract.contractId,
                status: "success",
                reportingTo,
              });

              logger.debug(`Updated reporting relationship: ${contract.contractId} -> ${reportingTo}`);
            } else {
              result.skipped++;
              result.details.push({
                contractId: contract.contractId,
                status: "skipped",
                reason: "No suitable parent found",
              });
            }

          } catch (error) {
            result.errors++;
            result.details.push({
              contractId: contract.contractId,
              status: "error",
              reason: (error as Error).message,
            });

            logger.error(`Error processing contract ${contract.contractId}:`, error);
          }
        }
      }

      logger.info(`Completed migration for tenant ${tenantId}:`, {
        totalProcessed: result.totalProcessed,
        successfulUpdates: result.successfulUpdates,
        skipped: result.skipped,
        errors: result.errors,
      });

      return result;

    } catch (error) {
      logger.error(`Error migrating tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Groups contracts by provider for better relationship detection
   */
  private groupContractsByProvider(contracts: any[]): Map<string, any[]> {
    const groups = new Map<string, any[]>();

    for (const contract of contracts) {
      const provider = contract.fixedFields.provider?.value || "Unknown";
      
      if (!groups.has(provider)) {
        groups.set(provider, []);
      }
      
      groups.get(provider)!.push(contract);
    }

    return groups;
  }

  /**
   * Gets all unique tenant IDs from contract extractions
   */
  private async getAllTenants(): Promise<string[]> {
    try {
      // This would ideally be a direct query, but we'll use the repository
      // In a real implementation, you might want to add a method to get unique tenants
      const allContracts = await this.contractExtractionRepository.getByTenant("", 1, 10000);
      const tenantIds = new Set(allContracts.extractions.map(c => c.tenantId));
      return Array.from(tenantIds);
    } catch (error) {
      logger.error("Error getting tenant IDs:", error);
      return [];
    }
  }

  /**
   * Validates all reporting relationships for a tenant
   */
  async validateTenantReportingRelationships(tenantId: string): Promise<{
    valid: number;
    invalid: number;
    details: {
      contractId: string;
      isValid: boolean;
      reason?: string;
    }[];
  }> {
    const result = {
      valid: 0,
      invalid: 0,
      details: [] as any[],
    };

    try {
      const allContracts = await this.contractExtractionRepository.findByTenantId(tenantId);

      for (const contract of allContracts) {
        if (!contract.reportingTo) {
          // No reporting relationship to validate
          continue;
        }

        const validation = await this.contractReportingService.validateReportingRelationship(
          contract.contractId,
          contract.reportingTo,
          allContracts
        );

        if (validation.isValid) {
          result.valid++;
        } else {
          result.invalid++;
        }

        result.details.push({
          contractId: contract.contractId,
          isValid: validation.isValid,
          reason: validation.reason,
        });
      }

      return result;

    } catch (error) {
      logger.error("Error validating reporting relationships:", error);
      throw error;
    }
  }
}
