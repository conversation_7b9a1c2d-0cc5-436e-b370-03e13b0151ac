/**
 * Role Controller
 * Handles role management operations within an organization
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaService } from "../../infrastructure/services/PrismaService";
import { RoleService } from "../../infrastructure/services/RoleService";

export class RoleController {
  private prisma: PrismaClient;
  private roleService: RoleService;

  constructor(roleService: RoleService, prisma?: PrismaClient) {
    this.roleService = roleService;
    this.prisma = prisma || PrismaService.getInstance();
  }

  /**
   * Get all roles in the tenant
   * @param req Request
   * @param res Response
   */
  async getRoles(req: Request, res: Response): Promise<void> {
    try {
      // Get tenant ID from authenticated user
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      const roles = await this.roleService.getRolesByTenant(tenantId);
      res.status(200).json(roles);
    } catch (error) {
      logger.error("Get roles error:", { error });
      res.status(500).json({ error: "Failed to get roles" });
    }
  }

  /**
   * Get a role by ID
   * @param req Request
   * @param res Response
   */
  async getRole(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      const role = await this.roleService.getRoleById(id, tenantId);

      if (!role) {
        res.status(404).json({ error: "Role not found" });
        return;
      }

      res.status(200).json(role);
    } catch (error) {
      logger.error("Get role error:", { error });
      res.status(500).json({ error: "Failed to get role" });
    }
  }

  /**
   * Create a new role in the tenant
   * @param req Request
   * @param res Response
   */
  async createRole(req: Request, res: Response): Promise<void> {
    try {
      const { name, description, permissions, isDefault } = req.body;
      const tenantId = req.user?.tenantId;

      // Validate request
      if (!name || !permissions) {
        res.status(400).json({ error: "Name and permissions are required" });
        return;
      }

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if role with the same name already exists
      const existingRole = await this.prisma.role.findFirst({
        where: { name, tenantId },
      });

      if (existingRole) {
        res.status(409).json({ error: "Role with this name already exists" });
        return;
      }

      // Create role
      const role = await this.roleService.createRole({
        name,
        description,
        permissions,
        isDefault: isDefault || false,
        tenantId,
      });

      res.status(201).json({
        message: "Role created successfully",
        roleId: role.id,
      });
    } catch (error) {
      logger.error("Create role error:", { error });
      res.status(500).json({
        error: `Role creation failed: ${(error as Error).message}`,
      });
    }
  }

  /**
   * Update a role
   * @param req Request
   * @param res Response
   */
  async updateRole(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, description, permissions, isDefault } = req.body;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if role exists and belongs to the tenant
      const existingRole = await this.roleService.getRoleById(id, tenantId);

      if (!existingRole) {
        res.status(404).json({ error: "Role not found" });
        return;
      }

      // Update role
      const updatedRole = await this.roleService.updateRole(id, {
        name,
        description,
        permissions,
        isDefault,
      });

      res.status(200).json({
        message: "Role updated successfully",
        role: updatedRole,
      });
    } catch (error) {
      logger.error("Update role error:", { error });
      res.status(500).json({
        error: `Role update failed: ${(error as Error).message}`,
      });
    }
  }

  /**
   * Delete a role
   * @param req Request
   * @param res Response
   */
  async deleteRole(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if role exists and belongs to the tenant
      const existingRole = await this.roleService.getRoleById(id, tenantId);

      if (!existingRole) {
        res.status(404).json({ error: "Role not found" });
        return;
      }

      // Check if role is in use
      const usersWithRole = await this.prisma.tenantUser.count({
        where: { roleId: id },
      });

      if (usersWithRole > 0) {
        res.status(400).json({ 
          error: "Cannot delete role that is assigned to users",
          usersCount: usersWithRole
        });
        return;
      }

      // Delete role
      await this.roleService.deleteRole(id, tenantId);

      res.status(200).json({
        message: "Role deleted successfully",
      });
    } catch (error) {
      logger.error("Delete role error:", { error });
      res.status(500).json({
        error: `Role deletion failed: ${(error as Error).message}`,
      });
    }
  }
}
