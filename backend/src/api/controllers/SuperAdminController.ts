/**
 * Super Admin Controller
 * Handles super admin specific operations and platform-wide management
 */

import { Request, Response } from "express";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";

export class SuperAdminController {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get platform overview statistics
   */
  async getPlatformOverview(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Super admin platform overview request", {
        userId: req.user?.userId,
        role: req.user?.role,
      });

      // Get basic platform statistics
      const [
        totalTenants,
        totalUsers,
        totalContracts,
        activeTenants,
        activeUsers,
      ] = await Promise.all([
        this.prisma.tenant.count(),
        this.prisma.user.count(),
        this.prisma.contractExtraction.count(),
        this.prisma.tenant.count({ where: { status: "ACTIVE" } }),
        this.prisma.user.count({ where: { status: "ACTIVE" } }),
      ]);

      // Get recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const [recentTenants, recentUsers, recentContracts] = await Promise.all([
        this.prisma.tenant.count({
          where: { createdAt: { gte: thirtyDaysAgo } },
        }),
        this.prisma.user.count({
          where: { createdAt: { gte: thirtyDaysAgo } },
        }),
        this.prisma.contractExtraction.count({
          where: { createdAt: { gte: thirtyDaysAgo } },
        }),
      ]);

      const overview = {
        totals: {
          tenants: totalTenants,
          users: totalUsers,
          contracts: totalContracts,
        },
        active: {
          tenants: activeTenants,
          users: activeUsers,
        },
        recent: {
          tenants: recentTenants,
          users: recentUsers,
          contracts: recentContracts,
        },
        timestamp: new Date().toISOString(),
      };

      logger.info("Platform overview retrieved successfully", {
        userId: req.user?.userId,
        overview,
      });

      res.status(200).json(overview);
    } catch (error) {
      logger.error("Error getting platform overview:", { error });
      res.status(500).json({
        error: "Failed to get platform overview",
        details: (error as Error).message,
      });
    }
  }

  /**
   * Get all tenants with basic information
   */
  async getAllTenants(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Super admin get all tenants request", {
        userId: req.user?.userId,
        role: req.user?.role,
      });

      const tenants = await this.prisma.tenant.findMany({
        select: {
          id: true,
          name: true,
          tier: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              tenantUsers: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      // Get contract counts for each tenant
      const tenantsWithCounts = await Promise.all(
        tenants.map(async (tenant) => {
          const contractCount = await this.prisma.contractExtraction.count({
            where: { tenantId: tenant.id },
          });

          return {
            ...tenant,
            userCount: tenant._count.tenantUsers,
            contractCount,
          };
        })
      );

      logger.info("All tenants retrieved successfully", {
        userId: req.user?.userId,
        tenantCount: tenantsWithCounts.length,
      });

      res.status(200).json({
        tenants: tenantsWithCounts,
        total: tenantsWithCounts.length,
      });
    } catch (error) {
      logger.error("Error getting all tenants:", { error });
      res.status(500).json({
        error: "Failed to get tenants",
        details: (error as Error).message,
      });
    }
  }

  /**
   * Get all users across all tenants
   */
  async getAllUsers(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Super admin get all users request", {
        userId: req.user?.userId,
        role: req.user?.role,
      });

      const users = await this.prisma.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          status: true,
          createdAt: true,
          tenantUsers: {
            select: {
              tenant: {
                select: {
                  id: true,
                  name: true,
                  tier: true,
                },
              },
              tenantRole: true,
              isActive: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      logger.info("All users retrieved successfully", {
        userId: req.user?.userId,
        userCount: users.length,
      });

      res.status(200).json({
        users,
        total: users.length,
      });
    } catch (error) {
      logger.error("Error getting all users:", { error });
      res.status(500).json({
        error: "Failed to get users",
        details: (error as Error).message,
      });
    }
  }

  /**
   * Test endpoint to verify super admin authentication
   */
  async testAuth(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Super admin auth test", {
        userId: req.user?.userId,
        role: req.user?.role,
        tenantId: req.user?.tenantId,
        isSuperAdmin: req.user?.isSuperAdmin,
        permissions: req.user?.permissions,
      });

      res.status(200).json({
        message: "Super admin authentication successful!",
        user: {
          userId: req.user?.userId,
          role: req.user?.role,
          tenantId: req.user?.tenantId,
          isSuperAdmin: req.user?.isSuperAdmin,
          permissions: req.user?.permissions,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error("Error in super admin auth test:", { error });
      res.status(500).json({
        error: "Super admin auth test failed",
        details: (error as Error).message,
      });
    }
  }
}
