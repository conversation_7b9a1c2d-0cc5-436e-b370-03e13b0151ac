/**
 * Contract Controller
 * Handles HTTP requests for contract management
 */

import { Request, Response } from "express";
import { ContractService } from "../services/ContractService";
import {
  ContractImportService,
  ContractImportResult,
} from "../services/ContractImportService";
import { ContractAIService } from "../services/ContractAIService";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { ContractExtraction } from "../../domain/contracts/ContractExtraction";
import { ContractReportingService } from "../services/ContractReportingService";
import { ContractHierarchyService } from "../services/ContractHierarchyService";
import { ReportingMigrationService } from "../services/ReportingMigrationService";
import { IntegrityConfigurationService } from "../services/IntegrityConfigurationService";
import { IntegrityAnalysisService } from "../services/IntegrityAnalysisService";
import { ContractEntitlementService } from "../services/ContractEntitlementService";

import { importJobService } from "../services/ImportJobService";
import { AppError } from "../../infrastructure/middleware/errorHandler";
import { logger } from "../../infrastructure/logging/logger";
import {
  AgreementType,
  ContractStatus,
  SecurityClassification,
  PrismaClient,
} from "@prisma/client";
import { DocumentFormat } from "../../infrastructure/ai/interfaces";
import multer from "multer";
import path from "path";

/**
 * Controller for contract operations
 */
// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
    files: 20, // Allow up to 20 files at once
  },
  fileFilter: (_, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (
      ext === ".csv" ||
      ext === ".xlsx" ||
      ext === ".xls" ||
      ext === ".pdf" ||
      ext === ".docx" ||
      ext === ".doc" ||
      ext === ".txt" ||
      ext === ".rtf" ||
      ext === ".odt" ||
      ext === ".md" ||
      ext === ".html" ||
      ext === ".htm"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Supported formats: CSV, Excel, PDF, Word, TXT, RTF, ODT, Markdown, HTML"
        )
      );
    }
  },
});

// Helper function to get document format from file extension
function getDocumentFormat(fileName: string): DocumentFormat {
  const ext = path.extname(fileName).toLowerCase();

  switch (ext) {
    case ".pdf":
      return DocumentFormat.PDF;
    case ".docx":
    case ".doc":
      return DocumentFormat.DOCX;
    case ".txt":
      return DocumentFormat.TEXT;
    case ".rtf":
    case ".csv":
    case ".xlsx":
    case ".xls":
    default:
      // For formats not explicitly defined in DocumentFormat enum,
      // default to TEXT as a fallback
      return DocumentFormat.TEXT;
  }
}

export class ContractController {
  private contractService: ContractService;
  private contractImportService: ContractImportService;
  private contractAIService: ContractAIService;
  private contractExtractionRepository: ContractExtractionRepository;
  private contractReportingService: ContractReportingService;
  private contractHierarchyService: ContractHierarchyService;
  private reportingMigrationService: ReportingMigrationService;
  private integrityConfigurationService: IntegrityConfigurationService;
  private integrityAnalysisService: IntegrityAnalysisService;
  private contractEntitlementService: ContractEntitlementService;

  constructor(contractService: ContractService, prisma: PrismaClient) {
    this.contractService = contractService;
    this.contractImportService = new ContractImportService(
      contractService,
      prisma
    );

    // Initialize ContractAIService with PrismaClient
    this.contractAIService = new ContractAIService(prisma);

    // Initialize ContractExtractionRepository with PrismaClient
    this.contractExtractionRepository = new ContractExtractionRepository(
      prisma
    );

    // Initialize hierarchy and reporting services
    this.contractHierarchyService = new ContractHierarchyService();
    this.contractReportingService = new ContractReportingService(
      this.contractHierarchyService
    );
    this.reportingMigrationService = new ReportingMigrationService(prisma);

    // Initialize integrity services
    this.integrityConfigurationService = new IntegrityConfigurationService(prisma);
    this.integrityAnalysisService = new IntegrityAnalysisService(prisma);

    // Initialize entitlement service
    this.contractEntitlementService = new ContractEntitlementService(prisma);
  }

  /**
   * Get the upload middleware for single file uploads
   */
  getUploadMiddleware() {
    return upload.single("document");
  }

  /**
   * Gets all contracts with extraction data for three-tier display (optimized)
   * @route GET /api/contracts/with-extraction
   */
  async getContractsWithExtraction(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      // Parse query parameters
      const {
        title,
        agreementType,
        status,
        provider,
        securityClassification,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        renewalMonths,
        page = 1,
        limit = 10,
      } = req.query;

      // Since we're using ContractExtraction as the primary model now,
      // query extractions directly instead of contracts
      const pageNum = parseInt(page as string, 10);
      const limitNum = parseInt(limit as string, 10);

      // Get extractions
      const result =
        await this.contractExtractionRepository.getByTenantWithFilters(
          tenantId,
          {
            title: title as string,
            agreementType: agreementType as string,
            status: status as string,
            provider: provider as string,
            securityClassification: securityClassification as string,
            startDateFrom: startDateFrom as string,
            startDateTo: startDateTo as string,
            endDateFrom: endDateFrom as string,
            endDateTo: endDateTo as string,
            renewalMonths: renewalMonths as string,
            page: pageNum,
            limit: limitNum,
          }
        );

      // Transform extractions to the expected format
      const contractsWithExtraction = await Promise.all(
        result.extractions.map(async (extraction: ContractExtraction) => {
          try {
            // Get basic contract information
            let contractInfo = null;
            try {
              contractInfo = await this.contractAIService.getBasicContractInfo(
                extraction.contractId,
                tenantId
              );
            } catch (error) {
              logger.warn(
                `Failed to get contract info for ${extraction.contractId}:`,
                error
              );
            }

            return {
              contractId: extraction.contractId,
              fixedFields: extraction.fixedFields,
              dynamicFields: extraction.dynamicFields,
              specialFields: extraction.specialFields,
              overallConfidence: extraction.overallConfidence,
              extractionDate: extraction.extractionDate?.toISOString(),
              extractionVersion: extraction.extractionVersion,
              contractInfo: contractInfo,
              updatedAt:
                extraction.updatedAt?.toISOString() ||
                extraction.createdAt?.toISOString(),
            };
          } catch (error) {
            logger.error(
              `Error processing extraction for contract ${extraction.contractId}:`,
              { error }
            );

            return {
              contractId: extraction.contractId,
              fixedFields: extraction.fixedFields,
              dynamicFields: extraction.dynamicFields,
              specialFields: extraction.specialFields,
              overallConfidence: extraction.overallConfidence,
              extractionDate: extraction.extractionDate?.toISOString(),
              extractionVersion: extraction.extractionVersion,
              contractInfo: null,
              updatedAt:
                extraction.updatedAt?.toISOString() ||
                extraction.createdAt?.toISOString(),
            };
          }
        })
      );

      // Filter out any null results
      const validExtractions = contractsWithExtraction.filter(Boolean);

      res.status(200).json({
        extractions: validExtractions,
        total: result.total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(result.total / limitNum),
      });
    } catch (error) {
      logger.error("Error in get contracts with extraction controller:", {
        error,
      });
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Gets all contracts with optional filtering
   * @route GET /api/contracts
   */
  async getContracts(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      // Parse query parameters
      const {
        title,
        agreementType,
        status,
        securityClassification,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        page = 1,
        limit = 10,
      } = req.query;

      // Build search params
      const searchParams = {
        title: title as string | undefined,
        agreementType: agreementType as AgreementType | undefined,
        status: status as ContractStatus | undefined,
        securityClassification: securityClassification as
          | SecurityClassification
          | undefined,
        startDateFrom: startDateFrom
          ? new Date(startDateFrom as string)
          : undefined,
        startDateTo: startDateTo ? new Date(startDateTo as string) : undefined,
        endDateFrom: endDateFrom ? new Date(endDateFrom as string) : undefined,
        endDateTo: endDateTo ? new Date(endDateTo as string) : undefined,
        tenantId,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      // Get contracts
      const result = await this.contractService.searchContracts(searchParams);

      // Transform dates to ISO strings for JSON response and enhance value with currency
      const contracts = await Promise.all(
        result.contracts.map(async (contract) => {
          let enhancedValue = contract.value;

          // Try to get currency and provider from metadata (with timeout)
          let enhancedProvider = null;
          try {
            const metadataPromise = this.contractService.getContractMetadata(
              contract.id,
              tenantId
            );

            // Add timeout to prevent hanging
            const metadata = await Promise.race([
              metadataPromise,
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error("Timeout")), 2000)
              ),
            ]);

            if (metadata) {
              // Extract currency information
              if (
                (metadata as any)?.currency &&
                contract.value &&
                contract.value !== "0"
              ) {
                // Check if value already contains currency prefix (format: "CURRENCY:AMOUNT")
                if (contract.value.includes(":")) {
                  // Value already has currency prefix, use as is
                  enhancedValue = contract.value;
                } else {
                  // Combine currency with value: "EUR:808668.96"
                  enhancedValue = `${(metadata as any).currency}:${contract.value
                    }`;
                }
              }

              // Extract provider information from metadata
              const autoExtractedFields = (metadata as any)
                ?.autoExtractedFields;
              if (autoExtractedFields) {
                // Check for provider in new schema format
                if (autoExtractedFields.provider) {
                  enhancedProvider = autoExtractedFields.provider;
                } else if (autoExtractedFields.vendorName) {
                  enhancedProvider = autoExtractedFields.vendorName;
                } else if (
                  autoExtractedFields.parties &&
                  Array.isArray(autoExtractedFields.parties)
                ) {
                  // Look for provider/vendor role in parties array
                  const providerParty = autoExtractedFields.parties.find(
                    (party: any) =>
                      party.role?.toLowerCase().includes("provider") ||
                      party.role?.toLowerCase().includes("vendor") ||
                      party.role?.toLowerCase().includes("supplier")
                  );
                  if (providerParty?.name) {
                    enhancedProvider = providerParty.name;
                  }
                }
              }
            }
          } catch (error) {
            // If metadata fetch fails, keep original values
            // Don't log to avoid spam, just continue with original values
          }

          return {
            ...contract.toDTO(),
            startDate: contract.startDate?.toISOString() || null,
            endDate: contract.endDate?.toISOString() || null,
            renewalDate: contract.renewalDate?.toISOString() || null,
            createdAt: contract.createdAt.toISOString(),
            updatedAt: contract.updatedAt.toISOString(),
            value: enhancedValue, // Enhanced value with currency
            provider: enhancedProvider, // Enhanced provider from metadata
          };
        })
      );

      res.status(200).json({
        contracts,
        total: result.total,
        page: searchParams.page,
        limit: searchParams.limit,
        totalPages: Math.ceil(result.total / searchParams.limit),
      });
    } catch (error) {
      logger.error("Error in get contracts controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets a contract by ID
   * @route GET /api/contracts/:id
   */
  async getContract(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get contract
      const contract = await this.contractService.getContract(id, tenantId);

      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      let enhancedValue = contract.value;
      let enhancedProvider = null;

      // Try to get currency and provider from metadata (same logic as listing API)
      try {
        const metadataPromise = this.contractService.getContractMetadata(
          contract.id,
          tenantId
        );

        // Add timeout to prevent hanging
        const metadata = (await Promise.race([
          metadataPromise,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Timeout")), 2000)
          ),
        ])) as any; // Type assertion to handle Promise.race typing issue

        if (metadata && metadata.toDTO) {
          const metadataDTO = metadata.toDTO();

          // Extract currency and enhance value
          if (metadataDTO.currency && contract.value) {
            const valueMatch = contract.value.match(/[\d,]+\.?\d*/);
            if (valueMatch) {
              enhancedValue = `${metadataDTO.currency}:${valueMatch[0]}`;
            }
          }

          // Extract provider from metadata (same logic as listing API)
          if (metadataDTO.autoExtractedFields?.provider) {
            enhancedProvider = metadataDTO.autoExtractedFields.provider;
          } else if (metadataDTO.autoExtractedFields?.publisher) {
            // Oracle contracts - use publisher as provider
            enhancedProvider = metadataDTO.autoExtractedFields.publisher;
          } else if (metadataDTO.autoExtractedFields?.parties) {
            const providerParty = metadataDTO.autoExtractedFields.parties.find(
              (party: any) => party.role?.toLowerCase() === "provider"
            );
            if (providerParty?.name) {
              enhancedProvider = providerParty.name;
            }
          }
        }
      } catch (error) {
        // If metadata fetch fails, keep original values
        // Don't log to avoid spam, just continue with original values
      }

      // Transform dates to ISO strings for JSON response
      const contractDTO = {
        ...contract.toDTO(),
        startDate: contract.startDate?.toISOString() || null,
        endDate: contract.endDate?.toISOString() || null,
        renewalDate: contract.renewalDate?.toISOString() || null,
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
        value: enhancedValue, // Enhanced value with currency
        provider: enhancedProvider, // Enhanced provider from metadata
      };

      res.status(200).json(contractDTO);
    } catch (error) {
      logger.error("Error in get contract controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a new contract
   * @route POST /api/contracts
   */
  async createContract(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const contractData = {
        ...req.body,
        tenantId,
        createdById: userId,
      };

      // Create contract
      const contract = await this.contractService.createContract(contractData);

      // Transform dates to ISO strings for JSON response
      const contractDTO = {
        ...contract.toDTO(),
        startDate: contract.startDate?.toISOString() || null,
        endDate: contract.endDate?.toISOString() || null,
        renewalDate: contract.renewalDate?.toISOString() || null,
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      };

      res.status(201).json(contractDTO);
    } catch (error) {
      logger.error("Error in create contract controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates an existing contract
   * @route PUT /api/contracts/:id
   */
  async updateContract(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;
      const contractData = req.body;

      // Update contract
      const contract = await this.contractService.updateContract(
        id,
        contractData,
        tenantId
      );

      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Transform dates to ISO strings for JSON response
      const contractDTO = {
        ...contract.toDTO(),
        startDate: contract.startDate?.toISOString() || null,
        endDate: contract.endDate?.toISOString() || null,
        renewalDate: contract.renewalDate?.toISOString() || null,
        createdAt: contract.createdAt.toISOString(),
        updatedAt: contract.updatedAt.toISOString(),
      };

      res.status(200).json(contractDTO);
    } catch (error) {
      logger.error("Error in update contract controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes a contract
   * @route DELETE /api/contracts/:id
   */
  async deleteContract(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Delete contract
      const success = await this.contractService.deleteContract(id, tenantId);

      if (!success) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Error in delete contract controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets contract metadata
   * @route GET /api/contracts/:id/metadata
   */
  async getContractMetadata(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get contract metadata
      const metadata = await this.contractService.getContractMetadata(
        id,
        tenantId
      );

      if (!metadata) {
        res.status(404).json({ error: "Contract metadata not found" });
        return;
      }

      // Transform dates to ISO strings for JSON response
      const metadataDTO = {
        ...metadata.toDTO(),

        lastExtractedAt: metadata.lastExtractedAt?.toISOString() || null,
        createdAt: metadata.createdAt.toISOString(),
        updatedAt: metadata.updatedAt.toISOString(),
      };

      res.status(200).json(metadataDTO);
    } catch (error) {
      logger.error("Error in get contract metadata controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates contract metadata
   * @route POST /api/contracts/:id/metadata
   */
  async updateContractMetadata(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;
      const metadataData = req.body;

      // Check if contract exists and belongs to tenant
      const contract = await this.contractService.getContract(id, tenantId);

      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Update metadata
      const metadata =
        await this.contractService.createOrUpdateContractMetadata({
          contractId: id,
          ...metadataData,
        });

      // Transform dates to ISO strings for JSON response
      const metadataDTO = {
        ...metadata.toDTO(),

        lastExtractedAt: metadata.lastExtractedAt?.toISOString() || null,
        createdAt: metadata.createdAt.toISOString(),
        updatedAt: metadata.updatedAt.toISOString(),
      };

      res.status(200).json({
        success: true,
        metadata: metadataDTO,
      });
    } catch (error) {
      logger.error("Error in update contract metadata controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets the status of an import job
   * @route GET /api/contracts/import/status/:jobId
   */
  async getImportStatus(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;
      const tenantId = req.user!.tenantId;

      if (!jobId) {
        res.status(400).json({
          success: false,
          error: "Job ID is required",
        });
        return;
      }

      const job = importJobService.getJob(jobId);

      if (!job) {
        res.status(404).json({
          success: false,
          error: "Import job not found",
        });
        return;
      }

      // Verify tenant access
      if (job.tenantId !== tenantId) {
        res.status(403).json({
          success: false,
          error: "Access denied",
        });
        return;
      }

      // Calculate progress percentage
      const progressPercentage =
        job.totalFiles > 0
          ? Math.round((job.processedFiles / job.totalFiles) * 100)
          : 0;

      // Calculate elapsed time
      const elapsedTime = job.endTime
        ? job.endTime.getTime() - job.startTime.getTime()
        : Date.now() - job.startTime.getTime();

      res.json({
        success: true,
        job: {
          id: job.id,
          status: job.status,
          totalFiles: job.totalFiles,
          processedFiles: job.processedFiles,
          successfulFiles: job.successfulFiles,
          failedFiles: job.failedFiles,
          progressPercentage,
          elapsedTimeMs: elapsedTime,
          estimatedTimeSeconds: job.estimatedTimeSeconds,
          batchInfo: job.batchInfo,
          errors: job.errors,
          contracts: job.contracts,
          startTime: job.startTime,
          endTime: job.endTime,
        },
      });
    } catch (error) {
      logger.error("Error getting import status:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get import status",
        details: (error as Error).message,
      });
    }
  }

  /**
   * Generates AI summary for a contract
   * @route POST /api/contracts/:id/generate-summary
   */
  async generateContractSummary(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;

      logger.info(`Generating AI summary for contract: ${contractId}`);

      // Get contract details
      const contract = await this.contractService.getContract(
        contractId,
        tenantId
      );
      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Get contract document
      const documentData = await this.contractService.getContractDocument(
        contractId,
        tenantId
      );
      if (!documentData) {
        res
          .status(400)
          .json({ error: "No contract document found for summary generation" });
        return;
      }

      // Prepare contract data for summary
      const contractData = {
        title: contract.title,
        description: contract.description,
        agreementType: contract.agreementType,
        status: contract.status,
        value: contract.value,
        counterparty: contract.counterparty,
        startDate: contract.startDate,
        endDate: contract.endDate,
        renewalDate: contract.renewalDate,
      };

      // Generate both narrative and tabular summaries using the AI service
      logger.info(`Generating summaries for contract: ${contractId}`);
      const summaries = await this.contractImportService
        .getContractAIService()
        .generateContractSummariesFromDocument(
          documentData.content,
          documentData.fileName,
          contractData
        );

      logger.info(`Summaries generated:`, {
        narrativeLength: summaries.narrative?.length || 0,
        tabularCount: summaries.tabular?.length || 0,
        hasNarrative: !!summaries.narrative,
        hasTabular: !!summaries.tabular && Array.isArray(summaries.tabular),
      });

      // Update contract metadata with the generated summaries
      const existingMetadata = await this.contractService.getContractMetadata(
        contractId,
        tenantId
      );

      if (existingMetadata) {
        // Update existing metadata
        logger.info(`Updating existing metadata for contract: ${contractId}`);
        await this.contractService.createOrUpdateContractMetadata({
          contractId: contractId,
          totalValue: existingMetadata.totalValue || undefined,
          currency: existingMetadata.currency || undefined,
          paymentTerms: existingMetadata.paymentTerms || undefined,
          autoExtractedFields:
            existingMetadata.autoExtractedFields || undefined,
          customMetadata: existingMetadata.customMetadata || undefined,
        });
      } else {
        // Create new metadata with both summaries
        logger.info(`Creating new metadata for contract: ${contractId}`);
        await this.contractService.createOrUpdateContractMetadata({
          contractId: contractId,
        });
      }

      logger.info(
        `Successfully generated and stored AI summaries for contract: ${contractId}`,
        {
          narrativeStored: !!summaries.narrative,
          tabularStored: !!summaries.tabular,
          tabularCount: summaries.tabular?.length || 0,
        }
      );

      res.json({
        success: true,
        summary: summaries.narrative,
        tabularSummary: summaries.tabular,
        message: "AI summaries generated successfully",
        debug: {
          narrativeLength: summaries.narrative?.length || 0,
          tabularCount: summaries.tabular?.length || 0,
          hasNarrative: !!summaries.narrative,
          hasTabular: !!summaries.tabular && Array.isArray(summaries.tabular),
        },
      });
    } catch (error) {
      logger.error("Error generating contract summary:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Generates DORA compliance analysis for a contract
   * @route POST /api/contracts/:id/generate-dora-compliance
   */
  async generateDORACompliance(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;

      logger.info(
        `Generating DORA compliance analysis for contract: ${contractId}`
      );

      // Get contract details
      const contract = await this.contractService.getContract(
        contractId,
        tenantId
      );
      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Get contract document metadata first to check if document exists
      logger.info(`Checking document availability for contract: ${contractId}`);
      const documentMetadata =
        await this.contractService.getContractDocumentMetadata(
          contractId,
          tenantId
        );

      if (!documentMetadata || !documentMetadata.hasDocument) {
        logger.error(`No document found for contract: ${contractId}`);
        res.status(400).json({
          error:
            "No contract document found for compliance analysis. Please ensure the contract has a valid document uploaded.",
        });
        return;
      }

      logger.info(
        `Document found - Size: ${documentMetadata.size} bytes, FileName: ${documentMetadata.fileName}, MimeType: ${documentMetadata.mimeType}`
      );

      // Instead of retrieving the potentially corrupted stored document,
      // we'll use the same approach as initial extraction by getting the document
      // and processing it the same way that works during upload
      logger.info(
        `Generating DORA compliance analysis for contract: ${contractId} using extraction method`
      );

      // Get the document content using the same method that works for extraction
      const documentData = await this.contractService.getContractDocument(
        contractId,
        tenantId
      );

      if (!documentData) {
        res.status(400).json({
          error: "Failed to retrieve contract document for analysis.",
        });
        return;
      }

      // Check if OCR text is available for this contract
      const ocrText = await this.contractAIService.getOCRTextForContract(contractId);

      let complianceAnalysis;
      if (ocrText) {
        logger.info(`Using OCR text for DORA compliance analysis for contract: ${contractId}`);
        // Use OCR text for analysis
        complianceAnalysis = await this.contractAIService.generateDORAComplianceFromText(
          ocrText,
          contract.title
        );
      } else {
        logger.info(`Using document buffer for DORA compliance analysis for contract: ${contractId}`);
        // Fallback to document buffer
        complianceAnalysis = await this.contractAIService.generateDORAComplianceAnalysis(
          documentData.content,
          documentData.fileName,
          contract.title
        );
      }

      logger.info(
        `DORA compliance analysis generated for contract: ${contractId}`
      );

      // Store the compliance analysis in the contract extraction data
      await this.contractAIService.updateComplianceAnalysis(
        contractId,
        tenantId,
        complianceAnalysis
      );

      logger.info(
        `Successfully generated and stored DORA compliance analysis for contract: ${contractId}`
      );

      res.json({
        success: true,
        compliance: complianceAnalysis,
        message: "DORA compliance analysis generated successfully",
      });
    } catch (error) {
      logger.error("Error generating DORA compliance analysis:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }



  /**
   * Generates integrity analysis for a contract
   * @route POST /api/contracts/:id/generate-integrity-analysis
   */
  async generateIntegrityAnalysis(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      logger.info(
        `Generating integrity analysis for contract: ${contractId}`
      );

      // Get contract details
      const contract = await this.contractService.getContract(
        contractId,
        tenantId
      );
      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Generate integrity analysis
      const integrityAnalysis = await this.integrityAnalysisService.analyzeContract(
        contractId,
        userId,
        tenantId
      );

      logger.info(
        `Integrity analysis generated for contract: ${contractId}`
      );

      res.json({
        success: true,
        analysis: integrityAnalysis,
        message: "Integrity analysis generated successfully",
      });
    } catch (error) {
      logger.error("Error generating integrity analysis:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Get user's integrity configurations
   * @route GET /api/integrity/configurations
   */
  async getIntegrityConfigurations(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      const configurations = await this.integrityConfigurationService.getUserConfigurations(
        userId,
        tenantId
      );

      res.json({
        success: true,
        configurations,
      });
    } catch (error) {
      logger.error("Error getting integrity configurations:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Get user's active integrity configuration
   * @route GET /api/integrity/configurations/active
   */
  async getActiveIntegrityConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      const activeConfig = await this.integrityConfigurationService.getActiveConfiguration(
        userId,
        tenantId
      );

      res.json({
        success: true,
        configuration: activeConfig,
      });
    } catch (error) {
      logger.error("Error getting active integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Reset user's integrity configuration to use updated template
   * @route POST /api/integrity/configurations/reset-to-template
   */
  async resetIntegrityConfigurationToTemplate(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      // Delete existing configuration
      await this.integrityConfigurationService.deleteUserConfiguration(userId, tenantId);

      // Get fresh configuration (will create from updated template)
      const newConfig = await this.integrityConfigurationService.getActiveConfiguration(
        userId,
        tenantId
      );

      res.json({
        success: true,
        message: "Configuration reset to updated template",
        configuration: newConfig,
      });
    } catch (error) {
      logger.error("Error resetting integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Create or update integrity configuration
   * @route POST /api/integrity/configurations
   */
  async createIntegrityConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const configurationData = req.body;

      const configuration = await this.integrityConfigurationService.createConfiguration(
        userId,
        tenantId,
        configurationData
      );

      res.json({
        success: true,
        configuration,
        message: "Integrity configuration created successfully",
      });
    } catch (error) {
      logger.error("Error creating integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Update integrity configuration
   * @route PUT /api/integrity/configurations/:id
   */
  async updateIntegrityConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const configId = req.params.id;
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const configurationData = req.body;

      const configuration = await this.integrityConfigurationService.updateConfiguration(
        configId,
        userId,
        tenantId,
        configurationData
      );

      res.json({
        success: true,
        configuration,
        message: "Integrity configuration updated successfully",
      });
    } catch (error) {
      logger.error("Error updating integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Set active integrity configuration
   * @route POST /api/integrity/configurations/:id/activate
   */
  async setActiveIntegrityConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const configId = req.params.id;
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      const configuration = await this.integrityConfigurationService.setActiveConfiguration(
        configId,
        userId,
        tenantId
      );

      res.json({
        success: true,
        configuration,
        message: "Integrity configuration activated successfully",
      });
    } catch (error) {
      logger.error("Error activating integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Reset user's integrity configuration to default template
   * @route DELETE /api/integrity/configurations/reset
   */
  async resetIntegrityConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      // Delete all existing configurations and create new one from template
      const template = this.integrityConfigurationService.getDefaultTemplate();
      const configuration = await this.integrityConfigurationService.createConfiguration(
        userId,
        tenantId,
        template
      );

      res.json({
        success: true,
        configuration,
        message: "Integrity configuration reset to default template",
      });
    } catch (error) {
      logger.error("Error resetting integrity configuration:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Get default integrity template
   * @route GET /api/integrity/template
   */
  async getIntegrityTemplate(req: Request, res: Response): Promise<void> {
    try {
      const template = this.integrityConfigurationService.getDefaultTemplate();

      res.json({
        success: true,
        template,
      });
    } catch (error) {
      logger.error("Error getting integrity template:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Debug endpoint to test tabular summary generation for a specific contract
   * @route POST /api/contracts/:id/debug-tabular-summary
   */
  async debugTabularSummary(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;

      logger.info(
        `[DEBUG] Testing tabular summary generation for contract ${contractId}`
      );

      // Get contract details
      const contract = await this.contractService.getContract(
        contractId,
        tenantId
      );
      if (!contract) {
        res.status(404).json({ error: "Contract not found" });
        return;
      }

      // Get contract document
      const documentData = await this.contractService.getContractDocument(
        contractId,
        tenantId
      );
      if (!documentData) {
        res.status(404).json({ error: "No contract document found" });
        return;
      }

      // Get contract metadata
      const metadata = await this.contractService.getContractMetadata(
        contractId,
        tenantId
      );

      // Prepare contract data
      const contractData = {
        title: contract.title,
        description: contract.description,
        agreementType: contract.agreementType,
        status: contract.status,
        value: contract.value,
        counterparty: contract.counterparty,
        startDate: contract.startDate,
        endDate: contract.endDate,
        renewalDate: contract.renewalDate,
        metadata: metadata || {},
      };

      logger.info(`[DEBUG] Contract data prepared:`, {
        hasTitle: !!contractData.title,
        hasMetadata: !!contractData.metadata,
        metadataKeys: Object.keys(contractData.metadata),
      });

      // Test tabular summary generation
      const contractAIService =
        this.contractImportService.getContractAIService();

      logger.info(`[DEBUG] Testing AI-based tabular summary generation...`);
      let aiTabularSummary = null;
      let aiError = null;

      try {
        aiTabularSummary =
          await contractAIService.generateTabularSummaryFromDocument(
            documentData.content,
            documentData.fileName,
            contractData
          );
        logger.info(
          `[DEBUG] AI tabular summary generated successfully: ${aiTabularSummary.length} aspects`
        );
      } catch (error) {
        aiError = error instanceof Error ? error.message : String(error);
        logger.error(`[DEBUG] AI tabular summary generation failed:`, error);
      }

      logger.info(
        `[DEBUG] Testing metadata-based tabular summary generation...`
      );
      const metadataTabularSummary =
        contractAIService.generateTabularSummaryFromMetadata(contractData);
      logger.info(
        `[DEBUG] Metadata tabular summary generated: ${metadataTabularSummary.length} aspects`
      );

      logger.info(`[DEBUG] Testing fallback tabular summary generation...`);
      const fallbackTabularSummary =
        contractAIService.generateFallbackTabularSummary(
          contractData,
          documentData.fileName
        );
      logger.info(
        `[DEBUG] Fallback tabular summary generated: ${fallbackTabularSummary.length} aspects`
      );

      res.json({
        contractId,
        contractTitle: contract.title,
        documentName: documentData.fileName,
        documentSize: documentData.content.length,
        contractData: {
          hasTitle: !!contractData.title,
          hasMetadata: !!contractData.metadata,
          metadataKeys: Object.keys(contractData.metadata),
        },
        results: {
          aiTabularSummary: {
            success: !!aiTabularSummary,
            error: aiError,
            data: aiTabularSummary,
            count: aiTabularSummary?.length || 0,
          },
          metadataTabularSummary: {
            success: true,
            data: metadataTabularSummary,
            count: metadataTabularSummary.length,
          },
          fallbackTabularSummary: {
            success: true,
            data: fallbackTabularSummary,
            count: fallbackTabularSummary.length,
          },
        },
      });
    } catch (error) {
      logger.error("[DEBUG] Error in tabular summary debug endpoint:", error);
      res.status(500).json({
        error: "Failed to debug tabular summary generation",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Gets Agreement Document
   * @route GET /api/contracts/:id/document
   */
  async getContractDocument(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get Agreement Document using the new service method
      const documentData = await this.contractService.getContractDocument(
        id,
        tenantId
      );

      if (!documentData) {
        res.status(404).json({ error: "Agreement Document not found" });
        return;
      }

      // Set response headers
      res.setHeader("Content-Type", documentData.mimeType);
      res.setHeader(
        "Content-Disposition",
        `inline; filename="${documentData.fileName}"`
      );
      res.setHeader("Content-Length", documentData.size);

      // Send document content
      res.send(documentData.content);
    } catch (error) {
      logger.error("Error in get Agreement Document controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets contracts for autocomplete (lightweight) using ContractExtraction
   * @route GET /api/contracts/autocomplete
   */
  async getContractsForAutocomplete(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const { search } = req.query;

      // Get contracts with minimal data for autocomplete from ContractExtraction
      const contracts = await this.contractService.getContractsForAutocomplete(
        tenantId,
        search as string
      );

      res.status(200).json({
        success: true,
        contracts: contracts.map((contract) => ({
          id: contract.id,
          title: contract.title,
          contractNumber: contract.contractNumber,
          supplier: contract.supplier, // Changed from counterparty to supplier
        })),
      });
    } catch (error) {
      logger.error("Error getting contracts for autocomplete:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Downloads a Agreement Document
   * @route GET /api/contracts/:id/download
   */
  async downloadContractDocument(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get Agreement Document using the new service method
      const documentData = await this.contractService.getContractDocument(
        id,
        tenantId
      );

      if (!documentData) {
        res.status(404).json({ error: "Agreement Document not found" });
        return;
      }

      // Set response headers for download
      res.setHeader("Content-Type", documentData.mimeType);
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${documentData.fileName}"`
      );
      res.setHeader("Content-Length", documentData.size);

      // Send document content
      res.send(documentData.content);
    } catch (error) {
      logger.error("Error in download Agreement Document controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets Agreement Document metadata without downloading content
   * @route GET /api/contracts/:id/document/metadata
   */
  async getContractDocumentMetadata(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get document metadata
      const documentMetadata =
        await this.contractService.getContractDocumentMetadata(id, tenantId);

      if (!documentMetadata) {
        res.status(404).json({ error: "Document not found" });
        return;
      }

      res.json(documentMetadata);
    } catch (error) {
      logger.error("Error in get Agreement Document metadata controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Imports contracts from multiple files (CSV, Excel, PDF, Word, etc.)
   * @route POST /api/contracts/import
   */
  async importContracts(req: Request, res: Response): Promise<void> {
    const uploadMiddleware = upload.array("files", 20); // Allow up to 20 files

    uploadMiddleware(req, res, async (err) => {
      if (err) {
        logger.error("Error uploading files:", { error: err });
        res.status(400).json({
          success: false,
          error: err.message,
          details:
            err instanceof multer.MulterError ? err.code : "VALIDATION_FAILED",
        });
        return;
      }

      try {
        const files = req.files as Express.Multer.File[];
        if (!files || files.length === 0) {
          res.status(400).json({
            success: false,
            error: "No files uploaded",
            details: "Please select at least one file to upload",
          });
          return;
        }

        const tenantId = req.user!.tenantId;
        const userId = req.user!.userId;

        // Initialize result object
        let result: ContractImportResult = {
          totalProcessed: 0,
          successful: 0,
          failed: 0,
          errors: [],
          contracts: [],
        };

        // Group files by type with improved categorization
        const csvFiles: Express.Multer.File[] = [];
        const excelFiles: Express.Multer.File[] = [];
        const documentFiles: Express.Multer.File[] = [];

        files.forEach((file) => {
          const fileExt = path.extname(file.originalname).toLowerCase();

          // Categorize files by extension
          if (fileExt === ".csv") {
            csvFiles.push(file);
          } else if (fileExt === ".xlsx" || fileExt === ".xls") {
            excelFiles.push(file);
          } else if (
            [
              ".pdf",
              ".docx",
              ".doc",
              ".txt",
              ".rtf",
              ".odt",
              ".md",
              ".html",
              ".htm",
            ].includes(fileExt)
          ) {
            documentFiles.push(file);
          } else {
            // Handle unsupported file types
            result.failed++;
            result.errors.push({
              file: file.originalname,
              error: `Unsupported file format: ${fileExt}`,
            });
          }
        });

        // Send an immediate response to indicate processing has started
        if (files.length > 1) {
          // Create a job for tracking
          const concurrencyLimit = Math.min(3, files.length);
          const estimatedBatches = Math.ceil(files.length / concurrencyLimit);
          const estimatedTimeSeconds = estimatedBatches * 120; // 2 minutes per batch

          const jobId = importJobService.createJob(
            tenantId,
            userId,
            files.length,
            estimatedTimeSeconds,
            {
              totalFiles: files.length,
              concurrencyLimit,
              estimatedBatches,
            }
          );

          // For larger batches, send an immediate 202 Accepted response
          res.status(202).json({
            message: "Import processing started",
            filesReceived: files.length,
            processingStarted: true,
            jobId: jobId,
            estimatedTimeSeconds: estimatedTimeSeconds,
            batchInfo: {
              totalFiles: files.length,
              concurrencyLimit: concurrencyLimit,
              estimatedBatches: estimatedBatches,
            },
            statusEndpoint: `/api/contracts/import/status/${jobId}`,
          });

          // Continue processing in the background with job tracking
          this.processImportedFilesWithJobTracking(
            csvFiles,
            excelFiles,
            documentFiles,
            tenantId,
            userId,
            jobId
          ).catch((error: Error) => {
            logger.error("Background import processing failed:", error);
            importJobService.failJob(jobId, error.message);
          });

          return;
        }

        // For smaller batches, process synchronously and return the complete result
        result = await this.processImportedFiles(
          csvFiles,
          excelFiles,
          documentFiles,
          tenantId,
          userId
        );

        // Return the final result
        res.status(200).json({
          ...result,
          success: result.successful > 0,
          processingTime: `${result.successful} contracts processed successfully in ${result.totalProcessed} files`,
        });
      } catch (error) {
        logger.error("Error importing contracts:", { error });

        // Handle AppError with specific status codes
        if (error instanceof AppError) {
          res.status(error.statusCode).json({
            success: false,
            error: error.message,
            statusCode: error.statusCode,
          });
          return;
        }

        // Handle other errors with 500 status
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          stack:
            process.env.NODE_ENV === "development"
              ? (error as Error).stack
              : undefined,
        });
      }
    });
  }

  /**
   * Process imported files by type with job tracking
   * @param csvFiles CSV files to process
   * @param excelFiles Excel files to process
   * @param documentFiles Document files to process
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param jobId Job ID for tracking
   * @returns Import result
   */
  private async processImportedFilesWithJobTracking(
    csvFiles: Express.Multer.File[],
    excelFiles: Express.Multer.File[],
    documentFiles: Express.Multer.File[],
    tenantId: string,
    userId: string,
    jobId: string
  ): Promise<ContractImportResult> {
    const result: ContractImportResult = {
      totalProcessed: 0,
      successful: 0,
      failed: 0,
      errors: [],
      contracts: [],
    };

    // Process document files first (highest priority) with job tracking
    if (documentFiles.length > 0) {
      try {
        logger.info(
          `Processing ${documentFiles.length} document files with job tracking`
        );
        const docResult =
          await this.contractImportService.importFromDocumentsWithJobTracking(
            documentFiles.map((file) => ({
              buffer: file.buffer,
              originalname: file.originalname,
            })),
            tenantId,
            userId,
            jobId
          );

        // Merge results
        result.totalProcessed += docResult.totalProcessed;
        result.successful += docResult.successful;
        result.failed += docResult.failed;
        result.errors.push(...docResult.errors);
        result.contracts.push(...docResult.contracts);

        logger.info(
          `Document processing complete: ${docResult.successful} successful, ${docResult.failed} failed`
        );
      } catch (error) {
        logger.error("Error processing document files:", { error });

        // If it's an AppError (like duplicate filename), re-throw it to be handled by the main catch block
        if (error instanceof AppError) {
          throw error;
        }

        result.failed += documentFiles.length;
        result.errors.push({
          error: (error as Error).message,
          data: {
            fileCount: documentFiles.length,
            fileTypes: documentFiles.map((f) => path.extname(f.originalname)),
          },
        });
      }
    }

    // Process CSV and Excel files (these don't have job tracking yet, but could be added)
    // For now, process them normally
    const remainingResult = await this.processImportedFiles(
      csvFiles,
      excelFiles,
      [],
      tenantId,
      userId
    );

    // Merge remaining results
    result.totalProcessed += remainingResult.totalProcessed;
    result.successful += remainingResult.successful;
    result.failed += remainingResult.failed;
    result.errors.push(...remainingResult.errors);
    result.contracts.push(...remainingResult.contracts);

    return result;
  }

  /**
   * Process imported files by type
   * @param csvFiles CSV files to process
   * @param excelFiles Excel files to process
   * @param documentFiles Document files to process
   * @param tenantId Tenant ID
   * @param userId User ID
   * @returns Import result
   */
  private async processImportedFiles(
    csvFiles: Express.Multer.File[],
    excelFiles: Express.Multer.File[],
    documentFiles: Express.Multer.File[],
    tenantId: string,
    userId: string
  ): Promise<ContractImportResult> {
    const result: ContractImportResult = {
      totalProcessed: 0,
      successful: 0,
      failed: 0,
      errors: [],
      contracts: [],
    };

    // Process document files first (highest priority)
    if (documentFiles.length > 0) {
      try {
        logger.info(`Processing ${documentFiles.length} document files`);
        const docResult = await this.contractImportService.importFromDocuments(
          documentFiles.map((file) => ({
            buffer: file.buffer,
            originalname: file.originalname,
          })),
          tenantId,
          userId
        );

        // Merge results
        result.totalProcessed += docResult.totalProcessed;
        result.successful += docResult.successful;
        result.failed += docResult.failed;
        result.errors.push(...docResult.errors);
        result.contracts.push(...docResult.contracts);

        logger.info(
          `Document processing complete: ${docResult.successful} successful, ${docResult.failed} failed`
        );
      } catch (error) {
        logger.error("Error processing document files:", { error });

        // If it's an AppError (like duplicate filename), re-throw it to be handled by the main catch block
        if (error instanceof AppError) {
          throw error;
        }

        result.failed += documentFiles.length;
        result.errors.push({
          error: (error as Error).message,
          data: {
            fileCount: documentFiles.length,
            fileTypes: documentFiles.map((f) => path.extname(f.originalname)),
          },
        });
      }
    }

    // Process CSV files
    for (const file of csvFiles) {
      try {
        logger.info(`Processing CSV file: ${file.originalname}`);
        const csvResult = await this.contractImportService.importFromCsv(
          file.buffer,
          tenantId
        );

        // Merge results
        result.totalProcessed += csvResult.totalProcessed;
        result.successful += csvResult.successful;
        result.failed += csvResult.failed;
        result.errors.push(...csvResult.errors);
        result.contracts.push(...csvResult.contracts);

        logger.info(
          `CSV processing complete for ${file.originalname}: ${csvResult.successful} successful, ${csvResult.failed} failed`
        );
      } catch (error) {
        logger.error(`Error processing CSV file ${file.originalname}:`, {
          error,
        });
        result.failed++;
        result.errors.push({
          file: file.originalname,
          error: (error as Error).message,
        });
      }
    }

    // Process Excel files
    for (const file of excelFiles) {
      try {
        logger.info(`Processing Excel file: ${file.originalname}`);
        const excelResult = await this.contractImportService.importFromExcel(
          file.buffer,
          tenantId
        );

        // Merge results
        result.totalProcessed += excelResult.totalProcessed;
        result.successful += excelResult.successful;
        result.failed += excelResult.failed;
        result.errors.push(...excelResult.errors);
        result.contracts.push(...excelResult.contracts);

        logger.info(
          `Excel processing complete for ${file.originalname}: ${excelResult.successful} successful, ${excelResult.failed} failed`
        );
      } catch (error) {
        logger.error(`Error processing Excel file ${file.originalname}:`, {
          error,
        });
        result.failed++;
        result.errors.push({
          file: file.originalname,
          error: (error as Error).message,
        });
      }
    }

    return result;
  }

  /**
   * Gets contracts with hierarchy information for a specific group
   * @route GET /api/contracts/hierarchy/:groupId
   */
  async getContractsWithHierarchy(req: Request, res: Response): Promise<void> {
    try {
      const { groupId } = req.params;
      const tenantId = req.user!.tenantId;

      // Get contracts with hierarchy information
      const contractsWithHierarchy =
        await this.contractService.getContractsWithHierarchy(groupId, tenantId);

      // Transform dates to ISO strings for JSON response
      const hierarchyData = contractsWithHierarchy.map((contract) => ({
        ...contract,
        startDate: contract.startDate?.toISOString() || null,
        endDate: contract.endDate?.toISOString() || null,
      }));

      res.status(200).json({
        groupId,
        contracts: hierarchyData,
        totalContracts: hierarchyData.length,
      });
    } catch (error) {
      logger.error("Error in get contracts with hierarchy controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets repository statistics for all contracts
   * @route GET /api/contracts/repository-statistics
   */
  async getRepositoryStatistics(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      // Get repository statistics
      const statistics = await this.contractService.getRepositoryStatistics(
        tenantId
      );

      res.status(200).json(statistics);
    } catch (error) {
      logger.error("Error in get repository statistics controller:", {
        error,
      });
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Gets hierarchy information for a specific agreement type
   * @route GET /api/contracts/hierarchy-info/:agreementType
   */
  async getHierarchyInfo(req: Request, res: Response): Promise<void> {
    try {
      const { agreementType } = req.params;

      const hierarchyInfo =
        this.contractService.getHierarchyInfo(agreementType);

      if (!hierarchyInfo) {
        res.status(404).json({
          error: "Hierarchy information not found for this agreement type",
        });
        return;
      }

      res.status(200).json({
        agreementType,
        hierarchyInfo,
      });
    } catch (error) {
      logger.error("Error in get hierarchy info controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets all supported hierarchy types
   * @route GET /api/contracts/hierarchy-types
   */
  async getAllHierarchyTypes(req: Request, res: Response): Promise<void> {
    try {
      const hierarchyTypes = this.contractService.getAllHierarchyTypes();

      // Convert Map to object for JSON response
      const hierarchyTypesObject = Object.fromEntries(hierarchyTypes);

      res.status(200).json({
        hierarchyTypes: hierarchyTypesObject,
        totalTypes: hierarchyTypes.size,
      });
    } catch (error) {
      logger.error("Error in get all hierarchy types controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Checks if one contract type can be a parent of another
   * @route GET /api/contracts/can-be-parent/:parentType/:childType
   */
  async canBeParent(req: Request, res: Response): Promise<void> {
    try {
      const { parentType, childType } = req.params;

      const canBeParent = this.contractService.canBeParent(
        parentType,
        childType
      );

      res.status(200).json({
        parentType,
        childType,
        canBeParent,
      });
    } catch (error) {
      logger.error("Error in can be parent controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates reporting relationship for a contract
   * @route PUT /api/contracts/:id/reporting-to
   */
  async updateReportingRelationship(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { id } = req.params;
      const { parentContractId } = req.body;
      const tenantId = req.user!.tenantId;

      if (!parentContractId) {
        res.status(400).json({ error: "Parent contract ID is required" });
        return;
      }

      // Basic validation - prevent self-referencing
      if (id === parentContractId) {
        res.status(400).json({
          error: "A contract cannot report to itself",
        });
        return;
      }

      // Check if contract exists
      const extraction =
        await this.contractExtractionRepository.findByContractId(id);
      if (!extraction) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Check if parent contract exists
      const parentExtraction =
        await this.contractExtractionRepository.findByContractId(
          parentContractId
        );
      if (!parentExtraction) {
        res.status(404).json({ error: "Parent contract not found" });
        return;
      }

      // Update reportingTo field without validation
      await this.contractExtractionRepository.updateReportingTo(
        id,
        parentContractId
      );

      logger.info(
        `Updated reporting relationship: ${id} now reports to ${parentContractId}`
      );

      res.status(200).json({
        success: true,
        message: "Reporting relationship updated successfully",
        contractId: id,
        reportingTo: parentContractId,
      });
    } catch (error) {
      logger.error("Error updating reporting relationship:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Removes reporting relationship for a contract
   * @route DELETE /api/contracts/:id/reporting-to
   */
  async removeReportingRelationship(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Check if contract exists
      const extraction =
        await this.contractExtractionRepository.findByContractId(id);
      if (!extraction) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Remove reportingTo field
      await this.contractExtractionRepository.updateReportingTo(id, null);

      logger.info(`Removed reporting relationship for contract: ${id}`);

      res.status(200).json({
        success: true,
        message: "Reporting relationship removed successfully",
        contractId: id,
      });
    } catch (error) {
      logger.error("Error removing reporting relationship:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets contracts that report to a specific contract
   * @route GET /api/contracts/:id/children
   */
  async getChildContracts(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get all contracts for the tenant
      const allExtractions =
        await this.contractExtractionRepository.findByTenantId(tenantId);

      // Find child contracts
      const childContracts = this.contractReportingService.getChildContracts(
        id,
        allExtractions
      );

      res.status(200).json({
        parentContractId: id,
        children: childContracts.map((contract) => ({
          id: contract.contractId,
          title: contract.fixedFields.original_filename?.value || "Untitled",
          agreementType: contract.fixedFields.agreement_type?.value,
          provider: contract.fixedFields.provider?.value,
          startDate: contract.fixedFields.start_date?.value,
          endDate: contract.fixedFields.end_date?.value,
        })),
      });
    } catch (error) {
      logger.error("Error getting child contracts:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets the reporting chain for a contract
   * @route GET /api/contracts/:id/reporting-chain
   */
  async getReportingChain(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get all contracts for the tenant
      const allExtractions =
        await this.contractExtractionRepository.findByTenantId(tenantId);

      // Get reporting chain
      const reportingChain = this.contractReportingService.getReportingChain(
        id,
        allExtractions
      );

      res.status(200).json({
        contractId: id,
        reportingChain: reportingChain.map((contract) => ({
          id: contract.contractId,
          title: contract.fixedFields.original_filename?.value || "Untitled",
          agreementType: contract.fixedFields.agreement_type?.value,
          provider: contract.fixedFields.provider?.value,
          startDate: contract.fixedFields.start_date?.value,
          endDate: contract.fixedFields.end_date?.value,
        })),
      });
    } catch (error) {
      logger.error("Error getting reporting chain:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Validates a potential reporting relationship
   * @route POST /api/contracts/:id/validate-reporting
   */
  async validateReportingRelationship(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { id } = req.params;
      const { parentContractId, allowManualOverride } = req.body;
      const tenantId = req.user!.tenantId;

      if (!parentContractId) {
        res.status(400).json({ error: "Parent contract ID is required" });
        return;
      }

      // Get all contracts for validation
      const allExtractions =
        await this.contractExtractionRepository.findByTenantId(tenantId);

      // Validate the relationship
      const validation =
        await this.contractReportingService.validateReportingRelationship(
          id,
          parentContractId,
          allExtractions,
          allowManualOverride || false
        );

      res.status(200).json({
        isValid: validation.isValid,
        reason: validation.reason,
        suggestedParent: validation.suggestedParent,
      });
    } catch (error) {
      logger.error("Error validating reporting relationship:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Migrates reporting relationships for all contracts in a tenant
   * @route POST /api/contracts/migrate-reporting
   */
  async migrateReportingRelationships(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      logger.info(
        `Starting reporting relationship migration for tenant: ${tenantId}`
      );

      // Run the migration for this tenant
      const result =
        await this.reportingMigrationService.migrateTenantContracts(tenantId);

      logger.info(
        `Completed reporting relationship migration for tenant: ${tenantId}`,
        result
      );

      res.status(200).json({
        success: true,
        message: "Reporting relationship migration completed",
        result,
      });
    } catch (error) {
      logger.error("Error migrating reporting relationships:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Validates all reporting relationships for a tenant
   * @route GET /api/contracts/validate-reporting
   */
  async validateAllReportingRelationships(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      logger.info(`Validating reporting relationships for tenant: ${tenantId}`);

      // Validate all relationships for this tenant
      const result =
        await this.reportingMigrationService.validateTenantReportingRelationships(
          tenantId
        );

      res.status(200).json({
        success: true,
        message: "Reporting relationship validation completed",
        result,
      });
    } catch (error) {
      logger.error("Error validating reporting relationships:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Get contract entitlements data
   * @route GET /api/contracts/:id/entitlements
   */
  async getContractEntitlements(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      const entitlements = await this.contractEntitlementService.getContractEntitlements(
        id,
        tenantId
      );

      res.status(200).json(entitlements);
    } catch (error) {
      logger.error("Error getting contract entitlements:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Get OCR text for a contract
   * @param req - Express request
   * @param res - Express response
   */
  async getContractOCRText(req: Request, res: Response): Promise<void> {
    try {
      const { id: contractId } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(401).json({
          success: false,
          message: "Unauthorized: No tenant ID found",
        });
        return;
      }

      logger.info(`Fetching OCR text for contract: ${contractId}`);

      // Check if contract exists and belongs to tenant
      const contract = await this.contractService.getContract(contractId, tenantId);

      if (!contract) {
        res.status(404).json({
          success: false,
          message: "Contract not found",
        });
        return;
      }

      // Get OCR text using the AI service helper method
      const ocrText = await this.contractAIService.getOCRTextForContract(contractId);

      if (!ocrText) {
        res.status(404).json({
          success: false,
          message: "OCR text not found for this contract",
          data: {
            status: "NOT_PROCESSED",
            ocrUsedForExtraction: false,
          }
        });
        return;
      }

      // Get extraction data to check if OCR was used
      const extractionData = await this.contractExtractionRepository.getByContractId(contractId, tenantId);

      const ocrData = {
        text: ocrText,
        confidence: 0.85, // Default confidence since we don't have detailed metadata access
        pageCount: 1, // Default page count
        processingTimeMs: 0, // Default processing time
        processedAt: new Date().toISOString(),
        status: "SUCCESS",
        ocrUsedForExtraction: (extractionData as any)?.ocrUsedForExtraction || false,
      };

      logger.info(`Successfully retrieved OCR text for contract: ${contractId} (${ocrData.text.length} characters)`);

      res.status(200).json({
        success: true,
        message: "OCR text retrieved successfully",
        data: ocrData,
      });
    } catch (error) {
      logger.error("Error fetching OCR text:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch OCR text",
        error: (error as Error).message,
      });
    }
  }


}
