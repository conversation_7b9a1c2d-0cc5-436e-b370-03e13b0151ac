/**
 * Contract Assessment Controller
 * Handles API requests for contract assessment features
 */

import { Request, Response } from "express";
import { ContractAssessmentService } from "../services/ContractAssessmentService";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";

/**
 * Contract Assessment Controller
 */
export class ContractAssessmentController {
  private contractAssessmentService: ContractAssessmentService;

  /**
   * Constructor
   * @param contractAssessmentService Contract assessment service
   */
  constructor(contractAssessmentService: ContractAssessmentService) {
    this.contractAssessmentService = contractAssessmentService;
  }

  /**
   * Creates or updates a contract assessment
   * @route POST /api/contracts/:id/assessment
   */
  async createOrUpdateAssessment(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      // Validate contract ID
      if (!contractId) {
        res.status(400).json({ error: "Contract ID is required" });
        return;
      }

      // Create assessment data
      const assessmentData = {
        contractId,
        tenantId,
        ...req.body,
      };

      // Create or update assessment
      const assessment = await this.contractAssessmentService.createAssessment(assessmentData);

      res.status(200).json(assessment);
    } catch (error) {
      logger.error("Error creating/updating contract assessment:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets a contract assessment
   * @route GET /api/contracts/:id/assessment
   */
  async getAssessment(req: Request, res: Response): Promise<void> {
    try {
      const contractId = req.params.id;
      const tenantId = req.user!.tenantId;

      // Validate contract ID
      if (!contractId) {
        res.status(400).json({ error: "Contract ID is required" });
        return;
      }

      // Get assessment
      const assessment = await this.contractAssessmentService.getAssessment(contractId, tenantId);

      if (!assessment) {
        res.status(404).json({ error: "Assessment not found" });
        return;
      }

      res.status(200).json(assessment);
    } catch (error) {
      logger.error("Error getting contract assessment:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }
}
