/**
 * License Controller
 * Handles HTTP requests for license management
 */

import { Request, Response } from "express";
import { LicenseService } from "../services/LicenseService";
import { DocumentFormat } from "@prisma/client";
import { LicenseType, LicenseStatus } from "../../domain/licenses/License";
import { LicenseDocumentType } from "../../domain/licenses/LicenseDocument";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Controller for license management endpoints
 */
export class LicenseController {
  private licenseService: LicenseService;

  constructor(licenseService: LicenseService) {
    this.licenseService = licenseService;
  }

  /**
   * Creates a new license
   * @route POST /api/licenses
   */
  async createLicense(req: Request, res: Response): Promise<void> {
    try {
      const {
        name,
        description,
        licenseNumber,
        licenseType,
        status,
        vendor,
        purchaseDate,
        startDate,
        endDate,
        renewalType,
        renewalDate,
        isAutoRenew,
        noticePeriodDays,
        totalValue,
        currency,
        costPeriod,
        totalLicenses,
        assignedLicenses,
        tenantId,
      } = req.body;

      // Get user ID from authenticated user
      const userId = req.user?.userId;

      // Validate required fields
      if (!name || !vendor || !licenseType || !totalLicenses || !tenantId) {
        res.status(400).json({
          error:
            "Missing required fields: name, vendor, licenseType, totalLicenses, and tenantId are required",
        });
        return;
      }

      // Parse dates if provided
      const parsedPurchaseDate = purchaseDate
        ? new Date(purchaseDate)
        : undefined;
      const parsedStartDate = startDate ? new Date(startDate) : undefined;
      const parsedEndDate = endDate ? new Date(endDate) : undefined;
      const parsedRenewalDate = renewalDate ? new Date(renewalDate) : undefined;

      // Create the license
      const license = await this.licenseService.createLicense({
        name,
        description,
        licenseNumber,
        licenseType: licenseType as LicenseType,
        status: status as LicenseStatus,
        vendor,
        purchaseDate: parsedPurchaseDate,
        startDate: parsedStartDate,
        endDate: parsedEndDate,
        renewalType,
        renewalDate: parsedRenewalDate,
        isAutoRenew,
        noticePeriodDays,
        totalValue,
        currency,
        costPeriod,
        totalLicenses: Number(totalLicenses),
        assignedLicenses: assignedLicenses
          ? Number(assignedLicenses)
          : undefined,
        tenantId,
        createdById: userId,
      });

      res.status(201).json(license);
    } catch (error) {
      logger.error("Error in createLicense controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets a license by ID
   * @route GET /api/licenses/:id
   */
  async getLicense(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const license = await this.licenseService.getLicenseById(id);

      if (!license) {
        res.status(404).json({ error: `License with ID ${id} not found` });
        return;
      }

      res.status(200).json(license);
    } catch (error) {
      logger.error("Error in getLicense controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates a license
   * @route PUT /api/licenses/:id
   */
  async updateLicense(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const {
        name,
        description,
        licenseNumber,
        licenseType,
        status,
        vendor,
        purchaseDate,
        startDate,
        endDate,
        renewalType,
        renewalDate,
        isAutoRenew,
        noticePeriodDays,
        totalValue,
        currency,
        costPeriod,
        totalLicenses,
        assignedLicenses,
      } = req.body;

      // Parse dates if provided
      const parsedPurchaseDate = purchaseDate
        ? new Date(purchaseDate)
        : undefined;
      const parsedStartDate = startDate ? new Date(startDate) : undefined;
      const parsedEndDate = endDate ? new Date(endDate) : undefined;
      const parsedRenewalDate = renewalDate ? new Date(renewalDate) : undefined;

      // Update the license
      const license = await this.licenseService.updateLicense(id, {
        name,
        description,
        licenseNumber,
        licenseType: licenseType as LicenseType,
        status: status as LicenseStatus,
        vendor,
        purchaseDate: parsedPurchaseDate,
        startDate: parsedStartDate,
        endDate: parsedEndDate,
        renewalType,
        renewalDate: parsedRenewalDate,
        isAutoRenew,
        noticePeriodDays,
        totalValue,
        currency,
        costPeriod,
        totalLicenses:
          totalLicenses !== undefined ? Number(totalLicenses) : undefined,
        assignedLicenses:
          assignedLicenses !== undefined ? Number(assignedLicenses) : undefined,
      });

      res.status(200).json(license);
    } catch (error) {
      logger.error("Error in updateLicense controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes a license
   * @route DELETE /api/licenses/:id
   */
  async deleteLicense(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deleted = await this.licenseService.deleteLicense(id);

      if (!deleted) {
        res.status(404).json({ error: `License with ID ${id} not found` });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error("Error in deleteLicense controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets licenses for a tenant
   * @route GET /api/licenses/tenant/:tenantId
   */
  async getLicensesByTenant(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const page = req.query.page ? Number(req.query.page) : 1;
      const limit = req.query.limit ? Number(req.query.limit) : 20;

      const result = await this.licenseService.getLicensesByTenant(
        tenantId,
        page,
        limit
      );

      res.status(200).json(result);
    } catch (error) {
      logger.error("Error in getLicensesByTenant controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Searches for licenses
   * @route GET /api/licenses/search
   */
  async searchLicenses(req: Request, res: Response): Promise<void> {
    try {
      const {
        name,
        vendor,
        licenseType,
        status,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        page = 1,
        limit = 20,
      } = req.query;

      // Parse dates if provided
      const parsedStartDateFrom = startDateFrom
        ? new Date(startDateFrom as string)
        : undefined;
      const parsedStartDateTo = startDateTo
        ? new Date(startDateTo as string)
        : undefined;
      const parsedEndDateFrom = endDateFrom
        ? new Date(endDateFrom as string)
        : undefined;
      const parsedEndDateTo = endDateTo
        ? new Date(endDateTo as string)
        : undefined;

      // Search for licenses - tenant context is automatically applied in the repository
      const result = await this.licenseService.searchLicenses({
        name: name as string,
        vendor: vendor as string,
        licenseType: licenseType as LicenseType,
        status: status as LicenseStatus,
        startDateFrom: parsedStartDateFrom,
        startDateTo: parsedStartDateTo,
        endDateFrom: parsedEndDateFrom,
        endDateTo: parsedEndDateTo,
        page: Number(page),
        limit: Number(limit),
      });

      res.status(200).json(result);
    } catch (error) {
      logger.error("Error in searchLicenses controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets expiring licenses
   * @route GET /api/licenses/expiring/:tenantId
   */
  async getExpiringLicenses(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const days = req.query.days ? Number(req.query.days) : 30;

      const licenses = await this.licenseService.getExpiringLicenses(
        tenantId,
        days
      );

      res.status(200).json(licenses);
    } catch (error) {
      logger.error("Error in getExpiringLicenses controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets licenses with compliance issues
   * @route GET /api/licenses/compliance/:tenantId
   */
  async getLicensesWithComplianceIssues(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { tenantId } = req.params;

      const licenses =
        await this.licenseService.getLicensesWithComplianceIssues(tenantId);

      res.status(200).json(licenses);
    } catch (error) {
      logger.error("Error in getLicensesWithComplianceIssues controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets licenses with utilization issues
   * @route GET /api/licenses/utilization/:tenantId
   */
  async getLicensesWithUtilizationIssues(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { tenantId } = req.params;
      const threshold = req.query.threshold ? Number(req.query.threshold) : 70;

      const licenses =
        await this.licenseService.getLicensesWithUtilizationIssues(
          tenantId,
          threshold
        );

      res.status(200).json(licenses);
    } catch (error) {
      logger.error("Error in getLicensesWithUtilizationIssues controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Renews a license
   * @route POST /api/licenses/:id/renew
   */
  async renewLicense(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { newEndDate } = req.body;

      // Validate required fields
      if (!newEndDate) {
        res.status(400).json({ error: "newEndDate is required" });
        return;
      }

      // Parse the new end date
      const parsedNewEndDate = new Date(newEndDate);

      // Renew the license
      const license = await this.licenseService.renewLicense(
        id,
        parsedNewEndDate
      );

      res.status(200).json(license);
    } catch (error) {
      logger.error("Error in renewLicense controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates the compliance status of a license
   * @route POST /api/licenses/:id/compliance
   */
  async updateComplianceStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { complianceStatus } = req.body;

      // Validate required fields
      if (!complianceStatus) {
        res.status(400).json({ error: "complianceStatus is required" });
        return;
      }

      // Update compliance status
      const license = await this.licenseService.updateComplianceStatus(
        id,
        complianceStatus
      );

      res.status(200).json(license);
    } catch (error) {
      logger.error("Error in updateComplianceStatus controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  // License Entitlement endpoints

  /**
   * Creates a new license entitlement
   * @route POST /api/licenses/:licenseId/entitlements
   */
  async createEntitlement(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;
      const { name, description, included, quantity } = req.body;

      // Validate required fields
      if (!name) {
        res.status(400).json({ error: "name is required" });
        return;
      }

      // Create the entitlement
      const entitlement = await this.licenseService.createEntitlement({
        licenseId,
        name,
        description,
        included,
        quantity,
      });

      res.status(201).json(entitlement);
    } catch (error) {
      logger.error("Error in createEntitlement controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets entitlements for a license
   * @route GET /api/licenses/:licenseId/entitlements
   */
  async getEntitlements(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;

      const entitlements = await this.licenseService.getEntitlements(licenseId);

      res.status(200).json(entitlements);
    } catch (error) {
      logger.error("Error in getEntitlements controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates a license entitlement
   * @route PUT /api/licenses/:licenseId/entitlements/:id
   */
  async updateEntitlement(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId, id } = req.params;
      const { name, description, included, quantity } = req.body;

      // First get the entitlement to ensure it exists
      const entitlements = await this.licenseService.getEntitlements(licenseId);
      const existingEntitlement = entitlements.find((e) => e.id === id);

      if (!existingEntitlement) {
        res.status(404).json({ error: `Entitlement with ID ${id} not found` });
        return;
      }

      // Update the entitlement
      const entitlement = await this.licenseService.updateEntitlement(id, {
        name,
        description,
        included,
        quantity,
      });

      res.status(200).json(entitlement);
    } catch (error) {
      logger.error("Error in updateEntitlement controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes a license entitlement
   * @route DELETE /api/licenses/:licenseId/entitlements/:id
   */
  async deleteEntitlement(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deleted = await this.licenseService.deleteEntitlement(id);

      if (!deleted) {
        res.status(404).json({ error: `Entitlement with ID ${id} not found` });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error("Error in deleteEntitlement controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  // License Document endpoints

  /**
   * Creates a new license document
   * @route POST /api/licenses/:licenseId/documents
   */
  async createDocument(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;
      const { name, documentType, documentFormat, documentUri, isEncrypted } =
        req.body;

      // Validate required fields
      if (!name || !documentType || !documentFormat || !documentUri) {
        res.status(400).json({
          error:
            "Missing required fields: name, documentType, documentFormat, and documentUri are required",
        });
        return;
      }

      // Create the document
      const document = await this.licenseService.createDocument({
        licenseId,
        name,
        documentType: documentType as LicenseDocumentType,
        documentFormat: documentFormat as DocumentFormat,
        documentUri,
        isEncrypted,
      });

      res.status(201).json(document);
    } catch (error) {
      logger.error("Error in createDocument controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets documents for a license
   * @route GET /api/licenses/:licenseId/documents
   */
  async getDocuments(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;

      const documents = await this.licenseService.getDocuments(licenseId);

      res.status(200).json(documents);
    } catch (error) {
      logger.error("Error in getDocuments controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates a license document
   * @route PUT /api/licenses/:licenseId/documents/:id
   */
  async updateDocument(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId, id } = req.params;
      const { name, documentType, documentFormat, isEncrypted } = req.body;

      // First get the document to ensure it exists
      const documents = await this.licenseService.getDocuments(licenseId);
      const existingDocument = documents.find((d) => d.id === id);

      if (!existingDocument) {
        res.status(404).json({ error: `Document with ID ${id} not found` });
        return;
      }

      // Update the document
      const document = await this.licenseService.updateDocument(id, {
        name,
        documentType: documentType as LicenseDocumentType,
        documentFormat: documentFormat as DocumentFormat,
        isEncrypted,
      });

      res.status(200).json(document);
    } catch (error) {
      logger.error("Error in updateDocument controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes a license document
   * @route DELETE /api/licenses/:licenseId/documents/:id
   */
  async deleteDocument(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deleted = await this.licenseService.deleteDocument(id);

      if (!deleted) {
        res.status(404).json({ error: `Document with ID ${id} not found` });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error("Error in deleteDocument controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  // License Usage endpoints

  /**
   * Creates a new license usage record
   * @route POST /api/licenses/:licenseId/usage
   */
  async createUsageRecord(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;
      const { date, usageCount, totalAvailable } = req.body;

      // Validate required fields
      if (!date || usageCount === undefined || totalAvailable === undefined) {
        res.status(400).json({
          error:
            "Missing required fields: date, usageCount, and totalAvailable are required",
        });
        return;
      }

      // Parse the date
      const parsedDate = new Date(date);

      // Create the usage record
      const usage = await this.licenseService.createUsageRecord({
        licenseId,
        date: parsedDate,
        usageCount: Number(usageCount),
        totalAvailable: Number(totalAvailable),
      });

      res.status(201).json(usage);
    } catch (error) {
      logger.error("Error in createUsageRecord controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets usage history for a license
   * @route GET /api/licenses/:licenseId/usage
   */
  async getUsageHistory(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;
      const { startDate, endDate } = req.query;

      // Validate required fields
      if (!startDate || !endDate) {
        res.status(400).json({ error: "startDate and endDate are required" });
        return;
      }

      // Parse dates
      const parsedStartDate = new Date(startDate as string);
      const parsedEndDate = new Date(endDate as string);

      const usageHistory = await this.licenseService.getUsageHistory(
        licenseId,
        parsedStartDate,
        parsedEndDate
      );

      res.status(200).json(usageHistory);
    } catch (error) {
      logger.error("Error in getUsageHistory controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets the latest usage record for a license
   * @route GET /api/licenses/:licenseId/usage/latest
   */
  async getLatestUsage(req: Request, res: Response): Promise<void> {
    try {
      const { licenseId } = req.params;

      const latestUsage = await this.licenseService.getLatestUsage(licenseId);

      if (!latestUsage) {
        res.status(404).json({
          error: `No usage records found for license with ID ${licenseId}`,
        });
        return;
      }

      res.status(200).json(latestUsage);
    } catch (error) {
      logger.error("Error in getLatestUsage controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates license allocation based on usage data
   * @route POST /api/licenses/:id/allocation
   */
  async updateLicenseAllocation(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const license = await this.licenseService.updateLicenseAllocation(id);

      res.status(200).json(license);
    } catch (error) {
      logger.error("Error in updateLicenseAllocation controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }
}
