/**
 * Folder Controller
 * Handles HTTP requests for folder management
 */

import { Request, Response } from "express";
import { FolderService } from "../services/FolderService";
import { ContractAIService } from "../services/ContractAIService";
import { logger } from "../../infrastructure/logging/logger";

export class FolderController {
  constructor(
    private folderService: FolderService,
    private contractAIService: ContractAIService
  ) { }

  /**
   * Gets all folders with optional filtering
   * @route GET /api/folders
   */
  async getFolders(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      // Parse query parameters
      const { name, page = 0, limit = 50 } = req.query;

      // Build search parameters
      const params = {
        tenantId,
        name: name as string,
        // Remove createdById filter - folders should be shared within tenant
        // This allows users to see system-created folders from auto-grouping
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      // Get folders
      const result = await this.folderService.getFolders(params);

      // Enhance contract data with three-tier extraction
      const enhancedFolders = await Promise.all(
        result.folders.map(async (folderWithCount) => {
          const enhancedContracts = await Promise.all(
            folderWithCount.contracts.map(async (contract) => {
              try {
                // Get three-tier extraction data
                const extraction =
                  await this.contractAIService.getThreeTierExtraction(
                    contract.id,
                    tenantId
                  );

                // Ensure folderName is null if folderId is null (folder was deleted)
                const folderName = contract.folderId
                  ? contract.folderName
                  : null;

                // Return in exact same format as getContractsWithExtraction
                return extraction
                  ? {
                    contractId: contract.id,
                    fixedFields: extraction.fixedFields,
                    dynamicFields: extraction.dynamicFields,
                    specialFields: extraction.specialFields,
                    overallConfidence: extraction.overallConfidence,
                    extractionDate: extraction.extractionDate,
                    extractionVersion: extraction.extractionVersion,
                    folderId: extraction.folderId || contract.folderId,
                    folderName: folderName,
                    updatedAt: contract.updatedAt,
                  }
                  : {
                    contractId: contract.id,
                    fixedFields: null,
                    dynamicFields: null,
                    specialFields: null,
                    overallConfidence: null,
                    extractionDate: null,
                    extractionVersion: null,
                    folderId: contract.folderId,
                    folderName: folderName,
                    updatedAt: contract.updatedAt,
                  };
              } catch (error) {
                // If extraction fails, return contract without extraction data
                logger.warn(
                  `Failed to get extraction for contract ${contract.id}:`,
                  error
                );
                // Ensure folderName is null if folderId is null (folder was deleted)
                const folderName = contract.folderId
                  ? contract.folderName
                  : null;

                // If extraction fails, return null extraction data (same as getContractsWithExtraction)
                return {
                  contractId: contract.id,
                  fixedFields: null,
                  dynamicFields: null,
                  specialFields: null,
                  overallConfidence: null,
                  extractionDate: null,
                  extractionVersion: null,
                  folderId: contract.folderId,
                  folderName: folderName,
                  updatedAt: contract.updatedAt,
                };
              }
            })
          );

          return {
            ...folderWithCount,
            contracts: enhancedContracts,
            folder: {
              ...folderWithCount.folder.toDTO(),
              createdAt: folderWithCount.folder.createdAt.toISOString(),
              updatedAt: folderWithCount.folder.updatedAt.toISOString(),
            },
          };
        })
      );

      // Transform result with enhanced folders
      const transformedResult = {
        ...result,
        folders: enhancedFolders,
      };

      res.status(200).json(transformedResult);
    } catch (error) {
      logger.error("Error in get folders controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets all providers and their contracts grouped as virtual folders
   * @route GET /api/folders/by-providers
   */
  async getFoldersByProviders(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      logger.info(`Getting all providers and contracts for tenant ${tenantId}`);

      // Get all contract extractions for the tenant
      const extractionData =
        await this.contractAIService.getAllExtractionsForTenant(tenantId);

      // Group contracts by provider (supplier)
      const providerGroups = new Map<string, any[]>();

      // Get full extraction data for each contract
      const fullExtractions = await Promise.all(
        extractionData.map(async (basicExtraction) => {
          try {
            const fullExtraction =
              await this.contractAIService.getThreeTierExtraction(
                basicExtraction.contractId,
                tenantId
              );
            return fullExtraction;
          } catch (error) {
            logger.warn(
              `Failed to get full extraction for contract ${basicExtraction.contractId}:`,
              error
            );
            return null;
          }
        })
      );

      // Filter out null extractions and group by provider
      fullExtractions
        .filter((extraction) => extraction !== null)
        .forEach((extraction) => {
          const provider =
            extraction.fixedFields?.provider?.value || "Unknown Provider";
          // Extract supplier name (first word) for grouping - normalize to title case
          const rawSupplierName = provider.split(/\s+/)[0];
          const supplierName =
            rawSupplierName.charAt(0).toUpperCase() +
            rawSupplierName.slice(1).toLowerCase();

          if (!providerGroups.has(supplierName)) {
            providerGroups.set(supplierName, []);
          }

          providerGroups.get(supplierName)!.push(extraction);
        });

      // Convert to folder-like structure
      const virtualFolders = Array.from(providerGroups.entries()).map(
        ([supplierName, contracts]) => {
          // Check if there's an actual folder for this supplier
          const existingFolder = contracts.find((c) => c.folderId)?.folderName;

          return {
            folder: {
              id: `virtual-${supplierName}`, // Virtual ID for providers without folders
              name: supplierName,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              tenantId,
              createdById: null,
              isVirtual: !existingFolder, // Mark as virtual if no actual folder exists
            },
            contractCount: contracts.length,
            contracts: contracts,
          };
        }
      );

      // Sort by supplier name
      virtualFolders.sort((a, b) => a.folder.name.localeCompare(b.folder.name));

      const result = {
        folders: virtualFolders,
        total: virtualFolders.length,
        page: 0,
        limit: virtualFolders.length,
      };

      res.status(200).json(result);
    } catch (error) {
      logger.error("Error in get folders by providers controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets folders for autocomplete
   * @route GET /api/folders/autocomplete
   */
  async getFoldersForAutocomplete(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const { search } = req.query;

      // Get folders for autocomplete
      const folders = await this.folderService.getFoldersForAutocomplete(
        tenantId,
        search as string
      );

      // Transform to simple format for autocomplete
      const foldersDTO = folders.map((folder) => ({
        id: folder.id,
        name: folder.name,
      }));

      res.status(200).json({ folders: foldersDTO });
    } catch (error) {
      logger.error("Error in get folders for autocomplete controller:", {
        error,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets a folder by ID
   * @route GET /api/folders/:id
   */
  async getFolder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user!.tenantId;

      // Get folder
      const folder = await this.folderService.getFolder(id, tenantId);

      if (!folder) {
        res.status(404).json({ error: "Folder not found" });
        return;
      }

      // Transform to DTO
      const folderDTO = {
        ...folder.toDTO(),
        createdAt: folder.createdAt.toISOString(),
        updatedAt: folder.updatedAt.toISOString(),
      };

      res.status(200).json(folderDTO);
    } catch (error) {
      logger.error("Error in get folder controller:", { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a new folder - DISABLED: Folders are now virtual and calculated at runtime
   * @route POST /api/folders
   */
  async createFolder(req: Request, res: Response): Promise<void> {
    res.status(400).json({
      error:
        "Folder creation disabled - folders are now virtual and calculated at runtime based on provider names",
    });
  }

  /**
   * Updates an existing folder - DISABLED: Folders are now virtual
   * @route PUT /api/folders/:id
   */
  async updateFolder(req: Request, res: Response): Promise<void> {
    res.status(400).json({
      error:
        "Folder updates disabled - folders are now virtual and calculated at runtime",
    });
  }

  /**
   * Deletes a folder - DISABLED: Folders are now virtual
   * @route DELETE /api/folders/:id
   */
  async deleteFolder(req: Request, res: Response): Promise<void> {
    res.status(400).json({
      error:
        "Folder deletion disabled - folders are now virtual and calculated at runtime",
    });
  }

  /**
   * Moves contracts to a folder - DISABLED: Folders are now virtual
   * @route POST /api/folders/move-contracts
   */
  async moveContractsToFolder(req: Request, res: Response): Promise<void> {
    res.status(400).json({
      error:
        "Moving contracts to folders disabled - folders are now virtual and calculated at runtime",
    });
  }
}
