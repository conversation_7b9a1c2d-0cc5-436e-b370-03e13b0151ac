/**
 * License Optimization Controller
 * Handles API requests for license optimization features
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

import { logger } from '../../infrastructure/logging/logger';
import { LicenseOptimizationService, OptimizationType, OptimizationPriority } from '../../infrastructure/ai/LicenseOptimizationService';

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize services
const licenseOptimizationService = new LicenseOptimizationService(prisma);

/**
 * Analyzes license usage and generates optimization recommendations
 * @param req - Express request
 * @param res - Express response
 */
export async function analyzeLicenseUsage(req: Request, res: Response): Promise<void> {
  try {
    const tenantId = req.tenantId;

    // Check if tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: {
        id: tenantId,
      },
    });

    if (!tenant) {
      res.status(404).json({
        success: false,
        error: 'Tenant not found',
      });
      return;
    }

    // Analyze license usage
    const result = await licenseOptimizationService.analyzeLicenseUsage(tenantId);

    res.status(200).json({
      success: true,
      result,
    });
  } catch (error) {
    logger.error('Error analyzing license usage', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to analyze license usage',
      message: (error as Error).message,
    });
  }
}

/**
 * Gets the latest optimization result for a tenant
 * @param req - Express request
 * @param res - Express response
 */
export async function getOptimizationResult(req: Request, res: Response): Promise<void> {
  try {
    const tenantId = req.tenantId;

    // Get optimization result
    const result = await licenseOptimizationService.getOptimizationResult(tenantId);

    if (!result) {
      res.status(404).json({
        success: false,
        error: 'Optimization result not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      result,
    });
  } catch (error) {
    logger.error('Error getting optimization result', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to get optimization result',
      message: (error as Error).message,
    });
  }
}

/**
 * Gets optimization types and priorities
 * @param req - Express request
 * @param res - Express response
 */
export async function getOptimizationMetadata(req: Request, res: Response): Promise<void> {
  try {
    // Return optimization types and priorities
    res.status(200).json({
      success: true,
      optimizationTypes: Object.values(OptimizationType),
      optimizationPriorities: Object.values(OptimizationPriority),
    });
  } catch (error) {
    logger.error('Error getting optimization metadata', { error });
    res.status(500).json({
      success: false,
      error: 'Failed to get optimization metadata',
      message: (error as Error).message,
    });
  }
}

/**
 * Gets recommendations for a specific license
 * @param req - Express request
 * @param res - Express response
 */
export async function getLicenseRecommendations(req: Request, res: Response): Promise<void> {
  try {
    const tenantId = req.tenantId;
    const { licenseId } = req.params;

    // Get optimization result
    const result = await licenseOptimizationService.getOptimizationResult(tenantId);

    if (!result) {
      res.status(404).json({
        success: false,
        error: 'Optimization result not found',
      });
      return;
    }

    // Filter recommendations for the specified license
    const recommendations = result.recommendations.filter(rec => rec.licenseId === licenseId);

    if (recommendations.length === 0) {
      res.status(404).json({
        success: false,
        error: 'No recommendations found for the specified license',
      });
      return;
    }

    res.status(200).json({
      success: true,
      recommendations,
    });
  } catch (error) {
    logger.error('Error getting license recommendations', { error, licenseId: req.params.licenseId });
    res.status(500).json({
      success: false,
      error: 'Failed to get license recommendations',
      message: (error as Error).message,
    });
  }
}
