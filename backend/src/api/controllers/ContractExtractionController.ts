/**
 * Contract Extraction Controller
 * Handles API requests for three-tier contract extraction data
 */

import { Request, Response } from "express";
import { ContractAIService } from "../services/ContractAIService";
import { ContractRelationshipService } from "../services/ContractRelationshipService";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { ContractExtraction } from "../../domain/contracts/ContractExtraction";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";

export class ContractExtractionController {
  private contractAIService: ContractAIService;
  private contractExtractionRepository: ContractExtractionRepository;
  private contractRelationshipService: ContractRelationshipService;
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.contractAIService = new ContractAIService(prisma);
    this.contractExtractionRepository = new ContractExtractionRepository(
      prisma
    );
    this.contractRelationshipService = new ContractRelationshipService();
    this.prisma = prisma;
  }

  /**
   * Gets three-tier extraction data for a contract with basic contract info
   * @route GET /api/contracts/:contractId/extraction
   */
  async getContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;

      const extraction = await this.contractAIService.getThreeTierExtraction(
        contractId,
        tenantId
      );

      if (!extraction) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Get basic contract information to include with extraction data
      const basicContractInfo =
        await this.contractAIService.getBasicContractInfo(contractId, tenantId);

      // Combine extraction data with basic contract info
      const response = {
        ...extraction,
        contractInfo: basicContractInfo,
      };

      res.status(200).json(response);
    } catch (error) {
      logger.error("Error getting contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Triggers three-tier extraction for a contract
   * @route POST /api/contracts/:contractId/extraction
   */
  async createContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;
      const { documentBuffer, fileName } = req.body;

      if (!documentBuffer || !fileName) {
        res
          .status(400)
          .json({ error: "Document buffer and file name are required" });
        return;
      }

      // Convert base64 string back to buffer if needed
      const buffer = Buffer.isBuffer(documentBuffer)
        ? documentBuffer
        : Buffer.from(documentBuffer, "base64");

      const result = await this.contractAIService.extractAndSaveThreeTierData(
        buffer,
        fileName,
        contractId,
        tenantId
      );

      res.status(201).json({
        success: true,
        extractionId: result.extractionId,
        data: {
          fixedFields: result.fixedFields,
          dynamicFields: result.dynamicFields,
          specialFields: result.specialFields,
          documentSummary: result.documentSummary,
          analysisFields: result.analysisFields,
        },
      });
    } catch (error) {
      logger.error("Error creating contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates three-tier extraction data for a contract
   * @route PUT /api/contracts/:contractId/extraction
   */
  async updateContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;
      const { fixedFields, dynamicFields, specialFields } = req.body;

      // Get existing extraction
      const existing = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );
      if (!existing) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Update extraction
      const updated = await this.contractExtractionRepository.update(
        existing.id,
        tenantId,
        {
          fixedFields,
          dynamicFields,
          specialFields,
          extractionVersion: "1.0",
        }
      );

      res.status(200).json(updated.toDTO());
    } catch (error) {
      logger.error("Error updating contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Partially updates specific fields in three-tier extraction data
   * @route PATCH /api/contracts/:contractId/extraction
   */
  async patchContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;
      const { fieldUpdates } = req.body;

      // Validate request body
      if (!fieldUpdates || !Array.isArray(fieldUpdates)) {
        res.status(400).json({
          error: "fieldUpdates array is required",
        });
        return;
      }

      // Get existing extraction
      const existing = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );
      if (!existing) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Clone existing data
      const updatedFixedFields = { ...existing.fixedFields } as any;
      const updatedDynamicFields = { ...existing.dynamicFields } as any;
      const updatedSpecialFields = { ...existing.specialFields } as any;

      // Apply field updates
      for (const update of fieldUpdates) {
        const { fieldType, fieldKey, newKey, newValue } = update;

        if (!fieldType || !fieldKey || newValue === undefined) {
          continue; // Skip invalid updates
        }

        switch (fieldType) {
          case "fixed":
            // Create field with confidence -1 for manual edits
            const fixedField = {
              value: newValue,
              confidence: -1,
            };

            if (newKey && newKey !== fieldKey) {
              // Key changed - remove old and add new
              delete updatedFixedFields[fieldKey];
              updatedFixedFields[newKey] = fixedField;
            } else {
              // Only value changed
              updatedFixedFields[fieldKey] = fixedField;
            }
            break;

          case "dynamic":
            // Create field with confidence -1 for manual edits
            const dynamicField = {
              value: newValue,
              confidence: -1,
              description: update.description || "",
            };

            if (newKey && newKey !== fieldKey) {
              // Key changed - remove old and add new
              delete updatedDynamicFields[fieldKey];
              updatedDynamicFields[newKey] = dynamicField;
            } else {
              // Only value changed
              updatedDynamicFields[fieldKey] = dynamicField;
            }
            break;

          case "special":
            const { vendor } = update;
            if (!vendor || !updatedSpecialFields[vendor]) {
              continue; // Skip if vendor not specified or doesn't exist
            }

            // Create field with confidence -1 for manual edits
            const specialField = {
              value: newValue,
              confidence: -1,
            };

            // Find the field in the nested category structure
            let fieldFound = false;
            for (const categoryName in updatedSpecialFields[vendor]) {
              const category = updatedSpecialFields[vendor][categoryName];
              if (
                category &&
                typeof category === "object" &&
                fieldKey in category
              ) {
                if (newKey && newKey !== fieldKey) {
                  // Key changed - remove old and add new
                  delete category[fieldKey];
                  category[newKey] = specialField;
                } else {
                  // Only value changed
                  category[fieldKey] = specialField;
                }
                fieldFound = true;
                break;
              }
            }

            // If field not found in any category, log warning
            if (!fieldFound) {
              console.warn(
                `Field ${fieldKey} not found in any category for vendor ${vendor}`
              );
            }
            break;
        }
      }

      // Update extraction with modified fields
      const updated = await this.contractExtractionRepository.update(
        existing.id,
        tenantId,
        {
          fixedFields: updatedFixedFields,
          dynamicFields: updatedDynamicFields,
          specialFields: updatedSpecialFields,
          extractionVersion: "1.0",
        }
      );

      res.status(200).json(updated.toDTO());
    } catch (error) {
      logger.error("Error patching contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes three-tier extraction data for a contract
   * @route DELETE /api/contracts/:contractId/extraction
   */
  async deleteContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;

      // Get existing extraction
      const existing = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );
      if (!existing) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      await this.contractExtractionRepository.delete(existing.id, tenantId);

      res.status(204).send();
    } catch (error) {
      logger.error("Error deleting contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Refreshes contract analysis by clearing all existing data and re-running complete analysis workflow
   * @route POST /api/contracts/:contractId/extraction/refresh
   */
  async refreshContractExtraction(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;

      logger.info(`Starting contract analysis refresh for contract: ${contractId}`);

      // Step 1: Get the original document from ContractVersion directly
      const contractVersion = await this.prisma.contractVersion.findFirst({
        where: { contractId },
        orderBy: { versionNumber: 'desc' },
        select: {
          documentContent: true,
          documentName: true,
          mimeType: true,
          documentSize: true
        }
      });

      if (!contractVersion || !contractVersion.documentContent) {
        res.status(404).json({ error: "Original contract document not found" });
        return;
      }

      // Step 2: Delete existing extraction data if it exists
      const existing = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );
      if (existing) {
        logger.info(`Deleting existing extraction data for contract: ${contractId}`);
        await this.contractExtractionRepository.delete(existing.id, tenantId);
      }

      // Step 3: Clear OCR data from ContractVersion
      logger.info(`Clearing OCR data for contract: ${contractId}`);
      await this.prisma.contractVersion.updateMany({
        where: { contractId },
        data: {
          ocrText: null,
          ocrProcessedAt: null,
          ocrConfidence: null,
          ocrStatus: null,
          ocrMetadata: {}
        }
      });

      // Step 4: Re-run complete analysis workflow
      logger.info(`Re-running complete analysis workflow for contract: ${contractId}`);
      const result = await this.contractAIService.extractAndSaveThreeTierData(
        Buffer.from(contractVersion.documentContent),
        contractVersion.documentName,
        contractId,
        tenantId
      );

      // Step 5: Get basic contract information to include with extraction data
      const basicContractInfo =
        await this.contractAIService.getBasicContractInfo(contractId, tenantId);

      // Combine extraction data with basic contract info
      const response = {
        ...result,
        contractInfo: basicContractInfo,
      };

      logger.info(`Successfully refreshed contract analysis for contract: ${contractId}`);
      res.status(200).json(response);
    } catch (error) {
      logger.error("Error refreshing contract extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets extraction statistics for a tenant
   * @route GET /api/extractions/stats
   */
  async getExtractionStats(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      const stats = await this.contractExtractionRepository.getExtractionStats(
        tenantId
      );

      res.status(200).json(stats);
    } catch (error) {
      logger.error("Error getting extraction stats:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets extractions by confidence threshold
   * @route GET /api/extractions/by-confidence
   */
  async getExtractionsByConfidence(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const { minConfidence, maxConfidence } = req.query;

      const min = minConfidence ? parseFloat(minConfidence as string) : 0;
      const max = maxConfidence
        ? parseFloat(maxConfidence as string)
        : undefined;

      const extractions =
        await this.contractExtractionRepository.getByConfidenceThreshold(
          tenantId,
          min,
          max
        );

      res.status(200).json({
        extractions: extractions.map((e) => e.toDTO()),
        total: extractions.length,
      });
    } catch (error) {
      logger.error("Error getting extractions by confidence:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Generates document summary for a contract
   * @route POST /api/contracts/:contractId/extraction/generate-summary
   */
  async generateDocumentSummary(req: Request, res: Response): Promise<void> {
    try {
      const { contractId } = req.params;
      const tenantId = req.user!.tenantId;
      const { documentBuffer, fileName } = req.body;

      if (!documentBuffer || !fileName) {
        res
          .status(400)
          .json({ error: "Document buffer and file name are required" });
        return;
      }

      // Convert base64 string back to buffer if needed
      const buffer = Buffer.isBuffer(documentBuffer)
        ? documentBuffer
        : Buffer.from(documentBuffer, "base64");

      // Generate document summary
      const summary =
        await this.contractAIService.generateContractSummaryFromDocument(
          buffer,
          fileName
        );

      // Update the extraction record with the new summary
      const existing = await this.contractExtractionRepository.getByContractId(
        contractId,
        tenantId
      );

      if (existing) {
        const documentSummary = {
          value: summary,
          confidence: 0.85, // Default confidence for generated summaries
          extractionDate: new Date(),
          processingTimeMs: Date.now(),
        };

        await this.contractExtractionRepository.update(existing.id, tenantId, {
          documentSummary,
        });

        res.status(200).json({
          success: true,
          summary,
          documentSummary,
        });
      } else {
        // Return summary without updating database if extraction doesn't exist
        res.status(200).json({
          success: true,
          summary,
          note: "Summary generated but not saved (no extraction record found)",
        });
      }
    } catch (error) {
      logger.error("Error generating document summary:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets contract relationships for a specific supplier
   * @route GET /api/suppliers/:supplierName/contract-relationships
   */
  async getSupplierContractRelationships(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { supplierName } = req.params;
      const tenantId = req.user!.tenantId;

      // Get query parameters for relationship criteria
      const {
        contractIdMatching = "true",
        startDateProximity = "true",
        executionDateCorrelation = "true",
        signatureDateAlignment = "true",
        signatureNameMatching = "true",
        referencedDocuments = "true",
        agreementTypeHierarchy = "true",
      } = req.query;

      logger.info(
        `Fetching contract relationships for supplier: ${supplierName}`
      );

      // Get all contract extractions for the supplier
      const extractions =
        await this.contractExtractionRepository.getByTenantWithFilters(
          tenantId,
          { provider: supplierName, limit: 1000 } // Get all contracts for the supplier
        );

      if (extractions.extractions.length === 0) {
        res.status(404).json({
          error: "No contracts found for the specified supplier",
          supplier: supplierName,
        });
        return;
      }

      // Configure relationship analysis criteria
      const criteria = {
        contractIdMatching: contractIdMatching === "true",
        startDateProximity: startDateProximity === "true",
        executionDateCorrelation: executionDateCorrelation === "true",
        signatureDateAlignment: signatureDateAlignment === "true",
        signatureNameMatching: signatureNameMatching === "true",
        referencedDocuments: referencedDocuments === "true",
        agreementTypeHierarchy: agreementTypeHierarchy === "true",
      };

      // Analyze relationships
      const analysis =
        await this.contractRelationshipService.analyzeSupplierContractRelationships(
          extractions.extractions,
          criteria
        );

      // Add manual reporting relationships to the analysis
      const manualRelationships = this.extractManualReportingRelationships(
        extractions.extractions
      );

      // Merge manual relationships with analyzed relationships
      const allRelationships = [
        ...analysis.relationships,
        ...manualRelationships,
      ];

      // Update the analysis with combined relationships
      const enhancedAnalysis = {
        ...analysis,
        relationships: allRelationships,
      };

      logger.info(
        `Found ${allRelationships.length} total relationships (${analysis.relationships.length} analyzed + ${manualRelationships.length} manual) among ${analysis.nodes.length} contracts`
      );

      res.json({
        success: true,
        supplier: supplierName,
        totalContracts: enhancedAnalysis.nodes.length,
        totalRelationships: allRelationships.length,
        totalClusters: enhancedAnalysis.clusters.length,
        analysis: enhancedAnalysis,
      });
    } catch (error) {
      logger.error("Error analyzing supplier contract relationships:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Extracts manual reporting relationships from contract extractions
   */
  private extractManualReportingRelationships(
    extractions: ContractExtraction[]
  ): any[] {
    const manualRelationships: any[] = [];

    for (const extraction of extractions) {
      if (extraction.reportingTo) {
        // Find the parent contract to verify it exists in this supplier's contracts
        const parentExists = extractions.some(
          (e) => e.contractId === extraction.reportingTo
        );

        if (parentExists) {
          manualRelationships.push({
            fromContractId: extraction.reportingTo, // Parent
            toContractId: extraction.contractId, // Child
            relationshipType: "MANUAL",
            strength: 1.0, // Manual relationships have full strength
            criteria: ["MANUAL_ASSIGNMENT"],
            details: "Manually assigned reporting relationship",
          });
        }
      }
    }

    return manualRelationships;
  }
}
