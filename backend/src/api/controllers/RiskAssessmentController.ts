/**
 * Risk Assessment Controller
 * Handles API requests for risk assessment features
 */

import { Request, Response } from "express";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";

import { logger } from "../../infrastructure/logging/logger";
import {
  RiskAssessmentService,
  RiskLevel,
  RiskCategory,
} from "../../infrastructure/ai/RiskAssessmentService";
import { SecureAIProcessor } from "../../infrastructure/ai/SecureAIProcessor";
import { EmbeddingService } from "../../infrastructure/ai/EmbeddingService";
import { VectorRepository } from "../../infrastructure/repositories/VectorRepository";
import { validateDocumentId } from "../../infrastructure/ai/util/sanitizer";

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize services
const secureAIProcessor = new SecureAIProcessor();
const vectorRepository = new VectorRepository(prisma);
const embeddingService = new EmbeddingService(vectorRepository);
const riskAssessmentService = new RiskAssessmentService(
  prisma,
  secureAIProcessor,
  embeddingService
);

/**
 * Schema for risk assessment request
 */
const riskAssessmentRequestSchema = z.object({
  contractId: z.string().min(1),
  contractText: z.string().optional(),
});

/**
 * Assesses risks in a contract
 * @param req - Express request
 * @param res - Express response
 */
export async function assessRisks(req: Request, res: Response): Promise<void> {
  try {
    const tenantId = req.tenantId;

    // Validate request body
    const validationResult = riskAssessmentRequestSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: "Invalid request data",
        details: validationResult.error.format(),
      });
      return;
    }

    const { contractId, contractText } = validationResult.data;

    // Validate contract ID
    const validContractId = validateDocumentId(contractId);
    if (!validContractId) {
      res.status(400).json({
        success: false,
        error: "Invalid contract ID",
      });
      return;
    }

    // Check if contract exists and belongs to tenant
    const contract = await prisma.contract.findFirst({
      where: {
        id: validContractId,
        tenantId,
      },
    });

    if (!contract) {
      res.status(404).json({
        success: false,
        error: "Contract not found",
      });
      return;
    }

    // Assess risks
    const assessment = await riskAssessmentService.assessRisks(
      validContractId,
      tenantId,
      contractText
    );

    res.status(200).json({
      success: true,
      assessment,
    });
  } catch (error) {
    logger.error("Error assessing risks", { error });
    res.status(500).json({
      success: false,
      error: "Failed to assess risks",
      message: (error as Error).message,
    });
  }
}

/**
 * Gets a risk assessment for a contract
 * @param req - Express request
 * @param res - Express response
 */
export async function getRiskAssessment(
  req: Request,
  res: Response
): Promise<void> {
  try {
    const tenantId = req.tenantId;
    const { contractId } = req.params;

    // Validate contract ID
    const validContractId = validateDocumentId(contractId);
    if (!validContractId) {
      res.status(400).json({
        success: false,
        error: "Invalid contract ID",
      });
      return;
    }

    // Get risk assessment
    const assessment = await riskAssessmentService.getRiskAssessment(
      validContractId,
      tenantId
    );

    if (!assessment) {
      res.status(404).json({
        success: false,
        error: "Risk assessment not found",
      });
      return;
    }

    res.status(200).json({
      success: true,
      assessment,
    });
  } catch (error) {
    logger.error("Error getting risk assessment", {
      error,
      contractId: req.params.contractId,
    });
    res.status(500).json({
      success: false,
      error: "Failed to get risk assessment",
      message: (error as Error).message,
    });
  }
}

/**
 * Gets risk levels and categories
 * @param req - Express request
 * @param res - Express response
 */
export async function getRiskMetadata(
  req: Request,
  res: Response
): Promise<void> {
  try {
    // Return risk levels and categories
    res.status(200).json({
      success: true,
      riskLevels: Object.values(RiskLevel),
      riskCategories: Object.values(RiskCategory),
    });
  } catch (error) {
    logger.error("Error getting risk metadata", { error });
    res.status(500).json({
      success: false,
      error: "Failed to get risk metadata",
      message: (error as Error).message,
    });
  }
}

/**
 * Gets risk factors for a contract
 * @param req - Express request
 * @param res - Express response
 */
export async function getRiskFactors(
  req: Request,
  res: Response
): Promise<void> {
  try {
    const tenantId = req.tenantId;
    const { contractId } = req.params;

    // Validate contract ID
    const validContractId = validateDocumentId(contractId);
    if (!validContractId) {
      res.status(400).json({
        success: false,
        error: "Invalid contract ID",
      });
      return;
    }

    // Get risk assessment
    const assessment = await riskAssessmentService.getRiskAssessment(
      validContractId,
      tenantId
    );

    if (!assessment) {
      res.status(404).json({
        success: false,
        error: "Risk assessment not found",
      });
      return;
    }

    // Return risk factors
    res.status(200).json({
      success: true,
      riskFactors: assessment.riskFactors,
    });
  } catch (error) {
    logger.error("Error getting risk factors", {
      error,
      contractId: req.params.contractId,
    });
    res.status(500).json({
      success: false,
      error: "Failed to get risk factors",
      message: (error as Error).message,
    });
  }
}
