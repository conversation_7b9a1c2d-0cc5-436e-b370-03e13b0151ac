/**
 * Test Extraction Controller
 * Provides endpoints for testing the enhanced three-tier extraction system
 */

import { Request, Response } from "express";
import { ContractAIService } from "../services/ContractAIService";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";
import multer from "multer";
import path from "path";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.pdf', '.doc', '.docx', '.txt'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, DOC, DOCX, and TXT files are allowed.'));
    }
  },
});

export class TestExtractionController {
  private contractAIService: ContractAIService;

  constructor(prisma: PrismaClient) {
    this.contractAIService = new ContractAIService(prisma);
  }

  /**
   * Test the enhanced three-tier extraction system with a sample document
   */
  async testEnhancedExtraction(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Testing enhanced three-tier extraction system");

      // Check if file was uploaded
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: "No file uploaded. Please upload a contract document.",
        });
        return;
      }

      const { buffer, originalname } = req.file;
      const startTime = Date.now();

      logger.info(`Processing test document: ${originalname}`);

      // Test the enhanced analyzeContractDocument method
      const analysisResult = await this.contractAIService.analyzeContractDocument(
        buffer,
        originalname
      );

      const processingTime = Date.now() - startTime;

      // Extract the raw three-tier data
      const rawData = (analysisResult as any).rawResult || {};

      // Prepare comprehensive response
      const response = {
        success: true,
        processingTimeMs: processingTime,
        fileName: originalname,
        fileSize: buffer.length,
        extractionResults: {
          // Fixed Fields (9 mandatory fields)
          fixedFields: rawData.fixed_fields || {},

          // Dynamic Fields (comprehensive contract-specific metadata)
          dynamicFields: rawData.dynamic_fields || {},

          // Special Fields (vendor-specific)
          specialFields: rawData.special_fields || {},

          // Analysis metadata
          analysisMetadata: {
            totalFixedFields: Object.keys(rawData.fixed_fields || {}).length,
            totalDynamicFields: Object.keys(rawData.dynamic_fields || {}).length,
            totalSpecialFields: Object.keys(rawData.special_fields || {}).reduce(
              (total, vendor) => total + Object.keys(rawData.special_fields[vendor] || {}).length,
              0
            ),
            overallConfidence: this.calculateOverallConfidence(rawData),
            highConfidenceFields: this.getHighConfidenceFields(rawData),
            lowConfidenceFields: this.getLowConfidenceFields(rawData),
          },
        },

        // Legacy format for backward compatibility
        legacyFormat: analysisResult,
      };

      logger.info(`Enhanced extraction test completed successfully in ${processingTime}ms`);
      logger.info(`Extracted ${response.extractionResults.analysisMetadata.totalDynamicFields} dynamic fields`);

      res.json(response);
    } catch (error) {
      logger.error("Error in enhanced extraction test:", error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  }

  /**
   * Test extraction with a sample text contract
   */
  async testWithSampleContract(req: Request, res: Response): Promise<void> {
    try {
      logger.info("Testing with sample contract text");

      // Sample contract text for testing
      const sampleContractText = `
MASTER SERVICES AGREEMENT

This Master Services Agreement ("Agreement") is entered into on January 1, 2024, between:

Provider: TechCorp Solutions Inc., a Delaware corporation
Client: Enterprise Customer LLC, a California corporation

Product: Cloud Software Platform and Professional Services

Contract ID: MSA-2024-001
Total Contract Value: USD 2,500,000.00
Contract Classification: SW_SAAS

Term: This Agreement shall commence on January 1, 2024, and shall continue until December 31, 2026.

Payment Terms: Net 30 days from invoice date. Late payments subject to 1.5% monthly interest.

Service Level Agreement: 99.9% uptime guarantee with 4-hour response time for critical issues.

Liability Limitation: Provider's liability is limited to 12 months of fees paid under this Agreement.

Governing Law: This Agreement shall be governed by the laws of the State of California.

Confidentiality: Both parties agree to maintain confidentiality for 5 years after termination.

Termination: Either party may terminate with 90 days written notice. Termination for cause requires 30 days notice.

Auto-Renewal: This Agreement automatically renews for successive 1-year terms unless terminated.

Data Retention: Customer data will be retained for 7 years after contract termination.

Intellectual Property: Customer retains ownership of all data and content provided.

Force Majeure: Performance excused for acts of God, war, natural disasters, and government actions.

Dispute Resolution: Binding arbitration in San Francisco, California.

Insurance: Provider maintains $5,000,000 professional liability insurance.

Security Standards: SOC 2 Type II compliance required with annual audits.

Training: Provider will provide 40 hours of training included in the contract price.

Support Level: Premier support with 24/7 availability and dedicated account manager.

Data Processing: GDPR and CCPA compliant data processing with data subject rights support.

Renewal Notice: 90 days written notice required to prevent auto-renewal.

Amendment: Any amendments must be in writing and signed by both parties.
      `;

      // Convert sample text to buffer
      const buffer = Buffer.from(sampleContractText, 'utf-8');
      const startTime = Date.now();

      logger.info("Processing sample contract text");

      // Test the enhanced analyzeContractDocument method
      const analysisResult = await this.contractAIService.analyzeContractDocument(
        buffer,
        "sample-contract.txt"
      );

      const processingTime = Date.now() - startTime;

      // Extract the raw three-tier data
      const rawData = (analysisResult as any).rawResult || {};

      // Prepare comprehensive response
      const response = {
        success: true,
        processingTimeMs: processingTime,
        sampleContract: true,
        extractionResults: {
          fixedFields: rawData.fixed_fields || {},
          dynamicFields: rawData.dynamic_fields || {},
          specialFields: rawData.special_fields || {},
          analysisMetadata: {
            totalFixedFields: Object.keys(rawData.fixed_fields || {}).length,
            totalDynamicFields: Object.keys(rawData.dynamic_fields || {}).length,
            totalSpecialFields: Object.keys(rawData.special_fields || {}).reduce(
              (total, vendor) => total + Object.keys(rawData.special_fields[vendor] || {}).length,
              0
            ),
            overallConfidence: this.calculateOverallConfidence(rawData),
            highConfidenceFields: this.getHighConfidenceFields(rawData),
            lowConfidenceFields: this.getLowConfidenceFields(rawData),
          },
        },
        legacyFormat: analysisResult,
      };

      logger.info(`Sample contract extraction completed in ${processingTime}ms`);
      logger.info(`Extracted ${response.extractionResults.analysisMetadata.totalDynamicFields} dynamic fields`);

      res.json(response);
    } catch (error) {
      logger.error("Error in sample contract test:", error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(rawData: any): number {
    const allFields: any[] = [];

    // Collect all fields with confidence scores
    if (rawData.fixed_fields) {
      Object.values(rawData.fixed_fields).forEach((field: any) => {
        if (field && typeof field.confidence === 'number') {
          allFields.push(field.confidence);
        }
      });
    }

    if (rawData.dynamic_fields) {
      Object.values(rawData.dynamic_fields).forEach((field: any) => {
        if (field && typeof field.confidence === 'number') {
          allFields.push(field.confidence);
        }
      });
    }

    if (allFields.length === 0) return 0;

    return allFields.reduce((sum, conf) => sum + conf, 0) / allFields.length;
  }

  /**
   * Get high confidence fields (>= 0.8)
   */
  private getHighConfidenceFields(rawData: any): Array<{ field: string; confidence: number; value: string }> {
    const highConfFields: Array<{ field: string; confidence: number; value: string }> = [];

    // Check fixed fields
    if (rawData.fixed_fields) {
      Object.entries(rawData.fixed_fields).forEach(([key, field]: [string, any]) => {
        if (field && field.confidence >= 0.8) {
          highConfFields.push({ field: key, confidence: field.confidence, value: field.value });
        }
      });
    }

    // Check dynamic fields
    if (rawData.dynamic_fields) {
      Object.entries(rawData.dynamic_fields).forEach(([key, field]: [string, any]) => {
        if (field && field.confidence >= 0.8) {
          highConfFields.push({ field: key, confidence: field.confidence, value: field.value });
        }
      });
    }

    return highConfFields;
  }

  /**
   * Get low confidence fields (< 0.5)
   */
  private getLowConfidenceFields(rawData: any): Array<{ field: string; confidence: number; value: string }> {
    const lowConfFields: Array<{ field: string; confidence: number; value: string }> = [];

    // Check fixed fields
    if (rawData.fixed_fields) {
      Object.entries(rawData.fixed_fields).forEach(([key, field]: [string, any]) => {
        if (field && field.confidence < 0.5 && field.value !== "N/A") {
          lowConfFields.push({ field: key, confidence: field.confidence, value: field.value });
        }
      });
    }

    // Check dynamic fields
    if (rawData.dynamic_fields) {
      Object.entries(rawData.dynamic_fields).forEach(([key, field]: [string, any]) => {
        if (field && field.confidence < 0.5 && field.value !== "N/A") {
          lowConfFields.push({ field: key, confidence: field.confidence, value: field.value });
        }
      });
    }

    return lowConfFields;
  }

  /**
   * Get the multer upload middleware
   */
  static getUploadMiddleware() {
    return upload.single('document');
  }
}
