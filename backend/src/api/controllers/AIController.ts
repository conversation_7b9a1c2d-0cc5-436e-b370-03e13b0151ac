/**
 * Unified AI Controller
 * Handles all AI-related functionality including:
 * - Document analysis and metadata extraction
 * - Chat functionality
 * - Agreement Analysis
 * - RAG (Retrieval Augmented Generation)
 */

import { Request, Response } from "express";
import { z } from "zod";
import { PrismaClient, SecurityClassification } from "@prisma/client";
import { GoogleGenAI } from "@google/genai";
import { v4 as uuidv4 } from "uuid";
import multer from "multer";
import path from "path";

import { logger } from "../../infrastructure/logging/logger";
import {
  SecureAIProcessor,
  DocumentFormat,
  DocumentProcessingOptions,
  AIJobStatus,
  EmbeddingService,
  RAGService,
  MessageRole,
  embeddingService,
  ragService,
} from "../../infrastructure/ai";
import { AIJobProcessor } from "../../infrastructure/ai/AIJobProcessor";
import { validateDocumentId } from "../../infrastructure/ai/util/sanitizer";
import { VectorRepository } from "../../infrastructure/repositories/VectorRepository";
import {
  saveAIDocument,
  getAIDocumentById,
  removeAIDocument,
} from "../../infrastructure/ai/AIDocumentStorage";

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize Google AI client
const getGoogleAI = () => {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error("GEMINI_API_KEY is not defined in environment variables");
  }
  return new GoogleGenAI({ apiKey });
};

/**
 * Configure multer for file uploads
 */
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (
      ext === ".pdf" ||
      ext === ".docx" ||
      ext === ".doc" ||
      ext === ".txt" ||
      ext === ".rtf" ||
      ext === ".csv" ||
      ext === ".xlsx" ||
      ext === ".xls"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Supported formats: PDF, Word, TXT, RTF, CSV, Excel"));
    }
  },
});

/**
 * Agreement Document analysis result interface
 */
export interface ContractDocumentAnalysisResult {
  title?: string;
  contractType?: string;
  parties?: Array<{
    name: string;
    role?: string;
  }>;
  dates?: {
    startDate?: string;
    endDate?: string;
    executionDate?: string;
    effectiveDate?: string;
  };
  value?: {
    amount?: string;
    currency?: string;
  };
  paymentTerms?: string;
  renewalTerms?: {
    isAutoRenew?: boolean;
    renewalPeriod?: string;
    noticePeriodDays?: number;
  };
  confidentiality?: string;
  terminationClauses?: string[];
  otherMetadata?: Record<string, any>;
}

/**
 * Analyzes a Agreement Document using Gemini AI
 * @param req Request
 * @param res Response
 */
export async function analyzeContractDocument(
  req: Request,
  res: Response
): Promise<void> {
  const uploadMiddleware = upload.single("file");

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      logger.error("Error uploading file:", { error: err });
      res.status(400).json({
        success: false,
        error: err.message,
      });
      return;
    }

    try {
      const file = req.file;
      if (!file) {
        res.status(400).json({
          success: false,
          error: "No file uploaded",
        });
        return;
      }

      const tenantId = req.user!.tenantId;

      // Convert document to base64
      const base64Content = file.buffer.toString("base64");
      const mimeType = getMimeType(file.originalname);

      // Prepare prompt text for comprehensive analysis
      const promptText = `You are a Agreement Analysis expert with extensive experience in legal document analysis. Extract comprehensive metadata from this Agreement Document following the detailed schema below.

CRITICAL INSTRUCTION: Extract ALL available information according to the comprehensive schema. This analysis will be used for contract management, risk assessment, and compliance tracking.

Return a JSON object with the following EXACT structure:

{
  "title": "Contract title",
  "provider": "Service/product provider company name",
  "client": "Customer/client company name",
  "contract_id": "Unique identifier or reference number",
  "effective_date": "Start date (YYYY-MM-DD format)",
  "end_date": "Expiration date (YYYY-MM-DD format)",
  "total_amount": 0.0,
  "currency": "Currency code (e.g., USD, EUR)",
  "payment_terms": "Plain text description of payment schedule",
  "governing_law": "Jurisdiction governing the agreement",
  "agreement_type": "Classification of contract type",
  "renewal_terms": [
    {
      "auto_renewal": "Yes|No|N/A",
      "notice_period": "Notice period for renewal",
      "renewal_term": "Duration of renewal",
      "pricing_conditionality": "Pricing conditions for renewal"
    }
  ],
  "line_items": [
    {
      "description": "Description of item/service",
      "amount": 0.0,
      "details": ["Optional breakdown items"]
    }
  ],
  "signatures": [
    {
      "party": "Provider|Client|Other",
      "company": "Company name",
      "name": "Signatory name",
      "title": "Signatory title",
      "date": "Signature date"
    }
  ],
  "liability": {
    "capping": "Liability cap description",
    "exclusions": "Liability exclusions"
  },
  "payment_terms_detailed": {
    "timeline": "<30 days|30-60 days|>60 days",
    "delayed_payment_charges": "Charges for late payment",
    "right_to_invoice": "Advance|Upon completion|Others"
  },
  "sla": {
    "availability": ">99.5%|95-99.5%|<95%",
    "response_time_p1": "<1 hr|1-2 hrs|>2 hrs",
    "response_time_p2": "<2 hrs|2-4 hrs|>4 hrs",
    "resolution_time_p1": "<4 hrs|4-8 hrs|>8 hrs",
    "resolution_time_p2": "<2 hrs|2-4 hrs|>4 hrs"
  },
  "termination": {
    "t4c": "Yes|No|N/A",
    "notice_t4c": "<=90 days|>90 days|N/A"
  },
  "contract_value": {
    "acv": "<100.000|100.000-500.000|500.000-2.000.000|>2.000.000"
  },
  "licensing": {
    "license_type": "Perpetual|Subscription|Others"
  },
  "rights": {
    "transfer_rights": "Yes, affiliates included|Yes, affiliates not included|No|Others"
  },
  "indemnification": {
    "third_party_claims": "Third-party claim provisions"
  },
  "data_privacy": {
    "compliance": "GDPR|Others|None"
  },
  "true_up_down": {
    "frequency": "Once every year|Once during the entire tenure|Others"
  },
  "service_credits": {
    "capping": "<=5%|5%-10%|>10%|Uncapped"
  },
  "governance": {
    "governance_type": "Defined|not defined"
  },
  "reporting_obligations": {
    "consumption": "Yes|No"
  },
  "ipr": {
    "custom_developments": "Vests with customer|Vests with supplier"
  },
  "data_breach": {
    "reporting_time": "upto 24 hrs|>24 hrs|not defined"
  }
}

EXTRACTION GUIDELINES:
1. Use exact values from the document where possible
2. For categorical fields, choose the closest matching option from the provided literals
3. Use "N/A" or "not defined" for missing information
4. Extract numerical values precisely for amounts
5. Use YYYY-MM-DD format for all dates
6. Be thorough in extracting line items and detailed terms

IMPORTANT: Your response MUST be a valid JSON object matching this exact structure. Do not include explanatory text outside the JSON.`;

      // Call Gemini API using @google/genai SDK
      const googleAI = getGoogleAI();
      const response = await googleAI.models.generateContent({
        model: "gemini-2.0-flash",
        config: {
          temperature: 0.1,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 4096,
        },
        contents: {
          role: "user",
          parts: [
            {
              text: promptText,
            },
            {
              inlineData: {
                data: base64Content,
                mimeType: mimeType,
              },
            },
          ],
        },
      });

      // Extract response text
      let responseText = "";
      if (response && response.text) {
        responseText = response.text;
      }

      // Extract JSON from response using multiple strategies (same as ContractAIService)
      let result: ContractDocumentAnalysisResult = {};
      let jsonObject = null;

      // Strategy 1: Try to parse the entire response as JSON directly
      try {
        jsonObject = JSON.parse(responseText.trim());
        logger.info("Successfully parsed entire response as JSON");
      } catch (directParseError) {
        // Strategy 2: Find JSON between triple backticks (markdown code blocks)
        const tripleBacktickMatch = responseText.match(
          /```(?:json)?\s*(\{[\s\S]*?\})\s*```/
        );
        if (tripleBacktickMatch) {
          try {
            jsonObject = JSON.parse(tripleBacktickMatch[1].trim());
            logger.info("Successfully extracted JSON from triple backticks");
          } catch (tripleBacktickError) {
            logger.warn(
              "Found JSON-like content in triple backticks but failed to parse",
              {
                error: tripleBacktickError,
                content: tripleBacktickMatch[1],
              }
            );
          }
        }

        // Strategy 3: Find JSON between single backticks
        if (!jsonObject) {
          const singleBacktickMatch = responseText.match(
            /`\s*(\{[\s\S]*?\})\s*`/
          );
          if (singleBacktickMatch) {
            try {
              jsonObject = JSON.parse(singleBacktickMatch[1].trim());
              logger.info("Successfully extracted JSON from single backticks");
            } catch (singleBacktickError) {
              logger.warn(
                "Found JSON-like content in single backticks but failed to parse",
                {
                  error: singleBacktickError,
                  content: singleBacktickMatch[1],
                }
              );
            }
          }
        }

        // Strategy 4: Find any JSON-like object in the text
        if (!jsonObject) {
          const jsonMatch = responseText.match(/(\{[\s\S]*\})/);
          if (jsonMatch) {
            try {
              jsonObject = JSON.parse(jsonMatch[0].trim());
              logger.info("Successfully extracted JSON from text");
            } catch (jsonMatchError) {
              logger.warn("Found JSON-like content but failed to parse", {
                error: jsonMatchError,
                content: jsonMatch[0],
              });
            }
          }
        }
      }

      // If we still don't have valid JSON, try one last approach - look for the first { and last }
      if (!jsonObject) {
        const firstBrace = responseText.indexOf("{");
        const lastBrace = responseText.lastIndexOf("}");

        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
          const potentialJson = responseText.substring(
            firstBrace,
            lastBrace + 1
          );
          try {
            jsonObject = JSON.parse(potentialJson);
            logger.info("Successfully extracted JSON using brace positions");
          } catch (braceError) {
            logger.warn("Failed to parse JSON using brace positions", {
              error: braceError,
              content: potentialJson,
            });
          }
        }
      }

      // If we have valid JSON, use it as the result
      if (jsonObject) {
        result = jsonObject;
        logger.info(
          "Successfully extracted comprehensive metadata from Agreement Document"
        );
      } else {
        logger.error("Could not extract valid JSON from Gemini response");
        logger.debug("Raw response:", responseText);
        // Return minimal result instead of empty object
        result = {
          title: file.originalname.replace(/\.[^/.]+$/, ""),
          contractType: "Unknown",
          value: {
            amount: "0",
            currency: "USD",
          },
        };
      }

      // Save document to AI document storage for potential future use
      const documentId = uuidv4();
      const aiDocumentData = {
        id: documentId,
        title: file.originalname,
        documentFormat: getDocumentFormat(file.originalname),
        documentUri: `memory://${documentId}`,
        documentSize: file.size,
        content: file.buffer,
      };
      saveAIDocument(aiDocumentData);

      res.status(200).json({
        success: true,
        documentId: documentId,
        metadata: result,
        filename: file.originalname,
        filesize: file.size,
      });
    } catch (error) {
      logger.error("Error analyzing Agreement Document", { error });
      res.status(500).json({
        success: false,
        error: (error as Error).message,
      });
    }
  });
}

/**
 * Gets MIME type from file name
 * @param fileName - File name
 * @returns MIME type
 */
function getMimeType(fileName: string): string {
  const ext = fileName.split(".").pop()?.toLowerCase();

  switch (ext) {
    case "pdf":
      return "application/pdf";
    case "docx":
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    case "doc":
      return "application/msword";
    case "txt":
      return "text/plain";
    case "rtf":
      return "application/rtf";
    case "csv":
      return "text/csv";
    case "xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    case "xls":
      return "application/vnd.ms-excel";
    case "html":
    case "htm":
      return "text/html";
    default:
      return "application/octet-stream";
  }
}

/**
 * Gets document format enum from file name
 * @param fileName - File name
 * @returns Document format enum
 */
function getDocumentFormat(fileName: string): DocumentFormat {
  const ext = fileName.split(".").pop()?.toLowerCase();

  switch (ext) {
    case "pdf":
      return DocumentFormat.PDF;
    case "docx":
    case "doc":
      return DocumentFormat.DOCX;
    case "txt":
      return DocumentFormat.TEXT;
    case "rtf":
    case "csv":
    case "xlsx":
    case "xls":
    case "html":
    case "htm":
    default:
      // For formats not explicitly defined in DocumentFormat enum,
      // default to TEXT as a fallback
      return DocumentFormat.TEXT;
  }
}

/**
 * Schema for document analysis request
 */
const documentAnalysisSchema = z.object({
  documentId: z.string().min(1),
  documentFormat: z.enum(["PDF", "DOCX", "TXT", "HTML"]),
  contractId: z.string().optional(),
  options: z
    .object({
      extractEntities: z.boolean().optional(),
      extractClauses: z.boolean().optional(),
      generateEmbeddings: z.boolean().optional(),
    })
    .optional(),
  documentUri: z.string().min(1),
});

/**
 * Schema for embedding generation request
 */
const embeddingGenerationSchema = z.object({
  documentId: z.string().min(1),
  text: z.string().min(1),
  metadata: z.record(z.any()).optional(),
});

/**
 * Schema for RAG query request
 */
const ragQuerySchema = z.object({
  query: z.string().min(1).max(1000),
  conversationId: z.string().optional(),
});

/**
 * Schema for conversation creation request
 */
const createConversationSchema = z
  .object({
    title: z.string().optional(),
  })
  .optional();

/**
 * Processes a document for analysis
 * @param req - Express request
 * @param res - Express response
 */
export async function processDocument(
  req: Request,
  res: Response
): Promise<void> {
  try {
    const tenantId = req.tenantId;

    // Validate request body
    const validationResult = documentAnalysisSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: "Invalid request data",
        details: validationResult.error.format(),
      });
      return;
    }

    const { documentId, documentFormat, contractId, options, documentUri } =
      validationResult.data;

    // Validate document ID
    const validDocumentId = validateDocumentId(documentId);
    if (!validDocumentId) {
      res.status(400).json({
        success: false,
        error: "Invalid document ID",
      });
      return;
    }

    // Process the document using Gemini AI

    try {
      // Get document from storage
      const document = await prisma.documentChunk.findFirst({
        where: {
          documentId: validDocumentId,
          tenantId: tenantId,
        },
      });

      if (!document) {
        res.status(404).json({
          success: false,
          error: "Document not found",
        });
        return;
      }

      // Call Gemini API for document analysis using @google/genai SDK
      const promptText = `Analyze this document and extract key information. Focus on identifying:
                - Document type
                - Parties involved
                - Key dates
                - Financial terms
                - Contract duration
                - Renewal terms
                - Termination conditions
                - Special clauses

                Return the results as a structured JSON object.`;

      const googleAI = getGoogleAI();
      const response = await googleAI.models.generateContent({
        model: "gemini-2.0-flash",
        config: {
          temperature: 0.1,
          topP: 0.95,
          topK: 40,
          maxOutputTokens: 4096,
        },
        contents: {
          role: "user",
          parts: [
            {
              text: promptText,
            },
            {
              text: document.content,
            },
          ],
        },
      });

      // Extract response text
      let analysisResult = {};
      if (response && response.text) {
        const responseText = response.text;

        // Try to extract JSON from the response
        try {
          const jsonMatch = responseText.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            analysisResult = JSON.parse(jsonMatch[0]);
          }
        } catch (parseError) {
          logger.error("Error parsing JSON from Gemini response", {
            error: parseError,
          });
        }
      }

      res.status(200).json({
        success: true,
        result: analysisResult,
      });
    } catch (error) {
      logger.error("Error processing document", { error });
      res.status(500).json({
        success: false,
        error: "Failed to process document",
        message: (error as Error).message,
      });
    }
  } catch (error) {
    logger.error("Error processing document", { error });
    res.status(500).json({
      success: false,
      error: "Failed to process document",
      message: (error as Error).message,
    });
  }
}

/**
 * Gets the status of an AI job
 * @param req - Express request
 * @param res - Express response
 */
export async function getJobStatus(req: Request, res: Response): Promise<void> {
  try {
    const { jobId } = req.params;

    // Redis has been removed, so we'll return a message instead
    res.status(200).json({
      success: true,
      message:
        "Job status functionality is currently disabled. Redis has been removed from the system.",
    });
  } catch (error) {
    logger.error("Error getting job status", {
      error,
      jobId: req.params.jobId,
    });
    res.status(500).json({
      success: false,
      error: "Failed to get job status",
      message: (error as Error).message,
    });
  }
}

/**
 * Generates embeddings for text
 * @param req - Express request
 * @param res - Express response
 */
export async function generateEmbeddings(
  req: Request,
  res: Response
): Promise<void> {
  try {
    const tenantId = req.tenantId;

    // Validate request body
    const validationResult = embeddingGenerationSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: "Invalid request data",
        details: validationResult.error.format(),
      });
      return;
    }

    const { documentId, text, metadata } = validationResult.data;

    // Validate document ID
    const validDocumentId = validateDocumentId(documentId);
    if (!validDocumentId) {
      res.status(400).json({
        success: false,
        error: "Invalid document ID",
      });
      return;
    }

    // Redis has been removed, so we'll return a message instead
    res.status(200).json({
      success: true,
      message:
        "Embedding generation functionality is currently disabled. Redis has been removed from the system.",
    });
  } catch (error) {
    logger.error("Error generating embeddings", { error });
    res.status(500).json({
      success: false,
      error: "Failed to generate embeddings",
      message: (error as Error).message,
    });
  }
}

/**
 * Creates a new conversation
 * @param req - Express request
 * @param res - Express response
 */
export async function createConversation(
  req: Request,
  res: Response
): Promise<void> {
  try {
    // Get tenant and user IDs from the request
    const tenantId = req.user?.tenantId;
    const userId = req.user?.userId;

    if (!tenantId || !userId) {
      res.status(401).json({
        success: false,
        error: "Authentication required",
      });
      return;
    }

    // Validate request body
    const validationResult = createConversationSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: "Invalid request data",
        details: validationResult.error.format(),
      });
      return;
    }

    // Extract title from data (might be undefined if body is empty)
    const title = validationResult.data?.title;

    // Create conversation
    const conversation = await ragService.createConversation(
      tenantId,
      userId,
      title
    );

    res.status(201).json({
      success: true,
      conversation,
    });
  } catch (error) {
    logger.error("Error creating conversation", { error });
    res.status(500).json({
      success: false,
      error: "Failed to create conversation",
      message: (error as Error).message,
    });
  }
}

/**
 * Gets a conversation by ID
 * @param req - Express request
 * @param res - Express response
 */
export async function getConversation(
  req: Request,
  res: Response
): Promise<void> {
  try {
    // Get tenant ID from the request
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      res.status(401).json({
        success: false,
        error: "Authentication required",
      });
      return;
    }

    const { conversationId } = req.params;

    // Get conversation
    const conversation = await ragService.getConversation(
      conversationId,
      tenantId
    );

    if (!conversation) {
      res.status(404).json({
        success: false,
        error: "Conversation not found",
      });
      return;
    }

    res.status(200).json({
      success: true,
      conversation,
    });
  } catch (error) {
    logger.error("Error getting conversation", {
      error,
      conversationId: req.params.conversationId,
    });
    res.status(500).json({
      success: false,
      error: "Failed to get conversation",
      message: (error as Error).message,
    });
  }
}

/**
 * Processes a RAG query
 * @param req - Express request
 * @param res - Express response
 */
export async function processQuery(req: Request, res: Response): Promise<void> {
  try {
    // Get tenant and user IDs from the request
    const tenantId = req.user?.tenantId;
    const userId = req.user?.userId;

    if (!tenantId || !userId) {
      res.status(401).json({
        success: false,
        error: "Authentication required",
      });
      return;
    }

    // Validate request body
    const validationResult = ragQuerySchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: "Invalid request data",
        details: validationResult.error.format(),
      });
      return;
    }

    const { query, conversationId } = validationResult.data;

    // Create conversation if not provided
    let actualConversationId = conversationId;
    if (!actualConversationId) {
      const conversation = await ragService.createConversation(
        tenantId,
        userId
      );
      actualConversationId = conversation.id;
    } else {
      // Verify conversation exists
      const conversation = await ragService.getConversation(
        actualConversationId,
        tenantId
      );
      if (!conversation) {
        res.status(404).json({
          success: false,
          error: "Conversation not found",
        });
        return;
      }
    }

    // Process query
    const response = await ragService.processQuery(
      actualConversationId,
      query,
      tenantId
    );

    res.status(200).json({
      success: true,
      conversationId: actualConversationId,
      response,
    });
  } catch (error) {
    logger.error("Error processing query", { error });
    res.status(500).json({
      success: false,
      error: "Failed to process query",
      message: (error as Error).message,
    });
  }
}
