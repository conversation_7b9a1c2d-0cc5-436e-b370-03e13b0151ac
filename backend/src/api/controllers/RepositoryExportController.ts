/**
 * Repository Export Controller
 * Handles API requests for exporting repository contracts to Excel
 */

import { Request, Response } from "express";
import { logger } from "../../infrastructure/logging/logger";
import { ContractService } from "../services/ContractService";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import * as XLSX from "xlsx";

export class RepositoryExportController {
  constructor(
    private contractService: ContractService,
    private contractExtractionRepository: ContractExtractionRepository
  ) { }

  /**
   * Exports all repository contracts to Excel format
   * @route GET /api/repository/export/excel
   */
  async exportToExcel(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      logger.info(`Exporting repository to Excel for tenant ${tenantId}`);

      // Get all contracts with extraction data for the tenant
      const extractions = await this.contractExtractionRepository.getByTenantId(
        tenantId
      );

      if (!extractions || extractions.length === 0) {
        res.status(404).json({ error: "No contracts found in repository" });
        return;
      }

      // Create Excel workbook
      const workbook = XLSX.utils.book_new();

      // Prepare contract data for Excel export
      const contractsData = this.prepareContractsSheetData(extractions);

      // Create main contracts sheet
      const contractsSheet = XLSX.utils.json_to_sheet(contractsData);
      XLSX.utils.book_append_sheet(
        workbook,
        contractsSheet,
        "Contract Repository"
      );

      // Generate Excel buffer
      const excelBuffer = XLSX.write(workbook, {
        type: "buffer",
        bookType: "xlsx",
      });

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = `Contract_Repository_Export_${timestamp}.xlsx`;

      // Set response headers for file download
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Content-Length", excelBuffer.length);

      // Send the Excel file
      res.send(excelBuffer);

      logger.info(
        `Repository export completed: ${extractions.length} contracts exported`
      );
    } catch (error) {
      logger.error("Error exporting repository to Excel:", {
        error,
        tenantId: req.user?.tenantId,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Exports repository contracts using UI table format
   * @route POST /api/repository/export/ui-table
   */
  async exportUITableToExcel(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const { tableData } = req.body;

      if (!tableData || !tableData.columns || !tableData.rows) {
        res.status(400).json({ error: "Invalid table data provided" });
        return;
      }

      logger.info(
        `Exporting repository UI table to Excel for tenant ${tenantId}`
      );

      // Create Excel workbook
      const workbook = XLSX.utils.book_new();

      // Convert UI table data to Excel format
      const excelData = tableData.rows.map((row: any) => {
        const rowData: any = {};
        tableData.columns.forEach((column: any, index: number) => {
          rowData[column.header] = row[index] || "";
        });
        return rowData;
      });

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Contract Repository");

      // Generate Excel buffer
      const excelBuffer = XLSX.write(workbook, {
        type: "buffer",
        bookType: "xlsx",
      });

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = `Contract_Repository_Export_${timestamp}.xlsx`;

      // Set response headers for file download
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Content-Length", excelBuffer.length);

      // Send the Excel file
      res.send(excelBuffer);

      logger.info(
        `Repository UI table export completed: ${tableData.rows.length} rows exported`
      );
    } catch (error) {
      logger.error("Error exporting repository UI table to Excel:", {
        error,
        tenantId: req.user?.tenantId,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets export statistics for the repository
   * @route GET /api/repository/export/stats
   */
  async getExportStatistics(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      logger.info(
        `Getting repository export statistics for tenant ${tenantId}`
      );

      // Get all contracts with extraction data
      const extractions = await this.contractExtractionRepository.getByTenantId(
        tenantId
      );

      const statistics = {
        totalContracts: extractions.length,
        contractsWithExtraction: extractions.filter((e: any) => e.fixedFields)
          .length,
        averageConfidence:
          extractions.length > 0
            ? extractions.reduce(
              (sum: number, e: any) => sum + (e.overallConfidence || 0),
              0
            ) / extractions.length
            : 0,
        agreementTypes: this.getAgreementTypeStats(extractions),
        statusDistribution: this.getStatusDistribution(extractions),
        folderDistribution: this.getFolderDistribution(extractions),
      };

      res.json(statistics);
    } catch (error) {
      logger.error("Error getting repository export statistics:", {
        error,
        tenantId: req.user?.tenantId,
      });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Prepares contract data for Excel sheet
   */
  private prepareContractsSheetData(extractions: any[]): any[] {
    return extractions.map((extraction) => {
      const fixedFields = extraction.fixedFields || {};

      return {
        "Contract Name":
          fixedFields.original_filename?.value ||
          extraction.contractId ||
          "N/A",
        "Contract ID": fixedFields.contract_id?.value || "N/A",
        "Agreement Type": fixedFields.agreement_type?.value || "N/A",
        "Provider/Supplier": fixedFields.provider?.value || "N/A",
        "Client/Counterparty": fixedFields.client?.value || "N/A",
        "Contract Value": fixedFields.total_amount?.value || "N/A",
        "Start Date": fixedFields.start_date?.value || "N/A",
        "End Date": fixedFields.end_date?.value || "N/A",
        "Auto Renewal": fixedFields.auto_renewal?.value || "N/A",
        "Notice Period": fixedFields.renewal_notice_period?.value || "N/A",
        Folder: extraction.folderName || "Standalone",
        "Overall Confidence": extraction.overallConfidence
          ? `${Math.round(extraction.overallConfidence * 100)}%`
          : "N/A",
        "Extraction Date": extraction.extractionDate
          ? new Date(extraction.extractionDate).toLocaleDateString()
          : "N/A",
        "Last Updated": extraction.updatedAt
          ? new Date(extraction.updatedAt).toLocaleDateString()
          : "N/A",
      };
    });
  }

  /**
   * Gets agreement type statistics
   */
  private getAgreementTypeStats(extractions: any[]): Record<string, number> {
    const stats: Record<string, number> = {};
    extractions.forEach((extraction) => {
      const agreementType =
        extraction.fixedFields?.agreement_type?.value || "Unknown";
      stats[agreementType] = (stats[agreementType] || 0) + 1;
    });
    return stats;
  }

  /**
   * Gets status distribution
   */
  private getStatusDistribution(extractions: any[]): Record<string, number> {
    const stats: Record<string, number> = {};
    extractions.forEach((extraction) => {
      // Calculate status based on dates (similar to frontend logic)
      const startDate = extraction.fixedFields?.start_date?.value;
      const endDate = extraction.fixedFields?.end_date?.value;

      let status = "Unknown";
      if (startDate && endDate) {
        const now = new Date();
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (start <= now && end >= now) {
          status = "Active";
        } else {
          status = "Inactive";
        }
      }

      stats[status] = (stats[status] || 0) + 1;
    });
    return stats;
  }

  /**
   * Gets folder distribution
   */
  private getFolderDistribution(extractions: any[]): Record<string, number> {
    const stats: Record<string, number> = {};
    extractions.forEach((extraction) => {
      const folder = extraction.folderName || "Standalone";
      stats[folder] = (stats[folder] || 0) + 1;
    });
    return stats;
  }
}
