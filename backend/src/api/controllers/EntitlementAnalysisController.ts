/**
 * Entitlement Analysis Controller
 * Handles API endpoints for entitlement analysis functionality
 */

import { Request, Response } from "express";
import { IContractExtractionRepository } from "../../domain/contracts/interfaces/IContractExtractionRepository";
import { IContractRepository } from "../../domain/contracts/interfaces/IContractRepository";
import { ContractAIService } from "../services/ContractAIService";
import { ContractService } from "../services/ContractService";
import { logger } from "../../infrastructure/logging/logger";
import * as XLSX from "xlsx";
import { v4 as uuidv4 } from "uuid";
import multer from "multer";
import path from "path";

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
  },
  fileFilter: (_, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (
      ext === ".pdf" ||
      ext === ".docx" ||
      ext === ".doc" ||
      ext === ".txt" ||
      ext === ".rtf" ||
      ext === ".odt"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Supported formats: PDF, Word, TXT, RTF, ODT"
        )
      );
    }
  },
});

// Job tracking interfaces
interface ExtractionJob {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  contractIds: string[];
  tenantId: string;
  userId: string;
  forceReExtraction: boolean;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: {
    total: number;
    completed: number;
    failed: number;
    current?: string;
  };
  results: {
    contractId: string;
    status: 'success' | 'failed';
    error?: string;
    fieldsExtracted?: number;
  }[];
}

export class EntitlementAnalysisController {
  // In-memory job storage (can be replaced with Redis/database later)
  private static extractionJobs = new Map<string, ExtractionJob>();

  // Define the exact order of analysis fields as specified
  private readonly ANALYSIS_FIELDS_ORDER = [
    "publisher",
    "reseller",
    "entitled_entity",
    "entitled_entity_country",
    "product_name",
    "license_type",
    "item_license_value",
    "item_quantity",
    "item_price",
    "total_quantity",
    "metric",
    "metric_definition",
    "term",
    "level",
    "limitations",
    "included_rights",
    "csi",
    "purchase_date",
    "governing_agreement",
    "support_contract_number",
    "support_start_date",
    "support_end_date",
    "original_document_name",
    "document_type",
    "license_value",
    "license_value_per_unit",
    "contractual_support_value",
    "support_value_per_year",
    "support_value_per_year_per_unit",
    "currency",
    "index",
    "delta",
  ];

  constructor(
    private contractExtractionRepository: IContractExtractionRepository,
    private contractRepository: IContractRepository,
    private contractAIService: ContractAIService,
    private contractService: ContractService
  ) { }

  /**
   * Get the upload middleware for single file uploads
   */
  getUploadMiddleware() {
    return upload.single("document");
  }

  /**
   * Gets all unique providers/suppliers from contracts
   * @route GET /api/entitlement-analysis/providers
   */
  async getProviders(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;

      // Get all extractions for the tenant
      const extractions = await this.contractExtractionRepository.getByTenantId(
        tenantId
      );

      // Extract unique providers and normalize them (first word only)
      const providerSet = new Set<string>();
      const providerMap = new Map<string, string>(); // normalized -> original

      extractions.forEach((extraction) => {
        const provider = extraction.fixedFields.provider?.value;
        if (provider && provider !== "N/A" && provider.trim()) {
          const normalized = provider.split(/\s+/)[0].toLowerCase(); // First word, lowercase
          const display = provider.split(/\s+/)[0]; // First word, original case

          if (!providerSet.has(normalized)) {
            providerSet.add(normalized);
            providerMap.set(normalized, display);
          }
        }
      });

      // Convert to array and sort
      const providers = Array.from(providerMap.values()).sort();

      res.status(200).json({
        providers,
        total: providers.length,
      });
    } catch (error) {
      logger.error("Error getting providers for entitlement analysis:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets contracts by provider for entitlement analysis
   * @route GET /api/entitlement-analysis/contracts/:provider
   */
  async getContractsByProvider(req: Request, res: Response): Promise<void> {
    try {
      const { provider } = req.params;
      const tenantId = req.user!.tenantId;

      // Get all extractions for the tenant
      const extractions = await this.contractExtractionRepository.getByTenantId(
        tenantId
      );

      // Filter contracts by provider (match first word)
      const matchingContracts = [];

      for (const extraction of extractions) {
        const extractionProvider = extraction.fixedFields.provider?.value;
        if (extractionProvider && extractionProvider !== "N/A") {
          const firstWord = extractionProvider.split(/\s+/)[0].toLowerCase();
          if (firstWord === provider.toLowerCase()) {
            // Get contract details
            try {
              const contract = await this.contractRepository.findById(
                extraction.contractId
              );
              if (contract && contract.tenantId === tenantId) {
                // Get filename from extraction data
                const fileName =
                  extraction.fixedFields.original_filename?.value ||
                  contract.title;

                matchingContracts.push({
                  id: contract.id,
                  title: contract.title,
                  fileName: fileName,
                  hasAnalysisFields: !!extraction.analysisFields,
                  provider: extractionProvider,
                });
              }
            } catch (contractError) {
              logger.warn(
                `Could not fetch contract ${extraction.contractId}:`,
                contractError
              );
            }
          }
        }
      }

      res.status(200).json({
        contracts: matchingContracts,
        total: matchingContracts.length,
        provider,
      });
    } catch (error) {
      logger.error("Error getting contracts by provider:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets entitlement analysis data for selected contracts
   * @route POST /api/entitlement-analysis/data
   */
  async getAnalysisData(req: Request, res: Response): Promise<void> {
    try {
      const { contractIds } = req.body;
      const tenantId = req.user!.tenantId;

      if (
        !contractIds ||
        !Array.isArray(contractIds) ||
        contractIds.length === 0
      ) {
        res.status(400).json({ error: "Contract IDs are required" });
        return;
      }

      const analysisData = [];
      const allFields = new Set<string>();

      // Get analysis data for each contract
      for (const contractId of contractIds) {
        try {
          const extraction =
            await this.contractExtractionRepository.getByContractId(
              contractId,
              tenantId
            );
          const contract = await this.contractRepository.findById(contractId);

          if (extraction && contract && contract.tenantId === tenantId) {
            // Get filename from extraction data
            const fileName =
              extraction.fixedFields.original_filename?.value || contract.title;

            const rowData: any = {
              contractId,
              contractName: contract.title,
              fileName: fileName,
            };

            // Add analysis fields if they exist
            if (extraction.analysisFields) {
              logger.info(
                `Found analysis fields for contract ${contractId}:`,
                Object.keys(extraction.analysisFields)
              );

              // Check if there's purchasing data (new object format or legacy array)
              const purchasingData = (extraction.analysisFields as any)
                .purchasing;

              logger.info(
                `Contract ${contractId} analysis fields:`,
                Object.keys(extraction.analysisFields)
              );
              logger.info(
                `Purchasing data for contract ${contractId}:`,
                purchasingData
              );

              // Handle both new object format and legacy array format
              let purchasingItems: Array<{ item: any, year: string, itemIndex: number, totalItems: number }> = [];

              if (Array.isArray(purchasingData)) {
                // Legacy array format - treat as TOTAL
                purchasingData.forEach((item, index) => {
                  purchasingItems.push({
                    item,
                    year: "TOTAL",
                    itemIndex: index,
                    totalItems: purchasingData.length
                  });
                });
              } else if (purchasingData && typeof purchasingData === 'object') {
                // New object format - process by year
                Object.entries(purchasingData).forEach(([year, items]) => {
                  if (Array.isArray(items)) {
                    items.forEach((item, index) => {
                      purchasingItems.push({
                        item,
                        year,
                        itemIndex: index,
                        totalItems: items.length
                      });
                    });
                  }
                });
              }

              if (purchasingItems.length > 0) {
                // Create separate rows for each purchasing item
                purchasingItems.forEach(
                  ({ item: purchaseItem, year, itemIndex, totalItems }) => {
                    const purchaseRowData = { ...rowData };

                    // Add metadata for purchasing items (include year info)
                    purchaseRowData._purchasingItemIndex = itemIndex;
                    purchaseRowData._purchasingYear = year;
                    purchaseRowData._isMultiItem = totalItems > 1;

                    // Add all analysis fields except purchasing
                    Object.entries(extraction.analysisFields || {}).forEach(
                      ([key, value]) => {
                        if (
                          key !== "extractionDate" &&
                          key !== "processingTimeMs" &&
                          key !== "purchasing"
                        ) {
                          allFields.add(key);
                          if (
                            typeof value === "object" &&
                            value !== null &&
                            "value" in value
                          ) {
                            purchaseRowData[key] = value.value || "N/A";
                            // Store confidence and metadata for contract-level fields
                            purchaseRowData[`${key}_confidence`] =
                              value.confidence || 0.8;
                            purchaseRowData[`${key}_isContractLevel`] = true;
                          } else {
                            purchaseRowData[key] = value || "N/A";
                            purchaseRowData[`${key}_confidence`] = 0.8;
                            purchaseRowData[`${key}_isContractLevel`] = true;
                          }
                        }
                      }
                    );

                    // Add purchasing item fields dynamically with year prefix
                    Object.entries(purchaseItem).forEach(([key, value]) => {
                      // Map purchasing array field names to display field names
                      const fieldMapping: Record<string, string> = {
                        license_type: "license_type",
                        license_value: "item_license_value",
                        quantity: "item_quantity",
                        price: "item_price",
                      };

                      const baseFieldName = fieldMapping[key] || key;
                      // Add year prefix to field name (e.g., "YEAR_1_license_type", "TOTAL_license_type")
                      const yearPrefix = year.replace(/\s+/g, "_").toUpperCase();
                      const displayFieldName = `${yearPrefix}_${baseFieldName}`;

                      allFields.add(displayFieldName);
                      purchaseRowData[displayFieldName] = value || "N/A";

                      // Check if this field was manually edited
                      const purchasingMetadata = (
                        extraction.analysisFields as any
                      ).purchasingMetadata;

                      // Look for metadata using year-based key structure
                      const yearMetadataKey = `${year}_${itemIndex}`;
                      const fieldMetadata =
                        purchasingMetadata?.[yearMetadataKey]?.[key] ||
                        purchasingMetadata?.[itemIndex]?.[key]; // Fallback for legacy format
                      const confidence = fieldMetadata?.confidence ?? 0.8;

                      purchaseRowData[`${displayFieldName}_confidence`] =
                        confidence;
                      purchaseRowData[`${displayFieldName}_isContractLevel`] =
                        false;
                    });

                    analysisData.push(purchaseRowData);
                  }
                );
              } else {
                // No purchasing array, process normally
                Object.entries(extraction.analysisFields).forEach(
                  ([key, value]) => {
                    if (
                      key !== "extractionDate" &&
                      key !== "processingTimeMs" &&
                      key !== "purchasingMetadata"
                    ) {
                      allFields.add(key);
                      if (
                        typeof value === "object" &&
                        value !== null &&
                        "value" in value
                      ) {
                        rowData[key] = value.value || "N/A";
                        // Store confidence and metadata for contract-level fields
                        rowData[`${key}_confidence`] = value.confidence || 0.8;
                        rowData[`${key}_isContractLevel`] = true;
                      } else {
                        rowData[key] = value || "N/A";
                        rowData[`${key}_confidence`] = 0.8;
                        rowData[`${key}_isContractLevel`] = true;
                      }
                    }
                  }
                );
                analysisData.push(rowData);
              }
            } else {
              logger.info(
                `No analysis fields found for contract ${contractId}`
              );
              analysisData.push(rowData);
            }
          }
        } catch (contractError) {
          logger.warn(
            `Error processing contract ${contractId}:`,
            contractError
          );
        }
      }

      // First, let's see what fields we actually have
      const foundFieldsArray = Array.from(allFields);
      logger.info(`Found analysis fields:`, foundFieldsArray);

      // Try to match found fields with our predefined order, but also include any unmatched fields
      const orderedFields: string[] = [];

      // Add fields in our predefined order if they exist
      this.ANALYSIS_FIELDS_ORDER.forEach((field) => {
        if (allFields.has(field)) {
          orderedFields.push(field);
        }
      });

      // Add any remaining fields that weren't in our predefined order
      foundFieldsArray.forEach((field) => {
        if (!orderedFields.includes(field)) {
          orderedFields.push(field);
        }
      });

      logger.info(`Final ordered fields:`, orderedFields);

      logger.info(
        `Total analysis fields found: ${allFields.size}, Ordered fields: ${orderedFields.length}`
      );
      logger.info(`All fields found:`, Array.from(allFields));
      logger.info(`Ordered fields:`, orderedFields);

      // Ensure all rows have all fields (fill missing with N/A)
      analysisData.forEach((row) => {
        orderedFields.forEach((field) => {
          if (!(field in row)) {
            row[field] = "N/A";
          }
        });
      });

      res.status(200).json({
        data: analysisData,
        fields: orderedFields,
        total: analysisData.length,
      });
    } catch (error) {
      logger.error("Error getting entitlement analysis data:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Exports entitlement analysis data as Excel
   * @route POST /api/entitlement-analysis/export
   */
  async exportAnalysisData(req: Request, res: Response): Promise<void> {
    try {
      const { contractIds, format = "xlsx" } = req.body;
      const tenantId = req.user!.tenantId;

      if (
        !contractIds ||
        !Array.isArray(contractIds) ||
        contractIds.length === 0
      ) {
        res.status(400).json({ error: "Contract IDs are required" });
        return;
      }

      // Get the analysis data (reuse the logic from getAnalysisData)
      const analysisData = [];
      const allFields = new Set<string>();

      for (const contractId of contractIds) {
        try {
          const extraction =
            await this.contractExtractionRepository.getByContractId(
              contractId,
              tenantId
            );
          const contract = await this.contractRepository.findById(contractId);

          if (extraction && contract && contract.tenantId === tenantId) {
            // Get filename from extraction data
            const fileName =
              extraction.fixedFields.original_filename?.value || contract.title;

            const rowData: any = {
              contractName: contract.title,
              fileName: fileName,
            };

            if (extraction.analysisFields) {
              // Check if there's a purchasing array
              const purchasingArray = (extraction.analysisFields as any)
                .purchasing;

              if (
                Array.isArray(purchasingArray) &&
                purchasingArray.length > 0
              ) {
                // Create separate rows for each purchasing item
                purchasingArray.forEach((purchaseItem: any) => {
                  const purchaseRowData = { ...rowData };

                  // Add all analysis fields except purchasing
                  Object.entries(extraction.analysisFields || {}).forEach(
                    ([key, value]) => {
                      if (
                        key !== "extractionDate" &&
                        key !== "processingTimeMs" &&
                        key !== "purchasing" &&
                        key !== "purchasingMetadata"
                      ) {
                        allFields.add(key);
                        if (
                          typeof value === "object" &&
                          value !== null &&
                          "value" in value
                        ) {
                          purchaseRowData[key] = value.value || "N/A";
                        } else {
                          purchaseRowData[key] = value || "N/A";
                        }
                      }
                    }
                  );

                  // Add purchasing item fields dynamically
                  Object.entries(purchaseItem).forEach(([key, value]) => {
                    // Map purchasing array field names to display field names
                    const fieldMapping: Record<string, string> = {
                      license_type: "license_type",
                      license_value: "item_license_value",
                      quantity: "item_quantity",
                      price: "item_price",
                    };

                    const displayFieldName = fieldMapping[key] || key;
                    allFields.add(displayFieldName);
                    purchaseRowData[displayFieldName] = value || "N/A";
                  });

                  analysisData.push(purchaseRowData);
                });
              } else {
                // No purchasing array, process normally
                Object.entries(extraction.analysisFields).forEach(
                  ([key, value]) => {
                    if (
                      key !== "extractionDate" &&
                      key !== "processingTimeMs" &&
                      key !== "purchasingMetadata"
                    ) {
                      allFields.add(key);
                      if (
                        typeof value === "object" &&
                        value !== null &&
                        "value" in value
                      ) {
                        rowData[key] = value.value || "N/A";
                      } else {
                        rowData[key] = value || "N/A";
                      }
                    }
                  }
                );
                analysisData.push(rowData);
              }
            } else {
              analysisData.push(rowData);
            }
          }
        } catch (contractError) {
          logger.warn(
            `Error processing contract ${contractId} for export:`,
            contractError
          );
        }
      }

      // Use the predefined field order for export, only including fields that were actually found
      const orderedFields = this.ANALYSIS_FIELDS_ORDER.filter((field) =>
        allFields.has(field)
      );

      // Ensure all rows have all fields
      analysisData.forEach((row) => {
        orderedFields.forEach((field) => {
          if (!(field in row)) {
            row[field] = "N/A";
          }
        });
      });

      if (analysisData.length === 0) {
        res
          .status(404)
          .json({ error: "No analysis data found for the selected contracts" });
        return;
      }

      // Create Excel workbook with proper headers
      const workbook = XLSX.utils.book_new();

      // Create headers mapping for Excel export
      const headers: Record<string, string> = {
        contractName: "Contract Name",
        fileName: "File Name",
        ...Object.fromEntries(
          orderedFields.map((field) => [
            field,
            field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
          ])
        ),
      };

      // Create worksheet with proper headers
      const worksheet = XLSX.utils.json_to_sheet(analysisData, {
        header: Object.keys(headers),
      });

      // Update the header row with display names
      const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        const cellValue = worksheet[cellAddress]?.v;
        if (cellValue && headers[cellValue as string]) {
          worksheet[cellAddress].v = headers[cellValue as string];
        }
      }

      // Add the worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Entitlement Analysis");

      // Generate buffer
      const buffer = XLSX.write(workbook, {
        type: "buffer",
        bookType: format as any,
      });

      // Set response headers
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = `entitlement-analysis-${timestamp}.${format}`;

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Content-Length", buffer.length);

      res.send(buffer);
    } catch (error) {
      logger.error("Error exporting entitlement analysis data:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates analysis fields for entitlement analysis
   * @route PUT /api/entitlement-analysis/update-field
   */
  async updateAnalysisField(req: Request, res: Response): Promise<void> {
    try {
      const {
        contractId,
        fieldKey,
        newValue,
        isContractLevel,
        purchasingItemIndex,
      } = req.body;
      const tenantId = req.user!.tenantId;

      if (!contractId || !fieldKey || newValue === undefined) {
        res.status(400).json({
          error: "Contract ID, field key, and new value are required",
        });
        return;
      }

      // Validate field key
      if (typeof fieldKey !== "string" || fieldKey.trim().length === 0) {
        res.status(400).json({ error: "Invalid field key" });
        return;
      }

      // Validate new value
      if (typeof newValue !== "string") {
        res.status(400).json({ error: "New value must be a string" });
        return;
      }

      // Validate contract ID format
      const uuidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidPattern.test(contractId)) {
        res.status(400).json({ error: "Invalid contract ID format" });
        return;
      }

      // Get the extraction record
      const extraction =
        await this.contractExtractionRepository.getByContractId(
          contractId,
          tenantId
        );

      if (!extraction) {
        res.status(404).json({ error: "Contract extraction not found" });
        return;
      }

      // Update the analysis fields
      const analysisFields = { ...extraction.analysisFields } as any;

      if (isContractLevel) {
        // Update contract-level field
        if (
          typeof analysisFields[fieldKey] === "object" &&
          analysisFields[fieldKey] !== null &&
          "value" in analysisFields[fieldKey]
        ) {
          analysisFields[fieldKey] = {
            ...analysisFields[fieldKey],
            value: newValue,
            confidence: -1, // Mark as manually edited
          };
        } else {
          analysisFields[fieldKey] = {
            value: newValue,
            confidence: -1,
          };
        }
      } else {
        // Update purchasing item field (handle both new object format and legacy array)

        // Parse year and field from fieldKey (e.g., "YEAR_1_license_type" -> year="YEAR 1", field="license_type")
        const yearFieldMatch = fieldKey.match(/^([A-Z_]+)_(.+)$/);
        if (!yearFieldMatch) {
          res.status(400).json({
            error: "Invalid field key format for purchasing item",
          });
          return;
        }

        const yearKey = yearFieldMatch[1].replace(/_/g, " "); // "YEAR_1" -> "YEAR 1"
        const baseFieldKey = yearFieldMatch[2]; // "license_type"

        // Map display field names to purchasing array field names
        const fieldMapping: Record<string, string> = {
          license_type: "license_type",
          item_license_value: "license_value",
          item_quantity: "quantity",
          item_price: "price",
        };

        const actualFieldKey = fieldMapping[baseFieldKey] || baseFieldKey;

        // Handle both new object format and legacy array format
        if (Array.isArray(analysisFields.purchasing)) {
          // Legacy array format
          if (
            purchasingItemIndex !== undefined &&
            purchasingItemIndex >= 0 &&
            purchasingItemIndex < analysisFields.purchasing.length
          ) {
            analysisFields.purchasing[purchasingItemIndex][actualFieldKey] = newValue;

            // Store metadata for legacy format
            if (!analysisFields.purchasingMetadata) {
              analysisFields.purchasingMetadata = {};
            }
            if (!analysisFields.purchasingMetadata[purchasingItemIndex]) {
              analysisFields.purchasingMetadata[purchasingItemIndex] = {};
            }
            analysisFields.purchasingMetadata[purchasingItemIndex][actualFieldKey] = {
              confidence: -1,
              lastModified: new Date().toISOString(),
            };
          } else {
            res.status(400).json({
              error: "Invalid purchasing item index for legacy array format",
            });
            return;
          }
        } else if (analysisFields.purchasing && typeof analysisFields.purchasing === 'object') {
          // New object format
          if (
            analysisFields.purchasing[yearKey] &&
            Array.isArray(analysisFields.purchasing[yearKey]) &&
            purchasingItemIndex !== undefined &&
            purchasingItemIndex >= 0 &&
            purchasingItemIndex < analysisFields.purchasing[yearKey].length
          ) {
            analysisFields.purchasing[yearKey][purchasingItemIndex][actualFieldKey] = newValue;

            // Store metadata for new format with year-based keys
            if (!analysisFields.purchasingMetadata) {
              analysisFields.purchasingMetadata = {};
            }
            const metadataKey = `${yearKey}_${purchasingItemIndex}`;
            if (!analysisFields.purchasingMetadata[metadataKey]) {
              analysisFields.purchasingMetadata[metadataKey] = {};
            }
            analysisFields.purchasingMetadata[metadataKey][actualFieldKey] = {
              confidence: -1,
              lastModified: new Date().toISOString(),
            };
          } else {
            res.status(400).json({
              error: `Invalid purchasing item index for year ${yearKey} or year not found`,
            });
            return;
          }
        } else {
          res.status(400).json({
            error: "Purchasing data not found or invalid format",
          });
          return;
        }
      }

      // Update the extraction record
      await this.contractExtractionRepository.update(extraction.id, tenantId, {
        analysisFields,
      });

      res.status(200).json({
        success: true,
        message: "Analysis field updated successfully",
      });
    } catch (error) {
      logger.error("Error updating analysis field:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Triggers analysis fields extraction for contracts that don't have them
   * @route POST /api/entitlement-analysis/extract-fields
   */
  async extractAnalysisFields(req: Request, res: Response): Promise<void> {
    try {
      const { contractIds, forceReExtraction = false } = req.body;
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      if (
        !contractIds ||
        !Array.isArray(contractIds) ||
        contractIds.length === 0
      ) {
        res.status(400).json({ error: "Contract IDs are required" });
        return;
      }

      // Validate contract IDs format
      const uuidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      for (const contractId of contractIds) {
        if (!uuidPattern.test(contractId)) {
          res.status(400).json({
            error: `Invalid contract ID format: ${contractId}`
          });
          return;
        }
      }

      // Filter contracts that need extraction
      const contractsToProcess = [];
      for (const contractId of contractIds) {
        try {
          const extraction = await this.contractExtractionRepository.getByContractId(
            contractId,
            tenantId
          );

          if (!extraction) {
            logger.warn(`No extraction record found for contract ${contractId}`);
            continue;
          }

          // Check if analysis fields already exist and forceReExtraction is false
          if (extraction.analysisFields && !forceReExtraction) {
            logger.info(`Contract ${contractId} already has analysis fields, skipping`);
            continue;
          }

          contractsToProcess.push(contractId);
        } catch (error) {
          logger.error(`Error checking contract ${contractId}:`, error);
        }
      }

      if (contractsToProcess.length === 0) {
        res.status(200).json({
          message: "No contracts need analysis fields extraction",
          contractsQueued: 0,
          skipped: contractIds.length,
        });
        return;
      }

      // Create a job ID for tracking
      const jobId = uuidv4();
      const estimatedTimeSeconds = contractsToProcess.length * 30; // 30 seconds per contract

      logger.info(
        `Starting analysis fields extraction job ${jobId} for ${contractsToProcess.length} contracts`
      );

      // Start background processing (don't await)
      this.processExtractionJob(
        jobId,
        contractsToProcess,
        tenantId,
        userId,
        forceReExtraction
      ).catch((error) => {
        logger.error(`Extraction job ${jobId} failed:`, error);
      });

      res.status(202).json({
        jobId,
        contractsQueued: contractsToProcess.length,
        skipped: contractIds.length - contractsToProcess.length,
        estimatedTimeSeconds,
        statusEndpoint: `/api/entitlement-analysis/extraction-status/${jobId}`,
        message: "Analysis fields extraction started",
      });
    } catch (error) {
      logger.error("Error starting analysis fields extraction:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }



  /**
   * Gets extraction job status
   * @route GET /api/entitlement-analysis/extraction-status/:jobId
   */
  async getExtractionStatus(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;
      const tenantId = req.user!.tenantId;

      const job = EntitlementAnalysisController.extractionJobs.get(jobId);
      if (!job) {
        res.status(404).json({ error: "Job not found" });
        return;
      }

      // Verify tenant access
      if (job.tenantId !== tenantId) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      res.status(200).json({
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        results: job.results,
        createdAt: job.createdAt,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
      });
    } catch (error) {
      logger.error("Error getting extraction status:", error);
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Background job processor for analysis fields extraction
   */
  private async processExtractionJob(
    jobId: string,
    contractIds: string[],
    tenantId: string,
    userId: string,
    forceReExtraction: boolean
  ): Promise<void> {
    // Initialize job
    const job: ExtractionJob = {
      jobId,
      status: 'pending',
      contractIds,
      tenantId,
      userId,
      forceReExtraction,
      createdAt: new Date(),
      progress: {
        total: contractIds.length,
        completed: 0,
        failed: 0,
      },
      results: [],
    };

    EntitlementAnalysisController.extractionJobs.set(jobId, job);

    try {
      job.status = 'processing';
      job.startedAt = new Date();

      logger.info(`Processing extraction job ${jobId} for ${contractIds.length} contracts`);

      for (const contractId of contractIds) {
        job.progress.current = contractId;

        try {
          await this.extractAnalysisFieldsForContract(contractId, tenantId);

          job.results.push({
            contractId,
            status: 'success',
            fieldsExtracted: this.ANALYSIS_FIELDS_ORDER.length,
          });
          job.progress.completed++;

          logger.info(`Successfully extracted analysis fields for contract ${contractId}`);
        } catch (error) {
          logger.error(`Failed to extract analysis fields for contract ${contractId}:`, error);

          job.results.push({
            contractId,
            status: 'failed',
            error: (error as Error).message,
          });
          job.progress.failed++;
        }
      }

      job.status = 'completed';
      job.completedAt = new Date();
      job.progress.current = undefined;

      logger.info(`Extraction job ${jobId} completed. Success: ${job.progress.completed}, Failed: ${job.progress.failed}`);
    } catch (error) {
      logger.error(`Extraction job ${jobId} failed:`, error);
      job.status = 'failed';
      job.completedAt = new Date();
      job.progress.current = undefined;
    }
  }

  /**
   * Extracts analysis fields for a single contract
   */
  private async extractAnalysisFieldsForContract(
    contractId: string,
    tenantId: string
  ): Promise<void> {
    // Get the contract and its document
    const contract = await this.contractRepository.findById(contractId);
    if (!contract || contract.tenantId !== tenantId) {
      throw new Error(`Contract ${contractId} not found or access denied`);
    }

    // Get the document content using the same method that works for compliance analysis
    const documentData = await this.contractService.getContractDocument(
      contractId,
      tenantId
    );

    if (!documentData) {
      throw new Error(`No document found for contract ${contractId}`);
    }

    const documentBuffer = documentData.content;

    // Get the original filename
    const extraction = await this.contractExtractionRepository.getByContractId(
      contractId,
      tenantId
    );
    const fileName = extraction?.fixedFields?.original_filename?.value || documentData.fileName || contract.title;

    // Check if OCR text is available for this contract
    // Use the main extractAnalysisFields function which automatically handles OCR text
    logger.info(`Extracting analysis fields for contract: ${contractId}`);
    const analysisFields = await this.contractAIService.extractAnalysisFields(
      documentBuffer,
      fileName,
      contractId
    );

    if (!analysisFields || Object.keys(analysisFields).length === 0) {
      throw new Error(`No analysis fields extracted for contract ${contractId}`);
    }

    // Format the analysis fields
    const formattedAnalysisFields: Record<string, any> = {};
    Object.entries(analysisFields).forEach(([key, value]: [string, any]) => {
      if (key === "purchasing") {
        // Handle purchasing data (both new object format and legacy array)
        formattedAnalysisFields[key] = value;
      } else if (
        value &&
        typeof value === "object" &&
        value.value !== undefined
      ) {
        formattedAnalysisFields[key] = {
          value: value.value,
          confidence: value.confidence || 0.5,
        };
      }
    });

    // Add metadata
    formattedAnalysisFields.extractionDate = new Date();
    formattedAnalysisFields.processingTimeMs = Date.now();

    // Update the extraction record with analysis fields
    if (extraction) {
      await this.contractExtractionRepository.update(
        extraction.id,
        tenantId,
        {
          analysisFields: formattedAnalysisFields,
        }
      );
    } else {
      throw new Error(`No extraction record found for contract ${contractId}`);
    }

    logger.info(
      `Successfully updated contract ${contractId} with ${Object.keys(formattedAnalysisFields).length} analysis fields`
    );
  }
}
