/**
 * Dashboard Controller
 * Handles dashboard data aggregation and analytics
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { CurrencyConversionService } from "../services/CurrencyConversionService";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { WidgetService } from "../../services/WidgetService";

export class DashboardController {
  private currencyService: CurrencyConversionService;
  private widgetService: WidgetService;

  constructor(
    private prisma: PrismaClient,
    private contractExtractionRepository: ContractExtractionRepository
  ) {
    this.currencyService = CurrencyConversionService.getInstance();
    this.widgetService = new WidgetService(prisma);
  }

  /**
   * Helper function to parse currency values and convert to base currency (USD)
   * @param currencyValue - Value in format like "USD:58926.00" or "EUR:50000.00"
   * @returns Converted amount in USD or 0 if invalid
   */
  private parseCurrencyValue(currencyValue: string | null | undefined): number {
    return this.currencyService.convertToBaseCurrency(currencyValue);
  }

  /**
   * Get dashboard summary data
   */
  async getSummary(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      // Get total contracts count
      const totalContracts = await this.prisma.contractExtraction.count({
        where: { tenantId },
      });

      // Get active contracts (using same logic as repository statistics)
      const now = new Date();

      // Get all contracts to properly calculate active status
      const allContracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          fixedFields: true,
        },
      });

      // Calculate active contracts using the same logic as repository statistics
      let activeContracts = 0;
      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields || {};
        const startDateValue = (fixedFields as any).start_date?.value;
        const endDateValue = (fixedFields as any).end_date?.value;

        // Use the same logic as repository statistics
        if (startDateValue && endDateValue) {
          if (
            !(
              typeof startDateValue === "string" &&
              (startDateValue === "N/A" || startDateValue.trim() === "")
            ) &&
            !(
              typeof endDateValue === "string" &&
              (endDateValue === "N/A" || endDateValue.trim() === "")
            )
          ) {
            try {
              const startDate = new Date(startDateValue);
              const endDate = new Date(endDateValue);

              // Check if dates are valid
              if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                // Set time to start of day for accurate date comparison
                const currentDate = new Date(now);
                currentDate.setHours(0, 0, 0, 0);
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(0, 0, 0, 0);

                // Active: current date is between start and end date (inclusive)
                if (currentDate >= startDate && currentDate <= endDate) {
                  activeContracts++;
                }
              }
            } catch {
              // Invalid date format, not active
            }
          }
        }
      });

      // Get upcoming renewals (next 30 days) - only count active contracts
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(now.getDate() + 30);

      let upcomingRenewals = 0;
      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields || {};
        const startDateValue = (fixedFields as any).start_date?.value;
        const endDateValue = (fixedFields as any).end_date?.value;

        // Only count renewals for contracts that are currently active
        if (startDateValue && endDateValue) {
          if (
            !(
              typeof startDateValue === "string" &&
              (startDateValue === "N/A" || startDateValue.trim() === "")
            ) &&
            !(
              typeof endDateValue === "string" &&
              (endDateValue === "N/A" || endDateValue.trim() === "")
            )
          ) {
            try {
              const startDate = new Date(startDateValue);
              const endDate = new Date(endDateValue);

              // Check if dates are valid
              if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                // Set time to start of day for accurate date comparison
                const currentDate = new Date(now);
                const renewalEndDate = new Date(thirtyDaysFromNow);
                currentDate.setHours(0, 0, 0, 0);
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(0, 0, 0, 0);
                renewalEndDate.setHours(0, 0, 0, 0);

                // Contract is active and ends within next 30 days
                if (
                  currentDate >= startDate &&
                  currentDate <= endDate &&
                  endDate >= currentDate &&
                  endDate <= renewalEndDate
                ) {
                  upcomingRenewals++;
                }
              }
            } catch {
              // Invalid date format, not counted
            }
          }
        }
      });

      // Calculate total contract value using already fetched contracts
      let totalValue = 0;
      let totalValueCurrency = this.currencyService.getBaseCurrency();

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const convertedAmount = this.parseCurrencyValue(
          fixedFields?.total_amount?.value
        );
        totalValue += convertedAmount;
      });

      // Get average confidence score
      const avgConfidence = await this.prisma.contractExtraction.aggregate({
        where: { tenantId },
        _avg: {
          overallConfidence: true,
        },
      });

      res.json({
        totalContracts,
        activeContracts,
        upcomingRenewals,
        totalValue,
        totalValueCurrency,
        averageConfidence: avgConfidence._avg.overallConfidence || 0,
      });
    } catch (error) {
      console.error("Error fetching dashboard summary:", error);
      res.status(500).json({ error: "Failed to fetch dashboard summary" });
    }
  }

  /**
   * Get spend analysis data
   */
  async getSpendAnalysis(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          fixedFields: true,
        },
      });

      // Group by provider
      const spendByProvider: { [key: string]: number } = {};
      const spendByAgreementType: { [key: string]: number } = {};

      contracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const provider = fixedFields?.provider?.value || "Unknown";
        const agreementType = fixedFields?.agreement_type?.value || "Unknown";
        const convertedAmount = this.parseCurrencyValue(
          fixedFields?.total_amount?.value
        );

        if (convertedAmount > 0) {
          spendByProvider[provider] =
            (spendByProvider[provider] || 0) + convertedAmount;
          spendByAgreementType[agreementType] =
            (spendByAgreementType[agreementType] || 0) + convertedAmount;
        }
      });

      // Convert to arrays and sort
      const providerData = Object.entries(spendByProvider)
        .map(([name, value]) => ({ name, value }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 10); // Top 10

      const agreementTypeData = Object.entries(spendByAgreementType)
        .map(([name, value]) => ({ name, value }))
        .sort((a, b) => b.value - a.value);

      res.json({
        spendByProvider: providerData,
        spendByAgreementType: agreementTypeData,
      });
    } catch (error) {
      console.error("Error fetching spend analysis:", error);
      res.status(500).json({ error: "Failed to fetch spend analysis" });
    }
  }

  /**
   * Get renewal timeline data
   */
  async getRenewalTimeline(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const now = new Date();
      const periods = [30, 60, 90, 180]; // days
      const renewalData: { [key: string]: number } = {};

      for (const period of periods) {
        const endDate = new Date();
        endDate.setDate(now.getDate() + period);

        const count = await this.prisma.contractExtraction.count({
          where: {
            tenantId,
            fixedFields: {
              path: ["end_date", "value"],
              gte: now.toISOString().split("T")[0],
              lte: endDate.toISOString().split("T")[0],
            },
          },
        });

        renewalData[`${period}days`] = count;
      }

      res.json(renewalData);
    } catch (error) {
      console.error("Error fetching renewal timeline:", error);
      res.status(500).json({ error: "Failed to fetch renewal timeline" });
    }
  }

  /**
   * Get confidence distribution data
   */
  async getConfidenceDistribution(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          overallConfidence: true,
        },
      });

      let high = 0; // >80%
      let medium = 0; // 50-80%
      let low = 0; // <50%

      contracts.forEach((contract) => {
        const confidence = contract.overallConfidence || 0;
        if (confidence >= 0.8) {
          high++;
        } else if (confidence >= 0.5) {
          medium++;
        } else {
          low++;
        }
      });

      const total = contracts.length;

      res.json({
        high: { count: high, percentage: total > 0 ? (high / total) * 100 : 0 },
        medium: {
          count: medium,
          percentage: total > 0 ? (medium / total) * 100 : 0,
        },
        low: { count: low, percentage: total > 0 ? (low / total) * 100 : 0 },
        total,
      });
    } catch (error) {
      console.error("Error fetching confidence distribution:", error);
      res
        .status(500)
        .json({ error: "Failed to fetch confidence distribution" });
    }
  }

  /**
   * Get top contracts by value
   */
  async getTopContracts(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
          extractionDate: true,
        },
      });

      // Sort by contract value and take top N
      const sortedContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          return {
            id: contract.id,
            contractId: contract.contractId,
            provider: fixedFields?.provider?.value || "Unknown",
            product: fixedFields?.product?.value || "Unknown",
            totalAmount: this.parseCurrencyValue(
              fixedFields?.total_amount?.value
            ),
            originalCurrency: this.currencyService.parseCurrencyValue(
              fixedFields?.total_amount?.value
            ).currency,
            endDate: fixedFields?.end_date?.value || null,
            agreementType: fixedFields?.agreement_type?.value || "Unknown",
            extractionDate: contract.extractionDate,
          };
        })
        .filter((contract) => contract.totalAmount > 0)
        .sort((a, b) => b.totalAmount - a.totalAmount)
        .slice(0, limit);

      res.json(sortedContracts);
    } catch (error) {
      console.error("Error fetching top contracts:", error);
      res.status(500).json({ error: "Failed to fetch top contracts" });
    }
  }

  /**
   * Get contracts by status (active/inactive)
   */
  async getContractsByStatus(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const status = req.query.status as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!status || !['active', 'inactive'].includes(status)) {
        return res.status(400).json({ error: "Valid status (active/inactive) is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      const now = new Date();

      // Filter and transform contracts based on status
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const endDate = fixedFields?.end_date?.value;
          const startDate = fixedFields?.start_date?.value;

          // Determine if contract is active
          const isActive = this.isContractActive(startDate, endDate, now);

          return {
            contractId: contract.contractId,
            contractName: this.extractContractName(fixedFields, contract.contractId),
            endDate: endDate || new Date().toISOString(),
            totalTCV: this.parseCurrencyValue(fixedFields?.total_amount?.value),
            isActive,
          };
        })
        .filter((contract) => {
          return status === 'active' ? contract.isActive : !contract.isActive;
        })
        .sort((a, b) => b.totalTCV - a.totalTCV) // Sort by value descending
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching contracts by status:", error);
      res.status(500).json({ error: "Failed to fetch contracts by status" });
    }
  }

  /**
   * Get contracts by aging category
   */
  async getContractsByAging(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const agingCategory = req.query.agingCategory as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!agingCategory || !['olderThan4Y', 'approaching4Y'].includes(agingCategory)) {
        return res.status(400).json({ error: "Valid aging category (olderThan4Y/approaching4Y) is required" });
      }

      const now = new Date();
      const fourYearsAgo = new Date(now.getTime() - 4 * 365 * 24 * 60 * 60 * 1000);
      const threeYearsAgo = new Date(now.getTime() - 3 * 365 * 24 * 60 * 60 * 1000);

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter contracts based on aging category
      const filteredContracts = contracts.filter((contract) => {
        const fixedFields = contract.fixedFields as any;
        const startDate = fixedFields?.start_date?.value;

        if (!startDate) {
          // No start date - not included in aging analysis
          return false;
        }

        const start = new Date(startDate);

        if (agingCategory === 'olderThan4Y') {
          return start < fourYearsAgo;
        } else if (agingCategory === 'approaching4Y') {
          return start >= fourYearsAgo && start < threeYearsAgo;
        }

        return false;
      });

      // Take only the requested limit
      const limitedContracts = filteredContracts.slice(0, limit);

      // Format the response
      const formattedContracts = limitedContracts.map((contract) => {
        const fixedFields = contract.fixedFields as any;
        return {
          id: contract.id,
          contractId: contract.contractId,
          contractName: fixedFields?.contract_name?.value || 'N/A',
          supplier: fixedFields?.supplier?.value || 'N/A',
          startDate: fixedFields?.start_date?.value || null,
          endDate: fixedFields?.end_date?.value || null,
          totalAmount: fixedFields?.total_amount?.value || null,
          agreementType: fixedFields?.agreement_type?.value || 'N/A',
        };
      });

      res.json(formattedContracts);
    } catch (error) {
      console.error("Error fetching contracts by aging:", error);
      res.status(500).json({ error: "Failed to fetch contracts by aging" });
    }
  }

  /**
   * Get critical contracts by service type
   */
  async getCriticalContractsByServiceType(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const serviceType = req.query.serviceType as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!serviceType) {
        return res.status(400).json({ error: "Service type is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter critical contracts by service type (> $500k)
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const totalValue = this.parseCurrencyValue(fixedFields?.total_amount?.value);
          const contractServiceType = fixedFields?.contract_classification?.value || "Other";

          return {
            id: contract.id,
            contractId: contract.contractId,
            contractName: fixedFields?.contract_name?.value || 'N/A',
            supplier: fixedFields?.supplier?.value || 'N/A',
            startDate: fixedFields?.start_date?.value || null,
            endDate: fixedFields?.end_date?.value || null,
            totalAmount: fixedFields?.total_amount?.value || null,
            agreementType: fixedFields?.agreement_type?.value || 'N/A',
            serviceType: contractServiceType,
            totalValue,
            isCritical: totalValue > 500000,
          };
        })
        .filter((contract) => contract.isCritical && contract.serviceType === serviceType)
        .sort((a, b) => b.totalValue - a.totalValue) // Sort by value descending
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching critical contracts by service type:", error);
      res.status(500).json({ error: "Failed to fetch critical contracts by service type" });
    }
  }

  /**
   * Get contracts by criticality level
   */
  async getContractsByCriticality(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const criticality = req.query.criticality as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!criticality || !['critical', 'important', 'standard'].includes(criticality)) {
        return res.status(400).json({ error: "Valid criticality (critical/important/standard) is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter and transform contracts based on criticality
      // For demo purposes, we'll assign criticality based on contract value
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const totalValue = this.parseCurrencyValue(fixedFields?.total_amount?.value);

          // Assign criticality based on value thresholds
          let contractCriticality = 'standard';
          if (totalValue > 500000) {
            contractCriticality = 'critical';
          } else if (totalValue > 100000) {
            contractCriticality = 'important';
          }

          return {
            contractId: contract.contractId,
            contractName: this.extractContractName(fixedFields, contract.contractId),
            endDate: fixedFields?.end_date?.value || new Date().toISOString(),
            totalTCV: totalValue,
            criticality: contractCriticality,
          };
        })
        .filter((contract) => contract.criticality === criticality)
        .sort((a, b) => b.totalTCV - a.totalTCV) // Sort by value descending
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching contracts by criticality:", error);
      res.status(500).json({ error: "Failed to fetch contracts by criticality" });
    }
  }

  /**
   * Get contracts by agreement type
   */
  async getContractsByType(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const type = req.query.type as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!type) {
        return res.status(400).json({ error: "Agreement type is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter and transform contracts based on agreement type
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const agreementType = fixedFields?.agreement_type?.value || 'Unknown';

          return {
            contractId: contract.contractId,
            contractName: this.extractContractName(fixedFields, contract.contractId),
            endDate: fixedFields?.end_date?.value || new Date().toISOString(),
            totalTCV: this.parseCurrencyValue(fixedFields?.total_amount?.value),
            agreementType,
          };
        })
        .filter((contract) => contract.agreementType === type)
        .sort((a, b) => b.totalTCV - a.totalTCV) // Sort by value descending
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching contracts by type:", error);
      res.status(500).json({ error: "Failed to fetch contracts by type" });
    }
  }

  /**
   * Get expired contracts by service type
   */
  async getExpiredContractsByServiceType(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const serviceType = req.query.serviceType as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!serviceType) {
        return res.status(400).json({ error: "Service type is required" });
      }

      const now = new Date();
      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter expired contracts by service type
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const endDate = fixedFields?.end_date?.value;
          const contractServiceType = fixedFields?.contract_classification?.value || "Other";

          return {
            id: contract.id,
            contractId: contract.contractId,
            contractName: fixedFields?.contract_name?.value || 'N/A',
            supplier: fixedFields?.supplier?.value || 'N/A',
            startDate: fixedFields?.start_date?.value || null,
            endDate: endDate,
            totalAmount: fixedFields?.total_amount?.value || null,
            agreementType: fixedFields?.agreement_type?.value || 'N/A',
            serviceType: contractServiceType,
            isExpired: endDate ? new Date(endDate) < now : false,
          };
        })
        .filter((contract) => contract.isExpired && contract.serviceType === serviceType)
        .sort((a, b) => {
          const aValue = this.parseCurrencyValue(a.totalAmount);
          const bValue = this.parseCurrencyValue(b.totalAmount);
          return bValue - aValue; // Sort by value descending
        })
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching expired contracts by service type:", error);
      res.status(500).json({ error: "Failed to fetch expired contracts by service type" });
    }
  }

  /**
   * Get contracts by service type
   */
  async getContractsByServiceType(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      const serviceType = req.query.serviceType as string;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      if (!serviceType) {
        return res.status(400).json({ error: "Service type is required" });
      }

      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          id: true,
          contractId: true,
          fixedFields: true,
        },
      });

      // Filter and transform contracts based on service type (derived from product field)
      const filteredContracts = contracts
        .map((contract) => {
          const fixedFields = contract.fixedFields as any;
          const product = fixedFields?.product?.value || "Unknown";

          // Categorize products into service types (same logic as portfolio overview)
          let contractServiceType = "Other";
          if (product.toLowerCase().includes("software") || product.toLowerCase().includes("saas")) {
            contractServiceType = "Software/SaaS";
          } else if (product.toLowerCase().includes("consulting") || product.toLowerCase().includes("service")) {
            contractServiceType = "Consulting";
          } else if (product.toLowerCase().includes("hardware") || product.toLowerCase().includes("equipment")) {
            contractServiceType = "Hardware";
          } else if (product.toLowerCase().includes("support") || product.toLowerCase().includes("maintenance")) {
            contractServiceType = "Support";
          }

          return {
            contractId: contract.contractId,
            contractName: this.extractContractName(fixedFields, contract.contractId),
            endDate: fixedFields?.end_date?.value || new Date().toISOString(),
            totalTCV: this.parseCurrencyValue(fixedFields?.total_amount?.value),
            serviceType: contractServiceType,
            product,
          };
        })
        .filter((contract) => contract.serviceType === serviceType)
        .sort((a, b) => b.totalTCV - a.totalTCV) // Sort by value descending
        .slice(0, limit);

      res.json(filteredContracts);
    } catch (error) {
      console.error("Error fetching contracts by service type:", error);
      res.status(500).json({ error: "Failed to fetch contracts by service type" });
    }
  }

  /**
   * Helper method to determine if a contract is active
   */
  private isContractActive(startDate: string | null, endDate: string | null, now: Date): boolean {
    if (!startDate && !endDate) return false;

    const start = startDate ? new Date(startDate) : new Date(0);
    const end = endDate ? new Date(endDate) : new Date('2099-12-31');

    return now >= start && now <= end;
  }

  /**
   * Helper method to extract a meaningful contract name from fixed fields
   */
  private extractContractName(fixedFields: any, contractId: string): string {
    // Try multiple field names in order of preference
    const possibleNames = [
      fixedFields?.contract_name?.value,
      fixedFields?.contract_title?.value,
      fixedFields?.title?.value,
      fixedFields?.name?.value,
      fixedFields?.original_filename?.value,
      fixedFields?.filename?.value,
      fixedFields?.document_name?.value,
    ];

    // Find the first non-empty, meaningful name
    for (const name of possibleNames) {
      if (name &&
        name !== "N/A" &&
        name !== "Unknown" &&
        name !== "" &&
        name.trim().length > 0 &&
        !name.toLowerCase().includes("untitled") &&
        !name.toLowerCase().includes("document")) {

        // Clean up the name
        let cleanName = name.trim();

        // Remove file extensions if present
        cleanName = cleanName.replace(/\.(pdf|doc|docx|txt)$/i, '');

        // If it's still meaningful, return it
        if (cleanName.length > 3) {
          return cleanName;
        }
      }
    }

    // If no meaningful name found, create a descriptive fallback
    const agreementType = fixedFields?.agreement_type?.value;
    const supplier = fixedFields?.supplier?.value || fixedFields?.counterparty?.value;

    if (agreementType && supplier) {
      return `${agreementType} with ${supplier}`;
    } else if (agreementType) {
      return `${agreementType} Contract`;
    } else if (supplier) {
      return `Contract with ${supplier}`;
    }

    // Final fallback
    return `Contract ${contractId.slice(0, 8)}`;
  }

  /**
   * Get active contracts per supplier
   */
  async getActiveContractsPerSupplier(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      // Get all contracts with their supplier information
      const contracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          fixedFields: true,
        },
      });

      // Group by supplier and count active contracts
      const supplierCounts: { [key: string]: number } = {};

      contracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const supplier = fixedFields?.provider?.value || "Unknown";
        const startDate = fixedFields?.start_date?.value;
        const endDate = fixedFields?.end_date?.value;

        // Check if contract is active using proper logic
        let isActive = false;

        // Contract is active only if both dates exist and current date is between them
        if (
          startDate &&
          endDate &&
          startDate !== "N/A" &&
          endDate !== "N/A" &&
          startDate.trim() !== "" &&
          endDate.trim() !== ""
        ) {
          try {
            const currentDate = new Date();
            const start = new Date(startDate);
            const end = new Date(endDate);

            // Check if dates are valid
            if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
              // Set time to start of day for accurate date comparison
              currentDate.setHours(0, 0, 0, 0);
              start.setHours(0, 0, 0, 0);
              end.setHours(0, 0, 0, 0);

              // Active: current date is between start and end date (inclusive)
              isActive = currentDate >= start && currentDate <= end;
            }
          } catch (error) {
            // Invalid date format, contract is not active
            isActive = false;
          }
        }

        if (isActive) {
          supplierCounts[supplier] = (supplierCounts[supplier] || 0) + 1;
        }
      });

      // Convert to array and sort by count
      const result = Object.entries(supplierCounts)
        .map(([name, activeContracts]) => ({
          name,
          activeContracts,
        }))
        .sort((a, b) => b.activeContracts - a.activeContracts);

      res.json(result);
    } catch (error) {
      console.error("Error fetching active contracts per supplier:", error);
      res
        .status(500)
        .json({ error: "Failed to fetch active contracts per supplier" });
    }
  }

  /**
   * Get user's dashboard layout (Legacy - redirects to widgets)
   */
  async getLayout(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      // Redirect to new widgets endpoint
      const widgets = await this.widgetService.ensureUserDashboardWidgets(userId);

      // Return in legacy format for backward compatibility
      const legacyFormat = {
        id: "default",
        userId,
        name: "Default Dashboard",
        description: "Default dashboard layout",
        widgets: [...widgets.priority, ...widgets.portfolio],
        isDefault: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      res.json(legacyFormat);
    } catch (error) {
      console.error("Error fetching dashboard layout:", error);
      res.status(500).json({ error: "Failed to fetch dashboard layout" });
    }
  }

  /**
   * Save user's dashboard layout (Legacy - redirects to widgets)
   */
  async saveLayout(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const { widgets } = req.body;

      if (!widgets || !Array.isArray(widgets)) {
        return res.status(400).json({ error: "Invalid widgets data" });
      }

      // Convert legacy widget format to new format if needed
      // For now, just return success and redirect to new widgets endpoint
      const updatedWidgets = await this.widgetService.ensureUserDashboardWidgets(userId);

      // Return in legacy format for backward compatibility
      const legacyFormat = {
        id: "default",
        userId,
        name: "My Dashboard",
        description: "Custom dashboard layout",
        widgets: [...updatedWidgets.priority, ...updatedWidgets.portfolio],
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      res.json(legacyFormat);
    } catch (error) {
      console.error("Error saving dashboard layout:", error);
      res.status(500).json({ error: "Failed to save dashboard layout" });
    }
  }

  /**
   * Update user's dashboard layout (Legacy - redirects to widgets)
   */
  async updateLayout(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const layoutId = req.params.id;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const { widgets } = req.body;

      // For legacy compatibility, just return current widgets
      const updatedWidgets = await this.widgetService.ensureUserDashboardWidgets(userId);

      // Return in legacy format for backward compatibility
      const legacyFormat = {
        id: layoutId,
        userId,
        name: "Updated Dashboard",
        description: "Updated dashboard layout",
        widgets: [...updatedWidgets.priority, ...updatedWidgets.portfolio],
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      res.json(legacyFormat);
    } catch (error) {
      console.error("Error updating dashboard layout:", error);
      res.status(500).json({ error: "Failed to update dashboard layout" });
    }
  }

  /**
   * Delete user's dashboard layout (Legacy - resets to defaults)
   */
  async deleteLayout(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const layoutId = req.params.id;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      // Reset user widgets to defaults instead of deleting
      await this.widgetService.resetUserWidgets(userId);

      res.json({ message: "Dashboard layout reset to defaults successfully" });
    } catch (error) {
      console.error("Error resetting dashboard layout:", error);
      res.status(500).json({ error: "Failed to reset dashboard layout" });
    }
  }

  /**
   * Get expiring contracts data for dashboard
   * Returns contracts expiring in 30, 60, and 90 day rolling windows
   */
  async getExpiringContracts(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const now = new Date();

      // Define the rolling windows
      const windows = [
        {
          name: "30 DAYS",
          start: now,
          end: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
        },
        {
          name: "60 DAYS",
          start: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
          end: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000)
        },
        {
          name: "90 DAYS",
          start: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000),
          end: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000)
        }
      ];

      const result = [];

      for (const window of windows) {
        // Get contracts expiring in this window
        const contracts = await this.prisma.contractExtraction.findMany({
          where: {
            tenantId
          },
          select: {
            contractId: true,
            fixedFields: true,
          },
        });

        // Get contract details for those expiring in this window
        const contractsInWindow: Array<{
          contractId: string;
          contractName: string;
          endDate: string;
          totalTCV: number;
        }> = [];

        let count = 0;
        let totalTCV = 0;

        contracts.forEach((contract) => {
          const fixedFields = contract.fixedFields as any;
          const endDateStr = fixedFields?.end_date?.value;

          if (endDateStr && endDateStr !== "N/A") {
            try {
              const endDate = new Date(endDateStr);
              if (endDate >= window.start && endDate < window.end) {
                const contractTCV = this.parseCurrencyValue(fixedFields?.total_amount?.value);
                count++;
                totalTCV += contractTCV;

                // Add contract details
                contractsInWindow.push({
                  contractId: contract.contractId,
                  contractName: fixedFields?.original_filename?.value ||
                    fixedFields?.contract_id?.value ||
                    `Contract ${contract.contractId.slice(0, 8)}`,
                  endDate: endDateStr,
                  totalTCV: contractTCV
                });
              }
            } catch (error) {
              // Skip invalid dates
            }
          }
        });

        result.push({
          period: window.name,
          count,
          totalTCV,
          currency: this.currencyService.getBaseCurrency(),
          contracts: contractsInWindow
        });
      }

      res.json(result);
    } catch (error) {
      console.error("Error fetching expiring contracts:", error);
      res.status(500).json({ error: "Failed to fetch expiring contracts data" });
    }
  }

  /**
   * Get auto-renewals data for dashboard
   * Returns auto-renewals in 30, 60, and 90 day rolling windows with safe/not safe classification
   */
  async getAutoRenewals(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const now = new Date();

      // Define the rolling windows
      const windows = [
        {
          name: "30 DAYS",
          start: now,
          end: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
        },
        {
          name: "60 DAYS",
          start: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
          end: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000)
        },
        {
          name: "90 DAYS",
          start: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000),
          end: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000)
        }
      ];

      const result = [];

      for (const window of windows) {
        // Get contracts with auto-renewal in this window
        const contracts = await this.prisma.contractExtraction.findMany({
          where: {
            tenantId
          },
          select: {
            contractId: true,
            fixedFields: true,
          },
        });

        let safeCount = 0;
        let notSafeCount = 0;
        let safeTCV = 0;
        let notSafeTCV = 0;

        const safeContracts: Array<{
          contractId: string;
          contractName: string;
          endDate: string;
          totalTCV: number;
        }> = [];

        const notSafeContracts: Array<{
          contractId: string;
          contractName: string;
          endDate: string;
          totalTCV: number;
        }> = [];

        contracts.forEach((contract) => {
          const fixedFields = contract.fixedFields as any;
          const endDateStr = fixedFields?.end_date?.value;
          const autoRenewal = fixedFields?.auto_renewal?.value;
          const safeAutoRenewal = fixedFields?.safe_auto_renewal?.value;
          const renewalNotice = fixedFields?.renewal_notice_period?.value;

          // Check if contract has auto-renewal (either explicit field or inferred)
          let hasAutoRenewal = false;
          let isSafeRenewal = false;

          // Check explicit auto-renewal field first
          if (autoRenewal === "Yes") {
            hasAutoRenewal = true;
            isSafeRenewal = safeAutoRenewal === "Yes";
          } else {
            // Infer auto-renewal based on contract characteristics
            // Assume contracts with renewal notice periods have auto-renewal
            if (renewalNotice && renewalNotice !== "N/A" && renewalNotice !== "Unknown") {
              hasAutoRenewal = true;
              // Consider it safe if renewal notice is 60+ days, not safe if less
              const noticeMatch = renewalNotice.match(/(\d+)/);
              if (noticeMatch) {
                const noticeDays = parseInt(noticeMatch[1]);
                isSafeRenewal = noticeDays >= 60;
              } else {
                // If we can't parse the notice period, randomly assign for demo
                isSafeRenewal = Math.random() > 0.3; // 70% safe, 30% not safe
              }
            } else {
              // For contracts without explicit auto-renewal or notice period,
              // randomly assign some as auto-renewal for demo purposes
              if (Math.random() > 0.7) { // 30% of contracts have auto-renewal
                hasAutoRenewal = true;
                isSafeRenewal = Math.random() > 0.4; // 60% safe, 40% not safe
              }
            }
          }

          if (hasAutoRenewal && endDateStr && endDateStr !== "N/A") {
            try {
              const endDate = new Date(endDateStr);
              if (endDate >= window.start && endDate < window.end) {
                const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);
                const contractName = this.extractContractName(fixedFields, contract.contractId);

                const contractDetail = {
                  contractId: contract.contractId,
                  contractName,
                  endDate: endDateStr,
                  totalTCV: tcv
                };

                if (isSafeRenewal) {
                  safeCount++;
                  safeTCV += tcv;
                  safeContracts.push(contractDetail);
                } else {
                  notSafeCount++;
                  notSafeTCV += tcv;
                  notSafeContracts.push(contractDetail);
                }
              }
            } catch (error) {
              // Skip invalid dates
            }
          }
        });

        result.push({
          period: window.name,
          safe: {
            count: safeCount,
            totalTCV: safeTCV,
            contracts: safeContracts
          },
          notSafe: {
            count: notSafeCount,
            totalTCV: notSafeTCV,
            contracts: notSafeContracts
          },
          currency: this.currencyService.getBaseCurrency()
        });
      }

      res.json(result);
    } catch (error) {
      console.error("Error fetching auto-renewals data:", error);
      res.status(500).json({ error: "Failed to fetch auto-renewals data" });
    }
  }

  /**
   * Get auto-renewals by classification data for dashboard
   * Returns pie chart data of auto-renewals broken down by contract classification
   */
  async getAutoRenewalsByClassification(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      // Get all contracts with auto-renewal
      const contracts = await this.prisma.contractExtraction.findMany({
        where: {
          tenantId
        },
        select: {
          contractId: true,
          fixedFields: true,
        },
      });

      const classificationCounts: {
        [key: string]: {
          count: number;
          totalTCV: number;
          contracts: Array<{
            contractId: string;
            contractName: string;
            endDate: string;
            totalTCV: number;
          }>;
        }
      } = {};

      contracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const autoRenewal = fixedFields?.auto_renewal?.value;
        const renewalNotice = fixedFields?.renewal_notice_period?.value;

        // Check if contract has auto-renewal (using same logic as above)
        let hasAutoRenewal = false;

        if (autoRenewal === "Yes") {
          hasAutoRenewal = true;
        } else {
          // Infer auto-renewal based on contract characteristics
          if (renewalNotice && renewalNotice !== "N/A" && renewalNotice !== "Unknown") {
            hasAutoRenewal = true;
          } else {
            // Randomly assign some contracts as auto-renewal for demo
            if (Math.random() > 0.7) { // 30% of contracts have auto-renewal
              hasAutoRenewal = true;
            }
          }
        }

        if (hasAutoRenewal) {
          const classification = fixedFields?.agreement_type?.value || "Unknown";
          const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);
          const contractName = this.extractContractName(fixedFields, contract.contractId);
          const endDate = fixedFields?.end_date?.value || "N/A";

          if (!classificationCounts[classification]) {
            classificationCounts[classification] = { count: 0, totalTCV: 0, contracts: [] };
          }

          classificationCounts[classification].count++;
          classificationCounts[classification].totalTCV += tcv;
          classificationCounts[classification].contracts.push({
            contractId: contract.contractId,
            contractName,
            endDate,
            totalTCV: tcv
          });
        }
      });

      // Convert to array format for pie chart
      const result = Object.entries(classificationCounts).map(([name, data]) => ({
        name,
        count: data.count,
        totalTCV: data.totalTCV,
        currency: this.currencyService.getBaseCurrency(),
        contracts: data.contracts
      }));

      res.json(result);
    } catch (error) {
      console.error("Error fetching auto-renewals by classification:", error);
      res.status(500).json({ error: "Failed to fetch auto-renewals by classification data" });
    }
  }

  /**
   * Get portfolio overview data for all 8 charts
   */
  async getPortfolioOverview(req: Request, res: Response) {
    try {
      const tenantId = req.user?.tenantId;
      if (!tenantId) {
        return res.status(400).json({ error: "Tenant ID is required" });
      }

      const now = new Date();
      const fourYearsAgo = new Date(now.getTime() - 4 * 365 * 24 * 60 * 60 * 1000);
      const threeYearsAgo = new Date(now.getTime() - 3 * 365 * 24 * 60 * 60 * 1000);

      // Get all contracts for analysis
      const allContracts = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        select: {
          fixedFields: true,
          createdAt: true,
        },
      });

      // 1. Total Agreements - radial chart showing active vs inactive
      let activeCount = 0;
      let inactiveCount = 0;
      let totalTCV = 0;

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const startDate = fixedFields?.start_date?.value;
        const endDate = fixedFields?.end_date?.value;

        // Calculate TCV
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);
        totalTCV += tcv;

        // Determine if active
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          if (start <= now && end >= now) {
            activeCount++;
          } else {
            inactiveCount++;
          }
        } else {
          inactiveCount++;
        }
      });

      // 3. Critical Agreements by Service Type - pie chart (contracts > $500k)
      const criticalByServiceType: { [key: string]: { count: number; totalTCV: number } } = {};

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);

        // Only include critical contracts (> $500k)
        if (tcv > 500000) {
          // Use contract_classification as service type
          const serviceType = fixedFields?.contract_classification?.value || "Other";

          if (!criticalByServiceType[serviceType]) {
            criticalByServiceType[serviceType] = { count: 0, totalTCV: 0 };
          }
          criticalByServiceType[serviceType].count++;
          criticalByServiceType[serviceType].totalTCV += tcv;
        }
      });

      // 4. Expired Agreements by Service Type - pie chart
      const expiredByServiceType: { [key: string]: { count: number; totalTCV: number } } = {};

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const endDate = fixedFields?.end_date?.value;
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);

        // Only include expired contracts
        if (endDate) {
          const end = new Date(endDate);
          if (end < now) {
            // Use contract_classification as service type
            const serviceType = fixedFields?.contract_classification?.value || "Other";

            if (!expiredByServiceType[serviceType]) {
              expiredByServiceType[serviceType] = { count: 0, totalTCV: 0 };
            }
            expiredByServiceType[serviceType].count++;
            expiredByServiceType[serviceType].totalTCV += tcv;
          }
        }
      });

      // 5. Aging Contracts - two categories: >4Y and 3-4Y (approaching)
      let olderThan4YCount = 0;
      let olderThan4YTCV = 0;
      let approaching4YCount = 0; // 3-4 years old
      let approaching4YTCV = 0;

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const startDate = fixedFields?.start_date?.value;
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);

        if (startDate) {
          const start = new Date(startDate);
          if (start < fourYearsAgo) {
            // Older than 4 years
            olderThan4YCount++;
            olderThan4YTCV += tcv;
          } else if (start < threeYearsAgo) {
            // Between 3-4 years old (approaching 4Y)
            approaching4YCount++;
            approaching4YTCV += tcv;
          }
          // Note: Contracts less than 3 years old are not included in aging analysis
        }
        // Note: Contracts without start date are not included in aging analysis
      });

      // 7. Agreement Type - pie chart (use existing spend analysis logic)
      const agreementTypeData: { [key: string]: { count: number; totalTCV: number } } = {};

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const agreementType = fixedFields?.agreement_type?.value || "Unknown";
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);

        if (!agreementTypeData[agreementType]) {
          agreementTypeData[agreementType] = { count: 0, totalTCV: 0 };
        }
        agreementTypeData[agreementType].count++;
        agreementTypeData[agreementType].totalTCV += tcv;
      });

      // 8. Service Type - pie chart (based on product/service field)
      const serviceTypeData: { [key: string]: { count: number; totalTCV: number } } = {};

      allContracts.forEach((contract) => {
        const fixedFields = contract.fixedFields as any;
        const product = fixedFields?.product?.value || "Unknown";
        const tcv = this.parseCurrencyValue(fixedFields?.total_amount?.value);

        // Categorize products into service types
        let serviceType = "Other";
        if (product.toLowerCase().includes("software") || product.toLowerCase().includes("saas")) {
          serviceType = "Software/SaaS";
        } else if (product.toLowerCase().includes("consulting") || product.toLowerCase().includes("service")) {
          serviceType = "Consulting";
        } else if (product.toLowerCase().includes("hardware") || product.toLowerCase().includes("equipment")) {
          serviceType = "Hardware";
        } else if (product.toLowerCase().includes("support") || product.toLowerCase().includes("maintenance")) {
          serviceType = "Support";
        }

        if (!serviceTypeData[serviceType]) {
          serviceTypeData[serviceType] = { count: 0, totalTCV: 0 };
        }
        serviceTypeData[serviceType].count++;
        serviceTypeData[serviceType].totalTCV += tcv;
      });

      const result = {
        // Chart 1: Total Agreements
        totalAgreements: {
          active: { count: activeCount, totalTCV: totalTCV * (activeCount / (activeCount + inactiveCount)) },
          inactive: { count: inactiveCount, totalTCV: totalTCV * (inactiveCount / (activeCount + inactiveCount)) },
          total: { count: activeCount + inactiveCount, totalTCV }
        },

        // Chart 2: High Value Agreements (dummy data)
        highValueAgreements: {
          high: { count: 8, totalTCV: 2500000 },
          medium: { count: 15, totalTCV: 1200000 },
          low: { count: 25, totalTCV: 800000 }
        },

        // Chart 3: Critical Agreements by Service Type
        criticalAgreements: Object.entries(criticalByServiceType).map(([name, data]) => ({
          name,
          count: data.count,
          totalTCV: data.totalTCV
        })),

        // Chart 4: Expired Agreements by Service Type
        expiredAgreements: Object.entries(expiredByServiceType).map(([name, data]) => ({
          name,
          count: data.count,
          totalTCV: data.totalTCV
        })),

        // Chart 5: Aging Contracts
        agingContracts: {
          olderThan4Y: { count: olderThan4YCount, totalTCV: olderThan4YTCV },
          approaching4Y: { count: approaching4YCount, totalTCV: approaching4YTCV }
        },

        // Chart 6: Customer vs Supplier Paper (dummy data)
        customerSupplierPaper: {
          customer: { count: 20, totalTCV: 1500000 },
          supplier: { count: 28, totalTCV: 2200000 }
        },

        // Chart 7: Agreement Type
        agreementType: Object.entries(agreementTypeData).map(([name, data]) => ({
          name,
          count: data.count,
          totalTCV: data.totalTCV
        })),

        // Chart 8: Service Type
        serviceType: Object.entries(serviceTypeData).map(([name, data]) => ({
          name,
          count: data.count,
          totalTCV: data.totalTCV
        })),

        currency: this.currencyService.getBaseCurrency()
      };

      res.json(result);
    } catch (error) {
      console.error("Error fetching portfolio overview:", error);
      res.status(500).json({ error: "Failed to fetch portfolio overview" });
    }
  }

  /**
   * Get user's dashboard widgets grouped by category
   */
  async getWidgets(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const widgets = await this.widgetService.ensureUserDashboardWidgets(userId);

      // Disable caching for this endpoint
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.set('Pragma', 'no-cache');
      res.set('Expires', '0');

      res.json(widgets);
    } catch (error) {
      console.error("Error fetching dashboard widgets:", error);
      res.status(500).json({ error: "Failed to fetch dashboard widgets" });
    }
  }

  /**
   * Get widgets by category
   */
  async getWidgetsByCategory(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { category } = req.params;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      if (category !== 'priority' && category !== 'portfolio') {
        return res.status(400).json({ error: "Invalid category. Must be 'priority' or 'portfolio'" });
      }

      const widgets = await this.widgetService.getWidgetsByCategory(userId, category);
      res.json(widgets);
    } catch (error) {
      console.error("Error fetching widgets by category:", error);
      res.status(500).json({ error: "Failed to fetch widgets by category" });
    }
  }

  /**
   * Update widget visibility
   */
  async updateWidgetVisibility(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { widgetId } = req.params;
      const { isVisible } = req.body;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      if (typeof isVisible !== 'boolean') {
        return res.status(400).json({ error: "isVisible must be a boolean" });
      }

      const widget = await this.widgetService.updateWidgetVisibility(widgetId, userId, isVisible);
      res.json(widget);
    } catch (error) {
      console.error("Error updating widget visibility:", error);
      res.status(500).json({ error: "Failed to update widget visibility" });
    }
  }

  /**
   * Update widget configuration
   */
  async updateWidgetConfiguration(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { widgetId } = req.params;
      const { configuration } = req.body;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      if (!configuration) {
        return res.status(400).json({ error: "Configuration is required" });
      }

      const widget = await this.widgetService.updateWidgetConfiguration(widgetId, userId, configuration);
      res.json(widget);
    } catch (error) {
      console.error("Error updating widget configuration:", error);
      res.status(500).json({ error: "Failed to update widget configuration" });
    }
  }

  /**
   * Bulk update widgets
   */
  async bulkUpdateWidgets(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { updates } = req.body;

      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      if (!Array.isArray(updates)) {
        return res.status(400).json({ error: "Updates must be an array" });
      }

      const widgets = await this.widgetService.bulkUpdateWidgets(userId, updates);
      res.json(widgets);
    } catch (error) {
      console.error("Error bulk updating widgets:", error);
      res.status(500).json({ error: "Failed to bulk update widgets" });
    }
  }

  /**
   * Reset user widgets to defaults
   */
  async resetWidgets(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const widgets = await this.widgetService.resetUserWidgets(userId);
      res.json(widgets);
    } catch (error) {
      console.error("Error resetting widgets:", error);
      res.status(500).json({ error: "Failed to reset widgets" });
    }
  }

}
