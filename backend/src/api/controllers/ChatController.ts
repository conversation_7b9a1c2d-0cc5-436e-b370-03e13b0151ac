/**
 * Chat Controller
 * Handles persistent chat functionality with auto-deletion
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import { processGeminiChat } from "./GeminiController";

export class ChatController {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }
  /**
   * Gets all conversations for the authenticated user
   */
  async getConversations(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;

      const conversations = await this.prisma.conversation.findMany({
        where: {
          tenantId,
          userId,
        },
        orderBy: {
          updatedAt: "desc",
        },
        include: {
          _count: {
            select: {
              messages: true,
            },
          },
        },
      });

      res.status(200).json(conversations);
    } catch (error) {
      logger.error("Error fetching conversations:", error);
      res.status(500).json({ error: "Failed to fetch conversations" });
    }
  }

  /**
   * Creates a new conversation
   */
  async createConversation(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { title } = req.body;

      const conversation = await this.prisma.conversation.create({
        data: {
          title: title || "New Conversation",
          tenantId,
          userId,
        },
      });

      res.status(201).json(conversation);
    } catch (error) {
      logger.error("Error creating conversation:", error);
      res.status(500).json({ error: "Failed to create conversation" });
    }
  }

  /**
   * Gets a conversation by ID with messages
   */
  async getConversation(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { conversationId } = req.params;

      const conversation = await this.prisma.conversation.findFirst({
        where: {
          id: conversationId,
          tenantId,
          userId,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });

      if (!conversation) {
        res.status(404).json({ error: "Conversation not found" });
        return;
      }

      res.status(200).json(conversation);
    } catch (error) {
      logger.error("Error fetching conversation:", error);
      res.status(500).json({ error: "Failed to fetch conversation" });
    }
  }

  /**
   * Updates a conversation (e.g., rename)
   */
  async updateConversation(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { conversationId } = req.params;
      const { title } = req.body;

      const conversation = await this.prisma.conversation.updateMany({
        where: {
          id: conversationId,
          tenantId,
          userId,
        },
        data: {
          title,
          updatedAt: new Date(),
        },
      });

      if (conversation.count === 0) {
        res.status(404).json({ error: "Conversation not found" });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Error updating conversation:", error);
      res.status(500).json({ error: "Failed to update conversation" });
    }
  }

  /**
   * Deletes a conversation and all its messages
   */
  async deleteConversation(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { conversationId } = req.params;

      // First check if conversation exists and belongs to user
      const conversation = await this.prisma.conversation.findFirst({
        where: {
          id: conversationId,
          tenantId,
          userId,
        },
      });

      if (!conversation) {
        res.status(404).json({ error: "Conversation not found" });
        return;
      }

      // Delete messages first (cascade should handle this, but being explicit)
      await this.prisma.message.deleteMany({
        where: {
          conversationId,
        },
      });

      // Delete conversation
      await this.prisma.conversation.delete({
        where: {
          id: conversationId,
        },
      });

      res.status(204).send();
    } catch (error) {
      logger.error("Error deleting conversation:", error);
      res.status(500).json({ error: "Failed to delete conversation" });
    }
  }

  /**
   * Gets all messages for a conversation
   */
  async getMessages(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { conversationId } = req.params;

      // First check if conversation exists and belongs to user
      const conversation = await this.prisma.conversation.findFirst({
        where: {
          id: conversationId,
          tenantId,
          userId,
        },
      });

      if (!conversation) {
        res.status(404).json({ error: "Conversation not found" });
        return;
      }

      const messages = await this.prisma.message.findMany({
        where: {
          conversationId,
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      res.status(200).json(messages);
    } catch (error) {
      logger.error("Error fetching messages:", error);
      res.status(500).json({ error: "Failed to fetch messages" });
    }
  }

  /**
   * Sends a message in a conversation and gets AI response
   */
  async sendMessage(req: Request, res: Response): Promise<void> {
    try {
      const tenantId = req.user!.tenantId;
      const userId = req.user!.userId;
      const { conversationId } = req.params;
      const { message, options } = req.body;

      if (!message || !message.trim()) {
        res.status(400).json({ error: "Message content is required" });
        return;
      }

      // First check if conversation exists and belongs to user
      const conversation = await this.prisma.conversation.findFirst({
        where: {
          id: conversationId,
          tenantId,
          userId,
        },
      });

      if (!conversation) {
        res.status(404).json({ error: "Conversation not found" });
        return;
      }

      // Store user message
      await this.prisma.message.create({
        data: {
          conversationId,
          role: "user",
          content: message.trim(),
          metadata: options || {},
        },
      });

      // Get conversation history for context
      const recentMessages = await this.prisma.message.findMany({
        where: {
          conversationId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 10, // Last 10 messages for context
      });

      // Prepare conversation history for Gemini
      const conversationHistory = recentMessages
        .reverse()
        .slice(0, -1) // Exclude the message we just added
        .map((msg: any) => ({
          role: msg.role,
          content: msg.content,
        }));

      // Create a mock request/response for Gemini controller
      const mockReq = {
        body: {
          message: message.trim(),
          conversationHistory,
          documentIds: options?.ragOptions?.documentId
            ? [options.ragOptions.documentId]
            : undefined,
          taggedContracts: options?.taggedContracts,
        },
        user: req.user,
      } as Request;

      let geminiResponse: any;
      const mockRes = {
        status: (code: number) => ({
          json: (data: any) => {
            geminiResponse = { statusCode: code, ...data };
          },
        }),
      } as Response;

      // Call Gemini controller
      await processGeminiChat(mockReq, mockRes);

      if (!geminiResponse || !geminiResponse.success) {
        throw new Error("Failed to get AI response");
      }

      // Store AI response
      const aiMessage = await this.prisma.message.create({
        data: {
          conversationId,
          role: "assistant",
          content: geminiResponse.message.content,
          metadata: {
            model: "gemini-2.0-flash",
            ...geminiResponse.message.metadata,
          },
        },
      });

      // Update conversation timestamp
      await this.prisma.conversation.update({
        where: {
          id: conversationId,
        },
        data: {
          updatedAt: new Date(),
        },
      });

      res.status(200).json(aiMessage);
    } catch (error) {
      logger.error("Error sending message:", error);
      res.status(500).json({ error: "Failed to send message" });
    }
  }
}
