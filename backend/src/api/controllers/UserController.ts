/**
 * User Controller
 * Handles user management operations within an organization
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaService } from "../../infrastructure/services/PrismaService";
import { UserService } from "../../infrastructure/services/UserService";

export class UserController {
  private prisma: PrismaClient;
  private userService: UserService;

  constructor(userService: UserService, prisma?: PrismaClient) {
    this.userService = userService;
    this.prisma = prisma || PrismaService.getInstance();
  }

  /**
   * Get all users in the tenant
   * @param req Request
   * @param res Response
   */
  async getUsers(req: Request, res: Response): Promise<void> {
    try {
      // Get tenant ID from authenticated user
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      const users = await this.userService.getUsersByTenant(tenantId);
      res.status(200).json(users);
    } catch (error) {
      logger.error("Get users error:", { error });
      res.status(500).json({ error: "Failed to get users" });
    }
  }

  /**
   * Get a user by ID
   * @param req Request
   * @param res Response
   */
  async getUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      const user = await this.userService.getUserById(id, tenantId);

      if (!user) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      res.status(200).json(user);
    } catch (error) {
      logger.error("Get user error:", { error });
      res.status(500).json({ error: "Failed to get user" });
    }
  }

  /**
   * Create a new user in the tenant
   * @param req Request
   * @param res Response
   */
  async createUser(req: Request, res: Response): Promise<void> {
    try {
      const { email, name, password, roleId } = req.body;
      const tenantId = req.user?.tenantId;
      const createdBy = req.user?.userId;

      // Validate request
      if (!email || !name || !password) {
        res.status(400).json({ error: "Email, name, and password are required" });
        return;
      }

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        res.status(409).json({ error: "User with this email already exists" });
        return;
      }

      // Create user
      const user = await this.userService.createUser({
        email,
        name,
        password,
        tenantId,
        roleId,
        createdBy,
      });

      res.status(201).json({
        message: "User created successfully",
        userId: user.id,
      });
    } catch (error) {
      logger.error("Create user error:", { error });
      res.status(500).json({
        error: `User creation failed: ${(error as Error).message}`,
      });
    }
  }

  /**
   * Update a user
   * @param req Request
   * @param res Response
   */
  async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, email, status, roleId } = req.body;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if user exists and belongs to the tenant
      const existingUser = await this.userService.getUserById(id, tenantId);

      if (!existingUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      // Update user
      const updatedUser = await this.userService.updateUser(id, {
        name,
        email,
        status,
        roleId,
      });

      res.status(200).json({
        message: "User updated successfully",
        user: updatedUser,
      });
    } catch (error) {
      logger.error("Update user error:", { error });
      res.status(500).json({
        error: `User update failed: ${(error as Error).message}`,
      });
    }
  }

  /**
   * Delete a user
   * @param req Request
   * @param res Response
   */
  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      // Check if user exists and belongs to the tenant
      const existingUser = await this.userService.getUserById(id, tenantId);

      if (!existingUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      // Delete user
      await this.userService.deleteUser(id, tenantId);

      res.status(200).json({
        message: "User deleted successfully",
      });
    } catch (error) {
      logger.error("Delete user error:", { error });
      res.status(500).json({
        error: `User deletion failed: ${(error as Error).message}`,
      });
    }
  }
}
