/**
 * Tenant Settings Controller
 * Handles HTTP requests for tenant settings and white-labeling
 */

import { Request, Response } from 'express';
import { TenantSettingsService } from '../services/TenantSettingsService';
import { logger } from '../../infrastructure/logging/logger';

/**
 * Controller for tenant settings and white-labeling endpoints
 */
export class TenantSettingsController {
  private tenantSettingsService: TenantSettingsService;

  constructor(tenantSettingsService: TenantSettingsService) {
    this.tenantSettingsService = tenantSettingsService;
  }

  /**
   * Gets tenant settings
   * @route GET /api/tenant-settings/:tenantId
   */
  async getTenantSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;

      const settings = await this.tenantSettingsService.getTenantSettings(tenantId);

      if (!settings) {
        res.status(404).json({ error: `Settings for tenant with ID ${tenantId} not found` });
        return;
      }

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in getTenantSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates tenant settings
   * @route PUT /api/tenant-settings/:tenantId
   */
  async updateTenantSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const {
        branding,
        preferences,
        whiteLabeling,
        customDomain,
        domainVerified,
        emailConfiguration,
        emailTemplates,
        themeSettings,
        loginPageSettings
      } = req.body;

      const settings = await this.tenantSettingsService.updateTenantSettings(tenantId, {
        branding,
        preferences,
        whiteLabeling,
        customDomain,
        domainVerified,
        emailConfiguration,
        emailTemplates,
        themeSettings,
        loginPageSettings
      });

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateTenantSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets white-labeling settings
   * @route GET /api/tenant-settings/:tenantId/white-labeling
   */
  async getWhiteLabelingSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;

      const settings = await this.tenantSettingsService.getWhiteLabelingSettings(tenantId);

      if (!settings) {
        res.status(404).json({ error: `Settings for tenant with ID ${tenantId} not found` });
        return;
      }

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in getWhiteLabelingSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates white-labeling settings
   * @route PUT /api/tenant-settings/:tenantId/white-labeling
   */
  async updateWhiteLabeling(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { whiteLabeling } = req.body;

      // Validate required fields
      if (!whiteLabeling) {
        res.status(400).json({ error: 'whiteLabeling is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateWhiteLabeling(tenantId, whiteLabeling);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateWhiteLabeling controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates branding settings
   * @route PUT /api/tenant-settings/:tenantId/branding
   */
  async updateBranding(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { branding } = req.body;

      // Validate required fields
      if (!branding) {
        res.status(400).json({ error: 'branding is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateBranding(tenantId, branding);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateBranding controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates theme settings
   * @route PUT /api/tenant-settings/:tenantId/theme
   */
  async updateThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { themeSettings } = req.body;

      // Validate required fields
      if (!themeSettings) {
        res.status(400).json({ error: 'themeSettings is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateThemeSettings(tenantId, themeSettings);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateThemeSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates login page settings
   * @route PUT /api/tenant-settings/:tenantId/login-page
   */
  async updateLoginPageSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { loginPageSettings } = req.body;

      // Validate required fields
      if (!loginPageSettings) {
        res.status(400).json({ error: 'loginPageSettings is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateLoginPageSettings(tenantId, loginPageSettings);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateLoginPageSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates email configuration
   * @route PUT /api/tenant-settings/:tenantId/email-configuration
   */
  async updateEmailConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { emailConfiguration } = req.body;

      // Validate required fields
      if (!emailConfiguration) {
        res.status(400).json({ error: 'emailConfiguration is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateEmailConfiguration(tenantId, emailConfiguration);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateEmailConfiguration controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates email templates
   * @route PUT /api/tenant-settings/:tenantId/email-templates
   */
  async updateEmailTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { emailTemplates } = req.body;

      // Validate required fields
      if (!emailTemplates) {
        res.status(400).json({ error: 'emailTemplates is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateEmailTemplates(tenantId, emailTemplates);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateEmailTemplates controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates custom domain settings
   * @route PUT /api/tenant-settings/:tenantId/custom-domain
   */
  async updateCustomDomain(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const { customDomain, domainVerified } = req.body;

      // Validate required fields
      if (!customDomain) {
        res.status(400).json({ error: 'customDomain is required' });
        return;
      }

      const settings = await this.tenantSettingsService.updateCustomDomain(
        tenantId,
        customDomain,
        domainVerified
      );

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in updateCustomDomain controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Verifies a custom domain
   * @route POST /api/tenant-settings/:tenantId/verify-domain
   */
  async verifyCustomDomain(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;

      const settings = await this.tenantSettingsService.verifyCustomDomain(tenantId);

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in verifyCustomDomain controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets email settings
   * @route GET /api/tenant-settings/:tenantId/email
   */
  async getEmailSettings(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;

      const settings = await this.tenantSettingsService.getEmailSettings(tenantId);

      if (!settings) {
        res.status(404).json({ error: `Settings for tenant with ID ${tenantId} not found` });
        return;
      }

      res.status(200).json(settings);
    } catch (error) {
      logger.error('Error in getEmailSettings controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }
}
