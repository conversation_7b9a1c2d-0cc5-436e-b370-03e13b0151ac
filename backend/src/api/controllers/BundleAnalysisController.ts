/**
 * Bundle Analysis Controller
 * Handles bundle interconnection analysis for contract folders
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { ContractAIService } from "../services/ContractAIService";
import { logger } from "../../infrastructure/logging/logger";

export class BundleAnalysisController {
  constructor(
    private prisma: PrismaClient,
    private contractAIService: ContractAIService
  ) { }

  /**
   * Gets bundle analysis for a folder if it exists, otherwise returns null
   * @route GET /api/bundle-analysis/:folderId
   */
  async getBundleAnalysis(req: Request, res: Response): Promise<void> {
    try {
      const { folderId } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      logger.info(`Getting bundle analysis for folder: ${folderId}`);

      // Handle virtual folders (e.g., "virtual-Nalta" for provider groupings)
      let folderName: string;
      let contracts: any[];

      if (folderId.startsWith('virtual-')) {
        // Extract provider name from virtual folder ID (e.g., "virtual-Nalta" -> "Nalta")
        const supplierName = folderId.replace('virtual-', '');
        folderName = supplierName;

        // Get all contract extractions for this tenant
        const contractExtractions = await this.prisma.contractExtraction.findMany({
          where: {
            tenantId: tenantId,
          },
          include: {
            contract: true,
          },
        });

        // Filter contracts by supplier name using the same logic as folder grouping
        // (first word of provider name, normalized to title case)
        const matchingExtractions = contractExtractions.filter((extraction) => {
          const fixedFields = extraction.fixedFields as any;
          const provider = fixedFields?.provider?.value || "";
          if (!provider || provider === "Unknown Provider") return false;

          // Extract first word and normalize to title case (same logic as FolderController)
          const rawSupplierName = provider.split(/\s+/)[0];
          const normalizedSupplierName =
            rawSupplierName.charAt(0).toUpperCase() +
            rawSupplierName.slice(1).toLowerCase();

          return normalizedSupplierName === supplierName;
        });

        if (matchingExtractions.length === 0) {
          res.status(404).json({ error: "No contracts found for this provider" });
          return;
        }

        // Extract contracts from the matching extractions
        contracts = matchingExtractions.map(extraction => extraction.contract);
      } else {
        // Handle real folder IDs
        const folder = await this.prisma.folder.findFirst({
          where: {
            id: folderId,
            tenantId: tenantId,
          },
          include: {
            contracts: true,
          },
        });

        if (!folder) {
          res.status(404).json({ error: "Folder not found" });
          return;
        }

        folderName = folder.name;
        contracts = folder.contracts;
      }

      // Get existing bundle analysis
      const bundleAnalysis = await (this.prisma as any).providerBundle.findUnique({
        where: {
          folderId: folderId,
        },
      });

      if (!bundleAnalysis) {
        // No analysis exists yet
        res.status(200).json({
          exists: false,
          folderId: folderId,
          folderName: folderName,
          message: "No analysis found. Click 'Fetch Data' to generate analysis.",
        });
        return;
      }

      // Return existing analysis
      res.status(200).json({
        exists: true,
        folderId: folderId,
        folderName: folderName,
        analysis: bundleAnalysis.analysisData,
        contractIds: bundleAnalysis.contractIds,
        extractionDate: bundleAnalysis.extractionDate,
        processingTimeMs: bundleAnalysis.processingTimeMs,
        overallConfidence: bundleAnalysis.overallConfidence,
        modelUsed: bundleAnalysis.modelUsed,
        createdAt: bundleAnalysis.createdAt,
        updatedAt: bundleAnalysis.updatedAt,
      });
    } catch (error) {
      logger.error("Error getting bundle analysis:", { error, folderId: req.params.folderId });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Triggers new bundle analysis for a folder using Gemini AI
   * @route POST /api/bundle-analysis/:folderId
   */
  async generateBundleAnalysis(req: Request, res: Response): Promise<void> {
    try {
      const { folderId } = req.params;
      const tenantId = req.user?.tenantId;

      if (!tenantId) {
        res.status(400).json({ error: "Tenant ID is required" });
        return;
      }

      logger.info(`Generating bundle analysis for folder: ${folderId}`);

      // Handle virtual folders (e.g., "virtual-Nalta" for provider groupings)
      let folderName: string;
      let contracts: any[];

      if (folderId.startsWith('virtual-')) {
        // Extract provider name from virtual folder ID (e.g., "virtual-Nalta" -> "Nalta")
        const supplierName = folderId.replace('virtual-', '');
        folderName = supplierName;

        // Get all contract extractions for this tenant
        const contractExtractions = await this.prisma.contractExtraction.findMany({
          where: {
            tenantId: tenantId,
          },
          include: {
            contract: true,
          },
        });

        // Filter contracts by supplier name using the same logic as folder grouping
        // (first word of provider name, normalized to title case)
        const matchingExtractions = contractExtractions.filter((extraction) => {
          const fixedFields = extraction.fixedFields as any;
          const provider = fixedFields?.provider?.value || "";
          if (!provider || provider === "Unknown Provider") return false;

          // Extract first word and normalize to title case (same logic as FolderController)
          const rawSupplierName = provider.split(/\s+/)[0];
          const normalizedSupplierName =
            rawSupplierName.charAt(0).toUpperCase() +
            rawSupplierName.slice(1).toLowerCase();

          return normalizedSupplierName === supplierName;
        });

        if (matchingExtractions.length === 0) {
          res.status(400).json({ error: "No contracts found for this provider" });
          return;
        }

        // Extract contracts from the matching extractions
        contracts = matchingExtractions.map(extraction => extraction.contract);
      } else {
        // Handle real folder IDs
        const folder = await this.prisma.folder.findFirst({
          where: {
            id: folderId,
            tenantId: tenantId,
          },
          include: {
            contracts: true,
          },
        });

        if (!folder) {
          res.status(404).json({ error: "Folder not found" });
          return;
        }

        if (!folder.contracts || folder.contracts.length === 0) {
          res.status(400).json({ error: "No contracts found in this folder" });
          return;
        }

        folderName = folder.name;
        contracts = folder.contracts;
      }

      // Get contract IDs
      const contractIds = contracts.map(contract => contract.id);

      logger.info(`Found ${contractIds.length} contracts in folder for analysis`);

      // Prepare contract data for Gemini analysis
      const contractsData = await this.prepareContractsDataForAnalysis(contractIds, tenantId);

      if (contractsData.length === 0) {
        res.status(400).json({ error: "No contract extraction data found for analysis" });
        return;
      }

      // Generate bundle analysis using Gemini
      const startTime = Date.now();
      const analysisResult = await this.contractAIService.generateBundleInterconnectionAnalysis(
        contractsData,
        folderName
      );
      const processingTimeMs = Date.now() - startTime;

      // Store or update the analysis result
      const bundleData = {
        folderId: folderId,
        tenantId: tenantId,
        analysisData: analysisResult,
        contractIds: contractIds,
        extractionDate: new Date(),
        processingTimeMs: processingTimeMs,
        modelUsed: "gemini-2.5-pro",
        overallConfidence: analysisResult.overallConfidence || null,
      };

      logger.info("Attempting to save bundle data:", {
        folderId,
        tenantId,
        contractIdsCount: contractIds.length,
        analysisDataSize: JSON.stringify(analysisResult).length,
        hasOverallConfidence: !!analysisResult.overallConfidence
      });

      let savedBundle;
      try {
        savedBundle = await (this.prisma as any).providerBundle.upsert({
          where: {
            folderId: folderId,
          },
          update: {
            ...bundleData,
            updatedAt: new Date(),
          },
          create: bundleData,
        });
        logger.info("Successfully saved bundle data to database");
      } catch (dbError) {
        logger.error("Database save error:", {
          error: dbError,
          folderId,
          analysisDataType: typeof analysisResult,
          analysisDataKeys: Object.keys(analysisResult || {})
        });
        throw new Error(`Failed to save bundle analysis: ${(dbError as Error).message}`);
      }

      logger.info(`Successfully generated and stored bundle analysis for folder: ${folderId}`);

      res.status(200).json({
        success: true,
        folderId: folderId,
        folderName: folderName,
        analysis: savedBundle.analysisData,
        contractIds: savedBundle.contractIds,
        extractionDate: savedBundle.extractionDate,
        processingTimeMs: savedBundle.processingTimeMs,
        overallConfidence: savedBundle.overallConfidence,
        message: "Bundle analysis generated successfully",
      });
    } catch (error) {
      logger.error("Error generating bundle analysis:", { error, folderId: req.params.folderId });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Prepares contract data for Gemini analysis
   */
  private async prepareContractsDataForAnalysis(contractIds: string[], tenantId: string) {
    const contractsData = [];

    for (const contractId of contractIds) {
      try {
        // Get contract with extraction data
        const contract = await this.prisma.contract.findFirst({
          where: {
            id: contractId,
            tenantId: tenantId,
          },
        });

        const extraction = await this.prisma.contractExtraction.findUnique({
          where: {
            contractId: contractId,
          },
        });

        if (contract && extraction) {
          contractsData.push({
            contractId: contract.id,
            documentName: contract.title,
            agreementType: contract.agreementType,
            provider: contract.provider,
            startDate: contract.startDate,
            endDate: contract.endDate,
            value: contract.value,
            fixedFields: extraction.fixedFields,
            dynamicFields: extraction.dynamicFields,
            specialFields: extraction.specialFields,
            // Use extraction data only - no OCR text needed
            overallConfidence: extraction.overallConfidence,
          });
        }
      } catch (error) {
        logger.warn(`Failed to prepare data for contract ${contractId}:`, error);
        // Continue with other contracts
      }
    }

    return contractsData;
  }
}
