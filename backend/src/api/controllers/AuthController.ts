/**
 * Authentication Controller
 * Handles user authentication, registration, and session management
 */

import { Request, Response } from "express";
import * as jwt from "jsonwebtoken";
import { AuthService } from "../../infrastructure/services/AuthService";
import { logger } from "../../infrastructure/logging/logger";
import { PrismaClient } from "@prisma/client";
import { PrismaService } from "../../infrastructure/services/PrismaService";
import { ConfigService } from "../../infrastructure/services/ConfigService";

export class AuthController {
  private authService: AuthService;
  private prisma: PrismaClient;
  private configService: ConfigService;

  constructor(
    authService: AuthService,
    prisma?: PrismaClient,
    configService?: ConfigService
  ) {
    this.authService = authService;
    this.prisma = prisma || PrismaService.getInstance();
    this.configService = configService || new ConfigService();
  }

  /**
   * Login a user with email and password
   * @param req Request
   * @param res Response
   */
  login(req: Request, res: Response): void {
    try {
      const { email, password } = req.body;

      // Log login attempt (without password)
      logger.info("Login attempt", {
        email: email?.toLowerCase(),
        ip: req.ip,
        userAgent: req.headers["user-agent"],
        timestamp: new Date().toISOString(),
      });

      // Validate request
      if (!email || !password) {
        logger.warn("Login validation failed: Missing credentials", {
          email: email?.toLowerCase(),
          hasPassword: !!password,
          ip: req.ip,
        });
        res.status(400).json({ error: "Email and password are required" });
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        logger.warn("Login validation failed: Invalid email format", {
          email: email.toLowerCase(),
          ip: req.ip,
        });
        res.status(400).json({ error: "Invalid email format" });
        return;
      }

      // Authenticate user
      this.authService
        .authenticate(email.toLowerCase().trim(), password)
        .then((tokenResponse) => {
          logger.info("Login successful", {
            email: email.toLowerCase(),
            userId: tokenResponse.userProfile?.id,
            tenantId: tokenResponse.userProfile?.tenantId,
            ip: req.ip,
          });
          // Return tokens
          res.status(200).json(tokenResponse);
        })
        .catch((error) => {
          const errorMessage = (error as Error).message;

          logger.error("Login error:", {
            error:
              error instanceof Error
                ? {
                    message: error.message,
                    stack: error.stack,
                    name: error.name,
                  }
                : error,
            email: email.toLowerCase(),
            ip: req.ip,
            userAgent: req.headers["user-agent"],
            timestamp: new Date().toISOString(),
          });

          // Handle specific error messages
          if (
            errorMessage.includes("User not found") ||
            errorMessage.includes("Invalid password")
          ) {
            res.status(401).json({
              error: "Invalid email or password",
              code: "INVALID_CREDENTIALS",
            });
          } else if (errorMessage.includes("not active")) {
            res.status(403).json({
              error: "Account is not active",
              code: "ACCOUNT_INACTIVE",
            });
          } else if (errorMessage.includes("not associated with any tenant")) {
            res.status(500).json({
              error: "Account configuration error. Please contact support.",
              code: "TENANT_ASSOCIATION_ERROR",
            });
          } else if (errorMessage.includes("default roles")) {
            res.status(500).json({
              error: "Account setup incomplete. Please contact support.",
              code: "ROLE_SETUP_ERROR",
            });
          } else {
            res.status(500).json({
              error: "Authentication failed. Please try again.",
              code: "AUTH_ERROR",
            });
          }
        });
    } catch (error) {
      logger.error("Login controller error:", {
        error:
          error instanceof Error
            ? {
                message: error.message,
                stack: error.stack,
                name: error.name,
              }
            : error,
        ip: req.ip,
        timestamp: new Date().toISOString(),
      });
      res.status(500).json({
        error: "Authentication failed. Please try again.",
        code: "CONTROLLER_ERROR",
      });
    }
  }

  /**
   * Register a new user
   * @param req Request
   * @param res Response
   */
  register(req: Request, res: Response): void {
    try {
      const { email, password, name, tenantId, company, plan } = req.body;

      // Validate request
      if (!email || !password || !name) {
        res
          .status(400)
          .json({ error: "Email, password, and name are required" });
        return;
      }

      // Check if user already exists
      this.prisma.user
        .findUnique({
          where: { email },
        })
        .then((existingUser) => {
          if (existingUser) {
            res
              .status(409)
              .json({ error: "User with this email already exists" });
            return;
          }

          // Create user and tenant
          this.authService
            .registerUser(email, password, name, tenantId, company, plan)
            .then((user) => {
              // Return success
              res.status(201).json({
                message: "User registered successfully",
                userId: user.id,
              });
            })
            .catch((error) => {
              logger.error("Registration error:", { error });
              res.status(500).json({
                error: `Registration failed: ${(error as Error).message}`,
              });
            });
        })
        .catch((error) => {
          logger.error("Registration error:", { error });
          res.status(500).json({
            error: `Registration failed: ${(error as Error).message}`,
          });
        });
    } catch (error) {
      logger.error("Registration error:", { error });
      res
        .status(500)
        .json({ error: `Registration failed: ${(error as Error).message}` });
    }
  }

  /**
   * Refresh an access token using a refresh token
   * @param req Request
   * @param res Response
   */
  refreshToken(req: Request, res: Response): void {
    try {
      const { refreshToken } = req.body;

      // Validate request
      if (!refreshToken) {
        res.status(400).json({ error: "Refresh token is required" });
        return;
      }

      // Refresh token
      this.authService
        .refreshToken(refreshToken)
        .then((tokenResponse) => {
          // Return new tokens
          res.status(200).json(tokenResponse);
        })
        .catch((error) => {
          logger.error("Token refresh error:", { error });
          res.status(401).json({ error: "Invalid refresh token" });
        });
    } catch (error) {
      logger.error("Token refresh error:", { error });
      res.status(401).json({ error: "Invalid refresh token" });
    }
  }

  /**
   * Logout a user by revoking their session
   * @param req Request
   * @param res Response
   */
  logout(req: Request, res: Response): void {
    try {
      // Get authorization header
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        res.status(401).json({ error: "Not authenticated" });
        return;
      }

      // Extract token
      const token = authHeader.split(" ")[1];

      if (!token) {
        res.status(401).json({ error: "Invalid token format" });
        return;
      }

      // Decode token to get user info
      try {
        const decoded = jwt.verify(
          token,
          this.configService.getAccessTokenSecret()
        ) as any;

        // Revoke session
        this.authService
          .logout(decoded.userId, decoded.sessionId || "unknown")
          .then(() => {
            // Return success
            res.status(200).json({ message: "Logged out successfully" });
          })
          .catch((error) => {
            logger.error("Logout error:", { error });
            res.status(500).json({ error: "Logout failed" });
          });
      } catch (jwtError) {
        logger.error("JWT verification error:", { jwtError });
        res.status(401).json({ error: "Invalid token" });
      }
    } catch (error) {
      logger.error("Logout error:", { error });
      res.status(500).json({ error: "Logout failed" });
    }
  }

  /**
   * Get the current user's profile
   * @param req Request
   * @param res Response
   */
  getProfile(req: Request, res: Response): void {
    try {
      // Get authorization header
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        res.status(401).json({ error: "Not authenticated" });
        return;
      }

      // Extract token
      const token = authHeader.split(" ")[1];

      if (!token) {
        res.status(401).json({ error: "Invalid token format" });
        return;
      }

      // Decode token to get user info
      try {
        const decoded = jwt.verify(
          token,
          this.configService.getAccessTokenSecret()
        ) as any;

        // Get user profile
        this.prisma.user
          .findUnique({
            where: { id: decoded.userId },
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              role: true,
              persona: true,
              createdAt: true,
              updatedAt: true,
              tenantUsers: {
                include: {
                  tenant: {
                    select: {
                      id: true,
                      name: true,
                      tier: true,
                      status: true,
                    },
                  },
                },
              },
            },
          })
          .then((userProfile) => {
            if (!userProfile) {
              res.status(404).json({ error: "User not found" });
              return;
            }

            // Return user profile
            res.status(200).json(userProfile);
          })
          .catch((error) => {
            logger.error("Get profile error:", { error });
            res.status(500).json({ error: "Failed to get user profile" });
          });
      } catch (jwtError) {
        logger.error("JWT verification error:", { jwtError });
        res.status(401).json({ error: "Invalid token" });
      }
    } catch (error) {
      logger.error("Get profile error:", { error });
      res.status(500).json({ error: "Failed to get user profile" });
    }
  }

  /**
   * Update user persona
   * @param req Request
   * @param res Response
   */
  updatePersona(req: Request, res: Response): void {
    try {
      const { persona } = req.body;
      const token = req.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        res.status(401).json({ error: "No token provided" });
        return;
      }

      if (!persona) {
        res.status(400).json({ error: "Persona is required" });
        return;
      }

      // Verify token
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

        // Update user persona
        this.prisma.user
          .update({
            where: { id: decoded.userId },
            data: { persona },
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              role: true,
              persona: true,
              createdAt: true,
              updatedAt: true,
            },
          })
          .then((updatedUser) => {
            logger.info("Persona updated successfully", {
              userId: decoded.userId,
              persona,
            });
            res.status(200).json({
              message: "Persona updated successfully",
              user: updatedUser,
            });
          })
          .catch((error) => {
            logger.error("Update persona error:", { error });
            res.status(500).json({ error: "Failed to update persona" });
          });
      } catch (jwtError) {
        logger.error("JWT verification error:", { jwtError });
        res.status(401).json({ error: "Invalid token" });
      }
    } catch (error) {
      logger.error("Update persona error:", { error });
      res.status(500).json({ error: "Failed to update persona" });
    }
  }
}
