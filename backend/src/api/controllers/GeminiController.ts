/**
 * Gemini Controller
 * Handles requests to the Google Gemini API for chat and document-based queries
 */

import { Request, Response } from "express";
import { GoogleGenAI } from "@google/genai";
import { v4 as uuidv4 } from "uuid";
import multer from "multer";
import path from "path";
import { PrismaClient, DocumentFormat } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";
import {
  saveAIDocument,
  getAIDocumentById,
  removeAIDocument,
} from "../../infrastructure/ai/AIDocumentStorage";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { PromptService } from "../services/PromptService";
import { QuestionAnalysisService } from "../services/QuestionAnalysisService";

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize repositories
const contractExtractionRepository = new ContractExtractionRepository(prisma);

// Initialize Google AI client
const getGoogleAI = () => {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error("GEMINI_API_KEY is not defined in environment variables");
  }
  return new GoogleGenAI({ apiKey });
};

// Configure multer for memory storage (files will be stored in memory, not on disk)
const storage = multer.memoryStorage();

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = [
      ".pdf",
      ".docx",
      ".doc",
      ".txt",
      ".csv",
      ".xlsx",
      ".xls",
    ];

    if (allowedExtensions.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error("Only PDF, Word, Text, CSV, and Excel files are allowed"));
    }
  },
});

/**
 * Get document format from file extension
 */
function getDocumentFormat(filename: string): DocumentFormat {
  const ext = path.extname(filename).toLowerCase();

  switch (ext) {
    case ".pdf":
      return "PDF";
    case ".docx":
    case ".doc":
      return "DOCX";
    case ".txt":
      return "TXT";
    case ".xlsx":
    case ".xls":
      return "XLSX";
    default:
      return "OTHER";
  }
}

/**
 * Get MIME type from file extension
 */
function getMimeType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();

  switch (ext) {
    case ".pdf":
      return "application/pdf";
    case ".docx":
      return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    case ".doc":
      return "application/msword";
    case ".txt":
      return "text/plain";
    case ".csv":
      return "text/csv";
    case ".xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    case ".xls":
      return "application/vnd.ms-excel";
    default:
      return "application/octet-stream";
  }
}

/**
 * Upload documents for AI processing
 * @param req - Express request
 * @param res - Express response
 */
export async function uploadDocuments(
  req: Request,
  res: Response
): Promise<void> {
  const uploadMiddleware = upload.array("files", 5); // Allow up to 5 files

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      logger.error("Error uploading files:", { error: err });
      res.status(400).json({
        success: false,
        error: err.message,
      });
      return;
    }

    try {
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        res.status(400).json({
          success: false,
          error: "No files uploaded",
        });
        return;
      }

      // Get tenant ID and user ID from request or find valid ones
      let tenantId = req.user?.tenantId;
      let userId = req.user?.userId; // Changed from id to userId to match the user object structure

      // If no tenant ID is provided, find the first available tenant
      if (!tenantId) {
        logger.info("No tenant ID provided, looking for a valid tenant");
        const firstTenant = await prisma.tenant.findFirst();

        if (!firstTenant) {
          logger.error("No tenants found in the database");
          res.status(500).json({
            success: false,
            error: "No valid tenant found",
            message: "Cannot upload documents without a valid tenant",
          });
          return;
        }

        tenantId = firstTenant.id;
        logger.info(`Using tenant ID: ${tenantId}`);
      }

      // If no user ID is provided, find a valid user or create a system user
      if (!userId) {
        logger.info("No user ID provided, looking for a valid user");

        // First try to find an existing user for this tenant
        let tenantUser = await prisma.tenantUser.findFirst({
          where: { tenantId },
          include: { user: true },
        });

        if (tenantUser) {
          userId = tenantUser.userId;
          logger.info(`Using user ID from tenant: ${userId}`);
        } else {
          // If no user exists for this tenant, check for any user
          const user = await prisma.user.findFirst();
          if (user) {
            userId = user.id;
            logger.info(`Using existing user ID: ${userId}`);
          } else {
            // If no user exists at all, use undefined (system will handle it)
            userId = undefined;
            logger.info("No valid user found, using undefined for createdById");
          }
        }
      }

      // Store document information in database
      const documentIds = await Promise.all(
        files.map(async (file) => {
          try {
            // Create document record with a unique ID
            const documentId = uuidv4();
            const documentFormat = getDocumentFormat(file.originalname);

            // Since we're using memory storage, file is available in buffer
            logger.info(`Storing document in memory: ${file.originalname}`);

            // Create an AI document using our in-memory storage module
            logger.info(`Creating AI document with ID: ${documentId}`);

            // Prepare AI document data with the file content in memory
            const aiDocumentData = {
              id: documentId,
              title: file.originalname,
              documentFormat,
              documentUri: `memory://${documentId}`, // Virtual URI
              documentSize: file.size,
              content: file.buffer, // Store the actual file content in memory
            };

            // Save the AI document to in-memory storage
            saveAIDocument(aiDocumentData);

            logger.info(`Document successfully stored with ID: ${documentId}`);
            return documentId;
          } catch (error) {
            logger.error(`Error processing file ${file.originalname}:`, {
              error,
            });
            // Don't throw here, just return null and filter out failed documents later
            return null;
          }
        })
      );

      // Filter out any null document IDs (failed uploads)
      const successfulDocumentIds = documentIds.filter((id) => id !== null);

      if (successfulDocumentIds.length === 0) {
        res.status(500).json({
          success: false,
          error: "Failed to upload any documents",
          message:
            "All document uploads failed. Please check the logs for details.",
        });
        return;
      }

      res.status(200).json({
        success: true,
        documentIds: successfulDocumentIds,
        message: `${successfulDocumentIds.length} of ${files.length} document(s) uploaded successfully`,
      });
    } catch (error: any) {
      logger.error("Error processing uploaded documents:", { error });
      res.status(500).json({
        success: false,
        error: "Failed to process uploaded documents",
        message: error.message,
      });
    }
  });
}

/**
 * Process a chat message using Google's Gemini API
 * @param req - Express request
 * @param res - Express response
 */
export async function processGeminiChat(
  req: Request,
  res: Response
): Promise<void> {
  try {
    // Get message, document IDs, tagged contracts, conversation history, and context from request body
    const {
      message,
      documentIds,
      taggedContracts,
      conversationHistory,
      context,
    } = req.body;

    if (!message && (!documentIds || documentIds.length === 0)) {
      res.status(400).json({
        success: false,
        error: "Either message or documentIds is required",
      });
      return;
    }

    // Use conversation history if provided, otherwise start fresh
    const validConversationHistory: any[] = conversationHistory || [];

    // Prepare request payload with conversation history
    let contents = [];

    // Build page-specific system prompt using PromptService
    const systemPrompt = PromptService.buildSystemPrompt(context);

    // Add system prompt as the first message to establish customer-focused persona
    contents.push({
      role: "user",
      parts: [
        {
          text: systemPrompt,
        },
      ],
    });

    contents.push({
      role: "model",
      parts: [
        {
          text: "I understand. I'm here to help you navigate your contracts and make informed decisions. I'll explain things clearly and focus on what matters most for your business. How can I assist you today?",
        },
      ],
    });

    // Add conversation history if available
    if (validConversationHistory.length > 0) {
      // Add previous messages to maintain context
      validConversationHistory.forEach((msg) => {
        contents.push({
          role: msg.role === "user" ? "user" : "model",
          parts: [{ text: msg.content }],
        });
      });
    }

    // Add the current user message
    contents.push({
      role: "user",
      parts: [{ text: message || "Please analyze these documents" }],
    });

    let payload: any = {
      contents: contents,
    };

    // If document IDs are provided, fetch document content and add to payload
    if (documentIds && documentIds.length > 0) {
      try {
        logger.info(
          `Processing ${documentIds.length} documents: ${JSON.stringify(
            documentIds
          )}`
        );

        // Check if we have too many documents
        if (documentIds.length > 5) {
          logger.warn(`Too many documents requested: ${documentIds.length}`);
          res.status(400).json({
            success: false,
            error: "Too many documents",
            message:
              "You can only process up to 5 documents at once. Please reduce the number of documents and try again.",
          });
          return;
        }

        // Fetch documents from database
        const documents = await Promise.all(
          documentIds.map(async (docId: string) => {
            logger.info(`Processing document with ID: ${docId}`);

            // Validate document ID format
            if (!docId || typeof docId !== "string" || docId.length < 10) {
              throw new Error(`Invalid document ID format: ${docId}`);
            }

            // Get the AI document from our in-memory storage
            const aiDocument = getAIDocumentById(docId);

            if (!aiDocument) {
              logger.error(
                `Document with ID ${docId} not found in memory storage`
              );
              throw new Error(`Document with ID ${docId} not found`);
            }

            // Get file name and content directly from memory
            const fileName = aiDocument.title;

            // Log document info for debugging
            logger.info(
              `Processing in-memory document: ${fileName}, size: ${aiDocument.documentSize} bytes`
            );

            try {
              // Get the file content directly from memory
              const fileContent = aiDocument.content;

              if (!fileContent) {
                throw new Error(
                  `Document content not available for ${fileName}`
                );
              }

              // Check if file is too large (Gemini has a limit per file)
              if (fileContent.length > 4 * 1024 * 1024) {
                // 4MB per file limit
                logger.warn(`File is too large: ${fileContent.length} bytes`);
                throw new Error(
                  `File ${fileName} is too large (${fileContent.length} bytes). Maximum size per file is 4MB.`
                );
              }

              // Convert to base64
              const base64Content = fileContent.toString("base64");

              return {
                inlineData: {
                  data: base64Content,
                  mimeType: getMimeType(fileName),
                },
              };
            } catch (error: any) {
              logger.error(`Error reading file ${fileName}:`, { error });
              throw new Error(
                `Error reading file ${fileName}: ${error.message || "Unknown error"
                }`
              );
            }
          })
        );

        // Add documents to the last message (current user message)
        const lastIndex = payload.contents.length - 1;
        if (lastIndex >= 0) {
          // Replace the parts array with text and documents
          payload.contents[lastIndex].parts = [
            { text: message || "Please analyze these documents" },
            ...documents,
          ];
        }
      } catch (error: any) {
        logger.error("Error processing documents for Gemini API:", {
          error,
          message: error.message,
          stack: error.stack,
        });

        // Provide more specific error messages based on the error type
        let errorMessage = error.message || "Unknown error";
        let statusCode = 500;

        if (errorMessage.includes("not found")) {
          statusCode = 404;
          errorMessage = `Document not found. Please check if the document IDs are correct: ${documentIds.join(
            ", "
          )}`;
        } else if (errorMessage.includes("too large")) {
          statusCode = 413;
          errorMessage =
            "One or more documents are too large. Please try with smaller documents.";
        } else if (errorMessage.includes("Invalid document ID")) {
          statusCode = 400;
          errorMessage = "Invalid document ID format provided.";
        }

        res.status(statusCode).json({
          success: false,
          error: "Failed to process documents",
          message: errorMessage,
          details: error.message,
        });
        return;
      }
    }

    // If tagged contracts are provided, add contract context to the message
    if (taggedContracts && taggedContracts.length > 0) {
      logger.info(`Processing tagged contracts`);
      logger.info(`Tagged contract IDs: ${JSON.stringify(taggedContracts)}`);

      try {
        // Fetch contract details for each tagged contract directly from database
        const contractContexts = await Promise.all(
          taggedContracts.map(async (contractId: string) => {
            try {
              logger.info(`Fetching contract details for ID: ${contractId}`);

              // Fetch contract directly from database with folder information
              const contract = await prisma.contract.findUnique({
                where: {
                  id: contractId,
                  tenantId: req.user!.tenantId, // Ensure user can only access their contracts
                },
                include: {
                  folder: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              });

              if (contract) {
                logger.info(`Contract found in database: ${contract.title}`);

                // Try to get metadata from database
                let metadata = null;
                try {
                  const contractMetadata =
                    await prisma.contractMetadata.findUnique({
                      where: { contractId: contractId },
                    });

                  if (contractMetadata) {
                    metadata = {
                      totalValue: contractMetadata.totalValue,
                      currency: contractMetadata.currency,
                      paymentTerms: contractMetadata.paymentTerms,
                      autoExtractedFields: contractMetadata.autoExtractedFields,
                    };
                    logger.info(`Metadata found in database for ${contractId}`);
                  }
                } catch (metadataError) {
                  logger.warn(
                    `No metadata found for ${contractId}:`,
                    (metadataError as any).message
                  );
                }

                // Return contract data with metadata and folder information
                return {
                  id: contract.id,
                  title: contract.title,
                  contractNumber: contract.contractNumber,
                  counterparty: contract.counterparty,
                  value: contract.value,
                  status: contract.status,
                  startDate: contract.startDate,
                  endDate: contract.endDate,
                  metadata: metadata,
                  folder: contract.folder,
                  folderId: contract.folderId,
                };
              } else {
                logger.warn(`Contract not found in database: ${contractId}`);
                return null;
              }
            } catch (error) {
              logger.error(`Error fetching contract ${contractId}:`, error);
              return null;
            }
          })
        );

        // Filter out null contracts and add context to the message
        const validContracts = contractContexts.filter(
          (contract) => contract !== null
        );

        logger.info(
          `Found ${validContracts.length} valid contracts for context`
        );

        if (validContracts.length > 0) {
          // Get folder context for related contracts
          const folderIds = validContracts
            .filter((c) => c.folderId)
            .map((c) => c.folderId!)
            .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

          let folderContracts: any[] = [];
          if (folderIds.length > 0) {
            try {
              folderContracts = await prisma.contract.findMany({
                where: {
                  folderId: { in: folderIds },
                  tenantId: req.user!.tenantId,
                  id: { notIn: validContracts.map((c) => c.id) }, // Exclude already tagged contracts
                },
                select: {
                  id: true,
                  title: true,
                  agreementType: true,
                  counterparty: true,
                  value: true,
                  status: true,
                  folderId: true,
                  folder: {
                    select: {
                      name: true,
                    },
                  },
                },
              });
            } catch (error) {
              logger.error("Error fetching folder contracts:", error);
            }
          }

          // Enhanced contract context with comprehensive extraction data
          const contractContext = await Promise.all(
            validContracts.map(async (contract) => {
              let contextText = `Contract: ${contract.title} (ID: ${contract.id
                })
- Contract Number: ${contract.contractNumber || "N/A"}
- Counterparty/Client: ${contract.counterparty || "N/A"}
- Contract Value: ${contract.value ? `$${contract.value.toLocaleString()}` : "N/A"
                }
- Status: ${contract.status}
- Start Date: ${contract.startDate
                  ? new Date(contract.startDate).toLocaleDateString()
                  : "N/A"
                }
- End Date: ${contract.endDate
                  ? new Date(contract.endDate).toLocaleDateString()
                  : "N/A"
                }`;

              // Add folder information
              if (contract.folder) {
                contextText += `
- Folder: ${contract.folder.name}`;
              }

              // Get comprehensive extraction data
              try {
                const extraction =
                  await contractExtractionRepository.getByContractId(
                    contract.id,
                    req.user!.tenantId
                  );

                if (extraction) {
                  contextText += `

EXTRACTED CONTRACT DATA:`;

                  // Add OCR text if available
                  try {
                    // Get the latest contract version to access OCR text
                    const latestVersion = await prisma.contractVersion.findFirst({
                      where: {
                        contractId: contract.id,
                      },
                      orderBy: {
                        versionNumber: "desc",
                      },
                      select: {
                        ocrText: true,
                        ocrStatus: true,
                        ocrConfidence: true,
                      },
                    });

                    if (latestVersion?.ocrText && latestVersion.ocrStatus === "SUCCESS") {
                      contextText += `

FULL CONTRACT TEXT (OCR):
${latestVersion.ocrText}

---

EXTRACTED FIELDS FROM ABOVE TEXT:`;
                    } else {
                      // OCR text not available, just show extracted fields
                      contextText += `

NOTE: Full contract text not available (OCR text missing or failed)

EXTRACTED FIELDS:`;
                    }
                  } catch (ocrError) {
                    logger.warn(
                      `Error fetching OCR text for contract ${contract.id}:`,
                      ocrError
                    );
                    contextText += `

NOTE: Could not retrieve full contract text

EXTRACTED FIELDS:`;
                  }

                  // Add fixed fields
                  if (extraction.fixedFields) {
                    contextText += `

Fixed Fields:`;
                    Object.entries(extraction.fixedFields).forEach(
                      ([key, field]) => {
                        if (
                          field &&
                          typeof field === "object" &&
                          "value" in field
                        ) {
                          contextText += `
- ${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}: ${field.value || "N/A"
                            }`;
                        }
                      }
                    );
                  }

                  // Add dynamic fields
                  if (
                    extraction.dynamicFields &&
                    Object.keys(extraction.dynamicFields).length > 0
                  ) {
                    contextText += `

Dynamic Fields:`;
                    Object.entries(extraction.dynamicFields).forEach(
                      ([key, field]) => {
                        if (
                          field &&
                          typeof field === "object" &&
                          "value" in field
                        ) {
                          contextText += `
- ${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}: ${field.value || "N/A"
                            }`;
                          if (field.description) {
                            contextText += ` (${field.description})`;
                          }
                        }
                      }
                    );
                  }

                  // Add special fields
                  if (
                    extraction.specialFields &&
                    Object.keys(extraction.specialFields).length > 0
                  ) {
                    contextText += `

Special Fields:`;
                    Object.entries(extraction.specialFields).forEach(
                      ([key, field]) => {
                        if (
                          field &&
                          typeof field === "object" &&
                          "value" in field
                        ) {
                          contextText += `
- ${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}: ${field.value || "N/A"
                            }`;
                        }
                      }
                    );
                  }

                  // Add analysis fields (Oracle entitlement analysis)
                  if (
                    extraction.analysisFields &&
                    Object.keys(extraction.analysisFields).length > 0
                  ) {
                    contextText += `

Analysis Fields:`;
                    Object.entries(extraction.analysisFields).forEach(
                      ([key, value]) => {
                        if (key === "purchasing" && Array.isArray(value)) {
                          contextText += `
- Purchasing Items:`;
                          value.forEach((item, index) => {
                            contextText += `
  Item ${index + 1}:`;
                            if (item.license_type)
                              contextText += `
    - License Type: ${item.license_type}`;
                            if (item.license_value)
                              contextText += `
    - License Value: ${item.license_value}`;
                            if (item.quantity)
                              contextText += `
    - Quantity: ${item.quantity}`;
                            if (item.price)
                              contextText += `
    - Price: ${item.price}`;
                          });
                        } else if (
                          value &&
                          typeof value === "object" &&
                          "value" in value
                        ) {
                          contextText += `
- ${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}: ${value.value || "N/A"
                            }`;
                        } else if (
                          key !== "extractionDate" &&
                          key !== "processingTimeMs"
                        ) {
                          contextText += `
- ${key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}: ${value || "N/A"
                            }`;
                        }
                      }
                    );
                  }

                  // Add document summary
                  if (extraction.documentSummary) {
                    contextText += `

Document Summary:
${extraction.documentSummary.value || "N/A"}`;
                  }
                } else {
                  contextText += `
- Note: No detailed extraction data available for this contract`;
                }
              } catch (extractionError) {
                logger.warn(
                  `Error fetching extraction data for contract ${contract.id}:`,
                  extractionError
                );
                contextText += `
- Note: Could not retrieve detailed extraction data`;
              }

              // Legacy metadata fallback
              if (contract.metadata) {
                contextText += `

Legacy Metadata:
- Effective Date: ${contract.metadata.effectiveDate
                    ? new Date(
                      contract.metadata.effectiveDate
                    ).toLocaleDateString()
                    : "N/A"
                  }
- Total Value: ${contract.metadata.totalValue
                    ? `$${contract.metadata.totalValue.toLocaleString()}`
                    : "N/A"
                  }
- Currency: ${contract.metadata.currency || "N/A"}
- Payment Terms: ${contract.metadata.paymentTerms || "N/A"}`;

                if (contract.metadata.autoExtractedFields) {
                  contextText += `
- Additional Details: ${JSON.stringify(
                    contract.metadata.autoExtractedFields,
                    null,
                    2
                  )}`;
                }
              }

              return contextText;
            })
          );

          const finalContractContext = (
            await Promise.all(contractContext)
          ).join("\n\n");

          logger.info(
            `Enhanced contract context prepared with extraction data`
          );

          // Add folder context if there are related contracts
          let folderContext = "";
          if (folderContracts.length > 0) {
            const folderGroups = folderContracts.reduce((groups, contract) => {
              const folderName = contract.folder?.name || "Unknown";
              if (!groups[folderName]) {
                groups[folderName] = [];
              }
              groups[folderName].push(contract);
              return groups;
            }, {} as Record<string, any[]>);

            folderContext = Object.entries(folderGroups)
              .map(([folderName, contracts]) => {
                const contractList = (contracts as any[])
                  .map(
                    (c: any) =>
                      `  - ${c.title} (${c.agreementType || "Unknown"}, ${c.status
                      })`
                  )
                  .join("\n");
                return `\nRelated contracts in folder "${folderName}":\n${contractList}`;
              })
              .join("\n");

            if (folderContext) {
              folderContext = `\n\nADDITIONAL CONTEXT - Related contracts in the same folders:${folderContext}`;
            }
          }

          // Enhance the message with contract context
          const enhancedMessage = `${message}

CONTEXT: The user is asking about the following contract(s):

${finalContractContext}${folderContext}

Please provide a response based on the comprehensive contract information above and the user's question.`;

          // Update the last message in the payload
          const lastIndex = payload.contents.length - 1;
          if (lastIndex >= 0) {
            payload.contents[lastIndex].parts[0].text = enhancedMessage;
          }
        }
      } catch (error) {
        logger.error("Error processing tagged contracts:", error);
        // Continue without contract context if there's an error
      }
    }

    // Call Gemini API using @google/genai SDK
    const googleAI = getGoogleAI();
    const response = await googleAI.models.generateContent({
      model: "gemini-2.0-flash",
      config: {
        temperature: 0.7,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 4096,
      },
      contents: payload.contents,
    });

    // Extract response text
    let responseText = "Sorry, I couldn't generate a response.";

    if (response && response.text) {
      responseText = response.text;
    }

    // Clean up temporary documents after processing
    if (documentIds && documentIds.length > 0) {
      documentIds.forEach((docId: string) => {
        removeAIDocument(docId);
        logger.info(`Removed temporary document: ${docId}`);
      });
    }

    // Generate contextual follow-up suggestions if we have a user message
    let followUpSuggestions: any[] = [];
    if (message && message.trim()) {
      try {
        // Try question analysis first, but don't fail if it doesn't work
        let questionAnalysis = null;
        try {
          questionAnalysis = await QuestionAnalysisService.analyzeQuestion(
            message.trim()
          );
          logger.info("Question analysis successful", {
            analysis: questionAnalysis,
          });
        } catch (error) {
          logger.warn("Question analysis failed, proceeding without it", {
            error: error instanceof Error ? error.message : String(error),
          });
        }

        // Generate contextual follow-ups (works with or without question analysis)
        followUpSuggestions =
          await QuestionAnalysisService.generateContextualFollowUps(
            message.trim(),
            responseText,
            questionAnalysis, // Will be null if analysis failed, which is fine
            context || "",
            taggedContracts ? { hasContracts: true } : undefined
          );

        logger.info("Generated follow-up suggestions", {
          count: followUpSuggestions.length,
          userQuestion: message.trim().substring(0, 100),
        });
      } catch (error) {
        logger.error("Error generating follow-up suggestions", { error });
        // Continue without follow-ups if generation fails
      }
    }

    // Return response with follow-up suggestions
    res.status(200).json({
      success: true,
      id: uuidv4(),
      message: {
        role: "assistant",
        content: responseText,
      },
      followUpSuggestions:
        followUpSuggestions.length > 0 ? followUpSuggestions : undefined,
    });
  } catch (error: any) {
    logger.error("Error calling Gemini API:", {
      message: error.message,
      stack: error.stack,
    });

    // Log more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      logger.error("Gemini API Error Response:", {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });

      // Handle specific Gemini API errors
      if (error.response.status === 400) {
        res.status(400).json({
          success: false,
          error: "Invalid request to Gemini API",
          message:
            "The request to the Gemini API was invalid. This might be due to document format issues or content restrictions.",
          details: error.response.data,
        });
        return;
      } else if (
        error.response.status === 413 ||
        error.response.status === 429
      ) {
        res.status(413).json({
          success: false,
          error: "Request too large or rate limited",
          message:
            "The request was too large or you've exceeded the rate limit. Try with smaller or fewer documents.",
          details: error.response.data,
        });
        return;
      }
    } else if (error.request) {
      // The request was made but no response was received
      logger.error("Gemini API No Response:", { request: error.request });

      // Handle timeout errors
      if (error.code === "ECONNABORTED") {
        res.status(504).json({
          success: false,
          error: "Request timeout",
          message:
            "The request to the Gemini API timed out. This might be due to large documents or server load.",
          details: "Connection aborted due to timeout",
        });
        return;
      }
    }

    // Provide a user-friendly error message
    let errorMessage = "An error occurred while processing your request.";
    let statusCode = 500;

    if (
      error.message.includes("network") ||
      error.message.includes("connect")
    ) {
      errorMessage =
        "Network error when connecting to the AI service. Please try again later.";
    } else if (error.message.includes("timeout")) {
      statusCode = 504;
      errorMessage =
        "The request timed out. This might be due to large documents or server load.";
    } else if (error.message.includes("too large")) {
      statusCode = 413;
      errorMessage =
        "The documents are too large to process. Please try with smaller documents.";
    }

    // Return error response
    res.status(statusCode).json({
      success: false,
      error: "Failed to process chat",
      message: errorMessage,
      details:
        error.response?.data ||
        error.message ||
        "No additional error details available",
    });
  }
}

/**
 * Analyze the current route to determine contract management lifecycle stage
 */
function analyzeLifecycleStage(context: string) {
  const path = context?.toLowerCase() || "";

  // Discovery & Analysis Stage
  if (path.includes("/contract-concierge/discovery")) {
    return {
      stage: "Contract Discovery & Analysis",
      category: "discovery",
      phase: "initial",
    };
  }

  if (path.includes("/contract-concierge/benchmark")) {
    return {
      stage: "Contract Benchmarking",
      category: "analysis",
      phase: "evaluation",
    };
  }

  // Contract Management Stages
  if (
    path.includes("/contract-management/contracts") &&
    !path.includes("/view/")
  ) {
    return {
      stage: "Contract Portfolio Management",
      category: "management",
      phase: "organization",
    };
  }

  if (path.includes("/contract-management/contracts/view/")) {
    return {
      stage: "Contract Review & Analysis",
      category: "review",
      phase: "detailed_analysis",
    };
  }

  if (path.includes("/contract-management/renewals")) {
    return {
      stage: "Contract Renewals Management",
      category: "renewals",
      phase: "lifecycle_management",
    };
  }

  if (path.includes("/contract-management/compliance")) {
    return {
      stage: "Compliance Monitoring",
      category: "compliance",
      phase: "risk_management",
    };
  }

  if (path.includes("/contract-management/obligations")) {
    return {
      stage: "Obligation Tracking",
      category: "obligations",
      phase: "performance_monitoring",
    };
  }

  if (path.includes("/contract-management/financials")) {
    return {
      stage: "Financial Management",
      category: "financials",
      phase: "cost_analysis",
    };
  }

  // Cockpit & Analytics
  if (path.includes("/cockpit")) {
    return {
      stage: "Strategic Analytics",
      category: "analytics",
      phase: "insights",
    };
  }

  // Administration
  if (path.includes("/administration")) {
    return {
      stage: "System Administration",
      category: "admin",
      phase: "configuration",
    };
  }

  // Default fallback
  return {
    stage: "General Contract Management",
    category: "general",
    phase: "exploration",
  };
}

/**
 * Get detailed context for each lifecycle stage
 */
function getLifecycleContext(lifecycleStage: {
  stage: string;
  category: string;
  phase: string;
}) {
  const contexts = {
    "Contract Discovery & Analysis": {
      description:
        "Initial contract upload, document processing, and AI-powered analysis",
      activities: [
        "Document upload",
        "AI extraction",
        "Metadata review",
        "Initial risk assessment",
      ],
      userNeeds: [
        "Understanding contract content",
        "Identifying key terms",
        "Risk evaluation",
      ],
      exampleSuggestions: [
        "What are the key obligations in this contract?",
        "Are there any unusual clauses I should review?",
        "What risks should I be aware of?",
        "How does this compare to standard agreements?",
      ],
    },
    "Contract Benchmarking": {
      description:
        "Comparing contracts against industry standards and internal benchmarks",
      activities: [
        "Performance comparison",
        "Market analysis",
        "Standard deviation review",
      ],
      userNeeds: [
        "Competitive analysis",
        "Performance metrics",
        "Improvement opportunities",
      ],
      exampleSuggestions: [
        "How does this contract perform vs industry standards?",
        "What terms are above/below market rates?",
        "Which clauses need renegotiation?",
        "What are the cost optimization opportunities?",
      ],
    },
    "Contract Portfolio Management": {
      description:
        "Managing multiple contracts, organization, and bulk operations",
      activities: [
        "Portfolio overview",
        "Status management",
        "Bulk operations",
        "Organization",
      ],
      userNeeds: [
        "Portfolio visibility",
        "Efficient organization",
        "Status tracking",
      ],
      exampleSuggestions: [
        "Which contracts need immediate attention?",
        "How can I organize contracts by priority?",
        "What contracts are expiring soon?",
        "Which agreements have compliance issues?",
      ],
    },
    "Contract Review & Analysis": {
      description:
        "Detailed analysis of individual contract terms and conditions",
      activities: [
        "Term analysis",
        "Risk assessment",
        "Obligation review",
        "Amendment tracking",
      ],
      userNeeds: [
        "Deep contract understanding",
        "Risk identification",
        "Action planning",
      ],
      exampleSuggestions: [
        "What are my key obligations under this contract?",
        "What penalties apply for non-compliance?",
        "When do I need to provide renewal notice?",
        "What are the termination conditions?",
      ],
    },
    "Contract Renewals Management": {
      description:
        "Managing contract renewal timelines, negotiations, and decisions",
      activities: [
        "Renewal tracking",
        "Notice management",
        "Renegotiation planning",
        "Decision support",
      ],
      userNeeds: [
        "Renewal timeline management",
        "Negotiation preparation",
        "Decision support",
      ],
      exampleSuggestions: [
        "Which contracts need renewal notices this month?",
        "What terms should I renegotiate?",
        "How much notice is required for renewal?",
        "What are the auto-renewal conditions?",
      ],
    },
    "Compliance Monitoring": {
      description:
        "Ensuring contract compliance and managing regulatory requirements",
      activities: [
        "Compliance tracking",
        "Audit preparation",
        "Risk monitoring",
        "Violation management",
      ],
      userNeeds: ["Compliance assurance", "Risk mitigation", "Audit readiness"],
      exampleSuggestions: [
        "Which contracts have compliance violations?",
        "What audits are due this quarter?",
        "Are there any regulatory changes affecting contracts?",
        "What compliance reports do I need?",
      ],
    },
    "Obligation Tracking": {
      description:
        "Monitoring and managing contractual obligations and deliverables",
      activities: [
        "Obligation monitoring",
        "Deadline tracking",
        "Performance measurement",
        "Escalation management",
      ],
      userNeeds: [
        "Obligation visibility",
        "Performance tracking",
        "Proactive management",
      ],
      exampleSuggestions: [
        "What obligations are due this week?",
        "Which deliverables are overdue?",
        "How is performance against SLAs?",
        "What escalations need attention?",
      ],
    },
    "Financial Management": {
      description:
        "Managing contract financials, payments, and cost optimization",
      activities: [
        "Cost tracking",
        "Payment monitoring",
        "Budget analysis",
        "Financial reporting",
      ],
      userNeeds: [
        "Cost visibility",
        "Payment tracking",
        "Financial optimization",
      ],
      exampleSuggestions: [
        "What are my total contract costs this quarter?",
        "Which payments are overdue?",
        "How can I optimize contract spending?",
        "What are the budget variances?",
      ],
    },
    "Strategic Analytics": {
      description: "High-level insights and strategic decision support",
      activities: [
        "Performance analytics",
        "Trend analysis",
        "Strategic insights",
        "Executive reporting",
      ],
      userNeeds: [
        "Strategic insights",
        "Performance trends",
        "Decision support",
      ],
      exampleSuggestions: [
        "What are the key performance trends?",
        "Which contracts drive the most value?",
        "What strategic risks need attention?",
        "How is portfolio performance trending?",
      ],
    },
    "System Administration": {
      description:
        "System configuration, user management, and administrative tasks",
      activities: [
        "User management",
        "System configuration",
        "Integration setup",
        "Audit management",
      ],
      userNeeds: [
        "System optimization",
        "User access control",
        "Configuration management",
      ],
      exampleSuggestions: [
        "How can I optimize user permissions?",
        "What integrations need configuration?",
        "Which users need access updates?",
        "What system audits are required?",
      ],
    },
    "General Contract Management": {
      description: "General contract management assistance and guidance",
      activities: [
        "General guidance",
        "Best practices",
        "Process improvement",
        "Training support",
      ],
      userNeeds: ["General assistance", "Best practices", "Process guidance"],
      exampleSuggestions: [
        "How can you help me with contract management?",
        "What are contract management best practices?",
        "How should I organize my contract workflow?",
        "What should I focus on first?",
      ],
    },
  };

  return (
    contexts[lifecycleStage.stage as keyof typeof contexts] ||
    contexts["General Contract Management"]
  );
}

/**
 * Generate contextual chat suggestions using Gemini AI
 */
export async function generateSuggestions(req: Request, res: Response) {
  try {
    const { context, contractId, conversationHistory = [] } = req.body;

    logger.info("Generating AI-powered chat suggestions", {
      context,
      contractId,
      historyLength: conversationHistory.length,
    });

    // Build context for AI suggestion generation
    let contractContext = "";

    // Get contract context if contractId is provided
    if (contractId) {
      try {
        const contract = await prisma.contract.findUnique({
          where: { id: contractId },
          select: {
            title: true,
            counterparty: true,
            agreementType: true,
            status: true,
            value: true,
            startDate: true,
            endDate: true,
          },
        });

        if (contract) {
          contractContext = `
Current Contract Context:
- Title: ${contract.title}
- Counterparty: ${contract.counterparty || "Not specified"}
- Agreement Type: ${contract.agreementType || "Not specified"}
- Status: ${contract.status || "Not specified"}
- Value: ${contract.value || "Not specified"}
- Start Date: ${contract.startDate || "Not specified"}
- End Date: ${contract.endDate || "Not specified"}
`;
        }
      } catch (error) {
        logger.warn("Could not fetch contract details for suggestions", {
          contractId,
          error,
        });
      }
    }

    // Build conversation context
    let conversationContext = "";
    if (conversationHistory.length > 0) {
      // Get the last 3-4 messages for context (to avoid token limits)
      const recentMessages = conversationHistory.slice(-4);
      conversationContext = `
Recent Conversation:
${recentMessages
          .map(
            (msg: any, index: number) =>
              `${index + 1}. ${msg.role === "user" ? "User" : "Assistant"}: ${msg.content
              }`
          )
          .join("\n")}
`;
    }

    // Analyze the current route to determine lifecycle stage
    const lifecycleStage = analyzeLifecycleStage(context);
    const lifecycleContext = getLifecycleContext(lifecycleStage);

    // Create the AI prompt for generating suggestions
    const suggestionPrompt = `You are a helpful contract management assistant serving customers who use this platform. Based on the customer's current workflow and context, generate 3-5 relevant, actionable suggestions that would help them with their contract management goals.

${contractContext}
${conversationContext}

Current Workflow Stage: ${lifecycleStage.stage}
Page Context: ${context || "General chat"}
Workflow Description: ${lifecycleContext.description}
Key Activities: ${lifecycleContext.activities.join(", ")}

Guidelines:
1. Generate actionable suggestions specific to the customer's current workflow stage
2. Focus on tasks and questions relevant to ${lifecycleStage.stage}
3. Be specific to the actual contract content when available
4. Make suggestions that help customers accomplish their business goals
5. Consider the typical customer needs at this stage: ${lifecycleContext.userNeeds.join(
      ", "
    )}
6. Keep suggestions concise but actionable (under 70 characters each)
7. Use customer-friendly language that's easy to understand
8. Frame suggestions around customer benefits and outcomes

Examples for ${lifecycleStage.stage}:
${lifecycleContext.exampleSuggestions.map((s: string) => `- "${s}"`).join("\n")}

Return your response as a JSON array of suggestion objects with this exact format:
[
  {
    "id": "unique-id-1",
    "text": "Your suggestion text here",
    "category": "${lifecycleStage.category}"
  },
  {
    "id": "unique-id-2",
    "text": "Another suggestion",
    "category": "${lifecycleStage.category}"
  }
]

Only return the JSON array, no other text.`;

    // Call Gemini API using @google/genai SDK
    const googleAI = getGoogleAI();
    const response = await googleAI.models.generateContent({
      model: "gemini-2.5-pro",
      config: {
        temperature: 0.7,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 1024,
      },
      contents: {
        role: "user",
        parts: [
          {
            text: suggestionPrompt,
          },
        ],
      },
    });

    // Extract response text
    let responseText = "";
    if (response && response.text) {
      responseText = response.text;
    } else {
      throw new Error("Invalid response structure from Gemini API");
    }

    // Parse the AI response
    let suggestions = [];
    try {
      // Clean the response text to extract JSON
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        suggestions = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error("No valid JSON found in AI response");
      }
    } catch (parseError) {
      logger.warn("Failed to parse AI suggestions, falling back to default", {
        responseText,
        parseError,
      });

      // Fallback suggestions based on context
      if (contractId) {
        suggestions = [
          {
            id: "contract-obligations",
            text: "What are my key obligations in this contract?",
            category: "followup",
          },
          {
            id: "contract-risks",
            text: "What should I be concerned about?",
            category: "followup",
          },
          {
            id: "contract-terms",
            text: "Help me understand the important terms",
            category: "followup",
          },
        ];
      } else {
        suggestions = [
          {
            id: "general-help",
            text: "What can you help me with?",
            category: "general",
          },
          {
            id: "contract-analysis",
            text: "What should I look for in my contracts?",
            category: "general",
          },
          {
            id: "contract-questions",
            text: "How do I better understand my agreements?",
            category: "general",
          },
        ];
      }
    }

    // Validate and clean suggestions
    const validSuggestions = suggestions
      .filter(
        (suggestion: any) =>
          suggestion &&
          typeof suggestion.text === "string" &&
          suggestion.text.trim().length > 0 &&
          suggestion.text.length <= 100
      )
      .slice(0, 5) // Limit to 5 suggestions max
      .map((suggestion: any, index: number) => ({
        id: suggestion.id || `ai-suggestion-${index + 1}`,
        text: suggestion.text.trim(),
        category: suggestion.category || "followup",
        priority: index + 1,
      }));

    logger.info("Generated AI suggestions", {
      count: validSuggestions.length,
      suggestions: validSuggestions.map((s: any) => s.text),
    });

    res.status(200).json({
      success: true,
      suggestions: validSuggestions,
    });
  } catch (error: any) {
    logger.error("Error generating AI suggestions:", {
      message: error.message,
      stack: error.stack,
    });

    // Fallback to basic suggestions on error
    const fallbackSuggestions = [
      {
        id: "fallback-1",
        text: "Help me understand my contract better",
        category: "general",
        priority: 1,
      },
      {
        id: "fallback-2",
        text: "What are the most important terms?",
        category: "general",
        priority: 2,
      },
    ];

    res.status(200).json({
      success: true,
      suggestions: fallbackSuggestions,
      fallback: true,
    });
  }
}
