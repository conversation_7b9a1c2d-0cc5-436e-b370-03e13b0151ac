/**
 * Notification Controller
 * Handles HTTP requests for notification management
 */

import { Request, Response } from 'express';
import { NotificationService } from '../services/NotificationService';
import { NotificationType, NotificationPriority, NotificationChannel } from '@prisma/client';
import { logger } from '../../infrastructure/logging/logger';

/**
 * Controller for notification management endpoints
 */
export class NotificationController {
  private notificationService: NotificationService;

  constructor(notificationService: NotificationService) {
    this.notificationService = notificationService;
  }

  /**
   * Gets notifications for a user
   * @route GET /api/notifications/user/:userId
   */
  async getUserNotifications(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const page = req.query.page ? Number(req.query.page) : 1;
      const limit = req.query.limit ? Number(req.query.limit) : 20;

      const result = await this.notificationService.getUserNotifications(userId, page, limit);

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error in getUserNotifications controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets unread notifications for a user
   * @route GET /api/notifications/user/:userId/unread
   */
  async getUnreadNotifications(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      const notifications = await this.notificationService.getUnreadNotifications(userId);

      res.status(200).json(notifications);
    } catch (error) {
      logger.error('Error in getUnreadNotifications controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets live notifications for a user (optimized for real-time updates)
   * @route GET /api/notifications/user/:userId/live
   */
  async getLiveNotifications(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const since = req.query.since ? new Date(req.query.since as string) : undefined;
      const limit = req.query.limit ? Number(req.query.limit) : 10;

      const result = await this.notificationService.getLiveNotifications(userId, since, limit);

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error in getLiveNotifications controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Marks a notification as read
   * @route POST /api/notifications/:id/read
   */
  async markAsRead(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const notification = await this.notificationService.markAsRead(id);

      res.status(200).json(notification);
    } catch (error) {
      logger.error('Error in markAsRead controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Marks all notifications for a user as read
   * @route POST /api/notifications/user/:userId/read-all
   */
  async markAllAsRead(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      const count = await this.notificationService.markAllAsRead(userId);

      res.status(200).json({ count });
    } catch (error) {
      logger.error('Error in markAllAsRead controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Deletes a notification
   * @route DELETE /api/notifications/:id
   */
  async deleteNotification(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deleted = await this.notificationService.deleteNotification(id);

      if (!deleted) {
        res.status(404).json({ error: `Notification with ID ${id} not found` });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error in deleteNotification controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets notification preferences for a user
   * @route GET /api/notifications/user/:userId/preferences
   */
  async getUserPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { tenantId } = req.query;

      // Validate required fields
      if (!tenantId) {
        res.status(400).json({ error: 'tenantId is required' });
        return;
      }

      const preferences = await this.notificationService.getUserPreferences(userId, tenantId as string);

      res.status(200).json(preferences);
    } catch (error) {
      logger.error('Error in getUserPreferences controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Updates notification preferences for a user
   * @route PUT /api/notifications/user/:userId/preferences
   */
  async updateUserPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { tenantId, preferences } = req.body;

      // Validate required fields
      if (!tenantId || !preferences || !Array.isArray(preferences)) {
        res.status(400).json({ error: 'tenantId and preferences array are required' });
        return;
      }

      // Validate preferences format
      for (const pref of preferences) {
        if (!pref.type || !pref.channel || pref.enabled === undefined) {
          res.status(400).json({
            error: 'Each preference must have type, channel, and enabled properties'
          });
          return;
        }
      }

      const updatedPreferences = await this.notificationService.updateUserPreferences(
        userId,
        tenantId,
        preferences.map(pref => ({
          type: pref.type as NotificationType,
          channel: pref.channel as NotificationChannel,
          enabled: pref.enabled
        }))
      );

      res.status(200).json(updatedPreferences);
    } catch (error) {
      logger.error('Error in updateUserPreferences controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a contract notification
   * @route POST /api/notifications/contract
   */
  async createContractNotification(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, userId, tenantId, contractId, priority, metadata } = req.body;

      // Validate required fields
      if (!title || !content || !userId || !tenantId || !contractId) {
        res.status(400).json({
          error: 'Missing required fields: title, content, userId, tenantId, and contractId are required'
        });
        return;
      }

      const notification = await this.notificationService.createContractNotification({
        title,
        content,
        userId,
        tenantId,
        contractId,
        priority: priority as NotificationPriority,
        metadata
      });

      res.status(201).json(notification);
    } catch (error) {
      logger.error('Error in createContractNotification controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a license notification
   * @route POST /api/notifications/license
   */
  async createLicenseNotification(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, userId, tenantId, licenseId, priority, metadata } = req.body;

      // Validate required fields
      if (!title || !content || !userId || !tenantId || !licenseId) {
        res.status(400).json({
          error: 'Missing required fields: title, content, userId, tenantId, and licenseId are required'
        });
        return;
      }

      const notification = await this.notificationService.createLicenseNotification({
        title,
        content,
        userId,
        tenantId,
        licenseId,
        priority: priority as NotificationPriority,
        metadata
      });

      res.status(201).json(notification);
    } catch (error) {
      logger.error('Error in createLicenseNotification controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a system notification
   * @route POST /api/notifications/system
   */
  async createSystemNotification(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, userId, tenantId, priority, metadata } = req.body;

      // Validate required fields
      if (!title || !content || !userId || !tenantId) {
        res.status(400).json({
          error: 'Missing required fields: title, content, userId, and tenantId are required'
        });
        return;
      }

      const notification = await this.notificationService.createSystemNotification({
        title,
        content,
        userId,
        tenantId,
        priority: priority as NotificationPriority,
        metadata
      });

      res.status(201).json(notification);
    } catch (error) {
      logger.error('Error in createSystemNotification controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates a security notification
   * @route POST /api/notifications/security
   */
  async createSecurityNotification(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, userId, tenantId, priority, metadata } = req.body;

      // Validate required fields
      if (!title || !content || !userId || !tenantId) {
        res.status(400).json({
          error: 'Missing required fields: title, content, userId, and tenantId are required'
        });
        return;
      }

      const notification = await this.notificationService.createSecurityNotification({
        title,
        content,
        userId,
        tenantId,
        priority: priority as NotificationPriority,
        metadata
      });

      res.status(201).json(notification);
    } catch (error) {
      logger.error('Error in createSecurityNotification controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Manually triggers notification cleanup
   * @route POST /api/notifications/cleanup
   */
  async cleanupNotifications(req: Request, res: Response): Promise<void> {
    try {
      const deletedCount = await this.notificationService.cleanupOldNotifications();

      res.status(200).json({
        message: 'Cleanup completed successfully',
        deletedCount
      });
    } catch (error) {
      logger.error('Error in cleanupNotifications controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Creates default notification preferences for a user
   * @route POST /api/notifications/user/:userId/preferences/default
   */
  async createDefaultPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { tenantId } = req.body;

      if (!tenantId) {
        res.status(400).json({ error: 'tenantId is required' });
        return;
      }

      const preferences = await this.notificationService.createDefaultPreferences(userId, tenantId);

      res.status(201).json(preferences);
    } catch (error) {
      logger.error('Error in createDefaultPreferences controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }

  /**
   * Gets notification statistics for a user
   * @route GET /api/notifications/user/:userId/stats
   */
  async getNotificationStats(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      const stats = await this.notificationService.getNotificationStats(userId);

      res.status(200).json(stats);
    } catch (error) {
      logger.error('Error in getNotificationStats controller:', { error });
      res.status(500).json({ error: (error as Error).message });
    }
  }
}
