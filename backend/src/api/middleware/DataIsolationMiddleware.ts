import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Middleware to enforce data isolation between users
 * - Admin users can see all resources in their tenant
 * - Regular users can only see resources they created
 */
export class DataIsolationMiddleware {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Apply data isolation to a request
   * @param req Express request
   * @param res Express response
   * @param next Express next function
   */
  apply = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip for non-GET requests
      if (req.method !== "GET") {
        return next();
      }

      // Skip for authentication routes
      if (req.path.startsWith("/api/auth")) {
        return next();
      }

      // Get user from request
      const user = req.user as any;
      if (!user) {
        return next();
      }

      // Get user role and ID
      const { role, userId, tenantId } = user;

      // Admin and Super Admin users can see all resources
      if (role === "ADMIN" || role === "SUPER_ADMIN") {
        // No need to modify the request
        return next();
      }

      // For regular users, add createdById filter to query parameters
      if (!req.query) {
        req.query = {};
      }

      // Add createdById filter
      req.query.createdById = userId;

      // Log the applied filter
      logger.debug("Applied data isolation filter", {
        userId,
        tenantId,
        role,
        path: req.path,
      });

      next();
    } catch (error) {
      logger.error("Error in data isolation middleware", { error });
      next();
    }
  };
}
