/**
 * Tenant Middleware
 * Provides tenant context for API routes
 */

import { Request, Response, NextFunction } from "express";
import { TenantContext } from "../../domain/shared/TenantContext";
import { logger } from "../../infrastructure/logging/logger";

/**
 * Middleware to require and validate tenant context
 */
export const requireTenant = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      res.status(401).json({ error: "Authentication required" });
      return;
    }

    // Set tenant context from user
    TenantContext.setCurrentTenant(req.user.tenantId);

    // Log tenant context for debugging
    logger.debug("Tenant context set", {
      tenantId: req.user.tenantId,
      path: req.path,
      method: req.method,
    });

    next();
  } catch (error) {
    logger.error("Error setting tenant context:", { error });
    res.status(500).json({ error: "Failed to set tenant context" });
    return;
  }
};
