/**
 * Authentication Middleware
 * Provides JWT authentication for API routes with proper security practices
 */

import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { logger } from "../../infrastructure/logging/logger";
import { ConfigService } from "../../infrastructure/services/ConfigService";
import { TenantContext } from "../../domain/shared/TenantContext";

// Create config service instance
const configService = new ConfigService();

/**
 * Interface for JWT payload
 */
export interface JwtPayload {
  userId: string;
  tenantId: string; // Always present, use special value for super admin
  role: string;
  permissions: string[];
  sessionId: string;
  isSuperAdmin?: boolean; // Flag for super admin
  iat: number;
  exp: number;
}

/**
 * Extend Express Request interface to include user information
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        tenantId: string; // Always present, use special value for super admin
        role: string;
        permissions: string[];
        sessionId: string;
        isSuperAdmin?: boolean; // Flag for super admin
      };
    }
  }
}

/**
 * Middleware to authenticate JWT tokens
 */
export const authenticateJWT = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    res.status(401).json({ error: "Authorization header missing" });
    return;
  }

  const token = authHeader.split(" ")[1];
  if (!token) {
    res.status(401).json({ error: "Token missing" });
    return;
  }

  try {
    const decoded = jwt.verify(
      token,
      configService.getAccessTokenSecret()
    ) as JwtPayload;

    req.user = {
      userId: decoded.userId,
      tenantId: decoded.tenantId,
      role: decoded.role,
      permissions: decoded.permissions || [],
      sessionId: decoded.sessionId,
      isSuperAdmin: decoded.isSuperAdmin || false,
    };

    // Only set tenant context for regular users (not super admin)
    if (decoded.tenantId !== 'SUPER_ADMIN' && !decoded.isSuperAdmin) {
      TenantContext.setCurrentTenant(decoded.tenantId);
    }

    logger.info("Authenticated request", {
      userId: decoded.userId,
      tenantId: decoded.tenantId,
      role: decoded.role,
      isSuperAdmin: decoded.isSuperAdmin || false,
      path: req.path,
      method: req.method,
    });

    next();
  } catch (error: any) {
    if (error.name === "TokenExpiredError") {
      logger.warn("Token expired:", { error });
      res.status(401).json({ error: "Token expired", code: "TOKEN_EXPIRED" });
    } else if (error.name === "JsonWebTokenError") {
      logger.warn("Invalid token:", { error });
      res.status(403).json({ error: "Invalid token", code: "INVALID_TOKEN" });
    } else {
      logger.error("Error authenticating JWT:", { error });
      res
        .status(403)
        .json({ error: "Authentication failed", code: "AUTH_FAILED" });
    }
  }
};

/**
 * Middleware to check if user has required permissions
 * @param requiredPermissions Array of required permissions
 */
export const requirePermissions = (
  requiredPermissions: string[]
): ((req: Request, res: Response, next: NextFunction) => void) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ error: "Authentication required" });
    }

    // Check if user has all required permissions
    const hasAllPermissions = requiredPermissions.every((permission) =>
      req.user!.permissions.includes(permission)
    );

    if (!hasAllPermissions) {
      logger.warn("Permission denied", {
        userId: req.user.userId,
        tenantId: req.user.tenantId,
        requiredPermissions,
        userPermissions: req.user.permissions,
        path: req.path,
        method: req.method,
      });

      return res.status(403).json({ error: "Insufficient permissions" });
    }

    next();
  };
};

/**
 * Middleware to check if user belongs to the tenant
 * Prevents tenant data leakage by ensuring users can only access their own tenant's data
 * Super admin users can access any tenant
 */
export const validateTenantAccess = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Check if user is authenticated
  if (!req.user) {
    res.status(401).json({ error: "Authentication required" });
    return;
  }

  // Super admin can access any tenant
  if (req.user.isSuperAdmin || req.user.role === 'SUPER_ADMIN' || req.user.tenantId === 'SUPER_ADMIN') {
    next();
    return;
  }

  // Get tenant ID from request parameters or query
  const requestTenantId = req.params.tenantId || (req.query.tenantId as string);

  // If no tenant ID in request, continue (will use user's tenant from JWT)
  if (!requestTenantId) {
    next();
    return;
  }

  // Check if user's tenant matches requested tenant
  if (req.user.tenantId !== requestTenantId) {
    logger.warn("Tenant access violation", {
      userId: req.user.userId,
      userTenantId: req.user.tenantId,
      requestedTenantId: requestTenantId,
      path: req.path,
      method: req.method,
    });

    res.status(403).json({ error: "Access denied to requested tenant" });
    return;
  }

  next();
};

/**
 * Middleware to enforce data isolation
 * Ensures users can only see data they created unless they have admin permissions
 */
export const enforceDataIsolation = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Check if user is authenticated
  if (!req.user) {
    res.status(401).json({ error: "Authentication required" });
    return;
  }

  // Skip for admin users who have full access
  if (
    req.user.permissions.includes("admin:access") ||
    req.user.role === "ADMIN" ||
    req.user.role === "SUPER_ADMIN"
  ) {
    next();
    return;
  }

  // For GET requests, add a filter for the user's ID
  if (req.method === "GET") {
    // Add createdById filter to query parameters
    if (!req.query.filters) {
      req.query.filters = JSON.stringify({ createdById: req.user.userId });
    } else {
      try {
        const filters = JSON.parse(req.query.filters as string);
        filters.createdById = req.user.userId;
        req.query.filters = JSON.stringify(filters);
      } catch (error) {
        // If filters is not valid JSON, create a new filters object
        req.query.filters = JSON.stringify({ createdById: req.user.userId });
      }
    }
  }

  // For other methods, the controllers will handle data isolation
  next();
};

/**
 * Middleware to check if user is super admin
 */
export const requireSuperAdmin = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Check if user is authenticated
  if (!req.user) {
    res.status(401).json({ error: "Authentication required" });
    return;
  }

  // Check if user is super admin
  if (!req.user.isSuperAdmin && req.user.role !== 'SUPER_ADMIN') {
    logger.warn("Super admin access denied", {
      userId: req.user.userId,
      role: req.user.role,
      path: req.path,
      method: req.method,
    });

    res.status(403).json({ error: "Super admin access required" });
    return;
  }

  next();
};

/**
 * Helper function to get tenant ID from request
 * For super admin, returns the tenant ID from request params/query if available
 * For regular users, returns their tenant ID from JWT
 */
export const getTenantId = (req: Request): string => {
  if (!req.user) {
    throw new Error("User not authenticated");
  }

  // For super admin, check if tenant ID is provided in request
  if (req.user.isSuperAdmin || req.user.role === 'SUPER_ADMIN' || req.user.tenantId === 'SUPER_ADMIN') {
    const requestTenantId = req.params.tenantId || (req.query.tenantId as string);
    if (requestTenantId) {
      return requestTenantId;
    }
    throw new Error("Super admin must specify tenant ID in request");
  }

  // For regular users, use their tenant ID from JWT
  return req.user.tenantId;
};
