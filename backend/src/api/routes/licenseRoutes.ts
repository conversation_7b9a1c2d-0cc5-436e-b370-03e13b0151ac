/**
 * License Routes
 * Defines API routes for license management with proper security and tenant isolation
 */

import express from "express";
import { LicenseController } from "../controllers/LicenseController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";

export const licenseRoutes = (licenseController: LicenseController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  // License routes with permission checks
  router.post(
    "/",
    requirePermissions(["licenses:write"]),
    licenseController.createLicense.bind(licenseController)
  );
  router.get(
    "/:id",
    requirePermissions(["licenses:read"]),
    licenseController.getLicense.bind(licenseController)
  );
  router.put(
    "/:id",
    requirePermissions(["licenses:write"]),
    licenseController.updateLicense.bind(licenseController)
  );
  router.delete(
    "/:id",
    requirePermissions(["licenses:write"]),
    licenseController.deleteLicense.bind(licenseController)
  );
  router.get(
    "/tenant/:tenantId",
    requirePermissions(["licenses:read"]),
    licenseController.getLicensesByTenant.bind(licenseController)
  );
  router.get(
    "/search",
    requirePermissions(["licenses:read"]),
    licenseController.searchLicenses.bind(licenseController)
  );
  router.get(
    "/expiring/:tenantId",
    requirePermissions(["licenses:read"]),
    licenseController.getExpiringLicenses.bind(licenseController)
  );
  router.get(
    "/compliance/:tenantId",
    requirePermissions(["licenses:read"]),
    licenseController.getLicensesWithComplianceIssues.bind(licenseController)
  );
  router.get(
    "/utilization/:tenantId",
    requirePermissions(["licenses:read"]),
    licenseController.getLicensesWithUtilizationIssues.bind(licenseController)
  );
  router.post(
    "/:id/renew",
    requirePermissions(["licenses:write"]),
    licenseController.renewLicense.bind(licenseController)
  );
  router.post(
    "/:id/compliance",
    requirePermissions(["licenses:write"]),
    licenseController.updateComplianceStatus.bind(licenseController)
  );
  router.post(
    "/:id/allocation",
    requirePermissions(["licenses:write"]),
    licenseController.updateLicenseAllocation.bind(licenseController)
  );

  // License Entitlement routes with permission checks
  router.post(
    "/:licenseId/entitlements",
    requirePermissions(["licenses:write"]),
    licenseController.createEntitlement.bind(licenseController)
  );
  router.get(
    "/:licenseId/entitlements",
    requirePermissions(["licenses:read"]),
    licenseController.getEntitlements.bind(licenseController)
  );
  router.put(
    "/:licenseId/entitlements/:id",
    requirePermissions(["licenses:write"]),
    licenseController.updateEntitlement.bind(licenseController)
  );
  router.delete(
    "/:licenseId/entitlements/:id",
    requirePermissions(["licenses:write"]),
    licenseController.deleteEntitlement.bind(licenseController)
  );

  // License Document routes with permission checks
  router.post(
    "/:licenseId/documents",
    requirePermissions(["licenses:write"]),
    licenseController.createDocument.bind(licenseController)
  );
  router.get(
    "/:licenseId/documents",
    requirePermissions(["licenses:read"]),
    licenseController.getDocuments.bind(licenseController)
  );
  router.put(
    "/:licenseId/documents/:id",
    requirePermissions(["licenses:write"]),
    licenseController.updateDocument.bind(licenseController)
  );
  router.delete(
    "/:licenseId/documents/:id",
    requirePermissions(["licenses:write"]),
    licenseController.deleteDocument.bind(licenseController)
  );

  // License Usage routes with permission checks
  router.post(
    "/:licenseId/usage",
    requirePermissions(["licenses:write"]),
    licenseController.createUsageRecord.bind(licenseController)
  );
  router.get(
    "/:licenseId/usage",
    requirePermissions(["licenses:read"]),
    licenseController.getUsageHistory.bind(licenseController)
  );
  router.get(
    "/:licenseId/usage/latest",
    requirePermissions(["licenses:read"]),
    licenseController.getLatestUsage.bind(licenseController)
  );

  return router;
};
