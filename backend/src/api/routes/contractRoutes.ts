/**
 * Contract Routes
 * Defines API routes for contract management with proper security and tenant isolation
 */

import express from "express";
import { ContractController } from "../controllers/ContractController";
import { ContractAssessmentController } from "../controllers/ContractAssessmentController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";

export const contractRoutes = (
  contractController: ContractController,
  contractAssessmentController: ContractAssessmentController
) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  // Contract routes with permission checks
  router.get(
    "/with-extraction",
    requirePermissions(["contracts:read"]),
    contractController.getContractsWithExtraction.bind(contractController)
  );

  router.get(
    "/repository-statistics",
    requirePermissions(["contracts:read"]),
    contractController.getRepositoryStatistics.bind(contractController)
  );

  router.get(
    "/",
    requirePermissions(["contracts:read"]),
    contractController.getContracts.bind(contractController)
  );

  router.get(
    "/autocomplete",
    requirePermissions(["contracts:read"]),
    contractController.getContractsForAutocomplete.bind(contractController)
  );

  // Contract Hierarchy routes (must come before /:id route)
  router.get(
    "/hierarchy/:groupId",
    requirePermissions(["contracts:read"]),
    contractController.getContractsWithHierarchy.bind(contractController)
  );

  router.get(
    "/hierarchy-info/:agreementType",
    requirePermissions(["contracts:read"]),
    contractController.getHierarchyInfo.bind(contractController)
  );

  router.get(
    "/hierarchy-types",
    requirePermissions(["contracts:read"]),
    contractController.getAllHierarchyTypes.bind(contractController)
  );

  router.get(
    "/can-be-parent/:parentType/:childType",
    requirePermissions(["contracts:read"]),
    contractController.canBeParent.bind(contractController)
  );

  router.get(
    "/:id",
    requirePermissions(["contracts:read"]),
    contractController.getContract.bind(contractController)
  );

  // Get OCR text for a contract
  router.get(
    "/:id/ocr-text",
    requirePermissions(["contracts:read"]),
    contractController.getContractOCRText.bind(contractController)
  );

  router.post(
    "/",
    requirePermissions(["contracts:write"]),
    contractController.createContract.bind(contractController)
  );

  router.put(
    "/:id",
    requirePermissions(["contracts:write"]),
    contractController.updateContract.bind(contractController)
  );

  router.delete(
    "/:id",
    requirePermissions(["contracts:write"]),
    contractController.deleteContract.bind(contractController)
  );

  router.get(
    "/:id/metadata",
    requirePermissions(["contracts:read"]),
    contractController.getContractMetadata.bind(contractController)
  );

  router.post(
    "/:id/metadata",
    requirePermissions(["contracts:write"]),
    contractController.updateContractMetadata.bind(contractController)
  );

  router.get(
    "/:id/document",
    requirePermissions(["contracts:read"]),
    contractController.getContractDocument.bind(contractController)
  );

  router.get(
    "/:id/document/metadata",
    requirePermissions(["contracts:read"]),
    contractController.getContractDocumentMetadata.bind(contractController)
  );

  router.get(
    "/:id/download",
    requirePermissions(["contracts:read"]),
    contractController.downloadContractDocument.bind(contractController)
  );

  router.post(
    "/import",
    requirePermissions(["contracts:write"]),
    contractController.importContracts.bind(contractController)
  );

  router.get(
    "/import/status/:jobId",
    requirePermissions(["contracts:read"]),
    contractController.getImportStatus.bind(contractController)
  );

  router.post(
    "/:id/generate-summary",
    requirePermissions(["contracts:write"]),
    contractController.generateContractSummary.bind(contractController)
  );

  router.post(
    "/:id/generate-dora-compliance",
    requirePermissions(["contracts:write"]),
    contractController.generateDORACompliance.bind(contractController)
  );

  // Integrity analysis routes
  router.post(
    "/:id/generate-integrity-analysis",
    requirePermissions(["contracts:write"]),
    contractController.generateIntegrityAnalysis.bind(contractController)
  );

  // Reporting relationship routes
  router.put(
    "/:id/reporting-to",
    requirePermissions(["contracts:write"]),
    contractController.updateReportingRelationship.bind(contractController)
  );

  router.delete(
    "/:id/reporting-to",
    requirePermissions(["contracts:write"]),
    contractController.removeReportingRelationship.bind(contractController)
  );

  router.get(
    "/:id/children",
    requirePermissions(["contracts:read"]),
    contractController.getChildContracts.bind(contractController)
  );

  router.get(
    "/:id/reporting-chain",
    requirePermissions(["contracts:read"]),
    contractController.getReportingChain.bind(contractController)
  );

  router.post(
    "/:id/validate-reporting",
    requirePermissions(["contracts:read"]),
    contractController.validateReportingRelationship.bind(contractController)
  );

  // Migration and validation routes
  router.post(
    "/migrate-reporting",
    requirePermissions(["contracts:write"]),
    contractController.migrateReportingRelationships.bind(contractController)
  );

  router.get(
    "/validate-reporting",
    requirePermissions(["contracts:read"]),
    contractController.validateAllReportingRelationships.bind(
      contractController
    )
  );

  // Debug endpoint for tabular summary testing
  router.post(
    "/:id/debug-tabular-summary",
    requirePermissions(["contracts:write"]),
    contractController.debugTabularSummary.bind(contractController)
  );

  // Contract Assessment routes
  router.post(
    "/:id/assessment",
    requirePermissions(["contracts:write"]),
    contractAssessmentController.createOrUpdateAssessment.bind(
      contractAssessmentController
    )
  );

  router.get(
    "/:id/assessment",
    requirePermissions(["contracts:read"]),
    contractAssessmentController.getAssessment.bind(
      contractAssessmentController
    )
  );

  // Contract entitlements route
  router.get(
    "/:id/entitlements",
    requirePermissions(["contracts:read"]),
    contractController.getContractEntitlements.bind(contractController)
  );

  return router;
};
