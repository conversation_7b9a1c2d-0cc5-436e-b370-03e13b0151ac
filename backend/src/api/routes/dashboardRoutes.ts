/**
 * Dashboard Routes
 * API routes for dashboard data and analytics
 */

import { Router } from "express";
import { DashboardController } from "../controllers/DashboardController";
import { authenticateJWT } from "../middleware/auth";

export const dashboardRoutes = (dashboardController: DashboardController) => {
  const router = Router();

  // Apply authentication middleware to all dashboard routes
  router.use(authenticateJWT);

  /**
   * GET /api/dashboard/summary
   * Get dashboard summary statistics
   */
  router.get("/summary", (req, res) => {
    dashboardController.getSummary(req, res);
  });

  /**
   * GET /api/dashboard/spend-analysis
   * Get spend analysis data (by provider, agreement type, etc.)
   */
  router.get("/spend-analysis", (req, res) => {
    dashboardController.getSpendAnalysis(req, res);
  });

  /**
   * GET /api/dashboard/renewal-timeline
   * Get renewal timeline data (30, 60, 90, 180 days)
   */
  router.get("/renewal-timeline", (req, res) => {
    dashboardController.getRenewalTimeline(req, res);
  });

  /**
   * GET /api/dashboard/confidence-distribution
   * Get extraction confidence distribution
   */
  router.get("/confidence-distribution", (req, res) => {
    dashboardController.getConfidenceDistribution(req, res);
  });

  /**
   * GET /api/dashboard/top-contracts
   * Get top contracts by value
   * Query params: limit (default: 10)
   */
  router.get("/top-contracts", (req, res) => {
    dashboardController.getTopContracts(req, res);
  });

  /**
   * GET /api/dashboard/contracts-by-status
   * Get contracts by status (active/inactive)
   * Query params: status (active|inactive), limit (default: 10)
   */
  router.get("/contracts-by-status", (req, res) => {
    dashboardController.getContractsByStatus(req, res);
  });

  /**
   * GET /api/dashboard/contracts-by-aging
   * Get contracts by aging category (olderThan4Y/approaching4Y)
   * Query params: agingCategory (olderThan4Y|approaching4Y), limit (default: 10)
   */
  router.get("/contracts-by-aging", (req, res) => {
    dashboardController.getContractsByAging(req, res);
  });

  /**
   * GET /api/dashboard/critical-contracts-by-service-type
   * Get critical contracts by service type
   * Query params: serviceType (service type), limit (default: 10)
   */
  router.get("/critical-contracts-by-service-type", (req, res) => {
    dashboardController.getCriticalContractsByServiceType(req, res);
  });

  /**
   * GET /api/dashboard/contracts-by-criticality
   * Get contracts by criticality level (critical/important/standard)
   * Query params: criticality (critical|important|standard), limit (default: 10)
   */
  router.get("/contracts-by-criticality", (req, res) => {
    dashboardController.getContractsByCriticality(req, res);
  });

  /**
   * GET /api/dashboard/contracts-by-type
   * Get contracts by agreement type
   * Query params: type (agreement type), limit (default: 10)
   */
  router.get("/contracts-by-type", (req, res) => {
    dashboardController.getContractsByType(req, res);
  });

  /**
   * GET /api/dashboard/expired-contracts-by-service-type
   * Get expired contracts by service type
   * Query params: serviceType (service type), limit (default: 10)
   */
  router.get("/expired-contracts-by-service-type", (req, res) => {
    dashboardController.getExpiredContractsByServiceType(req, res);
  });

  /**
   * GET /api/dashboard/contracts-by-service-type
   * Get contracts by service type
   * Query params: serviceType (service type), limit (default: 10)
   */
  router.get("/contracts-by-service-type", (req, res) => {
    dashboardController.getContractsByServiceType(req, res);
  });

  /**
   * GET /api/dashboard/active-contracts-per-supplier
   * Get active contracts count per supplier
   */
  router.get("/active-contracts-per-supplier", (req, res) => {
    dashboardController.getActiveContractsPerSupplier(req, res);
  });

  /**
   * GET /api/dashboard/expiring-contracts
   * Get contracts expiring in 30, 60, 90 day rolling windows
   */
  router.get("/expiring-contracts", (req, res) => {
    dashboardController.getExpiringContracts(req, res);
  });

  /**
   * GET /api/dashboard/auto-renewals
   * Get auto-renewals in 30, 60, 90 day rolling windows with safe/not safe classification
   */
  router.get("/auto-renewals", (req, res) => {
    dashboardController.getAutoRenewals(req, res);
  });

  /**
   * GET /api/dashboard/auto-renewals-by-classification
   * Get auto-renewals broken down by contract classification
   */
  router.get("/auto-renewals-by-classification", (req, res) => {
    dashboardController.getAutoRenewalsByClassification(req, res);
  });

  /**
   * GET /api/dashboard/portfolio-overview
   * Get portfolio overview data for all 8 charts
   */
  router.get("/portfolio-overview", (req, res) => {
    dashboardController.getPortfolioOverview(req, res);
  });

  /**
   * GET /api/dashboard/layout
   * Get user's dashboard layout
   */
  router.get("/layout", (req, res) => {
    dashboardController.getLayout(req, res);
  });

  /**
   * POST /api/dashboard/layout
   * Save user's dashboard layout
   */
  router.post("/layout", (req, res) => {
    dashboardController.saveLayout(req, res);
  });

  /**
   * PUT /api/dashboard/layout/:id
   * Update user's dashboard layout
   */
  router.put("/layout/:id", (req, res) => {
    dashboardController.updateLayout(req, res);
  });

  /**
   * DELETE /api/dashboard/layout/:id
   * Delete user's dashboard layout
   */
  router.delete("/layout/:id", (req, res) => {
    dashboardController.deleteLayout(req, res);
  });

  /**
   * GET /api/dashboard/widgets
   * Get user's dashboard widgets grouped by category
   */
  router.get("/widgets", (req, res) => {
    dashboardController.getWidgets(req, res);
  });

  /**
   * GET /api/dashboard/widgets/category/:category
   * Get widgets by category (priority or portfolio)
   */
  router.get("/widgets/category/:category", (req, res) => {
    dashboardController.getWidgetsByCategory(req, res);
  });

  /**
   * PUT /api/dashboard/widgets/:widgetId/visibility
   * Update widget visibility
   */
  router.put("/widgets/:widgetId/visibility", (req, res) => {
    dashboardController.updateWidgetVisibility(req, res);
  });

  /**
   * PUT /api/dashboard/widgets/:widgetId/configuration
   * Update widget configuration
   */
  router.put("/widgets/:widgetId/configuration", (req, res) => {
    dashboardController.updateWidgetConfiguration(req, res);
  });

  /**
   * PUT /api/dashboard/widgets/bulk
   * Bulk update multiple widgets
   */
  router.put("/widgets/bulk", (req, res) => {
    dashboardController.bulkUpdateWidgets(req, res);
  });

  /**
   * POST /api/dashboard/widgets/reset
   * Reset user widgets to defaults
   */
  router.post("/widgets/reset", (req, res) => {
    dashboardController.resetWidgets(req, res);
  });

  return router;
};
