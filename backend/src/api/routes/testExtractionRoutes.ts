/**
 * Test Extraction Routes
 * Routes for testing the enhanced three-tier extraction system
 */

import express from "express";
import { TestExtractionController } from "../controllers/TestExtractionController";
import { authenticateJWT, requirePermissions } from "../middleware/auth";
import { requireTenant } from "../middleware/tenantMiddleware";

export const testExtractionRoutes = (
  testExtractionController: TestExtractionController
) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(requireTenant);

  // Test extraction with uploaded document
  router.post(
    "/test-document",
    requirePermissions(["contracts:write"]),
    TestExtractionController.getUploadMiddleware(),
    testExtractionController.testEnhancedExtraction.bind(
      testExtractionController
    )
  );

  // Test extraction with sample contract
  router.post(
    "/test-sample",
    requirePermissions(["contracts:read"]),
    testExtractionController.testWithSampleContract.bind(
      testExtractionController
    )
  );

  return router;
};
