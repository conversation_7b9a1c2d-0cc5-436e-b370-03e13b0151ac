/**
 * AI Routes
 * Defines API routes for AI-related features
 */

import { Router } from "express";
import { authenticateJWT, validateTenantAccess } from "../middleware/auth";
import * as AIController from "../controllers/AIController";

const router = Router();

/**
 * @swagger
 * /api/ai/document:
 *   post:
 *     summary: Process a document for analysis
 *     description: Analyzes a document and extracts information using AI
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentId
 *               - documentFormat
 *               - documentUri
 *             properties:
 *               documentId:
 *                 type: string
 *                 description: ID of the document
 *               documentFormat:
 *                 type: string
 *                 enum: [PDF, DOCX, TXT, HTML]
 *                 description: Format of the document
 *               contractId:
 *                 type: string
 *                 description: ID of the associated contract (optional)
 *               options:
 *                 type: object
 *                 properties:
 *                   extractEntities:
 *                     type: boolean
 *                     description: Whether to extract entities
 *                   extractClauses:
 *                     type: boolean
 *                     description: Whether to extract clauses
 *                   generateEmbeddings:
 *                     type: boolean
 *                     description: Whether to generate embeddings
 *               documentUri:
 *                 type: string
 *                 description: URI to the document storage
 *     responses:
 *       202:
 *         description: Document analysis job queued
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post(
  "/document",
  authenticateJWT,
  validateTenantAccess,
  AIController.processDocument
);

/**
 * @swagger
 * /api/ai/job/{jobId}:
 *   get:
 *     summary: Get job status
 *     description: Gets the status of an AI job
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the job
 *     responses:
 *       200:
 *         description: Job status
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get("/job/:jobId", authenticateJWT, AIController.getJobStatus);

/**
 * @swagger
 * /api/ai/embeddings:
 *   post:
 *     summary: Generate embeddings
 *     description: Generates embeddings for text
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentId
 *               - text
 *             properties:
 *               documentId:
 *                 type: string
 *                 description: ID of the document
 *               text:
 *                 type: string
 *                 description: Text to generate embeddings for
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       202:
 *         description: Embedding generation job queued
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post(
  "/embeddings",
  authenticateJWT,
  validateTenantAccess,
  AIController.generateEmbeddings
);

/**
 * @swagger
 * /api/ai/conversation:
 *   post:
 *     summary: Create conversation
 *     description: Creates a new conversation for RAG
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Title of the conversation
 *     responses:
 *       201:
 *         description: Conversation created
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post(
  "/conversation",
  authenticateJWT,
  validateTenantAccess,
  AIController.createConversation
);

/**
 * @swagger
 * /api/ai/conversation/{conversationId}:
 *   get:
 *     summary: Get conversation
 *     description: Gets a conversation by ID
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: conversationId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the conversation
 *     responses:
 *       200:
 *         description: Conversation
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.get(
  "/conversation/:conversationId",
  authenticateJWT,
  validateTenantAccess,
  AIController.getConversation
);

/**
 * @swagger
 * /api/ai/query:
 *   post:
 *     summary: Process query
 *     description: Processes a RAG query
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 description: Query text
 *               conversationId:
 *                 type: string
 *                 description: ID of the conversation (optional)
 *     responses:
 *       200:
 *         description: Query response
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Conversation not found
 *       500:
 *         description: Server error
 */
router.post(
  "/query",
  authenticateJWT,
  validateTenantAccess,
  AIController.processQuery
);

/**
 * @swagger
 * /api/ai/analyze-contract:
 *   post:
 *     summary: Analyze Agreement Document
 *     description: Analyzes a Agreement Document using AI to extract metadata
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Agreement Document file
 *     responses:
 *       200:
 *         description: Agreement Analysis result
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post(
  "/analyze-contract",
  authenticateJWT,
  validateTenantAccess,
  AIController.analyzeContractDocument
);

export default router;
