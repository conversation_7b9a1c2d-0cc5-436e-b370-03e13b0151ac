/**
 * Authentication Routes
 * Defines API routes for user authentication with proper security
 */

import { Router, Request, Response } from "express";
import { AuthController } from "../controllers/AuthController";

// Define route handler type
type RouteHandler = (req: Request, res: Response) => void;

export const authRoutes = (authController: AuthController): Router => {
  const router = Router();

  // Login route
  const loginHandler: RouteHandler = (req, res) => {
    authController.login(req, res);
  };
  router.post("/login", loginHandler);

  // Register route
  const registerHandler: RouteHandler = (req, res) => {
    authController.register(req, res);
  };
  router.post("/register", registerHandler);

  // Refresh token route
  const refreshTokenHandler: RouteHandler = (req, res) => {
    authController.refreshToken(req, res);
  };
  router.post("/refresh-token", refreshTokenHandler);

  // Logout route (requires authentication)
  const logoutHandler: RouteHandler = (req, res) => {
    // Note: Authentication middleware is temporarily disabled
    // Check if user is authenticated manually
    if (!req.headers.authorization) {
      return res.status(401).json({ error: "Authorization header missing" });
    }
    authController.logout(req, res);
  };
  router.post("/logout", logoutHandler);

  // Profile route (requires authentication)
  const profileHandler: RouteHandler = (req, res) => {
    // Note: Authentication middleware is temporarily disabled
    // Check if user is authenticated manually
    if (!req.headers.authorization) {
      return res.status(401).json({ error: "Authorization header missing" });
    }
    authController.getProfile(req, res);
  };
  router.get("/profile", profileHandler);

  // Update persona route (requires authentication)
  const updatePersonaHandler: RouteHandler = (req, res) => {
    // Note: Authentication middleware is temporarily disabled
    // Check if user is authenticated manually
    if (!req.headers.authorization) {
      return res.status(401).json({ error: "Authorization header missing" });
    }
    authController.updatePersona(req, res);
  };
  router.put("/persona", updatePersonaHandler);

  return router;
};
