/**
 * Role Routes
 * Defines API routes for role management with proper security and tenant isolation
 */

import express from 'express';
import { RoleController } from '../controllers/RoleController';
import { authenticateJWT, validateTenantAccess, requirePermissions } from '../middleware/auth';

export const roleRoutes = (roleController: RoleController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Role management routes with permission checks
  router.get('/', requirePermissions(['users:read']), roleController.getRoles.bind(roleController));
  router.get('/:id', requirePermissions(['users:read']), roleController.getRole.bind(roleController));
  router.post('/', requirePermissions(['users:write']), roleController.createRole.bind(roleController));
  router.put('/:id', requirePermissions(['users:write']), roleController.updateRole.bind(roleController));
  router.delete('/:id', requirePermissions(['users:write']), roleController.deleteRole.bind(roleController));

  return router;
};
