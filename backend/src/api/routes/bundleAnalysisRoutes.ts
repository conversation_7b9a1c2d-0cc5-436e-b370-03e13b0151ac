/**
 * Bundle Analysis Routes
 * Defines API routes for bundle interconnection analysis
 */

import express from "express";
import { BundleAnalysisController } from "../controllers/BundleAnalysisController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";

export const bundleAnalysisRoutes = (bundleAnalysisController: BundleAnalysisController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  /**
   * @route GET /api/bundle-analysis/:folderId
   * @desc Get bundle analysis for a folder if it exists
   * @access Private - requires contracts:read permission
   */
  router.get(
    "/:folderId",
    requirePermissions(["contracts:read"]),
    bundleAnalysisController.getBundleAnalysis.bind(bundleAnalysisController)
  );

  /**
   * @route POST /api/bundle-analysis/:folderId
   * @desc Generate new bundle analysis for a folder using Gemini AI
   * @access Private - requires contracts:write permission
   */
  router.post(
    "/:folderId",
    requirePermissions(["contracts:write"]),
    bundleAnalysisController.generateBundleAnalysis.bind(bundleAnalysisController)
  );

  return router;
};
