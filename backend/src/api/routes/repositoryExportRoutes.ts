/**
 * Repository Export Routes
 * Defines API routes for repository export functionality with proper security and tenant isolation
 */

import express from "express";

import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";
import { RepositoryExportController } from "../controllers/RepositoryExportController";

export const repositoryExportRoutes = (
  repositoryExportController: RepositoryExportController
) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  // Repository export routes with permission checks
  router.get(
    "/export/excel",
    requirePermissions(["contracts:read"]),
    repositoryExportController.exportToExcel.bind(repositoryExportController)
  );

  router.post(
    "/export/ui-table",
    requirePermissions(["contracts:read"]),
    repositoryExportController.exportUITableToExcel.bind(
      repositoryExportController
    )
  );

  router.get(
    "/export/stats",
    requirePermissions(["contracts:read"]),
    repositoryExportController.getExportStatistics.bind(
      repositoryExportController
    )
  );

  return router;
};
