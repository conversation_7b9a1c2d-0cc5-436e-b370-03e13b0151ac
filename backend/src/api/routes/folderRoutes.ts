/**
 * Folder Routes
 * Defines API routes for folder management with proper security and tenant isolation
 */

import express from "express";
import { FolderController } from "../controllers/FolderController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";

export const folderRoutes = (folderController: FolderController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  // Folder routes with permission checks
  router.get(
    "/",
    requirePermissions(["contracts:read"]),
    folderController.getFolders.bind(folderController)
  );

  router.get(
    "/by-providers",
    requirePermissions(["contracts:read"]),
    folderController.getFoldersByProviders.bind(folderController)
  );

  router.get(
    "/autocomplete",
    requirePermissions(["contracts:read"]),
    folderController.getFoldersForAutocomplete.bind(folderController)
  );

  router.get(
    "/:id",
    requirePermissions(["contracts:read"]),
    folderController.getFolder.bind(folderController)
  );

  router.post(
    "/",
    requirePermissions(["contracts:write"]),
    folderController.createFolder.bind(folderController)
  );

  router.put(
    "/:id",
    requirePermissions(["contracts:write"]),
    folderController.updateFolder.bind(folderController)
  );

  router.delete(
    "/:id",
    requirePermissions(["contracts:write"]),
    folderController.deleteFolder.bind(folderController)
  );

  router.post(
    "/move-contracts",
    requirePermissions(["contracts:write"]),
    folderController.moveContractsToFolder.bind(folderController)
  );

  return router;
};
