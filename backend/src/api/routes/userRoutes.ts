/**
 * User Routes
 * Defines API routes for user management with proper security and tenant isolation
 */

import express from 'express';
import { UserController } from '../controllers/UserController';
import { authenticateJWT, validateTenantAccess, requirePermissions } from '../middleware/auth';

export const userRoutes = (userController: UserController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // User management routes with permission checks
  router.get('/', requirePermissions(['users:read']), userController.getUsers.bind(userController));
  router.get('/:id', requirePermissions(['users:read']), userController.getUser.bind(userController));
  router.post('/', requirePermissions(['users:write']), userController.createUser.bind(userController));
  router.put('/:id', requirePermissions(['users:write']), userController.updateUser.bind(userController));
  router.delete('/:id', requirePermissions(['users:write']), userController.deleteUser.bind(userController));

  return router;
};
