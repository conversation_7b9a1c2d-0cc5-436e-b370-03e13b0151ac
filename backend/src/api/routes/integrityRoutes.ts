/**
 * Integrity Configuration Routes
 * Routes for managing integrity configurations and templates
 */

import { Router } from "express";
import {
  authenticateJWT,
  requirePermissions,
  validateTenantAccess,
  enforceDataIsolation
} from "../middleware/auth";

// Import the controller factory function
export default function integrityRoutes(contractController: any) {
  const router = Router();

  // Create middleware stack for authenticated routes
  const authStack = [
    authenticateJWT,
    validateTenantAccess,
    enforceDataIsolation,
  ];

  /**
   * @route GET /api/integrity/template/public
   * @desc Get default integrity template (public for testing)
   * @access Public
   */
  router.get(
    "/template/public",
    contractController.getIntegrityTemplate.bind(contractController)
  );

  /**
   * @route GET /api/integrity/configurations
   * @desc Get user's integrity configurations
   * @access Private
   */
  router.get(
    "/configurations",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractController.getIntegrityConfigurations.bind(contractController)
  );

  /**
   * @route GET /api/integrity/configurations/active
   * @desc Get user's active integrity configuration
   * @access Private
   */
  router.get(
    "/configurations/active",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractController.getActiveIntegrityConfiguration.bind(contractController)
  );

  /**
   * @route POST /api/integrity/configurations
   * @desc Create new integrity configuration
   * @access Private
   */
  router.post(
    "/configurations",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractController.createIntegrityConfiguration.bind(contractController)
  );

  /**
   * @route PUT /api/integrity/configurations/:id
   * @desc Update integrity configuration
   * @access Private
   */
  router.put(
    "/configurations/:id",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractController.updateIntegrityConfiguration.bind(contractController)
  );

  /**
   * @route POST /api/integrity/configurations/:id/activate
   * @desc Set integrity configuration as active
   * @access Private
   */
  router.post(
    "/configurations/:id/activate",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractController.setActiveIntegrityConfiguration.bind(contractController)
  );

  /**
   * @route POST /api/integrity/configurations/reset-to-template
   * @desc Reset user's integrity configuration to default template
   * @access Private
   */
  router.post(
    "/configurations/reset-to-template",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractController.resetIntegrityConfigurationToTemplate.bind(contractController)
  );

  /**
   * @route GET /api/integrity/template
   * @desc Get default integrity template
   * @access Private
   */
  router.get(
    "/template",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractController.getIntegrityTemplate.bind(contractController)
  );

  return router;
}
