/**
 * Contract Extraction Routes
 * Defines API routes for three-tier contract extraction management
 */

import express from "express";
import { ContractExtractionController } from "../controllers/ContractExtractionController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
  enforceDataIsolation,
} from "../middleware/auth";

export const contractExtractionRoutes = (
  contractExtractionController: ContractExtractionController
) => {
  const router = express.Router();

  // Create middleware stack for contract extraction routes
  const authStack = [
    authenticateJWT,
    validateTenantAccess,
    enforceDataIsolation,
  ];

  // Contract extraction routes with permission checks
  router.get(
    "/contracts/:contractId/extraction",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractExtractionController.getContractExtraction.bind(
      contractExtractionController
    )
  );

  router.post(
    "/contracts/:contractId/extraction",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.createContractExtraction.bind(
      contractExtractionController
    )
  );

  router.put(
    "/contracts/:contractId/extraction",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.updateContractExtraction.bind(
      contractExtractionController
    )
  );

  router.patch(
    "/contracts/:contractId/extraction",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.patchContractExtraction.bind(
      contractExtractionController
    )
  );

  router.delete(
    "/contracts/:contractId/extraction",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.deleteContractExtraction.bind(
      contractExtractionController
    )
  );

  // Refresh extraction route
  router.post(
    "/contracts/:contractId/extraction/refresh",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.refreshContractExtraction.bind(
      contractExtractionController
    )
  );

  // Summary generation route
  router.post(
    "/contracts/:contractId/extraction/generate-summary",
    ...authStack,
    requirePermissions(["contracts:write"]),
    contractExtractionController.generateDocumentSummary.bind(
      contractExtractionController
    )
  );

  // Extraction statistics and analytics routes
  router.get(
    "/extractions/stats",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractExtractionController.getExtractionStats.bind(
      contractExtractionController
    )
  );

  router.get(
    "/extractions/by-confidence",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractExtractionController.getExtractionsByConfidence.bind(
      contractExtractionController
    )
  );

  // Supplier contract relationships route
  router.get(
    "/suppliers/:supplierName/contract-relationships",
    ...authStack,
    requirePermissions(["contracts:read"]),
    contractExtractionController.getSupplierContractRelationships.bind(
      contractExtractionController
    )
  );

  return router;
};
