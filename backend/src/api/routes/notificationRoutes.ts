/**
 * Notification Routes
 * Defines API routes for notification management with proper security and tenant isolation
 */

import express from 'express';
import { NotificationController } from '../controllers/NotificationController';
import { authenticateJWT, validateTenantAccess, requirePermissions } from '../middleware/auth';

export const notificationRoutes = (notificationController: NotificationController) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // User notification routes - basic access for authenticated users
  router.get('/user/:userId', notificationController.getUserNotifications.bind(notificationController));
  router.get('/user/:userId/unread', notificationController.getUnreadNotifications.bind(notificationController));
  router.get('/user/:userId/live', notificationController.getLiveNotifications.bind(notificationController));
  router.post('/user/:userId/read-all', notificationController.markAllAsRead.bind(notificationController));

  // Notification preference routes - basic access for authenticated users
  router.get('/user/:userId/preferences', notificationController.getUserPreferences.bind(notificationController));
  router.put('/user/:userId/preferences', notificationController.updateUserPreferences.bind(notificationController));
  router.post('/user/:userId/preferences/default', notificationController.createDefaultPreferences.bind(notificationController));

  // Notification statistics and management
  router.get('/user/:userId/stats', notificationController.getNotificationStats.bind(notificationController));
  router.post('/cleanup', requirePermissions(['admin:access']), notificationController.cleanupNotifications.bind(notificationController));

  // Individual notification routes - basic access for authenticated users
  router.post('/:id/read', notificationController.markAsRead.bind(notificationController));
  router.delete('/:id', notificationController.deleteNotification.bind(notificationController));

  // Notification creation routes with appropriate permission checks
  router.post('/contract', requirePermissions(['contracts:read']), notificationController.createContractNotification.bind(notificationController));
  router.post('/license', requirePermissions(['licenses:read']), notificationController.createLicenseNotification.bind(notificationController));
  router.post('/system', requirePermissions(['admin:access']), notificationController.createSystemNotification.bind(notificationController));
  router.post('/security', requirePermissions(['admin:access']), notificationController.createSecurityNotification.bind(notificationController));

  return router;
};
