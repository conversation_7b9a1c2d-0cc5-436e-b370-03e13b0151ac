/**
 * API Routes
 * Configures all API routes for the application
 */

import * as express from "express";
import { EncryptionService } from "../../infrastructure/services/EncryptionService";
import { ConfigService } from "../../infrastructure/services/ConfigService";
import { AuthService } from "../../infrastructure/services/AuthService";
import { PrismaService } from "../../infrastructure/services/PrismaService";

// Import repositories
import { LicenseRepository } from "../../infrastructure/repositories/LicenseRepository";
import { ContractRepository } from "../../infrastructure/repositories/ContractRepository";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { FolderRepository } from "../../infrastructure/repositories/FolderRepository";

// Import services
import { LicenseService } from "../services/LicenseService";
import { NotificationService } from "../services/NotificationService";
import { TenantSettingsService } from "../services/TenantSettingsService";
import { ContractService } from "../services/ContractService";
import { ContractAssessmentService } from "../services/ContractAssessmentService";
// Note: ContractAutoGroupingService removed since smart grouping functionality is disabled
import { ContractAIService } from "../services/ContractAIService";
import { FolderService } from "../services/FolderService";
import { UserService } from "../../infrastructure/services/UserService";
import { RoleService } from "../../infrastructure/services/RoleService";

// Import controllers
import { LicenseController } from "../controllers/LicenseController";
import { NotificationController } from "../controllers/NotificationController";
import { TenantSettingsController } from "../controllers/TenantSettingsController";
import { AuthController } from "../controllers/AuthController";
import { ContractController } from "../controllers/ContractController";
import { ContractAssessmentController } from "../controllers/ContractAssessmentController";

// Note: ContractAutoGroupingController removed since smart grouping functionality is disabled
import { ContractExtractionController } from "../controllers/ContractExtractionController";
import { TestExtractionController } from "../controllers/TestExtractionController";
import { FolderController } from "../controllers/FolderController";
import { RepositoryExportController } from "../controllers/RepositoryExportController";
import { UserController } from "../controllers/UserController";
import { RoleController } from "../controllers/RoleController";
import { DashboardController } from "../controllers/DashboardController";
import { BundleAnalysisController } from "../controllers/BundleAnalysisController";

// Import route definitions
import { licenseRoutes } from "./licenseRoutes";
import { notificationRoutes } from "./notificationRoutes";
import { tenantSettingsRoutes } from "./tenantSettingsRoutes";
import { authRoutes } from "./authRoutes";
import { contractRoutes } from "./contractRoutes";

// Note: contractAutoGroupingRoutes removed since smart grouping functionality is disabled
import { contractExtractionRoutes } from "./contractExtractionRoutes";
import { testExtractionRoutes } from "./testExtractionRoutes";
import { folderRoutes } from "./folderRoutes";
import { repositoryExportRoutes } from "./repositoryExportRoutes";
import { userRoutes } from "./userRoutes";
import { roleRoutes } from "./roleRoutes";
import aiRoutes from "./aiRoutes";
import riskAssessmentRoutes from "./riskAssessmentRoutes";
import licenseOptimizationRoutes from "./licenseOptimizationRoutes";
import { geminiRoutes } from "./geminiRoutes";
import { chatRoutes } from "./chatRoutes";
import { bundleAnalysisRoutes } from "./bundleAnalysisRoutes";

import entitlementAnalysisRoutes from "./entitlementAnalysisRoutes";
import { dashboardRoutes } from "./dashboardRoutes";
import superAdminRoutes from "./superAdminRoutes";
import integrityRoutes from "./integrityRoutes";

// Update any imports from authMiddleware to use auth instead
// Remove any imports from authMiddleware
// import { authenticate } from '../middleware/authMiddleware';

export const configureRoutes = (app: express.Application) => {
  const prisma = PrismaService.getInstance();
  const configService = new ConfigService();
  const encryptionService = new EncryptionService(configService);
  const authService = new AuthService();

  // Initialize repositories
  const licenseRepository = new LicenseRepository(prisma, encryptionService);
  const contractRepository = new ContractRepository(prisma, encryptionService);
  const contractExtractionRepository = new ContractExtractionRepository(prisma);
  const folderRepository = new FolderRepository(prisma);

  // Initialize services
  const licenseService = new LicenseService(
    licenseRepository,
    encryptionService
  );
  const notificationService = new NotificationService(prisma);
  const tenantSettingsService = new TenantSettingsService(prisma);
  const contractService = new ContractService(
    contractRepository,
    contractExtractionRepository,
    encryptionService
  );
  const contractAssessmentService = new ContractAssessmentService(prisma);
  const folderService = new FolderService(folderRepository);
  const contractAIService = new ContractAIService(prisma);
  // Note: contractAutoGroupingService removed since smart grouping functionality is disabled

  // Initialize services for user management
  const userService = new UserService(prisma);
  const roleService = new RoleService(prisma);

  // Initialize controllers
  const authController = new AuthController(authService, prisma);
  const licenseController = new LicenseController(licenseService);
  const notificationController = new NotificationController(
    notificationService
  );
  const tenantSettingsController = new TenantSettingsController(
    tenantSettingsService
  );
  const contractController = new ContractController(contractService, prisma);
  const contractAssessmentController = new ContractAssessmentController(
    contractAssessmentService
  );

  // Note: contractAutoGroupingController removed since smart grouping functionality is disabled
  const contractExtractionController = new ContractExtractionController(prisma);
  const testExtractionController = new TestExtractionController(prisma);
  const folderController = new FolderController(
    folderService,
    contractAIService
  );
  // Note: FolderExportController removed since folders are now virtual
  const repositoryExportController = new RepositoryExportController(
    contractService,
    contractExtractionRepository
  );
  const userController = new UserController(userService, prisma);
  const roleController = new RoleController(roleService, prisma);
  const dashboardController = new DashboardController(
    prisma,
    contractExtractionRepository
  );
  const bundleAnalysisController = new BundleAnalysisController(
    prisma,
    contractAIService
  );

  // Configure routes
  app.use("/api/auth", authRoutes(authController));
  app.use("/api/licenses", licenseRoutes(licenseController));
  app.use("/api/notifications", notificationRoutes(notificationController));
  app.use(
    "/api/tenant-settings",
    tenantSettingsRoutes(tenantSettingsController)
  );

  // Legacy contract routes
  app.use(
    "/api/contracts",
    contractRoutes(contractController, contractAssessmentController)
  );

  // Note: Contract auto-grouping routes removed since smart grouping functionality is disabled

  // Contract extraction routes (three-tier system)
  app.use("/api", contractExtractionRoutes(contractExtractionController));

  // Test extraction routes (for testing enhanced AI system)
  app.use(
    "/api/test-extraction",
    testExtractionRoutes(testExtractionController)
  );

  // Folder routes
  app.use("/api/folders", folderRoutes(folderController));

  // Note: Folder export routes removed since folders are now virtual

  // Repository export routes
  app.use(
    "/api/repository",
    repositoryExportRoutes(repositoryExportController)
  );

  // Note: Folder hierarchy routes removed since folders are now virtual

  app.use("/api/ai", aiRoutes);
  app.use("/api/risk-assessment", riskAssessmentRoutes);
  app.use("/api/license-optimization", licenseOptimizationRoutes);
  app.use("/api/users", userRoutes(userController));
  app.use("/api/roles", roleRoutes(roleController));
  app.use("/api/gemini", geminiRoutes());
  app.use("/api/chat", chatRoutes());
  app.use("/api/entitlement-analysis", entitlementAnalysisRoutes);
  app.use("/api/dashboard", dashboardRoutes(dashboardController));
  app.use("/api/super-admin", superAdminRoutes);
  app.use("/api/integrity", integrityRoutes(contractController));
  app.use("/api/bundle-analysis", bundleAnalysisRoutes(bundleAnalysisController));

  // Add more routes as needed
};
