/**
 * Super Admin Routes
 * Routes for super admin functionality and platform management
 */

import { Router } from "express";
import { SuperAdminController } from "../controllers/SuperAdminController";
import { authenticateJWT, requireSuperAdmin } from "../middleware/auth";

const router = Router();
const superAdminController = new SuperAdminController();

// Apply authentication and super admin middleware to all routes
router.use(authenticateJWT);
router.use(requireSuperAdmin);

/**
 * @route GET /api/super-admin/test
 * @desc Test super admin authentication
 * @access Super Admin only
 */
router.get("/test", (req, res) => {
  superAdminController.testAuth(req, res);
});

/**
 * @route GET /api/super-admin/platform/overview
 * @desc Get platform overview statistics
 * @access Super Admin only
 */
router.get("/platform/overview", (req, res) => {
  superAdminController.getPlatformOverview(req, res);
});

/**
 * @route GET /api/super-admin/tenants
 * @desc Get all tenants with basic information
 * @access Super Admin only
 */
router.get("/tenants", (req, res) => {
  superAdminController.getAllTenants(req, res);
});

/**
 * @route GET /api/super-admin/users
 * @desc Get all users across all tenants
 * @access Super Admin only
 */
router.get("/users", (req, res) => {
  superAdminController.getAllUsers(req, res);
});

export default router;
