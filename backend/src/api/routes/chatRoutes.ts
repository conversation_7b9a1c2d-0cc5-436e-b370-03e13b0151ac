/**
 * Chat Routes
 * Defines API routes for persistent chat functionality
 */

import express from "express";
import { PrismaClient } from "@prisma/client";
import { ChatController } from "../controllers/ChatController";
import {
  authenticateJWT,
  validateTenantAccess,
  enforceDataIsolation,
} from "../middleware/auth";

export const chatRoutes = () => {
  const router = express.Router();
  const prisma = new PrismaClient();
  const chatController = new ChatController(prisma);

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Apply data isolation middleware to all routes
  router.use(enforceDataIsolation);

  /**
   * @swagger
   * /api/chat/conversations:
   *   get:
   *     summary: Get user conversations
   *     description: Gets all conversations for the authenticated user
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of conversations
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  router.get(
    "/conversations",
    chatController.getConversations.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations:
   *   post:
   *     summary: Create conversation
   *     description: Creates a new conversation
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *                 description: Optional conversation title
   *     responses:
   *       201:
   *         description: Conversation created
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  router.post(
    "/conversations",
    chatController.createConversation.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations/{conversationId}:
   *   get:
   *     summary: Get conversation
   *     description: Gets a conversation by ID with messages
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the conversation
   *     responses:
   *       200:
   *         description: Conversation with messages
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Conversation not found
   *       500:
   *         description: Server error
   */
  router.get(
    "/conversations/:conversationId",
    chatController.getConversation.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations/{conversationId}:
   *   put:
   *     summary: Update conversation
   *     description: Updates a conversation (e.g., rename)
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the conversation
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *                 description: New conversation title
   *     responses:
   *       200:
   *         description: Conversation updated
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Conversation not found
   *       500:
   *         description: Server error
   */
  router.put(
    "/conversations/:conversationId",
    chatController.updateConversation.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations/{conversationId}:
   *   delete:
   *     summary: Delete conversation
   *     description: Deletes a conversation and all its messages
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the conversation
   *     responses:
   *       204:
   *         description: Conversation deleted
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Conversation not found
   *       500:
   *         description: Server error
   */
  router.delete(
    "/conversations/:conversationId",
    chatController.deleteConversation.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations/{conversationId}/messages:
   *   get:
   *     summary: Get conversation messages
   *     description: Gets all messages for a conversation
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the conversation
   *     responses:
   *       200:
   *         description: List of messages
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Conversation not found
   *       500:
   *         description: Server error
   */
  router.get(
    "/conversations/:conversationId/messages",
    chatController.getMessages.bind(chatController)
  );

  /**
   * @swagger
   * /api/chat/conversations/{conversationId}/messages:
   *   post:
   *     summary: Send message
   *     description: Sends a message in a conversation and gets AI response
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the conversation
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - message
   *             properties:
   *               message:
   *                 type: string
   *                 description: Message content
   *               options:
   *                 type: object
   *                 description: Optional message options (e.g., RAG options)
   *     responses:
   *       200:
   *         description: AI response message
   *       401:
   *         description: Unauthorized
   *       404:
   *         description: Conversation not found
   *       500:
   *         description: Server error
   */
  router.post(
    "/conversations/:conversationId/messages",
    chatController.sendMessage.bind(chatController)
  );

  return router;
};
