/**
 * Tenant Settings Routes
 * Defines API routes for tenant settings and white-labeling with proper security and tenant isolation
 */

import * as express from "express";
import { TenantSettingsController } from "../controllers/TenantSettingsController";
import {
  authenticateJWT,
  validateTenantAccess,
  requirePermissions,
} from "../middleware/auth";

export const tenantSettingsRoutes = (
  tenantSettingsController: TenantSettingsController
) => {
  const router = express.Router();

  // Apply authentication middleware to all routes
  router.use(authenticateJWT);

  // Apply tenant validation middleware to all routes
  router.use(validateTenantAccess);

  // Tenant settings routes with permission checks
  router.get(
    "/:tenantId",
    requirePermissions(["settings:read"]),
    tenantSettingsController.getTenantSettings.bind(tenantSettingsController)
  );
  router.put(
    "/:tenantId",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateTenantSettings.bind(tenantSettingsController)
  );

  // White-labeling routes with permission checks
  router.get(
    "/:tenantId/white-labeling",
    requirePermissions(["settings:read"]),
    tenantSettingsController.getWhiteLabelingSettings.bind(
      tenantSettingsController
    )
  );
  router.put(
    "/:tenantId/white-labeling",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateWhiteLabeling.bind(tenantSettingsController)
  );

  // Branding routes with permission checks
  router.put(
    "/:tenantId/branding",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateBranding.bind(tenantSettingsController)
  );

  // Theme routes with permission checks
  router.put(
    "/:tenantId/theme",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateThemeSettings.bind(tenantSettingsController)
  );

  // Login page routes with permission checks
  router.put(
    "/:tenantId/login-page",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateLoginPageSettings.bind(
      tenantSettingsController
    )
  );

  // Email routes with permission checks
  router.get(
    "/:tenantId/email",
    requirePermissions(["settings:read"]),
    tenantSettingsController.getEmailSettings.bind(tenantSettingsController)
  );
  router.put(
    "/:tenantId/email-configuration",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateEmailConfiguration.bind(
      tenantSettingsController
    )
  );
  router.put(
    "/:tenantId/email-templates",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateEmailTemplates.bind(tenantSettingsController)
  );

  // Custom domain routes with permission checks
  router.put(
    "/:tenantId/custom-domain",
    requirePermissions(["settings:write"]),
    tenantSettingsController.updateCustomDomain.bind(tenantSettingsController)
  );
  router.post(
    "/:tenantId/verify-domain",
    requirePermissions(["settings:write"]),
    tenantSettingsController.verifyCustomDomain.bind(tenantSettingsController)
  );

  return router;
};
