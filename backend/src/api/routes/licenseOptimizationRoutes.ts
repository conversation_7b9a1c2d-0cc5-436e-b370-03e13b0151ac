/**
 * License Optimization Routes
 * Defines API routes for license optimization features
 */

import { Router } from "express";
import { authenticateJWT, validateTenantAccess } from "../middleware/auth";
import * as LicenseOptimizationController from "../controllers/LicenseOptimizationController";

const router = Router();

/**
 * @swagger
 * /api/license-optimization/analyze:
 *   post:
 *     summary: Analyze license usage
 *     description: Analyzes license usage and generates optimization recommendations
 *     tags: [License Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: License optimization result
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Tenant not found
 *       500:
 *         description: Server error
 */
router.post(
  "/analyze",
  authenticateJWT,
  validateTenantAccess,
  LicenseOptimizationController.analyzeLicenseUsage
);

/**
 * @swagger
 * /api/license-optimization/result:
 *   get:
 *     summary: Get optimization result
 *     description: Gets the latest optimization result for a tenant
 *     tags: [License Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: License optimization result
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Optimization result not found
 *       500:
 *         description: Server error
 */
router.get(
  "/result",
  authenticateJWT,
  validateTenantAccess,
  LicenseOptimizationController.getOptimizationResult
);

/**
 * @swagger
 * /api/license-optimization/metadata:
 *   get:
 *     summary: Get optimization metadata
 *     description: Gets optimization types and priorities
 *     tags: [License Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Optimization metadata
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/metadata",
  authenticateJWT,
  LicenseOptimizationController.getOptimizationMetadata
);

/**
 * @swagger
 * /api/license-optimization/license/{licenseId}:
 *   get:
 *     summary: Get license recommendations
 *     description: Gets recommendations for a specific license
 *     tags: [License Optimization]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: licenseId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the license
 *     responses:
 *       200:
 *         description: License recommendations
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No recommendations found
 *       500:
 *         description: Server error
 */
router.get(
  "/license/:licenseId",
  authenticateJWT,
  validateTenantAccess,
  LicenseOptimizationController.getLicenseRecommendations
);

export default router;
