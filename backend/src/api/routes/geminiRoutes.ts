/**
 * Gemini Routes
 * Routes for interacting with Google's Gemini API
 */

import express from "express";
import * as GeminiController from "../controllers/GeminiController";
import { authenticateJWT } from "../middleware/auth";

export const geminiRoutes = () => {
  const router = express.Router();

  /**
   * @swagger
   * /api/gemini/chat:
   *   post:
   *     summary: Process chat with Gemini
   *     description: Processes a chat message using Google's Gemini API
   *     tags: [Gemini]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               message:
   *                 type: string
   *                 description: Chat message text
   *               documentIds:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: Array of document IDs to analyze
   *               taggedContracts:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: Array of contract IDs to include in context
   *     responses:
   *       200:
   *         description: Chat response
   *       400:
   *         description: Invalid request data
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  // Require authentication for contract access
  router.post("/chat", authenticate<PERSON>W<PERSON>, GeminiController.processGeminiChat);

  /**
   * @swagger
   * /api/gemini/upload:
   *   post:
   *     summary: Upload documents for AI processing
   *     description: Uploads documents to be analyzed by Gemini AI
   *     tags: [Gemini]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         multipart/form-data:
   *           schema:
   *             type: object
   *             required:
   *               - files
   *             properties:
   *               files:
   *                 type: array
   *                 items:
   *                   type: string
   *                   format: binary
   *                 description: Documents to upload (PDF, Word, Text, CSV, Excel)
   *     responses:
   *       200:
   *         description: Documents uploaded successfully
   *       400:
   *         description: Invalid request data
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  // For testing purposes, we're not requiring authentication
  router.post("/upload", GeminiController.uploadDocuments);

  /**
   * @swagger
   * /api/gemini/suggestions:
   *   post:
   *     summary: Generate contextual chat suggestions
   *     description: Generates intelligent suggestions based on context and conversation history
   *     tags: [Gemini]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               context:
   *                 type: string
   *                 description: Current page context or URL path
   *               contractId:
   *                 type: string
   *                 description: ID of pinned contract (optional)
   *               conversationHistory:
   *                 type: array
   *                 items:
   *                   type: object
   *                   properties:
   *                     role:
   *                       type: string
   *                       enum: [user, assistant]
   *                     content:
   *                       type: string
   *                 description: Recent conversation messages
   *     responses:
   *       200:
   *         description: Generated suggestions
   *       400:
   *         description: Invalid request data
   *       401:
   *         description: Unauthorized
   *       500:
   *         description: Server error
   */
  router.post(
    "/suggestions",
    authenticateJWT,
    GeminiController.generateSuggestions
  );

  return router;
};
