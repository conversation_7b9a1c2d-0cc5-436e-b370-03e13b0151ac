/**
 * Entitlement Analysis Routes
 * API routes for entitlement analysis functionality
 */

import { Router } from "express";
import { EntitlementAnalysisController } from "../controllers/EntitlementAnalysisController";
import {
  authenticateJWT,
  requirePermissions,
  validateTenantAccess,
  enforceDataIsolation,
} from "../middleware/auth";
import { ContractExtractionRepository } from "../../infrastructure/repositories/ContractExtractionRepository";
import { ContractRepository } from "../../infrastructure/repositories/ContractRepository";
import { ContractAIService } from "../services/ContractAIService";
import { ContractService } from "../services/ContractService";
import { EncryptionService } from "../../infrastructure/services/EncryptionService";
import { ConfigService } from "../../infrastructure/services/ConfigService";
import { PrismaClient } from "@prisma/client";

const router = Router();

// Initialize dependencies
const prisma = new PrismaClient();
const configService = new ConfigService();
const encryptionService = new EncryptionService(configService);
const contractExtractionRepository = new ContractExtractionRepository(prisma);
const contractRepository = new ContractRepository(prisma, encryptionService);
const contractAIService = new ContractAIService(prisma);
const contractService = new ContractService(
  contractRepository,
  contractExtractionRepository,
  encryptionService
);
const entitlementAnalysisController = new EntitlementAnalysisController(
  contractExtractionRepository,
  contractRepository,
  contractAIService,
  contractService
);

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// Apply tenant validation middleware to all routes
router.use(validateTenantAccess);

// Apply data isolation middleware to all routes
router.use(enforceDataIsolation);

// Get all unique providers/suppliers
router.get(
  "/providers",
  requirePermissions(["contracts:read"]),
  entitlementAnalysisController.getProviders.bind(entitlementAnalysisController)
);

// Get contracts by provider
router.get(
  "/contracts/:provider",
  requirePermissions(["contracts:read"]),
  entitlementAnalysisController.getContractsByProvider.bind(
    entitlementAnalysisController
  )
);

// Get entitlement analysis data for selected contracts
router.post(
  "/data",
  requirePermissions(["contracts:read"]),
  entitlementAnalysisController.getAnalysisData.bind(
    entitlementAnalysisController
  )
);

// Export entitlement analysis data
router.post(
  "/export",
  requirePermissions(["contracts:read"]),
  entitlementAnalysisController.exportAnalysisData.bind(
    entitlementAnalysisController
  )
);

// Update analysis field
router.put(
  "/update-field",
  requirePermissions(["contracts:write"]),
  entitlementAnalysisController.updateAnalysisField.bind(
    entitlementAnalysisController
  )
);

// Extract analysis fields for existing contracts
router.post(
  "/extract-fields",
  requirePermissions(["contracts:write"]),
  entitlementAnalysisController.extractAnalysisFields.bind(
    entitlementAnalysisController
  )
);



// Get extraction job status
router.get(
  "/extraction-status/:jobId",
  requirePermissions(["contracts:read"]),
  entitlementAnalysisController.getExtractionStatus.bind(
    entitlementAnalysisController
  )
);

export default router;
