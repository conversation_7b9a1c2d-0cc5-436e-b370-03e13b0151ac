/**
 * Risk Assessment Routes
 * Defines API routes for risk assessment features
 */

import { Router } from "express";
import { authenticateJWT, validateTenantAccess } from "../middleware/auth";
import * as RiskAssessmentController from "../controllers/RiskAssessmentController";

const router = Router();

/**
 * @swagger
 * /api/risk-assessment/assess:
 *   post:
 *     summary: Assess risks in a contract
 *     description: Analyzes a contract and identifies risk factors
 *     tags: [Risk Assessment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contractId
 *             properties:
 *               contractId:
 *                 type: string
 *                 description: ID of the contract
 *               contractText:
 *                 type: string
 *                 description: Contract text (optional, will be fetched if not provided)
 *     responses:
 *       200:
 *         description: Risk assessment result
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contract not found
 *       500:
 *         description: Server error
 */
router.post(
  "/assess",
  authenticateJWT,
  validateTenantAccess,
  RiskAssessmentController.assessRisks
);

/**
 * @swagger
 * /api/risk-assessment/contract/{contractId}:
 *   get:
 *     summary: Get risk assessment
 *     description: Gets a risk assessment for a contract
 *     tags: [Risk Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: contractId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the contract
 *     responses:
 *       200:
 *         description: Risk assessment
 *       400:
 *         description: Invalid contract ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Risk assessment not found
 *       500:
 *         description: Server error
 */
router.get(
  "/contract/:contractId",
  authenticateJWT,
  validateTenantAccess,
  RiskAssessmentController.getRiskAssessment
);

/**
 * @swagger
 * /api/risk-assessment/metadata:
 *   get:
 *     summary: Get risk metadata
 *     description: Gets risk levels and categories
 *     tags: [Risk Assessment]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Risk metadata
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/metadata",
  authenticateJWT,
  RiskAssessmentController.getRiskMetadata
);

/**
 * @swagger
 * /api/risk-assessment/factors/{contractId}:
 *   get:
 *     summary: Get risk factors
 *     description: Gets risk factors for a contract
 *     tags: [Risk Assessment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: contractId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the contract
 *     responses:
 *       200:
 *         description: Risk factors
 *       400:
 *         description: Invalid contract ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Risk assessment not found
 *       500:
 *         description: Server error
 */
router.get(
  "/factors/:contractId",
  authenticateJWT,
  validateTenantAccess,
  RiskAssessmentController.getRiskFactors
);

export default router;
