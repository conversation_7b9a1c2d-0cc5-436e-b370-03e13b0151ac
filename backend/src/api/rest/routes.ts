import * as express from "express";
import { validate } from "../../infrastructure/middleware/validate";
import { logger } from "../../infrastructure/logging/logger";

// Create router
export const apiRouter = express.Router();

// Log all API requests
apiRouter.use((req, res, next) => {
  logger.info(
    {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.headers["user-agent"],
    },
    `API Request: ${req.method} ${req.originalUrl}`
  );
  next();
});

// Define routes
apiRouter.get("/", (req, res) => {
  res.json({
    name: "Contract Lifecycle Management API",
    version: "0.1.0",
    status: "online",
    timestamp: new Date().toISOString(),
  });
});

// Contract Management routes are implemented in contractRoutes.ts

// User Management routes are implemented in userRoutes.ts

// Tenant Management routes are implemented in tenantRoutes.ts

// AI Processing routes are implemented in aiRoutes.ts

// Audit and Compliance routes are implemented in auditRoutes.ts
