/**
 * Default Widget Definitions
 * Defines the default widgets that are created for new users
 */

export interface DefaultWidget {
  name: string;
  description: string;
  category: 'priority' | 'portfolio';
  widgetType: string;
  isVisible: boolean;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  configuration: {
    refreshInterval: number;
    showHeader: boolean;
    showDescription: boolean;
    [key: string]: any;
  };
  order: number;
}

export const DEFAULT_WIDGETS: DefaultWidget[] = [
  // Priority Category Widgets (3x2 grid)
  {
    name: 'Expiring Contracts',
    description: 'Contracts expiring in the next 30-90 days',
    category: 'priority',
    widgetType: 'expiring-contracts',
    isVisible: true,
    position: { x: 0, y: 0, w: 4, h: 3 },
    configuration: {
      refreshInterval: 300,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 1
  },
  {
    name: 'Auto Renewals',
    description: 'Contracts with automatic renewal clauses',
    category: 'priority',
    widgetType: 'auto-renewals',
    isVisible: true,
    position: { x: 4, y: 0, w: 4, h: 3 },
    configuration: {
      refreshInterval: 300,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 2
  },
  {
    name: 'Auto Renewals by Classification',
    description: 'Auto renewals broken down by contract classification',
    category: 'priority',
    widgetType: 'auto-renewals-classification',
    isVisible: true,
    position: { x: 8, y: 0, w: 4, h: 3 },
    configuration: {
      refreshInterval: 300,
      showHeader: true,
      showDescription: true,
      chartType: 'pie',
      showLegend: false
    },
    order: 3
  },
  {
    name: 'Realised Savings',
    description: 'Savings already achieved through contract optimization',
    category: 'priority',
    widgetType: 'realised-savings',
    isVisible: true,
    position: { x: 0, y: 3, w: 4, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      format: 'currency',
      showTrend: true
    },
    order: 4
  },
  {
    name: 'Potential Savings',
    description: 'Potential savings opportunities identified',
    category: 'priority',
    widgetType: 'potential-savings',
    isVisible: true,
    position: { x: 4, y: 3, w: 4, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      format: 'currency',
      showTrend: true
    },
    order: 5
  },
  {
    name: 'Key Obligations',
    description: 'Important contractual obligations requiring attention',
    category: 'priority',
    widgetType: 'key-obligations',
    isVisible: true,
    position: { x: 8, y: 3, w: 4, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      pageSize: 10
    },
    order: 6
  },

  // Portfolio Category Widgets (4x2 grid)
  {
    name: 'Total Agreements',
    description: 'Overview of all agreements in the portfolio',
    category: 'portfolio',
    widgetType: 'total-agreements',
    isVisible: true,
    position: { x: 0, y: 0, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'pie',
      showLegend: false
    },
    order: 1
  },
  {
    name: 'High Value Agreements',
    description: 'Agreements categorized by contract value',
    category: 'portfolio',
    widgetType: 'high-value-agreements',
    isVisible: true,
    position: { x: 3, y: 0, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 2
  },
  {
    name: 'Critical Agreements',
    description: 'Agreements categorized by business criticality',
    category: 'portfolio',
    widgetType: 'critical-agreements',
    isVisible: true,
    position: { x: 6, y: 0, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 3
  },
  {
    name: 'Expired Agreements',
    description: 'Agreements that have already expired',
    category: 'portfolio',
    widgetType: 'expired-agreements',
    isVisible: true,
    position: { x: 9, y: 0, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      format: 'number',
      showTrend: true
    },
    order: 4
  },
  {
    name: 'Aging Contracts',
    description: 'Contracts categorized by age since execution',
    category: 'portfolio',
    widgetType: 'aging-contracts',
    isVisible: true,
    position: { x: 0, y: 3, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 5
  },
  {
    name: 'Customer Supplier Paper',
    description: 'Distribution of customer vs supplier agreements',
    category: 'portfolio',
    widgetType: 'customer-supplier-paper',
    isVisible: true,
    position: { x: 3, y: 3, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'pie',
      showLegend: false
    },
    order: 6
  },
  {
    name: 'Agreement Types',
    description: 'Distribution of different agreement types',
    category: 'portfolio',
    widgetType: 'agreement-types',
    isVisible: true,
    position: { x: 6, y: 3, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 7
  },
  {
    name: 'Service Types',
    description: 'Distribution of different service types',
    category: 'portfolio',
    widgetType: 'service-types',
    isVisible: true,
    position: { x: 9, y: 3, w: 3, h: 3 },
    configuration: {
      refreshInterval: 600,
      showHeader: true,
      showDescription: true,
      chartType: 'bar',
      showLegend: true
    },
    order: 8
  }
];

/**
 * Widget type to component mapping
 */
export const WIDGET_TYPE_MAPPING = {
  'expiring-contracts': 'ExpiringContractsWidget',
  'auto-renewals': 'AutoRenewalsWidget',
  'auto-renewals-classification': 'AutoRenewalsByClassificationWidget',
  'realised-savings': 'RealisedSavingsWidget',
  'potential-savings': 'PotentialSavingsWidget',
  'key-obligations': 'KeyObligationsWidget',
  'total-agreements': 'TotalAgreementsWidget',
  'high-value-agreements': 'HighValueAgreementsWidget',
  'critical-agreements': 'CriticalAgreementsWidget',
  'expired-agreements': 'ExpiredAgreementsWidget',
  'aging-contracts': 'AgingContractsWidget',
  'customer-supplier-paper': 'CustomerSupplierPaperWidget',
  'agreement-types': 'PortfolioAgreementTypeWidget',
  'service-types': 'ServiceTypeWidget'
} as const;

export type WidgetType = keyof typeof WIDGET_TYPE_MAPPING;
