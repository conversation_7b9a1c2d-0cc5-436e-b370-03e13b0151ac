import pino from 'pino';

// Configure logging
const logLevel = process.env.LOG_LEVEL || 'info';

export const logger = pino({
  level: logLevel,
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
    },
  },
});

// Export a specialized logger for HTTP request/response logging
export const httpLogger = {
  info: (message: string, data?: any) => {
    logger.info({ data }, message);
  },
  error: (message: string, error?: Error, data?: any) => {
    logger.error({ error: error instanceof Error ? { message: error.message, stack: error.stack } : error, data }, message);
  },
  warn: (message: string, data?: any) => {
    logger.warn({ data }, message);
  },
  debug: (message: string, data?: any) => {
    logger.debug({ data }, message);
  },
};