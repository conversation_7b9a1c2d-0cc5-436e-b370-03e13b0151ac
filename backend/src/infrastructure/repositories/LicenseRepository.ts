/**
 * License Repository Implementation
 * Implements the ILicenseRepository interface using Prisma ORM
 * and follows the Repository Pattern with proper tenant isolation
 */

import {
  PrismaClient,
  LicenseType,
  LicenseStatus,
  DocumentFormat,
  LicenseDocumentType,
} from "@prisma/client";
import { License } from "../../domain/licenses/License";
import { LicenseEntitlement } from "../../domain/licenses/LicenseEntitlement";
import { LicenseDocument } from "../../domain/licenses/LicenseDocument";
import { LicenseUsage } from "../../domain/licenses/LicenseUsage";
import {
  ILicenseRepository,
  LicenseSearchParams,
} from "../../domain/licenses/interfaces/ILicenseRepository";
import { EncryptionService } from "../services/EncryptionService";
import { BaseRepository } from "./BaseRepository";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../logging/logger";

/**
 * LicenseRepository implements the ILicenseRepository interface
 * using Prisma ORM to interact with the database with tenant isolation
 */
export class LicenseRepository
  extends BaseRepository<License>
  implements ILicenseRepository
{
  private encryptionService: EncryptionService;

  constructor(prisma: PrismaClient, encryptionService: EncryptionService) {
    super(prisma, "License");
    this.encryptionService = encryptionService;
  }

  /**
   * Creates a new license
   * @param license License entity to create
   * @returns Created license with generated ID
   */
  async create(license: License): Promise<License> {
    try {
      const data = license.toDTO();

      // Create the license in the database
      const createdLicense = await this.prisma.license.create({
        data: {
          id: data.id,
          name: data.name,
          description: data.description,
          licenseNumber: data.licenseNumber,
          licenseType: data.licenseType as LicenseType,
          status: data.status as LicenseStatus,
          vendor: data.vendor,
          purchaseDate: data.purchaseDate,
          startDate: data.startDate,
          endDate: data.endDate,
          renewalType: data.renewalType,
          renewalDate: data.renewalDate,
          isAutoRenew: data.isAutoRenew,
          noticePeriodDays: data.noticePeriodDays,
          totalValue: data.totalValue,
          currency: data.currency,
          costPeriod: data.costPeriod,
          totalLicenses: data.totalLicenses,
          assignedLicenses: data.assignedLicenses,
          availableLicenses: data.availableLicenses,
          complianceStatus: data.complianceStatus,
          lastComplianceCheck: data.lastComplianceCheck,
          tenantId: data.tenantId,
          createdById: data.createdById, // Add createdById field
        },
      });

      return License.fromPersistence(createdLicense);
    } catch (error) {
      logger.error("Error creating license:", { error });
      throw new Error(`Failed to create license: ${(error as Error).message}`);
    }
  }

  /**
   * Finds a license by ID with tenant isolation
   * @param id License ID
   * @returns License entity or null if not found
   */
  async findById(id: string): Promise<License | null> {
    try {
      this.logOperation("findById", { id });

      // Get current tenant ID
      const tenantId = this.getCurrentTenantId();

      // Find license with tenant filter
      const license = await this.prisma.license.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!license) {
        return null;
      }

      return License.fromPersistence(license);
    } catch (error) {
      logger.error("Error finding license by ID:", { error, id });
      throw new Error(`Failed to find license: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an existing license
   * @param license License entity to update
   * @returns Updated license
   */
  async update(license: License): Promise<License> {
    try {
      const data = license.toDTO();

      // Update the license in the database
      const updatedLicense = await this.prisma.license.update({
        where: { id: data.id },
        data: {
          name: data.name,
          description: data.description,
          licenseNumber: data.licenseNumber,
          licenseType: data.licenseType as LicenseType,
          status: data.status as LicenseStatus,
          vendor: data.vendor,
          purchaseDate: data.purchaseDate,
          startDate: data.startDate,
          endDate: data.endDate,
          renewalType: data.renewalType,
          renewalDate: data.renewalDate,
          isAutoRenew: data.isAutoRenew,
          noticePeriodDays: data.noticePeriodDays,
          totalValue: data.totalValue,
          currency: data.currency,
          costPeriod: data.costPeriod,
          totalLicenses: data.totalLicenses,
          assignedLicenses: data.assignedLicenses,
          availableLicenses: data.availableLicenses,
          complianceStatus: data.complianceStatus,
          lastComplianceCheck: data.lastComplianceCheck,
          updatedAt: new Date(),
        },
      });

      return License.fromPersistence(updatedLicense);
    } catch (error) {
      logger.error("Error updating license:", { error });
      throw new Error(`Failed to update license: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes a license by ID
   * @param id License ID
   * @returns true if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    try {
      // First, delete related entities
      await this.prisma.licenseEntitlement.deleteMany({
        where: { licenseId: id },
      });

      await this.prisma.licenseDocument.deleteMany({
        where: { licenseId: id },
      });

      await this.prisma.licenseUsage.deleteMany({
        where: { licenseId: id },
      });

      // Then delete the license
      const result = await this.prisma.license.delete({
        where: { id },
      });

      return !!result;
    } catch (error) {
      // If the license doesn't exist, return false
      if ((error as any).code === "P2025") {
        return false;
      }

      logger.error("Error deleting license:", { error });
      throw new Error(`Failed to delete license: ${(error as Error).message}`);
    }
  }

  /**
   * Searches for licenses based on various parameters with tenant isolation
   * @param params Search parameters
   * @returns Array of licenses matching the search criteria and total count
   */
  async search(
    params: LicenseSearchParams
  ): Promise<{ licenses: License[]; total: number }> {
    try {
      this.logOperation("search", { params });

      const {
        name,
        vendor,
        licenseType,
        status,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        page = 1,
        limit = 20,
      } = params;

      // Get current tenant ID from context instead of using the one from params
      const tenantId = this.getCurrentTenantId();

      // Build the where clause based on the provided parameters
      const where: any = { tenantId };

      if (name) {
        where.name = { contains: name, mode: "insensitive" };
      }

      if (vendor) {
        where.vendor = { contains: vendor, mode: "insensitive" };
      }

      if (licenseType) {
        where.licenseType = licenseType;
      }

      if (status) {
        where.status = status;
      }

      if (startDateFrom || startDateTo) {
        where.startDate = {};

        if (startDateFrom) {
          where.startDate.gte = startDateFrom;
        }

        if (startDateTo) {
          where.startDate.lte = startDateTo;
        }
      }

      if (endDateFrom || endDateTo) {
        where.endDate = {};

        if (endDateFrom) {
          where.endDate.gte = endDateFrom;
        }

        if (endDateTo) {
          where.endDate.lte = endDateTo;
        }
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get total count
      const total = await this.prisma.license.count({ where });

      // Get licenses
      const licenses = await this.prisma.license.findMany({
        where,
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" },
      });

      return {
        licenses: licenses.map((license) => License.fromPersistence(license)),
        total,
      };
    } catch (error) {
      logger.error("Error searching licenses:", { error });
      throw new Error(`Failed to search licenses: ${(error as Error).message}`);
    }
  }

  /**
   * Gets all licenses for a tenant
   * @param tenantId Tenant ID
   * @param page Page number (optional, default: 1)
   * @param limit Items per page (optional, default: 20)
   * @returns Array of licenses and total count
   */
  async getByTenant(
    tenantId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ licenses: License[]; total: number }> {
    try {
      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get total count
      const total = await this.prisma.license.count({
        where: { tenantId },
      });

      // Get licenses
      const licenses = await this.prisma.license.findMany({
        where: { tenantId },
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" },
      });

      return {
        licenses: licenses.map((license) => License.fromPersistence(license)),
        total,
      };
    } catch (error) {
      logger.error("Error getting licenses by tenant:", { error });
      throw new Error(
        `Failed to get licenses by tenant: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets licenses expiring within a specified period
   * @param tenantId Tenant ID
   * @param days Number of days from now
   * @returns Array of expiring licenses
   */
  async getExpiring(tenantId: string, days: number): Promise<License[]> {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const licenses = await this.prisma.license.findMany({
        where: {
          tenantId,
          endDate: {
            gte: today,
            lte: futureDate,
          },
          status: {
            not: LicenseStatus.EXPIRED,
          },
        },
        orderBy: { endDate: "asc" },
      });

      return licenses.map((license) => License.fromPersistence(license));
    } catch (error) {
      logger.error("Error getting expiring licenses:", { error });
      throw new Error(
        `Failed to get expiring licenses: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets licenses with compliance issues
   * @param tenantId Tenant ID
   * @returns Array of licenses with compliance issues
   */
  async getWithComplianceIssues(tenantId: string): Promise<License[]> {
    try {
      const licenses = await this.prisma.license.findMany({
        where: {
          tenantId,
          OR: [
            { status: LicenseStatus.COMPLIANCE_ISSUE },
            { complianceStatus: { not: "Compliant" } },
          ],
        },
        orderBy: { updatedAt: "desc" },
      });

      return licenses.map((license) => License.fromPersistence(license));
    } catch (error) {
      logger.error("Error getting licenses with compliance issues:", { error });
      throw new Error(
        `Failed to get licenses with compliance issues: ${
          (error as Error).message
        }`
      );
    }
  }

  /**
   * Gets licenses with utilization issues (over or under-utilized)
   * @param tenantId Tenant ID
   * @param threshold Utilization threshold percentage (default: 70%)
   * @returns Array of licenses with utilization issues
   */
  async getWithUtilizationIssues(
    tenantId: string,
    threshold: number = 70
  ): Promise<License[]> {
    try {
      // Get all licenses for the tenant
      const licenses = await this.prisma.license.findMany({
        where: { tenantId },
      });

      // Filter licenses with utilization issues
      const licensesWithIssues = licenses.filter((license) => {
        // Skip licenses with 0 total licenses
        if (license.totalLicenses === 0) return false;

        // Calculate utilization percentage
        const utilizationPercentage =
          (license.assignedLicenses / license.totalLicenses) * 100;

        // Check if over-utilized or under-utilized
        return utilizationPercentage > 100 || utilizationPercentage < threshold;
      });

      return licensesWithIssues.map((license) =>
        License.fromPersistence(license)
      );
    } catch (error) {
      logger.error("Error getting licenses with utilization issues:", {
        error,
      });
      throw new Error(
        `Failed to get licenses with utilization issues: ${
          (error as Error).message
        }`
      );
    }
  }

  // License Entitlement methods

  /**
   * Creates a new license entitlement
   * @param entitlement License entitlement to create
   * @returns Created entitlement
   */
  async createEntitlement(
    entitlement: LicenseEntitlement
  ): Promise<LicenseEntitlement> {
    try {
      const data = entitlement.toDTO();

      const createdEntitlement = await this.prisma.licenseEntitlement.create({
        data: {
          id: data.id,
          name: data.name,
          description: data.description,
          included: data.included,
          quantity: data.quantity,
          licenseId: data.licenseId,
        },
      });

      return LicenseEntitlement.fromPersistence(createdEntitlement);
    } catch (error) {
      logger.error("Error creating license entitlement:", { error });
      throw new Error(
        `Failed to create license entitlement: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all entitlements for a license
   * @param licenseId License ID
   * @returns Array of entitlements
   */
  async getEntitlements(licenseId: string): Promise<LicenseEntitlement[]> {
    try {
      const entitlements = await this.prisma.licenseEntitlement.findMany({
        where: { licenseId },
      });

      return entitlements.map((entitlement) =>
        LicenseEntitlement.fromPersistence(entitlement)
      );
    } catch (error) {
      logger.error("Error getting license entitlements:", { error });
      throw new Error(
        `Failed to get license entitlements: ${(error as Error).message}`
      );
    }
  }

  /**
   * Finds an entitlement by ID
   * @param id Entitlement ID
   * @returns Entitlement entity or null if not found
   */
  async findEntitlementById(id: string): Promise<LicenseEntitlement | null> {
    try {
      const entitlement = await this.prisma.licenseEntitlement.findUnique({
        where: { id },
      });

      if (!entitlement) {
        return null;
      }

      return LicenseEntitlement.fromPersistence(entitlement);
    } catch (error) {
      logger.error("Error finding entitlement by ID:", { error });
      throw new Error(
        `Failed to find entitlement: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates a license entitlement
   * @param entitlement License entitlement to update
   * @returns Updated entitlement
   */
  async updateEntitlement(
    entitlement: LicenseEntitlement
  ): Promise<LicenseEntitlement> {
    try {
      const data = entitlement.toDTO();

      const updatedEntitlement = await this.prisma.licenseEntitlement.update({
        where: { id: data.id },
        data: {
          name: data.name,
          description: data.description,
          included: data.included,
          quantity: data.quantity,
          updatedAt: new Date(),
        },
      });

      return LicenseEntitlement.fromPersistence(updatedEntitlement);
    } catch (error) {
      logger.error("Error updating license entitlement:", { error });
      throw new Error(
        `Failed to update license entitlement: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes a license entitlement
   * @param id Entitlement ID
   * @returns true if deleted, false if not found
   */
  async deleteEntitlement(id: string): Promise<boolean> {
    try {
      const result = await this.prisma.licenseEntitlement.delete({
        where: { id },
      });

      return !!result;
    } catch (error) {
      // If the entitlement doesn't exist, return false
      if ((error as any).code === "P2025") {
        return false;
      }

      logger.error("Error deleting license entitlement:", { error });
      throw new Error(
        `Failed to delete license entitlement: ${(error as Error).message}`
      );
    }
  }

  // License Document methods

  /**
   * Creates a new license document
   * @param document License document to create
   * @returns Created document
   */
  async createDocument(document: LicenseDocument): Promise<LicenseDocument> {
    try {
      const data = document.toDTO();

      // If the document is encrypted, ensure there's an encryption key ID
      if (data.isEncrypted && !data.encryptionKeyId) {
        // Generate a new encryption key ID
        data.encryptionKeyId = `key-${Date.now()}`;
      }

      const createdDocument = await this.prisma.licenseDocument.create({
        data: {
          id: data.id,
          name: data.name,
          documentType: data.documentType as LicenseDocumentType,
          documentFormat: data.documentFormat as DocumentFormat,
          documentUri: data.documentUri,
          documentHash: data.documentHash,
          isEncrypted: data.isEncrypted,
          encryptionKeyId: data.encryptionKeyId,
          uploadDate: data.uploadDate,
          licenseId: data.licenseId,
        },
      });

      return LicenseDocument.fromPersistence(createdDocument);
    } catch (error) {
      logger.error("Error creating license document:", { error });
      throw new Error(
        `Failed to create license document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all documents for a license
   * @param licenseId License ID
   * @returns Array of documents
   */
  async getDocuments(licenseId: string): Promise<LicenseDocument[]> {
    try {
      const documents = await this.prisma.licenseDocument.findMany({
        where: { licenseId },
      });

      return documents.map((document) =>
        LicenseDocument.fromPersistence(document)
      );
    } catch (error) {
      logger.error("Error getting license documents:", { error });
      throw new Error(
        `Failed to get license documents: ${(error as Error).message}`
      );
    }
  }

  /**
   * Finds a document by ID
   * @param id Document ID
   * @returns Document entity or null if not found
   */
  async findDocumentById(id: string): Promise<LicenseDocument | null> {
    try {
      const document = await this.prisma.licenseDocument.findUnique({
        where: { id },
      });

      if (!document) {
        return null;
      }

      return LicenseDocument.fromPersistence(document);
    } catch (error) {
      logger.error("Error finding document by ID:", { error });
      throw new Error(`Failed to find document: ${(error as Error).message}`);
    }
  }

  /**
   * Updates a license document
   * @param document License document to update
   * @returns Updated document
   */
  async updateDocument(document: LicenseDocument): Promise<LicenseDocument> {
    try {
      const data = document.toDTO();

      const updatedDocument = await this.prisma.licenseDocument.update({
        where: { id: data.id },
        data: {
          name: data.name,
          documentType: data.documentType as LicenseDocumentType,
          documentFormat: data.documentFormat as DocumentFormat,
          isEncrypted: data.isEncrypted,
          encryptionKeyId: data.encryptionKeyId,
          updatedAt: new Date(),
        },
      });

      return LicenseDocument.fromPersistence(updatedDocument);
    } catch (error) {
      logger.error("Error updating license document:", { error });
      throw new Error(
        `Failed to update license document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes a license document
   * @param id Document ID
   * @returns true if deleted, false if not found
   */
  async deleteDocument(id: string): Promise<boolean> {
    try {
      const result = await this.prisma.licenseDocument.delete({
        where: { id },
      });

      return !!result;
    } catch (error) {
      // If the document doesn't exist, return false
      if ((error as any).code === "P2025") {
        return false;
      }

      logger.error("Error deleting license document:", { error });
      throw new Error(
        `Failed to delete license document: ${(error as Error).message}`
      );
    }
  }

  // License Usage methods

  /**
   * Creates a new license usage record
   * @param usage License usage to create
   * @returns Created usage record
   */
  async createUsage(usage: LicenseUsage): Promise<LicenseUsage> {
    try {
      const data = usage.toDTO();

      const createdUsage = await this.prisma.licenseUsage.create({
        data: {
          id: data.id,
          date: data.date,
          usageCount: data.usageCount,
          totalAvailable: data.totalAvailable,
          utilizationPercentage: data.utilizationPercentage,
          licenseId: data.licenseId,
        },
      });

      return LicenseUsage.fromPersistence(createdUsage);
    } catch (error) {
      logger.error("Error creating license usage record:", { error });
      throw new Error(
        `Failed to create license usage record: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets usage history for a license
   * @param licenseId License ID
   * @param startDate Start date for usage history
   * @param endDate End date for usage history
   * @returns Array of usage records
   */
  async getUsageHistory(
    licenseId: string,
    startDate: Date,
    endDate: Date
  ): Promise<LicenseUsage[]> {
    try {
      const usageRecords = await this.prisma.licenseUsage.findMany({
        where: {
          licenseId,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: { date: "asc" },
      });

      return usageRecords.map((usage) => LicenseUsage.fromPersistence(usage));
    } catch (error) {
      logger.error("Error getting license usage history:", { error });
      throw new Error(
        `Failed to get license usage history: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets the latest usage record for a license
   * @param licenseId License ID
   * @returns Latest usage record or null if none exists
   */
  async getLatestUsage(licenseId: string): Promise<LicenseUsage | null> {
    try {
      const latestUsage = await this.prisma.licenseUsage.findFirst({
        where: { licenseId },
        orderBy: { date: "desc" },
      });

      if (!latestUsage) {
        return null;
      }

      return LicenseUsage.fromPersistence(latestUsage);
    } catch (error) {
      logger.error("Error getting latest license usage:", { error });
      throw new Error(
        `Failed to get latest license usage: ${(error as Error).message}`
      );
    }
  }
}
