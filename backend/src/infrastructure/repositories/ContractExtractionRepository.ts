/**
 * Contract Extraction Repository Implementation
 * Implements the IContractExtractionRepository interface using Prisma ORM
 */

import { PrismaClient } from "@prisma/client";
import { ContractExtraction } from "../../domain/contracts/ContractExtraction";
import {
  IContractExtractionRepository,
  CreateContractExtractionParams,
  UpdateContractExtractionParams,
} from "../../domain/contracts/interfaces/IContractExtractionRepository";
import { v4 as uuidv4 } from "uuid";

export class ContractExtractionRepository
  implements IContractExtractionRepository {
  constructor(private readonly prisma: PrismaClient) { }

  async create(
    params: CreateContractExtractionParams
  ): Promise<ContractExtraction> {
    try {
      // Calculate overall confidence
      const extraction = new ContractExtraction(
        uuidv4(),
        params.contractId,
        params.tenantId,
        params.fixedFields,
        params.dynamicFields,
        params.specialFields,
        new Date(),
        params.extractionVersion,
        params.documentSummary,
        params.complianceAnalysis,
        undefined, // integrityAnalysis - will be set separately
        params.analysisFields,
        undefined, // overallConfidence - will be calculated
        params.processingTimeMs,
        params.modelUsed,
        params.folderId
      );

      const overallConfidence = extraction.calculateOverallConfidence();

      const created = await this.prisma.contractExtraction.create({
        data: {
          id: extraction.id,
          contractId: params.contractId,
          tenantId: params.tenantId,
          fixedFields: params.fixedFields as any,
          dynamicFields: params.dynamicFields as any,
          specialFields: params.specialFields as any,
          documentSummary: params.documentSummary as any,
          complianceAnalysis: params.complianceAnalysis as any,
          integrityAnalysis: null as any, // Will be set later when integrity analysis is generated
          analysisFields: params.analysisFields as any,
          extractionVersion: params.extractionVersion,
          overallConfidence,
          processingTimeMs: params.processingTimeMs,
          modelUsed: params.modelUsed,
          folderId: params.folderId,
          reportingTo: null, // Will be set later via auto-population or manual assignment
        },
      });

      return ContractExtraction.fromPersistence(created);
    } catch (error) {
      console.error("Error creating contract extraction:", error);
      throw new Error(
        `Failed to create contract extraction: ${(error as Error).message}`
      );
    }
  }

  async getByContractId(
    contractId: string,
    tenantId: string
  ): Promise<ContractExtraction | null> {
    try {
      const extraction = await this.prisma.contractExtraction.findFirst({
        where: {
          contractId,
          tenantId,
        },
      });

      return extraction ? ContractExtraction.fromPersistence(extraction) : null;
    } catch (error) {
      console.error("Error getting contract extraction by contract ID:", error);
      throw new Error(
        `Failed to get contract extraction: ${(error as Error).message}`
      );
    }
  }

  async getById(
    id: string,
    tenantId: string
  ): Promise<ContractExtraction | null> {
    try {
      const extraction = await this.prisma.contractExtraction.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      return extraction ? ContractExtraction.fromPersistence(extraction) : null;
    } catch (error) {
      console.error("Error getting contract extraction by ID:", error);
      throw new Error(
        `Failed to get contract extraction: ${(error as Error).message}`
      );
    }
  }

  async update(
    id: string,
    tenantId: string,
    params: UpdateContractExtractionParams
  ): Promise<ContractExtraction> {
    try {
      // Get existing extraction to calculate new confidence if fields are updated
      const existing = await this.getById(id, tenantId);
      if (!existing) {
        throw new Error("Contract extraction not found");
      }

      let overallConfidence = existing.overallConfidence;

      // If fields are being updated, recalculate confidence
      if (params.fixedFields || params.dynamicFields || params.specialFields) {
        const updatedExtraction = new ContractExtraction(
          existing.id,
          existing.contractId,
          existing.tenantId,
          params.fixedFields || existing.fixedFields,
          params.dynamicFields || existing.dynamicFields,
          params.specialFields || existing.specialFields,
          existing.extractionDate,
          params.extractionVersion || existing.extractionVersion,
          params.documentSummary || existing.documentSummary,
          params.complianceAnalysis || existing.complianceAnalysis,
          params.integrityAnalysis || existing.integrityAnalysis,
          params.analysisFields || existing.analysisFields,
          undefined,
          params.processingTimeMs || existing.processingTimeMs,
          params.modelUsed || existing.modelUsed
        );
        overallConfidence = updatedExtraction.calculateOverallConfidence();
      }

      const updated = await this.prisma.contractExtraction.update({
        where: {
          id,
          tenantId,
        },
        data: {
          ...(params.fixedFields && { fixedFields: params.fixedFields as any }),
          ...(params.dynamicFields && {
            dynamicFields: params.dynamicFields as any,
          }),
          ...(params.specialFields && {
            specialFields: params.specialFields as any,
          }),
          ...(params.documentSummary && {
            documentSummary: params.documentSummary as any,
          }),
          ...(params.complianceAnalysis && {
            complianceAnalysis: params.complianceAnalysis as any,
          }),
          ...(params.integrityAnalysis && {
            integrityAnalysis: params.integrityAnalysis as any,
          }),
          ...(params.analysisFields && {
            analysisFields: params.analysisFields as any,
          }),
          ...(params.extractionVersion && {
            extractionVersion: params.extractionVersion,
          }),
          ...(params.processingTimeMs && {
            processingTimeMs: params.processingTimeMs,
          }),
          ...(params.modelUsed && { modelUsed: params.modelUsed }),
          overallConfidence,
        },
      });

      return ContractExtraction.fromPersistence(updated);
    } catch (error) {
      console.error("Error updating contract extraction:", error);
      throw new Error(
        `Failed to update contract extraction: ${(error as Error).message}`
      );
    }
  }

  async delete(id: string, tenantId: string): Promise<void> {
    try {
      await this.prisma.contractExtraction.delete({
        where: {
          id,
          tenantId,
        },
      });
    } catch (error) {
      console.error("Error deleting contract extraction:", error);
      throw new Error(
        `Failed to delete contract extraction: ${(error as Error).message}`
      );
    }
  }

  async getByTenant(
    tenantId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ extractions: ContractExtraction[]; total: number }> {
    try {
      const skip = (page - 1) * limit;

      const [extractions, total] = await Promise.all([
        this.prisma.contractExtraction.findMany({
          where: { tenantId },
          skip,
          take: limit,
          orderBy: { extractionDate: "desc" },
        }),
        this.prisma.contractExtraction.count({ where: { tenantId } }),
      ]);

      return {
        extractions: extractions.map(ContractExtraction.fromPersistence),
        total,
      };
    } catch (error) {
      console.error("Error getting contract extractions by tenant:", error);
      throw new Error(
        `Failed to get contract extractions: ${(error as Error).message}`
      );
    }
  }

  async getByTenantId(tenantId: string): Promise<ContractExtraction[]> {
    try {
      const extractions = await this.prisma.contractExtraction.findMany({
        where: { tenantId },
        orderBy: { extractionDate: "desc" },
      });

      return extractions.map(ContractExtraction.fromPersistence);
    } catch (error) {
      console.error(
        "Error getting all contract extractions for tenant:",
        error
      );
      throw new Error(
        `Failed to get contract extractions: ${(error as Error).message}`
      );
    }
  }

  async getByTenantWithFilters(
    tenantId: string,
    filters: {
      title?: string;
      agreementType?: string;
      status?: string;
      provider?: string;
      securityClassification?: string;
      startDateFrom?: string;
      startDateTo?: string;
      endDateFrom?: string;
      endDateTo?: string;
      renewalMonths?: string; // "3", "6", "12" for contracts renewing in next X months
      page?: number;
      limit?: number;
    }
  ): Promise<{ extractions: ContractExtraction[]; total: number }> {
    try {
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const skip = (page - 1) * limit;

      // Build the where clause based on the provided parameters
      const where: any = {
        tenantId,
      };

      // Note: Folder filtering removed - folders are now calculated at runtime

      // Filter by title (search in multiple fields: original_filename, provider, client)
      // Make search case-insensitive
      if (filters.title) {
        const searchTerm = filters.title.toLowerCase();
        where.OR = [
          {
            fixedFields: {
              path: ["original_filename", "value"],
              string_contains: searchTerm,
              mode: "insensitive",
            },
          },
          {
            fixedFields: {
              path: ["provider", "value"],
              string_contains: searchTerm,
              mode: "insensitive",
            },
          },
          {
            fixedFields: {
              path: ["client", "value"],
              string_contains: searchTerm,
              mode: "insensitive",
            },
          },
        ];
      }

      // Filter by contract status (exact match in fixedFields.contract_status)
      if (filters.status) {
        const statusFilter = {
          fixedFields: {
            path: ["contract_status", "value"],
            equals: filters.status,
          },
        };

        if (where.AND) {
          where.AND.push(statusFilter);
        } else if (where.OR) {
          where.AND = [{ OR: where.OR }, statusFilter];
          delete where.OR;
        } else {
          where.fixedFields = {
            ...where.fixedFields,
            path: ["contract_status", "value"],
            string_contains: filters.status,
            mode: "insensitive",
          };
        }
      }

      // Filter by agreement type (search in fixedFields.agreement_type)
      if (filters.agreementType) {
        const agreementTypeFilter = {
          fixedFields: {
            path: ["agreement_type", "value"],
            string_contains: filters.agreementType,
            mode: "insensitive",
          },
        };

        if (where.AND) {
          where.AND.push(agreementTypeFilter);
        } else if (where.OR) {
          where.AND = [{ OR: where.OR }, agreementTypeFilter];
          delete where.OR;
        } else {
          where.fixedFields = {
            ...where.fixedFields,
            path: ["agreement_type", "value"],
            string_contains: filters.agreementType,
            mode: "insensitive",
          };
        }
      }

      // Filter by provider (search in fixedFields.provider)
      if (filters.provider) {
        const providerFilter = {
          fixedFields: {
            path: ["provider", "value"],
            string_contains: filters.provider,
            mode: "insensitive",
          },
        };

        if (where.AND) {
          // If we already have AND conditions, add to them
          where.AND.push(providerFilter);
        } else if (where.OR) {
          // If we have OR conditions, combine them
          where.AND = [{ OR: where.OR }, providerFilter];
          delete where.OR;
        } else {
          // Simple case - just add the provider filter
          where.fixedFields = {
            ...where.fixedFields,
            path: ["provider", "value"],
            string_contains: filters.provider,
            mode: "insensitive",
          };
        }
      }

      // Filter by date ranges (search in fixedFields.start_date and end_date)
      if (filters.startDateFrom || filters.startDateTo) {
        // This is complex with JSON fields, we'll handle it in the application layer
        // For now, we'll get all records and filter in memory
      }

      // Filter by renewal dates (contracts renewing in next X months)
      if (filters.renewalMonths) {
        // This will be handled in the application layer after fetching
        // since we need to calculate renewal dates based on end_date
      }

      let [extractions, total] = await Promise.all([
        this.prisma.contractExtraction.findMany({
          where,
          skip,
          take: limit,
          orderBy: { extractionDate: "desc" },
        }),
        this.prisma.contractExtraction.count({ where }),
      ]);

      // Apply renewal filtering in application layer
      if (filters.renewalMonths) {
        const renewalMonthsNum = parseInt(filters.renewalMonths, 10);
        const now = new Date();
        const futureDate = new Date(now);
        futureDate.setMonth(futureDate.getMonth() + renewalMonthsNum);

        extractions = extractions.filter((extraction) => {
          const fixedFields = extraction.fixedFields as any;
          const endDateValue = fixedFields?.end_date?.value;

          if (!endDateValue) return false;

          try {
            const endDate = new Date(endDateValue);
            return endDate >= now && endDate <= futureDate;
          } catch {
            return false;
          }
        });

        // Update total count for filtered results
        total = extractions.length;
      }

      return {
        extractions: extractions.map(ContractExtraction.fromPersistence),
        total,
      };
    } catch (error) {
      console.error("Error getting contract extractions with filters:", error);
      throw new Error(
        `Failed to get contract extractions: ${(error as Error).message}`
      );
    }
  }

  async getByConfidenceThreshold(
    tenantId: string,
    minConfidence: number,
    maxConfidence?: number
  ): Promise<ContractExtraction[]> {
    try {
      const where: any = {
        tenantId,
        overallConfidence: {
          gte: minConfidence,
        },
      };

      if (maxConfidence !== undefined) {
        where.overallConfidence.lte = maxConfidence;
      }

      const extractions = await this.prisma.contractExtraction.findMany({
        where,
        orderBy: { overallConfidence: "desc" },
      });

      return extractions.map(ContractExtraction.fromPersistence);
    } catch (error) {
      console.error("Error getting contract extractions by confidence:", error);
      throw new Error(
        `Failed to get contract extractions: ${(error as Error).message}`
      );
    }
  }

  async getByExtractionVersion(
    tenantId: string,
    version: string
  ): Promise<ContractExtraction[]> {
    try {
      const extractions = await this.prisma.contractExtraction.findMany({
        where: {
          tenantId,
          extractionVersion: version,
        },
        orderBy: { extractionDate: "desc" },
      });

      return extractions.map(ContractExtraction.fromPersistence);
    } catch (error) {
      console.error("Error getting contract extractions by version:", error);
      throw new Error(
        `Failed to get contract extractions: ${(error as Error).message}`
      );
    }
  }

  async existsForContract(
    contractId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const count = await this.prisma.contractExtraction.count({
        where: {
          contractId,
          tenantId,
        },
      });

      return count > 0;
    } catch (error) {
      console.error("Error checking if extraction exists:", error);
      throw new Error(
        `Failed to check extraction existence: ${(error as Error).message}`
      );
    }
  }

  async getExtractionStats(tenantId: string): Promise<{
    totalExtractions: number;
    averageConfidence: number;
    highConfidenceCount: number;
    lowConfidenceCount: number;
    latestExtractionDate: Date | null;
  }> {
    try {
      const [totalExtractions, avgResult, highConfCount, lowConfCount, latest] =
        await Promise.all([
          this.prisma.contractExtraction.count({ where: { tenantId } }),
          this.prisma.contractExtraction.aggregate({
            where: { tenantId },
            _avg: { overallConfidence: true },
          }),
          this.prisma.contractExtraction.count({
            where: { tenantId, overallConfidence: { gte: 0.8 } },
          }),
          this.prisma.contractExtraction.count({
            where: { tenantId, overallConfidence: { lt: 0.5 } },
          }),
          this.prisma.contractExtraction.findFirst({
            where: { tenantId },
            orderBy: { extractionDate: "desc" },
            select: { extractionDate: true },
          }),
        ]);

      return {
        totalExtractions,
        averageConfidence: avgResult._avg.overallConfidence || 0,
        highConfidenceCount: highConfCount,
        lowConfidenceCount: lowConfCount,
        latestExtractionDate: latest?.extractionDate || null,
      };
    } catch (error) {
      console.error("Error getting extraction stats:", error);
      throw new Error(
        `Failed to get extraction stats: ${(error as Error).message}`
      );
    }
  }

  /**
   * Finds contract extractions by tenant ID (alias for getByTenantId)
   */
  async findByTenantId(tenantId: string): Promise<ContractExtraction[]> {
    return this.getByTenantId(tenantId);
  }

  /**
   * Finds a contract extraction by contract ID (alias for getByContractId)
   */
  async findByContractId(
    contractId: string
  ): Promise<ContractExtraction | null> {
    try {
      const extraction = await this.prisma.contractExtraction.findFirst({
        where: { contractId },
      });

      return extraction ? ContractExtraction.fromPersistence(extraction) : null;
    } catch (error) {
      console.error("Error finding contract extraction by contract ID:", error);
      throw new Error(
        `Failed to find contract extraction: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates the reportingTo field for a contract extraction
   */
  async updateReportingTo(
    contractId: string,
    reportingTo: string | null
  ): Promise<void> {
    try {
      await this.prisma.contractExtraction.updateMany({
        where: { contractId },
        data: { reportingTo },
      });
    } catch (error) {
      console.error("Error updating reporting relationship:", error);
      throw new Error(
        `Failed to update reporting relationship: ${(error as Error).message}`
      );
    }
  }

  /**
   * Updates the integrity analysis for a contract extraction
   */
  async updateIntegrityAnalysis(
    contractId: string,
    tenantId: string,
    integrityAnalysis: any
  ): Promise<void> {
    try {
      await this.prisma.contractExtraction.updateMany({
        where: {
          contractId,
          tenantId,
        },
        data: {
          integrityAnalysis: integrityAnalysis,
        } as any,
      });
    } catch (error) {
      throw new Error(
        `Failed to update integrity analysis: ${(error as Error).message}`
      );
    }
  }
}
