/**
 * Vector Repository
 * Manages document embeddings for RAG functionality
 */

import { PrismaClient } from "@prisma/client";
import { logger } from "../logging/logger";

// Define Vector class if it doesn't exist
class Vector extends Array<number> {
  static from(array: number[] | Float32Array): Vector {
    const vector = new Vector();
    if (array instanceof Float32Array) {
      for (let i = 0; i < array.length; i++) {
        vector.push(array[i]);
      }
    } else {
      array.forEach((value) => vector.push(value));
    }
    return vector;
  }
}

/**
 * Document chunk with embedding
 */
export interface DocumentChunkWithEmbedding {
  id: string;
  documentId: string;
  tenantId: string;
  chunkIndex: number;
  content: string;
  metadata: Record<string, any>;
  embedding?: number[] | Float32Array;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Search result with similarity score
 */
export interface SearchResult {
  chunk: DocumentChunkWithEmbedding;
  similarity: number;
}

/**
 * Repository for managing document embeddings
 */
export class VectorRepository {
  private prisma: PrismaClient;

  /**
   * Creates a new vector repository
   * @param prisma - Prisma client instance
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Stores a document chunk with its embedding
   * @param chunk - Document chunk with embedding
   * @returns Stored document chunk
   */
  async storeDocumentChunk(
    chunk: DocumentChunkWithEmbedding
  ): Promise<DocumentChunkWithEmbedding> {
    try {
      // Convert embedding to Vector type if provided
      let embeddingVector: any = null;
      if (chunk.embedding) {
        embeddingVector = Vector.from(chunk.embedding);
      }

      // Store document chunk in database
      const result = await this.prisma.documentChunk.create({
        data: {
          id: chunk.id,
          documentId: chunk.documentId,
          tenantId: chunk.tenantId,
          chunkIndex: chunk.chunkIndex,
          content: chunk.content,
          metadata: chunk.metadata,
          // Embedding is handled separately in a raw SQL query
        },
      });

      return {
        ...result,
        metadata: result.metadata as Record<string, any>,
        embedding: chunk.embedding, // Return the original embedding
      };
    } catch (error) {
      logger.error("Failed to store document chunk", {
        error,
        chunkId: chunk.id,
      });
      throw new Error(
        `Failed to store document chunk: ${(error as Error).message}`
      );
    }
  }

  /**
   * Retrieves a document chunk by ID
   * @param id - Document chunk ID
   * @returns Document chunk or null if not found
   */
  async getDocumentChunk(
    id: string
  ): Promise<DocumentChunkWithEmbedding | null> {
    try {
      const chunk = await this.prisma.documentChunk.findUnique({
        where: { id },
      });

      if (!chunk) {
        return null;
      }

      return {
        ...chunk,
        metadata: chunk.metadata as Record<string, any>,
        embedding: undefined, // Embedding is not available in the Prisma model
      };
    } catch (error) {
      logger.error("Failed to retrieve document chunk", { error, id });
      throw new Error(
        `Failed to retrieve document chunk: ${(error as Error).message}`
      );
    }
  }

  /**
   * Searches for similar document chunks using vector similarity
   * @param embedding - Query embedding vector
   * @param tenantId - Tenant ID for isolation
   * @param limit - Maximum number of results to return
   * @param threshold - Minimum similarity threshold (0-1)
   * @returns Array of search results with similarity scores
   */
  async searchSimilarChunks(
    embedding: number[] | Float32Array,
    tenantId: string,
    limit: number = 5,
    threshold: number = 0.7
  ): Promise<SearchResult[]> {
    try {
      // Convert embedding to Vector type
      const queryVector = Vector.from(embedding);

      // Execute vector similarity search with tenant isolation
      const results = await this.prisma.$queryRaw<Array<any>>`
        SELECT
          id,
          "documentId",
          "tenantId",
          "chunkIndex",
          content,
          metadata,
          "createdAt",
          "updatedAt",
          1 - (embedding <=> ${queryVector}) as similarity
        FROM "DocumentChunk"
        WHERE
          "tenantId" = ${tenantId}
          AND embedding IS NOT NULL
          AND 1 - (embedding <=> ${queryVector}) > ${threshold}
        ORDER BY similarity DESC
        LIMIT ${limit}
      `;

      // Map results to SearchResult interface
      return results.map((result) => ({
        chunk: {
          id: result.id,
          documentId: result.documentId,
          tenantId: result.tenantId,
          chunkIndex: result.chunkIndex,
          content: result.content,
          metadata: result.metadata,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          // Embedding is not returned in search results to save bandwidth
        },
        similarity: result.similarity,
      }));
    } catch (error) {
      logger.error("Failed to search similar chunks", { error, tenantId });
      throw new Error(
        `Failed to search similar chunks: ${(error as Error).message}`
      );
    }
  }

  /**
   * Deletes document chunks by document ID
   * @param documentId - Document ID
   * @param tenantId - Tenant ID for isolation
   * @returns Number of deleted chunks
   */
  async deleteDocumentChunks(
    documentId: string,
    tenantId: string
  ): Promise<number> {
    try {
      const result = await this.prisma.documentChunk.deleteMany({
        where: {
          documentId,
          tenantId,
        },
      });

      return result.count;
    } catch (error) {
      logger.error("Failed to delete document chunks", {
        error,
        documentId,
        tenantId,
      });
      throw new Error(
        `Failed to delete document chunks: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all document chunks for a document
   * @param documentId - Document ID
   * @param tenantId - Tenant ID for isolation
   * @returns Array of document chunks
   */
  async getDocumentChunks(
    documentId: string,
    tenantId: string
  ): Promise<DocumentChunkWithEmbedding[]> {
    try {
      const chunks = await this.prisma.documentChunk.findMany({
        where: {
          documentId,
          tenantId,
        },
        orderBy: {
          chunkIndex: "asc",
        },
      });

      return chunks.map((chunk) => ({
        ...chunk,
        metadata: chunk.metadata as Record<string, any>,
        embedding: undefined, // Embedding is not available in the Prisma model
      }));
    } catch (error) {
      logger.error("Failed to retrieve document chunks", {
        error,
        documentId,
        tenantId,
      });
      throw new Error(
        `Failed to retrieve document chunks: ${(error as Error).message}`
      );
    }
  }
}
