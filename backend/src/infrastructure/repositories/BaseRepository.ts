/**
 * Base Repository
 * Provides common functionality for all repositories with tenant isolation
 */

import { PrismaClient } from '@prisma/client';
import { TenantContext } from '../../domain/shared/TenantContext';
import { logger } from '../logging/logger';

/**
 * Base repository class that enforces tenant isolation
 */
export abstract class BaseRepository<T> {
  protected prisma: PrismaClient;
  protected entityName: string;

  constructor(prisma: PrismaClient, entityName: string) {
    this.prisma = prisma;
    this.entityName = entityName;
  }

  /**
   * Gets the current tenant ID from the context
   * @returns Current tenant ID
   * @throws Error if no tenant ID is available
   */
  protected getCurrentTenantId(): string {
    const tenantId = TenantContext.getCurrentTenant();
    
    if (!tenantId) {
      const error = new Error(`No tenant context available for ${this.entityName} repository operation`);
      logger.error('Tenant context missing', { error, entityName: this.entityName });
      throw error;
    }
    
    return tenantId;
  }

  /**
   * Ensures that an entity belongs to the current tenant
   * @param entity Entity to check
   * @throws Error if entity does not belong to the current tenant
   */
  protected ensureTenantOwnership(entity: any): void {
    const currentTenantId = this.getCurrentTenantId();
    
    if (!entity) {
      return;
    }
    
    if (entity.tenantId !== currentTenantId) {
      const error = new Error(`Entity does not belong to the current tenant`);
      logger.error('Tenant ownership violation', { 
        error, 
        entityName: this.entityName,
        entityId: entity.id,
        entityTenantId: entity.tenantId,
        currentTenantId
      });
      throw error;
    }
  }

  /**
   * Adds tenant filter to a query
   * @param query Query object
   * @returns Query with tenant filter added
   */
  protected addTenantFilter(query: any): any {
    const tenantId = this.getCurrentTenantId();
    
    // Create a new object to avoid modifying the original
    const queryWithTenant = { ...query };
    
    // Add tenant filter
    if (!queryWithTenant.where) {
      queryWithTenant.where = { tenantId };
    } else {
      queryWithTenant.where = {
        ...queryWithTenant.where,
        tenantId
      };
    }
    
    return queryWithTenant;
  }

  /**
   * Logs an operation with tenant context
   * @param operation Operation name
   * @param details Operation details
   */
  protected logOperation(operation: string, details: any): void {
    logger.debug(`${this.entityName} repository: ${operation}`, {
      ...details,
      tenantId: this.getCurrentTenantId(),
      entityName: this.entityName
    });
  }
}
