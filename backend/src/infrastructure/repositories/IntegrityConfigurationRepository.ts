import { PrismaClient, IntegrityConfiguration } from "@prisma/client";
import { logger } from "../logging/logger";

export interface CreateIntegrityConfigurationInput {
  userId: string;
  tenantId: string;
  configurationName: string;
  clauses: any;
  isActive?: boolean;
  isDefault?: boolean;
}

export interface UpdateIntegrityConfigurationInput {
  configurationName?: string;
  clauses?: any;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * Repository for managing integrity configuration data
 */
export class IntegrityConfigurationRepository {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Create a new integrity configuration
   */
  async create(input: CreateIntegrityConfigurationInput): Promise<IntegrityConfiguration> {
    try {


      return await this.prisma.integrityConfiguration.create({
        data: {
          userId: input.userId,
          tenantId: input.tenantId,
          configurationName: input.configurationName,
          clauses: input.clauses,
          isActive: input.isActive ?? true,
          isDefault: input.isDefault ?? false,
        },
      });
    } catch (error) {
      logger.error("Error creating integrity configuration:", error);
      throw new Error(`Failed to create integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Get integrity configuration by ID
   */
  async findById(id: string, tenantId: string): Promise<IntegrityConfiguration | null> {
    try {
      return await this.prisma.integrityConfiguration.findFirst({
        where: {
          id,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error finding integrity configuration by ID:", error);
      throw new Error(`Failed to find integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Get user's active integrity configuration
   */
  async findActiveByUser(userId: string, tenantId: string): Promise<IntegrityConfiguration | null> {
    try {
      return await this.prisma.integrityConfiguration.findFirst({
        where: {
          userId,
          tenantId,
          isActive: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
    } catch (error) {
      logger.error("Error finding active integrity configuration:", error);
      throw new Error(`Failed to find active integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Get all configurations for a user
   */
  async findByUser(userId: string, tenantId: string): Promise<IntegrityConfiguration[]> {
    try {
      return await this.prisma.integrityConfiguration.findMany({
        where: {
          userId,
          tenantId,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });
    } catch (error) {
      logger.error("Error finding user integrity configurations:", error);
      throw new Error(`Failed to find user integrity configurations: ${(error as Error).message}`);
    }
  }

  /**
   * Get default configuration for tenant
   */
  async findDefaultByTenant(tenantId: string): Promise<IntegrityConfiguration | null> {
    try {
      return await this.prisma.integrityConfiguration.findFirst({
        where: {
          tenantId,
          isDefault: true,
          isActive: true,
        },
      });
    } catch (error) {
      logger.error("Error finding default integrity configuration:", error);
      throw new Error(`Failed to find default integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Update integrity configuration
   */
  async update(
    id: string,
    tenantId: string,
    input: UpdateIntegrityConfigurationInput
  ): Promise<IntegrityConfiguration> {
    try {


      return await this.prisma.integrityConfiguration.update({
        where: {
          id,
          tenantId,
        },
        data: input,
      });
    } catch (error) {
      logger.error("Error updating integrity configuration:", error);
      throw new Error(`Failed to update integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Delete integrity configuration
   */
  async delete(id: string, tenantId: string): Promise<void> {
    try {


      await this.prisma.integrityConfiguration.delete({
        where: {
          id,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error deleting integrity configuration:", error);
      throw new Error(`Failed to delete integrity configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Set configuration as active (deactivate others for the user)
   */
  async setActive(id: string, userId: string, tenantId: string): Promise<IntegrityConfiguration> {
    try {


      return await this.prisma.$transaction(async (tx) => {
        // Deactivate all other configurations for this user
        await tx.integrityConfiguration.updateMany({
          where: {
            userId,
            tenantId,
            id: { not: id },
          },
          data: {
            isActive: false,
          },
        });

        // Activate the selected configuration
        return await tx.integrityConfiguration.update({
          where: {
            id,
            tenantId,
          },
          data: {
            isActive: true,
          },
        });
      });
    } catch (error) {
      logger.error("Error setting integrity configuration as active:", error);
      throw new Error(`Failed to set integrity configuration as active: ${(error as Error).message}`);
    }
  }

  /**
   * Check if configuration name exists for user
   */
  async existsByName(
    userId: string,
    tenantId: string,
    configurationName: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      const where: any = {
        userId,
        tenantId,
        configurationName,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await this.prisma.integrityConfiguration.count({
        where,
      });

      return count > 0;
    } catch (error) {
      logger.error("Error checking integrity configuration name existence:", error);
      throw new Error(`Failed to check integrity configuration name: ${(error as Error).message}`);
    }
  }

  /**
   * Deactivate all configurations for a user
   */
  async deactivateAllForUser(userId: string, tenantId: string): Promise<void> {
    try {
      await this.prisma.integrityConfiguration.updateMany({
        where: {
          userId,
          tenantId,
        },
        data: {
          isActive: false,
        },
      });
    } catch (error) {
      logger.error("Error deactivating integrity configurations:", error);
      throw new Error(`Failed to deactivate integrity configurations: ${(error as Error).message}`);
    }
  }

  /**
   * Delete all configurations for a user
   */
  async deleteAllForUser(userId: string, tenantId: string): Promise<void> {
    try {
      await this.prisma.integrityConfiguration.deleteMany({
        where: {
          userId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Error deleting integrity configurations:", error);
      throw new Error(`Failed to delete integrity configurations: ${(error as Error).message}`);
    }
  }
}
