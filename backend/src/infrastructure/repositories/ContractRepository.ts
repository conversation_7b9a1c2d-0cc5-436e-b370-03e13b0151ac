/**
 * Contract Repository Implementation
 * Implements the IContractRepository interface using Prisma ORM
 * and follows the Repository Pattern
 */

import {
  PrismaClient,
  ContractStatus as PrismaContractStatus,
} from "@prisma/client";
import { Contract } from "../../domain/contracts/Contract";
import { ContractVersion } from "../../domain/contracts/ContractVersion";
import { ContractMetadata } from "../../domain/contracts/ContractMetadata";
import {
  IContractRepository,
  ContractSearchParams,
} from "../../domain/contracts/interfaces/IContractRepository";
import { EncryptionService } from "../services/EncryptionService";
import { v4 as uuidv4 } from "uuid";

/**
 * ContractRepository implements the IContractRepository interface
 * using Prisma ORM to interact with the database
 */
export class ContractRepository implements IContractRepository {
  private prisma: PrismaClient;
  private encryptionService: EncryptionService;

  constructor(prisma: PrismaClient, encryptionService: EncryptionService) {
    this.prisma = prisma;
    this.encryptionService = encryptionService;
  }

  /**
   * Creates a new contract
   * @param contract Contract entity to create
   * @returns Created contract with generated ID
   */
  async create(contract: Contract): Promise<Contract> {
    try {
      const data = contract.toDTO();

      // Map calculated status to backend status enum
      let mappedStatus = "ACTIVE"; // Default to ACTIVE
      if (data.status === "Active") {
        mappedStatus = "ACTIVE";
      } else if (data.status === "Inactive") {
        mappedStatus = "EXPIRED";
      } else if (data.status === "Unknown") {
        mappedStatus = "DRAFT";
      }

      // Ensure status is a valid enum value
      const validStatus = [
        "DRAFT",
        "REVIEW",
        "PENDING_APPROVAL",
        "APPROVED",
        "ACTIVE",
        "EXPIRING",
        "EXPIRED",
        "TERMINATED",
        "RENEWED",
      ].includes(mappedStatus)
        ? mappedStatus
        : "DRAFT";

      // Map frontend security classification to backend
      let mappedSecurityClassification = data.securityClassification;
      if (
        data.securityClassification &&
        data.securityClassification.toString() === "HIGHLY_RESTRICTED"
      ) {
        mappedSecurityClassification = "RESTRICTED";
      }

      // Ensure security classification is a valid enum value
      const validSecurityClassification = [
        "PUBLIC",
        "INTERNAL",
        "CONFIDENTIAL",
        "RESTRICTED",
      ].includes(mappedSecurityClassification)
        ? mappedSecurityClassification
        : "CONFIDENTIAL";

      const createdContract = await this.prisma.contract.create({
        data: {
          id: data.id || uuidv4(),
          title: data.title,
          description: data.description || "",
          contractNumber: data.contractNumber || "",
          agreementType: data.agreementType,
          classification: data.classification,
          status: validStatus as PrismaContractStatus,
          startDate: data.startDate,
          endDate: data.endDate,
          renewalType: data.renewalType,
          renewalDate: data.renewalDate,
          isAutoRenew: data.isAutoRenew || false,
          noticePeriodDays: data.noticePeriodDays,
          version: data.version || 1,
          securityClassification: validSecurityClassification,
          isEncrypted: data.isEncrypted || false,
          currentVersionId: data.currentVersionId,
          counterparty: data.counterparty || "",
          value: data.value || "",
          tenantId: data.tenantId,
          createdById: data.createdById, // Add createdById field
        },
      });

      return Contract.fromPersistence(createdContract);
    } catch (error) {
      console.error("Error creating contract:", error);
      throw new Error(`Failed to create contract: ${(error as Error).message}`);
    }
  }

  /**
   * Finds a contract by ID
   * @param id Contract ID
   * @returns Contract entity or null if not found
   */
  async findById(id: string): Promise<Contract | null> {
    try {
      const contract = await this.prisma.contract.findUnique({
        where: { id },
        include: {
          folder: true, // Include folder information
        },
      });

      if (!contract) {
        return null;
      }

      return Contract.fromPersistence(contract);
    } catch (error) {
      console.error("Error finding contract by ID:", error);
      throw new Error(`Failed to find contract: ${(error as Error).message}`);
    }
  }

  /**
   * Updates an existing contract
   * @param contract Contract entity with updated values
   * @returns Updated contract
   */
  async update(contract: Contract): Promise<Contract> {
    try {
      const data = contract.toDTO();

      // Map calculated status to backend status enum
      let mappedStatus = "ACTIVE"; // Default to ACTIVE
      if (data.status === "Active") {
        mappedStatus = "ACTIVE";
      } else if (data.status === "Inactive") {
        mappedStatus = "EXPIRED";
      } else if (data.status === "Unknown") {
        mappedStatus = "DRAFT";
      }

      // Ensure status is a valid enum value
      const validStatus = [
        "DRAFT",
        "REVIEW",
        "PENDING_APPROVAL",
        "APPROVED",
        "ACTIVE",
        "EXPIRING",
        "EXPIRED",
        "TERMINATED",
        "RENEWED",
      ].includes(mappedStatus)
        ? mappedStatus
        : "DRAFT";

      // Map frontend security classification to backend
      let mappedSecurityClassification = data.securityClassification;
      if (
        data.securityClassification &&
        data.securityClassification.toString() === "HIGHLY_RESTRICTED"
      ) {
        mappedSecurityClassification = "RESTRICTED";
      }

      // Ensure security classification is a valid enum value
      const validSecurityClassification = [
        "PUBLIC",
        "INTERNAL",
        "CONFIDENTIAL",
        "RESTRICTED",
      ].includes(mappedSecurityClassification)
        ? mappedSecurityClassification
        : "CONFIDENTIAL";

      const updatedContract = await this.prisma.contract.update({
        where: { id: data.id },
        data: {
          title: data.title,
          description: data.description || "",
          contractNumber: data.contractNumber || "",

          status: validStatus as PrismaContractStatus,
          startDate: data.startDate,
          endDate: data.endDate,
          renewalType: data.renewalType,
          renewalDate: data.renewalDate,
          isAutoRenew: data.isAutoRenew || false,
          noticePeriodDays: data.noticePeriodDays,
          version: data.version || 1,
          securityClassification: validSecurityClassification,
          isEncrypted: data.isEncrypted || false,
          currentVersionId: data.currentVersionId,
          counterparty: data.counterparty || "",
          value: data.value || "",
          updatedAt: new Date(),
        },
      });

      return Contract.fromPersistence(updatedContract);
    } catch (error) {
      console.error("Error updating contract:", error);
      throw new Error(`Failed to update contract: ${(error as Error).message}`);
    }
  }

  /**
   * Deletes a contract by ID
   * @param id Contract ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    try {
      // Check if contract exists
      const contract = await this.prisma.contract.findUnique({
        where: { id },
      });

      if (!contract) {
        return false;
      }

      // Use a transaction to ensure all related entities are deleted
      return await this.prisma.$transaction(async (tx) => {
        // Delete related entities that we know exist
        // Only delete from tables that actually exist in the current database

        // Delete contract metadata (if exists)
        try {
          await tx.contractMetadata.deleteMany({
            where: { contractId: id },
          });
        } catch (error) {
          console.warn("ContractMetadata table not found, skipping deletion");
        }

        // Delete contract versions (if exists)
        try {
          await tx.contractVersion.deleteMany({
            where: { contractId: id },
          });
        } catch (error) {
          console.warn("ContractVersion table not found, skipping deletion");
        }

        // Delete comments (if exists)
        try {
          await tx.comment.deleteMany({
            where: { contractId: id },
          });
        } catch (error) {
          console.warn("Comment table not found, skipping deletion");
        }

        // Delete contract parties (if exists)
        try {
          await tx.contractParty.deleteMany({
            where: { contractId: id },
          });
        } catch (error) {
          console.warn("ContractParty table not found, skipping deletion");
        }

        // Delete contract extractions (three-tier extraction data)
        try {
          await tx.contractExtraction.deleteMany({
            where: { contractId: id },
          });
        } catch (error) {
          console.warn("ContractExtraction table not found, skipping deletion");
        }

        // Finally delete the contract
        await tx.contract.delete({
          where: { id },
        });

        return true;
      });
    } catch (error) {
      console.error("Error deleting contract:", error);
      throw new Error(`Failed to delete contract: ${(error as Error).message}`);
    }
  }

  /**
   * Checks if a contract with the given title already exists for the tenant
   * @param title Contract title to check
   * @param tenantId Tenant ID
   * @returns True if a contract with this title exists, false otherwise
   */
  async existsByTitle(title: string, tenantId: string): Promise<boolean> {
    try {
      const count = await this.prisma.contract.count({
        where: {
          title: {
            equals: title,
            mode: "insensitive", // Case-insensitive comparison
          },
          tenantId,
        },
      });

      return count > 0;
    } catch (error) {
      console.error("Error checking if contract exists by title:", error);
      throw new Error(
        `Failed to check contract existence: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all contracts for a tenant (without pagination)
   * @param tenantId Tenant ID
   * @returns Array of all contracts for the tenant
   */
  async findByTenantId(tenantId: string): Promise<Contract[]> {
    try {
      const contracts = await this.prisma.contract.findMany({
        where: { tenantId },
        orderBy: { updatedAt: "desc" },
        include: {
          folder: true, // Include folder information
        },
      });

      return contracts.map((contract) => Contract.fromPersistence(contract));
    } catch (error) {
      console.error("Error finding contracts by tenant ID:", error);
      throw new Error(`Failed to find contracts: ${(error as Error).message}`);
    }
  }

  /**
   * Searches for contracts based on various parameters
   * @param params Search parameters
   * @returns Array of contracts matching the search criteria
   */
  async search(
    params: ContractSearchParams
  ): Promise<{ contracts: Contract[]; total: number }> {
    try {
      const {
        title,
        agreementType,
        status,
        securityClassification,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        tenantId,
        page = 1,
        limit = 20,
        folderId, // Add folderId parameter support
      } = params;

      // Build the where clause based on the provided parameters
      const where: any = {
        tenantId,
      };

      // Handle folder filtering
      if (folderId !== undefined) {
        where.folderId = folderId; // Filter by specific folder (can be null for standalone)
      }
      // If no folderId filter is specified, return ALL contracts (both grouped and standalone)
      // This allows the frontend to display all contracts with their grouping status

      if (title) {
        where.title = { contains: title, mode: "insensitive" };
      }

      if (agreementType) {
        where.agreementType = agreementType;
      }

      if (status) {
        // Map frontend status to backend status
        let mappedStatus = status;
        if (status && status.toString() === "PENDING") {
          mappedStatus = "PENDING_APPROVAL";
        }

        // Ensure status is a valid enum value
        const validStatus = [
          "DRAFT",
          "REVIEW",
          "PENDING_APPROVAL",
          "APPROVED",
          "ACTIVE",
          "EXPIRING",
          "EXPIRED",
          "TERMINATED",
          "RENEWED",
        ].includes(mappedStatus)
          ? mappedStatus
          : "DRAFT";

        where.status = validStatus;
      }

      if (securityClassification) {
        where.securityClassification = securityClassification;
      }

      // Handle date ranges
      if (startDateFrom || startDateTo) {
        where.startDate = {};
        if (startDateFrom) where.startDate.gte = startDateFrom;
        if (startDateTo) where.startDate.lte = startDateTo;
      }

      if (endDateFrom || endDateTo) {
        where.endDate = {};
        if (endDateFrom) where.endDate.gte = endDateFrom;
        if (endDateTo) where.endDate.lte = endDateTo;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute query with pagination and include folder information
      const [contracts, total] = await Promise.all([
        this.prisma.contract.findMany({
          where,
          skip,
          take: limit,
          orderBy: { updatedAt: "desc" },
          include: {
            folder: true, // Include folder information
          },
        }),
        this.prisma.contract.count({ where }),
      ]);

      // Convert to domain entities
      const contractEntities = contracts.map((contract) =>
        Contract.fromPersistence(contract)
      );

      return { contracts: contractEntities, total };
    } catch (error) {
      console.error("Error searching contracts:", error);
      throw new Error(
        `Failed to search contracts: ${(error as Error).message}`
      );
    }
  }

  /**
   * Creates a new contract version
   * @param contractVersion Contract version to create
   * @param documentBuffer Optional document buffer to store
   * @returns Created contract version
   */
  async createVersion(
    contractVersion: ContractVersion,
    documentBuffer?: Buffer
  ): Promise<ContractVersion> {
    try {
      const data = contractVersion.toDTO();

      // If the document is encrypted, ensure there's an encryption key ID
      if (data.encryptionKeyId) {
        // In a real implementation, we might validate the key ID here
        // or handle additional encryption operations
      }

      const createdVersion = await this.prisma.contractVersion.create({
        data: {
          id: data.id || uuidv4(),
          contractId: data.contractId,
          versionNumber: data.versionNumber,
          documentUri: data.documentUri,
          documentHash: data.documentHash,
          documentFormat: data.documentFormat,
          documentSize: data.documentSize,
          documentName: data.documentName,
          documentContent: documentBuffer, // Store the actual document content
          mimeType: data.mimeType, // Store the MIME type
          encryptionKeyId: data.encryptionKeyId,
          versionComment: data.versionComment,
          changelog: data.changelog ? (data.changelog as any) : undefined,
          metadata: data.metadata ? (data.metadata as any) : undefined,
          createdBy: data.createdBy,
        },
      });

      // Get the current contract to preserve its status
      const currentContract = await this.prisma.contract.findUnique({
        where: { id: data.contractId },
      });

      // Map frontend status to backend status
      let mappedStatus = currentContract?.status || "DRAFT";
      if (mappedStatus && mappedStatus.toString() === "PENDING") {
        mappedStatus = "PENDING_APPROVAL";
      }

      // Ensure we have a valid status
      const validStatus =
        mappedStatus &&
        [
          "DRAFT",
          "REVIEW",
          "PENDING_APPROVAL",
          "APPROVED",
          "ACTIVE",
          "EXPIRING",
          "EXPIRED",
          "TERMINATED",
          "RENEWED",
        ].includes(mappedStatus)
          ? mappedStatus
          : "DRAFT";

      // Update the contract with the new version information
      await this.prisma.contract.update({
        where: { id: data.contractId },
        data: {
          version: data.versionNumber,
          currentVersionId: createdVersion.id,
          status: validStatus, // Ensure status is valid
          updatedAt: new Date(),
        },
      });

      return ContractVersion.fromPersistence(createdVersion);
    } catch (error) {
      console.error("Error creating contract version:", error);
      throw new Error(
        `Failed to create contract version: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets all versions of a contract
   * @param contractId Contract ID
   * @returns Array of contract versions
   */
  async getVersionHistory(contractId: string): Promise<ContractVersion[]> {
    try {
      const versions = await this.prisma.contractVersion.findMany({
        where: { contractId },
        orderBy: { versionNumber: "desc" },
      });

      return versions.map((version) =>
        ContractVersion.fromPersistence(version)
      );
    } catch (error) {
      console.error("Error getting version history:", error);
      throw new Error(
        `Failed to get version history: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets a specific version of a contract
   * @param contractId Contract ID
   * @param versionNumber Version number
   * @returns Contract version or null if not found
   */
  async getVersion(
    contractId: string,
    versionNumber: number
  ): Promise<ContractVersion | null> {
    try {
      const version = await this.prisma.contractVersion.findFirst({
        where: {
          contractId,
          versionNumber,
        },
      });

      if (!version) {
        return null;
      }

      return ContractVersion.fromPersistence(version);
    } catch (error) {
      console.error("Error getting specific version:", error);
      throw new Error(`Failed to get version: ${(error as Error).message}`);
    }
  }

  /**
   * Gets the latest version of a contract
   * @param contractId Contract ID
   * @returns Latest contract version or null if not found
   */
  async getLatestVersion(contractId: string): Promise<ContractVersion | null> {
    try {
      const version = await this.prisma.contractVersion.findFirst({
        where: {
          contractId,
        },
        orderBy: {
          versionNumber: "desc",
        },
      });

      if (!version) {
        return null;
      }

      return ContractVersion.fromPersistence(version);
    } catch (error) {
      console.error("Error getting latest contract version:", error);
      throw new Error(
        `Failed to get latest contract version: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets document content from a document ID
   * @param documentId Document ID (version ID)
   * @returns Document content as Buffer or null if not found
   */
  // This method is commented out because documentContent is not in the schema
  // In a real implementation, you would have a field to store document content
  /*
  async getDocumentContent(documentId: string): Promise<Buffer | null> {
    try {
      // In a real implementation, this would retrieve the document from a storage service
      // For this implementation, we'll check if there's a document in the database
      const version = await this.prisma.contractVersion.findUnique({
        where: {
          id: documentId,
        },
      });

      if (!version) {
        return null;
      }

      // In a real implementation, you would retrieve the document content
      // For now, return a placeholder
      return Buffer.from("Document content placeholder");
    } catch (error) {
      console.error("Error getting document content:", error);
      throw new Error(
        `Failed to get document content: ${(error as Error).message}`
      );
    }
  }
  */

  /**
   * Creates or updates contract metadata
   * @param metadata Contract metadata to create or update
   * @returns Created or updated contract metadata
   */
  async saveMetadata(metadata: ContractMetadata): Promise<ContractMetadata> {
    try {
      const data = metadata.toDTO();

      // Check if metadata contains sensitive information that needs encryption
      if (metadata.hasSensitiveInformation() && data.totalValue) {
        // In a real implementation, we would encrypt sensitive data here
        // For demonstration, we'll show the pattern but not implement full encryption
        const sensitiveData = {
          totalValue: data.totalValue.toString(),
          paymentTerms: data.paymentTerms,
        };

        // Encrypt sensitive data
        const { encryptedData, keyId } = await this.encryptionService.encrypt(
          JSON.stringify(sensitiveData)
        );

        // Store the encrypted data
        data.encryptedFields = encryptedData;
      }

      // Check if metadata already exists for this contract
      const existingMetadata = await this.prisma.contractMetadata.findUnique({
        where: { contractId: data.contractId },
      });

      let savedMetadata;

      if (existingMetadata) {
        // Update existing metadata
        savedMetadata = await this.prisma.contractMetadata.update({
          where: { id: existingMetadata.id },
          data: {
            totalValue: data.totalValue,
            currency: data.currency,
            paymentTerms: data.paymentTerms,
            autoExtractedFields: data.autoExtractedFields
              ? (data.autoExtractedFields as any)
              : undefined,
            customMetadata: data.customMetadata
              ? (data.customMetadata as any)
              : undefined,
            encryptedFields: data.encryptedFields,
            lastExtractedAt: data.lastExtractedAt,

            updatedAt: new Date(),
          },
        });
      } else {
        // Create new metadata
        savedMetadata = await this.prisma.contractMetadata.create({
          data: {
            id: data.id || uuidv4(),
            contractId: data.contractId,
            totalValue: data.totalValue,
            currency: data.currency,
            paymentTerms: data.paymentTerms,
            autoExtractedFields: data.autoExtractedFields
              ? (data.autoExtractedFields as any)
              : undefined,
            customMetadata: data.customMetadata
              ? (data.customMetadata as any)
              : undefined,
            encryptedFields: data.encryptedFields,
            lastExtractedAt: data.lastExtractedAt,
          },
        });
      }

      return ContractMetadata.fromPersistence(savedMetadata);
    } catch (error) {
      console.error("Error saving contract metadata:", error);
      throw new Error(
        `Failed to save contract metadata: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets metadata for a contract
   * @param contractId Contract ID
   * @returns Contract metadata or null if not found
   */
  async getMetadata(contractId: string): Promise<ContractMetadata | null> {
    try {
      const metadata = await this.prisma.contractMetadata.findUnique({
        where: { contractId },
      });

      if (!metadata) {
        return null;
      }

      const metadataEntity = ContractMetadata.fromPersistence(metadata);

      // If there are encrypted fields, decrypt them
      if (metadata.encryptedFields && this.encryptionService) {
        try {
          const decryptedData = await this.encryptionService.decrypt(
            metadata.encryptedFields
          );

          // Parse the decrypted data
          const sensitiveData = JSON.parse(decryptedData);

          // We would typically merge this back into the entity
          // but for demonstration purposes, we'll just log it
          console.log("Decrypted sensitive data available");
        } catch (decryptError) {
          console.error("Error decrypting metadata fields:", decryptError);
          // Don't fail the operation, just return metadata without decrypted fields
        }
      }

      return metadataEntity;
    } catch (error) {
      console.error("Error getting contract metadata:", error);
      throw new Error(
        `Failed to get contract metadata: ${(error as Error).message}`
      );
    }
  }
}
