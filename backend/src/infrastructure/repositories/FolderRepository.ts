/**
 * Folder Repository Implementation
 * Implements folder data access using Prisma
 */

import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { Folder } from "../../domain/folders/Folder";
import {
  IFolderRepository,
  FolderSearchParams,
  FolderWithContractCount,
} from "../../domain/folders/interfaces/IFolderRepository";
import { logger } from "../logging/logger";

export class FolderRepository implements IFolderRepository {
  constructor(private prisma: PrismaClient) {}

  async create(folder: Folder): Promise<Folder> {
    try {
      const id = uuidv4();
      const folderData = {
        id,
        name: folder.name,
        tenantId: folder.tenantId,
        createdById: folder.createdById,
      };

      const created = await this.prisma.folder.create({
        data: folderData,
      });

      return new Folder({
        id: created.id,
        name: created.name,
        createdAt: created.createdAt,
        updatedAt: created.updatedAt,
        tenantId: created.tenantId,
        createdById: created.createdById || undefined,
      });
    } catch (error) {
      logger.error("Error creating folder:", { error, folder: folder.toDTO() });
      throw new Error(`Failed to create folder: ${(error as Error).message}`);
    }
  }

  async findById(id: string): Promise<Folder | null> {
    try {
      const folder = await this.prisma.folder.findUnique({
        where: { id },
      });

      if (!folder) {
        return null;
      }

      return new Folder({
        id: folder.id,
        name: folder.name,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
        tenantId: folder.tenantId,
        createdById: folder.createdById || undefined,
      });
    } catch (error) {
      logger.error("Error finding folder by ID:", { error, id });
      throw new Error(`Failed to find folder: ${(error as Error).message}`);
    }
  }

  async findMany(params: FolderSearchParams): Promise<Folder[]> {
    try {
      const where: any = {
        tenantId: params.tenantId,
      };

      if (params.name) {
        where.name = {
          contains: params.name,
          mode: "insensitive",
        };
      }

      if (params.createdById) {
        where.createdById = params.createdById;
      }

      const folders = await this.prisma.folder.findMany({
        where,
        orderBy: { name: "asc" },
        skip:
          params.page && params.limit ? params.page * params.limit : undefined,
        take: params.limit,
      });

      return folders.map(
        (folder) =>
          new Folder({
            id: folder.id,
            name: folder.name,
            createdAt: folder.createdAt,
            updatedAt: folder.updatedAt,
            tenantId: folder.tenantId,
            createdById: folder.createdById || undefined,
          })
      );
    } catch (error) {
      logger.error("Error finding folders:", { error, params });
      throw new Error(`Failed to find folders: ${(error as Error).message}`);
    }
  }

  async findManyWithContractCount(
    params: FolderSearchParams
  ): Promise<FolderWithContractCount[]> {
    try {
      const where: any = {
        tenantId: params.tenantId,
      };

      if (params.name) {
        where.name = {
          contains: params.name,
          mode: "insensitive",
        };
      }

      if (params.createdById) {
        where.createdById = params.createdById;
      }

      const folders = await this.prisma.folder.findMany({
        where,
        include: {
          _count: {
            select: { contracts: true },
          },
          contracts: {
            orderBy: { updatedAt: "desc" },
          },
        },
        orderBy: { name: "asc" },
        skip:
          params.page && params.limit ? params.page * params.limit : undefined,
        take: params.limit,
      });

      // Transform folders to domain entities with basic contract data
      const transformedFolders = folders.map((folder) => {
        const basicContracts = folder.contracts.map((contract) => ({
          ...contract,
          createdAt: contract.createdAt.toISOString(),
          updatedAt: contract.updatedAt.toISOString(),
          startDate: contract.startDate?.toISOString() || null,
          endDate: contract.endDate?.toISOString() || null,
          renewalDate: contract.renewalDate?.toISOString() || null,
        }));

        return {
          folder: new Folder({
            id: folder.id,
            name: folder.name,
            createdAt: folder.createdAt,
            updatedAt: folder.updatedAt,
            tenantId: folder.tenantId,
            createdById: folder.createdById || undefined,
          }),
          contractCount: folder._count.contracts,
          contracts: basicContracts,
        };
      });

      return transformedFolders;
    } catch (error) {
      logger.error("Error finding folders with contract count:", {
        error,
        params,
      });
      throw new Error(
        `Failed to find folders with contract count: ${
          (error as Error).message
        }`
      );
    }
  }

  async update(
    id: string,
    folderData: Partial<Folder>
  ): Promise<Folder | null> {
    try {
      const updateData: any = {};

      if (folderData.name !== undefined) {
        updateData.name = folderData.name;
      }

      const updated = await this.prisma.folder.update({
        where: { id },
        data: updateData,
      });

      return new Folder({
        id: updated.id,
        name: updated.name,
        createdAt: updated.createdAt,
        updatedAt: updated.updatedAt,
        tenantId: updated.tenantId,
        createdById: updated.createdById || undefined,
      });
    } catch (error) {
      if ((error as any).code === "P2025") {
        return null; // Record not found
      }
      logger.error("Error updating folder:", { error, id, folderData });
      throw new Error(`Failed to update folder: ${(error as Error).message}`);
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      await this.prisma.folder.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      if ((error as any).code === "P2025") {
        return false; // Record not found
      }
      logger.error("Error deleting folder:", { error, id });
      throw new Error(`Failed to delete folder: ${(error as Error).message}`);
    }
  }

  async nameExists(
    name: string,
    tenantId: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      const where: any = {
        name: {
          equals: name,
          mode: "insensitive",
        },
        tenantId,
      };

      if (excludeId) {
        where.id = {
          not: excludeId,
        };
      }

      const folder = await this.prisma.folder.findFirst({
        where,
      });

      return folder !== null;
    } catch (error) {
      logger.error("Error checking folder name existence:", {
        error,
        name,
        tenantId,
        excludeId,
      });
      throw new Error(
        `Failed to check folder name existence: ${(error as Error).message}`
      );
    }
  }

  async count(params: FolderSearchParams): Promise<number> {
    try {
      const where: any = {
        tenantId: params.tenantId,
      };

      if (params.name) {
        where.name = {
          contains: params.name,
          mode: "insensitive",
        };
      }

      if (params.createdById) {
        where.createdById = params.createdById;
      }

      return await this.prisma.folder.count({ where });
    } catch (error) {
      logger.error("Error counting folders:", { error, params });
      throw new Error(`Failed to count folders: ${(error as Error).message}`);
    }
  }

  async moveContractsToFolder(
    contractIds: string[],
    folderId: string | null,
    tenantId: string
  ): Promise<number> {
    try {
      // Use a transaction to update both Contract and ContractExtraction tables
      const result = await this.prisma.$transaction(async (tx) => {
        // Update the Contract table
        const contractResult = await tx.contract.updateMany({
          where: {
            id: { in: contractIds },
            tenantId, // Ensure contracts belong to the tenant
          },
          data: {
            folderId,
          },
        });

        // Update the ContractExtraction table to keep it in sync
        await tx.contractExtraction.updateMany({
          where: {
            contractId: { in: contractIds },
            tenantId, // Ensure extractions belong to the tenant
          },
          data: {
            folderId,
          },
        });

        return contractResult;
      });

      logger.info("Successfully moved contracts to folder:", {
        contractIds,
        folderId,
        tenantId,
        movedCount: result.count,
      });

      return result.count;
    } catch (error) {
      logger.error("Error moving contracts to folder:", {
        error,
        contractIds,
        folderId,
        tenantId,
      });
      throw new Error(
        `Failed to move contracts to folder: ${(error as Error).message}`
      );
    }
  }
}
