/**
 * User Service
 * Provides functionality for user management within an organization
 */

import * as crypto from "crypto";
import { PrismaClient, User } from "@prisma/client";
import { logger } from "../logging/logger";
import { PrismaService } from "./PrismaService";

/**
 * Interface for creating a user
 */
export interface CreateUserInput {
  email: string;
  name: string;
  password: string;
  tenantId: string;
  roleId?: string;
  createdBy?: string;
}

/**
 * Interface for updating a user
 */
export interface UpdateUserInput {
  name?: string;
  email?: string;
  status?: string;
  roleId?: string;
}

/**
 * Service for user management operations
 */
export class UserService {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || PrismaService.getInstance();
  }

  /**
   * Get all users in a tenant
   * @param tenantId Tenant ID
   * @returns Array of users
   */
  async getUsersByTenant(tenantId: string): Promise<any[]> {
    try {
      const tenantUsers = await this.prisma.tenantUser.findMany({
        where: { tenantId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              role: true,
              createdAt: true,
              updatedAt: true,
              createdBy: true,
            },
          },
          role: true,
        },
      });

      // Transform the data to a more usable format
      return tenantUsers.map((tu) => ({
        id: tu.user.id,
        email: tu.user.email,
        name: tu.user.name,
        status: tu.user.status,
        systemRole: tu.user.role,
        tenantRole: tu.tenantRole,
        role: tu.role,
        createdAt: tu.user.createdAt,
        updatedAt: tu.user.updatedAt,
        createdBy: tu.user.createdBy,
        isActive: tu.isActive,
      }));
    } catch (error) {
      logger.error("Get users by tenant error:", { error, tenantId });
      throw new Error(`Failed to get users: ${(error as Error).message}`);
    }
  }

  /**
   * Get a user by ID within a tenant
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns User or null if not found
   */
  async getUserById(userId: string, tenantId: string): Promise<any | null> {
    try {
      const tenantUser = await this.prisma.tenantUser.findFirst({
        where: {
          userId,
          tenantId,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              status: true,
              role: true,
              createdAt: true,
              updatedAt: true,
              createdBy: true,
            },
          },
          role: true,
        },
      });

      if (!tenantUser) {
        return null;
      }

      return {
        id: tenantUser.user.id,
        email: tenantUser.user.email,
        name: tenantUser.user.name,
        status: tenantUser.user.status,
        systemRole: tenantUser.user.role,
        tenantRole: tenantUser.tenantRole,
        role: tenantUser.role,
        createdAt: tenantUser.user.createdAt,
        updatedAt: tenantUser.user.updatedAt,
        createdBy: tenantUser.user.createdBy,
        isActive: tenantUser.isActive,
      };
    } catch (error) {
      logger.error("Get user by ID error:", { error, userId, tenantId });
      throw new Error(`Failed to get user: ${(error as Error).message}`);
    }
  }

  /**
   * Create a new user in a tenant
   * @param input User creation input
   * @returns Created user
   */
  async createUser(input: CreateUserInput): Promise<User> {
    try {
      const { email, name, password, tenantId, roleId, createdBy } = input;

      // Generate password hash and salt
      const salt = crypto.randomBytes(16).toString("hex");
      const passwordHash = await this.hashPassword(password, salt);

      // Start a transaction
      return await this.prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email,
            name,
            passwordHash,
            passwordSalt: salt,
            status: "ACTIVE",
            role: "USER", // Default system role
            createdBy,
          },
        });

        // Associate user with tenant
        await tx.tenantUser.create({
          data: {
            userId: user.id,
            tenantId,
            tenantRole: "USER", // Default tenant role
            roleId, // Custom role if provided
            createdBy,
          },
        });

        return user;
      });
    } catch (error) {
      logger.error("Create user error:", { error, input });
      throw new Error(`User creation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Update a user
   * @param userId User ID
   * @param input Update input
   * @returns Updated user
   */
  async updateUser(userId: string, input: UpdateUserInput): Promise<any> {
    try {
      const { name, email, status, roleId } = input;

      // Start a transaction
      return await this.prisma.$transaction(async (tx) => {
        // Update user
        const userUpdateData: any = {};
        if (name) userUpdateData.name = name;
        if (email) userUpdateData.email = email;
        if (status) userUpdateData.status = status;

        if (Object.keys(userUpdateData).length > 0) {
          await tx.user.update({
            where: { id: userId },
            data: userUpdateData,
          });
        }

        // Update tenant user role if provided
        if (roleId) {
          await tx.tenantUser.updateMany({
            where: { userId },
            data: { roleId },
          });
        }

        // Get updated user
        const updatedUser = await tx.user.findUnique({
          where: { id: userId },
          include: {
            tenantUsers: {
              include: {
                role: true,
              },
            },
          },
        });

        return updatedUser;
      });
    } catch (error) {
      logger.error("Update user error:", { error, userId, input });
      throw new Error(`User update failed: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a user from a tenant
   * @param userId User ID
   * @param tenantId Tenant ID
   */
  async deleteUser(userId: string, tenantId: string): Promise<void> {
    try {
      // Start a transaction
      await this.prisma.$transaction(async (tx) => {
        // Delete tenant user association
        await tx.tenantUser.deleteMany({
          where: {
            userId,
            tenantId,
          },
        });

        // Check if user has any other tenant associations
        const otherTenantUsers = await tx.tenantUser.findFirst({
          where: { userId },
        });

        // If no other tenant associations, delete the user
        if (!otherTenantUsers) {
          await tx.user.delete({
            where: { id: userId },
          });
        }
      });
    } catch (error) {
      logger.error("Delete user error:", { error, userId, tenantId });
      throw new Error(`User deletion failed: ${(error as Error).message}`);
    }
  }

  /**
   * Hashes a password with a salt
   * @param password Password to hash
   * @param salt Salt to use
   * @returns Hashed password
   */
  private async hashPassword(password: string, salt: string): Promise<string> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(password, salt, 10000, 64, "sha512", (err, derivedKey) => {
        if (err) {
          reject(err);
        }
        resolve(derivedKey.toString("hex"));
      });
    });
  }
}
