/**
 * Prisma Service
 * Provides a singleton instance of PrismaClient with proper configuration
 */

import { PrismaClient } from "@prisma/client";
import { ConfigService } from "./ConfigService";
import * as dotenv from "dotenv";
import * as path from "path";

// Load environment variables from local .env file
dotenv.config();

export class PrismaService {
  private static instance: PrismaClient;
  private static configService: ConfigService;

  /**
   * Gets the singleton instance of PrismaClient
   * @returns PrismaClient instance
   */
  public static getInstance(): PrismaClient {
    if (!PrismaService.instance) {
      PrismaService.configService = new ConfigService();

      // Set DATABASE_URL explicitly
      const dbUrl =
        process.env.DATABASE_URL ||
        "postgresql://postgres:postgres@localhost:5432/clm_dev";

      // Create PrismaClient instance
      PrismaService.instance = new PrismaClient({
        datasources: {
          db: {
            url: dbUrl,
          },
        },
      });

      console.log("PrismaClient initialized with DATABASE_URL:", dbUrl);
    }

    return PrismaService.instance;
  }
}
