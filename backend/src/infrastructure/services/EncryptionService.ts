/**
 * Encryption Service
 * Handles encryption and decryption operations for sensitive data using AES-256
 * with proper key management and secure practices
 */

import crypto from 'crypto';
import { logger } from '../logging/logger';
import { ConfigService } from './ConfigService';

/**
 * EncryptionService provides methods for encrypting and decrypting data
 * using AES-256 encryption algorithm with proper key management
 */
export class EncryptionService {
  private readonly algorithm: string = 'aes-256-gcm';
  private readonly keyLength: number = 32; // 256 bits
  private readonly ivLength: number = 16; // Initialization vector length
  private readonly authTagLength: number = 16; // Authentication tag length
  private readonly configService: ConfigService;
  private keyCache: Map<string, Buffer> = new Map();

  /**
   * Constructor
   * @param configService Configuration service for accessing encryption keys
   */
  constructor(configService: ConfigService) {
    this.configService = configService;
  }

  /**
   * Encrypts data using AES-256-GCM
   * @param data Data to encrypt
   * @param keyId Optional key identifier (for multiple keys scenario)
   * @returns Encrypted data with IV and auth tag
   * @throws Error if encryption fails
   */
  async encrypt(data: string, keyId?: string): Promise<{
    encryptedData: string;
    keyId: string;
  }> {
    try {
      // In a real implementation, key management would be more sophisticated
      // For example, using AWS KMS, HashiCorp Vault, or a key management service
      const key = this.getEncryptionKey();

      // Generate a random initialization vector
      const iv = crypto.randomBytes(this.ivLength);

      // Create cipher
      const cipher = crypto.createCipheriv(this.algorithm, key, iv) as crypto.CipherGCM;

      // Encrypt the data
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Get authentication tag
      const authTag = cipher.getAuthTag();

      // Combine IV, encrypted data, and auth tag for storage
      // Format: iv:authTag:encryptedData
      const encryptedResult = Buffer.concat([
        iv,
        authTag,
        Buffer.from(encrypted, 'hex')
      ]).toString('base64');

      // Return the encrypted data and a key identifier
      // In a real system, this would be a reference to the actual key used
      return {
        encryptedData: encryptedResult,
        keyId: keyId || 'default',
      };
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypts data encrypted with AES-256-GCM
   * @param encryptedData Encrypted data with IV and auth tag
   * @param keyId Optional key identifier
   * @returns Decrypted data as string
   * @throws Error if decryption fails
   */
  async decrypt(encryptedData: string, keyId?: string): Promise<string> {
    try {
      // Get the encryption key
      const key = this.getEncryptionKey(keyId);

      // Convert the combined data back to a buffer
      const data = Buffer.from(encryptedData, 'base64');

      // Extract the IV, auth tag, and encrypted data
      const iv = data.subarray(0, this.ivLength);
      const authTag = data.subarray(this.ivLength, this.ivLength + this.authTagLength);
      const encrypted = data.subarray(this.ivLength + this.authTagLength).toString('hex');

      // Create decipher
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv) as crypto.DecipherGCM;

      // Set authentication tag
      decipher.setAuthTag(authTag);

      // Decrypt the data
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Gets the encryption key from configuration service
   * @param keyId Optional key identifier
   * @returns Buffer containing the encryption key
   * @throws Error if key is not available
   */
  private getEncryptionKey(keyId?: string): Buffer {
    // Use default key if no key ID provided
    const useKeyId = keyId || 'default';

    // Check if key is in cache
    if (this.keyCache.has(useKeyId)) {
      return this.keyCache.get(useKeyId)!;
    }

    // Get key from configuration
    let keyHex: string;

    if (useKeyId === 'default') {
      // Get default key from configuration
      keyHex = this.configService.getEncryptionKey();

      if (!keyHex) {
        logger.error('Default encryption key not found in configuration');
        throw new Error('Encryption key not found in configuration');
      }
    } else {
      // In a real implementation, this would fetch from a key management system
      // For now, we'll derive a key from the default key and key ID
      const defaultKey = this.configService.getEncryptionKey();

      if (!defaultKey) {
        logger.error('Default encryption key not found in configuration');
        throw new Error('Encryption key not found in configuration');
      }

      keyHex = crypto.createHmac('sha256', defaultKey)
        .update(useKeyId)
        .digest('hex');
    }

    // Use a key derivation function to get a key of the right length
    const key = crypto.scryptSync(keyHex, 'salt', this.keyLength);

    // Cache key
    this.keyCache.set(useKeyId, key);

    return key;
  }

  /**
   * Generates a secure random key that can be used for encryption
   * This is primarily for development/testing purposes
   * @returns Base64 encoded random key
   */
  generateKey(): string {
    return crypto.randomBytes(this.keyLength).toString('base64');
  }

  /**
   * Creates a hash of data for integrity verification
   * @param data Data to hash
   * @returns SHA-256 hash of the data
   */
  createHash(data: Buffer | string): string {
    return crypto
      .createHash('sha256')
      .update(data)
      .digest('hex');
  }

  /**
   * Verifies data integrity by comparing hash
   * @param data Data to verify
   * @param hash Previously calculated hash
   * @returns Boolean indicating if the hash matches
   */
  verifyHash(data: Buffer | string, hash: string): boolean {
    const calculatedHash = this.createHash(data);
    return calculatedHash === hash;
  }
}