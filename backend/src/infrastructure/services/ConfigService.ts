/**
 * Configuration Service
 * Provides secure access to configuration values and environment variables
 */

import * as dotenv from "dotenv";
import * as fs from "fs";
import * as crypto from "crypto";

/**
 * Service for accessing configuration values
 */
export class ConfigService {
  private config: Record<string, string> = {};
  private initialized = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initializes the configuration service
   */
  private initialize(): void {
    try {
      // Load environment variables from local .env file
      dotenv.config();

      // Set default values for required configuration
      this.config = {
        NODE_ENV: process.env.NODE_ENV || "development",
        PORT: process.env.PORT || "3001",
        DATABASE_URL: process.env.DATABASE_URL || "",
        ACCESS_TOKEN_SECRET:
          process.env.ACCESS_TOKEN_SECRET ||
          process.env.JWT_SECRET ||
          crypto.randomBytes(64).toString("hex"),
        REFRESH_TOKEN_SECRET:
          process.env.REFRESH_TOKEN_SECRET ||
          process.env.JWT_REFRESH_SECRET ||
          crypto.randomBytes(64).toString("hex"),
        ENCRYPTION_KEY:
          process.env.ENCRYPTION_KEY || crypto.randomBytes(32).toString("hex"),
        ENCRYPTION_IV:
          process.env.ENCRYPTION_IV || crypto.randomBytes(16).toString("hex"),
      };

      this.initialized = true;
      console.log("Configuration service initialized");
    } catch (error) {
      console.error("Failed to initialize configuration service:", error);
      throw new Error(
        `Configuration initialization failed: ${(error as Error).message}`
      );
    }
  }

  /**
   * Validates required configuration values
   * This is a simplified version that doesn't throw errors
   */
  private validateConfig(): void {
    const requiredKeys = [
      "DATABASE_URL",
      "ACCESS_TOKEN_SECRET",
      "REFRESH_TOKEN_SECRET",
      "ENCRYPTION_KEY",
    ];

    for (const key of requiredKeys) {
      if (!this.config[key]) {
        if (this.config.NODE_ENV === "production") {
          console.warn(
            `Required configuration key ${key} is missing in production`
          );
        } else {
          console.warn(
            `Configuration key ${key} is missing, using generated value`
          );
        }
      }
    }
  }

  /**
   * Gets a configuration value
   * @param key Configuration key
   * @returns Configuration value
   */
  get(key: string): string {
    if (!this.initialized) {
      this.initialize();
    }
    return this.config[key] || process.env[key] || "";
  }

  /**
   * Gets the access token secret
   * @returns Access token secret
   */
  getAccessTokenSecret(): string {
    return this.get("ACCESS_TOKEN_SECRET") || this.get("JWT_SECRET") || "";
  }

  /**
   * Gets the refresh token secret
   * @returns Refresh token secret
   */
  getRefreshTokenSecret(): string {
    return (
      this.get("REFRESH_TOKEN_SECRET") || this.get("JWT_REFRESH_SECRET") || ""
    );
  }

  /**
   * Gets the encryption key
   * @returns Encryption key
   */
  getEncryptionKey(): string {
    return this.get("ENCRYPTION_KEY");
  }

  /**
   * Gets the encryption initialization vector
   * @returns Encryption IV
   */
  getEncryptionIV(): string {
    return this.get("ENCRYPTION_IV");
  }

  /**
   * Gets the database URL
   * @returns Database URL
   */
  getDatabaseUrl(): string {
    return this.get("DATABASE_URL");
  }

  /**
   * Gets the port
   * @returns Port
   */
  getPort(): number {
    return parseInt(this.get("PORT") || "3000", 10);
  }

  /**
   * Checks if the environment is production
   * @returns Whether the environment is production
   */
  isProduction(): boolean {
    return this.get("NODE_ENV") === "production";
  }

  /**
   * Checks if the environment is development
   * @returns Whether the environment is development
   */
  isDevelopment(): boolean {
    return this.get("NODE_ENV") === "development";
  }

  /**
   * Checks if the environment is test
   * @returns Whether the environment is test
   */
  isTest(): boolean {
    return this.get("NODE_ENV") === "test";
  }

  /**
   * Gets the trust proxy setting for Express
   * @returns Trust proxy configuration
   */
  getTrustProxy(): boolean | number {
    if (this.isProduction()) {
      // In production, trust only the first proxy (nginx/load balancer)
      return 1;
    }
    // In development, trust all proxies for flexibility
    return true;
  }

  /**
   * Gets rate limiting configuration
   * @returns Rate limiting configuration object
   */
  getRateLimitConfig() {
    return {
      windowMs: parseInt(this.get("RATE_LIMIT_WINDOW_MS") || "900000", 10), // 15 minutes
      maxRequests: parseInt(this.get("RATE_LIMIT_MAX_REQUESTS") || "100", 10), // 100 requests
      skipSuccessfulRequests: this.get("RATE_LIMIT_SKIP_SUCCESSFUL") === "true",
      skipFailedRequests: this.get("RATE_LIMIT_SKIP_FAILED") === "true",
    };
  }

  /**
   * Gets logging configuration
   * @returns Logging configuration
   */
  getLogConfig() {
    return {
      level: this.get("LOG_LEVEL") || "info",
      enableHealthChecks: this.get("LOG_HEALTH_CHECKS") === "true",
      enableRequestLogging: this.get("LOG_REQUESTS") !== "false", // Default to true
    };
  }

  /**
   * Gets the current environment
   * @returns The current environment (development, production, test)
   */
  getEnvironment(): string {
    return this.get("NODE_ENV") || "development";
  }
}
