/**
 * Authentication Service
 * Provides secure authentication functionality with proper token management
 */

import * as jwt from "jsonwebtoken";
import * as crypto from "crypto";
import { PrismaClient, User } from "@prisma/client";
import { logger } from "../logging/logger";
import { ConfigService } from "./ConfigService";
import { PrismaService } from "./PrismaService";

/**
 * Interface for JWT payload
 */
export interface JwtPayload {
  userId: string;
  tenantId: string;
  role: string;
  permissions: string[];
  sessionId: string;
  iat: number;
  exp: number;
}

/**
 * Interface for token response
 */
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Service for authentication operations
 */
export class AuthService {
  private prisma: PrismaClient;
  private configService: ConfigService;

  constructor(prisma?: PrismaClient, configService?: ConfigService) {
    this.prisma = prisma || PrismaService.getInstance();
    this.configService = configService || new ConfigService();
  }

  /**
   * Registers a new user
   * @param email User email
   * @param password User password
   * @param name User name
   * @param tenantId Optional tenant ID (if not provided, a new tenant will be created)
   * @param company Optional company name for the tenant
   * @param plan Optional plan/tier for the tenant
   * @returns Created user
   */
  async registerUser(
    email: string,
    password: string,
    name: string,
    tenantId?: string,
    company?: string,
    plan?: string
  ): Promise<User> {
    try {
      // Generate password hash and salt
      const salt = crypto.randomBytes(16).toString("hex");
      const passwordHash = await this.hashPassword(password, salt);

      // Start a transaction
      return await this.prisma.$transaction(async (tx) => {
        // Create user with proper schema fields
        const userId = crypto.randomUUID();

        // Create user
        const user = await tx.user.create({
          data: {
            id: userId,
            email,
            name,
            passwordHash,
            passwordSalt: salt,
            status: "ACTIVE",
            role: "USER",
          },
        });

        // Handle tenant association
        let userTenantId = tenantId;
        let isNewTenant = false;

        // If no tenant ID provided, create a new tenant
        if (!userTenantId) {
          const tenantName = company || `${name}'s Organization`;
          const tenantTier = plan?.toUpperCase() || "FREE";

          const tenant = await tx.tenant.create({
            data: {
              name: tenantName,
              tier: tenantTier,
              status: "ACTIVE",
            },
          });
          userTenantId = tenant.id;
          isNewTenant = true;
        }

        // Check if this is the first user in the tenant
        const existingUsers = await tx.tenantUser.count({
          where: { tenantId: userTenantId },
        });

        const isFirstUser = existingUsers === 0;

        // If this is a new tenant or the first user, create default roles
        if (isNewTenant || isFirstUser) {
          try {
            // 🔍 DEBUGGING: Log Prisma Client information
            logger.info("🔍 PRISMA CLIENT DEBUG - Starting role creation", {
              prismaVersion: require("@prisma/client/package.json").version,
              userTenantId,
              isNewTenant,
              isFirstUser,
              timestamp: new Date().toISOString(),
            });

            // 🔍 DEBUGGING: Log transaction context
            logger.info("🔍 TRANSACTION DEBUG - Transaction context", {
              transactionType: typeof tx,
              availableMethods: Object.getOwnPropertyNames(tx).filter(
                (prop) => typeof (tx as any)[prop] === "object"
              ),
              roleModelExists: !!tx.role,
              roleCreateExists: !!(tx.role && tx.role.create),
              timestamp: new Date().toISOString(),
            });

            // Create default roles directly using the transaction
            // Check if roles already exist for this tenant
            logger.info("🔍 ROLE CHECK - Checking existing roles", {
              userTenantId,
              timestamp: new Date().toISOString(),
            });

            const existingRoles = await tx.role.findMany({
              where: { tenantId: userTenantId },
            });

            logger.info("🔍 ROLE CHECK - Existing roles found", {
              existingRolesCount: existingRoles.length,
              existingRoleNames: existingRoles.map((r) => r.name),
              userTenantId,
              timestamp: new Date().toISOString(),
            });

            if (existingRoles.length === 0) {
              // 🔍 DEBUGGING: Log tenant verification before role creation
              logger.info("🔍 TENANT VERIFICATION - Verifying tenant exists", {
                userTenantId,
                timestamp: new Date().toISOString(),
              });

              const tenantExists = await tx.tenant.findUnique({
                where: { id: userTenantId },
              });

              logger.info("🔍 TENANT VERIFICATION - Tenant check result", {
                tenantExists: !!tenantExists,
                tenantId: tenantExists?.id,
                tenantName: tenantExists?.name,
                userTenantId,
                timestamp: new Date().toISOString(),
              });

              // 🔍 DEBUGGING: Log role creation data structure
              const adminRoleData = {
                name: "Admin",
                description: "Full access to all features",
                permissions: [
                  "contracts:read",
                  "contracts:write",
                  "contracts:approve",
                  "licenses:read",
                  "licenses:write",
                  "licenses:approve",
                  "users:read",
                  "users:write",
                  "settings:read",
                  "settings:write",
                ],
                isDefault: true,
                tenant: {
                  connect: { id: userTenantId },
                },
              };

              logger.info("🔍 ROLE CREATION - Admin role data structure", {
                roleData: adminRoleData,
                tenantConnectionId: userTenantId,
                dataStructureKeys: Object.keys(adminRoleData),
                tenantConnectionStructure: adminRoleData.tenant,
                timestamp: new Date().toISOString(),
              });

              // Create Admin role
              logger.info(
                "🔍 ROLE CREATION - Attempting to create Admin role",
                {
                  userTenantId,
                  timestamp: new Date().toISOString(),
                }
              );

              const adminRole = await tx.role.create({
                data: adminRoleData,
              });

              logger.info(
                "🔍 ROLE CREATION - Admin role created successfully",
                {
                  adminRoleId: adminRole.id,
                  adminRoleName: adminRole.name,
                  userTenantId,
                  timestamp: new Date().toISOString(),
                }
              );

              // 🔍 DEBUGGING: Log User role creation data
              const userRoleData = {
                name: "User",
                description: "Standard user with limited access",
                permissions: [
                  "contracts:read",
                  "contracts:write",
                  "licenses:read",
                  "licenses:write",
                ],
                isDefault: false,
                tenant: {
                  connect: { id: userTenantId },
                },
              };

              logger.info("🔍 ROLE CREATION - User role data structure", {
                roleData: userRoleData,
                tenantConnectionId: userTenantId,
                timestamp: new Date().toISOString(),
              });

              // Create User role
              const userRole = await tx.role.create({
                data: userRoleData,
              });

              logger.info("🔍 ROLE CREATION - User role created successfully", {
                userRoleId: userRole.id,
                userRoleName: userRole.name,
                userTenantId,
                timestamp: new Date().toISOString(),
              });

              // 🔍 DEBUGGING: Log Viewer role creation data
              const viewerRoleData = {
                name: "Viewer",
                description: "Read-only access",
                permissions: ["contracts:read", "licenses:read"],
                isDefault: false,
                tenant: {
                  connect: { id: userTenantId },
                },
              };

              logger.info("🔍 ROLE CREATION - Viewer role data structure", {
                roleData: viewerRoleData,
                tenantConnectionId: userTenantId,
                timestamp: new Date().toISOString(),
              });

              // Create Viewer role
              const viewerRole = await tx.role.create({
                data: viewerRoleData,
              });

              logger.info(
                "🔍 ROLE CREATION - Viewer role created successfully",
                {
                  viewerRoleId: viewerRole.id,
                  viewerRoleName: viewerRole.name,
                  userTenantId,
                  timestamp: new Date().toISOString(),
                }
              );

              logger.info(
                "🔍 ROLE CREATION - All default roles created successfully",
                {
                  adminRoleId: adminRole.id,
                  userRoleId: userRole.id,
                  viewerRoleId: viewerRole.id,
                  userTenantId,
                  timestamp: new Date().toISOString(),
                }
              );
            }
          } catch (roleError) {
            // 🔍 DEBUGGING: Enhanced error logging for role creation
            logger.error(
              "❌ ROLE CREATION ERROR - Detailed error information",
              {
                errorMessage:
                  roleError instanceof Error
                    ? roleError.message
                    : String(roleError),
                errorStack:
                  roleError instanceof Error ? roleError.stack : undefined,
                errorName:
                  roleError instanceof Error ? roleError.name : undefined,
                fullError: roleError,
                userTenantId,
                isNewTenant,
                isFirstUser,
                prismaVersion: require("@prisma/client/package.json").version,
                nodeVersion: process.version,
                timestamp: new Date().toISOString(),
              }
            );

            // 🔍 DEBUGGING: Check if this is a Prisma Client type mismatch error
            const errorMessage =
              roleError instanceof Error
                ? roleError.message
                : String(roleError);
            if (
              errorMessage.includes("Argument") &&
              errorMessage.includes("missing")
            ) {
              logger.error("❌ PRISMA TYPE MISMATCH DETECTED", {
                suspectedCause: "Prisma Client generated with outdated schema",
                errorIndicators: {
                  containsArgumentMissing:
                    errorMessage.includes("Argument") &&
                    errorMessage.includes("missing"),
                  containsTenantField: errorMessage.includes("Tenant"),
                  containsCreateMethod: errorMessage.includes("create"),
                },
                recommendedFix:
                  "Run 'npx prisma generate' to regenerate Prisma Client",
                userTenantId,
                timestamp: new Date().toISOString(),
              });
            }

            // Continue with registration even if role creation fails
            logger.warn(
              "⚠️ CONTINUING REGISTRATION - Role creation failed but continuing with user registration",
              {
                userTenantId,
                timestamp: new Date().toISOString(),
              }
            );
          }

          // Get the admin role
          const adminRole = await tx.role.findFirst({
            where: {
              tenantId: userTenantId,
              name: "Admin",
            },
          });

          // Associate user with tenant as admin
          await tx.tenantUser.create({
            data: {
              userId: user.id,
              tenantId: userTenantId,
              tenantRole: "ADMIN", // First user of a tenant is an admin
              roleId: adminRole?.id, // Assign the admin role if it exists
              isActive: true,
            },
          });
        } else {
          // For additional users, assign as regular user
          // Get the user role
          const userRole = await tx.role.findFirst({
            where: {
              tenantId: userTenantId,
              name: "User",
            },
          });

          // Associate user with tenant as regular user
          await tx.tenantUser.create({
            data: {
              userId: user.id,
              tenantId: userTenantId,
              tenantRole: "USER",
              roleId: userRole?.id, // Assign the user role
              isActive: true,
            },
          });
        }

        return user;
      });
    } catch (error) {
      // 🔍 DEBUGGING: Enhanced registration error logging
      logger.error("❌ USER REGISTRATION ERROR - Comprehensive error details", {
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        errorName: error instanceof Error ? error.name : undefined,
        fullError: error,
        registrationContext: {
          email,
          name,
          tenantId,
          company,
          plan,
        },
        prismaVersion: require("@prisma/client/package.json").version,
        nodeVersion: process.version,
        timestamp: new Date().toISOString(),
      });

      // 🔍 DEBUGGING: Check for specific Prisma-related errors
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (
        errorMessage.includes("Prisma") ||
        errorMessage.includes("Invalid") ||
        errorMessage.includes("Argument")
      ) {
        logger.error("❌ PRISMA-RELATED ERROR DETECTED IN REGISTRATION", {
          suspectedPrismaIssue: true,
          errorContainsPrisma: errorMessage.includes("Prisma"),
          errorContainsInvalid: errorMessage.includes("Invalid"),
          errorContainsArgument: errorMessage.includes("Argument"),
          recommendedActions: [
            "Check Prisma Client version",
            "Run 'npx prisma generate'",
            "Verify database schema is up to date",
            "Check for schema/client version mismatch",
          ],
          timestamp: new Date().toISOString(),
        });
      }

      throw new Error(`Registration failed: ${(error as Error).message}`);
    }
  }

  /**
   * Hashes a password with a salt
   * @param password Password to hash
   * @param salt Salt to use
   * @returns Hashed password
   */
  private async hashPassword(password: string, salt: string): Promise<string> {
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(password, salt, 10000, 64, "sha512", (err, derivedKey) => {
        if (err) {
          reject(err);
        }
        resolve(derivedKey.toString("hex"));
      });
    });
  }

  /**
   * Authenticates a user with email and password
   * @param email User email
   * @param password User password
   * @returns Token response if authentication is successful
   */
  async authenticate(email: string, password: string): Promise<any> {
    try {
      logger.info("Authentication attempt", { email: email.toLowerCase() });

      // Find user by email
      // Using raw query to get all fields including passwordHash and passwordSalt
      const users = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM "User" WHERE email = ${email.toLowerCase()} LIMIT 1
      `;

      const user = users[0] as any;

      if (!user) {
        logger.warn("Authentication failed: User not found", {
          email: email.toLowerCase(),
        });
        throw new Error("User not found");
      }

      logger.info("User found, verifying password", {
        userId: user.id,
        email: user.email,
      });

      // Verify password
      const passwordValid = await this.verifyPassword(
        password,
        user.passwordHash,
        user.passwordSalt
      );

      if (!passwordValid) {
        logger.warn("Authentication failed: Invalid password", {
          userId: user.id,
          email: user.email,
        });
        throw new Error("Invalid password");
      }

      // Check if user is active
      if (user.status !== "ACTIVE") {
        logger.warn("Authentication failed: User account not active", {
          userId: user.id,
          email: user.email,
          status: user.status,
        });
        throw new Error("User account is not active");
      }

      logger.info("Password verified, checking user role", {
        userId: user.id,
        role: user.role,
      });

      // Handle super admin users (no tenant association required)
      if (user.role === 'SUPER_ADMIN') {
        logger.info("Super admin authentication", {
          userId: user.id,
          email: user.email,
        });

        // Super admin gets platform-wide permissions
        const permissions = [
          'super_admin:access',
          'platform:read',
          'platform:write',
          'tenants:read',
          'tenants:write',
          'users:read',
          'users:write',
          'analytics:read',
          'system:read',
          'system:write',
          'audit:read'
        ];

        // Create session without tenant ID for super admin
        const sessionId = await this.createSuperAdminSession(user.id);

        // Generate tokens for super admin (use special tenant ID)
        const tokenResponse = this.generateTokens(
          user.id,
          'SUPER_ADMIN', // Special tenant ID for super admin
          user.role,
          permissions,
          sessionId
        );

        logger.info("Super admin authentication successful", {
          userId: user.id,
          sessionId,
        });

        return tokenResponse;
      }

      // Regular user authentication - get tenant information
      logger.info("Regular user authentication, getting tenant information", {
        userId: user.id,
      });

      const tenantUser = await this.prisma.tenantUser.findFirst({
        where: { userId: user.id },
        include: {
          tenant: true,
          role: true,
        },
      });

      if (!tenantUser) {
        logger.error(
          "Authentication failed: User not associated with any tenant",
          {
            userId: user.id,
            email: user.email,
          }
        );
        throw new Error("User is not associated with any tenant");
      }

      if (!tenantUser.tenant) {
        logger.error("Authentication failed: Tenant not found", {
          userId: user.id,
          tenantId: tenantUser.tenantId,
        });
        throw new Error("Associated tenant not found");
      }

      logger.info("Tenant found, getting permissions", {
        userId: user.id,
        tenantId: tenantUser.tenantId,
        tenantRole: tenantUser.tenantRole,
      });

      // Get user permissions
      const permissions = await this.getUserPermissions(
        user.id,
        tenantUser.tenantId
      );

      logger.info("Permissions retrieved, creating session", {
        userId: user.id,
        permissionCount: permissions.length,
      });

      // Create session
      const sessionId = await this.createSession(user.id, tenantUser.tenantId);

      // Generate tokens
      const tokenResponse = this.generateTokens(
        user.id,
        tenantUser.tenantId,
        tenantUser.tenantRole,
        permissions,
        sessionId
      );

      // Create user profile object with additional details
      const userProfile = {
        id: user.id,
        email: user.email,
        name: user.name,
        status: user.status,
        role: user.role,
        persona: user.persona,
        tenantId: tenantUser.tenantId,
        tenantName: tenantUser.tenant.name,
        tenantRole: tenantUser.tenantRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      logger.info("Authentication successful", {
        userId: user.id,
        tenantId: tenantUser.tenantId,
        sessionId,
      });

      // Return tokens and user profile
      return {
        ...tokenResponse,
        userProfile,
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      logger.error("Authentication error:", {
        error:
          error instanceof Error
            ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
            : error,
        email: email.toLowerCase(),
        timestamp: new Date().toISOString(),
      });

      // Re-throw with original message to preserve specific error types
      throw new Error(errorMessage);
    }
  }

  /**
   * Refreshes an access token using a refresh token
   * @param refreshToken Refresh token
   * @returns New token response
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(
        refreshToken,
        this.configService.getRefreshTokenSecret()
      ) as JwtPayload;

      // In a production environment, we would check if the session is valid
      // For now, we'll skip the session check since the UserSession model might not be set up
      // This is a simplified implementation for development purposes
      /*
      const session = await this.prisma.userSession.findUnique({
        where: { id: decoded.sessionId },
      });

      if (!session || session.isRevoked) {
        throw new Error("Invalid session");
      }
      */

      // Get user
      const user = await this.prisma.user.findUnique({
        where: { id: decoded.userId },
        include: {
          tenantUsers: {
            where: { tenantId: decoded.tenantId },
          },
        },
      });

      if (!user || user.tenantUsers.length === 0) {
        throw new Error("User not found");
      }

      // Get user permissions
      const permissions = await this.getUserPermissions(
        user.id,
        decoded.tenantId
      );

      // Generate new tokens
      return this.generateTokens(
        user.id,
        decoded.tenantId,
        decoded.role,
        permissions,
        decoded.sessionId
      );
    } catch (error) {
      logger.error("Token refresh error:", { error });
      throw new Error(`Token refresh failed: ${(error as Error).message}`);
    }
  }

  /**
   * Logs out a user by revoking their session
   * @param userId User ID
   * @param sessionId Session ID
   */
  async logout(userId: string, sessionId: string): Promise<void> {
    try {
      // In a production environment, we would update the session
      // For now, we'll skip the session update since the UserSession model might not be set up
      // This is a simplified implementation for development purposes
      /*
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: { isRevoked: true, revokedAt: new Date() },
      });
      */

      // Just log the logout for now
      logger.info("User logged out", { userId, sessionId });
    } catch (error) {
      logger.error("Logout error:", { error });
      throw new Error(`Logout failed: ${(error as Error).message}`);
    }
  }

  /**
   * Verifies a password against a hash and salt
   * @param password Password to verify
   * @param hash Password hash
   * @param salt Password salt
   * @returns Whether the password is valid
   */
  private async verifyPassword(
    password: string,
    hash: string,
    salt: string
  ): Promise<boolean> {
    try {
      const hashedPassword = await this.hashPassword(password, salt);
      return hashedPassword === hash;
    } catch (error) {
      logger.error("Password verification error:", { error });
      return false;
    }
  }

  /**
   * Creates a new user session
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Session ID
   */
  private async createSession(
    userId: string,
    tenantId: string
  ): Promise<string> {
    // In a production environment, we would create a session in the database
    // For now, we'll generate a unique session ID since the UserSession model might not be set up
    // This is a simplified implementation for development purposes

    // Generate a unique session ID
    const sessionId = crypto.randomUUID();

    // Log the session creation
    logger.info("Session created", { userId, tenantId, sessionId });

    return sessionId;
  }

  /**
   * Creates a new super admin session (no tenant)
   * @param userId User ID
   * @returns Session ID
   */
  private async createSuperAdminSession(userId: string): Promise<string> {
    // Generate a unique session ID
    const sessionId = crypto.randomUUID();

    // Log the session creation
    logger.info("Super admin session created", { userId, sessionId });

    return sessionId;
  }

  /**
   * Gets user permissions
   * @param userId User ID
   * @param tenantId Tenant ID
   * @returns Array of permission strings
   */
  private async getUserPermissions(
    userId: string,
    tenantId: string
  ): Promise<string[]> {
    // Get the tenant user with their role
    const tenantUser = await this.prisma.tenantUser.findFirst({
      where: { userId, tenantId },
      include: { role: true },
    });

    if (!tenantUser) {
      return [];
    }

    // If the user has a custom role with permissions, use those
    if (tenantUser.role && tenantUser.role.permissions) {
      try {
        // The permissions are stored as JSON in the database
        const rolePermissions = tenantUser.role.permissions as string[];

        // Add admin:access permission for ADMIN role
        if (tenantUser.tenantRole === "ADMIN") {
          return [...rolePermissions, "admin:access"];
        }

        return rolePermissions;
      } catch (error) {
        logger.error("Error parsing role permissions:", { error });
      }
    }

    // Fallback to basic permission sets based on tenant role
    switch (tenantUser.tenantRole) {
      case "ADMIN":
        return [
          "contracts:read",
          "contracts:write",
          "contracts:approve",
          "licenses:read",
          "licenses:write",
          "licenses:approve",
          "users:read",
          "users:write",
          "settings:read",
          "settings:write",
          "admin:access",
        ];
      case "USER":
        return [
          "contracts:read",
          "contracts:write",
          "licenses:read",
          "licenses:write",
        ];
      case "VIEWER":
        return ["contracts:read", "licenses:read"];
      default:
        return [];
    }
  }

  /**
   * Generates access and refresh tokens
   * @param userId User ID
   * @param tenantId Tenant ID
   * @param role User role
   * @param permissions User permissions
   * @param sessionId Session ID
   * @returns Token response
   */
  private generateTokens(
    userId: string,
    tenantId: string,
    role: string,
    permissions: string[],
    sessionId: string
  ): TokenResponse {
    // Access token expires in 15 minutes
    const accessTokenExpiry = 15 * 60; // 15 minutes in seconds

    // Create access token
    const accessToken = jwt.sign(
      {
        userId,
        tenantId,
        role,
        permissions,
        sessionId,
        isSuperAdmin: role === 'SUPER_ADMIN',
      },
      this.configService.getAccessTokenSecret(),
      { expiresIn: accessTokenExpiry }
    );

    // Create refresh token (expires in 7 days)
    const refreshToken = jwt.sign(
      {
        userId,
        tenantId,
        role,
        sessionId,
        isSuperAdmin: role === 'SUPER_ADMIN',
      },
      this.configService.getRefreshTokenSecret(),
      { expiresIn: "7d" }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: accessTokenExpiry,
    };
  }


}
