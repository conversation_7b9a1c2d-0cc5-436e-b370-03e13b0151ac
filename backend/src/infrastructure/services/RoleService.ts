/**
 * Role Service
 * Provides functionality for role management within an organization
 */

import { PrismaClient, Role } from "@prisma/client";
import { logger } from "../logging/logger";
import { PrismaService } from "./PrismaService";

/**
 * Interface for creating a role
 */
export interface CreateRoleInput {
  name: string;
  description?: string;
  permissions: string[] | any; // Array of permission strings or JSON
  isDefault: boolean;
  tenantId: string;
}

/**
 * Interface for updating a role
 */
export interface UpdateRoleInput {
  name?: string;
  description?: string;
  permissions?: string[] | any; // Array of permission strings or JSON
  isDefault?: boolean;
}

/**
 * Service for role management operations
 */
export class RoleService {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || PrismaService.getInstance();
  }

  /**
   * Get all roles in a tenant
   * @param tenantId Tenant ID
   * @returns Array of roles
   */
  async getRolesByTenant(tenantId: string): Promise<Role[]> {
    try {
      return await this.prisma.role.findMany({
        where: { tenantId },
      });
    } catch (error) {
      logger.error("Get roles by tenant error:", { error, tenantId });
      throw new Error(`Failed to get roles: ${(error as Error).message}`);
    }
  }

  /**
   * Get a role by ID within a tenant
   * @param roleId Role ID
   * @param tenantId Tenant ID
   * @returns Role or null if not found
   */
  async getRoleById(roleId: string, tenantId: string): Promise<Role | null> {
    try {
      return await this.prisma.role.findFirst({
        where: {
          id: roleId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Get role by ID error:", { error, roleId, tenantId });
      throw new Error(`Failed to get role: ${(error as Error).message}`);
    }
  }

  /**
   * Create a new role in a tenant
   * @param input Role creation input
   * @returns Created role
   */
  async createRole(input: CreateRoleInput): Promise<Role> {
    try {
      const { name, description, permissions, isDefault, tenantId } = input;

      // If default is true, unset default flag on other roles
      if (isDefault) {
        await this.prisma.role.updateMany({
          where: { tenantId, isDefault: true },
          data: { isDefault: false },
        });
      }

      // Ensure permissions is stored as JSON
      const permissionsJson =
        typeof permissions === "string" ? JSON.parse(permissions) : permissions;

      // Create role
      return await this.prisma.role.create({
        data: {
          name,
          description,
          permissions: permissionsJson,
          isDefault,
          tenant: {
            connect: { id: tenantId },
          },
        },
      });
    } catch (error) {
      logger.error("Create role error:", { error, input });
      throw new Error(`Role creation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Update a role
   * @param roleId Role ID
   * @param input Update input
   * @returns Updated role
   */
  async updateRole(roleId: string, input: UpdateRoleInput): Promise<Role> {
    try {
      const { name, description, permissions, isDefault } = input;

      // If setting as default, unset default flag on other roles
      if (isDefault) {
        const role = await this.prisma.role.findUnique({
          where: { id: roleId },
          select: { tenantId: true },
        });

        if (role) {
          await this.prisma.role.updateMany({
            where: {
              tenantId: role.tenantId,
              isDefault: true,
              id: { not: roleId },
            },
            data: { isDefault: false },
          });
        }
      }

      // Ensure permissions is stored as JSON if provided
      let permissionsJson;
      if (permissions) {
        permissionsJson =
          typeof permissions === "string"
            ? JSON.parse(permissions)
            : permissions;
      }

      // Update role
      return await this.prisma.role.update({
        where: { id: roleId },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(permissionsJson && { permissions: permissionsJson }),
          ...(isDefault !== undefined && { isDefault }),
        },
      });
    } catch (error) {
      logger.error("Update role error:", { error, roleId, input });
      throw new Error(`Role update failed: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a role
   * @param roleId Role ID
   * @param tenantId Tenant ID
   */
  async deleteRole(roleId: string, tenantId: string): Promise<void> {
    try {
      await this.prisma.role.deleteMany({
        where: {
          id: roleId,
          tenantId,
        },
      });
    } catch (error) {
      logger.error("Delete role error:", { error, roleId, tenantId });
      throw new Error(`Role deletion failed: ${(error as Error).message}`);
    }
  }

  /**
   * Create default roles for a tenant
   * @param tenantId Tenant ID
   */
  async createDefaultRoles(tenantId: string): Promise<void> {
    try {
      // First, verify that the tenant exists
      const tenant = await this.prisma.tenant.findUnique({
        where: { id: tenantId },
      });

      if (!tenant) {
        throw new Error(`Tenant with ID ${tenantId} not found`);
      }

      // Check if roles already exist for this tenant
      const existingRoles = await this.prisma.role.findMany({
        where: { tenantId },
      });

      if (existingRoles.length > 0) {
        logger.info(
          `Roles already exist for tenant ${tenantId}, skipping creation`
        );
        return;
      }

      // Create Admin role
      await this.createRole({
        name: "Admin",
        description: "Full access to all features",
        permissions: [
          "contracts:read",
          "contracts:write",
          "contracts:approve",
          "licenses:read",
          "licenses:write",
          "licenses:approve",
          "users:read",
          "users:write",
          "settings:read",
          "settings:write",
        ],
        isDefault: true,
        tenantId,
      });

      // Create User role
      await this.createRole({
        name: "User",
        description: "Standard user with limited access",
        permissions: [
          "contracts:read",
          "contracts:write",
          "licenses:read",
          "licenses:write",
        ],
        isDefault: false,
        tenantId,
      });

      // Create Viewer role
      await this.createRole({
        name: "Viewer",
        description: "Read-only access",
        permissions: ["contracts:read", "licenses:read"],
        isDefault: false,
        tenantId,
      });
    } catch (error) {
      logger.error("Create default roles error:", { error, tenantId });
      throw new Error(
        `Failed to create default roles: ${(error as Error).message}`
      );
    }
  }
}
