/**
 * Configuration Service
 * Provides access to application configuration
 */

import * as dotenv from 'dotenv';
import * as path from 'path';
import { logger } from '../logging/logger';

// Load environment variables
dotenv.config();

/**
 * Configuration Service
 * Provides access to application configuration
 */
export class ConfigService {
  private readonly envConfig: { [key: string]: string };

  /**
   * Constructor
   */
  constructor() {
    this.envConfig = process.env as { [key: string]: string };
  }

  /**
   * Gets a configuration value
   * @param key Configuration key
   * @param defaultValue Default value if not found
   * @returns Configuration value
   */
  get(key: string, defaultValue?: string): string {
    return this.envConfig[key] || defaultValue || '';
  }

  /**
   * Gets the database URL
   * @returns Database URL
   */
  getDatabaseUrl(): string {
    return this.get('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/multistrat');
  }

  /**
   * Gets the encryption key
   * @returns Encryption key
   */
  getEncryptionKey(): string {
    return this.get('ENCRYPTION_KEY', 'default-encryption-key-for-development-only');
  }

  /**
   * Gets the JWT secret
   * @returns JWT secret
   */
  getJwtSecret(): string {
    return this.get('JWT_SECRET', 'default-jwt-secret-for-development-only');
  }

  /**
   * Gets the port
   * @returns Port
   */
  getPort(): number {
    return parseInt(this.get('PORT', '5000'), 10);
  }

  /**
   * Gets the environment
   * @returns Environment
   */
  getEnvironment(): string {
    return this.get('NODE_ENV', 'development');
  }

  /**
   * Checks if the environment is production
   * @returns Boolean indicating if the environment is production
   */
  isProduction(): boolean {
    return this.getEnvironment() === 'production';
  }

  /**
   * Gets the OpenAI API key
   * @returns OpenAI API key
   */
  getOpenAIApiKey(): string {
    return this.get('OPENAI_API_KEY', '');
  }

  /**
   * Gets the Anthropic API key
   * @returns Anthropic API key
   */
  getAnthropicApiKey(): string {
    return this.get('ANTHROPIC_API_KEY', '');
  }
}
