import { Request, Response, NextFunction } from "express";
import { AnyZodObject, ZodError } from "zod";
import { logger } from "../logging/logger";

/**
 * Middleware factory for validating requests using Zod schemas
 * @param schema The Zod schema to validate against
 * @returns Express middleware function
 */
export const validate = (schema: AnyZodObject) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate request against schema
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params,
      });

      // If validation succeeds, continue
      next();
    } catch (error) {
      // Log validation errors
      logger.warn(
        {
          path: req.path,
          method: req.method,
          error: error instanceof ZodError ? error.errors : error,
        },
        `Validation error for ${req.method} ${req.path}`
      );

      // Send validation error response
      if (error instanceof ZodError) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.errors.map((err) => ({
            path: err.path.join("."),
            message: err.message,
          })),
        });
      }

      // Pass other errors to the error handler
      next(error);
    }
  };
};
