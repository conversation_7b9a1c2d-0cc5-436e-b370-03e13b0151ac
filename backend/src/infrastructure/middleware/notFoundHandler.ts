import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

/**
 * Middleware to handle 404 Not Found errors
 * This is used as a fallback for any routes that are not explicitly defined
 */
export const notFoundHandler = (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  const message = `Resource not found: ${req.method} ${req.originalUrl}`;
  const error = new AppError(message, 404, true);
  
  next(error);
};