import { Request, Response, NextFunction } from 'express';
import { logger } from '../logging/logger';
import { ZodError } from 'zod';

// Application error class for custom errors
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode: number, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    // Maintain proper stack trace for debugging
    Error.captureStackTrace(this, this.constructor);
  }
}

// Error handler middleware
export const errorHandler = (
  err: Error, 
  req: Request, 
  res: Response, 
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) => {
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errorDetails: any = {};
  let isOperational = false;

  // Custom AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;
    isOperational = err.isOperational;
  } 
  // Zod validation errors
  else if (err instanceof ZodError) {
    statusCode = 400;
    message = 'Validation Error';
    errorDetails = err.errors;
    isOperational = true;
  } 
  // Default Error
  else {
    errorDetails = {
      name: err.name,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    };
  }

  // Log error
  if (isOperational) {
    logger.warn({ 
      message, 
      statusCode,
      path: req.path,
      method: req.method,
      errorDetails
    }, `Operational error: ${message}`);
  } else {
    logger.error({ 
      err,
      path: req.path,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query
    }, `Unhandled error: ${err.message}`);
  }

  // Send response
  res.status(statusCode).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { error: errorDetails })
  });
};