/**
 * Document Analyzer Service
 * Responsible for extracting metadata from Agreement Documents
 * using AI services like LangChain and Vercel AI SDK
 */

import { Decimal } from "@prisma/client/runtime/library";
import { v4 as uuidv4 } from "uuid";
import * as fs from "fs";
import * as path from "path";
import { generateText } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";

import { logger } from "../logging/logger";
import {
  IDocumentAnalyzer,
  DocumentFormat,
  DocumentProcessingOptions,
  DocumentAnalysisResult,
  DocumentProcessingStatus,
  EntityType,
  ExtractedEntity,
  RiskLevel,
} from "./interfaces";
import { ContractMetadata } from "../../domain/contracts/ContractMetadata";
import { sanitizeInput } from "./util/sanitizer";

/**
 * Temporary storage location for documents being processed
 * in an isolated environment
 */
const TEMP_STORAGE_PATH =
  process.env.TEMP_DOCUMENT_PATH || "/tmp/contract-processing";

/**
 * Default processing options if none are provided
 */
const DEFAULT_PROCESSING_OPTIONS: DocumentProcessingOptions = {
  extractText: true,
  extractEntities: true,
  performRiskAnalysis: true,
  generateEmbeddings: false,
  timeoutSeconds: 60,
};

/**
 * Implementation of the DocumentAnalyzer interface
 * Uses AI services to extract metadata from Agreement Documents
 */
export class DocumentAnalyzer implements IDocumentAnalyzer {
  /**
   * Creates a new document analyzer
   */
  constructor() {
    // Ensure temporary storage directory exists
    if (!fs.existsSync(TEMP_STORAGE_PATH)) {
      try {
        fs.mkdirSync(TEMP_STORAGE_PATH, { recursive: true });
      } catch (error) {
        logger.error("Failed to create temporary storage directory", {
          error,
          path: TEMP_STORAGE_PATH,
        });
      }
    }
  }

  /**
   * Analyzes a document and extracts metadata
   *
   * @param documentBuffer - Binary content of the document
   * @param documentFormat - Format of the document
   * @param options - Processing options
   * @returns Analysis results
   */
  async analyzeDocument(
    documentBuffer: Buffer,
    documentFormat: DocumentFormat,
    options?: DocumentProcessingOptions
  ): Promise<DocumentAnalysisResult> {
    const startTime = Date.now();
    const processingOptions = { ...DEFAULT_PROCESSING_OPTIONS, ...options };

    // Create result structure with initial state
    const result: DocumentAnalysisResult = {
      status: DocumentProcessingStatus.PROCESSING,
      processedAt: new Date(),
      metadata: {
        processingTimeMs: 0,
        originalFormat: documentFormat,
      },
    };

    try {
      // Validate inputs
      if (!documentBuffer || documentBuffer.length === 0) {
        throw new Error("Empty document buffer provided");
      }

      // Generate a random filename for isolated processing
      const tempFilePath = path.join(TEMP_STORAGE_PATH, `doc-${uuidv4()}`);

      try {
        // Write document to temporary location for processing
        fs.writeFileSync(tempFilePath, documentBuffer);

        // Process document based on format and options
        let textContent: string | undefined;

        // Extract text from document based on format
        if (processingOptions.extractText) {
          textContent = await this.extractText(tempFilePath, documentFormat);
          result.textContent = textContent;

          // Analyze text content to get stats
          if (textContent) {
            const wordCount = textContent.split(/\s+/).filter(Boolean).length;
            const pageCount = this.estimatePageCount(
              textContent,
              documentFormat
            );

            if (result.metadata) {
              result.metadata.wordCount = wordCount;
              result.metadata.pageCount = pageCount;
            }
          }
        }

        // Extract entities if requested and text is available
        if (processingOptions.extractEntities && textContent) {
          result.entities = await this.extractEntities(textContent);
        }

        // Perform risk analysis if requested and text is available
        if (processingOptions.performRiskAnalysis && textContent) {
          result.riskAnalysis = await this.performRiskAnalysis(textContent);
        }

        // Set final status
        result.status = DocumentProcessingStatus.COMPLETED;
      } catch (error) {
        logger.error("Error processing document", { error });
        result.status = DocumentProcessingStatus.FAILED;
        result.error = (error as Error).message;
      } finally {
        // Clean up temporary file
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
        } catch (cleanupError) {
          logger.warn("Failed to clean up temporary file", {
            error: cleanupError,
            path: tempFilePath,
          });
        }
      }
    } catch (error) {
      logger.error("Document analysis failed", { error });
      result.status = DocumentProcessingStatus.FAILED;
      result.error = (error as Error).message;
    }

    // Calculate processing time
    const endTime = Date.now();
    if (result.metadata) {
      result.metadata.processingTimeMs = endTime - startTime;
    }

    return result;
  }

  /**
   * Converts document analysis results to a domain ContractMetadata object
   *
   * @param analysisResult - Document analysis results
   * @param contractId - ID of the contract to associate metadata with
   * @returns Contract metadata object
   */
  convertToContractMetadata(
    analysisResult: DocumentAnalysisResult,
    contractId: string
  ): ContractMetadata {
    // Create a new metadata object with a generated ID
    const metadataId = uuidv4();

    // Extract dates from entities if available
    let totalValue: Decimal | null = null;
    let currency: string | null = null;
    let paymentTerms: string | null = null;

    // Custom metadata to store additional extracted information
    const customMetadata: Record<string, any> = {};

    // Auto-extracted fields with raw entity data
    const autoExtractedFields: Record<string, any> = {};

    // Process entities if available
    if (analysisResult.entities && analysisResult.entities.length > 0) {
      // Group entities by type for easier processing
      const entitiesByType: Record<string, ExtractedEntity[]> = {};

      analysisResult.entities.forEach((entity) => {
        if (!entitiesByType[entity.type]) {
          entitiesByType[entity.type] = [];
        }
        entitiesByType[entity.type].push(entity);
      });

      // Extract dates
      if (entitiesByType[EntityType.DATE]) {
        // Sort dates by confidence
        const dateEntities = entitiesByType[EntityType.DATE].sort(
          (a, b) => b.confidence - a.confidence
        );

        // Try to identify effective and execution dates
        // This is a simple heuristic - in a real system, this would be more sophisticated
        for (const dateEntity of dateEntities) {
          const dateText = dateEntity.text;
          const dateValue = new Date(dateText);

          if (!isNaN(dateValue.getTime())) {
            // Check context to identify date type
            const metadata = dateEntity.metadata || {};

            // Date context processing removed as effective_date and execution_date fields are no longer used
          }
        }

        // Store all date entities in auto-extracted fields
        autoExtractedFields.dates = dateEntities;
      }

      // Extract monetary values
      if (entitiesByType[EntityType.MONETARY_VALUE]) {
        const monetaryEntities = entitiesByType[EntityType.MONETARY_VALUE].sort(
          (a, b) => b.confidence - a.confidence
        );

        if (monetaryEntities.length > 0) {
          // Take the highest confidence monetary value as the contract value
          const highestConfidenceValue = monetaryEntities[0];

          // Parse the monetary value
          const valueText = highestConfidenceValue.text;
          const currencyMatch = valueText.match(/[A-Z]{3}|\$|€|£|¥/);
          const amountMatch = valueText.match(/[\d,]+(\.\d+)?/);

          if (amountMatch) {
            const amountStr = amountMatch[0].replace(/,/g, "");
            const amount = parseFloat(amountStr);

            if (!isNaN(amount)) {
              totalValue = new Decimal(amount);

              if (currencyMatch) {
                const currencySymbolMap: Record<string, string> = {
                  $: "USD",
                  "€": "EUR",
                  "£": "GBP",
                  "¥": "JPY",
                };

                currency =
                  currencyMatch[0].length === 3
                    ? currencyMatch[0]
                    : currencySymbolMap[currencyMatch[0]] || null;
              }
            }
          }
        }

        // Store all monetary entities in auto-extracted fields
        autoExtractedFields.monetaryValues = monetaryEntities;
      }

      // Extract payment terms
      if (entitiesByType[EntityType.LEGAL_TERM]) {
        const legalTerms = entitiesByType[EntityType.LEGAL_TERM].filter(
          (entity) => {
            const text = entity.text.toLowerCase();
            return (
              text.includes("payment") ||
              text.includes("net") ||
              text.includes("due") ||
              text.includes("term")
            );
          }
        );

        if (legalTerms.length > 0) {
          // Sort by confidence and take highest
          const highestConfidenceTerm = legalTerms.sort(
            (a, b) => b.confidence - a.confidence
          )[0];

          paymentTerms = highestConfidenceTerm.text;
        }

        // Store all legal terms in auto-extracted fields
        autoExtractedFields.legalTerms = entitiesByType[EntityType.LEGAL_TERM];
      }

      // Store obligations in auto-extracted fields
      if (entitiesByType[EntityType.OBLIGATION]) {
        autoExtractedFields.obligations = entitiesByType[EntityType.OBLIGATION];
      }

      // Store organizations in auto-extracted fields
      if (entitiesByType[EntityType.ORGANIZATION]) {
        autoExtractedFields.organizations =
          entitiesByType[EntityType.ORGANIZATION];
      }
    }

    // Include risk analysis in custom metadata if available
    if (analysisResult.riskAnalysis) {
      customMetadata.riskAnalysis = analysisResult.riskAnalysis;
    }

    // Include document processing metadata
    if (analysisResult.metadata) {
      customMetadata.documentMetadata = analysisResult.metadata;
    }

    // Create ContractMetadata instance
    return new ContractMetadata({
      id: metadataId,
      contractId,

      totalValue,
      currency,
      paymentTerms,
      autoExtractedFields,
      customMetadata,
      lastExtractedAt: new Date(),
    });
  }

  /**
   * Extracts text content from a document
   *
   * @param filePath - Path to the temporary document file
   * @param format - Format of the document
   * @returns Extracted text content
   * @private
   */
  private async extractText(
    filePath: string,
    format: DocumentFormat
  ): Promise<string> {
    // Read file for processing
    const fileBuffer = fs.readFileSync(filePath);

    try {
      // For plain text files, just read the content
      if (format === DocumentFormat.TEXT) {
        return fileBuffer.toString("utf-8");
      }

      // For other formats, use Vercel AI SDK to extract text
      // This leverages Claude 3.5 Sonnet for document processing
      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Extract all the text content from this document. Preserve paragraph structure. Don't summarize, just extract the raw text.",
              },
              {
                type: "file",
                data: fileBuffer,
                mimeType: format,
              },
            ],
          },
        ],
      });

      // Return extracted text
      return result.text;
    } catch (error) {
      logger.error("Text extraction failed", { error, format });
      throw new Error(`Failed to extract text: ${(error as Error).message}`);
    }
  }

  /**
   * Extracts entities from document text
   *
   * @param text - Document text content
   * @returns Array of extracted entities
   * @private
   */
  private async extractEntities(text: string): Promise<ExtractedEntity[]> {
    try {
      // Sanitize the input text
      const sanitizedText = sanitizeInput(text);

      // Split text for processing if it's very large
      let processableText = sanitizedText;
      if (sanitizedText.length > 20000) {
        // Use LangChain text splitter to break into chunks
        const textSplitter = new RecursiveCharacterTextSplitter({
          chunkSize: 18000,
          chunkOverlap: 1000,
        });

        const chunks = await textSplitter.splitText(sanitizedText);
        // We'll just use the first chunk for entity extraction in this version
        processableText = chunks[0];
      }

      // Use AI to extract entities
      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `
                Extract the following entities from this Agreement Document. 
                Return the results as a JSON array containing objects with fields: type, text, confidence, metadata.
                Entity types to extract:
                - DATE: Any dates mentioned (effective dates, execution dates, termination dates, etc.)
                - MONETARY_VALUE: Contract values, payment amounts, fees, etc.
                - ORGANIZATION: Company names, legal entities, etc.
                - PERSON: Names of individuals
                - LEGAL_TERM: Key legal terms, especially payment terms
                - OBLIGATION: Contractual obligations, requirements, duties
                
                For each entity, include contextual information in the metadata field.
                
                Here's the contract text:
                ${processableText}
                `,
              },
            ],
          },
        ],
      });

      // Parse the JSON response
      const responseText = result.text;
      // Extract JSON array from response
      const jsonMatch = responseText.match(/\[\s*\{.*\}\s*\]/s);

      if (!jsonMatch) {
        throw new Error("Could not parse entity extraction results");
      }

      const jsonStr = jsonMatch[0];
      const entities = JSON.parse(jsonStr) as ExtractedEntity[];

      // Validate and transform entities
      return entities.map((entity) => ({
        type: entity.type as EntityType,
        text: entity.text,
        confidence: entity.confidence || 0.8, // Default confidence if not provided
        metadata: entity.metadata || {},
        position: entity.position,
      }));
    } catch (error) {
      logger.error("Entity extraction failed", { error });
      // Return empty array instead of failing the entire process
      return [];
    }
  }

  /**
   * Performs risk analysis on document text
   *
   * @param text - Document text content
   * @returns Risk analysis results
   * @private
   */
  private async performRiskAnalysis(text: string): Promise<{
    riskLevel: RiskLevel;
    riskFactors: string[];
    confidenceScore: number;
  }> {
    try {
      // Sanitize the input text
      const sanitizedText = sanitizeInput(text);

      // Get a summary of the text for risk analysis
      let textForAnalysis = sanitizedText;
      if (sanitizedText.length > 20000) {
        // Use AI to generate a summary for long documents
        const summaryResult = await generateText({
          model: anthropic("claude-3-5-sonnet-20241022"),
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: `
                  Summarize this Agreement Document, focusing on key clauses, obligations, liabilities, 
                  termination conditions, payment terms, and any potential risks.
                  
                  Contract:
                  ${sanitizedText.substring(0, 20000)}
                  `,
                },
              ],
            },
          ],
        });

        textForAnalysis = summaryResult.text;
      }

      // Perform risk analysis using AI
      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `
                Analyze this contract text for potential legal, financial, and operational risks.
                Evaluate clauses related to liability, termination, payment, data protection, and obligations.
                
                Return the results as a JSON object with:
                - riskLevel: One of ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
                - riskFactors: Array of specific risk factors identified
                - confidenceScore: Number between 0-1 indicating confidence in the analysis
                
                Contract text:
                ${textForAnalysis}
                `,
              },
            ],
          },
        ],
      });

      // Parse the JSON response
      const responseText = result.text;
      // Extract JSON object from response
      const jsonMatch = responseText.match(/\{\s*"riskLevel".*\}/s);

      if (!jsonMatch) {
        throw new Error("Could not parse risk analysis results");
      }

      const jsonStr = jsonMatch[0];
      const riskAnalysis = JSON.parse(jsonStr) as {
        riskLevel: RiskLevel;
        riskFactors: string[];
        confidenceScore: number;
      };

      return {
        riskLevel: riskAnalysis.riskLevel,
        riskFactors: riskAnalysis.riskFactors || [],
        confidenceScore: riskAnalysis.confidenceScore || 0.7,
      };
    } catch (error) {
      logger.error("Risk analysis failed", { error });
      // Return a default low-risk analysis instead of failing
      return {
        riskLevel: RiskLevel.LOW,
        riskFactors: ["Analysis failed, defaulting to low risk"],
        confidenceScore: 0.3,
      };
    }
  }

  /**
   * Estimates page count based on text content and document format
   *
   * @param text - Document text content
   * @param format - Document format
   * @returns Estimated page count
   * @private
   */
  private estimatePageCount(text: string, format: DocumentFormat): number {
    // Simple heuristic based on character count
    // In a real system, this would be more sophisticated
    const charactersPerPage = format === DocumentFormat.TEXT ? 3000 : 2000;
    return Math.max(1, Math.ceil(text.length / charactersPerPage));
  }
}
