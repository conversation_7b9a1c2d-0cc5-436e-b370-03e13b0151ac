/**
 * Input Sanitization Utilities
 * Provides functions for sanitizing inputs to AI processing
 */

import { logger } from '../../logging/logger';

/**
 * Sanitizes text input before sending to AI services
 * Removes potential injection attacks and normalizes text
 *
 * @param input - Text to sanitize
 * @returns Sanitized text
 */
export function sanitizeInput(input: string): string {
  if (!input) {
    return '';
  }

  let sanitized = input;

  // Remove any potential prompt injection patterns
  sanitized = sanitized.replace(/(\[.*?\]|\{.*?\}|<.*?>|\|\||\|\>|\>\|)/g, ' ');

  // Replace multiple spaces with single space
  sanitized = sanitized.replace(/\s+/g, ' ');

  // Remove non-printable characters
  sanitized = sanitized.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

  // Truncate very long lines
  const lines = sanitized.split('\n');
  const truncatedLines = lines.map(line =>
    line.length > 1000 ? line.substring(0, 1000) + '...' : line
  );
  sanitized = truncatedLines.join('\n');

  return sanitized.trim();
}

/**
 * Sanitizes a file path to prevent directory traversal
 *
 * @param path - File path to sanitize
 * @returns Sanitized file path
 */
export function sanitizeFilePath(path: string): string {
  if (!path) {
    return '';
  }

  // Remove any directory traversal sequences
  let sanitized = path.replace(/\.\.\//g, '');

  // Remove any absolute path indicators
  sanitized = sanitized.replace(/^\/+/g, '');

  // Remove any dangerous characters
  sanitized = sanitized.replace(/[;&|`$]/g, '');

  return sanitized;
}

/**
 * Validates a JSON string and returns a safe parsed object
 *
 * @param jsonStr - JSON string to validate
 * @returns Parsed object or null if invalid
 */
export function validateJsonString(jsonStr: string): any | null {
  try {
    // Check for potential code execution
    if (/(\beval\b|\bexec\b|\bFunction\b|\bsetTimeout\b|\bsetInterval\b)/i.test(jsonStr)) {
      return null;
    }

    return JSON.parse(jsonStr);
  } catch (error) {
    return null;
  }
}

/**
 * Checks if a string contains potentially malicious patterns
 *
 * @param input - String to check
 * @returns True if the string appears to contain malicious content
 */
export function containsMaliciousContent(input: string): boolean {
  if (!input) {
    return false;
  }

  // Check for potential command injection
  const commandInjectionPatterns = /(\bsh\b|\bbash\b|\bcmd\b|\bexec\b|\beval\b|\bsystem\b)/i;
  if (commandInjectionPatterns.test(input)) {
    return true;
  }

  // Check for SQL injection
  const sqlInjectionPatterns = /('|;|--|\/\*|\*\/|UNION SELECT|DROP TABLE|DROP DATABASE|DELETE FROM|INSERT INTO)/i;
  if (sqlInjectionPatterns.test(input)) {
    return true;
  }

  // Check for script tags or JavaScript events
  const scriptPatterns = /(<script|javascript:|on\w+\s*=)/i;
  if (scriptPatterns.test(input)) {
    return true;
  }

  return false;
}

/**
 * Sanitizes metadata for AI processing
 * @param metadata - Metadata object to sanitize
 * @returns Sanitized metadata
 */
export function sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
  try {
    if (!metadata || typeof metadata !== 'object') {
      return {};
    }

    const sanitized: Record<string, any> = {};

    // Process each key-value pair
    for (const [key, value] of Object.entries(metadata)) {
      // Sanitize keys
      const sanitizedKey = key.replace(/[^\w\d_-]/g, '_');

      // Sanitize values based on type
      if (typeof value === 'string') {
        sanitized[sanitizedKey] = sanitizeInput(value);
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        sanitized[sanitizedKey] = value;
      } else if (Array.isArray(value)) {
        sanitized[sanitizedKey] = value
          .filter(item => typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean')
          .map(item => typeof item === 'string' ? sanitizeInput(item) : item);
      } else if (value && typeof value === 'object') {
        sanitized[sanitizedKey] = sanitizeMetadata(value);
      }
    }

    return sanitized;
  } catch (error) {
    logger.error('Error sanitizing metadata', { error });
    // Return empty object on error
    return {};
  }
}

/**
 * Validates and sanitizes a document ID
 * @param documentId - Document ID to validate
 * @returns Sanitized document ID or null if invalid
 */
export function validateDocumentId(documentId: string): string | null {
  try {
    if (!documentId || typeof documentId !== 'string') {
      return null;
    }

    // Check for valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(documentId)) {
      return documentId;
    }

    // Allow alphanumeric IDs with limited special characters
    const validIdRegex = /^[a-zA-Z0-9_-]{1,64}$/;
    if (validIdRegex.test(documentId)) {
      return documentId;
    }

    return null;
  } catch (error) {
    logger.error('Error validating document ID', { error, documentId });
    return null;
  }
}