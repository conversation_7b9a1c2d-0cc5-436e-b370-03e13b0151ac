/**
 * AI Processing Interfaces
 * Defines contracts for AI document processing operations
 */

import { ContractMetadata } from "../../domain/contracts/ContractMetadata";

/**
 * Document processing status enum
 */
export enum DocumentProcessingStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

/**
 * Document format types supported by the processor
 */
export enum DocumentFormat {
  PDF = "application/pdf",
  DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  TEXT = "text/plain",
}

/**
 * Risk level assessment for Agreement Documents
 */
export enum RiskLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

/**
 * Document processing options
 */
export interface DocumentProcessingOptions {
  /**
   * Whether to extract text content from the document
   */
  extractText?: boolean;

  /**
   * Whether to extract entities from the document
   */
  extractEntities?: boolean;

  /**
   * Whether to perform risk analysis
   */
  performRiskAnalysis?: boolean;

  /**
   * Whether to generate embeddings for search
   */
  generateEmbeddings?: boolean;

  /**
   * Optional processing timeout in seconds
   */
  timeoutSeconds?: number;
}

/**
 * Entity types that can be extracted from documents
 */
export enum EntityType {
  DATE = "DATE",
  MONETARY_VALUE = "MONETARY_VALUE",
  ORGANIZATION = "ORGANIZATION",
  PERSON = "PERSON",
  LOCATION = "LOCATION",
  LEGAL_TERM = "LEGAL_TERM",
  OBLIGATION = "OBLIGATION",
  CUSTOM = "CUSTOM",
}

/**
 * Extracted entity from a document
 */
export interface ExtractedEntity {
  /**
   * Type of the extracted entity
   */
  type: EntityType;

  /**
   * The actual text content of the entity
   */
  text: string;

  /**
   * Confidence score (0-1) of the extraction
   */
  confidence: number;

  /**
   * Optional metadata about the entity
   */
  metadata?: Record<string, any>;

  /**
   * Position in the document text (if available)
   */
  position?: {
    startChar: number;
    endChar: number;
    pageNumber?: number;
  };
}

/**
 * Document analysis result structure
 */
export interface DocumentAnalysisResult {
  /**
   * Status of the document processing
   */
  status: DocumentProcessingStatus;

  /**
   * Timestamp when processing started
   */
  processedAt: Date;

  /**
   * Extracted plain text from the document (if requested)
   */
  textContent?: string;

  /**
   * Entities extracted from the document (if requested)
   */
  entities?: ExtractedEntity[];

  /**
   * Risk analysis results (if requested)
   */
  riskAnalysis?: {
    riskLevel: RiskLevel;
    riskFactors: string[];
    confidenceScore: number;
  };

  /**
   * Error message if processing failed
   */
  error?: string;

  /**
   * Optional additional data from the processing
   */
  additionalData?: Record<string, any>;

  /**
   * Processing metadata
   */
  metadata?: {
    processingTimeMs: number;
    originalFormat: DocumentFormat;
    pageCount?: number;
    wordCount?: number;
  };
}

/**
 * Interface for document analyzer service
 * Responsible for extracting metadata from documents
 */
export interface IDocumentAnalyzer {
  /**
   * Analyzes a document and extracts metadata
   *
   * @param documentBuffer - Binary content of the document
   * @param documentFormat - Format of the document
   * @param options - Processing options
   * @returns Analysis results
   */
  analyzeDocument(
    documentBuffer: Buffer,
    documentFormat: DocumentFormat,
    options?: DocumentProcessingOptions
  ): Promise<DocumentAnalysisResult>;

  /**
   * Converts document analysis results to a domain ContractMetadata object
   *
   * @param analysisResult - Document analysis results
   * @param contractId - ID of the contract to associate metadata with
   * @returns Contract metadata object
   */
  convertToContractMetadata(
    analysisResult: DocumentAnalysisResult,
    contractId: string
  ): ContractMetadata;
}

/**
 * Interface for the secure AI processor service
 * Provides a façade for AI operations with security isolation
 */
export interface ISecureAIProcessor {
  /**
   * Processes a document in an isolated environment
   *
   * @param documentBuffer - Binary content of the document
   * @param documentFormat - Format of the document
   * @param contractId - ID of the contract associated with the document
   * @param options - Processing options
   * @returns Processing result with extracted metadata
   */
  processDocument(
    documentBuffer: Buffer,
    documentFormat: DocumentFormat,
    contractId: string,
    options?: DocumentProcessingOptions
  ): Promise<{
    analysisResult: DocumentAnalysisResult;
    contractMetadata: ContractMetadata;
  }>;

  /**
   * Checks if a document format is supported
   *
   * @param format - Document format to check
   * @returns Boolean indicating if the format is supported
   */
  isSupportedFormat(format: string): boolean;

  /**
   * Gets the current processing status of a document
   *
   * @param processingId - ID of the processing job
   * @returns Current processing status
   */
  getProcessingStatus(processingId: string): Promise<DocumentProcessingStatus>;
}
