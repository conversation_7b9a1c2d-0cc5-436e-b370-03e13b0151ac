/**
 * RAG Service
 * Implements Retrieval-Augmented Generation for document Q&A
 */

import { v4 as uuidv4 } from "uuid";
import { PrismaClient } from "@prisma/client";
import { generateText, anthropic } from "./generateText";

import { logger } from "../logging/logger";
import { EmbeddingService } from "./EmbeddingService";
import { sanitizeInput } from "./util/sanitizer";

import { MessageRole as AIMessageRole } from "./generateText";

/**
 * Message role types
 */
export enum MessageRole {
  USER = "user",
  ASSISTANT = "assistant",
  SYSTEM = "system",
}

/**
 * Message interface
 */
export interface Message {
  id: string;
  role: string;
  content: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  conversationId: string;
}

/**
 * Conversation interface
 */
export interface Conversation {
  id: string;
  title: string | undefined;
  tenantId: string;
  userId: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * RAG response interface
 */
export interface RAGResponse {
  message: Message;
  citations: Array<{
    text: string;
    documentId: string;
    metadata: Record<string, any>;
  }>;
}

/**
 * RAG Service for document Q&A
 */
export class RAGService {
  private prisma: PrismaClient;
  private embeddingService: EmbeddingService;

  /**
   * Creates a new RAG service
   * @param prisma - Prisma client
   * @param embeddingService - Embedding service
   */
  constructor(prisma: PrismaClient, embeddingService: EmbeddingService) {
    this.prisma = prisma;
    this.embeddingService = embeddingService;
  }

  /**
   * Creates a new conversation
   * @param tenantId - Tenant ID
   * @param userId - User ID
   * @param title - Optional conversation title
   * @returns Created conversation
   */
  async createConversation(
    tenantId: string,
    userId: string,
    title?: string
  ): Promise<Conversation> {
    try {
      const conversation = await this.prisma.conversation.create({
        data: {
          id: uuidv4(),
          title: title || "New Conversation",
          tenantId,
          userId,
        },
        include: {
          messages: true,
        },
      });

      return {
        ...conversation,
        title: conversation.title || undefined,
        messages: conversation.messages.map((message) => ({
          ...message,
          metadata: message.metadata as Record<string, any>,
        })),
      };
    } catch (error) {
      logger.error("Failed to create conversation", {
        error,
        tenantId,
        userId,
      });
      throw new Error(
        `Failed to create conversation: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets a conversation by ID
   * @param conversationId - Conversation ID
   * @param tenantId - Tenant ID for isolation
   * @returns Conversation or null if not found
   */
  async getConversation(
    conversationId: string,
    tenantId: string
  ): Promise<Conversation | null> {
    try {
      const conversation = await this.prisma.conversation.findFirst({
        where: {
          id: conversationId,
          tenantId,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });

      if (!conversation) {
        return null;
      }

      return {
        ...conversation,
        title: conversation.title || undefined,
        messages: conversation.messages.map((message) => ({
          ...message,
          metadata: message.metadata as Record<string, any>,
        })),
      };
    } catch (error) {
      logger.error("Failed to get conversation", {
        error,
        conversationId,
        tenantId,
      });
      throw new Error(
        `Failed to get conversation: ${(error as Error).message}`
      );
    }
  }

  /**
   * Adds a message to a conversation
   * @param conversationId - Conversation ID
   * @param role - Message role
   * @param content - Message content
   * @param metadata - Optional message metadata
   * @returns Added message
   */
  async addMessage(
    conversationId: string,
    role: MessageRole,
    content: string,
    metadata?: Record<string, any>
  ): Promise<Message> {
    try {
      const message = await this.prisma.message.create({
        data: {
          id: uuidv4(),
          conversationId,
          role,
          content,
          metadata: metadata || {},
        },
      });

      return {
        ...message,
        metadata: message.metadata as Record<string, any>,
      };
    } catch (error) {
      logger.error("Failed to add message", { error, conversationId, role });
      throw new Error(`Failed to add message: ${(error as Error).message}`);
    }
  }

  /**
   * Processes a user query using RAG
   * @param conversationId - Conversation ID
   * @param query - User query
   * @param tenantId - Tenant ID for isolation
   * @returns RAG response
   */
  async processQuery(
    conversationId: string,
    query: string,
    tenantId: string
  ): Promise<RAGResponse> {
    try {
      // Sanitize input query
      const sanitizedQuery = sanitizeInput(query);

      // Get conversation history
      const conversation = await this.getConversation(conversationId, tenantId);
      if (!conversation) {
        throw new Error(`Conversation not found: ${conversationId}`);
      }

      // Add user message to conversation
      await this.addMessage(conversationId, MessageRole.USER, sanitizedQuery);

      // Retrieve relevant document chunks
      const searchResults = await this.embeddingService.searchSimilarDocuments(
        sanitizedQuery,
        tenantId,
        5,
        0.7
      );

      // Extract context from search results
      const context = searchResults.map((result) => ({
        text: result.chunk.content,
        documentId: result.chunk.documentId,
        metadata: result.chunk.metadata,
        similarity: result.similarity,
      }));

      // Build conversation history for context
      const conversationHistory = conversation.messages
        .slice(-5)
        .map((message) => ({
          role: message.role,
          content: message.content,
        }));

      // Generate response using AI
      const response = await this.generateResponse(
        sanitizedQuery,
        context,
        conversationHistory
      );

      // Extract citations from response
      const { message: responseMessage, citations } =
        this.extractCitations(response);

      // Add assistant message to conversation
      const assistantMessage = await this.addMessage(
        conversationId,
        MessageRole.ASSISTANT,
        responseMessage,
        { citations }
      );

      return {
        message: assistantMessage,
        citations,
      };
    } catch (error) {
      logger.error("Failed to process query", {
        error,
        conversationId,
        tenantId,
      });

      // Add error message to conversation
      const errorMessage = await this.addMessage(
        conversationId,
        MessageRole.ASSISTANT,
        "I apologize, but I'm having trouble processing your question right now. Please try again in a moment, or feel free to rephrase your question. I'm here to help you understand your contracts better.",
        { error: (error as Error).message }
      );

      return {
        message: errorMessage,
        citations: [],
      };
    }
  }

  /**
   * Generates a response using AI
   * @param query - User query
   * @param context - Retrieved context
   * @param conversationHistory - Conversation history
   * @returns Generated response
   * @private
   */
  private async generateResponse(
    query: string,
    context: Array<{
      text: string;
      documentId: string;
      metadata: Record<string, any>;
      similarity: number;
    }>,
    conversationHistory: Array<{
      role: string;
      content: string;
    }>
  ): Promise<string> {
    try {
      // Build context string
      const contextString = context
        .map(
          (item, index) =>
            `[${index + 1}] Document ID: ${item.documentId}\n${item.text}\n`
        )
        .join("\n");

      // Build system prompt
      const systemPrompt = `You are a helpful contract management assistant serving customers who use this platform to manage their contracts and licenses.

Your role is to help customers understand their documents and make informed business decisions. When responding:

1. **Be Customer-Focused**: Address the customer directly using "you" and "your"
2. **Use Clear Language**: Explain complex terms in simple, understandable language
3. **Be Supportive**: Help customers feel confident about their contract decisions
4. **Focus on Business Value**: Frame responses around how information helps achieve their business goals
5. **Provide Context**: Explain why certain contract terms or information matters to their business

Answer the customer's question based on the provided context from their documents.
If the answer is not in the context, say "I don't have enough information in your documents to answer that question, but I'd be happy to help you understand what to look for."
Always cite your sources using the format [n] where n is the document number.
Be helpful, empathetic, and focused on empowering the customer to make better contract decisions.`;

      // Generate response using AI
      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: AIMessageRole.SYSTEM,
            content: systemPrompt,
          },
          // Convert conversation history to valid messages
          ...conversationHistory.map((msg) => ({
            role: (msg.role === "user"
              ? AIMessageRole.USER
              : msg.role === "assistant"
              ? AIMessageRole.ASSISTANT
              : AIMessageRole.SYSTEM) as AIMessageRole,
            content: msg.content,
          })),
          {
            role: AIMessageRole.USER,
            content: `Context information:\n${contextString}\n\nQuestion: ${query}`,
          },
        ],
      });

      return result.text;
    } catch (error) {
      logger.error("Failed to generate response", { error });
      throw new Error(
        `Failed to generate response: ${(error as Error).message}`
      );
    }
  }

  /**
   * Extracts citations from response
   * @param response - Generated response
   * @returns Response message and citations
   * @private
   */
  private extractCitations(response: string): {
    message: string;
    citations: Array<{
      text: string;
      documentId: string;
      metadata: Record<string, any>;
    }>;
  } {
    // This is a simple implementation that can be enhanced
    const citations: Array<{
      text: string;
      documentId: string;
      metadata: Record<string, any>;
    }> = [];

    // Extract citations using regex
    const citationRegex = /\[(\d+)\]/g;
    let match;
    while ((match = citationRegex.exec(response)) !== null) {
      const citationNumber = parseInt(match[1], 10);

      // Add citation if not already added
      if (!citations.some((c) => c.text === `[${citationNumber}]`)) {
        citations.push({
          text: `[${citationNumber}]`,
          documentId: `doc-${citationNumber}`, // This would be replaced with actual document ID
          metadata: {},
        });
      }
    }

    return {
      message: response,
      citations,
    };
  }
}
