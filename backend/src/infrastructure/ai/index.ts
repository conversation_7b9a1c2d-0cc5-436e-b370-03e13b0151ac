/**
 * AI Module - Main Export File
 * Exports all AI processing components as a unified facade
 * following the :FacadePattern for clean integration
 */

// Export interfaces
export {
  DocumentProcessingStatus,
  DocumentFormat,
  RiskLevel,
  DocumentProcessingOptions,
  EntityType,
  ExtractedEntity,
  DocumentAnalysisResult,
  IDocumentAnalyzer,
  ISecureAIProcessor,
} from "./interfaces";

// Export implementations
export { DocumentAnalyzer } from "./DocumentAnalyzer";
export { SecureAIProcessor } from "./SecureAIProcessor";
export { EmbeddingService } from "./EmbeddingService";
export { AIJobType, AIJobStatus } from "./AIJobProcessor";
export { RAGService, MessageRole } from "./RAGService";

// Export utils
export * from "./util/sanitizer";

/**
 * Create and export default instance of SecureAIProcessor
 * This provides a simple entry point for other modules
 */
import { SecureAIProcessor } from "./SecureAIProcessor";
import { DocumentAnalyzer } from "./DocumentAnalyzer";
import { EmbeddingService } from "./EmbeddingService";
// import { AIJobProcessor } from "./AIJobProcessor";
import { RAGService } from "./RAGService";
import { EncryptionService } from "../services/EncryptionService";
import { VectorRepository } from "../repositories/VectorRepository";
import { ConfigService } from "../services/ConfigService";
import { PrismaService } from "../services/PrismaService";

// Get config service instance
const configService = new ConfigService();

// Get encryption service instance
const encryptionService = new EncryptionService(configService);

// Create document analyzer
const documentAnalyzer = new DocumentAnalyzer();

// Create secure AI processor with dependencies
const secureAIProcessor = new SecureAIProcessor(
  documentAnalyzer,
  encryptionService
);

// Get Prisma client from service
const prisma = PrismaService.getInstance();

// Create vector repository
const vectorRepository = new VectorRepository(prisma);

// Create embedding service
const embeddingService = new EmbeddingService(vectorRepository);

// Create RAG service
const ragService = new RAGService(prisma, embeddingService);

// Skip Redis initialization - we don't need it
// const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
// const aiJobProcessor = new AIJobProcessor(redisUrl, prisma, secureAIProcessor, embeddingService);

// Export default processor instance
export default secureAIProcessor;

// Export service instances
export { vectorRepository, embeddingService, ragService };
