/**
 * License Optimization Service
 * Analyzes license usage, provides cost optimization recommendations, and calculates potential savings
 */

import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { generateText, anthropic, MessageRole } from "./generateText";

import { logger } from "../logging/logger";
import { sanitizeInput } from "./util/sanitizer";

/**
 * Optimization type enum
 */
export enum OptimizationType {
  CONSOLIDATION = "CONSOLIDATION",
  DOWNGRADE = "DOWNGRADE",
  REMOVAL = "REMOVAL",
  RENEGOTIATION = "RENEGOTIATION",
  USAGE_BASED = "USAGE_BASED",
}

/**
 * Optimization priority enum
 */
export enum OptimizationPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

/**
 * License usage data interface
 */
export interface LicenseUsageData {
  licenseId: string;
  totalSeats: number;
  usedSeats: number;
  lastUsedDate?: Date;
  usageFrequency?: number; // Average usage per week/month
  peakUsage?: number; // Maximum usage observed
  costPerSeat: number;
  currency: string;
  billingCycle: string; // monthly, quarterly, annual
}

/**
 * Optimization recommendation interface
 */
export interface OptimizationRecommendation {
  id: string;
  licenseId: string;
  tenantId: string;
  type: OptimizationType;
  priority: OptimizationPriority;
  description: string;
  currentCost: number;
  projectedCost: number;
  potentialSavings: number;
  currency: string;
  implementationSteps: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * License optimization result interface
 */
export interface LicenseOptimizationResult {
  tenantId: string;
  totalCurrentCost: number;
  totalProjectedCost: number;
  totalPotentialSavings: number;
  currency: string;
  recommendations: OptimizationRecommendation[];
  summary: string;
  createdAt: Date;
}

/**
 * License optimization service
 */
export class LicenseOptimizationService {
  private prisma: PrismaClient;

  /**
   * Creates a new license optimization service
   * @param prisma - Prisma client
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Analyzes license usage and generates optimization recommendations
   * @param tenantId - Tenant ID
   * @returns License optimization result
   */
  async analyzeLicenseUsage(
    tenantId: string
  ): Promise<LicenseOptimizationResult> {
    try {
      // Get license usage data
      const usageData = await this.getLicenseUsageData(tenantId);

      if (usageData.length === 0) {
        throw new Error("No license data found for tenant");
      }

      // Generate optimization recommendations
      const recommendations = await this.generateOptimizationRecommendations(
        tenantId,
        usageData
      );

      // Calculate totals
      const totalCurrentCost = recommendations.reduce(
        (sum, rec) => sum + rec.currentCost,
        0
      );
      const totalProjectedCost = recommendations.reduce(
        (sum, rec) => sum + rec.projectedCost,
        0
      );
      const totalPotentialSavings = recommendations.reduce(
        (sum, rec) => sum + rec.potentialSavings,
        0
      );

      // Generate summary
      const summary = await this.generateOptimizationSummary(
        tenantId,
        recommendations,
        totalCurrentCost,
        totalProjectedCost,
        totalPotentialSavings,
        usageData[0].currency
      );

      // Create optimization result
      const result: LicenseOptimizationResult = {
        tenantId,
        totalCurrentCost,
        totalProjectedCost,
        totalPotentialSavings,
        currency: usageData[0].currency,
        recommendations,
        summary,
        createdAt: new Date(),
      };

      // Store optimization result
      await this.storeOptimizationResult(result);

      return result;
    } catch (error) {
      logger.error("Failed to analyze license usage", { error, tenantId });
      throw new Error(
        `Failed to analyze license usage: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets license usage data for a tenant
   * @param tenantId - Tenant ID
   * @returns Array of license usage data
   * @private
   */
  private async getLicenseUsageData(
    tenantId: string
  ): Promise<LicenseUsageData[]> {
    try {
      // Get licenses for tenant
      const licenses = await this.prisma.license.findMany({
        where: {
          tenantId,
          status: "ACTIVE",
        },
        // No include needed
      });

      // Map to LicenseUsageData
      return licenses.map((license) => {
        // Create mock cost data
        const cost = { amountPerSeat: 100, currency: "USD" };

        // Create mock usage data since we don't have actual usage data
        const mockUsedSeats = Math.floor(Math.random() * 100);
        const mockLastUsedDate = new Date();
        mockLastUsedDate.setDate(
          mockLastUsedDate.getDate() - Math.floor(Math.random() * 100)
        );

        return {
          licenseId: license.id,
          totalSeats: 100, // Default value
          usedSeats: mockUsedSeats,
          lastUsedDate: mockLastUsedDate,
          usageFrequency: 5, // Daily usage frequency as a number
          peakUsage: mockUsedSeats + 10,
          costPerSeat: cost?.amountPerSeat || 0,
          currency: cost?.currency || "USD",
          billingCycle: "MONTHLY", // Default value
        };
      });
    } catch (error) {
      logger.error("Failed to get license usage data", { error, tenantId });
      throw new Error(
        `Failed to get license usage data: ${(error as Error).message}`
      );
    }
  }

  /**
   * Generates optimization recommendations based on license usage data
   * @param tenantId - Tenant ID
   * @param usageData - License usage data
   * @returns Array of optimization recommendations
   * @private
   */
  private async generateOptimizationRecommendations(
    tenantId: string,
    usageData: LicenseUsageData[]
  ): Promise<OptimizationRecommendation[]> {
    try {
      const recommendations: OptimizationRecommendation[] = [];

      // Process each license
      for (const license of usageData) {
        // Skip licenses with no cost data
        if (license.costPerSeat <= 0) {
          continue;
        }

        // Calculate usage ratio
        const usageRatio = license.usedSeats / license.totalSeats;

        // Generate recommendations based on usage patterns
        if (usageRatio < 0.5) {
          // Underutilized license - recommend downsizing
          const unusedSeats = license.totalSeats - license.usedSeats;
          const currentCost = license.totalSeats * license.costPerSeat;
          const projectedCost = license.usedSeats * license.costPerSeat;
          const potentialSavings = currentCost - projectedCost;

          recommendations.push({
            id: uuidv4(),
            licenseId: license.licenseId,
            tenantId,
            type: OptimizationType.DOWNGRADE,
            priority:
              usageRatio < 0.3
                ? OptimizationPriority.HIGH
                : OptimizationPriority.MEDIUM,
            description: `Reduce license seats from ${license.totalSeats} to ${license.usedSeats} to eliminate ${unusedSeats} unused seats.`,
            currentCost,
            projectedCost,
            potentialSavings,
            currency: license.currency,
            implementationSteps: [
              "Review current license agreement for seat reduction options",
              "Contact vendor to request seat reduction",
              "Negotiate prorated refund for unused seats if possible",
              "Update license management system with new seat count",
            ],
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        } else if (
          license.peakUsage &&
          license.peakUsage < license.totalSeats * 0.8
        ) {
          // License with low peak usage - recommend optimization based on actual usage
          const recommendedSeats = Math.ceil(license.peakUsage * 1.2); // 20% buffer
          const currentCost = license.totalSeats * license.costPerSeat;
          const projectedCost = recommendedSeats * license.costPerSeat;
          const potentialSavings = currentCost - projectedCost;

          if (potentialSavings > 0) {
            recommendations.push({
              id: uuidv4(),
              licenseId: license.licenseId,
              tenantId,
              type: OptimizationType.USAGE_BASED,
              priority: OptimizationPriority.MEDIUM,
              description: `Optimize license seats from ${license.totalSeats} to ${recommendedSeats} based on peak usage patterns.`,
              currentCost,
              projectedCost,
              potentialSavings,
              currency: license.currency,
              implementationSteps: [
                "Review usage patterns to confirm peak usage data",
                "Adjust seat allocation to match actual usage plus buffer",
                "Monitor usage to ensure adequate capacity",
                "Consider usage-based licensing model if available",
              ],
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }
        }

        // Check for unused licenses (no usage in last 90 days)
        if (license.lastUsedDate) {
          const daysSinceLastUsed = Math.floor(
            (Date.now() - license.lastUsedDate.getTime()) /
              (1000 * 60 * 60 * 24)
          );

          if (daysSinceLastUsed > 90) {
            const currentCost = license.totalSeats * license.costPerSeat;
            const projectedCost = 0;
            const potentialSavings = currentCost;

            recommendations.push({
              id: uuidv4(),
              licenseId: license.licenseId,
              tenantId,
              type: OptimizationType.REMOVAL,
              priority: OptimizationPriority.HIGH,
              description: `Consider removing unused license. No usage detected in the last ${daysSinceLastUsed} days.`,
              currentCost,
              projectedCost,
              potentialSavings,
              currency: license.currency,
              implementationSteps: [
                "Confirm with stakeholders that the license is no longer needed",
                "Check contract terms for early termination options",
                "Initiate license termination process with vendor",
                "Remove license from inventory and management systems",
              ],
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }
        }
      }

      // Look for consolidation opportunities across similar licenses
      // This would require more complex analysis of license types and compatibility
      // Simplified implementation for now

      return recommendations;
    } catch (error) {
      logger.error("Failed to generate optimization recommendations", {
        error,
        tenantId,
      });
      throw new Error(
        `Failed to generate optimization recommendations: ${
          (error as Error).message
        }`
      );
    }
  }

  /**
   * Generates a summary of the optimization recommendations
   * @param tenantId - Tenant ID
   * @param recommendations - Optimization recommendations
   * @param totalCurrentCost - Total current cost
   * @param totalProjectedCost - Total projected cost
   * @param totalPotentialSavings - Total potential savings
   * @param currency - Currency
   * @returns Summary text
   * @private
   */
  private async generateOptimizationSummary(
    tenantId: string,
    recommendations: OptimizationRecommendation[],
    totalCurrentCost: number,
    totalProjectedCost: number,
    totalPotentialSavings: number,
    currency: string
  ): Promise<string> {
    try {
      // If no recommendations, return a simple summary
      if (recommendations.length === 0) {
        return "No optimization opportunities identified at this time.";
      }

      // Count recommendations by type and priority
      const typeCounts = {
        [OptimizationType.CONSOLIDATION]: 0,
        [OptimizationType.DOWNGRADE]: 0,
        [OptimizationType.REMOVAL]: 0,
        [OptimizationType.RENEGOTIATION]: 0,
        [OptimizationType.USAGE_BASED]: 0,
      };

      const priorityCounts = {
        [OptimizationPriority.LOW]: 0,
        [OptimizationPriority.MEDIUM]: 0,
        [OptimizationPriority.HIGH]: 0,
      };

      recommendations.forEach((rec) => {
        typeCounts[rec.type]++;
        priorityCounts[rec.priority]++;
      });

      // Calculate savings percentage
      const savingsPercentage =
        (totalPotentialSavings / totalCurrentCost) * 100;

      // Generate summary using AI
      const prompt = `
Generate a concise executive summary (3-4 paragraphs) of license optimization opportunities. The analysis identified:

- Total current license cost: ${totalCurrentCost} ${currency}
- Projected optimized cost: ${totalProjectedCost} ${currency}
- Potential annual savings: ${totalPotentialSavings} ${currency} (${savingsPercentage.toFixed(
        2
      )}%)

Recommendations by type:
- ${typeCounts[OptimizationType.DOWNGRADE]} license downgrades
- ${typeCounts[OptimizationType.REMOVAL]} license removals
- ${typeCounts[OptimizationType.USAGE_BASED]} usage-based optimizations
- ${typeCounts[OptimizationType.CONSOLIDATION]} consolidation opportunities
- ${typeCounts[OptimizationType.RENEGOTIATION]} renegotiation opportunities

Recommendations by priority:
- ${priorityCounts[OptimizationPriority.HIGH]} high priority
- ${priorityCounts[OptimizationPriority.MEDIUM]} medium priority
- ${priorityCounts[OptimizationPriority.LOW]} low priority

Provide a business-focused summary highlighting the financial impact, key opportunities, and recommended next steps.
`;

      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: MessageRole.USER,
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 1000,
      });

      return result.text.trim();
    } catch (error) {
      logger.error("Failed to generate optimization summary", {
        error,
        tenantId,
      });
      return `License optimization analysis identified ${recommendations.length} opportunities with potential savings of ${totalPotentialSavings} ${currency}.`;
    }
  }

  /**
   * Stores an optimization result in the database
   * @param result - License optimization result
   * @private
   */
  private async storeOptimizationResult(
    result: LicenseOptimizationResult
  ): Promise<void> {
    try {
      // Create optimization analysis record
      const analysis = await this.prisma.licenseOptimizationAnalysis.create({
        data: {
          tenantId: result.tenantId,
          totalCurrentCost: result.totalCurrentCost,
          totalProjectedCost: result.totalProjectedCost,
          totalPotentialSavings: result.totalPotentialSavings,
          currency: result.currency,
          summary: result.summary,
        },
      });

      // Create recommendation records
      await Promise.all(
        result.recommendations.map((rec) =>
          this.prisma.optimizationRecommendation.create({
            data: {
              id: rec.id,
              analysisId: analysis.id,
              licenseId: rec.licenseId,
              tenantId: rec.tenantId,
              type: rec.type,
              priority: rec.priority,
              description: rec.description,
              currentCost: rec.currentCost,
              projectedCost: rec.projectedCost,
              potentialSavings: rec.potentialSavings,
              currency: rec.currency,
              implementationSteps: rec.implementationSteps,
            },
          })
        )
      );
    } catch (error) {
      logger.error("Failed to store optimization result", {
        error,
        tenantId: result.tenantId,
      });
      throw new Error(
        `Failed to store optimization result: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets the latest optimization result for a tenant
   * @param tenantId - Tenant ID
   * @returns License optimization result or null if not found
   */
  async getOptimizationResult(
    tenantId: string
  ): Promise<LicenseOptimizationResult | null> {
    try {
      // Get optimization analysis from database
      const analysis = await this.prisma.licenseOptimizationAnalysis.findFirst({
        where: {
          tenantId,
        },
        orderBy: {
          createdAt: "desc",
        },
        include: {
          recommendations: true,
        },
      });

      if (!analysis) {
        return null;
      }

      // Convert to LicenseOptimizationResult
      return {
        tenantId: analysis.tenantId,
        totalCurrentCost: analysis.totalCurrentCost,
        totalProjectedCost: analysis.totalProjectedCost,
        totalPotentialSavings: analysis.totalPotentialSavings,
        currency: analysis.currency,
        recommendations: analysis.recommendations.map((rec) => ({
          id: rec.id,
          licenseId: rec.licenseId,
          tenantId: rec.tenantId,
          type: rec.type as OptimizationType,
          priority: rec.priority as OptimizationPriority,
          description: rec.description,
          currentCost: rec.currentCost,
          projectedCost: rec.projectedCost,
          potentialSavings: rec.potentialSavings,
          currency: rec.currency,
          implementationSteps: rec.implementationSteps,
          createdAt: rec.createdAt,
          updatedAt: rec.updatedAt,
        })),
        summary: analysis.summary,
        createdAt: analysis.createdAt,
      };
    } catch (error) {
      logger.error("Failed to get optimization result", { error, tenantId });
      throw new Error(
        `Failed to get optimization result: ${(error as Error).message}`
      );
    }
  }
}
