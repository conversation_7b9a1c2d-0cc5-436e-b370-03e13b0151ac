/**
 * Temporary AI Document Storage
 * Provides in-memory storage for documents used by the AI assistant
 * Documents are not persisted and are only available during the current session
 */

import { logger } from "../logging/logger";

// Define the AI document type
export interface AIDocument {
  id: string;
  title: string;
  documentFormat: string;
  documentUri: string;
  documentSize: number;
  content?: Buffer; // Optional content buffer for direct access
}

// In-memory storage for AI documents
const inMemoryDocuments: Map<string, AIDocument> = new Map();

/**
 * Gets an AI document by ID
 * @param id Document ID
 * @returns AI document or null if not found
 */
export function getAIDocumentById(id: string): AIDocument | null {
  return inMemoryDocuments.get(id) || null;
}

/**
 * Saves an AI document to in-memory storage
 * @param document AI document to save
 * @returns Saved AI document
 */
export function saveAIDocument(document: AIDocument): AIDocument {
  // Store in memory
  inMemoryDocuments.set(document.id, document);
  logger.info(`Stored AI document in memory: ${document.id}`);

  return document;
}

/**
 * Removes an AI document from memory
 * @param id Document ID
 * @returns true if deleted, false if not found
 */
export function removeAIDocument(id: string): boolean {
  if (!inMemoryDocuments.has(id)) {
    return false;
  }

  inMemoryDocuments.delete(id);
  logger.info(`Removed AI document from memory: ${id}`);

  return true;
}

/**
 * Clears all AI documents from memory
 */
export function clearAIDocuments(): void {
  inMemoryDocuments.clear();
  logger.info("Cleared all AI documents from memory");
}
