/**
 * AI Job Processor
 * Manages AI processing jobs using BullMQ
 */

import { Queue, Worker, Job, QueueEvents } from "bullmq";
import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

import { logger } from "../logging/logger";
import { SecureAIProcessor } from "./SecureAIProcessor";
import { DocumentFormat, DocumentProcessingOptions } from "./interfaces";
import { EmbeddingService } from "./EmbeddingService";
import { VectorRepository } from "../repositories/VectorRepository";

/**
 * Job types for AI processing
 */
export enum AIJobType {
  DOCUMENT_ANALYSIS = "DOCUMENT_ANALYSIS",
  EMBEDDING_GENERATION = "EMBEDDING_GENERATION",
  RISK_ASSESSMENT = "RISK_ASSESSMENT",
  RAG_QUERY = "RAG_QUERY",
}

/**
 * Job status for AI processing
 */
export enum AIJobStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

/**
 * Job data for document analysis
 */
export interface DocumentAnalysisJobData {
  documentId: string;
  documentFormat: DocumentFormat;
  contractId: string;
  tenantId: string;
  options?: DocumentProcessingOptions;
  documentUri: string; // URI to the document storage
}

/**
 * Job data for embedding generation
 */
export interface EmbeddingGenerationJobData {
  documentId: string;
  tenantId: string;
  text: string;
  metadata?: Record<string, any>;
}

/**
 * Job data for risk assessment
 */
export interface RiskAssessmentJobData {
  documentId: string;
  contractId: string;
  tenantId: string;
  text: string;
}

/**
 * Job data for RAG query
 */
export interface RAGQueryJobData {
  query: string;
  conversationId: string;
  userId: string;
  tenantId: string;
}

/**
 * Union type for all job data types
 */
export type AIJobData =
  | DocumentAnalysisJobData
  | EmbeddingGenerationJobData
  | RiskAssessmentJobData
  | RAGQueryJobData;

/**
 * AI Job Processor using BullMQ
 */
export class AIJobProcessor {
  private documentAnalysisQueue: Queue;
  private embeddingGenerationQueue: Queue;
  private riskAssessmentQueue: Queue;
  private ragQueryQueue: Queue;
  private prisma: PrismaClient;
  private secureAIProcessor: SecureAIProcessor;
  private embeddingService: EmbeddingService;
  private workers: Worker[] = [];

  /**
   * Creates a new AI Job Processor
   * @param redisUrl - Redis URL for BullMQ
   * @param prisma - Prisma client
   * @param secureAIProcessor - Secure AI processor
   * @param embeddingService - Embedding service
   */
  constructor(
    redisUrl: string,
    prisma: PrismaClient,
    secureAIProcessor: SecureAIProcessor,
    embeddingService: EmbeddingService
  ) {
    this.prisma = prisma;
    this.secureAIProcessor = secureAIProcessor;
    this.embeddingService = embeddingService;

    // Create queues
    this.documentAnalysisQueue = new Queue("document-analysis", {
      connection: { url: redisUrl },
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    });

    this.embeddingGenerationQueue = new Queue("embedding-generation", {
      connection: { url: redisUrl },
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    });

    this.riskAssessmentQueue = new Queue("risk-assessment", {
      connection: { url: redisUrl },
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    });

    this.ragQueryQueue = new Queue("rag-query", {
      connection: { url: redisUrl },
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    });

    // Initialize workers
    this.initializeWorkers(redisUrl);

    // Set up queue events
    this.setupQueueEvents(redisUrl);
  }

  /**
   * Initializes workers for processing jobs
   * @param redisUrl - Redis URL
   * @private
   */
  private initializeWorkers(redisUrl: string): void {
    // Document analysis worker
    const documentAnalysisWorker = new Worker(
      "document-analysis",
      async (job: Job<DocumentAnalysisJobData>) => {
        return this.processDocumentAnalysisJob(job);
      },
      { connection: { url: redisUrl }, concurrency: 2 }
    );

    // Embedding generation worker
    const embeddingGenerationWorker = new Worker(
      "embedding-generation",
      async (job: Job<EmbeddingGenerationJobData>) => {
        return this.processEmbeddingGenerationJob(job);
      },
      { connection: { url: redisUrl }, concurrency: 5 }
    );

    // Risk assessment worker
    const riskAssessmentWorker = new Worker(
      "risk-assessment",
      async (job: Job<RiskAssessmentJobData>) => {
        return this.processRiskAssessmentJob(job);
      },
      { connection: { url: redisUrl }, concurrency: 2 }
    );

    // RAG query worker
    const ragQueryWorker = new Worker(
      "rag-query",
      async (job: Job<RAGQueryJobData>) => {
        return this.processRAGQueryJob(job);
      },
      { connection: { url: redisUrl }, concurrency: 5 }
    );

    // Add error handlers
    documentAnalysisWorker.on("error", (error) => {
      logger.error("Document analysis worker error", { error });
    });

    embeddingGenerationWorker.on("error", (error) => {
      logger.error("Embedding generation worker error", { error });
    });

    riskAssessmentWorker.on("error", (error) => {
      logger.error("Risk assessment worker error", { error });
    });

    ragQueryWorker.on("error", (error) => {
      logger.error("RAG query worker error", { error });
    });

    // Store workers for cleanup
    this.workers.push(
      documentAnalysisWorker,
      embeddingGenerationWorker,
      riskAssessmentWorker,
      ragQueryWorker
    );
  }

  /**
   * Sets up queue events for monitoring
   * @param redisUrl - Redis URL
   * @private
   */
  private setupQueueEvents(redisUrl: string): void {
    // Document analysis queue events
    const documentAnalysisEvents = new QueueEvents("document-analysis", {
      connection: { url: redisUrl },
    });

    documentAnalysisEvents.on("completed", async ({ jobId, returnvalue }) => {
      await this.updateJobStatus(jobId, AIJobStatus.COMPLETED, returnvalue);
    });

    documentAnalysisEvents.on("failed", async ({ jobId, failedReason }) => {
      await this.updateJobStatus(jobId, AIJobStatus.FAILED, {
        error: failedReason,
      });
    });

    // Embedding generation queue events
    const embeddingGenerationEvents = new QueueEvents("embedding-generation", {
      connection: { url: redisUrl },
    });

    embeddingGenerationEvents.on(
      "completed",
      async ({ jobId, returnvalue }) => {
        await this.updateJobStatus(jobId, AIJobStatus.COMPLETED, returnvalue);
      }
    );

    embeddingGenerationEvents.on("failed", async ({ jobId, failedReason }) => {
      await this.updateJobStatus(jobId, AIJobStatus.FAILED, {
        error: failedReason,
      });
    });

    // Risk assessment queue events
    const riskAssessmentEvents = new QueueEvents("risk-assessment", {
      connection: { url: redisUrl },
    });

    riskAssessmentEvents.on("completed", async ({ jobId, returnvalue }) => {
      await this.updateJobStatus(jobId, AIJobStatus.COMPLETED, returnvalue);
    });

    riskAssessmentEvents.on("failed", async ({ jobId, failedReason }) => {
      await this.updateJobStatus(jobId, AIJobStatus.FAILED, {
        error: failedReason,
      });
    });

    // RAG query queue events
    const ragQueryEvents = new QueueEvents("rag-query", {
      connection: { url: redisUrl },
    });

    ragQueryEvents.on("completed", async ({ jobId, returnvalue }) => {
      await this.updateJobStatus(jobId, AIJobStatus.COMPLETED, returnvalue);
    });

    ragQueryEvents.on("failed", async ({ jobId, failedReason }) => {
      await this.updateJobStatus(jobId, AIJobStatus.FAILED, {
        error: failedReason,
      });
    });
  }

  /**
   * Updates job status in the database
   * @param jobId - Job ID
   * @param status - New status
   * @param result - Job result
   * @private
   */
  private async updateJobStatus(
    jobId: string,
    status: AIJobStatus,
    result?: any
  ): Promise<void> {
    try {
      await this.prisma.aIJob.update({
        where: { id: jobId },
        data: {
          status,
          updatedAt: new Date(),
          ...(result && {
            results: {
              create: {
                resultData: result,
                confidence: result.confidence || null,
              },
            },
          }),
        },
      });
    } catch (error) {
      logger.error("Failed to update job status", { error, jobId, status });
    }
  }

  /**
   * Processes a document analysis job
   * @param job - Document analysis job
   * @returns Job result
   * @private
   */
  private async processDocumentAnalysisJob(
    job: Job<DocumentAnalysisJobData>
  ): Promise<any> {
    const {
      documentId,
      documentFormat,
      contractId,
      options,
      documentUri,
      tenantId,
    } = job.data;

    try {
      // Update job status to processing
      if (job.id) {
        await this.updateJobStatus(job.id, AIJobStatus.PROCESSING);
      }

      // Fetch document from storage (implementation depends on your storage solution)
      const documentBuffer = await this.fetchDocumentFromStorage(documentUri);

      // Process document using secure AI processor
      const result = await this.secureAIProcessor.processDocument(
        documentBuffer,
        documentFormat,
        contractId,
        options
      );

      // If embedding generation is requested, queue a job for it
      if (options?.generateEmbeddings && result.analysisResult.textContent) {
        await this.queueEmbeddingGenerationJob({
          documentId,
          tenantId,
          text: result.analysisResult.textContent,
          metadata: {
            documentFormat,
            contractId,
            documentSize: documentBuffer.length,
          },
        });
      }

      return result;
    } catch (error) {
      logger.error("Document analysis job failed", {
        error,
        jobId: job.id,
        documentId,
      });
      throw error;
    }
  }

  /**
   * Processes an embedding generation job
   * @param job - Embedding generation job
   * @returns Job result
   * @private
   */
  private async processEmbeddingGenerationJob(
    job: Job<EmbeddingGenerationJobData>
  ): Promise<any> {
    const { documentId, tenantId, text, metadata } = job.data;

    try {
      // Update job status to processing
      if (job.id) {
        await this.updateJobStatus(job.id, AIJobStatus.PROCESSING);
      }

      // Process document and generate embeddings
      const chunks = await this.embeddingService.processDocument(
        documentId,
        tenantId,
        text,
        metadata
      );

      return {
        documentId,
        tenantId,
        chunkCount: chunks.length,
        success: true,
      };
    } catch (error) {
      logger.error("Embedding generation job failed", {
        error,
        jobId: job.id,
        documentId,
      });
      throw error;
    }
  }

  /**
   * Processes a risk assessment job
   * @param job - Risk assessment job
   * @returns Job result
   * @private
   */
  private async processRiskAssessmentJob(
    job: Job<RiskAssessmentJobData>
  ): Promise<any> {
    // Implementation will be added in a future update
    return { message: "Risk assessment not yet implemented" };
  }

  /**
   * Processes a RAG query job
   * @param job - RAG query job
   * @returns Job result
   * @private
   */
  private async processRAGQueryJob(job: Job<RAGQueryJobData>): Promise<any> {
    // Implementation will be added in a future update
    return { message: "RAG query not yet implemented" };
  }

  /**
   * Fetches a document from storage
   * @param documentUri - Document URI
   * @returns Document buffer
   * @private
   */
  private async fetchDocumentFromStorage(documentUri: string): Promise<Buffer> {
    // Implementation depends on your storage solution
    // This is a placeholder
    return Buffer.from("");
  }

  /**
   * Queues a document analysis job
   * @param jobData - Job data
   * @returns Job ID
   */
  async queueDocumentAnalysisJob(
    jobData: DocumentAnalysisJobData
  ): Promise<string> {
    try {
      // Generate job ID
      const jobId = uuidv4();

      // Create job record in database
      await this.prisma.aIJob.create({
        data: {
          id: jobId,
          sourceId: jobData.documentId,
          type: AIJobType.DOCUMENT_ANALYSIS,
          status: AIJobStatus.PENDING,
          priority: 1,
        },
      });

      // Add job to queue
      await this.documentAnalysisQueue.add("document-analysis", jobData, {
        jobId,
      });

      return jobId;
    } catch (error) {
      logger.error("Failed to queue document analysis job", { error, jobData });
      throw new Error(
        `Failed to queue document analysis job: ${(error as Error).message}`
      );
    }
  }

  /**
   * Queues an embedding generation job
   * @param jobData - Job data
   * @returns Job ID
   */
  async queueEmbeddingGenerationJob(
    jobData: EmbeddingGenerationJobData
  ): Promise<string> {
    try {
      // Generate job ID
      const jobId = uuidv4();

      // Create job record in database
      await this.prisma.aIJob.create({
        data: {
          id: jobId,
          sourceId: jobData.documentId,
          type: AIJobType.EMBEDDING_GENERATION,
          status: AIJobStatus.PENDING,
          priority: 2,
        },
      });

      // Add job to queue
      await this.embeddingGenerationQueue.add("embedding-generation", jobData, {
        jobId,
      });

      return jobId;
    } catch (error) {
      logger.error("Failed to queue embedding generation job", {
        error,
        jobData,
      });
      throw new Error(
        `Failed to queue embedding generation job: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets job status
   * @param jobId - Job ID
   * @returns Job status and results
   */
  async getJobStatus(jobId: string): Promise<{
    status: AIJobStatus;
    results?: any;
  }> {
    try {
      const job = await this.prisma.aIJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new Error(`Job not found: ${jobId}`);
      }

      // Get the results separately since there's no relation defined
      const results = await this.prisma.aIResult.findMany({
        where: { jobId: jobId },
        orderBy: { createdAt: "desc" },
        take: 1,
      });

      return {
        status: job.status as AIJobStatus,
        results: results[0]?.resultData || null,
      };
    } catch (error) {
      logger.error("Failed to get job status", { error, jobId });
      throw new Error(`Failed to get job status: ${(error as Error).message}`);
    }
  }

  /**
   * Cleans up resources
   */
  async cleanup(): Promise<void> {
    try {
      // Close workers
      await Promise.all(this.workers.map((worker) => worker.close()));

      // Close queues
      await this.documentAnalysisQueue.close();
      await this.embeddingGenerationQueue.close();
      await this.riskAssessmentQueue.close();
      await this.ragQueryQueue.close();
    } catch (error) {
      logger.error("Failed to clean up AI job processor", { error });
    }
  }
}
