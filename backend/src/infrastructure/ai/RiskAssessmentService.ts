/**
 * Risk Assessment Service
 * Identifies, scores, and categorizes risks in contracts and provides mitigation recommendations
 */

import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { generateText, anthropic, MessageRole } from "./generateText";

import { logger } from "../logging/logger";
import { SecureAIProcessor } from "./SecureAIProcessor";
import { sanitizeInput } from "./util/sanitizer";
import { EmbeddingService } from "./EmbeddingService";

/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

/**
 * Risk category enum
 */
export enum RiskCategory {
  LEGAL = "LEGAL",
  FINANCIAL = "FINANCIAL",
  OPERATIONAL = "OPERATIONAL",
  COMPLIANCE = "COMPLIANCE",
  SECURITY = "SECURITY",
  REPUTATION = "REPUTATION",
}

/**
 * Risk factor interface
 */
export interface RiskFactor {
  id: string;
  contractId: string;
  tenantId: string;
  category: RiskCategory;
  level: RiskLevel;
  description: string;
  clause: string;
  clauseLocation: string;
  mitigation: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Risk assessment result interface
 */
export interface RiskAssessmentResult {
  contractId: string;
  tenantId: string;
  overallRiskLevel: RiskLevel;
  riskFactors: RiskFactor[];
  summary: string;
  createdAt: Date;
}

/**
 * Risk assessment service
 */
export class RiskAssessmentService {
  private prisma: PrismaClient;
  private secureAIProcessor: SecureAIProcessor;
  private embeddingService: EmbeddingService;

  /**
   * Creates a new risk assessment service
   * @param prisma - Prisma client
   * @param secureAIProcessor - Secure AI processor
   * @param embeddingService - Embedding service
   */
  constructor(
    prisma: PrismaClient,
    secureAIProcessor: SecureAIProcessor,
    embeddingService: EmbeddingService
  ) {
    this.prisma = prisma;
    this.secureAIProcessor = secureAIProcessor;
    this.embeddingService = embeddingService;
  }

  /**
   * Assesses risks in a contract
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @param contractText - Contract text (optional, will be fetched if not provided)
   * @returns Risk assessment result
   */
  async assessRisks(
    contractId: string,
    tenantId: string,
    contractText?: string
  ): Promise<RiskAssessmentResult> {
    try {
      // Get contract text if not provided
      let text = contractText;
      if (!text) {
        const contract = await this.prisma.contract.findUnique({
          where: { id: contractId },
        });

        if (!contract || contract.tenantId !== tenantId) {
          throw new Error(`Contract not found: ${contractId}`);
        }

        // Use the contract description as content
        if (!contract.description) {
          throw new Error(`No description found for contract: ${contractId}`);
        }

        text = contract.description;
      }

      // Sanitize input
      const sanitizedText = sanitizeInput(text || "");
      if (!sanitizedText) {
        throw new Error("Contract text is empty or invalid");
      }

      // Identify risk factors
      const riskFactors = await this.identifyRiskFactors(
        contractId,
        tenantId,
        sanitizedText
      );

      // Calculate overall risk level
      const overallRiskLevel = this.calculateOverallRiskLevel(riskFactors);

      // Generate summary
      const summary = await this.generateRiskSummary(
        contractId,
        tenantId,
        riskFactors,
        sanitizedText
      );

      // Create risk assessment result
      const result: RiskAssessmentResult = {
        contractId,
        tenantId,
        overallRiskLevel,
        riskFactors,
        summary,
        createdAt: new Date(),
      };

      // Store risk assessment in database
      await this.storeRiskAssessment(result);

      return result;
    } catch (error) {
      logger.error("Failed to assess risks", { error, contractId, tenantId });
      throw new Error(`Failed to assess risks: ${(error as Error).message}`);
    }
  }

  /**
   * Identifies risk factors in a contract
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @param contractText - Contract text
   * @returns Array of risk factors
   * @private
   */
  private async identifyRiskFactors(
    contractId: string,
    tenantId: string,
    contractText: string
  ): Promise<RiskFactor[]> {
    try {
      // Define the prompt for risk factor identification
      const prompt = `
You are a legal expert specializing in contract risk assessment. Analyze the following contract and identify key risk factors.

For each risk factor:
1. Categorize it as LEGAL, FINANCIAL, OPERATIONAL, COMPLIANCE, SECURITY, or REPUTATION
2. Assign a risk level: LOW, MEDIUM, HIGH, or CRITICAL
3. Provide a clear description of the risk
4. Quote the specific clause that contains the risk
5. Suggest a practical mitigation strategy

Format your response as a JSON array with the following structure:
[
  {
    "category": "CATEGORY",
    "level": "LEVEL",
    "description": "Description of the risk",
    "clause": "Quoted text from the contract",
    "clauseLocation": "Section or page reference if available",
    "mitigation": "Recommended mitigation strategy"
  }
]

CONTRACT:
${contractText.substring(0, 15000)}
`;

      // Generate risk factors using AI
      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: MessageRole.USER,
            content: prompt,
          },
        ],
        temperature: 0.2,
        maxTokens: 4000,
      });

      // Parse the response
      const responseText = result.text.trim();

      // Extract JSON array from response
      const jsonMatch = responseText.match(/\[\s*\{.*\}\s*\]/s);
      if (!jsonMatch) {
        throw new Error("Failed to parse risk factors from AI response");
      }

      const jsonStr = jsonMatch[0];
      const parsedFactors = JSON.parse(jsonStr);

      // Convert to RiskFactor objects
      return parsedFactors.map((factor: any) => ({
        id: uuidv4(),
        contractId,
        tenantId,
        category: factor.category as RiskCategory,
        level: factor.level as RiskLevel,
        description: factor.description,
        clause: factor.clause,
        clauseLocation: factor.clauseLocation || "",
        mitigation: factor.mitigation,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));
    } catch (error) {
      logger.error("Failed to identify risk factors", { error, contractId });
      throw new Error(
        `Failed to identify risk factors: ${(error as Error).message}`
      );
    }
  }

  /**
   * Calculates the overall risk level based on risk factors
   * @param riskFactors - Array of risk factors
   * @returns Overall risk level
   * @private
   */
  private calculateOverallRiskLevel(riskFactors: RiskFactor[]): RiskLevel {
    if (riskFactors.length === 0) {
      return RiskLevel.LOW;
    }

    // Count risk factors by level
    const counts = {
      [RiskLevel.CRITICAL]: 0,
      [RiskLevel.HIGH]: 0,
      [RiskLevel.MEDIUM]: 0,
      [RiskLevel.LOW]: 0,
    };

    riskFactors.forEach((factor) => {
      counts[factor.level]++;
    });

    // Determine overall risk level based on counts
    if (counts[RiskLevel.CRITICAL] > 0) {
      return RiskLevel.CRITICAL;
    } else if (counts[RiskLevel.HIGH] > 2) {
      return RiskLevel.HIGH;
    } else if (counts[RiskLevel.HIGH] > 0 || counts[RiskLevel.MEDIUM] > 3) {
      return RiskLevel.MEDIUM;
    } else {
      return RiskLevel.LOW;
    }
  }

  /**
   * Generates a summary of the risk assessment
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @param riskFactors - Array of risk factors
   * @param contractText - Contract text
   * @returns Summary text
   * @private
   */
  private async generateRiskSummary(
    contractId: string,
    _tenantId: string,
    riskFactors: RiskFactor[],
    _contractText: string
  ): Promise<string> {
    try {
      // If no risk factors, return a simple summary
      if (riskFactors.length === 0) {
        return "No significant risks were identified in this contract.";
      }

      // Count risk factors by level and category
      const levelCounts = {
        [RiskLevel.CRITICAL]: 0,
        [RiskLevel.HIGH]: 0,
        [RiskLevel.MEDIUM]: 0,
        [RiskLevel.LOW]: 0,
      };

      const categoryCounts = {
        [RiskCategory.LEGAL]: 0,
        [RiskCategory.FINANCIAL]: 0,
        [RiskCategory.OPERATIONAL]: 0,
        [RiskCategory.COMPLIANCE]: 0,
        [RiskCategory.SECURITY]: 0,
        [RiskCategory.REPUTATION]: 0,
      };

      riskFactors.forEach((factor) => {
        levelCounts[factor.level]++;
        categoryCounts[factor.category]++;
      });

      // Generate summary using AI
      const prompt = `
Summarize the risk assessment for this contract. The assessment identified:
- ${levelCounts[RiskLevel.CRITICAL]} critical risks
- ${levelCounts[RiskLevel.HIGH]} high risks
- ${levelCounts[RiskLevel.MEDIUM]} medium risks
- ${levelCounts[RiskLevel.LOW]} low risks

Across these categories:
- ${categoryCounts[RiskCategory.LEGAL]} legal risks
- ${categoryCounts[RiskCategory.FINANCIAL]} financial risks
- ${categoryCounts[RiskCategory.OPERATIONAL]} operational risks
- ${categoryCounts[RiskCategory.COMPLIANCE]} compliance risks
- ${categoryCounts[RiskCategory.SECURITY]} security risks
- ${categoryCounts[RiskCategory.REPUTATION]} reputation risks

Provide a concise executive summary (3-4 paragraphs) of the risk profile, highlighting the most significant concerns and overall risk posture. Be specific but concise.
`;

      const result = await generateText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: [
          {
            role: MessageRole.USER,
            content: prompt,
          },
        ],
        temperature: 0.3,
        maxTokens: 1000,
      });

      return result.text.trim();
    } catch (error) {
      logger.error("Failed to generate risk summary", { error, contractId });
      return `Risk assessment identified ${riskFactors.length} risk factors. Please review the detailed findings.`;
    }
  }

  /**
   * Stores a risk assessment in the database
   * @param assessment - Risk assessment result
   * @private
   */
  private async storeRiskAssessment(
    assessment: RiskAssessmentResult
  ): Promise<void> {
    try {
      // Create risk assessment record
      const riskAssessment = await this.prisma.riskAssessment.create({
        data: {
          contractId: assessment.contractId,
          tenantId: assessment.tenantId,
          overallRiskLevel: assessment.overallRiskLevel,
          summary: assessment.summary,
        },
      });

      // Create risk factor records
      await Promise.all(
        assessment.riskFactors.map((factor) =>
          this.prisma.riskFactor.create({
            data: {
              id: factor.id,
              assessmentId: riskAssessment.id,
              contractId: factor.contractId,
              tenantId: factor.tenantId,
              category: factor.category,
              level: factor.level,
              description: factor.description,
              clause: factor.clause,
              clauseLocation: factor.clauseLocation,
              mitigation: factor.mitigation,
            },
          })
        )
      );
    } catch (error) {
      logger.error("Failed to store risk assessment", {
        error,
        contractId: assessment.contractId,
      });
      throw new Error(
        `Failed to store risk assessment: ${(error as Error).message}`
      );
    }
  }

  /**
   * Gets a risk assessment by contract ID
   * @param contractId - Contract ID
   * @param tenantId - Tenant ID
   * @returns Risk assessment result or null if not found
   */
  async getRiskAssessment(
    contractId: string,
    tenantId: string
  ): Promise<RiskAssessmentResult | null> {
    try {
      // Get risk assessment from database
      const assessment = await this.prisma.riskAssessment.findFirst({
        where: {
          contractId,
          tenantId,
        },
        orderBy: {
          createdAt: "desc",
        },
        include: {
          riskFactors: true,
        },
      });

      if (!assessment) {
        return null;
      }

      // Convert to RiskAssessmentResult
      return {
        contractId: assessment.contractId,
        tenantId: assessment.tenantId,
        overallRiskLevel: assessment.overallRiskLevel as RiskLevel,
        riskFactors: assessment.riskFactors.map((factor) => ({
          id: factor.id,
          contractId: factor.contractId,
          tenantId: factor.tenantId,
          category: factor.category as RiskCategory,
          level: factor.level as RiskLevel,
          description: factor.description,
          clause: factor.clause,
          clauseLocation: factor.clauseLocation,
          mitigation: factor.mitigation,
          createdAt: factor.createdAt,
          updatedAt: factor.updatedAt,
        })),
        summary: assessment.summary,
        createdAt: assessment.createdAt,
      };
    } catch (error) {
      logger.error("Failed to get risk assessment", {
        error,
        contractId,
        tenantId,
      });
      throw new Error(
        `Failed to get risk assessment: ${(error as Error).message}`
      );
    }
  }
}
