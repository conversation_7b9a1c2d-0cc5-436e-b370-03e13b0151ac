/**
 * Embedding Service
 * Generates and manages embeddings for document text
 */

import { v4 as uuidv4 } from 'uuid';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';

import { logger } from '../logging/logger';
import { VectorRepository, DocumentChunkWithEmbedding } from '../repositories/VectorRepository';
import { sanitizeInput } from './util/sanitizer';

/**
 * Chunk options for text splitting
 */
export interface ChunkOptions {
  chunkSize?: number;
  chunkOverlap?: number;
  minChunkSize?: number;
}

/**
 * Default chunk options
 */
const DEFAULT_CHUNK_OPTIONS: ChunkOptions = {
  chunkSize: 1000,
  chunkOverlap: 200,
  minChunkSize: 100,
};

/**
 * Service for generating and managing embeddings
 */
export class EmbeddingService {
  private vectorRepository: VectorRepository;
  private embeddings: OpenAIEmbeddings;
  private defaultChunkOptions: ChunkOptions;

  /**
   * Creates a new embedding service
   * @param vectorRepository - Vector repository for storing embeddings
   * @param apiKey - OpenAI API key (optional, defaults to env var)
   * @param chunkOptions - Options for text chunking
   */
  constructor(
    vectorRepository: VectorRepository,
    apiKey?: string,
    chunkOptions?: ChunkOptions
  ) {
    this.vectorRepository = vectorRepository;
    this.defaultChunkOptions = { ...DEFAULT_CHUNK_OPTIONS, ...chunkOptions };

    // Initialize OpenAI embeddings
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey || process.env.OPENAI_API_KEY,
      modelName: 'text-embedding-3-small',
      dimensions: 1536,
    });
  }

  /**
   * Processes a document and generates embeddings for its chunks
   * @param documentId - ID of the document
   * @param tenantId - Tenant ID for isolation
   * @param text - Document text content
   * @param metadata - Additional metadata for the document
   * @param options - Chunk options
   * @returns Array of stored document chunks
   */
  async processDocument(
    documentId: string,
    tenantId: string,
    text: string,
    metadata: Record<string, any> = {},
    options?: ChunkOptions
  ): Promise<DocumentChunkWithEmbedding[]> {
    try {
      // Sanitize input text
      const sanitizedText = sanitizeInput(text);

      // Split text into chunks
      const chunks = await this.splitTextIntoChunks(sanitizedText, options);

      // Generate embeddings for chunks
      const embeddedChunks = await this.generateEmbeddings(chunks);

      // Store chunks with embeddings
      const storedChunks: DocumentChunkWithEmbedding[] = [];

      for (let i = 0; i < embeddedChunks.length; i++) {
        const { text, embedding } = embeddedChunks[i];

        // Create document chunk
        const chunk: DocumentChunkWithEmbedding = {
          id: uuidv4(),
          documentId,
          tenantId,
          chunkIndex: i,
          content: text,
          metadata: {
            ...metadata,
            chunkIndex: i,
            totalChunks: embeddedChunks.length,
          },
          embedding,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Store chunk in repository
        const storedChunk = await this.vectorRepository.storeDocumentChunk(chunk);
        storedChunks.push(storedChunk);
      }

      return storedChunks;
    } catch (error) {
      logger.error('Failed to process document', { error, documentId, tenantId });
      throw new Error(`Failed to process document: ${(error as Error).message}`);
    }
  }

  /**
   * Splits text into chunks for embedding
   * @param text - Text to split
   * @param options - Chunk options
   * @returns Array of text chunks
   * @private
   */
  private async splitTextIntoChunks(text: string, options?: ChunkOptions): Promise<string[]> {
    try {
      // Merge options with defaults
      const chunkOptions = { ...this.defaultChunkOptions, ...options };

      // Create text splitter
      const textSplitter = new RecursiveCharacterTextSplitter({
        chunkSize: chunkOptions.chunkSize!,
        chunkOverlap: chunkOptions.chunkOverlap!,
        separators: ['\n\n', '\n', '. ', ', ', ' ', ''],
      });

      // Split text into chunks
      const chunks = await textSplitter.splitText(text);

      // Filter out chunks that are too small
      return chunks.filter(chunk => chunk.length >= (chunkOptions.minChunkSize || 0));
    } catch (error) {
      logger.error('Failed to split text into chunks', { error });
      throw new Error(`Failed to split text into chunks: ${(error as Error).message}`);
    }
  }

  /**
   * Generates embeddings for text chunks
   * @param chunks - Text chunks
   * @returns Array of chunks with embeddings
   * @private
   */
  private async generateEmbeddings(chunks: string[]): Promise<Array<{ text: string; embedding: number[] }>> {
    try {
      // Generate embeddings for all chunks
      const embeddings = await this.embeddings.embedDocuments(chunks);

      // Combine chunks with their embeddings
      return chunks.map((text, i) => ({
        text,
        embedding: embeddings[i],
      }));
    } catch (error) {
      logger.error('Failed to generate embeddings', { error });
      throw new Error(`Failed to generate embeddings: ${(error as Error).message}`);
    }
  }

  /**
   * Generates an embedding for a single query
   * @param text - Query text
   * @returns Embedding vector
   */
  async generateQueryEmbedding(text: string): Promise<number[]> {
    try {
      // Sanitize input text
      const sanitizedText = sanitizeInput(text);

      // Generate embedding for query
      return await this.embeddings.embedQuery(sanitizedText);
    } catch (error) {
      logger.error('Failed to generate query embedding', { error });
      throw new Error(`Failed to generate query embedding: ${(error as Error).message}`);
    }
  }

  /**
   * Searches for similar document chunks
   * @param query - Query text
   * @param tenantId - Tenant ID for isolation
   * @param limit - Maximum number of results
   * @param threshold - Minimum similarity threshold
   * @returns Search results with similarity scores
   */
  async searchSimilarDocuments(
    query: string,
    tenantId: string,
    limit: number = 5,
    threshold: number = 0.7
  ) {
    try {
      // Generate embedding for query
      const queryEmbedding = await this.generateQueryEmbedding(query);

      // Search for similar chunks
      return await this.vectorRepository.searchSimilarChunks(
        queryEmbedding,
        tenantId,
        limit,
        threshold
      );
    } catch (error) {
      logger.error('Failed to search similar documents', { error, tenantId });
      throw new Error(`Failed to search similar documents: ${(error as Error).message}`);
    }
  }
}
