/**
 * Secure AI Processor
 * Implements the ISecureAIProcessor interface using the :IsolatedSubsystemPattern
 * and :FacadePattern for secure document processing
 */

import * as fs from "fs";
import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import { promisify } from "util";
import { exec as execCallback } from "child_process";

import { logger } from "../logging/logger";
import { EncryptionService } from "../services/EncryptionService";
import {
  ISecureAIProcessor,
  IDocumentAnalyzer,
  DocumentFormat,
  DocumentProcessingOptions,
  DocumentProcessingStatus,
  DocumentAnalysisResult,
} from "./interfaces";
import { DocumentAnalyzer } from "./DocumentAnalyzer";
import { ContractMetadata } from "../../domain/contracts/ContractMetadata";
import { sanitizeFilePath, containsMaliciousContent } from "./util/sanitizer";

// Convert exec to Promise-based
const exec = promisify(execCallback);

/**
 * Map of processing jobs with their status
 */
interface ProcessingJob {
  status: DocumentProcessingStatus;
  startTime: Date;
  contractId: string;
  documentFormat: DocumentFormat;
  result?: DocumentAnalysisResult;
}

/**
 * Implementation of the Secure AI Processor using the :IsolatedSubsystemPattern
 * Acts as a facade for document processing operations with security isolation
 */
export class SecureAIProcessor implements ISecureAIProcessor {
  private readonly processingJobs: Map<string, ProcessingJob>;
  private readonly tempStoragePath: string;
  private readonly maxProcessingTimeMs: number;
  private readonly supportedFormats: Set<string>;
  private readonly documentAnalyzer: IDocumentAnalyzer;
  private readonly encryptionService: EncryptionService;

  /**
   * Creates a new instance of the Secure AI Processor
   *
   * @param documentAnalyzer - Document analyzer implementation (optional)
   * @param encryptionService - Encryption service for securing documents
   */
  constructor(
    documentAnalyzer?: IDocumentAnalyzer,
    encryptionService?: EncryptionService
  ) {
    this.processingJobs = new Map<string, ProcessingJob>();
    this.tempStoragePath =
      process.env.TEMP_DOCUMENT_PATH || "/tmp/secure-ai-processing";
    this.maxProcessingTimeMs = parseInt(
      process.env.MAX_PROCESSING_TIME_MS || "300000",
      10
    ); // 5 min default

    // Initialize document analyzer
    this.documentAnalyzer = documentAnalyzer || new DocumentAnalyzer();

    // Initialize encryption service with a simple mock
    this.encryptionService =
      encryptionService ||
      ({
        encrypt: async (data: string) => ({
          encryptedData: data,
          keyId: "mock",
        }),
        decrypt: async (data: string) => data,
        createHash: (data: Buffer | string) => "mock-hash",
        verifyHash: (data: Buffer | string, hash: string) => true,
        generateKey: () => "mock-key",
      } as any);

    // Define supported formats
    this.supportedFormats = new Set<string>([
      DocumentFormat.PDF,
      DocumentFormat.DOCX,
      DocumentFormat.XLSX,
      DocumentFormat.TEXT,
    ]);

    // Ensure temporary storage directory exists
    this.ensureTempStorageExists();

    // Start cleaning job for old processing jobs
    this.startCleanupJob();
  }

  /**
   * Processes a document in an isolated environment with security measures
   *
   * @param documentBuffer - Binary content of the document
   * @param documentFormat - Format of the document
   * @param contractId - ID of the contract associated with the document
   * @param options - Processing options
   * @returns Processing result with extracted metadata
   */
  async processDocument(
    documentBuffer: Buffer,
    documentFormat: DocumentFormat,
    contractId: string,
    options?: DocumentProcessingOptions
  ): Promise<{
    analysisResult: DocumentAnalysisResult;
    contractMetadata: ContractMetadata;
  }> {
    // Validate inputs
    if (!documentBuffer || documentBuffer.length === 0) {
      throw new Error("Empty document buffer provided");
    }

    if (!this.isSupportedFormat(documentFormat)) {
      throw new Error(`Unsupported document format: ${documentFormat}`);
    }

    if (!contractId) {
      throw new Error("Contract ID is required");
    }

    // Generate processing ID for tracking
    const processingId = uuidv4();

    // Create a processing job record
    this.processingJobs.set(processingId, {
      status: DocumentProcessingStatus.PENDING,
      startTime: new Date(),
      contractId,
      documentFormat,
    });

    try {
      // Update job status
      this.updateJobStatus(processingId, DocumentProcessingStatus.PROCESSING);

      // Audit log the processing start
      logger.info("Starting secure document processing", {
        contractId,
        processingId,
        documentFormat,
        documentSize: documentBuffer.length,
      });

      // Check for malicious content in document (basic check)
      const sampleText = documentBuffer.toString(
        "utf8",
        0,
        Math.min(1000, documentBuffer.length)
      );
      if (containsMaliciousContent(sampleText)) {
        throw new Error("Potential malicious content detected in document");
      }

      // Encrypt document for processing
      const encryptedData = await this.encryptDocument(documentBuffer);

      // Create isolated environment for processing
      const isolatedEnvPath = await this.createIsolatedEnvironment(
        processingId
      );

      try {
        // Write encrypted document to isolated environment
        const encryptedDocPath = path.join(isolatedEnvPath, "document.enc");
        fs.writeFileSync(encryptedDocPath, encryptedData.encryptedData);

        // Write encryption key ID to isolated environment (in real production, this would use a more secure approach)
        fs.writeFileSync(
          path.join(isolatedEnvPath, "key.id"),
          encryptedData.keyId
        );

        // Process the document in isolated environment
        const analysisResult = await this.processInIsolatedEnvironment(
          processingId,
          documentBuffer,
          documentFormat,
          options
        );

        // Convert analysis result to contract metadata
        const contractMetadata =
          this.documentAnalyzer.convertToContractMetadata(
            analysisResult,
            contractId
          );

        // Update job status
        this.updateJobStatus(
          processingId,
          DocumentProcessingStatus.COMPLETED,
          analysisResult
        );

        // Audit log the processing completion
        logger.info("Completed secure document processing", {
          contractId,
          processingId,
          documentFormat,
          processingTimeMs: analysisResult.metadata?.processingTimeMs,
        });

        return {
          analysisResult,
          contractMetadata,
        };
      } finally {
        // Clean up isolated environment
        await this.cleanupIsolatedEnvironment(isolatedEnvPath);
      }
    } catch (error) {
      // Update job status to failed
      this.updateJobStatus(processingId, DocumentProcessingStatus.FAILED);

      // Audit log the processing failure
      logger.error("Failed secure document processing", {
        contractId,
        processingId,
        documentFormat,
        error: (error as Error).message,
      });

      throw error;
    }
  }

  /**
   * Checks if a document format is supported
   *
   * @param format - Document format to check
   * @returns Boolean indicating if the format is supported
   */
  isSupportedFormat(format: string): boolean {
    return this.supportedFormats.has(format);
  }

  /**
   * Gets the current processing status of a document
   *
   * @param processingId - ID of the processing job
   * @returns Current processing status
   */
  async getProcessingStatus(
    processingId: string
  ): Promise<DocumentProcessingStatus> {
    const job = this.processingJobs.get(processingId);

    if (!job) {
      throw new Error(`Processing job not found: ${processingId}`);
    }

    return job.status;
  }

  /**
   * Updates the status of a processing job
   *
   * @param processingId - ID of the processing job
   * @param status - New status
   * @param result - Optional analysis result
   * @private
   */
  private updateJobStatus(
    processingId: string,
    status: DocumentProcessingStatus,
    result?: DocumentAnalysisResult
  ): void {
    const job = this.processingJobs.get(processingId);

    if (job) {
      job.status = status;

      if (result) {
        job.result = result;
      }
    }
  }

  /**
   * Ensures the temporary storage directory exists
   *
   * @private
   */
  private ensureTempStorageExists(): void {
    try {
      if (!fs.existsSync(this.tempStoragePath)) {
        fs.mkdirSync(this.tempStoragePath, { recursive: true });
      }
    } catch (error) {
      logger.error("Failed to create temporary storage directory", {
        error,
        path: this.tempStoragePath,
      });
    }
  }

  /**
   * Starts a job to clean up old processing records and temporary files
   *
   * @private
   */
  private startCleanupJob(): void {
    // Run cleanup every hour
    setInterval(() => {
      try {
        // Clean up old processing jobs
        const now = Date.now();

        for (const [id, job] of this.processingJobs.entries()) {
          const jobAge = now - job.startTime.getTime();

          // Remove jobs older than 24 hours
          if (jobAge > 24 * 60 * 60 * 1000) {
            this.processingJobs.delete(id);
          }
        }

        // Clean up old temporary files (async)
        this.cleanupOldTempFiles().catch((error) => {
          logger.error("Failed to clean up old temporary files", { error });
        });
      } catch (error) {
        logger.error("Error in cleanup job", { error });
      }
    }, 60 * 60 * 1000); // 1 hour
  }

  /**
   * Cleans up old temporary files
   *
   * @private
   */
  private async cleanupOldTempFiles(): Promise<void> {
    try {
      // Find files older than 24 hours
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 24);

      const files = fs.readdirSync(this.tempStoragePath);

      for (const file of files) {
        try {
          const filePath = path.join(this.tempStoragePath, file);
          const stats = fs.statSync(filePath);

          if (stats.isDirectory() && stats.mtime < cutoffTime) {
            // Remove old directory
            fs.rmdirSync(filePath, { recursive: true });
          } else if (stats.isFile() && stats.mtime < cutoffTime) {
            // Remove old file
            fs.unlinkSync(filePath);
          }
        } catch (fileError) {
          logger.warn("Failed to clean up file", { file, error: fileError });
        }
      }
    } catch (error) {
      logger.error("Failed to clean up old temporary files", { error });
    }
  }

  /**
   * Creates an isolated environment for document processing
   *
   * @param processingId - ID of the processing job
   * @returns Path to the isolated environment
   * @private
   */
  private async createIsolatedEnvironment(
    processingId: string
  ): Promise<string> {
    // Create a directory for this processing job
    const isolatedPath = path.join(
      this.tempStoragePath,
      sanitizeFilePath(processingId)
    );

    try {
      fs.mkdirSync(isolatedPath, { recursive: true });

      // Make sure permissions are restricted (in a production environment, this would be more comprehensive)
      fs.chmodSync(isolatedPath, 0o700);

      return isolatedPath;
    } catch (error) {
      logger.error("Failed to create isolated environment", {
        error,
        processingId,
      });
      throw new Error(
        `Failed to create isolated environment: ${(error as Error).message}`
      );
    }
  }

  /**
   * Cleans up an isolated environment
   *
   * @param envPath - Path to the isolated environment
   * @private
   */
  private async cleanupIsolatedEnvironment(envPath: string): Promise<void> {
    try {
      if (fs.existsSync(envPath)) {
        // Delete the directory and all its contents
        fs.rmdirSync(envPath, { recursive: true });
      }
    } catch (error) {
      logger.warn("Failed to clean up isolated environment", {
        error,
        path: envPath,
      });
    }
  }

  /**
   * Encrypts a document for secure processing
   *
   * @param documentBuffer - Document buffer to encrypt
   * @returns Encrypted document data
   * @private
   */
  private async encryptDocument(
    documentBuffer: Buffer
  ): Promise<{ encryptedData: string; keyId: string }> {
    try {
      // Create a hash of the document for verification
      const documentHash = this.encryptionService.createHash(documentBuffer);

      // Encrypt the document with the hash
      const encryptedData = await this.encryptionService.encrypt(
        documentBuffer.toString("base64")
      );

      return encryptedData;
    } catch (error) {
      logger.error("Failed to encrypt document", { error });
      throw new Error(
        `Failed to encrypt document: ${(error as Error).message}`
      );
    }
  }

  /**
   * Processes a document in an isolated environment
   *
   * @param processingId - ID of the processing job
   * @param documentBuffer - Document buffer
   * @param documentFormat - Format of the document
   * @param options - Processing options
   * @returns Analysis result
   * @private
   */
  private async processInIsolatedEnvironment(
    processingId: string,
    documentBuffer: Buffer,
    documentFormat: DocumentFormat,
    options?: DocumentProcessingOptions
  ): Promise<DocumentAnalysisResult> {
    // In a real implementation, this would run in a container, VM, or sandbox
    // For this implementation, we'll use the document analyzer directly with security precautions

    // Set timeout to prevent infinite processing
    const timeoutMs = options?.timeoutSeconds
      ? options.timeoutSeconds * 1000
      : this.maxProcessingTimeMs;

    try {
      // Process the document with timeout
      const analysisPromise = this.documentAnalyzer.analyzeDocument(
        documentBuffer,
        documentFormat,
        options
      );

      // Create a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(
            new Error(`Document processing timed out after ${timeoutMs}ms`)
          );
        }, timeoutMs);
      });

      // Race the promises
      return await Promise.race([analysisPromise, timeoutPromise]);
    } catch (error) {
      logger.error("Error in isolated processing environment", {
        error,
        processingId,
      });

      // Return a failed analysis result
      return {
        status: DocumentProcessingStatus.FAILED,
        processedAt: new Date(),
        error: `Processing failed: ${(error as Error).message}`,
        metadata: {
          processingTimeMs: timeoutMs,
          originalFormat: documentFormat,
        },
      };
    }
  }
}
