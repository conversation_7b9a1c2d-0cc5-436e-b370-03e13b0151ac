/**
 * Widget Service
 * Handles widget management operations for dashboard customization
 */

import { PrismaClient } from '@prisma/client';
import { DEFAULT_WIDGETS, DefaultWidget } from '../constants/defaultWidgets';

export interface WidgetData {
  id: string;
  userId: string;
  name: string;
  description?: string;
  category: 'priority' | 'portfolio';
  widgetType: string;
  isVisible: boolean;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  configuration: any;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupedWidgets {
  priority: WidgetData[];
  portfolio: WidgetData[];
}

export class WidgetService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Ensure user has default widgets, create them if they don't exist
   */
  async ensureUserDashboardWidgets(userId: string): Promise<GroupedWidgets> {
    const existingWidgets = await this.prisma.dashboardLayout.findMany({
      where: { userId },
      orderBy: [{ category: 'asc' }, { order: 'asc' }]
    });

    if (existingWidgets.length === 0) {
      await this.createDefaultWidgets(userId);
      return this.getWidgetsByUser(userId);
    }

    return this.groupWidgetsByCategory(existingWidgets);
  }

  /**
   * Create default widgets for a user
   */
  async createDefaultWidgets(userId: string): Promise<void> {
    const widgetData = DEFAULT_WIDGETS.map((widget: DefaultWidget) => ({
      userId,
      name: widget.name,
      description: widget.description,
      category: widget.category,
      widgetType: widget.widgetType,
      isVisible: widget.isVisible,
      position: widget.position,
      configuration: widget.configuration,
      order: widget.order
    }));

    await this.prisma.dashboardLayout.createMany({
      data: widgetData
    });
  }

  /**
   * Get all widgets for a user grouped by category
   */
  async getWidgetsByUser(userId: string): Promise<GroupedWidgets> {
    const widgets = await this.prisma.dashboardLayout.findMany({
      where: { userId },
      orderBy: [{ category: 'asc' }, { order: 'asc' }]
    });

    return this.groupWidgetsByCategory(widgets);
  }

  /**
   * Get widgets by category for a user
   */
  async getWidgetsByCategory(userId: string, category: 'priority' | 'portfolio'): Promise<WidgetData[]> {
    const widgets = await this.prisma.dashboardLayout.findMany({
      where: { userId, category },
      orderBy: { order: 'asc' }
    });

    return widgets.map(this.mapToWidgetData);
  }

  /**
   * Get visible widgets by category for a user
   */
  async getVisibleWidgetsByCategory(userId: string, category: 'priority' | 'portfolio'): Promise<WidgetData[]> {
    const widgets = await this.prisma.dashboardLayout.findMany({
      where: { userId, category, isVisible: true },
      orderBy: { order: 'asc' }
    });

    return widgets.map(this.mapToWidgetData);
  }

  /**
   * Update widget visibility
   */
  async updateWidgetVisibility(widgetId: string, userId: string, isVisible: boolean): Promise<WidgetData> {
    const widget = await this.prisma.dashboardLayout.update({
      where: { id: widgetId, userId },
      data: { isVisible, updatedAt: new Date() }
    });

    return this.mapToWidgetData(widget);
  }

  /**
   * Update widget position
   */
  async updateWidgetPosition(
    widgetId: string, 
    userId: string, 
    position: { x: number; y: number; w: number; h: number }
  ): Promise<WidgetData> {
    const widget = await this.prisma.dashboardLayout.update({
      where: { id: widgetId, userId },
      data: { position, updatedAt: new Date() }
    });

    return this.mapToWidgetData(widget);
  }

  /**
   * Update widget configuration
   */
  async updateWidgetConfiguration(
    widgetId: string, 
    userId: string, 
    configuration: any
  ): Promise<WidgetData> {
    const widget = await this.prisma.dashboardLayout.update({
      where: { id: widgetId, userId },
      data: { configuration, updatedAt: new Date() }
    });

    return this.mapToWidgetData(widget);
  }

  /**
   * Update widget order within category
   */
  async updateWidgetOrder(
    widgetId: string, 
    userId: string, 
    newOrder: number
  ): Promise<WidgetData> {
    const widget = await this.prisma.dashboardLayout.update({
      where: { id: widgetId, userId },
      data: { order: newOrder, updatedAt: new Date() }
    });

    return this.mapToWidgetData(widget);
  }

  /**
   * Bulk update multiple widgets
   */
  async bulkUpdateWidgets(
    userId: string, 
    updates: Array<{ id: string; data: Partial<WidgetData> }>
  ): Promise<WidgetData[]> {
    const updatePromises = updates.map(({ id, data }) =>
      this.prisma.dashboardLayout.update({
        where: { id, userId },
        data: { ...data, updatedAt: new Date() }
      })
    );

    const updatedWidgets = await Promise.all(updatePromises);
    return updatedWidgets.map(this.mapToWidgetData);
  }

  /**
   * Reset user widgets to defaults
   */
  async resetUserWidgets(userId: string): Promise<GroupedWidgets> {
    // Delete existing widgets
    await this.prisma.dashboardLayout.deleteMany({
      where: { userId }
    });

    // Create default widgets
    await this.createDefaultWidgets(userId);

    return this.getWidgetsByUser(userId);
  }

  /**
   * Group widgets by category
   */
  private groupWidgetsByCategory(widgets: any[]): GroupedWidgets {
    const grouped: GroupedWidgets = {
      priority: [],
      portfolio: []
    };

    widgets.forEach(widget => {
      const widgetData = this.mapToWidgetData(widget);
      if (widget.category === 'priority') {
        grouped.priority.push(widgetData);
      } else if (widget.category === 'portfolio') {
        grouped.portfolio.push(widgetData);
      }
    });

    return grouped;
  }

  /**
   * Map database record to WidgetData interface
   */
  private mapToWidgetData(widget: any): WidgetData {
    return {
      id: widget.id,
      userId: widget.userId,
      name: widget.name,
      description: widget.description,
      category: widget.category,
      widgetType: widget.widgetType,
      isVisible: widget.isVisible,
      position: widget.position,
      configuration: widget.configuration,
      order: widget.order,
      createdAt: widget.createdAt,
      updatedAt: widget.updatedAt
    };
  }
}
