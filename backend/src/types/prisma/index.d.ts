declare module '@prisma/client/runtime/library' {
  export class Vector extends Array<number> {}
}

declare module '@prisma/client' {
  interface DocumentChunk {
    embedding?: number[] | any;
  }

  interface Contract {
    versions?: any[];
    version?: string;
  }

  interface License {
    usageData?: any[];
    costs?: any[];
    seats?: number;
    billingCycle?: string;
  }

  interface ContractInclude {
    versions?: boolean;
  }

  interface LicenseInclude {
    usageData?: boolean;
  }
}

export {};
