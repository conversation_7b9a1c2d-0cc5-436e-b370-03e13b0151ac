/**
 * Server Entry Point
 * Configures and starts the Express server with proper security and multi-tenancy
 */

import express, { Request, Response } from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import compression from "compression";
import morgan from "morgan";
import { configureRoutes } from "./api/routes";
import { logger } from "./infrastructure/logging/logger";
import { TenantContext } from "./domain/shared/TenantContext";
import { ConfigService } from "./infrastructure/services/ConfigService";
import { errorHandler } from "./infrastructure/middleware/errorHandler";
import { notFoundHandler } from "./infrastructure/middleware/notFoundHandler";
import { ChatCleanupService } from "./api/services/ChatCleanupService";
import { PrismaClient } from "@prisma/client";

// Create Express app
const app = express();
const configService = new ConfigService();
const port = configService.getPort();

// Configure trust proxy for production deployment
// This is essential for rate limiting and getting real client IPs behind proxies
const trustProxyConfig = configService.getTrustProxy();
app.set("trust proxy", trustProxyConfig);
logger.info("Trust proxy configured", {
  environment: configService.getEnvironment(),
  trustProxy: trustProxyConfig,
});

// Initialize services
const prisma = new PrismaClient();
const chatCleanupService = new ChatCleanupService(prisma);

// Test database connection
async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    logger.info("✅ Database connection successful");
    logger.info("Database connection details:", {
      provider: "PostgreSQL",
      status: "Connected",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("❌ Database connection failed:", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
}

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"], // Adjust based on your needs
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "blob:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    xssFilter: true,
    noSniff: true,
    referrerPolicy: { policy: "same-origin" },
  })
);

// CORS configuration - Allow all origins
app.use(
  cors({
    origin: "*", // Allow all origins
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
    ],
    maxAge: 86400, // 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204,
  })
);

// Additional middleware from index.ts
app.use(compression()); // Compress responses
app.use(morgan(configService.isDevelopment() ? "dev" : "combined")); // HTTP request logging

// Rate limiting to prevent abuse
// Configure rate limiting with proper proxy support
const rateLimitSettings = configService.getRateLimitConfig();
const rateLimitConfig = {
  windowMs: rateLimitSettings.windowMs,
  max: rateLimitSettings.maxRequests,
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: "Too many requests, please try again later" },
  // Skip successful requests (2xx and 3xx responses)
  skipSuccessfulRequests: rateLimitSettings.skipSuccessfulRequests,
  // Skip failed requests (4xx and 5xx responses)
  skipFailedRequests: rateLimitSettings.skipFailedRequests,
  // Key generator function to handle proxy scenarios
  keyGenerator: (req: Request): string => {
    // In production, use X-Forwarded-For header if available, otherwise fall back to req.ip
    if (configService.isProduction()) {
      const forwardedFor = req.headers["x-forwarded-for"];
      if (forwardedFor && typeof forwardedFor === "string") {
        // Get the first IP from the X-Forwarded-For header
        return forwardedFor.split(",")[0].trim();
      }
    }
    return req.ip || "unknown";
  },
  // Handler for when rate limit is exceeded
  handler: (req: Request, res: Response) => {
    logger.warn("Rate limit exceeded", {
      ip: req.ip,
      forwardedFor: req.headers["x-forwarded-for"],
      userAgent: req.headers["user-agent"],
      path: req.path,
      method: req.method,
    });
    res.status(429).json({
      error: "Too many requests, please try again later",
      retryAfter: Math.round(rateLimitConfig.windowMs / 1000),
    });
  },
};

app.use(rateLimit(rateLimitConfig));

// Log rate limiting configuration
logger.info("Rate limiting configured", {
  windowMs: rateLimitSettings.windowMs,
  maxRequests: rateLimitSettings.maxRequests,
  skipSuccessfulRequests: rateLimitSettings.skipSuccessfulRequests,
  skipFailedRequests: rateLimitSettings.skipFailedRequests,
  environment: configService.getEnvironment(),
});

// Enhanced health check endpoint
app.get("/health", (req, res) => {
  try {
    // Basic health information
    const healthInfo = {
      status: "ok",
      timestamp: new Date().toISOString(),
      environment: configService.getEnvironment(),
      requestInfo: {
        ip: req.ip,
        method: req.method,
        path: req.path,
        headers: {
          host: req.headers.host,
          origin: req.headers.origin,
          userAgent: req.headers["user-agent"],
        },
      },
      server: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version,
      },
    };

    res.status(200).json(healthInfo);

    // Log health check for debugging
    if (configService.get("LOG_HEALTH_CHECKS") === "true") {
      logger.info("Health check request", { healthInfo });
    }
  } catch (error) {
    logger.error("Health check error", { error });
    res.status(500).json({ status: "error", message: "Health check failed" });
  }
});

// Body parsing middleware with increased limits for file uploads
app.use(express.json({ limit: "50mb" })); // Increased limit for file uploads
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Set server timeout for long-running operations (10 minutes)
app.use((req, res, next) => {
  // Set longer timeout for import endpoints and AI operations
  if (req.path.includes("/import") || req.path.includes("/upload") || req.path.includes("/ai") || req.path.includes("/refresh")) {
    req.setTimeout(600000); // 10 minutes for import operations and AI processing
    res.setTimeout(600000); // 10 minutes for import operations and AI processing
  } else {
    req.setTimeout(120000); // 2 minutes for other operations
    res.setTimeout(120000); // 2 minutes for other operations
  }
  next();
});

// Initialize tenant context for each request
app.use(TenantContext.middleware());

// Configure routes
try {
  configureRoutes(app);
  logger.info("Routes configured successfully");
} catch (error) {
  logger.error("Error configuring routes:", error);
  throw error;
}

// Add 404 handler
app.use(notFoundHandler);

// Error handling middleware
app.use(errorHandler);

// Start server with database connection test
async function startServer() {
  try {
    // Test database connection first
    await testDatabaseConnection();

    // Start the server
    const server = app.listen(port, () => {
      logger.info(
        `Server running on port ${port} in ${configService.getEnvironment()} mode`
      );
      logger.info(`CORS configuration: All origins allowed (*)`);

      // Start chat cleanup service (with delay to avoid startup issues)
      setTimeout(() => {
        try {
          chatCleanupService.start();
        } catch (error) {
          logger.error("Error starting chat cleanup service:", error);
        }
      }, 10000); // 10 second delay
    });

    return server;
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Initialize server
(async () => {
  const server = await startServer();

  // Handle server errors
  server.on("error", (error) => {
    logger.error("Server error:", {
      error: error.message,
      stack: error.stack,
      fullError: error,
    });
    chatCleanupService.stop();
    process.exit(1);
  });

  // Handle unhandled promise rejections
  process.on("unhandledRejection", (reason, promise) => {
    logger.error("Unhandled Rejection at:", { promise, reason });
  });

  // Handle uncaught exceptions
  process.on("uncaughtException", (error) => {
    logger.error("Uncaught Exception:", { error });
    chatCleanupService.stop();
    process.exit(1);
  });

  // Graceful shutdown
  process.on("SIGTERM", () => {
    logger.info("SIGTERM received, shutting down gracefully");
    chatCleanupService.stop();
    server.close(() => {
      logger.info("Process terminated");
      process.exit(0);
    });
  });

  process.on("SIGINT", () => {
    logger.info("SIGINT received, shutting down gracefully");
    chatCleanupService.stop();
    server.close(() => {
      logger.info("Process terminated");
      process.exit(0);
    });
  });
})();

export default app;
