/**
 * Migration Script: Migrate to Widget System
 * Migrates existing users to the new widget-based dashboard system
 */

import { PrismaClient } from '@prisma/client';
import { WidgetService } from '../services/WidgetService';

const prisma = new PrismaClient();
const widgetService = new WidgetService(prisma);

interface MigrationStats {
  totalUsers: number;
  usersWithOldLayouts: number;
  usersWithoutLayouts: number;
  usersMigrated: number;
  usersWithNewWidgets: number;
  errors: string[];
}

async function migrateToWidgetSystem(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalUsers: 0,
    usersWithOldLayouts: 0,
    usersWithoutLayouts: 0,
    usersMigrated: 0,
    usersWithNewWidgets: 0,
    errors: []
  };

  try {
    console.log('🚀 Starting migration to widget system...');

    // Get all users
    const users = await prisma.user.findMany({
      select: { id: true, email: true, name: true }
    });

    stats.totalUsers = users.length;
    console.log(`📊 Found ${stats.totalUsers} users to process`);

    for (const user of users) {
      try {
        console.log(`👤 Processing user: ${user.email} (${user.id})`);

        // Check if user already has widgets in new format
        const existingWidgets = await prisma.dashboardLayout.findMany({
          where: { userId: user.id }
        });

        if (existingWidgets.length > 0) {
          // Check if these are new format widgets (have category and widgetType)
          const hasNewFormat = existingWidgets.some(widget => 
            widget.category && widget.widgetType
          );

          if (hasNewFormat) {
            console.log(`  ✅ User already has new widget format (${existingWidgets.length} widgets)`);
            stats.usersWithNewWidgets++;
            continue;
          } else {
            console.log(`  🔄 User has old format layouts, migrating...`);
            stats.usersWithOldLayouts++;
            
            // Delete old format layouts
            await prisma.dashboardLayout.deleteMany({
              where: { userId: user.id }
            });
            
            console.log(`  🗑️ Deleted ${existingWidgets.length} old format layouts`);
          }
        } else {
          console.log(`  📝 User has no existing layouts`);
          stats.usersWithoutLayouts++;
        }

        // Create default widgets for user
        await widgetService.createDefaultWidgets(user.id);
        console.log(`  ✨ Created default widgets for user`);
        
        stats.usersMigrated++;

      } catch (error) {
        const errorMsg = `Failed to migrate user ${user.email}: ${error}`;
        console.error(`  ❌ ${errorMsg}`);
        stats.errors.push(errorMsg);
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`  Total users: ${stats.totalUsers}`);
    console.log(`  Users with old layouts: ${stats.usersWithOldLayouts}`);
    console.log(`  Users without layouts: ${stats.usersWithoutLayouts}`);
    console.log(`  Users already with new widgets: ${stats.usersWithNewWidgets}`);
    console.log(`  Users migrated: ${stats.usersMigrated}`);
    console.log(`  Errors: ${stats.errors.length}`);

    if (stats.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      stats.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('\n✅ Migration completed successfully!');

  } catch (error) {
    console.error('💥 Migration failed:', error);
    stats.errors.push(`Migration failed: ${error}`);
  }

  return stats;
}

/**
 * Verify migration results
 */
async function verifyMigration(): Promise<void> {
  console.log('\n🔍 Verifying migration results...');

  try {
    // Count total widgets created
    const totalWidgets = await prisma.dashboardLayout.count();
    console.log(`📊 Total widgets in database: ${totalWidgets}`);

    // Count widgets by category
    const priorityWidgets = await prisma.dashboardLayout.count({
      where: { category: 'priority' }
    });
    const portfolioWidgets = await prisma.dashboardLayout.count({
      where: { category: 'portfolio' }
    });

    console.log(`  Priority widgets: ${priorityWidgets}`);
    console.log(`  Portfolio widgets: ${portfolioWidgets}`);

    // Count users with widgets
    const usersWithWidgets = await prisma.user.count({
      where: {
        dashboardLayouts: {
          some: {}
        }
      }
    });

    console.log(`👥 Users with widgets: ${usersWithWidgets}`);

    // Sample widget data
    const sampleWidget = await prisma.dashboardLayout.findFirst({
      include: { user: { select: { email: true } } }
    });

    if (sampleWidget) {
      console.log('\n📋 Sample widget:');
      console.log(`  User: ${sampleWidget.user.email}`);
      console.log(`  Name: ${sampleWidget.name}`);
      console.log(`  Category: ${sampleWidget.category}`);
      console.log(`  Widget Type: ${sampleWidget.widgetType}`);
      console.log(`  Visible: ${sampleWidget.isVisible}`);
      console.log(`  Position: ${JSON.stringify(sampleWidget.position)}`);
    }

    console.log('\n✅ Verification completed!');

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

/**
 * Main migration function
 */
async function main() {
  try {
    const stats = await migrateToWidgetSystem();
    await verifyMigration();
    
    if (stats.errors.length === 0) {
      console.log('\n🎉 Migration completed successfully with no errors!');
      process.exit(0);
    } else {
      console.log(`\n⚠️ Migration completed with ${stats.errors.length} errors.`);
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Migration script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  main();
}

export { migrateToWidgetSystem, verifyMigration };
