import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkWidgets() {
  try {
    console.log('🔍 Checking dashboard widgets...');
    
    const widgets = await prisma.dashboardLayout.findMany({
      orderBy: [{ userId: 'asc' }, { category: 'asc' }, { order: 'asc' }]
    });
    
    console.log(`📊 Total widgets: ${widgets.length}`);
    
    if (widgets.length === 0) {
      console.log('❌ No widgets found in database');
      return;
    }
    
    // Group by user
    const userGroups = widgets.reduce((acc, widget) => {
      if (!acc[widget.userId]) {
        acc[widget.userId] = [];
      }
      acc[widget.userId].push(widget);
      return acc;
    }, {} as Record<string, any[]>);
    
    console.log(`👥 Users with widgets: ${Object.keys(userGroups).length}`);
    
    for (const [userId, userWidgets] of Object.entries(userGroups)) {
      console.log(`\n👤 User: ${userId}`);
      console.log(`  Total widgets: ${userWidgets.length}`);
      
      const priorityWidgets = userWidgets.filter(w => w.category === 'priority');
      const portfolioWidgets = userWidgets.filter(w => w.category === 'portfolio');
      
      console.log(`  Priority widgets: ${priorityWidgets.length}`);
      console.log(`  Portfolio widgets: ${portfolioWidgets.length}`);
      
      // Show first widget as sample
      if (userWidgets.length > 0) {
        const sample = userWidgets[0];
        console.log(`  Sample widget:`, {
          name: sample.name,
          category: sample.category,
          widgetType: sample.widgetType,
          isVisible: sample.isVisible,
          order: sample.order
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking widgets:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkWidgets();
