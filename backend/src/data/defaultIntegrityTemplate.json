{"configurationName": "Default Integrity Configuration", "description": "Standard integrity assessment based on key contractual clauses", "version": "1.0", "clauses": [{"id": "termination_convenience", "name": "Termination for convenience", "description": "Who has the right to terminate the contract for convenience", "category": "Legal/contractual", "possibleValues": [{"value": "Yes, available to customer only", "weightage": 10.0, "riskLevel": "Low", "description": "Low risk - only customer can terminate"}, {"value": "Yes, available to both", "weightage": 5.0, "riskLevel": "Medium", "description": "Medium risk - both parties can terminate"}, {"value": "Yes, available to supplier only", "weightage": 0.0, "riskLevel": "High", "description": "High risk - only supplier can terminate"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - termination rights not mentioned"}]}, {"id": "price_revision", "name": "Revision in Prices", "description": "Flexibility for price changes during contract term", "category": "Commercial terms", "possibleValues": [{"value": "No change throughout the term", "weightage": 10.0, "riskLevel": "Low", "description": "Low risk - prices are fixed"}, {"value": "Revision possible anytime", "weightage": 0.0, "riskLevel": "High", "description": "High risk - prices can change anytime"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - price changes not specified"}]}, {"id": "invoicing_payment", "name": "Invoicing and payment", "description": "Payment terms and timeline", "category": "Commercial terms", "possibleValues": [{"value": ">60 days", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - extended payment terms"}, {"value": "30-60 days", "weightage": 2.5, "riskLevel": "Medium", "description": "Medium risk - standard payment terms"}, {"value": "<30 days", "weightage": 0.0, "riskLevel": "High", "description": "High risk - quick payment terms"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - payment terms not specified"}]}, {"id": "limitation_liability", "name": "Limitation of liability", "description": "Liability cap as percentage of contract value", "category": "Legal/contractual", "possibleValues": [{"value": "Capped at >100% of the charge paid/payable for the products/services delivered in 12 months prior to the event", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - liability capped below contract value"}, {"value": "Capped at 100% of the charge paid/payable for the products/services delivered in 12 months prior to the event", "weightage": 2.5, "riskLevel": "Medium", "description": "Medium risk - liability capped at contract value"}, {"value": "Capped at <100% of the charge paid/payable for the products/services delivered in 12 months prior to the event", "weightage": 0.0, "riskLevel": "High", "description": "High risk - liability capped above contract value"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - liability not capped"}]}, {"id": "intellectual_property", "name": "Intellectual property rights (IPR)", "description": "Ownership of customizations and IP", "category": "Legal/contractual", "possibleValues": [{"value": "IPR of customizations to be in Customer's name", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - customer owns customizations"}, {"value": "IPR of customizations to be in Supplier's name", "weightage": 0.0, "riskLevel": "High", "description": "High risk - supplier owns customizations"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - IPR not specified"}]}, {"id": "audit_rights", "name": "Right to audit", "description": "Customer's right to audit supplier operations", "category": "Legal/contractual", "possibleValues": [{"value": "Mentioned", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - audit rights specified"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - no audit rights"}]}, {"id": "sla_response_time_p1", "name": "SLA: Response time (P1)", "description": "Service level agreement for P1 incident response time", "category": "Operational", "possibleValues": [{"value": "<1 hr", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - fast P1 response time"}, {"value": ">1 hr", "weightage": 0.0, "riskLevel": "High", "description": "High risk - slow P1 response time"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - P1 response time not specified"}]}, {"id": "sla_response_time_p2", "name": "SLA: Response time (P2)", "description": "Service level agreement for P2 incident response time", "category": "Operational", "possibleValues": [{"value": "<4 hrs", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - fast P2 response time"}, {"value": ">4 hrs", "weightage": 0.0, "riskLevel": "High", "description": "High risk - slow P2 response time"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - P2 response time not specified"}]}, {"id": "sla_resolution_time_p1", "name": "SLA: Resolution time (P1)", "description": "Service level agreement for P1 incident resolution time", "category": "Operational", "possibleValues": [{"value": "<4 hrs", "weightage": 10.0, "riskLevel": "Low", "description": "Low risk - fast P1 resolution time"}, {"value": ">4 hrs", "weightage": 0.0, "riskLevel": "High", "description": "High risk - slow P1 resolution time"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - P1 resolution time not specified"}]}, {"id": "sla_resolution_time_p2", "name": "SLA: Resolution time (P2)", "description": "Service level agreement for P2 incident resolution time", "category": "Operational", "possibleValues": [{"value": "<8 hrs", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - fast P2 resolution time"}, {"value": ">8 hrs", "weightage": 0.0, "riskLevel": "High", "description": "High risk - slow P2 resolution time"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - P2 resolution time not specified"}]}, {"id": "resource_replacement", "name": "Timeline for replacement of resources", "description": "Time allowed for replacing personnel/resources", "category": "Operational", "possibleValues": [{"value": "Mentioned", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - replacement timeline specified"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - no replacement timeline"}]}, {"id": "jurisdiction", "name": "Juris<PERSON>", "description": "Legal jurisdiction for dispute resolution", "category": "Legal/contractual", "possibleValues": [{"value": "Customer's HQ", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - customer's jurisdiction"}, {"value": "Supplier's HQ", "weightage": 2.5, "riskLevel": "Medium", "description": "Medium risk - supplier's jurisdiction"}, {"value": "Other or not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - other jurisdiction or not specified"}]}, {"id": "payment_suspension", "name": "Right to suspend payments", "description": "Customer's right to suspend payments", "category": "Commercial terms", "possibleValues": [{"value": "Mentioned", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - payment suspension rights specified"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - no payment suspension rights"}]}, {"id": "indemnity", "name": "Indemnity", "description": "Supplier's indemnification obligations", "category": "Legal/contractual", "possibleValues": [{"value": "Supplier indemnifies customer against all third party claims (uncapped) + associated costs", "weightage": 10.0, "riskLevel": "Low", "description": "Low risk - full indemnification with costs"}, {"value": "Supplier indemnifies customer against all third party claims (uncapped)", "weightage": 5.0, "riskLevel": "Medium", "description": "Medium risk - full indemnification without costs"}, {"value": "Capped indemnity", "weightage": 2.5, "riskLevel": "Medium", "description": "Medium risk - capped indemnification"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - no indemnification"}]}, {"id": "third_party_use", "name": "Third party use (use by affiliates)", "description": "Rights for affiliates to use the services", "category": "Use rights & restrictions", "possibleValues": [{"value": "Allowed", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - affiliate usage allowed"}, {"value": "Not allowed", "weightage": 0.0, "riskLevel": "High", "description": "High risk - affiliate usage restricted"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - affiliate usage not specified"}]}, {"id": "territorial_use", "name": "Territorial use (no geographical restrictions)", "description": "Geographical restrictions on service usage", "category": "Use rights & restrictions", "possibleValues": [{"value": "Allowed", "weightage": 5.0, "riskLevel": "Low", "description": "Low risk - no geographical restrictions"}, {"value": "Not allowed", "weightage": 0.0, "riskLevel": "High", "description": "High risk - geographical restrictions apply"}, {"value": "Not mentioned", "weightage": 0.0, "riskLevel": "High", "description": "High risk - geographical restrictions not specified"}]}]}