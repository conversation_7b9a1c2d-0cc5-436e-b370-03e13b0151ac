/*
  Warnings:

  - You are about to drop the column `aiGeneratedSummary` on the `ContractMetadata` table. All the data in the column will be lost.
  - You are about to drop the column `aiGeneratedTabularSummary` on the `ContractMetadata` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "ContractMetadata" DROP COLUMN "aiGeneratedSummary",
DROP COLUMN "aiGeneratedTabularSummary";

-- CreateTable
CREATE TABLE "ContractExtraction" (
    "id" TEXT NOT NULL,
    "contractId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "fixedFields" JSONB NOT NULL,
    "dynamicFields" JSONB NOT NULL,
    "specialFields" JSONB NOT NULL,
    "extractionDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "extractionVersion" TEXT NOT NULL DEFAULT '1.0',
    "overallConfidence" DOUBLE PRECISION,
    "processingTimeMs" INTEGER,
    "modelUsed" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ContractExtraction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ContractExtraction_contractId_key" ON "ContractExtraction"("contractId");

-- CreateIndex
CREATE INDEX "ContractExtraction_contractId_idx" ON "ContractExtraction"("contractId");

-- CreateIndex
CREATE INDEX "ContractExtraction_tenantId_idx" ON "ContractExtraction"("tenantId");

-- CreateIndex
CREATE INDEX "ContractExtraction_extractionDate_idx" ON "ContractExtraction"("extractionDate");
