/*
  Warnings:

  - You are about to drop the column `isDefault` on the `DashboardLayout` table. All the data in the column will be lost.
  - You are about to drop the column `widgets` on the `DashboardLayout` table. All the data in the column will be lost.
  - Added the required column `category` to the `DashboardLayout` table without a default value. This is not possible if the table is not empty.
  - Added the required column `position` to the `DashboardLayout` table without a default value. This is not possible if the table is not empty.
  - Added the required column `widgetType` to the `DashboardLayout` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "DashboardLayout" DROP COLUMN "isDefault",
DROP COLUMN "widgets",
ADD COLUMN     "category" TEXT NOT NULL,
ADD COLUMN     "configuration" JSONB,
ADD COLUMN     "isVisible" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "position" JSONB NOT NULL,
ADD COLUMN     "widgetType" TEXT NOT NULL;

-- CreateIndex
CREATE INDEX "DashboardLayout_userId_category_idx" ON "DashboardLayout"("userId", "category");

-- CreateIndex
CREATE INDEX "DashboardLayout_userId_widgetType_idx" ON "DashboardLayout"("userId", "widgetType");
