-- Migration to restructure Oracle fields into nested JSON structure
-- First, migrate existing Oracle field data to the new oracleFields JSON column

-- Add the new oracleFields column
ALTER TABLE "ContractMetadata" ADD COLUMN "oracleFields" JSONB;

-- Migrate existing Oracle field data to the new JSON structure
UPDATE "ContractMetadata" 
SET "oracleFields" = jsonb_build_object(
  'publisher', "publisher",
  'reseller', "reseller",
  'entitled_entity', "entitledEntity",
  'entitled_entity_country', "entitledEntityCountry",
  'product_name', "productName",
  'raw_product_name', "rawProductName",
  'total_quantity', "totalQuantity",
  'metric', "metric",
  'metric_definition', "metricDefinition",
  'term', "term",
  'level', "level",
  'limitations', "limitations",
  'included_rights', "includedRights",
  'csi', "csi",
  'purchase_date', "purchaseDate",
  'governing_agreement', "governingAgreement",
  'support_contract_number', "supportContractNumber",
  'support_start_date', "supportStartDate",
  'support_end_date', "supportEndDate",
  'original_document_name', "originalDocumentName",
  'document_type', "documentType",
  'license_value', "licenseValue",
  'license_value_per_unit', "licenseValuePerUnit",
  'contractual_support_value', "contractualSupportValue",
  'support_value_per_year', "supportValuePerYear",
  'support_value_per_year_per_unit', "supportValuePerYearPerUnit",
  'oracle_currency', "oracleCurrency",
  'index_field', "indexField",
  'delta', "delta"
)
WHERE "publisher" IS NOT NULL 
   OR "reseller" IS NOT NULL 
   OR "entitledEntity" IS NOT NULL 
   OR "entitledEntityCountry" IS NOT NULL 
   OR "productName" IS NOT NULL 
   OR "rawProductName" IS NOT NULL 
   OR "totalQuantity" IS NOT NULL 
   OR "metric" IS NOT NULL 
   OR "metricDefinition" IS NOT NULL 
   OR "term" IS NOT NULL 
   OR "level" IS NOT NULL 
   OR "limitations" IS NOT NULL 
   OR "includedRights" IS NOT NULL 
   OR "csi" IS NOT NULL 
   OR "purchaseDate" IS NOT NULL 
   OR "governingAgreement" IS NOT NULL 
   OR "supportContractNumber" IS NOT NULL 
   OR "supportStartDate" IS NOT NULL 
   OR "supportEndDate" IS NOT NULL 
   OR "originalDocumentName" IS NOT NULL 
   OR "documentType" IS NOT NULL 
   OR "licenseValue" IS NOT NULL 
   OR "licenseValuePerUnit" IS NOT NULL 
   OR "contractualSupportValue" IS NOT NULL 
   OR "supportValuePerYear" IS NOT NULL 
   OR "supportValuePerYearPerUnit" IS NOT NULL 
   OR "oracleCurrency" IS NOT NULL 
   OR "indexField" IS NOT NULL 
   OR "delta" IS NOT NULL;

-- Remove the old Oracle field columns
ALTER TABLE "ContractMetadata" DROP COLUMN "publisher";
ALTER TABLE "ContractMetadata" DROP COLUMN "reseller";
ALTER TABLE "ContractMetadata" DROP COLUMN "entitledEntity";
ALTER TABLE "ContractMetadata" DROP COLUMN "entitledEntityCountry";
ALTER TABLE "ContractMetadata" DROP COLUMN "productName";
ALTER TABLE "ContractMetadata" DROP COLUMN "rawProductName";
ALTER TABLE "ContractMetadata" DROP COLUMN "totalQuantity";
ALTER TABLE "ContractMetadata" DROP COLUMN "metric";
ALTER TABLE "ContractMetadata" DROP COLUMN "metricDefinition";
ALTER TABLE "ContractMetadata" DROP COLUMN "term";
ALTER TABLE "ContractMetadata" DROP COLUMN "level";
ALTER TABLE "ContractMetadata" DROP COLUMN "limitations";
ALTER TABLE "ContractMetadata" DROP COLUMN "includedRights";
ALTER TABLE "ContractMetadata" DROP COLUMN "csi";
ALTER TABLE "ContractMetadata" DROP COLUMN "purchaseDate";
ALTER TABLE "ContractMetadata" DROP COLUMN "governingAgreement";
ALTER TABLE "ContractMetadata" DROP COLUMN "supportContractNumber";
ALTER TABLE "ContractMetadata" DROP COLUMN "supportStartDate";
ALTER TABLE "ContractMetadata" DROP COLUMN "supportEndDate";
ALTER TABLE "ContractMetadata" DROP COLUMN "originalDocumentName";
ALTER TABLE "ContractMetadata" DROP COLUMN "documentType";
ALTER TABLE "ContractMetadata" DROP COLUMN "licenseValue";
ALTER TABLE "ContractMetadata" DROP COLUMN "licenseValuePerUnit";
ALTER TABLE "ContractMetadata" DROP COLUMN "contractualSupportValue";
ALTER TABLE "ContractMetadata" DROP COLUMN "supportValuePerYear";
ALTER TABLE "ContractMetadata" DROP COLUMN "supportValuePerYearPerUnit";
ALTER TABLE "ContractMetadata" DROP COLUMN "oracleCurrency";
ALTER TABLE "ContractMetadata" DROP COLUMN "indexField";
ALTER TABLE "ContractMetadata" DROP COLUMN "delta";
