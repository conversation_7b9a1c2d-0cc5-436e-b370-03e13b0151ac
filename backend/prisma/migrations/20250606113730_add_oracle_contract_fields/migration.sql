-- AlterTable
ALTER TABLE "ContractMetadata" ADD COLUMN     "contractualSupportValue" DECIMAL(65,30),
ADD COLUMN     "csi" TEXT,
ADD COLUMN     "delta" TEXT,
ADD COLUMN     "documentType" TEXT,
ADD COLUMN     "entitledEntity" TEXT,
ADD COLUMN     "entitledEntityCountry" TEXT,
ADD COLUMN     "governingAgreement" TEXT,
ADD COLUMN     "includedRights" TEXT,
ADD COLUMN     "indexField" TEXT,
ADD COLUMN     "level" TEXT,
ADD COLUMN     "licenseValue" DECIMAL(65,30),
ADD COLUMN     "licenseValuePerUnit" DECIMAL(65,30),
ADD COLUMN     "limitations" TEXT,
ADD COLUMN     "metric" TEXT,
ADD COLUMN     "metricDefinition" TEXT,
ADD COLUMN     "oracleCurrency" TEXT,
ADD COLUMN     "originalDocumentName" TEXT,
ADD COLUMN     "productName" TEXT,
ADD COLUMN     "publisher" TEXT,
ADD COLUMN     "purchaseDate" TIMESTAMP(3),
ADD COLUMN     "rawProductName" TEXT,
ADD COLUMN     "reseller" TEXT,
ADD COLUMN     "supportContractNumber" TEXT,
ADD COLUMN     "supportEndDate" TIMESTAMP(3),
ADD COLUMN     "supportStartDate" TIMESTAMP(3),
ADD COLUMN     "supportValuePerYear" DECIMAL(65,30),
ADD COLUMN     "supportValuePerYearPerUnit" DECIMAL(65,30),
ADD COLUMN     "term" TEXT,
ADD COLUMN     "totalQuantity" TEXT;
