-- AlterTable
ALTER TABLE "ContractExtraction" ADD COLUMN     "integrityAnalysis" JSONB;

-- CreateTable
CREATE TABLE "IntegrityConfiguration" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "configurationName" TEXT NOT NULL DEFAULT 'Default',
    "clauses" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "IntegrityConfiguration_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IntegrityConfiguration_userId_idx" ON "IntegrityConfiguration"("userId");

-- CreateIndex
CREATE INDEX "IntegrityConfiguration_tenantId_idx" ON "IntegrityConfiguration"("tenantId");

-- CreateIndex
CREATE INDEX "IntegrityConfiguration_isActive_idx" ON "IntegrityConfiguration"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "IntegrityConfiguration_userId_tenantId_configurationName_key" ON "IntegrityConfiguration"("userId", "tenantId", "configurationName");
