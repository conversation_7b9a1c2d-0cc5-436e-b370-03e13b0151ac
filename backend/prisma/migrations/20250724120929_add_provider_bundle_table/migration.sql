-- CreateTable
CREATE TABLE "ProviderBundle" (
    "id" TEXT NOT NULL,
    "folderId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "analysisData" JSONB NOT NULL,
    "contractIds" TEXT[],
    "extractionDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processingTimeMs" INTEGER,
    "modelUsed" TEXT DEFAULT 'gemini-2.0-flash',
    "overallConfidence" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProviderBundle_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProviderBundle_folderId_key" ON "ProviderBundle"("folderId");

-- CreateIndex
CREATE INDEX "ProviderBundle_folderId_idx" ON "ProviderBundle"("folderId");

-- CreateIndex
CREATE INDEX "ProviderBundle_tenantId_idx" ON "ProviderBundle"("tenantId");

-- CreateIndex
CREATE INDEX "ProviderBundle_extractionDate_idx" ON "ProviderBundle"("extractionDate");

-- AddForeignKey
ALTER TABLE "ProviderBundle" ADD CONSTRAINT "ProviderBundle_folderId_fkey" FOREIGN KEY ("folderId") REFERENCES "Folder"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContractExtraction" ADD CONSTRAINT "ContractExtraction_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "Contract"("id") ON DELETE CASCADE ON UPDATE CASCADE;
