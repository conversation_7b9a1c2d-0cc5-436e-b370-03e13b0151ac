/*
  Warnings:

  - A unique constraint covering the columns `[contractId]` on the table `ContractMetadata` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,type,channel,tenantId]` on the table `NotificationPreference` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "DocumentChunk" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "chunkIndex" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DocumentChunk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Conversation" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContractAssessment" (
    "id" TEXT NOT NULL,
    "contractId" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "annualContractValue" DOUBLE PRECISION,
    "totalContractValue" DOUBLE PRECISION,
    "currency" TEXT,
    "terminationForConvenience" BOOLEAN,
    "terminationNoticeDays" INTEGER,
    "autoRenewal" BOOLEAN,
    "licenseItems" TEXT,
    "geographicLimitations" TEXT,
    "customerDefinition" TEXT,
    "consumptionReporting" BOOLEAN,
    "auditRequirements" TEXT,
    "activeUsersPercentage" DOUBLE PRECISION,
    "featureUtilization" DOUBLE PRECISION,
    "usageFrequency" TEXT,
    "volumeChangeForecast" TEXT,
    "additionalProducts" TEXT,
    "redundantProducts" TEXT,
    "downgradePotential" BOOLEAN,
    "preferredContractLength" TEXT,
    "paymentFlexibility" TEXT,
    "vendorSwitchWillingness" TEXT,
    "satisfactionRating" DOUBLE PRECISION,
    "impactRating" DOUBLE PRECISION,
    "isNicheOffering" BOOLEAN,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ContractAssessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenantSettings" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "branding" JSONB,
    "preferences" JSONB,
    "whiteLabeling" JSONB,
    "customDomain" TEXT,
    "domainVerified" BOOLEAN NOT NULL DEFAULT false,
    "emailConfiguration" JSONB,
    "emailTemplates" JSONB,
    "themeSettings" JSONB,
    "loginPageSettings" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TenantSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "DocumentChunk_documentId_idx" ON "DocumentChunk"("documentId");

-- CreateIndex
CREATE INDEX "DocumentChunk_tenantId_idx" ON "DocumentChunk"("tenantId");

-- CreateIndex
CREATE INDEX "Conversation_tenantId_idx" ON "Conversation"("tenantId");

-- CreateIndex
CREATE INDEX "Conversation_userId_idx" ON "Conversation"("userId");

-- CreateIndex
CREATE INDEX "Message_conversationId_idx" ON "Message"("conversationId");

-- CreateIndex
CREATE UNIQUE INDEX "ContractAssessment_contractId_key" ON "ContractAssessment"("contractId");

-- CreateIndex
CREATE INDEX "ContractAssessment_contractId_idx" ON "ContractAssessment"("contractId");

-- CreateIndex
CREATE INDEX "ContractAssessment_tenantId_idx" ON "ContractAssessment"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "TenantSettings_tenantId_key" ON "TenantSettings"("tenantId");

-- CreateIndex
CREATE INDEX "TenantSettings_tenantId_idx" ON "TenantSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "ContractMetadata_contractId_key" ON "ContractMetadata"("contractId");

-- CreateIndex
CREATE INDEX "ContractMetadata_contractId_idx" ON "ContractMetadata"("contractId");

-- CreateIndex
CREATE INDEX "LicenseOptimizationAnalysis_tenantId_idx" ON "LicenseOptimizationAnalysis"("tenantId");

-- CreateIndex
CREATE INDEX "Notification_userId_idx" ON "Notification"("userId");

-- CreateIndex
CREATE INDEX "Notification_tenantId_idx" ON "Notification"("tenantId");

-- CreateIndex
CREATE INDEX "NotificationDelivery_notificationId_idx" ON "NotificationDelivery"("notificationId");

-- CreateIndex
CREATE INDEX "NotificationPreference_userId_idx" ON "NotificationPreference"("userId");

-- CreateIndex
CREATE INDEX "NotificationPreference_tenantId_idx" ON "NotificationPreference"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "NotificationPreference_userId_type_channel_tenantId_key" ON "NotificationPreference"("userId", "type", "channel", "tenantId");

-- CreateIndex
CREATE INDEX "OptimizationRecommendation_analysisId_idx" ON "OptimizationRecommendation"("analysisId");

-- CreateIndex
CREATE INDEX "OptimizationRecommendation_licenseId_idx" ON "OptimizationRecommendation"("licenseId");

-- CreateIndex
CREATE INDEX "OptimizationRecommendation_tenantId_idx" ON "OptimizationRecommendation"("tenantId");

-- CreateIndex
CREATE INDEX "RiskAssessment_contractId_idx" ON "RiskAssessment"("contractId");

-- CreateIndex
CREATE INDEX "RiskAssessment_tenantId_idx" ON "RiskAssessment"("tenantId");

-- CreateIndex
CREATE INDEX "RiskFactor_assessmentId_idx" ON "RiskFactor"("assessmentId");

-- CreateIndex
CREATE INDEX "RiskFactor_contractId_idx" ON "RiskFactor"("contractId");

-- CreateIndex
CREATE INDEX "RiskFactor_tenantId_idx" ON "RiskFactor"("tenantId");

-- CreateIndex
CREATE INDEX "Role_tenantId_idx" ON "Role"("tenantId");

-- CreateIndex
CREATE INDEX "TenantUser_tenantId_idx" ON "TenantUser"("tenantId");

-- CreateIndex
CREATE INDEX "TenantUser_userId_idx" ON "TenantUser"("userId");

-- AddForeignKey
ALTER TABLE "Role" ADD CONSTRAINT "Role_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUser" ADD CONSTRAINT "TenantUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUser" ADD CONSTRAINT "TenantUser_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TenantUser" ADD CONSTRAINT "TenantUser_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OptimizationRecommendation" ADD CONSTRAINT "OptimizationRecommendation_analysisId_fkey" FOREIGN KEY ("analysisId") REFERENCES "LicenseOptimizationAnalysis"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RiskFactor" ADD CONSTRAINT "RiskFactor_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "RiskAssessment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationDelivery" ADD CONSTRAINT "NotificationDelivery_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "Notification"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationPreference" ADD CONSTRAINT "NotificationPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
