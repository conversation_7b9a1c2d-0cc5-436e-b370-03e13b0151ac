/**
 * Database Seed Script
 * Creates initial super admin user and platform configuration
 */

import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import { logger } from '../src/infrastructure/logging/logger';

const prisma = new PrismaClient();

/**
 * Hash password with salt
 */
async function hashPassword(password: string, salt: string): Promise<string> {
  return new Promise((resolve, reject) => {
    crypto.pbkdf2(password, salt, 10000, 64, 'sha512', (err, derivedKey) => {
      if (err) reject(err);
      resolve(derivedKey.toString('hex'));
    });
  });
}

/**
 * Create super admin user
 */
async function createSuperAdmin() {
  const email = process.env.SUPER_ADMIN_EMAIL || '<EMAIL>';
  const password = process.env.SUPER_ADMIN_PASSWORD || 'SuperSecurePassword123!';
  const name = process.env.SUPER_ADMIN_NAME || 'Platform Administrator';

  try {
    // Check if super admin already exists
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (existingSuperAdmin) {
      logger.info('✅ Super admin already exists', { email: existingSuperAdmin.email });
      return existingSuperAdmin;
    }

    // Check if email is already taken
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      logger.error('❌ Email already exists for super admin', { email });
      throw new Error(`Email ${email} is already taken`);
    }

    // Generate password hash and salt
    const salt = crypto.randomBytes(16).toString('hex');
    const passwordHash = await hashPassword(password, salt);

    // Create super admin user (no tenant association)
    const superAdmin = await prisma.user.create({
      data: {
        email,
        name,
        passwordHash,
        passwordSalt: salt,
        status: 'ACTIVE',
        role: 'SUPER_ADMIN', // String value, not enum
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    logger.info('✅ Super admin created successfully', { 
      id: superAdmin.id,
      email: superAdmin.email,
      name: superAdmin.name 
    });

    return superAdmin;

  } catch (error) {
    logger.error('❌ Failed to create super admin', { error });
    throw error;
  }
}

/**
 * Create platform configuration
 */
async function createPlatformConfig() {
  try {
    // Add any platform-level configuration here
    // For example: default settings, feature flags, etc.
    
    logger.info('✅ Platform configuration initialized');
  } catch (error) {
    logger.error('❌ Failed to create platform configuration', { error });
    throw error;
  }
}

/**
 * Main seed function
 */
async function main() {
  try {
    logger.info('🌱 Starting database seeding...');

    // Create super admin
    await createSuperAdmin();

    // Create platform configuration
    await createPlatformConfig();

    logger.info('🎉 Database seeding completed successfully!');

  } catch (error) {
    logger.error('💥 Database seeding failed', { error });
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Run seed script
 */
if (require.main === module) {
  main()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seed script failed:', error);
      process.exit(1);
    });
}

export { main as seed, createSuperAdmin };
