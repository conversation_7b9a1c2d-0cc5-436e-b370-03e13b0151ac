generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                   @id @default(uuid())
  email                   String                   @unique
  name                    String
  passwordHash            String
  passwordSalt            String
  status                  String
  role                    String
  persona                 String?
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  createdBy               String?
  notifications           Notification[]
  notificationPreferences NotificationPreference[]
  tenantUsers             TenantUser[]
  dashboardLayouts        DashboardLayout[]
}

model UserSession {
  id        String    @id @default(uuid())
  userId    String
  tenantId  String
  ipAddress String
  userAgent String
  isRevoked Boolean   @default(false)
  revokedAt DateTime?
  expiresAt DateTime
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Tenant {
  id          String       @id @default(uuid())
  name        String
  tier        String
  status      String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  roles       Role[]
  tenantUsers TenantUser[]
}

model Role {
  id          String       @id @default(uuid())
  name        String
  description String?
  permissions Json
  isDefault   Boolean      @default(false)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  tenantId    String
  tenant      Tenant       @relation(fields: [tenantId], references: [id])
  tenantUsers TenantUser[]

  @@index([tenantId])
}

model TenantUser {
  id         String   @id @default(uuid())
  tenantRole String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  createdBy  String?
  isActive   Boolean  @default(true)
  tenantId   String
  userId     String
  roleId     String?
  role       Role?    @relation(fields: [roleId], references: [id])
  tenant     Tenant   @relation(fields: [tenantId], references: [id])
  user       User     @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([userId])
}

model Contract {
  id                     String                  @id @default(uuid())
  title                  String
  description            String?
  contractNumber         String?
  status                 ContractStatus
  startDate              DateTime?
  endDate                DateTime?
  renewalType            String?
  renewalDate            DateTime?
  isAutoRenew            Boolean                 @default(false)
  noticePeriodDays       Int?
  version                Int                     @default(1)
  securityClassification SecurityClassification  @default(CONFIDENTIAL)
  isEncrypted            Boolean                 @default(true)
  currentVersionId       String?
  counterparty           String?
  value                  String?
  assessmentCompleted    Boolean                 @default(false)
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  tenantId               String
  createdById            String?
  agreementType          AgreementType
  classification         ContractClassification?
  folderId               String?
  provider               String?
  folder                 Folder?                 @relation(fields: [folderId], references: [id])
  contractExtraction     ContractExtraction?

  @@index([folderId])
}

model ContractVersion {
  id              String         @id @default(uuid())
  contractId      String
  versionNumber   Int
  documentUri     String
  documentHash    String?
  documentFormat  DocumentFormat
  documentSize    Int
  documentName    String
  encryptionKeyId String?
  versionComment  String?
  changelog       Json?
  metadata        Json?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  createdBy       String?
  documentContent Bytes?
  mimeType        String?
  ocrText         String?        // Extracted OCR text
  ocrProcessedAt  DateTime?      // When OCR was completed
  ocrConfidence   Float?         // OCR confidence score
  ocrStatus       String?        // OCR processing status (SUCCESS, FAILED)
  ocrMetadata     Json?          // Additional OCR metadata (page count, etc.)
}

model ContractMetadata {
  id                  String    @id @default(uuid())
  contractId          String    @unique
  totalValue          Decimal?
  currency            String?
  paymentTerms        String?
  autoExtractedFields Json?
  customMetadata      Json?
  encryptedFields     String?
  lastExtractedAt     DateTime?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  oracleFields        Json?

  @@index([contractId])
}

model ContractTemplate {
  id            String        @id @default(uuid())
  name          String
  description   String?
  industryType  String?
  isEncrypted   Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  tenantId      String
  agreementType AgreementType @default(OTHER)
}

model ClauseTemplate {
  id                 String   @id @default(uuid())
  name               String
  text               String
  category           String?
  riskLevel          String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  contractTemplateId String
}

model Party {
  id          String   @id @default(uuid())
  name        String
  type        String
  contactInfo Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  tenantId    String
}

model ContractParty {
  id         String   @id @default(uuid())
  role       String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  contractId String
  partyId    String
}

model Obligation {
  id          String    @id @default(uuid())
  description String
  dueDate     DateTime?
  status      String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  contractId  String
}

model ApprovalWorkflow {
  id          String   @id @default(uuid())
  steps       Json
  currentStep Int      @default(0)
  status      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  contractId  String
}

model Comment {
  id         String   @id @default(uuid())
  text       String
  timestamp  DateTime @default(now())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  contractId String
  authorId   String
}

model AIJob {
  id        String   @id @default(uuid())
  sourceId  String
  type      String
  status    String
  priority  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AIResult {
  id         String   @id @default(uuid())
  resultData Json
  confidence Float?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  jobId      String
}

model LicenseOptimizationAnalysis {
  id                    String                       @id @default(uuid())
  tenantId              String
  totalCurrentCost      Float
  totalProjectedCost    Float
  totalPotentialSavings Float
  currency              String
  summary               String
  createdAt             DateTime                     @default(now())
  updatedAt             DateTime                     @updatedAt
  recommendations       OptimizationRecommendation[]

  @@index([tenantId])
}

model OptimizationRecommendation {
  id                  String                      @id @default(uuid())
  analysisId          String
  licenseId           String
  tenantId            String
  type                String
  priority            String
  description         String
  currentCost         Float
  projectedCost       Float
  potentialSavings    Float
  currency            String
  implementationSteps String[]
  createdAt           DateTime                    @default(now())
  updatedAt           DateTime                    @updatedAt
  analysis            LicenseOptimizationAnalysis @relation(fields: [analysisId], references: [id])

  @@index([analysisId])
  @@index([licenseId])
  @@index([tenantId])
}

model License {
  id                  String        @id @default(uuid())
  name                String
  description         String?
  licenseNumber       String?
  licenseType         LicenseType
  status              LicenseStatus
  vendor              String
  purchaseDate        DateTime?
  startDate           DateTime?
  endDate             DateTime?
  renewalType         String?
  renewalDate         DateTime?
  isAutoRenew         Boolean       @default(false)
  noticePeriodDays    Int?
  totalValue          Decimal?
  currency            String?
  costPeriod          String?
  totalLicenses       Int
  assignedLicenses    Int           @default(0)
  availableLicenses   Int           @default(0)
  complianceStatus    String        @default("Compliant")
  lastComplianceCheck DateTime?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  tenantId            String
  createdById         String?
}

model LicenseEntitlement {
  id          String   @id @default(uuid())
  name        String
  description String?
  included    Boolean  @default(true)
  quantity    Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  licenseId   String
}

model LicenseDocument {
  id              String              @id @default(uuid())
  name            String
  documentType    LicenseDocumentType
  documentFormat  DocumentFormat
  documentUri     String
  documentHash    String?
  isEncrypted     Boolean             @default(false)
  encryptionKeyId String?
  uploadDate      DateTime            @default(now())
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  licenseId       String
}

model LicenseUsage {
  id                    String   @id @default(uuid())
  date                  DateTime
  usageCount            Int
  totalAvailable        Int
  utilizationPercentage Float
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  licenseId             String
}

model AuditLog {
  id        String   @id @default(uuid())
  timestamp DateTime @default(now())
  action    String
  resource  String
  details   Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  actorId   String?
}

model RiskAssessment {
  id               String       @id @default(uuid())
  contractId       String
  tenantId         String
  overallRiskLevel String
  summary          String
  score            Float?
  factors          Json?
  timestamp        DateTime     @default(now())
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  riskFactors      RiskFactor[]

  @@index([contractId])
  @@index([tenantId])
}

model RiskFactor {
  id             String         @id @default(uuid())
  assessmentId   String
  contractId     String
  tenantId       String
  category       String
  level          String
  description    String
  clause         String
  clauseLocation String
  mitigation     String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  assessment     RiskAssessment @relation(fields: [assessmentId], references: [id])

  @@index([assessmentId])
  @@index([contractId])
  @@index([tenantId])
}

model Notification {
  id           String                 @id @default(uuid())
  title        String
  content      String
  type         NotificationType
  priority     NotificationPriority   @default(MEDIUM)
  isRead       Boolean                @default(false)
  readAt       DateTime?
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  userId       String
  tenantId     String
  metadata     Json?
  resourceId   String?
  resourceType String?
  actionUrl    String?                // URL for clickable navigation
  user         User                   @relation(fields: [userId], references: [id])
  deliveries   NotificationDelivery[]

  @@index([userId])
  @@index([tenantId])
}

model NotificationDelivery {
  id             String              @id @default(uuid())
  channel        NotificationChannel
  status         String
  sentAt         DateTime?
  deliveredAt    DateTime?
  errorMessage   String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  notificationId String
  notification   Notification        @relation(fields: [notificationId], references: [id])

  @@index([notificationId])
}

model NotificationPreference {
  id        String              @id @default(uuid())
  enabled   Boolean             @default(true)
  channel   NotificationChannel
  type      NotificationType
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  userId    String
  tenantId  String
  user      User                @relation(fields: [userId], references: [id])

  @@unique([userId, type, channel, tenantId])
  @@index([userId])
  @@index([tenantId])
}

model DocumentChunk {
  id         String   @id @default(uuid())
  documentId String
  tenantId   String
  chunkIndex Int
  content    String
  metadata   Json?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([documentId])
  @@index([tenantId])
}

model Conversation {
  id        String    @id @default(uuid())
  title     String
  tenantId  String
  userId    String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  messages  Message[]

  @@index([tenantId])
  @@index([userId])
}

model Message {
  id             String       @id @default(uuid())
  conversationId String
  role           String
  content        String
  metadata       Json?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
}

model Folder {
  id          String     @id @default(uuid())
  name        String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  tenantId    String
  createdById String?
  contracts   Contract[]

  @@index([tenantId])
  @@index([createdById])
}

model ProviderBundle {
  id                String   @id @default(uuid())
  folderId          String   @unique
  tenantId          String
  analysisData      Json     // Stores the bundle interconnection analysis results
  contractIds       String[] // Array of contract IDs included in this analysis
  extractionDate    DateTime @default(now())
  processingTimeMs  Int?
  modelUsed         String?  @default("gemini-2.0-flash")
  overallConfidence Float?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([folderId])
  @@index([tenantId])
  @@index([extractionDate])
}

model ContractExtraction {
  id                String   @id @default(uuid())
  contractId        String   @unique
  tenantId          String
  fixedFields       Json
  dynamicFields     Json
  specialFields     Json
  extractionDate    DateTime @default(now())
  extractionVersion String   @default("1.0")
  overallConfidence Float?
  processingTimeMs  Int?
  modelUsed         String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  folderId          String?
  analysisFields    Json?
  documentSummary   Json?
  complianceAnalysis Json?
  integrityAnalysis Json?
  reportingTo       String?  // Contract ID this contract reports to
  ocrUsedForExtraction Boolean? @default(false) // Whether OCR was used during initial extraction
  contract          Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)

  @@index([contractId])
  @@index([tenantId])
  @@index([extractionDate])
  @@index([folderId])
  @@index([reportingTo])
}

model ContractAssessment {
  id                        String    @id @default(uuid())
  contractId                String    @unique
  tenantId                  String
  annualContractValue       Float?
  totalContractValue        Float?
  currency                  String?
  terminationForConvenience Boolean?
  terminationNoticeDays     Int?
  autoRenewal               Boolean?
  licenseItems              String?
  geographicLimitations     String?
  customerDefinition        String?
  consumptionReporting      Boolean?
  auditRequirements         String?
  activeUsersPercentage     Float?
  featureUtilization        Float?
  usageFrequency            String?
  volumeChangeForecast      String?
  additionalProducts        String?
  redundantProducts         String?
  downgradePotential        Boolean?
  preferredContractLength   String?
  paymentFlexibility        String?
  vendorSwitchWillingness   String?
  satisfactionRating        Float?
  impactRating              Float?
  isNicheOffering           Boolean?
  completedAt               DateTime?
  createdAt                 DateTime  @default(now())
  updatedAt                 DateTime  @updatedAt

  @@index([contractId])
  @@index([tenantId])
}

model TenantSettings {
  id                 String   @id @default(uuid())
  tenantId           String   @unique
  branding           Json?
  preferences        Json?
  whiteLabeling      Json?
  customDomain       String?
  domainVerified     Boolean  @default(false)
  emailConfiguration Json?
  emailTemplates     Json?
  themeSettings      Json?
  loginPageSettings  Json?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@index([tenantId])
}

model DashboardLayout {
  id            String   @id @default(uuid())
  userId        String
  name          String
  description   String?
  category      String   // 'priority' or 'portfolio'
  widgetType    String   // specific widget identifier
  isVisible     Boolean  @default(true)
  position      Json     // {x, y, w, h}
  configuration Json?    // widget-specific settings
  order         Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, category])
  @@index([userId, widgetType])
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  USER
  VIEWER
  LEGAL_ADMIN
  FINANCE_ADMIN
  PROCUREMENT_ADMIN
  IT_ADMIN
}

enum ContractClassification {
  SW_SAAS
  IAAS
  PAAS
  PROFESSIONAL_SERVICES
  MANAGED_SERVICES
  HARDWARE
  RESELLER
  NETWORK
  OTHER
}

enum AgreementType {
  MSA
  NDA
  SOW
  PO
  SLA
  DPA
  BAA
  EULA
  LOI
  MOA
  MOU
  JV
  CA
  LPA
  SSA
  ESA
  PSA
  TOS
  DUA
  OEM
  RFP
  RFQ
  BPA
  PPA
  LSA
  ISA
  SPA
  APA
  TPA
  IP
  RSA
  VARA
  DDA
  TSA
  IA
  INVOICE
  SCHEDULE
  OTHER
  ORDER
}

enum ContractStatus {
  DRAFT
  REVIEW
  PENDING_APPROVAL
  APPROVED
  ACTIVE
  EXPIRING
  EXPIRED
  TERMINATED
  RENEWED
}

enum DocumentFormat {
  PDF
  DOCX
  DOC
  RTF
  TXT
  XLSX
  OTHER
}

enum SecurityClassification {
  PUBLIC
  INTERNAL
  CONFIDENTIAL
  RESTRICTED
}

enum LicenseType {
  SOFTWARE
  SAAS
  HARDWARE
  SERVICE
  OTHER
}

enum LicenseStatus {
  ACTIVE
  EXPIRED
  EXPIRING_SOON
  INACTIVE
  COMPLIANCE_ISSUE
}

enum LicenseDocumentType {
  CONTRACT
  INVOICE
  AGREEMENT
  TERMS
  CERTIFICATE
  OTHER
}

enum NotificationType {
  CONTRACT
  LICENSE
  USER
  SYSTEM
  SECURITY
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum NotificationChannel {
  EMAIL
  IN_APP
  MOBILE_PUSH
  SLACK
  TEAMS
}

model IntegrityConfiguration {
  id                String   @id @default(uuid())
  userId            String
  tenantId          String
  configurationName String   @default("Default")
  clauses           Json     // Dynamic clauses with values and weightages
  isActive          Boolean  @default(true)
  isDefault         Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([userId, tenantId, configurationName])
  @@index([userId])
  @@index([tenantId])
  @@index([isActive])
}
