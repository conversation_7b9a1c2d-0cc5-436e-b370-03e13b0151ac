# PGVector Extension Setup Guide

This document provides instructions for setting up and troubleshooting the pgvector extension in PostgreSQL for the MAIT application.

## What is pgvector?

pgvector is a PostgreSQL extension that adds vector similarity search capabilities to the database. It's used in our application for storing and querying vector embeddings for AI features.

## Automatic Setup

The pgvector extension should be automatically installed when you start the application with Docker Compose. The following mechanisms are in place to ensure this:

1. The Docker Compose file uses the `ankane/pgvector:latest` image which comes with pgvector pre-installed
2. A SQL script is mounted to the PostgreSQL container to create the extension during initialization
3. A Node.js script runs during backend startup to create the extension if it doesn't exist

## Manual Setup

If you're experiencing issues with the pgvector extension, you can manually set it up:

### Using Docker

```bash
# Connect to the PostgreSQL container
docker exec -it multistrat-postgres bash

# Connect to the database
psql -U postgres -d clm_dev

# Create the extension
CREATE EXTENSION IF NOT EXISTS vector;

# Verify the extension is installed
\dx vector
```

### Using Local PostgreSQL

If you're running PostgreSQL locally:

```bash
# Connect to the database
psql -d clm_dev

# Create the extension
CREATE EXTENSION IF NOT EXISTS vector;

# Verify the extension is installed
\dx vector
```

## Troubleshooting

If you're experiencing issues with the pgvector extension, try the following:

### Check if the extension is installed

Run the following command in the backend directory:

```bash
npm run db:check-pgvector
```

This will check if the pgvector extension is installed and attempt to install it if it's not.

### Common Issues and Solutions

1. **Error: could not open extension control file**

   - Make sure you're using the `ankane/pgvector` Docker image
   - Try installing pgvector manually in the PostgreSQL container

2. **Error: type "vector" does not exist**

   - The pgvector extension is not properly installed
   - Run `CREATE EXTENSION IF NOT EXISTS vector;` in the PostgreSQL database

3. **Error during migration**

   - Make sure the pgvector extension is created before running migrations
   - Try running the extension creation script manually: `npm run db:create-pgvector`

4. **Permission issues**
   - Make sure the PostgreSQL user has superuser privileges
   - Try connecting as the postgres user

## Additional Resources

- [pgvector GitHub Repository](https://github.com/pgvector/pgvector)
- [pgvector Documentation](https://github.com/pgvector/pgvector#readme)
- [ankane/pgvector Docker Image](https://hub.docker.com/r/ankane/pgvector)
