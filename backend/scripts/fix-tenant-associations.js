#!/usr/bin/env node

/**
 * Fix Tenant Associations Script
 * 
 * This script fixes users who are missing tenant associations after database imports.
 * Run this after restoring a production database dump to ensure all users can log in.
 * 
 * Usage:
 *   node scripts/fix-tenant-associations.js
 *   npm run fix-tenant-associations
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixTenantAssociations() {
  console.log('🔧 Starting Tenant Association Fix Script...\n');
  
  try {
    // Step 1: Check current state
    console.log('📊 Checking current database state...');
    
    const totalUsers = await prisma.user.count();
    const totalTenants = await prisma.tenant.count();
    const totalAssociations = await prisma.tenantUser.count();
    
    console.log(`  Users: ${totalUsers}`);
    console.log(`  Tenants: ${totalTenants}`);
    console.log(`  Current associations: ${totalAssociations}\n`);
    
    // Step 2: Find users without tenant associations
    console.log('🔍 Finding users without tenant associations...');
    
    const usersWithoutTenants = await prisma.user.findMany({
      where: {
        tenantUsers: {
          none: {}
        },
        role: {
          not: 'SUPER_ADMIN' // Skip super admin users
        }
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true
      }
    });
    
    console.log(`Found ${usersWithoutTenants.length} users without tenant associations:`);
    
    if (usersWithoutTenants.length === 0) {
      console.log('✅ All users already have tenant associations! No fixes needed.\n');
      return;
    }
    
    // List the problematic users
    usersWithoutTenants.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.name}) - ${user.status}/${user.role}`);
    });
    console.log('');
    
    // Step 3: Get available tenants
    console.log('🏢 Finding available tenants...');
    
    const availableTenants = await prisma.tenant.findMany({
      where: {
        status: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        tier: true,
        status: true
      }
    });
    
    if (availableTenants.length === 0) {
      console.error('❌ No active tenants found! Cannot proceed.');
      console.error('   Please ensure at least one tenant exists with status "ACTIVE".\n');
      return;
    }
    
    console.log(`Found ${availableTenants.length} active tenant(s):`);
    availableTenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ${tenant.name} (${tenant.tier})`);
    });
    
    // Use the first tenant for all associations
    const targetTenant = availableTenants[0];
    console.log(`\n🎯 Will associate all users with: ${targetTenant.name}\n`);
    
    // Step 4: Fix each user
    console.log('🔨 Creating tenant associations...');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const user of usersWithoutTenants) {
      try {
        console.log(`  Fixing ${user.email}...`);
        
        // Create the tenant-user association
        await prisma.tenantUser.create({
          data: {
            userId: user.id,
            tenantId: targetTenant.id,
            tenantRole: 'ADMIN', // Make them admin since they're from production
            isActive: true
          }
        });
        
        console.log(`    ✅ Success`);
        successCount++;
        
      } catch (error) {
        console.error(`    ❌ Failed: ${error.message}`);
        errorCount++;
      }
    }
    
    // Step 5: Summary
    console.log('\n📋 SUMMARY:');
    console.log(`  ✅ Successfully fixed: ${successCount} users`);
    console.log(`  ❌ Failed to fix: ${errorCount} users`);
    
    if (successCount > 0) {
      console.log('\n🎉 Tenant associations have been fixed!');
      console.log('   Users should now be able to log in successfully.');
    }
    
    // Step 6: Verification
    console.log('\n🔍 Verification:');
    const remainingIssues = await prisma.user.count({
      where: {
        tenantUsers: { none: {} },
        role: { not: 'SUPER_ADMIN' }
      }
    });
    
    if (remainingIssues === 0) {
      console.log('  ✅ All users now have tenant associations!');
    } else {
      console.log(`  ⚠️  ${remainingIssues} users still need fixing.`);
    }
    
    console.log('\n✨ Script completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Script failed with error:');
    console.error(`   ${error.message}`);
    
    if (error.stack) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  fixTenantAssociations()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}

module.exports = { fixTenantAssociations };
