// Script to check if pgvector extension is installed in PostgreSQL
const { Client } = require('pg');
require('dotenv').config({ path: process.env.NODE_ENV === 'production' ? '.env' : '.env.docker' });

async function checkPgVectorExtension() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    
    console.log('Checking for pgvector extension...');
    const result = await client.query(`
      SELECT * FROM pg_extension WHERE extname = 'vector';
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ pgvector extension is installed!');
      console.log('Extension details:', result.rows[0]);
    } else {
      console.log('❌ pgvector extension is NOT installed!');
      console.log('Attempting to install pgvector extension...');
      
      try {
        await client.query('CREATE EXTENSION IF NOT EXISTS vector;');
        console.log('✅ pgvector extension installed successfully!');
      } catch (installError) {
        console.error('Error installing pgvector extension:', installError.message);
        console.log('\nTroubleshooting steps:');
        console.log('1. Make sure you are using the ankane/pgvector Docker image');
        console.log('2. Check if you have superuser privileges in PostgreSQL');
        console.log('3. Try running the command manually in psql: CREATE EXTENSION IF NOT EXISTS vector;');
      }
    }
    
    // Check if vector type is available
    try {
      console.log('\nTesting vector type...');
      await client.query('CREATE TEMP TABLE vector_test (id serial, embedding vector(3));');
      await client.query('INSERT INTO vector_test (embedding) VALUES (\'[1,2,3]\');');
      const vectorResult = await client.query('SELECT * FROM vector_test;');
      console.log('✅ Vector type is working correctly!');
      console.log('Test result:', vectorResult.rows[0]);
    } catch (vectorError) {
      console.error('❌ Error testing vector type:', vectorError.message);
    }
    
  } catch (error) {
    console.error('Error checking pgvector extension:', error);
  } finally {
    await client.end();
  }
}

checkPgVectorExtension();
