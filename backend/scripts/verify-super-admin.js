/**
 * Verify Super Admin Creation
 * Simple script to check if super admin user was created successfully
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifySuperAdmin() {
  try {
    console.log('🔍 Checking for super admin user...');

    // Find super admin user
    const superAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        createdAt: true,
      }
    });

    if (superAdmin) {
      console.log('✅ Super admin found:');
      console.log('   ID:', superAdmin.id);
      console.log('   Email:', superAdmin.email);
      console.log('   Name:', superAdmin.name);
      console.log('   Role:', superAdmin.role);
      console.log('   Status:', superAdmin.status);
      console.log('   Created:', superAdmin.createdAt);

      // Check if super admin has any tenant associations
      const tenantUsers = await prisma.tenantUser.findMany({
        where: { userId: superAdmin.id },
        include: {
          tenant: {
            select: { name: true, tier: true }
          }
        }
      });

      if (tenantUsers.length > 0) {
        console.log('⚠️  Super admin has tenant associations:');
        tenantUsers.forEach(tu => {
          console.log(`   - Tenant: ${tu.tenant.name} (${tu.tenant.tier})`);
        });
      } else {
        console.log('✅ Super admin has no tenant associations (correct)');
      }

    } else {
      console.log('❌ No super admin user found');
    }

    // Count total users by role
    const userCounts = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        role: true
      }
    });

    console.log('\n📊 User counts by role:');
    userCounts.forEach(count => {
      console.log(`   ${count.role}: ${count._count.role}`);
    });

  } catch (error) {
    console.error('❌ Error verifying super admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifySuperAdmin();
