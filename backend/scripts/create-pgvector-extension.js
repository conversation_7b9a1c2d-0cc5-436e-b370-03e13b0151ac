// Script to create pgvector extension in PostgreSQL
const { Client } = require('pg');
require('dotenv').config({ path: process.env.NODE_ENV === 'production' ? '.env' : '.env.docker' });

async function createPgVectorExtension() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    
    console.log('Creating pgvector extension...');
    await client.query('CREATE EXTENSION IF NOT EXISTS vector;');
    
    console.log('pgvector extension created successfully!');
  } catch (error) {
    console.error('Error creating pgvector extension:', error);
  } finally {
    await client.end();
  }
}

createPgVectorExtension();
