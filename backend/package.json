{"name": "backend", "version": "0.1.0", "description": "Express.js backend for the B2B2B Contract Management Platform", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/src/server.js", "test": "jest", "lint": "eslint . --ext .ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:create-pgvector": "node scripts/create-pgvector-extension.js", "db:check-pgvector": "node scripts/check-pgvector.js", "db:fix-tenant-associations": "node scripts/fix-tenant-associations.js", "seed": "ts-node prisma/seed.ts", "db:seed": "npm run seed"}, "keywords": ["express", "backend", "api", "contract-management"], "author": "", "license": "MIT", "dependencies": {"@ai-sdk/anthropic": "^1.2.11", "@auth/core": "^0.39.0", "@google/genai": "^1.8.0", "@langchain/community": "^0.3.41", "@langchain/core": "^0.3.49", "@langchain/openai": "^0.5.7", "@neondatabase/serverless": "^1.0.0", "@prisma/client": "^6.6.0", "ai": "^4.3.15", "axios": "^1.10.0", "bullmq": "^5.51.1", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "glob": "^11.0.2", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.24", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "openai": "^4.96.0", "pg": "^8.15.6", "pgvector": "^0.2.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.3", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "chalk": "^5.4.1", "eslint": "^9.25.1", "jest": "^29.7.0", "prisma": "^6.6.0", "rimraf": "^6.0.1", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}