/**
 * Quick test for supplier mapping fallback
 */

const { SupplierMappingService } = require('../dist/src/api/services/SupplierMappingService');

async function testSupplierMapping() {
  const service = new SupplierMappingService();
  
  console.log('Testing supplier mapping for "nalta":');
  
  // Test normalization
  const normalized = service.normalizeSupplierName('nalta');
  console.log('Normalized name:', normalized);
  
  // Test if supported
  const isSupported = await service.isSupplierSupported('nalta');
  console.log('Is supported:', isSupported);
  
  // Test getting fields
  const fields = await service.getSupplierFields('nalta');
  console.log('Fields count:', fields.length);
  console.log('First few fields:', fields.slice(0, 5));
  
  // Test categorical mapping
  const categoricalMapping = await service.getCategoricalMapping('nalta');
  console.log('Categories:', Object.keys(categoricalMapping));
  
  // Test prompt generation
  const prompt = await service.generateCategoricalPrompt('nalta');
  console.log('Prompt generated:', prompt.length > 0);
}

testSupplierMapping().catch(console.error);
