/**
 * Test for Sequential Contract AI Extraction
 * Tests the new three-step sequential extraction process
 */

const { ContractAIService } = require('../../dist/src/api/services/ContractAIService');
const { PrismaClient } = require('@prisma/client');

// Mock the dependencies
jest.mock('@google/genai');
jest.mock('../../dist/src/infrastructure/logging/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  }
}));

describe('ContractAIService Sequential Extraction', () => {
  let contractAIService;
  let mockPrisma;

  beforeEach(() => {
    // Mock Prisma client
    mockPrisma = {
      // Add any necessary mock methods
    };

    // Mock environment variable
    process.env.GEMINI_API_KEY = 'test-api-key';

    // Create service instance
    contractAIService = new ContractAIService(mockPrisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('extractFixedFieldsOnly', () => {
    it('should extract only fixed fields from a contract document', async () => {
      // Mock the Gemini API response
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                fixed_fields: {
                  agreement_type: { value: "MSA", confidence: 0.95 },
                  provider: { value: "Oracle Corporation", confidence: 0.90 },
                  client: { value: "Test Client", confidence: 0.85 },
                  product: { value: "Cloud Services", confidence: 0.88 },
                  total_amount: { value: "USD:1000000.00", confidence: 0.90 },
                  start_date: { value: "2024-01-01", confidence: 0.95 },
                  end_date: { value: "2026-12-31", confidence: 0.90 },
                  contract_id: { value: "MSA-2024-001", confidence: 0.85 },
                  contract_classification: { value: "SW_SAAS", confidence: 0.88 },
                  contract_status: { value: "Active", confidence: 0.90 },
                  contract_term: { value: "36 months", confidence: 0.85 },
                  auto_renewal: { value: "No", confidence: 0.90 },
                  renewal_notice_period: { value: "90 days", confidence: 0.85 },
                  relationships: { value: "N/A", confidence: 0.80 },
                  original_filename: { value: "test.pdf", confidence: 1.0 }
                }
              })
            }]
          }
        }]
      };

      // Mock the GoogleGenAI generateContent method
      contractAIService.googleAI = {
        models: {
          generateContent: jest.fn().mockResolvedValue(mockResponse)
        }
      };

      const documentBuffer = Buffer.from('test document content');
      const fileName = 'test.pdf';

      const result = await contractAIService.extractFixedFieldsOnly(documentBuffer, fileName);

      expect(result).toBeDefined();
      expect(result.provider.value).toBe('Oracle Corporation');
      expect(result.agreement_type.value).toBe('MSA');
      expect(result.original_filename.value).toBe(fileName);
      expect(contractAIService.googleAI.models.generateContent).toHaveBeenCalledTimes(1);
    });

    it('should handle API errors gracefully', async () => {
      // Mock API error
      contractAIService.googleAI = {
        models: {
          generateContent: jest.fn().mockRejectedValue(new Error('API Error'))
        }
      };

      const documentBuffer = Buffer.from('test document content');
      const fileName = 'test.pdf';

      await expect(contractAIService.extractFixedFieldsOnly(documentBuffer, fileName))
        .rejects.toThrow('API Error');
    });
  });

  describe('extractDynamicFieldsWithExclusion', () => {
    it('should extract dynamic fields while excluding supplier-specific fields', async () => {
      // Mock the Gemini API response
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                dynamic_fields: {
                  "General": {
                    "contract_description": {
                      value: "Test contract description",
                      description: "Detailed contract description",
                      confidence: 0.88
                    }
                  },
                  "Commercial terms": {
                    "payment_terms": {
                      value: "Net 30 days",
                      description: "Payment terms",
                      confidence: 0.90
                    }
                  }
                }
              })
            }]
          }
        }]
      };

      contractAIService.googleAI = {
        models: {
          generateContent: jest.fn().mockResolvedValue(mockResponse)
        }
      };

      const documentBuffer = Buffer.from('test document content');
      const fileName = 'test.pdf';
      const supplierName = 'Oracle Corporation';
      const supplierSpecificFields = {
        "oracle": {
          "Commercial terms": {
            "license_value": { value: "USD:500000", confidence: 0.9 }
          }
        }
      };

      const result = await contractAIService.extractDynamicFieldsWithExclusion(
        documentBuffer,
        fileName,
        supplierName,
        supplierSpecificFields
      );

      expect(result).toBeDefined();
      expect(result.General).toBeDefined();
      expect(result.General.contract_description.value).toBe('Test contract description');
      expect(result.Financial).toBeDefined();
      expect(result.Financial.payment_terms.value).toBe('Net 30 days');
      expect(contractAIService.googleAI.models.generateContent).toHaveBeenCalledTimes(1);
    });
  });

  describe('parseGeminiResponse', () => {
    it('should parse valid JSON response directly', () => {
      const mockResponse = {
        text: JSON.stringify({ test: 'data' })
      };

      const result = contractAIService.parseGeminiResponse(mockResponse);
      expect(result).toEqual({ test: 'data' });
    });

    it('should parse JSON from candidates structure', () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({ test: 'data' })
            }]
          }
        }]
      };

      const result = contractAIService.parseGeminiResponse(mockResponse);
      expect(result).toEqual({ test: 'data' });
    });

    it('should extract JSON from markdown code blocks', () => {
      const mockResponse = {
        text: '```json\n{"test": "data"}\n```'
      };

      const result = contractAIService.parseGeminiResponse(mockResponse);
      expect(result).toEqual({ test: 'data' });
    });

    it('should throw error for invalid response structure', () => {
      const mockResponse = {};

      expect(() => contractAIService.parseGeminiResponse(mockResponse))
        .toThrow('Invalid response structure from Gemini API');
    });

    it('should throw error for missing text property', () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{}] // Missing text property
          }
        }]
      };

      expect(() => contractAIService.parseGeminiResponse(mockResponse))
        .toThrow('Invalid response structure from Gemini API');
    });



    it('should throw error when no valid JSON found', () => {
      const mockResponse = { text: 'invalid json content' };

      expect(() => contractAIService.parseGeminiResponse(mockResponse))
        .toThrow('Failed to extract valid JSON from AI response');
    });
  });
});
