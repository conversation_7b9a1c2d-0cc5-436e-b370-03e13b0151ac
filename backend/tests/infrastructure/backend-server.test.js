/**
 * Infrastructure Tests for Backend Express Server
 * 
 * These tests verify that the Express.js backend server for the B2B2B SaaS platform
 * is properly configured and can start without errors.
 */

const request = require('supertest');
const path = require('path');
const fs = require('fs');

describe('Backend Express Server Tests', () => {
  // Core Logic Testing
  describe('Core Logic - Backend Server Configuration', () => {
    test('backend/src/index.ts exists and contains Express server configuration', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      expect(fs.existsSync(serverFilePath)).toBe(true);
      
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      expect(serverFileContent).toContain('import express from');
      expect(serverFileContent).toContain('const app = express()');
      expect(serverFileContent).toContain('app.listen(');
    });

    test('server has essential middleware configured', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      // Check for required middleware
      expect(serverFileContent).toContain('helmet');
      expect(serverFileContent).toContain('cors');
      expect(serverFileContent).toContain('compression');
      expect(serverFileContent).toContain('express.json');
      expect(serverFileContent).toContain('morgan');
    });

    test('server has error handling middleware', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      // Check for error handling middleware
      expect(serverFileContent).toContain('errorHandler');
      expect(serverFileContent).toContain('notFoundHandler');
    });

    test('server has routes configured', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      const routesFilePath = path.resolve(__dirname, '../../src/api/rest/routes.ts');
      
      // Check for routes configuration
      expect(serverFileContent).toContain('apiRouter');
      expect(fs.existsSync(routesFilePath)).toBe(true);
    });
  });

  // Contextual Integration Testing
  describe('Contextual Integration - Backend API Endpoints', () => {
    test('API has health check endpoint', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      // Check for health check endpoint
      expect(serverFileContent).toContain('/api/health');
    });

    test('Routes file includes required API endpoints', () => {
      const routesFilePath = path.resolve(__dirname, '../../src/api/rest/routes.ts');
      const routesFileContent = fs.readFileSync(routesFilePath, 'utf8');
      
      // Check for required API endpoints
      expect(routesFileContent).toContain('/contracts');
      expect(routesFileContent).toContain('/users');
      expect(routesFileContent).toContain('/tenants');
    });

    test('Logging middleware is properly configured', () => {
      const loggerFilePath = path.resolve(__dirname, '../../src/infrastructure/logging/logger.ts');
      expect(fs.existsSync(loggerFilePath)).toBe(true);
      
      const loggerFileContent = fs.readFileSync(loggerFilePath, 'utf8');
      expect(loggerFileContent).toContain('logger');
    });

    test('Validation middleware is properly configured', () => {
      const validateFilePath = path.resolve(__dirname, '../../src/infrastructure/middleware/validate.ts');
      expect(fs.existsSync(validateFilePath)).toBe(true);
      
      const validateFileContent = fs.readFileSync(validateFilePath, 'utf8');
      expect(validateFileContent).toContain('validate');
    });
  });
});