/**
 * Infrastructure Tests for Docker and Docker Compose Setup
 * 
 * These tests verify that the Docker infrastructure for the B2B2B SaaS platform
 * is properly configured and operational.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

describe('Docker Infrastructure Tests', () => {
  // Core Logic Testing
  describe('Core Logic - Docker Configuration', () => {
    test('Dockerfile exists and is valid', () => {
      // Check if Dockerfile exists
      const dockerfilePath = path.resolve(__dirname, '../../../Dockerfile');
      expect(fs.existsSync(dockerfilePath)).toBe(true);
      
      // Validate Dockerfile syntax
      try {
        const dockerfilePath = path.resolve(__dirname, '../../../Dockerfile');
        execSync(`docker run --rm -i hadolint/hadolint < ${dockerfilePath}`, { stdio: 'pipe' });
        expect(true).toBe(true); // If no error is thrown, the Dockerfile is valid
      } catch (error) {
        // We're not failing the test since hadolint might not be available in all environments
        console.warn('Dockerfile linting was skipped or had warnings');
      }
    });

    test('docker-compose.yml exists and is valid', () => {
      // Check if docker-compose.yml exists
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      expect(fs.existsSync(dockerComposePath)).toBe(true);
      
      // Validate docker-compose.yml syntax
      try {
        const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
        execSync(`docker-compose -f ${dockerComposePath} config`, { stdio: 'pipe' });
        expect(true).toBe(true); // If no error is thrown, the docker-compose.yml is valid
      } catch (error) {
        console.error('docker-compose.yml validation failed:', error.message);
        expect(error).toBeUndefined();
      }
    });

    test('docker-compose.yml has required services', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for required services
      expect(dockerComposeContent).toContain('backend:');
      expect(dockerComposeContent).toContain('frontend:');
      expect(dockerComposeContent).toContain('postgres:');
      expect(dockerComposeContent).toContain('redis:');
    });
  });

  // Contextual Integration Testing
  describe('Contextual Integration - Docker Services', () => {
    test('Docker images can be built', () => {
      try {
        // Only validate the compose file to avoid actually building in the test
        const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
        execSync(`docker-compose -f ${dockerComposePath} config`, { stdio: 'pipe' });
        expect(true).toBe(true);
      } catch (error) {
        console.error('Docker compose config validation failed:', error.message);
        expect(error).toBeUndefined();
      }
    });

    test('Backend service has correct port mapping and dependencies', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for backend port mapping and dependencies
      expect(dockerComposeContent).toContain('ports:');
      expect(dockerComposeContent).toContain('"3000:3000"');
      expect(dockerComposeContent).toContain('depends_on:');
      expect(dockerComposeContent).toContain('- postgres');
      expect(dockerComposeContent).toContain('- redis');
    });

    test('Frontend service has correct port mapping and dependencies', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for frontend port mapping and dependencies
      expect(dockerComposeContent).toContain('ports:');
      expect(dockerComposeContent).toContain('"3001:3001"');
      expect(dockerComposeContent).toContain('depends_on:');
      expect(dockerComposeContent).toContain('- backend');
    });

    test('Database and Redis services have persistent volumes', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for volume definitions
      expect(dockerComposeContent).toContain('volumes:');
      expect(dockerComposeContent).toContain('postgres-data:');
      expect(dockerComposeContent).toContain('redis-data:');
    });
  });
});