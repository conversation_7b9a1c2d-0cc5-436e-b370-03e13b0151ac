/**
 * Infrastructure Tests for Database and Redis Connections
 * 
 * These tests verify that the PostgreSQL database and Redis connections
 * are properly configured in the B2B2B SaaS platform.
 */

const fs = require('fs');
const path = require('path');

describe('Database and Redis Infrastructure Tests', () => {
  // Core Logic Testing
  describe('Core Logic - Database Configuration', () => {
    test('Prisma schema exists and is valid', () => {
      const prismaSchemaPath = path.resolve(__dirname, '../../prisma/schema.prisma');
      expect(fs.existsSync(prismaSchemaPath)).toBe(true);
      
      const prismaSchema = fs.readFileSync(prismaSchemaPath, 'utf8');
      expect(prismaSchema).toContain('datasource db');
      expect(prismaSchema).toContain('provider = "postgresql"');
      expect(prismaSchema).toContain('url      = env("DATABASE_URL")');
    });

    test('Docker Compose has correct PostgreSQL configuration', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for PostgreSQL configuration
      expect(dockerComposeContent).toContain('postgres:');
      expect(dockerComposeContent).toContain('image: postgres');
      expect(dockerComposeContent).toContain('POSTGRES_USER');
      expect(dockerComposeContent).toContain('POSTGRES_PASSWORD');
      expect(dockerComposeContent).toContain('POSTGRES_DB');
    });

    test('PostgreSQL connection string is formatted correctly in Docker Compose', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for PostgreSQL connection string
      expect(dockerComposeContent).toContain('DATABASE_URL=postgresql://');
    });

    test('Prisma schema defines required models', () => {
      const prismaSchemaPath = path.resolve(__dirname, '../../prisma/schema.prisma');
      const prismaSchema = fs.readFileSync(prismaSchemaPath, 'utf8');
      
      // Check for required models
      expect(prismaSchema).toContain('model Contract');
      expect(prismaSchema).toContain('model User');
      expect(prismaSchema).toContain('model Tenant');
    });
  });

  // Contextual Integration Testing
  describe('Contextual Integration - Redis Connection', () => {
    test('Docker Compose has correct Redis configuration', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for Redis configuration
      expect(dockerComposeContent).toContain('redis:');
      expect(dockerComposeContent).toContain('image: redis');
    });

    test('Redis connection string is formatted correctly in Docker Compose', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for Redis connection string
      expect(dockerComposeContent).toContain('REDIS_URL=redis://');
    });

    test('Environment variables properly connect to Redis and PostgreSQL', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for Redis and PostgreSQL connection environment variables
      expect(dockerComposeContent).toContain('DATABASE_URL=********************************************/');
      expect(dockerComposeContent).toContain('REDIS_URL=redis://redis:6379');
    });

    test('Database and Redis health checks are configured', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for health checks
      expect(dockerComposeContent).toContain('healthcheck');
      expect(dockerComposeContent).toContain('test: ["CMD-SHELL", "pg_isready');
      expect(dockerComposeContent).toContain('test: ["CMD", "redis-cli", "ping"]');
    });
  });
});