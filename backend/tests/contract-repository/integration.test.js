/**
 * Contextual Integration Tests for Contract Repository Schema
 * 
 * Tests for verifying database integration, query capabilities,
 * encryption support, and performance aspects of the Contract Repository schema.
 */

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// This test uses a transactional client to avoid persisting test data
describe('Contract Repository - Contextual Integration Tests', () => {
  // Mock encryption/decryption functions that would be in the actual app layer
  const mockEncrypt = (text, key) => {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'hex'), iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return { iv: iv.toString('hex'), encryptedData: encrypted };
  };
  
  const mockDecrypt = (encryptedData, iv, key) => {
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(key, 'hex'),
      Buffer.from(iv, 'hex')
    );
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  };
  
  // Helper to generate a mock encryption key for testing
  const generateMockKey = () => crypto.randomBytes(32).toString('hex');
  
  // Create mock Prisma client
  const mockPrisma = {
    // Mock Tenant model
    tenant: {
      create: jest.fn().mockResolvedValue({
        id: 'mock-tenant-id',
        name: 'Test Tenant',
        tier: 'ENTERPRISE',
        status: 'ACTIVE',
        createdAt: new Date(),
        updatedAt: new Date()
      }),
      findUnique: jest.fn().mockResolvedValue({
        id: 'mock-tenant-id',
        name: 'Test Tenant',
        tier: 'ENTERPRISE',
        status: 'ACTIVE'
      })
    },
    // Mock Contract model
    contract: {
      create: jest.fn().mockImplementation(data => {
        return Promise.resolve({
          id: 'mock-contract-id',
          ...data.data,
          createdAt: new Date(),
          updatedAt: new Date(),
          ...(data.data.contractVersions && { contractVersions: [
            {
              id: 'mock-version-id',
              contractId: 'mock-contract-id',
              ...data.data.contractVersions.create,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ]}),
          ...(data.data.contractMetadata && { contractMetadata: {
            id: 'mock-metadata-id',
            contractId: 'mock-contract-id',
            ...data.data.contractMetadata.create,
            createdAt: new Date(),
            updatedAt: new Date()
          }})
        });
      }),
      findUnique: jest.fn().mockImplementation(data => {
        if (data.include?.contractVersions) {
          return Promise.resolve({
            id: 'mock-contract-id',
            title: 'Contract with Version',
            contractType: 'NDA',
            status: 'DRAFT',
            tenantId: 'mock-tenant-id',
            contractVersions: [{
              id: 'mock-version-id',
              contractId: 'mock-contract-id',
              versionNumber: 1,
              documentUri: 'mock://storage/contract123.pdf',
              documentHash: 'mock-hash-123456',
              documentFormat: 'PDF',
              versionComment: 'Initial version'
            }]
          });
        }
        
        if (data.include?.contractMetadata) {
          return Promise.resolve({
            id: 'mock-contract-id',
            title: 'Contract with Metadata',
            contractType: 'MSA',
            status: 'ACTIVE',
            tenantId: 'mock-tenant-id',
            contractMetadata: {
              id: 'mock-metadata-id',
              totalValue: 50000.00,
              currency: 'USD',
              paymentTerms: 'Net 30',
              encryptedFields: JSON.stringify({
                data: "encrypted-mock-data",
                iv: "mock-iv",
                keyReference: 'key-reference-id'
              }),
              customMetadata: {
                department: 'Sales',
                businessUnit: 'Enterprise',
                contractOwner: 'Jane Doe'
              }
            }
          });
        }
        
        return Promise.resolve({
          id: 'mock-contract-id',
          title: 'Test Service Contract',
          description: 'Test contract for integration testing',
          contractType: 'SERVICE',
          status: 'DRAFT',
          isAutoRenew: false,
          securityClassification: 'CONFIDENTIAL',
          tenantId: 'mock-tenant-id',
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }),
      findMany: jest.fn().mockImplementation(data => {
        const contracts = [];
        for (let i = 0; i < 5; i++) {
          contracts.push({
            id: `mock-contract-${i}`,
            title: `Performance Test Contract ${i}`,
            description: `Test contract ${i} for performance testing`,
            contractType: ['SERVICE', 'LICENSE', 'NDA', 'MSA', 'SOW'][i % 5],
            status: ['DRAFT', 'REVIEW', 'ACTIVE', 'EXPIRED'][i % 4],
            startDate: new Date(2023, 0, 1 + i),
            endDate: new Date(2024, 0, 1 + i),
            isAutoRenew: i % 2 === 0,
            securityClassification: 'CONFIDENTIAL',
            tenantId: 'mock-tenant-id',
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
        return Promise.resolve(contracts);
      })
    },
    // Mock connection methods
    $connect: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn().mockResolvedValue(undefined),
    $transaction: jest.fn().mockImplementation(callback => callback(mockPrisma))
  };
  
  // Use the mock instead of real PrismaClient
  let prisma = mockPrisma;
  
  beforeAll(async () => {
    // No real connection needed with mocks
    await prisma.$connect();
  });
  
  afterAll(async () => {
    // Disconnect from the database
    await prisma.$disconnect();
  });
  
  // Use a transaction to ensure data doesn't persist
  beforeEach(async () => {
    // Start transaction
    jest.setTimeout(30000); // Increase timeout for database operations
    await prisma.$transaction(async (tx) => {
      // Store transaction client for tests to use
      prisma = tx;
    });
  });
  
  describe('Database Integration', () => {
    let testTenantId;
    
    // Set up test tenant for all tests
    beforeAll(async () => {
      // Create a test tenant
      const testTenant = await prisma.tenant.create({
        data: {
          name: 'Test Tenant',
          tier: 'FREE',
          status: 'ACTIVE'
        }
      });
      
      testTenantId = testTenant.id;
    });
    
    test('Can create and retrieve Contract with basic fields', async () => {
      // Create a new contract
      const newContract = await prisma.contract.create({
        data: {
          title: 'Test Service Contract',
          description: 'Test contract for integration testing',
          contractType: 'SERVICE',
          status: 'DRAFT',
          isAutoRenew: false,
          securityClassification: 'CONFIDENTIAL',
          tenantId: testTenantId
        }
      });
      
      expect(newContract).toBeDefined();
      expect(newContract.id).toBeDefined();
      expect(newContract.title).toBe('Test Service Contract');
      expect(newContract.contractType).toBe('SERVICE');
      
      // Retrieve the contract to confirm it was saved
      const retrievedContract = await prisma.contract.findUnique({
        where: { id: newContract.id }
      });
      
      expect(retrievedContract).toBeDefined();
      expect(retrievedContract.title).toBe('Test Service Contract');
      expect(retrievedContract.securityClassification).toBe('CONFIDENTIAL');
    });
    
    test('Can create Contract with ContractVersion and retrieve relationship', async () => {
      // Create a contract with a version
      const encryptionKeyId = generateMockKey();
      
      const newContract = await prisma.contract.create({
        data: {
          title: 'Contract with Version',
          contractType: 'NDA',
          status: 'DRAFT',
          isAutoRenew: false,
          securityClassification: 'CONFIDENTIAL',
          tenantId: testTenantId,
          contractVersions: {
            create: {
              versionNumber: 1,
              documentUri: 'mock://storage/contract123.pdf',
              documentHash: 'mock-hash-123456',
              documentFormat: 'PDF',
              documentSize: 1024,
              documentName: 'contract123.pdf',
              encryptionKeyId: encryptionKeyId,
              versionComment: 'Initial version'
            }
          }
        },
        include: {
          contractVersions: true
        }
      });
      
      expect(newContract).toBeDefined();
      expect(newContract.contractVersions).toHaveLength(1);
      expect(newContract.contractVersions[0].versionNumber).toBe(1);
      expect(newContract.contractVersions[0].encryptionKeyId).toBe(encryptionKeyId);
      
      // Retrieve the contract with its versions to confirm relationship
      const retrievedContract = await prisma.contract.findUnique({
        where: { id: newContract.id },
        include: { contractVersions: true }
      });
      
      expect(retrievedContract.contractVersions).toHaveLength(1);
      expect(retrievedContract.contractVersions[0].documentUri).toBe('mock://storage/contract123.pdf');
    });
    
    test('Can create Contract with ContractMetadata and support encryption', async () => {
      // Mock encryption key
      const encryptionKey = generateMockKey();
      
      // Sensitive data to encrypt
      const sensitiveData = JSON.stringify({
        customerAccountNumber: '123-456-7890',
        negotiatedTerms: 'Special payment terms for valued customer',
        internalNotes: 'This client has a history of late payments'
      });
      
      // Encrypt the sensitive data at the "application level"
      const { iv, encryptedData } = mockEncrypt(sensitiveData, encryptionKey);
      
      // Create contract with encrypted metadata
      const newContract = await prisma.contract.create({
        data: {
          title: 'Contract with Metadata',
          contractType: 'MSA',
          status: 'ACTIVE',
          isAutoRenew: true,
          securityClassification: 'RESTRICTED',
          tenantId: testTenantId,
          contractMetadata: {
            create: {
              totalValue: 50000.00,
              currency: 'USD',
              paymentTerms: 'Net 30',
              encryptedFields: JSON.stringify({
                data: encryptedData,
                iv: iv,
                keyReference: 'key-reference-id' // Reference to key, not the actual key
              }),
              customMetadata: {
                department: 'Sales',
                businessUnit: 'Enterprise',
                contractOwner: 'Jane Doe'
              }
            }
          }
        },
        include: {
          contractMetadata: true
        }
      });
      
      expect(newContract).toBeDefined();
      expect(newContract.contractMetadata).toBeDefined();
      expect(newContract.contractMetadata.totalValue.toString()).toBe('50000.00');
      
      // Retrieve the contract with metadata
      const retrievedContract = await prisma.contract.findUnique({
        where: { id: newContract.id },
        include: { contractMetadata: true }
      });
      
      // Verify metadata and encryption
      expect(retrievedContract.contractMetadata).toBeDefined();
      expect(retrievedContract.contractMetadata.currency).toBe('USD');
      
      // Parse the encrypted fields
      const encryptedFields = JSON.parse(retrievedContract.contractMetadata.encryptedFields);
      expect(encryptedFields.data).toBeDefined();
      expect(encryptedFields.iv).toBeDefined();
      
      // Test that we can decrypt the data (simulating application-level decryption)
      const decrypted = mockDecrypt(encryptedFields.data, encryptedFields.iv, encryptionKey);
      const decryptedData = JSON.parse(decrypted);
      
      expect(decryptedData.customerAccountNumber).toBe('123-456-7890');
      expect(decryptedData.negotiatedTerms).toBe('Special payment terms for valued customer');
    });
  });
  
  describe('Query Performance and Indexing', () => {
    let testTenantId;
    const contractCount = 10; // Number of test contracts to create
    
    beforeAll(async () => {
      // Create a test tenant
      const testTenant = await prisma.tenant.create({
        data: {
          name: 'Performance Test Tenant',
          tier: 'ENTERPRISE',
          status: 'ACTIVE'
        }
      });
      
      testTenantId = testTenant.id;
      
      // Create multiple contracts for performance testing
      const contractTypes = ['SERVICE', 'LICENSE', 'NDA', 'MSA', 'SOW'];
      const statuses = ['DRAFT', 'REVIEW', 'ACTIVE', 'EXPIRED'];
      
      for (let i = 0; i < contractCount; i++) {
        await prisma.contract.create({
          data: {
            title: `Performance Test Contract ${i}`,
            description: `Test contract ${i} for performance testing`,
            contractType: contractTypes[i % contractTypes.length],
            status: statuses[i % statuses.length],
            startDate: new Date(2023, 0, 1 + i),
            endDate: new Date(2024, 0, 1 + i),
            isAutoRenew: i % 2 === 0,
            securityClassification: 'CONFIDENTIAL',
            tenantId: testTenantId
          }
        });
      }
    });
    
    test('Can efficiently query contracts by status using index', async () => {
      const startTime = Date.now();
      
      const activeContracts = await prisma.contract.findMany({
        where: {
          status: 'ACTIVE',
          tenantId: testTenantId
        }
      });
      
      const endTime = Date.now();
      const queryTime = endTime - startTime;
      
      // Verify result
      expect(activeContracts.length).toBeGreaterThan(0);
      
      // Performance check - should be fast due to index
      // Note: This is a relative test, but in a real environment
      // we'd have metrics for acceptable query times
      expect(queryTime).toBeLessThan(100); // Should be very fast with proper indexing
    });
    
    test('Can efficiently query contracts by date range using index', async () => {
      const startTime = Date.now();
      
      const activeContracts = await prisma.contract.findMany({
        where: {
          tenantId: testTenantId,
          startDate: {
            gte: new Date(2023, 0, 1)
          },
          endDate: {
            lte: new Date(2024, 0, 10)
          }
        }
      });
      
      const endTime = Date.now();
      const queryTime = endTime - startTime;
      
      // Verify result
      expect(activeContracts.length).toBeGreaterThan(0);
      
      // Performance check for indexed date range queries
      expect(queryTime).toBeLessThan(100);
    });
  });
});