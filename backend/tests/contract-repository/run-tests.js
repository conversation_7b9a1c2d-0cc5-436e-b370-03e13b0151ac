/**
 * Test Runner for Contract Repository Schema Tests
 * 
 * This script runs all the tests for the Contract Repository schema
 * and reports the results in a structured way.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for better readability in terminal
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

console.log(`${colors.cyan}=================================${colors.reset}`);
console.log(`${colors.cyan}CONTRACT REPOSITORY SCHEMA TESTS${colors.reset}`);
console.log(`${colors.cyan}=================================${colors.reset}`);
console.log('\n');

// Tests to run
const tests = [
  {
    name: 'CORE LOGIC: Schema Validation',
    file: 'schema-validation.test.js',
    description: 'Verifies schema structure, entities, relationships, enums, and constraints'
  },
  {
    name: 'CORE LOGIC: Migration Generation',
    file: 'migration-generation.test.js', 
    description: 'Verifies migration can be generated and contains proper SQL'
  },
  {
    name: 'CONTEXTUAL INTEGRATION: Database Operations & Encryption',
    file: 'integration.test.js',
    description: 'Tests database operations, relationships, and encryption patterns'
  }
];

const results = {
  passed: [],
  failed: []
};

// Function to run a test and capture result
function runTest(test) {
  console.log(`${colors.cyan}Running: ${test.name}${colors.reset}`);
  console.log(`Description: ${test.description}`);
  
  try {
    // Run Jest for the specific test file
    const output = execSync(
      `npx jest --verbose ${path.join(__dirname, test.file)}`, 
      { stdio: 'pipe' }
    ).toString();
    
    // Check for failures in the output
    if (output.includes('FAIL') || output.includes('failed')) {
      console.log(`${colors.red}✘ FAILED${colors.reset}`);
      console.log(`${colors.yellow}Error Output:${colors.reset}`);
      console.log(output);
      results.failed.push(test);
    } else {
      console.log(`${colors.green}✓ PASSED${colors.reset}`);
      results.passed.push(test);
    }
  } catch (error) {
    console.log(`${colors.red}✘ FAILED${colors.reset}`);
    console.log(`${colors.yellow}Error Output:${colors.reset}`);
    console.log(error.stdout ? error.stdout.toString() : error.message);
    results.failed.push(test);
  }
  
  console.log('\n');
}

// Run each test
tests.forEach(runTest);

// Print summary
console.log(`${colors.cyan}=================================${colors.reset}`);
console.log(`${colors.cyan}TEST SUMMARY${colors.reset}`);
console.log(`${colors.cyan}=================================${colors.reset}`);
console.log(`Total Tests: ${tests.length}`);
console.log(`${colors.green}Passed: ${results.passed.length}${colors.reset}`);
console.log(`${colors.red}Failed: ${results.failed.length}${colors.reset}`);

// Print failed tests if any
if (results.failed.length > 0) {
  console.log('\n');
  console.log(`${colors.red}Failed Tests:${colors.reset}`);
  results.failed.forEach((test, index) => {
    console.log(`${index + 1}. ${test.name}`);
  });
}

// Determine overall result
const allPassed = results.failed.length === 0;
console.log('\n');
console.log(`${colors.cyan}=================================${colors.reset}`);
console.log(allPassed 
  ? `${colors.green}✓ ALL TESTS PASSED${colors.reset}` 
  : `${colors.red}✘ SOME TESTS FAILED${colors.reset}`);
console.log(`${colors.cyan}=================================${colors.reset}`);

// Exit with appropriate code
process.exit(allPassed ? 0 : 1);