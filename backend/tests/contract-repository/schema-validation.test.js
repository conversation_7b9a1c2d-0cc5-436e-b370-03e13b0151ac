/**
 * Core Logic Tests for Contract Repository Database Schema
 * 
 * Tests for verifying Prisma schema validity, entity/relationship correctness, 
 * and enum definitions for the Contract Repository component.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Path to the Prisma schema
const SCHEMA_PATH = path.join(__dirname, '../../prisma/schema.prisma');

describe('Contract Repository Schema - Core Logic Tests', () => {
  
  // Helper function to get schema content
  const getSchemaContent = () => fs.readFileSync(SCHEMA_PATH, 'utf8');
  
  describe('Schema Validation', () => {
    test('Prisma schema file exists', () => {
      expect(fs.existsSync(SCHEMA_PATH)).toBe(true);
    });
    
    test('Prisma schema is valid (npx prisma validate)', () => {
      let error = null;
      let output = '';
      try {
        // Execute prisma validate command with DATABASE_URL from test environment
        output = execSync('npx prisma validate', {
          stdio: 'pipe',
          env: { ...process.env, DATABASE_URL: 'postgresql://postgres:postgres@localhost:5432/contract_test_db' }
        }).toString();
        console.log('Validation output:', output);
      } catch (e) {
        error = e;
        console.error('Validation error:', e);
      }
      expect(error).toBeNull();
    });
  });
  
  describe('Entity and Relationship Tests', () => {
    let schemaContent;
    
    beforeAll(() => {
      schemaContent = getSchemaContent();
    });
    
    test('Contract entity exists with required fields', () => {
      // Check for Contract model definition
      expect(schemaContent).toMatch(/model\s+Contract\s+{/);
      
      // Check for required fields
      const requiredFields = [
        'id', 'title', 'contractType', 'status', 
        'version', 'securityClassification', 'isEncrypted'
      ];
      
      requiredFields.forEach(field => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${field}\\s+`));
      });
    });
    
    test('ContractVersion entity exists with version tracking capability', () => {
      // Check for ContractVersion model
      expect(schemaContent).toMatch(/model\s+ContractVersion\s+{/);
      
      // Check for version tracking fields
      const versionFields = [
        'contractId', 'versionNumber', 'documentUri', 
        'documentHash', 'documentFormat', 'versionComment'
      ];
      
      versionFields.forEach(field => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${field}\\s+`));
      });
      
      // Check for unique constraint on contractId and versionNumber
      expect(schemaContent).toMatch(/@@unique\(\[contractId, versionNumber\]\)/);
    });
    
    test('ContractMetadata entity exists with encryption support', () => {
      // Check for ContractMetadata model
      expect(schemaContent).toMatch(/model\s+ContractMetadata\s+{/);
      
      // Check for one-to-one relationship with Contract
      expect(schemaContent).toMatch(/contractId\s+String\s+@unique/);
      
      // Check for encryption-related fields
      expect(schemaContent).toMatch(/encryptedFields\s+String\?/);
    });
    
    test('One-to-many relationship between Contract and ContractVersion exists', () => {
      // Check for relationship in Contract model
      expect(schemaContent).toMatch(/contractVersions\s+ContractVersion\[\]/);
      
      // Check for relationship in ContractVersion model
      expect(schemaContent).toMatch(/contract\s+Contract\s+@relation\(fields: \[contractId\], references: \[id\]\)/);
    });
    
    test('One-to-one relationship between Contract and ContractMetadata exists', () => {
      // Check in Contract model
      expect(schemaContent).toMatch(/contractMetadata\s+ContractMetadata\?/);
      
      // Check in ContractMetadata model
      expect(schemaContent).toMatch(/contract\s+Contract\s+@relation\(fields: \[contractId\], references: \[id\]\)/);
    });
  });
  
  describe('Enum Definitions Tests', () => {
    let schemaContent;
    
    beforeAll(() => {
      schemaContent = getSchemaContent();
    });
    
    test('ContractType enum is properly defined', () => {
      expect(schemaContent).toMatch(/enum\s+ContractType\s+{/);
      
      const contractTypes = [
        'SERVICE', 'LICENSE', 'EMPLOYMENT', 
        'NDA', 'MSA', 'SOW', 'OTHER'
      ];
      
      contractTypes.forEach(type => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${type}\\s*$`, 'm'));
      });
    });
    
    test('ContractStatus enum is properly defined', () => {
      expect(schemaContent).toMatch(/enum\s+ContractStatus\s+{/);
      
      const statusTypes = [
        'DRAFT', 'REVIEW', 'PENDING_APPROVAL', 'APPROVED',
        'ACTIVE', 'EXPIRING', 'EXPIRED', 'TERMINATED', 'RENEWED'
      ];
      
      statusTypes.forEach(status => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${status}\\s*$`, 'm'));
      });
    });
    
    test('DocumentFormat enum is properly defined', () => {
      expect(schemaContent).toMatch(/enum\s+DocumentFormat\s+{/);
      
      const formatTypes = [
        'PDF', 'DOCX', 'DOC', 'RTF', 'TXT', 'XLSX', 'OTHER'
      ];
      
      formatTypes.forEach(format => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${format}\\s*$`, 'm'));
      });
    });
    
    test('SecurityClassification enum is properly defined', () => {
      expect(schemaContent).toMatch(/enum\s+SecurityClassification\s+{/);
      
      const securityLevels = [
        'PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED'
      ];
      
      securityLevels.forEach(level => {
        expect(schemaContent).toMatch(new RegExp(`\\s+${level}\\s*$`, 'm'));
      });
    });
  });
  
  describe('Database Constraints and Indexes Tests', () => {
    let schemaContent;
    
    beforeAll(() => {
      schemaContent = getSchemaContent();
    });
    
    test('Contract model has appropriate indexes for performance', () => {
      // Check for required indexes
      const requiredIndexes = [
        '@@index\\(\\[tenantId\\]\\)',
        '@@index\\(\\[status\\]\\)',
        '@@index\\(\\[contractType\\]\\)',
        '@@index\\(\\[startDate, endDate\\]\\)',
        '@@index\\(\\[title\\]\\)'
      ];
      
      requiredIndexes.forEach(index => {
        expect(schemaContent).toMatch(new RegExp(index));
      });
    });
    
    test('ContractVersion model has necessary indexes and constraints', () => {
      // Check for indexes and unique constraints
      expect(schemaContent).toMatch(/@@index\(\[contractId\]\)/);
      expect(schemaContent).toMatch(/@@index\(\[versionNumber\]\)/);
      expect(schemaContent).toMatch(/@@unique\(\[contractId, versionNumber\]\)/);
    });
  });
});