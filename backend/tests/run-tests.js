/**
 * Main Test Runner for B2B2B SaaS Platform Infrastructure Tests
 * 
 * This script runs all the infrastructure tests for the platform
 * and reports the results.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk'); // You may need to install this package with npm install chalk

console.log(chalk.bold.blue('🎯 Running B2B2B SaaS Platform Infrastructure Tests'));
console.log(chalk.bold.blue('================================================'));

// Define test suites
const testSuites = [
  {
    name: 'Docker Infrastructure Tests',
    path: './infrastructure/docker.test.js',
    category: 'Core Logic'
  },
  {
    name: 'Backend Express Server Tests',
    path: './infrastructure/backend-server.test.js',
    category: 'Core Logic'
  },
  {
    name: 'Database and Redis Tests',
    path: './infrastructure/database.test.js',
    category: 'Core Logic'
  },
  {
    name: 'Frontend Next.js Tests',
    path: '../../frontend/tests/components/frontend.test.js',
    category: 'Core Logic'
  },
  {
    name: 'API Connectivity Tests',
    path: './integration/api-connectivity.test.js',
    category: 'Contextual Integration'
  }
];

// Test results
const results = {
  total: testSuites.length,
  passed: 0,
  failed: 0,
  failedTests: []
};

// Run each test suite
testSuites.forEach(suite => {
  console.log(chalk.blue(`\n📋 Running ${suite.name} (${suite.category})...`));
  try {
    // Run test using Jest (or your preferred test runner)
    execSync(`npx jest ${suite.path} --silent`, { stdio: 'pipe' });
    console.log(chalk.green(`✅ ${suite.name} PASSED`));
    results.passed++;
  } catch (error) {
    console.log(chalk.red(`❌ ${suite.name} FAILED`));
    console.error(chalk.red(error.stdout.toString()));
    results.failed++;
    results.failedTests.push({
      name: suite.name,
      category: suite.category,
      error: error.stdout.toString()
    });
  }
});

// Print summary
console.log(chalk.bold.blue('\n📊 Test Results Summary'));
console.log(chalk.bold.blue('===================='));
console.log(`Total Tests: ${results.total}`);
console.log(chalk.green(`Passed: ${results.passed}`));
console.log(chalk.red(`Failed: ${results.failed}`));

// Print failed tests
if (results.failed > 0) {
  console.log(chalk.bold.red('\n❌ Failed Tests:'));
  results.failedTests.forEach(test => {
    console.log(chalk.red(`- ${test.name} (${test.category})`));
  });
}

// Overall result
if (results.failed === 0) {
  console.log(chalk.bold.green('\n✅ All infrastructure tests PASSED'));
} else {
  console.log(chalk.bold.red(`\n❌ ${results.failed} test suites FAILED`));
}

// Exit with appropriate code
process.exit(results.failed > 0 ? 1 : 0);