/**
 * Integration Tests for API Connectivity
 * 
 * These tests verify that the backend API endpoints are accessible from the frontend
 * and that the security middleware is properly configured.
 */

const fs = require('fs');
const path = require('path');

describe('API Connectivity Integration Tests', () => {
  // Core Logic Testing
  describe('Core Logic - API Configuration', () => {
    test('API routes are defined in the backend', () => {
      const routesFilePath = path.resolve(__dirname, '../../src/api/rest/routes.ts');
      expect(fs.existsSync(routesFilePath)).toBe(true);
      
      const routesFileContent = fs.readFileSync(routesFilePath, 'utf8');
      expect(routesFileContent).toContain('apiRouter');
      expect(routesFileContent).toContain('/contracts');
      expect(routesFileContent).toContain('/users');
      expect(routesFileContent).toContain('/tenants');
    });

    test('API health endpoint is defined', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      expect(serverFileContent).toContain('/api/health');
      expect(serverFileContent).toContain('status: \'ok\'');
    });

    test('Error handling middleware is properly configured', () => {
      const errorHandlerPath = path.resolve(__dirname, '../../src/infrastructure/middleware/errorHandler.ts');
      const notFoundHandlerPath = path.resolve(__dirname, '../../src/infrastructure/middleware/notFoundHandler.ts');
      
      expect(fs.existsSync(errorHandlerPath)).toBe(true);
      expect(fs.existsSync(notFoundHandlerPath)).toBe(true);
    });

    test('Validation middleware is properly configured', () => {
      const validatePath = path.resolve(__dirname, '../../src/infrastructure/middleware/validate.ts');
      expect(fs.existsSync(validatePath)).toBe(true);
      
      const validateContent = fs.readFileSync(validatePath, 'utf8');
      expect(validateContent).toContain('validate');
    });
  });

  // Contextual Integration Testing
  describe('Contextual Integration - Frontend-Backend Connection', () => {
    test('Frontend is configured to connect to the backend API', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for API URL environment variable in frontend service
      expect(dockerComposeContent).toContain('NEXT_PUBLIC_API_URL=http://localhost:3000');
    });

    test('Backend service has CORS enabled for frontend connections', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      expect(serverFileContent).toContain('cors');
    });

    test('Security middleware is properly configured', () => {
      const serverFilePath = path.resolve(__dirname, '../../src/index.ts');
      const serverFileContent = fs.readFileSync(serverFilePath, 'utf8');
      
      // Check for security middleware
      expect(serverFileContent).toContain('helmet');
      expect(serverFileContent).toContain('rateLimit');
    });

    test('Network ports are properly mapped in Docker Compose', () => {
      const dockerComposePath = path.resolve(__dirname, '../../../docker-compose.yml');
      const dockerComposeContent = fs.readFileSync(dockerComposePath, 'utf8');
      
      // Check for port mappings
      expect(dockerComposeContent).toContain('"3000:3000"'); // Backend
      expect(dockerComposeContent).toContain('"3001:3001"'); // Frontend
    });
  });
});