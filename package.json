{"name": "b2b2b-contract-management-platform", "version": "0.1.0", "description": "B2B2B SaaS Platform for Contract Lifecycle Management", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"setup": "npm install && npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "prisma:generate": "cd backend && npx prisma generate", "prisma:migrate": "cd backend && npx prisma migrate dev"}, "keywords": ["contract", "lifecycle", "management", "SaaS", "B2B2B"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}}