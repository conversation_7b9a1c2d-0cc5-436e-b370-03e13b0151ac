services:
  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: multistrat-backend:latest
    container_name: multistrat-backend
    ports:
      - "5000:5000"
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - app-network
    dns:
      - 8.8.8.8
      - 1.1.1.1
    command: >
      sh -c "
        cd /app &&
        echo 'Waiting for database to be ready...' &&
        sleep 5 &&
        npm run db:create-pgvector &&
        npm run db:check-pgvector &&
        npx prisma generate &&
        npx prisma migrate deploy &&
        npm run db:seed &&
        npm run dev
      "

  # Frontend Next.js application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=https://multistratapi.appvintech.com
        - NODE_ENV=production
    image: multistrat-frontend:latest
    container_name: multistrat-frontend
    ports:
      - "3002:3000"
    env_file:
      - ./frontend/.env
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL database with pgvector extension
  postgres:
    image: ankane/pgvector:latest
    container_name: multistrat-postgres
#    expose:
#     - 5432
#    ports:
#      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=clm_dev
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-data:

networks:
  app-network:
    driver: bridge
