{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "noUnusedLocals": false, "noUnusedParameters": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/components/ui/*": ["src/components/ui/*"], "@/lib/*": ["src/lib/*"], "@/utils/*": ["src/utils/*"], "@/styles/*": ["src/styles/*"], "@/app/*": ["src/app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}