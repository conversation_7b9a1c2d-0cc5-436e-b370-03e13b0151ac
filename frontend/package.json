{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@auth/core": "^0.39.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.74.7", "@tanstack/react-query-devtools": "^5.74.7", "@tanstack/react-table": "^8.21.3", "@tanstack/table-core": "^8.21.3", "@types/node": "^22.15.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-auth": "^5.0.0-beta.5", "next-themes": "^0.4.6", "postcss": "^8.5.3", "posthog-js": "^1.256.2", "posthog-node": "^5.1.1", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@types/jest": "^29.5.14", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}}