/**
 * Folder Timeline Component
 * Professional timeline visualization for folder contract history
 * Unified component with consistent styling and single close button
 */

"use client";

import React from "react";
import { FileText, Award } from "lucide-react";
import {
  Timeline,
  TimelineItem,
  TimelineModal,
  TimelineHeader,
} from "@/components/ui/timeline";
import { Contract } from "@/services/contractService";
import { FolderWithContractCount } from "@/services/folderService";
import { formatRelativeDate, formatContractValue } from "@/lib/format-utils";

interface FolderTimelineProps {
  folder: FolderWithContractCount;
  contracts: Contract[];
  isOpen: boolean;
  onClose: () => void;
}

// Utility function to detect MSA contracts
function isMSAContract(agreementType: string | undefined | null): boolean {
  if (!agreementType) return false;

  const normalizedType = agreementType.toLowerCase().replace(/[_\s-]/g, "");
  return (
    normalizedType.includes("msa") ||
    normalizedType.includes("masterserviceagreement") ||
    normalizedType.includes("masterservice")
  );
}

export function FolderTimeline({
  folder,
  contracts,
  isOpen,
  onClose,
}: FolderTimelineProps) {
  // Convert contracts to timeline items
  const timelineItems: TimelineItem[] = contracts.map((contract) => {
    // Use updatedAt as proxy for when contract was added to folder
    const timestamp = new Date(contract.updatedAt);

    // Check if this is an MSA contract
    const isMSA = isMSAContract(contract.agreementType);

    // Create metadata for the timeline item
    const metadata: Record<string, any> = {};

    if (contract.agreementType) {
      metadata["Type"] = contract.agreementType.replace(/_/g, " ");
    }

    if (contract.provider) {
      metadata["Provider"] = contract.provider;
    }

    if (
      contract.value &&
      contract.value !== "N/A" &&
      contract.value !== "Not specified"
    ) {
      try {
        metadata["Value"] = formatContractValue(
          contract.value,
          undefined,
          true
        );
      } catch (error) {
        // Fallback to original value if formatting fails
        metadata["Value"] = contract.value;
      }
    }

    if (contract.endDate) {
      const endDate = new Date(contract.endDate);
      const now = new Date();
      const isExpired = endDate < now;
      metadata["Status"] = isExpired ? "Expired" : "Active";
    }

    // Add MSA indicator to metadata if applicable
    if (isMSA) {
      metadata["MSA"] = "Master Service Agreement";
    }

    return {
      id: contract.id,
      title: contract.title,
      description:
        contract.description ||
        `Contract ${contract.contractNumber || ""}`.trim(),
      timestamp,
      icon: isMSA ? <Award /> : <FileText />,
      metadata,
      // Add custom properties for MSA styling
      isMSA,
    };
  });

  return (
    <TimelineModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${folder.folder.name} Timeline`}
      subtitle="Chronological view of contract additions and updates"
    >
      {timelineItems.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
          <h3 className="text-base font-medium text-foreground mb-2">
            No Timeline Data
          </h3>
          <p className="text-sm text-muted-foreground">
            This folder doesn't contain any contracts yet.
          </p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Timeline Header */}
          <TimelineHeader
            title="Contract History"
            subtitle={`Showing ${timelineItems.length} contract${
              timelineItems.length === 1 ? "" : "s"
            } in chronological order`}
            itemCount={timelineItems.length}
          />

          {/* Grouped Timeline */}
          <Timeline
            items={timelineItems}
            showConnectors={true}
            variant="default"
            groupByDate={true}
            className="px-0"
          />

          {/* Summary Footer */}
          <div className="border-t pt-4 mt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {timelineItems.length}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Total Contracts
                </div>
              </div>
              {/* <div>
                <div className="text-lg font-semibold text-[#09260D] dark:text-[#4ade80]">
                  {timelineItems.filter((item) => item.isMSA).length}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  MSA Contracts
                </div>
              </div> */}
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {
                    new Set(
                      timelineItems.map((item) => item.timestamp.toDateString())
                    ).size
                  }
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Active Days
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {formatRelativeDate(folder.folder.createdAt)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Folder Created
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {formatRelativeDate(folder.folder.updatedAt)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Last Updated
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </TimelineModal>
  );
}
