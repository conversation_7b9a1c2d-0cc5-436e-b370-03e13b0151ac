/**
 * Folder Accordion Component
 * Displays folders as expandable accordion items with contracts
 */

"use client";

import { useState } from "react";
import {
  ChevronDown,
  ChevronRight,
  Folder,
  FolderOpen,
  Clock,
  FlaskConical,
  GitBranch,
  Network,
} from "lucide-react";
import { calculateContractStatus } from "@/lib/contract-status-utils";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTable, DataTableColumn } from "@/components/ui/data-table";
import { Contract } from "@/services/contractService";
import { FolderWithContractCount } from "@/services/folderService";
import { cn } from "@/lib/utils";
import { formatRelativeDate } from "@/lib/format-utils";
// Note: Modal components removed since they're now handled in parent component
// Note: FolderHierarchyTimeline removed since folders are now virtual

interface FolderAccordionProps {
  folders: FolderWithContractCount[];
  contractColumns: DataTableColumn<Contract>[];
  loading?: boolean;
  // Note: Folder management callbacks removed since folders are now virtual
  onDeleteContract?: (contract: Contract) => void;
  className?: string;
  // New props for provider selection
  onProviderSelect?: (provider: FolderWithContractCount) => void;
  selectedProvider?: string;
}

export function FolderAccordion({
  folders,
  contractColumns,
  loading = false,
  className,
  onProviderSelect,
  selectedProvider,
}: FolderAccordionProps) {
  const [openFolders, setOpenFolders] = useState<Set<string>>(new Set());
  // Note: Modal states removed since they're now handled in parent component

  const toggleFolder = (folderId: string) => {
    const newOpenFolders = new Set<string>();
    // Only allow one folder to be open at a time
    if (!openFolders.has(folderId)) {
      newOpenFolders.add(folderId);
    }
    // If clicking the same folder that's already open, close it (newOpenFolders remains empty)
    setOpenFolders(newOpenFolders);
  };

  // Handler for provider selection
  const handleProviderClick = (provider: FolderWithContractCount) => {
    if (onProviderSelect) {
      onProviderSelect(provider);
    }
  };

  const getFolderContracts = (
    folderWithCount: FolderWithContractCount
  ): Contract[] => {
    // Transform contracts from extraction format to Contract format
    return (folderWithCount.contracts || []).map((contractData: any) => {
      // Check if this is the new extraction format
      if (contractData.contractId && contractData.fixedFields) {
        // Extract dates for status calculation
        const startDate = contractData.fixedFields?.start_date?.value || null;
        const endDate = contractData.fixedFields?.end_date?.value || null;

        // Calculate status based on dates using the same logic as contracts tab
        const calculatedStatus = calculateContractStatus(startDate, endDate);

        // Transform from extraction format to Contract format
        return {
          id: contractData.contractId,
          title:
            contractData.fixedFields?.original_filename?.value ||
            "Unknown Contract",
          agreementType:
            contractData.fixedFields?.agreement_type?.value || "UNKNOWN",
          provider: contractData.fixedFields?.provider?.value || "N/A",
          value: contractData.fixedFields?.total_amount?.value || "N/A",
          startDate: startDate,
          endDate: endDate,
          contractNumber: contractData.fixedFields?.contract_id?.value || "N/A",
          classification:
            contractData.fixedFields?.contract_classification?.value || null,
          status: calculatedStatus, // Use calculated status
          updatedAt: contractData.updatedAt,
          // Add extraction data for three-tier functions
          extraction: {
            fixedFields: contractData.fixedFields,
            dynamicFields: contractData.dynamicFields,
            specialFields: contractData.specialFields,
            overallConfidence: contractData.overallConfidence,
          },
        } as Contract;
      }

      // If it's already in Contract format, return as is
      return contractData as Contract;
    });
  };

  // Create folder-specific columns (same as regular columns since folders are now virtual)
  const createFolderColumns = (): DataTableColumn<Contract>[] => {
    return contractColumns;
  };

  if (loading) {
    return (
      <div className={cn("space-y-2", className)}>
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="h-12 bg-muted/50 rounded-lg animate-pulse"
          />
        ))}
      </div>
    );
  }

  if (folders.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No folders found</p>
        <p className="text-sm text-muted-foreground mt-1">
          Create folders to organize your contracts
        </p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-1", className)}>
      {folders.map((folderWithCount) => {
        const { folder, contractCount } = folderWithCount;
        const isOpen = openFolders.has(folder.id);
        const folderContracts = getFolderContracts(folderWithCount);

        return (
          <Collapsible
            key={folder.id}
            open={isOpen}
            onOpenChange={() => toggleFolder(folder.id)}
          >
            <div className="border rounded-lg bg-card">
              {/* Folder Header */}
              <CollapsibleTrigger asChild>
                <div
                  className={`flex items-center justify-between px-4 py-3 hover:bg-muted/50 transition-colors cursor-pointer ${selectedProvider === folder.name ? 'bg-primary/10 border-l-4 border-primary' : ''
                    }`}
                  onClick={() => {
                    // Handle provider selection on click
                    handleProviderClick(folderWithCount);
                  }}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      {isOpen ? (
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      )}
                      {isOpen ? (
                        <FolderOpen className="h-5 w-5 text-emerald-500" />
                      ) : (
                        <Folder className="h-5 w-5 text-emerald-500" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold m-0 text-foreground truncate">
                          {folder.name}
                        </h3>
                        <Badge
                          variant="secondary"
                          className="text-xs font-medium"
                        >
                          {contractCount}
                        </Badge>
                        {selectedProvider === folder.name && (
                          <Badge
                            variant="default"
                            className="text-xs font-medium bg-primary text-primary-foreground"
                          >
                            Selected
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          Updated {formatRelativeDate(folder.updatedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleTrigger>

              {/* Folder Content */}
              <CollapsibleContent>
                <div className="border-t bg-muted/10">
                  {folderContracts.length > 0 ? (
                    <div className="p-3">
                      <DataTable
                        data={folderContracts}
                        columns={createFolderColumns()}
                        loading={false}
                        minWidth="1660px"
                        containerClassName="border-0 rounded-none"
                        emptyMessage="No contracts in this folder"
                      />
                    </div>
                  ) : (
                    <div className="py-6 px-4 text-center">
                      <p className="text-sm text-muted-foreground">
                        No contracts in this folder
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Drag contracts here or use the "Group Contracts" feature
                      </p>
                    </div>
                  )}
                </div>
              </CollapsibleContent>

              {/* Note: Hierarchy timeline removed since folders are now virtual */}

              {/* Note: Export modal removed since folders are now virtual */}
            </div>
          </Collapsible>
        );
      })}
    </div>
  );
}
