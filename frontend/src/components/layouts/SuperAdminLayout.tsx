/**
 * Super Admin Layout Component
 * Layout specifically for super admin pages
 */

import React, { ReactNode } from 'react';
import { SuperAdminTopNavbar } from './SuperAdminTopNavbar';
import { SuperAdminRoute } from '@/components/auth/RoleBasedRoute';

interface SuperAdminLayoutProps {
  children: ReactNode;
}

export function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  return (
    <SuperAdminRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Top Navigation */}
        <SuperAdminTopNavbar />

        {/* Main content */}
        <main className="min-h-[calc(100vh-4rem)]">
          {children}
        </main>
      </div>
    </SuperAdminRoute>
  );
}
