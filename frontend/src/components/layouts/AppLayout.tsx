/**
 * App Layout Component
 * Main layout for the application
 */

import React, { ReactNode } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { SuperAdminLayout } from './SuperAdminLayout';
import { useAuth } from '@/contexts/AuthContext';

interface AppLayoutProps {
  children: ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const { user } = useAuth();

  // Use Super Admin layout for super admin users
  if (user?.isSuperAdmin || user?.role === 'SUPER_ADMIN') {
    return <SuperAdminLayout>{children}</SuperAdminLayout>;
  }

  // Default layout for regular users
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />

        {/* Content */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
