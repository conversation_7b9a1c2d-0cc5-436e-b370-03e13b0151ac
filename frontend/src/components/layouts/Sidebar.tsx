/**
 * Sidebar Component
 * Main navigation sidebar for the application
 */

import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  HomeIcon,
  FileTextIcon,
  KeyIcon,
  BellIcon,
  SettingsIcon,
  UsersIcon,
  SearchIcon,
  MessageSquareIcon,
  BarChartIcon,
  BuildingIcon,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
  requiredPermission?: string;
}

function NavItem({
  href,
  icon,
  label,
  active,
  requiredPermission,
}: NavItemProps) {
  const { hasPermission } = useAuth();

  // Skip rendering if permission is required but not granted
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return null;
  }

  return (
    <Link
      href={href}
      className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors font-medium ${
        active
          ? "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
      }`}
    >
      {icon}
      <span>{label}</span>
    </Link>
  );
}

export function Sidebar() {
  const router = useRouter();
  const { user } = useAuth();

  // Check if a path is active
  const isActive = (path: string) => {
    if (path === "/") {
      return router.pathname === "/";
    }
    return router.pathname.startsWith(path);
  };

  return (
    <div className="w-64 h-full bg-sidebar border-r border-sidebar-border flex flex-col">
      {/* Logo */}
      <div className="p-4 border-b border-sidebar-border">
        <Link href="/" className="flex items-center gap-2">
          <img src="/maitlogo.svg" alt="MAIT" className="h-8 w-auto" />
          <span className="text-xl font-extrabold text-sidebar-foreground logo-text">
            MAIT
          </span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        <NavItem
          href="/"
          icon={<HomeIcon size={18} />}
          label="Dashboard"
          active={isActive("/")}
        />

        <NavItem
          href="/contracts"
          icon={<FileTextIcon size={18} />}
          label="Contracts"
          active={isActive("/contracts")}
          requiredPermission="contracts:read"
        />

        <NavItem
          href="/licenses"
          icon={<KeyIcon size={18} />}
          label="Licenses"
          active={isActive("/licenses")}
          requiredPermission="licenses:read"
        />

        <NavItem
          href="/chat"
          icon={<MessageSquareIcon size={18} />}
          label="Chat"
          active={isActive("/chat")}
          requiredPermission="chat:read"
        />

        <NavItem
          href="/search"
          icon={<SearchIcon size={18} />}
          label="Search"
          active={isActive("/search")}
          requiredPermission="ai:read"
        />

        <NavItem
          href="/notifications"
          icon={<BellIcon size={18} />}
          label="Notifications"
          active={isActive("/notifications")}
        />

        <NavItem
          href="/analytics"
          icon={<BarChartIcon size={18} />}
          label="Analytics"
          active={isActive("/analytics")}
        />

        {/* Admin section */}
        {user?.role === "admin" && (
          <>
            <div className="pt-4 pb-2">
              <div className="px-3 text-xs font-bold text-sidebar-disabled uppercase tracking-wider">
                Admin
              </div>
            </div>

            <NavItem
              href="/admin/users"
              icon={<UsersIcon size={18} />}
              label="Users"
              active={isActive("/admin/users")}
              requiredPermission="users:read"
            />

            <NavItem
              href="/admin/tenants"
              icon={<BuildingIcon size={18} />}
              label="Tenants"
              active={isActive("/admin/tenants")}
              requiredPermission="tenants:read"
            />

            <NavItem
              href="/admin/settings"
              icon={<SettingsIcon size={18} />}
              label="Settings"
              active={isActive("/admin/settings")}
              requiredPermission="settings:read"
            />
          </>
        )}
      </nav>

      {/* User section */}
      <div className="p-4 border-t border-sidebar-border">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-sidebar-primary flex items-center justify-center text-sidebar-primary-foreground font-semibold">
            {user?.userId.substring(0, 2).toUpperCase() || "U"}
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate text-sidebar-foreground">
              {user?.userId || "User"}
            </div>
            <div className="text-xs text-sidebar-disabled truncate">
              {user?.role || "Role"}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
