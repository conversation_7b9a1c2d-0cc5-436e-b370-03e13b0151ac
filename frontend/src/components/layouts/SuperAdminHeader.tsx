/**
 * Super Admin Header Component
 * Header for super admin pages with platform-wide controls
 */

import React, { useState, useEffect } from 'react';
import { 
  BellIcon, 
  SearchIcon, 
  RefreshCwIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ServerIcon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SystemStatus {
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  lastUpdated: string;
}

export function SuperAdminHeader() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    status: 'healthy',
    message: 'All systems operational',
    lastUpdated: new Date().toLocaleTimeString()
  });
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'warning', message: 'High memory usage on server-2', time: '5m ago' },
    { id: 2, type: 'info', message: 'New tenant registration: Acme Corp', time: '15m ago' },
    { id: 3, type: 'error', message: 'Failed backup job for tenant-db-3', time: '1h ago' },
  ]);

  const refreshSystemStatus = () => {
    // Simulate system status check
    setSystemStatus({
      status: 'healthy',
      message: 'All systems operational',
      lastUpdated: new Date().toLocaleTimeString()
    });
  };

  const getStatusIcon = () => {
    switch (systemStatus.status) {
      case 'healthy':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case 'critical':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ServerIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (systemStatus.status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <header className="h-16 border-b border-border bg-background flex items-center justify-between px-6">
      {/* Left side - Search */}
      <div className="flex items-center gap-4 flex-1">
        <div className="relative max-w-md">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tenants, users, or system logs..."
            className="pl-10 w-80"
          />
        </div>
      </div>

      {/* Right side - Status and controls */}
      <div className="flex items-center gap-4">
        {/* System Status */}
        <div className={`flex items-center gap-2 px-3 py-1 rounded-full border text-sm ${getStatusColor()}`}>
          {getStatusIcon()}
          <span className="font-medium">{systemStatus.message}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshSystemStatus}
            className="h-6 w-6 p-0 ml-2"
          >
            <RefreshCwIcon className="h-3 w-3" />
          </Button>
        </div>

        {/* Notifications */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <BellIcon className="h-5 w-5" />
              {notifications.length > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
                  {notifications.length}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>System Notifications</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {notifications.length === 0 ? (
              <DropdownMenuItem disabled>
                No new notifications
              </DropdownMenuItem>
            ) : (
              notifications.map((notification) => (
                <DropdownMenuItem key={notification.id} className="flex flex-col items-start p-3">
                  <div className="flex items-center gap-2 w-full">
                    {notification.type === 'error' && <XCircleIcon className="h-4 w-4 text-red-500" />}
                    {notification.type === 'warning' && <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />}
                    {notification.type === 'info' && <CheckCircleIcon className="h-4 w-4 text-blue-500" />}
                    <span className="text-sm font-medium flex-1">{notification.message}</span>
                  </div>
                  <span className="text-xs text-muted-foreground mt-1">{notification.time}</span>
                </DropdownMenuItem>
              ))
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-center">
              <Button variant="ghost" size="sm" className="w-full">
                View All Notifications
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Last updated timestamp */}
        <div className="text-xs text-muted-foreground">
          Updated: {systemStatus.lastUpdated}
        </div>
      </div>
    </header>
  );
}
