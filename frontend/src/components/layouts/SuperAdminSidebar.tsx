/**
 * Super Admin Sidebar Component
 * Navigation sidebar specifically for super admin users
 */

import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  LayoutDashboardIcon,
  BuildingIcon,
  UsersIcon,
  BarChartIcon,
  SettingsIcon,
  ShieldIcon,
  DatabaseIcon,
  ActivityIcon,
  AlertTriangleIcon,
  FileTextIcon,
  TrendingUpIcon,
  ServerIcon,
  BellIcon,
  LogOutIcon,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
  badge?: string | number;
}

function NavItem({ href, icon, label, active, badge }: NavItemProps) {
  return (
    <Link
      href={href}
      className={`flex items-center justify-between gap-3 px-3 py-2 rounded-md transition-colors font-medium ${
        active
          ? "bg-primary text-primary-foreground font-semibold"
          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
      }`}
    >
      <div className="flex items-center gap-3">
        {icon}
        <span>{label}</span>
      </div>
      {badge && (
        <span className="bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full">
          {badge}
        </span>
      )}
    </Link>
  );
}

interface NavSectionProps {
  title: string;
  children: React.ReactNode;
}

function NavSection({ title, children }: NavSectionProps) {
  return (
    <div className="space-y-1">
      <div className="px-3 py-2">
        <div className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
          {title}
        </div>
      </div>
      {children}
    </div>
  );
}

export function SuperAdminSidebar() {
  const router = useRouter();
  const { user, logout } = useAuth();

  // Check if a path is active
  const isActive = (path: string) => {
    if (path === "/super-admin/dashboard") {
      return router.pathname === "/super-admin/dashboard" || router.pathname === "/super-admin";
    }
    return router.pathname.startsWith(path);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <div className="w-64 h-full bg-background border-r border-border flex flex-col">
      {/* Logo */}
      <div className="p-4 border-b border-border">
        <Link href="/super-admin/dashboard" className="flex items-center gap-2">
          <ShieldIcon className="h-8 w-8 text-primary" />
          <div>
            <span className="text-xl font-extrabold text-foreground">
              APTIO
            </span>
            <div className="text-xs text-muted-foreground font-medium">
              Super Admin
            </div>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-6 overflow-y-auto">
        {/* Overview Section */}
        <NavSection title="Overview">
          <NavItem
            href="/super-admin/dashboard"
            icon={<LayoutDashboardIcon size={18} />}
            label="Dashboard"
            active={isActive("/super-admin/dashboard")}
          />
          <NavItem
            href="/super-admin/analytics"
            icon={<TrendingUpIcon size={18} />}
            label="Platform Analytics"
            active={isActive("/super-admin/analytics")}
          />
          <NavItem
            href="/super-admin/monitoring"
            icon={<ActivityIcon size={18} />}
            label="System Monitoring"
            active={isActive("/super-admin/monitoring")}
          />
        </NavSection>

        {/* Management Section */}
        <NavSection title="Management">
          <NavItem
            href="/super-admin/tenants"
            icon={<BuildingIcon size={18} />}
            label="Tenant Management"
            active={isActive("/super-admin/tenants")}
          />
          <NavItem
            href="/super-admin/users"
            icon={<UsersIcon size={18} />}
            label="User Management"
            active={isActive("/super-admin/users")}
          />
          <NavItem
            href="/super-admin/contracts"
            icon={<FileTextIcon size={18} />}
            label="Contract Overview"
            active={isActive("/super-admin/contracts")}
          />
        </NavSection>

        {/* System Section */}
        <NavSection title="System">
          <NavItem
            href="/super-admin/database"
            icon={<DatabaseIcon size={18} />}
            label="Database Management"
            active={isActive("/super-admin/database")}
          />
          <NavItem
            href="/super-admin/performance"
            icon={<ServerIcon size={18} />}
            label="Performance"
            active={isActive("/super-admin/performance")}
          />
          <NavItem
            href="/super-admin/security"
            icon={<ShieldIcon size={18} />}
            label="Security & Audit"
            active={isActive("/super-admin/security")}
          />
        </NavSection>

        {/* Configuration Section */}
        <NavSection title="Configuration">
          <NavItem
            href="/super-admin/settings"
            icon={<SettingsIcon size={18} />}
            label="Platform Settings"
            active={isActive("/super-admin/settings")}
          />
          <NavItem
            href="/super-admin/notifications"
            icon={<BellIcon size={18} />}
            label="System Notifications"
            active={isActive("/super-admin/notifications")}
          />
          <NavItem
            href="/super-admin/alerts"
            icon={<AlertTriangleIcon size={18} />}
            label="Alerts & Issues"
            active={isActive("/super-admin/alerts")}
            badge="3"
          />
        </NavSection>
      </nav>

      {/* User section */}
      <div className="p-4 border-t border-border space-y-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-semibold">
            <ShieldIcon size={16} />
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate text-foreground">
              Super Admin
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {user?.email || "<EMAIL>"}
            </div>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start gap-2"
        >
          <LogOutIcon size={14} />
          Logout
        </Button>
      </div>
    </div>
  );
}
