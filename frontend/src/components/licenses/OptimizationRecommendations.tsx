/**
 * Optimization Recommendations Component
 * Displays license optimization recommendations
 */

import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircle2,
  HandCoins,
  Trash2,
  RefreshC<PERSON>,
  BarChart3,
  <PERSON>ertTriangle,
  ArrowRight,
} from "lucide-react";

/**
 * Optimization recommendation interface
 */
export interface OptimizationRecommendation {
  id: string;
  licenseId: string;
  type: string;
  priority: string;
  description: string;
  currentCost: number;
  projectedCost: number;
  potentialSavings: number;
  currency: string;
  implementationSteps: string[];
}

/**
 * Props for OptimizationRecommendations component
 */
interface OptimizationRecommendationsProps {
  recommendations: OptimizationRecommendation[];
  totalCurrentCost: number;
  totalProjectedCost: number;
  totalPotentialSavings: number;
  currency: string;
  summary: string;
  onImplement?: (recommendation: OptimizationRecommendation) => void;
}

/**
 * Optimization Recommendations Component
 */
export function OptimizationRecommendations({
  recommendations,
  totalCurrentCost,
  totalProjectedCost,
  totalPotentialSavings,
  currency,
  summary,
  onImplement,
}: OptimizationRecommendationsProps) {
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get recommendation type icon
  const getRecommendationTypeIcon = (type: string) => {
    switch (type) {
      case "CONSOLIDATION":
        return <RefreshCw className="h-5 w-5" />;
      case "DOWNGRADE":
        return <ArrowDownIcon className="h-5 w-5" />;
      case "REMOVAL":
        return <Trash2 className="h-5 w-5" />;
      case "RENEGOTIATION":
        return <HandCoins className="h-5 w-5" />;
      case "USAGE_BASED":
        return <BarChart3 className="h-5 w-5" />;
      default:
        return <CheckCircle2 className="h-5 w-5" />;
    }
  };

  // Get priority badge variant
  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "destructive";
      case "MEDIUM":
        return "warning";
      case "LOW":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Sort recommendations by potential savings (highest first)
  const sortedRecommendations = [...recommendations].sort(
    (a, b) => b.potentialSavings - a.potentialSavings
  );

  // Calculate savings percentage
  const savingsPercentage =
    totalCurrentCost > 0 ? (totalPotentialSavings / totalCurrentCost) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle>License Optimization Summary</CardTitle>
          <CardDescription>
            Potential cost savings and optimization opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Cost</span>
                <span className="font-semibold">
                  {formatCurrency(totalCurrentCost)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Projected Cost</span>
                <span className="font-semibold">
                  {formatCurrency(totalProjectedCost)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Potential Savings</span>
                <span className="font-semibold text-green-600">
                  {formatCurrency(totalPotentialSavings)}
                </span>
              </div>
              <div className="pt-2">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs">Savings Percentage</span>
                  <span className="text-xs font-medium">
                    {savingsPercentage.toFixed(1)}%
                  </span>
                </div>
                <Progress value={savingsPercentage} className="h-2" />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">{summary}</div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Optimization Recommendations</h3>
        {sortedRecommendations.length > 0 ? (
          <Accordion type="single" collapsible className="space-y-4">
            {sortedRecommendations.map((recommendation) => (
              <AccordionItem
                key={recommendation.id}
                value={recommendation.id}
                className="border rounded-lg overflow-hidden"
              >
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      {getRecommendationTypeIcon(recommendation.type)}
                      <span className="font-medium">{recommendation.type}</span>
                      <Badge
                        variant={
                          getPriorityBadgeVariant(
                            recommendation.priority
                          ) as any
                        }
                      >
                        {recommendation.priority}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1 text-green-600">
                        <ArrowUpIcon className="h-4 w-4" />
                        <span>
                          {formatCurrency(recommendation.potentialSavings)}
                        </span>
                      </div>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <p>{recommendation.description}</p>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Current Cost</span>
                        <span>
                          {formatCurrency(recommendation.currentCost)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Projected Cost</span>
                        <span>
                          {formatCurrency(recommendation.projectedCost)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-green-600">
                        <span className="text-sm font-medium">
                          Potential Savings
                        </span>
                        <span className="font-medium">
                          {formatCurrency(recommendation.potentialSavings)}
                        </span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-2">
                        Implementation Steps
                      </h4>
                      <ul className="space-y-2">
                        {recommendation.implementationSteps.map(
                          (step, index) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm"
                            >
                              <ArrowRight className="h-4 w-4 mt-0.5 flex-shrink-0" />
                              <span>{step}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                    {onImplement && (
                      <Button
                        onClick={() => onImplement(recommendation)}
                        className="w-full"
                      >
                        Implement Recommendation
                      </Button>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <Card>
            <CardContent className="flex items-center justify-center p-6">
              <div className="flex flex-col items-center text-center">
                <AlertTriangle className="h-8 w-8 text-muted-foreground mb-2" />
                <p>No optimization recommendations available.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
