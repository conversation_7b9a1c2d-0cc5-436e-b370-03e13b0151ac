"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  BarChart3,
  <PERSON><PERSON>dingUp,
  HandCoins,
  Calendar,
  Users,
  Target,
  Award,
  AlertTriangle,
  CheckCircle,
  Download,
  Share,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Contract } from "@/services/contractService";
import { KeyDeviationsDisplay } from "./KeyDeviationsDisplay";

interface BenchmarkContractComparisonProps {
  contracts: Contract[];
  onBack: () => void;
}

interface BenchmarkMetric {
  name: string;
  icon: React.ReactNode;
  contracts: {
    id: string;
    value: number;
    displayValue: string;
    rank: number;
    color: string;
  }[];
  unit?: string;
  description: string;
}

export function BenchmarkContractComparison({
  contracts,
  onBack,
}: BenchmarkContractComparisonProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Calculate benchmark metrics
  const calculateBenchmarkMetrics = (): BenchmarkMetric[] => {
    // Contract Values
    const values = contracts.map((contract) => ({
      id: contract.id,
      value: parseFloat(contract.value?.replace(/[^0-9.-]+/g, "") || "0"),
      displayValue: contract.value || "N/A",
    }));
    const sortedValues = [...values].sort((a, b) => b.value - a.value);
    const valueMetric: BenchmarkMetric = {
      name: "Contract Value",
      icon: <HandCoins className="h-5 w-5" />,
      contracts: sortedValues.map((item, index) => ({
        ...item,
        rank: index + 1,
        color:
          index === 0
            ? "text-green-600"
            : index === 1
              ? "text-blue-600"
              : "text-gray-600",
      })),
      unit: "USD",
      description: "Total contract value comparison across selected contracts",
    };

    // Contract Duration (from LLM extraction only)
    const durations = contracts.map((contract) => {
      // Get LLM-extracted term from extraction data only
      const extraction = (contract as any).extraction;
      const extractedTerm = extraction?.fixedFields?.contract_term?.value;

      let displayValue = "N/A";
      let numericValue = 0;

      if (extractedTerm && typeof extractedTerm === "string") {
        const normalizedTerm = extractedTerm.trim();
        if (
          normalizedTerm !== "N/A" &&
          normalizedTerm !== "" &&
          normalizedTerm !== "Not specified"
        ) {
          displayValue = normalizedTerm;
          // Try to extract numeric value for sorting (rough approximation)
          const monthMatch = normalizedTerm.match(/(\d+)\s*months?/i);
          const yearMatch = normalizedTerm.match(/(\d+)\s*years?/i);
          const dayMatch = normalizedTerm.match(/(\d+)\s*days?/i);

          if (monthMatch) {
            numericValue = parseInt(monthMatch[1]) * 30; // Convert to days for sorting
          } else if (yearMatch) {
            numericValue = parseInt(yearMatch[1]) * 365; // Convert to days for sorting
          } else if (dayMatch) {
            numericValue = parseInt(dayMatch[1]);
          }
        }
      }

      return {
        id: contract.id,
        value: numericValue,
        displayValue: displayValue,
      };
    });
    const sortedDurations = [...durations].sort((a, b) => b.value - a.value);
    const durationMetric: BenchmarkMetric = {
      name: "Contract Duration",
      icon: <Calendar className="h-5 w-5" />,
      contracts: sortedDurations.map((item, index) => ({
        ...item,
        rank: index + 1,
        color:
          index === 0
            ? "text-green-600"
            : index === 1
              ? "text-blue-600"
              : "text-gray-600",
      })),
      unit: "days",
      description: "Contract duration comparison from start to end date",
    };

    // Performance Score (simulated based on status and other factors)
    const performanceScores = contracts.map((contract) => {
      let score = 50; // Base score
      if (contract.status === "Active") score += 30;
      if (contract.value) score += 10;
      if (contract.startDate && contract.endDate) score += 10;
      return {
        id: contract.id,
        value: score,
        displayValue: `${score}/100`,
      };
    });
    const sortedPerformance = [...performanceScores].sort(
      (a, b) => b.value - a.value
    );
    const performanceMetric: BenchmarkMetric = {
      name: "Performance Score",
      icon: <Target className="h-5 w-5" />,
      contracts: sortedPerformance.map((item, index) => ({
        ...item,
        rank: index + 1,
        color:
          index === 0
            ? "text-green-600"
            : index === 1
              ? "text-blue-600"
              : "text-gray-600",
      })),
      unit: "score",
      description:
        "Overall performance score based on contract completeness and status",
    };

    return [valueMetric, durationMetric, performanceMetric];
  };

  const benchmarkMetrics = calculateBenchmarkMetrics();

  // Get contract by ID
  const getContractById = (id: string) => {
    return contracts.find((c) => c.id === id);
  };

  // Calculate overall rankings
  const calculateOverallRankings = () => {
    const contractScores = contracts.map((contract) => {
      let totalScore = 0;
      let totalMetrics = 0;

      benchmarkMetrics.forEach((metric) => {
        const contractMetric = metric.contracts.find(
          (c) => c.id === contract.id
        );
        if (contractMetric) {
          // Invert rank to score (1st place = highest score)
          totalScore += contracts.length + 1 - contractMetric.rank;
          totalMetrics++;
        }
      });

      return {
        contract,
        averageScore: totalMetrics > 0 ? totalScore / totalMetrics : 0,
      };
    });

    return contractScores.sort((a, b) => b.averageScore - a.averageScore);
  };

  const overallRankings = calculateOverallRankings();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Selection
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Share className="mr-2 h-4 w-4" />
            Share Report
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Page Title */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
            <BarChart3 className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
              Benchmark Analysis
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Comparing {contracts.length} contracts across key performance
              metrics
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Contracts
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {contracts.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <Target className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Metrics
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {benchmarkMetrics.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <Award className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Top Performer
                  </p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white truncate">
                    {overallRankings[0]?.contract.title.substring(0, 15)}...
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Avg. Score
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Math.round(
                      overallRankings.reduce(
                        (sum, item) => sum + item.averageScore,
                        0
                      ) / overallRankings.length
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="metrics">Detailed Metrics</TabsTrigger>
          <TabsTrigger value="rankings">Rankings</TabsTrigger>
          <TabsTrigger value="deviations">Key Deviations T&C</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Contract Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contracts.map((contract, index) => (
              <Card key={contract.id} className="relative">
                <div className="absolute top-3 right-3">
                  <Badge variant="outline" className="text-xs">
                    #{index + 1}
                  </Badge>
                </div>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between pr-8">
                    <Badge
                      variant={
                        contract.status === "Active" ? "default" : "secondary"
                      }
                      className={
                        contract.status === "Active"
                          ? "bg-green-500 hover:bg-green-600"
                          : ""
                      }
                    >
                      {contract.status}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg line-clamp-2">
                    {contract.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Counterparty:
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {contract.counterparty || "N/A"}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <HandCoins className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Value:
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {contract.value || "N/A"}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      Start Date:
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {contract.startDate
                        ? new Date(contract.startDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Metrics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {benchmarkMetrics.map((metric, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {metric.icon}
                    {metric.name}
                  </CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {metric.description}
                  </p>
                </CardHeader>
                <CardContent className="space-y-3">
                  {metric.contracts.slice(0, 3).map((contractMetric, idx) => {
                    const contract = getContractById(contractMetric.id);
                    return (
                      <div
                        key={contractMetric.id}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className={`w-2 h-2 rounded-full ${idx === 0
                              ? "bg-green-500"
                              : idx === 1
                                ? "bg-blue-500"
                                : "bg-gray-400"
                              }`}
                          />
                          <span className="text-sm font-medium text-gray-900 dark:text-white truncate max-w-[120px]">
                            {contract?.title}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span
                            className={`text-sm font-bold ${contractMetric.color}`}
                          >
                            {contractMetric.displayValue}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            #{contractMetric.rank}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Detailed Metrics Tab */}
        <TabsContent value="metrics" className="space-y-6">
          {benchmarkMetrics.map((metric, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {metric.icon}
                  {metric.name}
                </CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {metric.description}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metric.contracts.map((contractMetric) => {
                    const contract = getContractById(contractMetric.id);
                    const maxValue = Math.max(
                      ...metric.contracts.map((c) => c.value)
                    );
                    const percentage =
                      maxValue > 0
                        ? (contractMetric.value / maxValue) * 100
                        : 0;

                    return (
                      <div key={contractMetric.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Badge variant="outline" className="text-xs">
                              #{contractMetric.rank}
                            </Badge>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {contract?.title}
                            </span>
                          </div>
                          <span className={`font-bold ${contractMetric.color}`}>
                            {contractMetric.displayValue}
                          </span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Rankings Tab */}
        <TabsContent value="rankings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-500" />
                Overall Performance Rankings
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Contracts ranked by overall performance across all metrics
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {overallRankings.map((ranking, index) => (
                  <div
                    key={ranking.contract.id}
                    className={`flex items-center justify-between p-4 rounded-lg border ${index === 0
                      ? "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20"
                      : index === 1
                        ? "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50"
                        : index === 2
                          ? "border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20"
                          : "border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
                      }`}
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-sm ${index === 0
                          ? "bg-yellow-500 text-white"
                          : index === 1
                            ? "bg-gray-400 text-white"
                            : index === 2
                              ? "bg-orange-500 text-white"
                              : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                          }`}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {ranking.contract.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {ranking.contract.counterparty || "No counterparty"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="font-bold text-gray-900 dark:text-white">
                          {Math.round(ranking.averageScore * 10) / 10}
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Avg. Score
                        </p>
                      </div>
                      {index === 0 && (
                        <Award className="h-6 w-6 text-yellow-500" />
                      )}
                      {index === 1 && (
                        <div className="w-6 h-6 rounded-full bg-gray-400" />
                      )}
                      {index === 2 && (
                        <div className="w-6 h-6 rounded-full bg-orange-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance Insights */}
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-primary rounded-lg">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Key Insights
                  </h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Top performing contract:{" "}
                        {overallRankings[0]?.contract.title}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {contracts.filter((c) => c.status !== "Active").length}{" "}
                        contracts need attention
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Average performance score:{" "}
                        {Math.round(
                          (overallRankings.reduce(
                            (sum, item) => sum + item.averageScore,
                            0
                          ) /
                            overallRankings.length) *
                          10
                        ) / 10}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Key Deviations T&C Tab */}
        <TabsContent value="deviations" className="space-y-6">
          <KeyDeviationsDisplay contracts={contracts} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
