"use client";

import React from "react";
import { Card, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  FileText,
  Scale,
  Globe,
  Users,
  Lock,
  Gavel,
  HelpCircle
} from "lucide-react";
import { Contract } from "@/services/contractService";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface KeyDeviationsDisplayProps {
  contracts: Contract[];
}

// Define the key deviation fields we want to display
const KEY_DEVIATION_FIELDS = [
  {
    key: "Liability cap",
    label: "Liability Cap",
    icon: <Shield className="h-4 w-4" />,
    description: "Limitation of liability provisions and caps"
  },
  {
    key: "Exclusions to LoL",
    label: "Exclusions to LoL",
    icon: <AlertTriangle className="h-4 w-4" />,
    description: "Exclusions to limitation of liability"
  },
  {
    key: "Termination for convenience",
    label: "Termination for Convenience",
    icon: <XCircle className="h-4 w-4" />,
    description: "Right to terminate contract without cause"
  },
  {
    key: "Notice for termination for convenience",
    label: "Notice for Termination",
    icon: <FileText className="h-4 w-4" />,
    description: "Required notice period for convenience termination"
  },
  {
    key: "Governing law",
    label: "Governing Law",
    icon: <Gavel className="h-4 w-4" />,
    description: "Legal jurisdiction governing the contract"
  },
  {
    key: "Territorial scope",
    label: "Territorial Scope",
    icon: <Globe className="h-4 w-4" />,
    description: "Geographical restrictions on service usage"
  },
  {
    key: "Third-party usage",
    label: "Third-party Usage",
    icon: <Users className="h-4 w-4" />,
    description: "Rights for third-party or affiliate usage"
  },
  {
    key: "Intellectual property rights (IPR)",
    label: "Intellectual Property Rights",
    icon: <Lock className="h-4 w-4" />,
    description: "Intellectual property ownership and licensing terms"
  },
  {
    key: "Data-protection / DPA",
    label: "Data Protection / DPA",
    icon: <Shield className="h-4 w-4" />,
    description: "Data protection and privacy agreement terms"
  },
  {
    key: "Warranty / disclaimer",
    label: "Warranty / Disclaimer",
    icon: <CheckCircle className="h-4 w-4" />,
    description: "Warranty provisions and disclaimers"
  },
  {
    key: "Support terms / SLA",
    label: "Support Terms / SLA",
    icon: <HelpCircle className="h-4 w-4" />,
    description: "Support service level agreements and terms"
  }
];

export function KeyDeviationsDisplay({ contracts }: KeyDeviationsDisplayProps) {
  // Helper function to get field value from contract's special fields
  const getFieldValue = (contract: Contract, fieldKey: string): string => {
    if (!contract.extraction?.specialFields) return "N/A";

    // Look through all supplier categories in special fields
    for (const supplierKey in contract.extraction.specialFields) {
      const supplierData = contract.extraction.specialFields[supplierKey];
      if (supplierData && typeof supplierData === 'object') {
        // Look through all categories for this supplier
        for (const categoryKey in supplierData) {
          const categoryData = supplierData[categoryKey as keyof typeof supplierData];
          if (categoryData && typeof categoryData === 'object') {
            // Look for the field in this category
            const fieldData = (categoryData as any)[fieldKey];
            if (fieldData) {
              return fieldData.value || fieldData || "N/A";
            }
          }
        }
      }
    }

    return "N/A";
  };

  // Helper function to get contract name
  const getContractName = (contract: Contract): string => {
    return contract.title || contract.extraction?.fixedFields?.original_filename?.value || `Contract ${contract.id.slice(0, 8)}`;
  };



  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Scale className="h-5 w-5" style={{ color: "#09260D" }} />
          Key Deviations in Terms & Conditions
        </CardTitle>
        <CardDescription>
          Compare critical contract terms and conditions across selected contracts to identify deviations and standardization opportunities.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium text-gray-900 dark:text-white w-[200px] bg-gray-50 dark:bg-gray-800">
                  Field
                </th>
                {contracts.map((contract) => {
                  // Calculate equal width for contract columns based on number of contracts
                  const getContractWidth = () => {
                    switch (contracts.length) {
                      case 2: return "w-[400px]";
                      case 3: return "w-[266px]";
                      case 4: return "w-[200px]";
                      default: return "w-[200px]";
                    }
                  };

                  return (
                    <th key={contract.id} className={`text-left p-3 font-medium text-gray-900 dark:text-white ${getContractWidth()} bg-gray-50 dark:bg-gray-800`}>
                      {getContractName(contract)}
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody>
              {KEY_DEVIATION_FIELDS.map((field) => (
                <tr key={field.key} className="border-b">
                  <td className="p-3 align-top">
                    <div className="flex items-center gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-sm bg-primary text-primary-foreground">
                            <div className="text-xs whitespace-pre-wrap break-words">
                              {field.description}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <span className="font-medium text-sm">{field.label}</span>
                    </div>
                  </td>
                  {contracts.map((contract) => {
                    const value = getFieldValue(contract, field.key);
                    const isNA = value === "N/A" || value === "" || value === "—";

                    return (
                      <td key={contract.id} className="p-3 align-top">
                        <div className={`text-sm whitespace-normal break-words ${isNA ? 'text-gray-500 italic' : 'text-gray-900 dark:text-white'}`}>
                          {isNA ? "N/A" : value}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
