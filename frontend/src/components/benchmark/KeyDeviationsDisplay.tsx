"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  FileText,
  Scale,
  Globe,
  Users,
  Lock,
  Gavel,
  HelpCircle
} from "lucide-react";
import { Contract } from "@/services/contractService";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface KeyDeviationsDisplayProps {
  contracts: Contract[];
}

// Define the key deviation fields we want to display
const KEY_DEVIATION_FIELDS = [
  {
    key: "Liability cap",
    label: "Liability Cap",
    icon: <Shield className="h-4 w-4" />,
    description: "Limitation of liability provisions and caps"
  },
  {
    key: "Exclusions to LoL",
    label: "Exclusions to LoL",
    icon: <AlertTriangle className="h-4 w-4" />,
    description: "Exclusions to limitation of liability"
  },
  {
    key: "Termination for convenience",
    label: "Termination for Convenience",
    icon: <XCircle className="h-4 w-4" />,
    description: "Right to terminate contract without cause"
  },
  {
    key: "Notice for termination for convenience",
    label: "Notice for Termination",
    icon: <FileText className="h-4 w-4" />,
    description: "Required notice period for convenience termination"
  },
  {
    key: "Governing law",
    label: "Governing Law",
    icon: <Gavel className="h-4 w-4" />,
    description: "Legal jurisdiction governing the contract"
  },
  {
    key: "Territorial scope",
    label: "Territorial Scope",
    icon: <Globe className="h-4 w-4" />,
    description: "Geographical restrictions on service usage"
  },
  {
    key: "Third-party usage",
    label: "Third-party Usage",
    icon: <Users className="h-4 w-4" />,
    description: "Rights for third-party or affiliate usage"
  },
  {
    key: "Intellectual property rights (IPR)",
    label: "Intellectual Property Rights",
    icon: <Lock className="h-4 w-4" />,
    description: "Intellectual property ownership and licensing terms"
  },
  {
    key: "Data-protection / DPA",
    label: "Data Protection / DPA",
    icon: <Shield className="h-4 w-4" />,
    description: "Data protection and privacy agreement terms"
  },
  {
    key: "Warranty / disclaimer",
    label: "Warranty / Disclaimer",
    icon: <CheckCircle className="h-4 w-4" />,
    description: "Warranty provisions and disclaimers"
  },
  {
    key: "Support terms / SLA",
    label: "Support Terms / SLA",
    icon: <HelpCircle className="h-4 w-4" />,
    description: "Support service level agreements and terms"
  }
];

export function KeyDeviationsDisplay({ contracts }: KeyDeviationsDisplayProps) {
  // Helper function to get field value from contract's special fields
  const getFieldValue = (contract: Contract, fieldKey: string): string => {
    if (!contract.extraction?.specialFields) return "N/A";

    // Look through all supplier categories in special fields
    for (const supplierKey in contract.extraction.specialFields) {
      const supplierData = contract.extraction.specialFields[supplierKey];
      if (supplierData && typeof supplierData === 'object') {
        // Look through all categories for this supplier
        for (const categoryKey in supplierData) {
          const categoryData = supplierData[categoryKey as keyof typeof supplierData];
          if (categoryData && typeof categoryData === 'object') {
            // Look for the field in this category
            const fieldData = (categoryData as any)[fieldKey];
            if (fieldData) {
              return fieldData.value || fieldData || "N/A";
            }
          }
        }
      }
    }

    return "N/A";
  };

  // Helper function to get contract name
  const getContractName = (contract: Contract): string => {
    return contract.title || contract.extraction?.fixedFields?.original_filename?.value || `Contract ${contract.id.slice(0, 8)}`;
  };

  // Helper function to get supplier name
  const getSupplierName = (contract: Contract): string => {
    return contract.provider || contract.counterparty || "Unknown Supplier";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scale className="h-5 w-5 text-orange-600" />
            Key Deviations in Terms & Conditions
          </CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Compare critical contract terms and conditions across selected contracts to identify deviations and standardization opportunities.
          </p>
        </CardHeader>
      </Card>

      {/* Deviations Comparison Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50 dark:bg-gray-800">
                  <th className="text-left p-4 font-medium text-gray-900 dark:text-white min-w-[200px]">
                    Terms & Conditions
                  </th>
                  {contracts.map((contract) => (
                    <th key={contract.id} className="text-left p-4 font-medium text-gray-900 dark:text-white min-w-[200px]">
                      <div className="space-y-1">
                        <div className="font-medium">{getContractName(contract)}</div>
                        <div className="text-xs text-gray-500">{getSupplierName(contract)}</div>
                        <Badge variant="outline" className="text-xs">
                          {contract.status || "Unknown"}
                        </Badge>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {KEY_DEVIATION_FIELDS.map((field, index) => (
                  <tr key={field.key} className={`border-b ${index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}`}>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <Info className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-sm bg-primary text-primary-foreground">
                              <div className="text-xs whitespace-pre-wrap break-words">
                                {field.description}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {field.icon}
                        <span className="font-medium">{field.label}</span>
                      </div>
                    </td>
                    {contracts.map((contract) => {
                      const value = getFieldValue(contract, field.key);
                      const isNA = value === "N/A" || value === "" || value === "—";

                      return (
                        <td key={contract.id} className="p-4">
                          <div className="space-y-1">
                            <div className={`text-sm ${isNA ? 'text-gray-500 italic' : 'text-gray-900 dark:text-white'}`}>
                              {value}
                            </div>
                            {!isNA && (
                              <Badge
                                variant="outline"
                                className="text-xs bg-green-50 border-green-200 text-green-700"
                              >
                                Found
                              </Badge>
                            )}
                            {isNA && (
                              <Badge
                                variant="outline"
                                className="text-xs bg-red-50 border-red-200 text-red-700"
                              >
                                Missing
                              </Badge>
                            )}
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {Math.round((contracts.reduce((acc, contract) => {
                    const foundFields = KEY_DEVIATION_FIELDS.filter(field => {
                      const value = getFieldValue(contract, field.key);
                      return value !== "N/A" && value !== "" && value !== "—";
                    }).length;
                    return acc + (foundFields / KEY_DEVIATION_FIELDS.length);
                  }, 0) / contracts.length) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Average Coverage</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {KEY_DEVIATION_FIELDS.filter(field => {
                    const values = contracts.map(contract => getFieldValue(contract, field.key));
                    const filteredValues = values.filter(v => v !== "N/A" && v !== "" && v !== "—");
                    const uniqueValues = Array.from(new Set(filteredValues));
                    return uniqueValues.length > 1;
                  }).length}
                </div>
                <div className="text-sm text-gray-600">Deviating Terms</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {KEY_DEVIATION_FIELDS.filter(field => {
                    const values = contracts.map(contract => getFieldValue(contract, field.key));
                    return values.every(v => v === "N/A" || v === "" || v === "—");
                  }).length}
                </div>
                <div className="text-sm text-gray-600">Missing Across All</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
