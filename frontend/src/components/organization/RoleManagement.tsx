"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { UserService, Role } from "@/services/user-service";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pencil, Trash2, Plus, AlertCircle, Check, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DeleteConfirmationModal } from "@/components/ui/delete-confirmation-modal";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

// Define the role form schema
const roleFormSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  description: z.string().optional(),
  permissions: z.array(z.string()),
  isDefault: z.boolean(),
});

type RoleFormValues = z.infer<typeof roleFormSchema>;

// Role interface is imported from user-service.ts

// Available permissions in the system
const availablePermissions = [
  { id: "contracts:read", label: "View Contracts" },
  { id: "contracts:write", label: "Create/Edit Contracts" },
  { id: "contracts:approve", label: "Approve Contracts" },
  { id: "licenses:read", label: "View Licenses" },
  { id: "licenses:write", label: "Create/Edit Licenses" },
  { id: "licenses:approve", label: "Approve Licenses" },
  { id: "users:read", label: "View Users" },
  { id: "users:write", label: "Manage Users" },
  { id: "settings:read", label: "View Settings" },
  { id: "settings:write", label: "Manage Settings" },
];

export function RoleManagement() {
  const { user } = useAuth();
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Create form
  const form = useForm<RoleFormValues>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: "",
      description: "",
      permissions: [],
      isDefault: false,
    },
  });

  // Edit form
  const editForm = useForm<RoleFormValues>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: "",
      description: "",
      permissions: [],
      isDefault: false,
    },
  });

  // Function to fetch roles
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const rolesData = await UserService.getRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast.error("Failed to load roles");
    } finally {
      setLoading(false);
    }
  };

  // Load roles
  useEffect(() => {
    fetchRoles();
  }, []);

  // Handle form submission for adding a new role
  const onSubmit = async (data: RoleFormValues) => {
    try {
      await UserService.createRole(data);
      toast.success("Role created successfully");

      // Recall the roles API to refresh the list
      await fetchRoles();

      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error creating role:", error);
      toast.error("Failed to create role");
    }
  };

  // Handle form submission for editing a role
  const onEditSubmit = async (data: RoleFormValues) => {
    if (!selectedRole) return;

    try {
      await UserService.updateRole(selectedRole.id, data);
      toast.success("Role updated successfully");

      // Recall the roles API to refresh the list
      await fetchRoles();

      setIsEditDialogOpen(false);
      editForm.reset();
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error("Failed to update role");
    }
  };

  // Handle role deletion
  const handleDeleteRole = async () => {
    if (!selectedRole) return;

    setIsDeleting(true);
    try {
      await UserService.deleteRole(selectedRole.id);
      toast.success("Role deleted successfully");

      // Recall the roles API to refresh the list
      await fetchRoles();

      setIsDeleteDialogOpen(false);
      setSelectedRole(null);
    } catch (error: any) {
      console.error("Error deleting role:", error);
      if (error.response?.data?.usersCount) {
        toast.error(
          `Cannot delete role: It is assigned to ${error.response.data.usersCount} users`
        );
      } else {
        toast.error("Failed to delete role");
      }
    } finally {
      setIsDeleting(false);
    }
  };

  // Open edit dialog and populate form
  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    editForm.reset({
      name: role.name,
      description: role.description || "",
      permissions: role.permissions,
      isDefault: role.isDefault,
    });
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteDialog = (role: Role) => {
    setSelectedRole(role);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Role Management</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Role
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Role</DialogTitle>
              <DialogDescription>
                Create a new role with specific permissions.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Legal Admin" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the role's purpose"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="isDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Default Role</FormLabel>
                        <FormDescription>
                          Make this the default role for new users
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                <div>
                  <FormLabel>Permissions</FormLabel>
                  <div className="mt-2 space-y-2">
                    {availablePermissions.map((permission) => (
                      <FormField
                        key={permission.id}
                        control={form.control}
                        name="permissions"
                        render={({ field }) => (
                          <FormItem
                            key={permission.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(permission.id)}
                                onCheckedChange={(checked) => {
                                  const updatedPermissions = checked
                                    ? [...field.value, permission.id]
                                    : field.value?.filter(
                                        (value) => value !== permission.id
                                      );
                                  field.onChange(updatedPermissions);
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {permission.label}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">Create Role</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p>Loading roles...</p>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Organization Roles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <div className="grid grid-cols-[1fr_2fr_auto_auto_auto] gap-4 p-4 font-medium border-b">
                <div>Name</div>
                <div>Description</div>
                <div>Default</div>
                <div>Permissions</div>
                <div className="text-right">Actions</div>
              </div>
              {roles.length === 0 ? (
                <div className="p-4 text-center">
                  <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p>No roles found</p>
                </div>
              ) : (
                roles.map((role) => (
                  <div
                    key={role.id}
                    className="grid grid-cols-[1fr_2fr_auto_auto_auto] gap-4 p-4 items-center border-b last:border-0"
                  >
                    <div className="font-medium">{role.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {role.description || "No description"}
                    </div>
                    <div className="text-center">
                      {role.isDefault ? (
                        <Check className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-muted-foreground mx-auto" />
                      )}
                    </div>
                    <div>
                      <Badge variant="outline">
                        {role.permissions.length} permissions
                      </Badge>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditRole(role)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteDialog(role)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
            <DialogDescription>
              Update role information and permissions.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form
              onSubmit={editForm.handleSubmit(onEditSubmit)}
              className="space-y-4"
            >
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Legal Admin" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the role's purpose"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Default Role</FormLabel>
                      <FormDescription>
                        Make this the default role for new users
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              <div>
                <FormLabel>Permissions</FormLabel>
                <div className="mt-2 space-y-2">
                  {availablePermissions.map((permission) => (
                    <FormField
                      key={permission.id}
                      control={editForm.control}
                      name="permissions"
                      render={({ field }) => (
                        <FormItem
                          key={permission.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(permission.id)}
                              onCheckedChange={(checked) => {
                                const updatedPermissions = checked
                                  ? [...field.value, permission.id]
                                  : field.value?.filter(
                                      (value) => value !== permission.id
                                    );
                                field.onChange(updatedPermissions);
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {permission.label}
                          </FormLabel>
                        </FormItem>
                      )}
                    />
                  ))}
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Update Role</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteRole}
        title="Delete Role"
        description={`Are you sure you want to delete the role "${selectedRole?.name}"? This action cannot be undone and will permanently remove the role from your organization.`}
        itemName={selectedRole?.name}
        isLoading={isDeleting}
      />
    </div>
  );
}
