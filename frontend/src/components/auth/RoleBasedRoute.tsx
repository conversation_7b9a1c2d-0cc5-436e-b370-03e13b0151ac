/**
 * Role-Based Route Protection Component
 * Prevents super admin from accessing normal user routes and vice versa
 */

import { useAuth } from "@/contexts/AuthContext";

interface RoleBasedRouteProps {
  children: React.ReactNode;
  allowedRoles?: ('USER' | 'ADMIN' | 'SUPER_ADMIN')[];
}

/**
 * Component that protects routes based on user roles
 * Prevents cross-role access between normal users and super admin
 */
export const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  allowedRoles = ['USER', 'ADMIN'],
}) => {
  const { user, isLoading, isAuthenticated } = useAuth();

  // Let middleware handle redirects to avoid conflicts with AuthContext
  // This component now only handles rendering logic

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show nothing while redirecting or if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  // Check role access
  const userRole = user.role as 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  const isRoleAllowed = allowedRoles.includes(userRole);

  if (!isRoleAllowed) {
    return null;
  }

  // Render children if role is allowed
  return <>{children}</>;
};

/**
 * Higher-order component for normal user routes
 * Prevents super admin access
 */
export const NormalUserRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <RoleBasedRoute allowedRoles={['USER', 'ADMIN']}>
      {children}
    </RoleBasedRoute>
  );
};

/**
 * Higher-order component for super admin routes
 * Prevents normal user access
 */
export const SuperAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <RoleBasedRoute allowedRoles={['SUPER_ADMIN']}>
      {children}
    </RoleBasedRoute>
  );
};
