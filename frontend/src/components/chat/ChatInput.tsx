/**
 * Chat Input Component
 * Provides an input field for sending messages
 */

import React, { useState, useRef, useEffect } from "react";
import { SendIcon } from "lucide-react";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || disabled) return;

    onSendMessage(message);
    setMessage("");

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    // Submit on Enter (without Shift)
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="relative flex items-center">
        <textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full p-3 pr-12 border border-border rounded-lg resize-none max-h-32 focus:outline-none focus:ring-2 focus:ring-ring bg-background"
          rows={1}
        />
        <button
          type="submit"
          disabled={!message.trim() || disabled}
          className="absolute right-3 p-1 rounded-full text-primary hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <SendIcon size={20} />
        </button>
      </div>

      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-right">
        Press Enter to send, Shift+Enter for new line
      </div>
    </form>
  );
}
