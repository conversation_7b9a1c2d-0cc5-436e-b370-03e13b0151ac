/**
 * Chat Interface Component
 * Provides a user interface for interacting with the RAG system
 */

import React, { useState, useEffect, useRef } from "react";
import { <PERSON> } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { apiClient } from "@/lib/api-client";
import { ChatMessage } from "./ChatMessage";
import { ChatInput } from "./ChatInput";
import { ChatSidebar } from "./ChatSidebar";
import { Spinner } from "../ui/Spinner";

// Message interface
interface Message {
  id?: string;
  conversationId: string;
  role: "system" | "user" | "assistant";
  content: string;
  metadata?: Record<string, any>;
  createdAt?: string;
}

// Conversation interface
interface Conversation {
  id: string;
  tenantId: string;
  userId: string;
  title?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface ChatInterfaceProps {
  documentId?: string;
  documentType?: "contract" | "license";
  initialPrompt?: string;
}

export function ChatInterface({
  documentId,
  documentType,
  initialPrompt,
}: ChatInterfaceProps) {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] =
    useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch user conversations on mount
  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize without auto-creating conversations
  useEffect(() => {
    if (!initializing) return;

    if (conversations.length > 0) {
      setCurrentConversation(conversations[0]);
      fetchMessages(conversations[0].id);
    }

    setInitializing(false);
  }, [conversations, initializing]);

  // Send initial prompt if provided
  useEffect(() => {
    if (initialPrompt && currentConversation && messages.length === 0) {
      sendMessage(initialPrompt);
    }
  }, [initialPrompt, currentConversation, messages]);

  // Fetch user conversations
  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get("/api/chat/conversations");
      setConversations(response);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching conversations:", error);
      setLoading(false);
    }
  };

  // Fetch messages for a conversation
  const fetchMessages = async (conversationId: string) => {
    try {
      setLoading(true);
      const response = await apiClient.get(
        `/api/chat/conversations/${conversationId}/messages`
      );
      setMessages(response);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching messages:", error);
      setLoading(false);
    }
  };

  // Create a new conversation with meaningful title
  const createNewConversation = async (firstMessage?: string) => {
    try {
      setLoading(true);

      // Generate meaningful title from first message or context
      let title = "New Conversation";
      if (firstMessage) {
        // Take first 4-6 words of the message as title
        const words = firstMessage.trim().split(" ").slice(0, 5);
        title = words.join(" ");
        if (firstMessage.length > title.length) {
          title += "...";
        }
      } else if (documentId) {
        title = `${documentType?.charAt(0).toUpperCase()}${
          documentType?.slice(1) || ""
        } ${documentId}`;
      }

      const response = await apiClient.post("/api/chat/conversations", {
        title,
      });
      setConversations((prev) => [response, ...prev]);
      setCurrentConversation(response);
      setMessages([]);
      setLoading(false);
      return response;
    } catch (error) {
      console.error("Error creating conversation:", error);
      setLoading(false);
      return null;
    }
  };

  // Send a message
  const sendMessage = async (content: string) => {
    // Create conversation if none exists
    let conversation = currentConversation;
    if (!conversation) {
      conversation = await createNewConversation(content);
      if (!conversation) return;
    }

    try {
      // Add user message to UI immediately
      const userMessage: Message = {
        conversationId: conversation.id,
        role: "user",
        content,
      };

      setMessages((prev) => [...prev, userMessage]);

      // Set options based on document context
      const options = documentId
        ? {
            ragOptions: {
              documentId,
              metadata: {
                documentType,
              },
            },
          }
        : undefined;

      // Send message to API
      setLoading(true);
      const response = await apiClient.post(
        `/api/chat/conversations/${conversation.id}/messages`,
        { message: content, options }
      );

      // Add assistant response to UI
      setMessages((prev) => [
        ...prev.filter((m) => m.id || m.role !== "user"),
        response,
      ]);
      setLoading(false);
    } catch (error) {
      console.error("Error sending message:", error);
      setLoading(false);
    }
  };

  // Select a conversation
  const selectConversation = (conversation: Conversation) => {
    setCurrentConversation(conversation);
    fetchMessages(conversation.id);
  };

  // Delete a conversation
  const deleteConversation = async (conversationId: string) => {
    try {
      await apiClient.delete(`/api/chat/conversations/${conversationId}`);
      setConversations((prev) => prev.filter((c) => c.id !== conversationId));

      if (currentConversation?.id === conversationId) {
        if (conversations.length > 1) {
          const nextConversation = conversations.find(
            (c) => c.id !== conversationId
          );
          if (nextConversation) {
            setCurrentConversation(nextConversation);
            fetchMessages(nextConversation.id);
          }
        } else {
          // Don't auto-create, just clear current conversation
          setCurrentConversation(null);
          setMessages([]);
        }
      }
    } catch (error) {
      console.error("Error deleting conversation:", error);
    }
  };

  // Rename a conversation
  const renameConversation = async (conversationId: string, title: string) => {
    try {
      const response = await apiClient.put(
        `/api/chat/conversations/${conversationId}`,
        { title }
      );
      setConversations((prev) =>
        prev.map((c) => (c.id === conversationId ? response : c))
      );

      if (currentConversation?.id === conversationId) {
        setCurrentConversation(response);
      }
    } catch (error) {
      console.error("Error renaming conversation:", error);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <ChatSidebar
        conversations={conversations}
        currentConversation={currentConversation}
        onSelectConversation={selectConversation}
        onDeleteConversation={deleteConversation}
        onRenameConversation={renameConversation}
      />

      {/* Main chat area */}
      <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900">
        {/* Chat header */}
        <div className="p-4 border-b dark:border-gray-700">
          <h2 className="text-lg font-semibold">
            {currentConversation?.title || "New Conversation"}
          </h2>
          {documentId && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {documentType?.charAt(0).toUpperCase()}
              {documentType?.slice(1) || ""} ID: {documentId}
            </p>
          )}
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4">
          {!currentConversation ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center max-w-md">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Welcome to Your Contract Assistant
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  I'm here to help you understand your contracts and make
                  informed decisions. Ask me about contract terms, risks,
                  obligations, or any questions about your agreements.
                </p>
                <div className="text-sm text-gray-500 dark:text-gray-500">
                  💡 Tip: I can help you understand your contracts, explain
                  terms, and answer questions about your agreements. Your
                  conversations are automatically saved for your reference.
                </div>
              </div>
            </div>
          ) : (
            <>
              {messages.map((message, index) => (
                <ChatMessage key={message.id || index} message={message} />
              ))}

              {loading && (
                <div className="flex justify-center my-4">
                  <Spinner size="md" />
                </div>
              )}

              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Input */}
        <div className="p-4 border-t border-border">
          <ChatInput
            onSendMessage={sendMessage}
            disabled={loading}
            placeholder={
              !currentConversation
                ? "Ask me about your contracts..."
                : "What would you like to know about your contracts?"
            }
          />
        </div>
      </div>
    </div>
  );
}
