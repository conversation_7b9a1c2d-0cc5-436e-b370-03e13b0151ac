/**
 * Enhanced Chat Input Component
 * Provides chat input with contract tagging support (@-mention functionality)
 */

import React, { useState, useRef, useEffect } from "react";
import { SendIcon, X } from "lucide-react";
import { usePathname } from "next/navigation";
import { ContractAutocomplete } from "./ContractAutocomplete";
import { contractService } from "@/services/contractService";

interface Contract {
  id: string;
  title: string;
  contractNumber?: string;
  counterparty?: string;
}

interface TaggedContract extends Contract {
  displayName: string;
}

interface EnhancedChatInputProps {
  onSendMessage: (message: string, taggedContracts?: string[]) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function EnhancedChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Ask me about your contracts... Use @ to tag specific contracts",
}: EnhancedChatInputProps) {
  const pathname = usePathname();

  // Dynamic placeholder based on current page
  const getPlaceholder = () => {
    const contractViewMatch = pathname.match(
      /\/contract-management\/contracts\/analysis\/([^\/]+)/
    );
    const discoveryAnalysisMatch = pathname.match(
      /\/contract-concierge\/discovery\/([^\/]+)/
    );

    if (contractViewMatch || discoveryAnalysisMatch) {
      return "Ask me about this contract... (automatically tagged)";
    }
    return placeholder;
  };
  const [message, setMessage] = useState("");
  const [taggedContracts, setTaggedContracts] = useState<TaggedContract[]>([]);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [autocompleteSearch, setAutocompleteSearch] = useState("");
  const [autocompletePosition, setAutocompletePosition] = useState({
    top: 0,
    left: 0,
  });
  const [cursorPosition, setCursorPosition] = useState(0);
  const [autoTaggedContract, setAutoTaggedContract] =
    useState<TaggedContract | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Automatic contract tagging based on current page
  useEffect(() => {
    const detectAndTagContract = async () => {
      let contractId: string | null = null;

      // Check if we're on a contract view page
      const contractViewMatch = pathname.match(
        /\/contract-management\/contracts\/analysis\/([^\/]+)/
      );

      // Check if we're on a discovery analysis page
      const discoveryAnalysisMatch = pathname.match(
        /\/contract-concierge\/discovery\/([^\/]+)/
      );

      if (contractViewMatch) {
        contractId = contractViewMatch[1];
      } else if (discoveryAnalysisMatch) {
        contractId = discoveryAnalysisMatch[1];
      }

      if (contractId) {
        // Check if this contract is already tagged (manually or auto-tagged)
        const isAlreadyTagged =
          taggedContracts.some((tc) => tc.id === contractId) ||
          autoTaggedContract?.id === contractId;

        if (!isAlreadyTagged) {
          try {
            // Fetch contract details
            const contract = await contractService.getContract(contractId);

            const displayName = contract.contractNumber
              ? `${contract.title} (#${contract.contractNumber})`
              : contract.title;

            const taggedContract: TaggedContract = {
              id: contract.id,
              title: contract.title,
              contractNumber: contract.contractNumber,
              counterparty: contract.counterparty,
              displayName,
            };

            // Set as auto-tagged contract
            setAutoTaggedContract(taggedContract);

            console.log(
              "Auto-tagged contract:",
              taggedContract,
              "from",
              contractViewMatch ? "contract view URL" : "discovery analysis URL"
            );
          } catch (error) {
            console.error("Failed to auto-tag contract:", error);
          }
        }
      } else {
        // Clear auto-tagged contract if not on a relevant page
        if (autoTaggedContract) {
          setAutoTaggedContract(null);
        }
      }
    };

    detectAndTagContract();
  }, [pathname, taggedContracts, autoTaggedContract]);

  // Listen for contract tagging events from the link icon
  useEffect(() => {
    const handleTagContract = (event: CustomEvent) => {
      const contractData = event.detail;

      // Check if contract is already tagged
      const isAlreadyTagged = taggedContracts.some(
        (tc) => tc.id === contractData.id
      );

      if (!isAlreadyTagged) {
        const displayName = contractData.contractNumber
          ? `${contractData.title} (#${contractData.contractNumber})`
          : contractData.title;

        const taggedContract: TaggedContract = {
          id: contractData.id,
          title: contractData.title,
          contractNumber: contractData.contractNumber,
          counterparty: contractData.counterparty,
          displayName,
        };

        // Add to manually tagged contracts
        setTaggedContracts((prev) => [...prev, taggedContract]);

        console.log("Contract tagged from link icon:", taggedContract);
      }
    };

    window.addEventListener("tagContract", handleTagContract as EventListener);

    return () => {
      window.removeEventListener(
        "tagContract",
        handleTagContract as EventListener
      );
    };
  }, [taggedContracts]);

  // Handle input changes and detect @ mentions
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;

    setMessage(value);
    setCursorPosition(cursorPos);

    // Check for @ mention
    const textBeforeCursor = value.substring(0, cursorPos);
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/);

    if (atMatch) {
      const searchTerm = atMatch[1];
      setAutocompleteSearch(searchTerm);

      // Position the dropdown just above the input field
      setAutocompletePosition({
        top: -10, // Just above the input (will be adjusted by transform)
        left: 0,
      });

      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
      setAutocompleteSearch("");
    }
  };

  // Handle contract selection from autocomplete
  const handleContractSelect = (contract: Contract) => {
    const displayName = contract.contractNumber
      ? `${contract.title} (#${contract.contractNumber})`
      : contract.title;

    // Add to tagged contracts if not already tagged
    if (!taggedContracts.find((tc) => tc.id === contract.id)) {
      setTaggedContracts((prev) => [...prev, { ...contract, displayName }]);
    }

    // Replace the @ mention in the message
    const textBeforeCursor = message.substring(0, cursorPosition);
    const textAfterCursor = message.substring(cursorPosition);
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/);

    if (atMatch) {
      const beforeAt = textBeforeCursor.substring(0, atMatch.index);
      const newMessage = `${beforeAt}@${displayName} ${textAfterCursor}`;
      setMessage(newMessage);

      // Set cursor position after the tag
      setTimeout(() => {
        if (textareaRef.current) {
          const newCursorPos = beforeAt.length + displayName.length + 2;
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          textareaRef.current.focus();
        }
      }, 0);
    }

    setShowAutocomplete(false);
    setAutocompleteSearch("");
  };

  // Remove tagged contract
  const removeTaggedContract = (contractId: string) => {
    setTaggedContracts((prev) => prev.filter((tc) => tc.id !== contractId));

    // Also remove from message text
    const contractToRemove = taggedContracts.find((tc) => tc.id === contractId);
    if (contractToRemove) {
      const newMessage = message.replace(
        new RegExp(`@${contractToRemove.displayName}\\s?`, "g"),
        ""
      );
      setMessage(newMessage);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || disabled) return;

    // Combine manually tagged contracts with auto-tagged contract
    const allTaggedContracts = [...taggedContracts];
    if (autoTaggedContract) {
      // Add auto-tagged contract if not already in manually tagged contracts
      if (!allTaggedContracts.some((tc) => tc.id === autoTaggedContract.id)) {
        allTaggedContracts.push(autoTaggedContract);
      }
    }

    // Extract contract IDs for API call
    const contractIds = allTaggedContracts.map((tc) => tc.id);

    console.log("Sending message with tagged contracts:", {
      message,
      manuallyTaggedContracts: taggedContracts,
      autoTaggedContract,
      allTaggedContracts,
      contractIds,
    });

    onSendMessage(message, contractIds.length > 0 ? contractIds : undefined);
    setMessage("");
    setTaggedContracts([]);
    // Note: We don't clear autoTaggedContract here as it should persist for the page

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey && !showAutocomplete) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div ref={containerRef} className="relative">
      {/* Tagged Contracts Display - Only show manually tagged contracts */}
      {taggedContracts.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {/* Manually tagged contracts */}
          {taggedContracts.map((contract) => (
            <div
              key={contract.id}
              className="inline-flex items-center gap-1 px-2 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-md text-sm"
            >
              <span>{contract.displayName}</span>
              <button
                type="button"
                onClick={() => removeTaggedContract(contract.id)}
                className="hover:bg-green-300 dark:hover:bg-green-800 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Chat Input Form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-end">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            placeholder={getPlaceholder()}
            disabled={disabled}
            className="w-full p-3 pr-12 border rounded-lg resize-none max-h-32 focus:outline-none focus:ring-2 focus:ring-green-700 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
            rows={2}
          />
          <button
            type="submit"
            disabled={!message.trim() || disabled}
            className="absolute right-3 bottom-3 p-1 rounded-full text-green-700 hover:bg-green-200 dark:hover:bg-green-900/30 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <SendIcon size={20} />
          </button>
        </div>
      </form>

      {/* Contract Autocomplete */}
      <ContractAutocomplete
        isVisible={showAutocomplete}
        searchTerm={autocompleteSearch}
        onSelect={handleContractSelect}
        onClose={() => setShowAutocomplete(false)}
        position={autocompletePosition}
      />
    </div>
  );
}
