/**
 * Chat Sidebar Component
 * Displays a list of conversations and provides actions
 */

import React, { useState } from "react";
import {
  PlusIcon,
  TrashIcon,
  PencilIcon,
  MessageSquareIcon,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Conversation {
  id: string;
  tenantId: string;
  userId: string;
  title?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface ChatSidebarProps {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  onSelectConversation: (conversation: Conversation) => void;
  onNewConversation?: () => void;
  onDeleteConversation: (conversationId: string) => void;
  onRenameConversation: (conversationId: string, title: string) => void;
}

export function ChatSidebar({
  conversations,
  currentConversation,
  onSelectConversation,
  onNewConversation,
  onDeleteConversation,
  onRenameConversation,
}: ChatSidebarProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState("");

  // Start editing a conversation title
  const startEditing = (conversation: Conversation) => {
    setEditingId(conversation.id);
    setEditTitle(conversation.title || "");
  };

  // Save edited title
  const saveTitle = (conversationId: string) => {
    if (editTitle.trim()) {
      onRenameConversation(conversationId, editTitle);
    }
    setEditingId(null);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingId(null);
  };

  // Handle key press in edit input
  const handleKeyPress = (e: React.KeyboardEvent, conversationId: string) => {
    if (e.key === "Enter") {
      saveTitle(conversationId);
    } else if (e.key === "Escape") {
      cancelEditing();
    }
  };

  return (
    <div className="w-64 bg-gray-100 dark:bg-gray-800 border-r dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b dark:border-gray-700">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Conversations
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Start typing to create a new conversation
        </div>
      </div>

      {/* Conversation list */}
      <div className="flex-1 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No conversations yet
          </div>
        ) : (
          <ul className="divide-y divide-border">
            {conversations.map((conversation) => (
              <li key={conversation.id} className="relative">
                {editingId === conversation.id ? (
                  <div className="p-2">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      onKeyDown={(e) => handleKeyPress(e, conversation.id)}
                      onBlur={() => saveTitle(conversation.id)}
                      autoFocus
                      className="w-full p-1 border border-border rounded bg-background focus:outline-none focus:ring-1 focus:ring-ring"
                    />
                  </div>
                ) : (
                  <button
                    onClick={() => onSelectConversation(conversation)}
                    className={`w-full text-left p-3 flex items-start gap-3 hover:bg-secondary ${
                      currentConversation?.id === conversation.id
                        ? "bg-secondary"
                        : ""
                    }`}
                  >
                    <MessageSquareIcon
                      size={18}
                      className="mt-1 flex-shrink-0 text-muted-foreground"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        {conversation.title || "New Conversation"}
                      </div>
                      {conversation.updatedAt && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatDistanceToNow(
                            new Date(conversation.updatedAt),
                            { addSuffix: true }
                          )}
                        </div>
                      )}
                    </div>
                  </button>
                )}

                {/* Actions */}
                {editingId !== conversation.id && (
                  <div className="absolute right-2 top-2 flex gap-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        startEditing(conversation);
                      }}
                      className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                      <PencilIcon size={14} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteConversation(conversation.id);
                      }}
                      className="p-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                      <TrashIcon size={14} />
                    </button>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
