/**
 * Chat Message Component
 * Displays a single message in the chat interface
 */

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import ReactMarkdown from "react-markdown";

interface MessageProps {
  message: {
    id?: string;
    role: "system" | "user" | "assistant";
    content: string;
    metadata?: Record<string, any>;
    createdAt?: string;
  };
}

export function ChatMessage({ message }: MessageProps) {
  const { role, content, metadata, createdAt } = message;

  // Format timestamp
  const formattedTime = createdAt
    ? formatDistanceToNow(new Date(createdAt), { addSuffix: true })
    : "";

  // Determine message style based on role
  const isUser = role === "user";
  const isSystem = role === "system";

  // Extract citations if available
  const citations = metadata?.citations || [];

  return (
    <div
      className={`flex mb-4 ${isSystem ? "opacity-75" : ""} ${
        isUser ? "justify-end" : "justify-start"
      }`}
    >
      <div
        className={`flex max-w-[80%] ${
          isUser ? "flex-row-reverse" : "flex-row"
        } gap-3`}
      >
        {/* Avatar */}
        <div className="flex-shrink-0">
          <Avatar className={isUser ? "bg-secondary" : "bg-muted"}>
            <AvatarImage
              src={isUser ? undefined : "/ai-avatar.png"}
              alt={isUser ? "You" : "AI Assistant"}
            />
            <AvatarFallback
              className={isUser ? "text-custom-green" : "text-muted-foreground"}
            >
              {isUser ? "You" : "AI"}
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Message content */}
        <div className="flex-1">
          {/* Header */}
          <div
            className={`flex items-center mb-1 ${
              isUser ? "justify-end" : "justify-start"
            }`}
          >
            <span className="font-semibold mr-2 text-sm">
              {isUser ? "You" : isSystem ? "System" : "AI Assistant"}
            </span>
            {formattedTime && (
              <span className="text-xs text-muted-foreground">
                {formattedTime}
              </span>
            )}
          </div>

          {/* Content */}
          <div
            className={`p-3 rounded-lg ${
              isUser
                ? "bg-primary text-primary-foreground"
                : isSystem
                ? "bg-muted italic"
                : "bg-card border"
            }`}
          >
            <div
              className={`prose max-w-none prose-sm ${
                isUser ? "prose-invert" : ""
              }`}
            >
              <ReactMarkdown>{content}</ReactMarkdown>
            </div>

            {/* Citations */}
            {citations.length > 0 && (
              <div className="mt-2 pt-2 border-t border-border">
                <p className="text-xs font-semibold text-muted-foreground mb-1">
                  Sources:
                </p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  {citations.map((citation: string, index: number) => (
                    <li key={index}>{citation}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Processing metadata */}
            {metadata?.ragMetadata && (
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                <span className="opacity-75">
                  {metadata.ragMetadata.chunksRetrieved} chunks retrieved •
                  {Math.round(metadata.ragMetadata.processingTimeMs / 100) / 10}
                  s
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
