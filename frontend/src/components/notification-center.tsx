"use client";

import * as React from "react";
import { useState } from "react";
import Link from "next/link";
import {
  Bell,
  FileText,
  Key,
  Users,
  Shield,
  CheckCircle2,
  AlertTriangle,
  X,
  Settings,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/contexts/NotificationContext";
import type { Notification } from "@/contexts/NotificationContext";



export function NotificationCenter() {
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  } = useNotifications();

  const [open, setOpen] = useState(false);

  const handleNotificationItemClick = (notification: Notification) => {
    // Mark as read when clicking the notification
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    // Navigate to actionUrl if available
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }

    setOpen(false); // Close the popover after clicking
  };

  const handleDeleteNotification = (notificationId: string) => {
    deleteNotification(notificationId);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const getNotificationIcon = (type: string, priority: string) => {
    switch (type) {
      case "CONTRACT":
        return <FileText className="h-5 w-5 text-primary" />;
      case "LICENSE":
        return <Key className="h-5 w-5 text-primary" />;
      case "USER":
        return <Users className="h-5 w-5 text-primary" />;
      case "SYSTEM":
        if (priority === "HIGH" || priority === "CRITICAL") {
          return <AlertTriangle className="h-5 w-5 text-amber-500" />;
        }
        return <Shield className="h-5 w-5 text-primary" />;
      case "SECURITY":
        return <Shield className="h-5 w-5 text-red-500" />;
      default:
        return <Bell className="h-5 w-5 text-primary" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "CRITICAL":
        return (
          <Badge variant="destructive" className="text-xs">
            Critical
          </Badge>
        );
      case "HIGH":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 text-xs">
            High
          </Badge>
        );
      case "MEDIUM":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 text-xs">
            Medium
          </Badge>
        );
      case "LOW":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
            Low
          </Badge>
        );
      default:
        return null;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[380px] p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="font-semibold">Notifications</div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              disabled={unreadCount === 0 || isLoading}
            >
              Mark all as read
            </Button>
            <Link href="/settings/notifications" onClick={() => setOpen(false)}>
              <Button variant="ghost" size="icon">
                <Settings className="h-4 w-4" />
                <span className="sr-only">Notification Settings</span>
              </Button>
            </Link>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <div className="border-b">
            <TabsList className="w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="all"
                className="rounded-none border-b-2 border-b-transparent px-4 py-3 data-[state=active]:border-b-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                All
              </TabsTrigger>
              <TabsTrigger
                value="unread"
                className="rounded-none border-b-2 border-b-transparent px-4 py-3 data-[state=active]:border-b-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
              >
                Unread
                {unreadCount > 0 && (
                  <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                    {unreadCount}
                  </span>
                )}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent
            value="all"
            className="max-h-[400px] overflow-auto focus-visible:outline-none"
          >
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                <p className="text-sm text-muted-foreground">Loading notifications...</p>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8">
                <AlertTriangle className="h-10 w-10 text-red-500 mb-2" />
                <p className="text-sm text-red-600">{error}</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Bell className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  No notifications
                </p>
              </div>
            ) : (
              <div className="divide-y">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start gap-3 p-4 cursor-pointer hover:bg-muted/30 transition-colors ${!notification.isRead ? "bg-muted/50" : ""
                      }`}
                    onClick={() => handleNotificationItemClick(notification)}
                  >
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(
                        notification.type,
                        notification.priority
                      )}
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">
                          {notification.title}
                        </p>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteNotification(notification.id);
                          }}
                        >
                          <X className="h-3 w-3" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {notification.content}
                      </p>
                      <div className="flex items-center justify-between pt-1">
                        <p className="text-xs text-muted-foreground">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                        {getPriorityBadge(notification.priority)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent
            value="unread"
            className="max-h-[400px] overflow-auto focus-visible:outline-none"
          >
            {unreadCount === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <CheckCircle2 className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  No unread notifications
                </p>
              </div>
            ) : (
              <div className="divide-y">
                {notifications
                  .filter((notification) => !notification.isRead)
                  .map((notification) => (
                    <div
                      key={notification.id}
                      className="flex items-start gap-3 p-4 bg-muted/50 cursor-pointer hover:bg-muted/70 transition-colors"
                      onClick={() => handleNotificationItemClick(notification)}
                    >
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(
                          notification.type,
                          notification.priority
                        )}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">
                            {notification.title}
                          </p>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteNotification(notification.id);
                            }}
                          >
                            <X className="h-3 w-3" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {notification.content}
                        </p>
                        <div className="flex items-center justify-between pt-1">
                          <p className="text-xs text-muted-foreground">
                            {formatTimeAgo(notification.createdAt)}
                          </p>
                          {getPriorityBadge(notification.priority)}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-center p-4 border-t">
          <Link
            href="/dashboard/notifications"
            className="text-sm text-primary hover:underline"
            onClick={() => setOpen(false)}
          >
            View all notifications
          </Link>
        </div>
      </PopoverContent>
    </Popover>
  );
}
