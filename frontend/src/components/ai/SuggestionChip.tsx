/**
 * Suggestion Chip Component
 * Individual clickable suggestion button
 */

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Suggestion } from "./ChatSuggestions";
import {
  FileText,
  Calendar,
  DollarSign,
  AlertTriangle,
  Search,
  Upload,
  BarChart3,
  Clock,
  Shield,
  Users,
  MessageSquare,
  Lightbulb,
  FolderOpen,
  CheckSquare,
  TrendingUp,
  Settings,
} from "lucide-react";

interface SuggestionChipProps {
  suggestion: Suggestion;
  onClick: () => void;
  disabled?: boolean;
}

// Helper function to render icons based on icon name
const renderIcon = (iconName?: string) => {
  if (!iconName) return null;

  const iconProps = { className: "h-3 w-3" };

  switch (iconName) {
    case "FileText":
      return <FileText {...iconProps} />;
    case "Calendar":
      return <Calendar {...iconProps} />;
    case "DollarSign":
      return <DollarSign {...iconProps} />;
    case "AlertTriangle":
      return <AlertTriangle {...iconProps} />;
    case "Search":
      return <Search {...iconProps} />;
    case "Upload":
      return <Upload {...iconProps} />;
    case "BarChart3":
      return <BarChart3 {...iconProps} />;
    case "Clock":
      return <Clock {...iconProps} />;
    case "Shield":
      return <Shield {...iconProps} />;
    case "Users":
      return <Users {...iconProps} />;
    case "MessageSquare":
      return <MessageSquare {...iconProps} />;
    case "Lightbulb":
      return <Lightbulb {...iconProps} />;
    case "FolderOpen":
      return <FolderOpen {...iconProps} />;
    case "CheckSquare":
      return <CheckSquare {...iconProps} />;
    case "TrendingUp":
      return <TrendingUp {...iconProps} />;
    case "Settings":
      return <Settings {...iconProps} />;
    default:
      return <MessageSquare {...iconProps} />;
  }
};

export function SuggestionChip({
  suggestion,
  onClick,
  disabled = false,
}: SuggestionChipProps) {
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={onClick}
      disabled={disabled}
      className="h-auto py-2 px-3 text-xs font-normal border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200 rounded-full whitespace-nowrap"
    >
      {suggestion.icon && (
        <span className="mr-1.5 flex-shrink-0">
          {renderIcon(suggestion.icon)}
        </span>
      )}
      <span className="truncate">{suggestion.text}</span>
    </Button>
  );
}
