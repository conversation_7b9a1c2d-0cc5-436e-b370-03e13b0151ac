"use client";

import { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import {
  MessageSquare,
  X,
  Send,
  Loader2,
  Upload,
  Maximize2,
  Minimize2,
  FileText,
  Trash2,
  Link,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useDropzone } from "react-dropzone";
import { apiClient } from "@/lib/api-client";
import { EnhancedChatInput } from "../chat/EnhancedChatInput";
import { ChatSuggestions, Suggestion } from "./ChatSuggestions";
import { useChatSuggestions } from "@/hooks/useChatSuggestions";
import { contractService } from "@/services/contractService";
import ReactMarkdown from "react-markdown";

interface Message {
  id?: string;
  role: "user" | "assistant" | "system";
  content: string;
  documents?: File[];
}

interface Contract {
  id: string;
  title: string;
  contractNumber?: string;
  provider?: string;
}

export function FloatingAIChat() {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [documents, setDocuments] = useState<File[]>([]);
  const [uploadingDocuments, setUploadingDocuments] = useState(false);
  const [pinnedContractId, setPinnedContractId] = useState<
    string | undefined
  >();
  const [contractTitle, setContractTitle] = useState<string | undefined>();
  const [dynamicSuggestions, setDynamicSuggestions] = useState<Suggestion[]>(
    []
  );
  const [showFollowUps, setShowFollowUps] = useState(false);
  const [showContractList, setShowContractList] = useState(false);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [contractsLoading, setContractsLoading] = useState(false);
  const [comparisonContractIds, setComparisonContractIds] = useState<string[]>(
    []
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const contractListRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  // Initialize chat suggestions
  const { suggestions, triggerContextualSuggestions } = useChatSuggestions({
    messages,
    pinnedContractId,
    contractTitle,
    isLoading,
  });

  // Use dynamic suggestions if available, otherwise use static suggestions
  const currentSuggestions =
    showFollowUps && dynamicSuggestions.length > 0
      ? dynamicSuggestions
      : suggestions;

  // Fetch contracts when contract list is opened
  useEffect(() => {
    if (showContractList && contracts.length === 0) {
      fetchContracts();
    }
  }, [showContractList]);

  // Close contract list when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        contractListRef.current &&
        !contractListRef.current.contains(event.target as Node)
      ) {
        setShowContractList(false);
      }
    };

    if (showContractList) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showContractList]);

  const fetchContracts = async () => {
    setContractsLoading(true);
    try {
      const response = await contractService.getContracts();
      setContracts(response.contracts || []);
    } catch (error) {
      console.error("Failed to fetch contracts:", error);
      toast.error("Failed to load contracts");
    } finally {
      setContractsLoading(false);
    }
  };

  // Scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // No longer storing messages in session storage
  // This ensures chat history is cleared when the page refreshes

  // Get context from current page and add welcome message if needed
  useEffect(() => {
    // Only add welcome message if chat is opened and no messages exist
    // This ensures we don't override messages loaded from session storage
    if (isOpen && messages.length === 0) {
      // Add a welcome message based on the current page
      let welcomeMessage = "Hi, I'm MAI. How can I help you today?";
      // "Hello! I'm your contract management assistant. I'm here to help you navigate your contracts, understand key terms, and make informed decisions. What would you like to know?";

      // if (pathname.includes("/contracts")) {
      //   welcomeMessage =
      //     "Welcome to your contract workspace! I can help you analyze contracts, understand key terms, identify risks, and answer questions about your agreements. What can I assist you with today?";
      // } else if (pathname.includes("/licenses")) {
      //   welcomeMessage =
      //     "I'm here to help you manage your software licenses and ensure compliance. I can answer questions about license terms, usage rights, and renewal dates. How can I help?";
      // }

      // Check again to make sure messages is still empty before setting
      // This prevents race conditions with the session storage loading
      if (messages.length === 0) {
        setMessages([
          {
            role: "assistant",
            content: welcomeMessage,
          },
        ]);
        console.log("Added welcome message based on current page:", pathname);
      }
    }
  }, [isOpen, messages.length, pathname]);

  // Detect pinned contract from URL parameters and auto-tag comparison contracts
  useEffect(() => {
    const detectPinnedContract = () => {
      // Check if we're on a contract view page
      const contractViewMatch = pathname.match(
        /\/contract-management\/contracts\/analysis\/([^\/]+)/
      );

      // Check if we're on a discovery analysis page
      const discoveryAnalysisMatch = pathname.match(
        /\/contract-concierge\/discovery\/([^\/]+)/
      );

      // Check if we're on a benchmark comparison page
      const benchmarkCompareMatch = pathname.match(
        /\/contract-concierge\/benchmark\/compare/
      );

      if (contractViewMatch) {
        setPinnedContractId(contractViewMatch[1]);
      } else if (discoveryAnalysisMatch) {
        setPinnedContractId(discoveryAnalysisMatch[1]);
      } else if (benchmarkCompareMatch) {
        // Extract contract IDs from comparison page URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const contractIds = urlParams.getAll("ids");

        // Store the contract IDs for use in API calls
        setComparisonContractIds(contractIds);
        setPinnedContractId(undefined);
      } else {
        // Clear comparison contract IDs when not on comparison page
        setComparisonContractIds([]);

        // Check session storage for analyzed contract ID
        const analyzedContractId = sessionStorage.getItem("analyzedContractId");
        if (analyzedContractId) {
          setPinnedContractId(analyzedContractId);
        } else {
          setPinnedContractId(undefined);
        }
      }
    };

    detectPinnedContract();
  }, [pathname]);

  // Handle suggestion clicks
  const handleSuggestionClick = (suggestion: Suggestion) => {
    // Reset follow-up state when user clicks any suggestion
    setShowFollowUps(false);
    setDynamicSuggestions([]);

    // Send the suggestion text as a message
    handleMessageSubmit(suggestion.text);
  };

  // File upload handling
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      setDocuments((prev) => [...prev, ...acceptedFiles]);
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "text/plain": [".txt"],
      "application/rtf": [".rtf"],
    },
  });

  const clearDocuments = () => {
    setDocuments([]);
  };

  const removeDocument = (index: number) => {
    setDocuments((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadDocuments = async (files: File[]): Promise<string[]> => {
    setUploadingDocuments(true);
    try {
      const documentIds: string[] = [];

      for (const file of files) {
        const formData = new FormData();
        formData.append("files", file); // Note: The backend expects "files" not "file"

        const response = await apiClient.post("/api/gemini/upload", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        if (
          response &&
          response.success &&
          response.documentIds &&
          response.documentIds.length > 0
        ) {
          documentIds.push(...response.documentIds);
        }
      }

      return documentIds;
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error("Failed to upload documents. Please try again.");
      throw error;
    } finally {
      setUploadingDocuments(false);
    }
  };

  const handleMessageSubmit = async (
    message: string,
    taggedContracts?: string[]
  ) => {
    // Combine tagged contracts from input with comparison page contracts
    const allTaggedContracts = [
      ...(taggedContracts || []),
      ...comparisonContractIds,
    ];

    // Remove duplicates
    const uniqueTaggedContracts = Array.from(new Set(allTaggedContracts));

    if (!message.trim() && documents.length === 0) {
      toast.error("Please enter a message or upload documents");
      return;
    }

    // Reset follow-up state when user sends a new message
    setShowFollowUps(false);
    setDynamicSuggestions([]);

    let documentIds: string[] = [];

    // Upload documents if any
    if (documents.length > 0) {
      try {
        documentIds = await uploadDocuments(documents);
      } catch (error) {
        // Error is already handled in uploadDocuments
        return;
      }
    }

    // Add user message to chat
    const userMessage: Message = {
      role: "user",
      content: message,
      documents: documents.length > 0 ? [...documents] : undefined,
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    // Clear documents after sending
    clearDocuments();

    try {
      // Call Gemini API without conversation history
      // This ensures each chat session is independent and clears on page refresh
      const payload = {
        message: message.trim(),
        documentIds: documentIds.length > 0 ? documentIds : undefined,
        taggedContracts:
          uniqueTaggedContracts.length > 0 ? uniqueTaggedContracts : undefined,
        context: pathname, // Send current path as context
      };

      const response = await apiClient.post("/api/gemini/chat", payload, {
        timeout: 300000, // 5 minutes for chat operations
      });

      // Add assistant response to chat
      if (response && response.success && response.message) {
        const assistantMessage: Message = {
          id: response.id,
          role: response.message.role,
          content: response.message.content,
        };

        setMessages((prev) => [...prev, assistantMessage]);

        // Handle follow-up suggestions if available
        if (
          response.followUpSuggestions &&
          response.followUpSuggestions.length > 0
        ) {
          setDynamicSuggestions(response.followUpSuggestions);
          setShowFollowUps(true);
          console.log(
            "Received follow-up suggestions:",
            response.followUpSuggestions
          );
        } else {
          // Trigger contextual suggestions after the first user interaction
          triggerContextualSuggestions();
          setShowFollowUps(false);
        }
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error: any) {
      console.error("Error calling Gemini API:", error);

      // Add error message to chat
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "I apologize, but I'm having trouble processing your request right now. Please try again in a moment, or feel free to rephrase your question.",
        },
      ]);

      toast.error("Failed to get AI response");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Floating button */}
      {!isOpen && (
        <Button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-4 right-4 rounded-full h-14 w-14 p-0 shadow-xl bg-primary hover:bg-primary/90 border-0 transition-all duration-300 transform hover:scale-105 z-50"
        >
          <MessageSquare className="h-6 w-6 text-primary-foreground" />
        </Button>
      )}

      {/* Chat window */}
      {isOpen && (
        <Card
          className={`fixed bottom-4 right-4 shadow-xl border border-border rounded-xl overflow-hidden transition-all duration-300 z-50 flex flex-col ${isExpanded ? "w-[80vw] h-[80vh] max-w-6xl" : "w-[500px] h-[700px]"
            }`}
        >
          <CardHeader className="px-4 py-3 flex-shrink-0 border-b bg-primary flex flex-row items-center justify-between">
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <div className="w-8 h-8 bg-primary/80 rounded-lg flex items-center justify-center">
                <MessageSquare className="h-4 w-4 text-primary-foreground" />
              </div>
              <span className="text-primary-foreground font-semibold">MAI</span>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-white/20 rounded-lg"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <Minimize2 className="h-4 w-4 text-white" />
                ) : (
                  <Maximize2 className="h-4 w-4 text-white" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-white/20 rounded-lg"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4 text-white" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-4 overflow-y-auto flex-1 bg-white dark:bg-slate-900">
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div
                  key={message.id || index}
                  className={`flex ${message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                >
                  {message.role !== "user" && (
                    <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center mr-2 flex-shrink-0">
                      <MessageSquare className="h-4 w-4 text-primary-foreground" />
                    </div>
                  )}
                  <div
                    className={`rounded-2xl px-4 py-2.5 max-w-[80%] shadow-sm ${message.role === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-card border border-border"
                      }`}
                  >
                    {message.role === "user" ? (
                      <div className="whitespace-pre-wrap text-sm">
                        {message.content}
                      </div>
                    ) : (
                      <div className="prose prose-sm dark:prose-invert max-w-none text-sm">
                        <ReactMarkdown>{message.content}</ReactMarkdown>
                      </div>
                    )}
                    {message.documents && message.documents.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {message.documents.map((doc, i) => (
                          <Badge
                            key={i}
                            variant={
                              message.role === "user" ? "secondary" : "outline"
                            }
                            className="text-xs bg-opacity-20 flex items-center gap-1"
                          >
                            <FileText className="h-3 w-3" />
                            {doc.name}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  {message.role === "user" && (
                    <div className="w-8 h-8 rounded-lg bg-secondary flex items-center justify-center ml-2 flex-shrink-0">
                      <span className="text-xs font-medium text-secondary-foreground">
                        You
                      </span>
                    </div>
                  )}
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center mr-2 flex-shrink-0">
                    <MessageSquare className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <div className="rounded-2xl px-4 py-3 bg-card border border-border shadow-sm">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      <span className="text-sm text-muted-foreground">
                        Thinking...
                      </span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Chat Suggestions */}
            {currentSuggestions.length > 0 && (
              <div className="px-4 pb-1">
                <ChatSuggestions
                  suggestions={currentSuggestions}
                  onSuggestionClick={handleSuggestionClick}
                  isLoading={isLoading}
                  isExpanded={isExpanded}
                />
              </div>
            )}
          </CardContent>

          <CardFooter className="p-4 pt-3 border-t bg-background flex flex-col mt-auto flex-shrink-0">
            {documents.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-1.5 bg-muted p-2 rounded-lg">
                {documents.map((doc, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1 bg-background border border-border shadow-sm pl-2 pr-1 py-1"
                  >
                    <FileText className="h-3 w-3 text-primary" />
                    <span className="truncate max-w-[100px] text-xs">
                      {doc.name}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0 hover:bg-destructive/10 rounded-full ml-1"
                      onClick={() => removeDocument(index)}
                    >
                      <X className="h-3 w-3 text-red-500" />
                    </Button>
                  </Badge>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                  onClick={clearDocuments}
                >
                  Clear all
                </Button>
              </div>
            )}

            <div className="flex w-full gap-2 items-stretch">
              {/* Vertical Icon Stack */}
              <div className="flex flex-col gap-1 justify-end">
                <div
                  {...getRootProps()}
                  className={`cursor-pointer ${isDragActive ? "opacity-70" : ""
                    }`}
                >
                  <input {...getInputProps()} />
                  <Button
                    type="button"
                    size="icon"
                    variant="outline"
                    className="h-8 w-8 rounded-full border-border hover:bg-secondary"
                    disabled={isLoading || uploadingDocuments}
                  >
                    <Upload className="h-3.5 w-3.5 text-primary" />
                  </Button>
                </div>

                <div className="relative" ref={contractListRef}>
                  <Button
                    type="button"
                    size="icon"
                    variant="outline"
                    className="h-8 w-8 rounded-full border-border hover:bg-secondary"
                    disabled={isLoading || uploadingDocuments}
                    onClick={() => setShowContractList(!showContractList)}
                  >
                    <Link className="h-3.5 w-3.5 text-primary" />
                  </Button>

                  {showContractList && (
                    <div className="absolute bottom-10 left-0 z-50 w-80 bg-background border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      <div className="p-3 border-b border-border">
                        <h3 className="text-sm font-medium text-foreground">
                          Select a Contract
                        </h3>
                      </div>
                      {contractsLoading ? (
                        <div className="p-3 text-center">
                          <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                          <p className="text-xs text-muted-foreground mt-1">
                            Loading contracts...
                          </p>
                        </div>
                      ) : contracts.length > 0 ? (
                        <div className="py-1">
                          {contracts.slice(0, 10).map((contract) => (
                            <button
                              key={contract.id}
                              className="w-full text-left px-3 py-2 hover:bg-secondary text-sm flex items-center gap-2"
                              onClick={() => {
                                // Tag the contract instead of sending a message
                                const contractData = {
                                  id: contract.id,
                                  title: contract.title,
                                  contractNumber: contract.contractNumber,
                                  provider: contract.provider,
                                };

                                // Trigger the contract tagging in EnhancedChatInput
                                const event = new CustomEvent("tagContract", {
                                  detail: contractData,
                                });
                                window.dispatchEvent(event);

                                setShowContractList(false);
                              }}
                            >
                              <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div className="min-w-0 flex-1">
                                <div className="font-medium truncate">
                                  {contract.title}
                                </div>
                                {contract.contractNumber && (
                                  <div className="text-xs text-muted-foreground">
                                    #{contract.contractNumber}
                                  </div>
                                )}
                              </div>
                            </button>
                          ))}
                        </div>
                      ) : (
                        <div className="p-3 text-center text-sm text-muted-foreground">
                          No contracts found
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex-1">
                <EnhancedChatInput
                  onSendMessage={handleMessageSubmit}
                  disabled={isLoading || uploadingDocuments}
                  placeholder="Ask me about your contracts, terms, or any questions you have..."
                />
              </div>
            </div>
          </CardFooter>
        </Card>
      )}
    </>
  );
}
