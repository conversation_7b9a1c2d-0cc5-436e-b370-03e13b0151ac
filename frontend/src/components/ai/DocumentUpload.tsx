/**
 * Document Upload Component
 * Allows users to upload documents for AI analysis
 */

"use client";

import React, { useState, useRef } from "react";

import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Loader2, Upload, FileText, File, X } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { useMutation } from "@tanstack/react-query";
import { apiClient } from "../../lib/api-client";
import { v4 as uuidv4 } from "uuid";

/**
 * Document format enum
 */
export enum DocumentFormat {
  PDF = "PDF",
  DOCX = "DOCX",
  TXT = "TXT",
  HTML = "HTML",
}

/**
 * Document upload options interface
 */
export interface DocumentUploadOptions {
  extractEntities: boolean;
  extractClauses: boolean;
  generateEmbeddings: boolean;
}

/**
 * Props for DocumentUpload component
 */
interface DocumentUploadProps {
  onUploadComplete?: (jobId: string, documentId: string) => void;
  contractId?: string;
}

/**
 * Document Upload Component
 */
export function DocumentUpload({
  onUploadComplete,
  contractId,
}: DocumentUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [file, setFile] = useState<File | null>(null);
  const [documentFormat, setDocumentFormat] = useState<DocumentFormat>(
    DocumentFormat.PDF
  );
  const [options, setOptions] = useState<DocumentUploadOptions>({
    extractEntities: true,
    extractClauses: true,
    generateEmbeddings: true,
  });
  const [uploadProgress, setUploadProgress] = useState(0);
  const [documentId, setDocumentId] = useState<string>("");

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);

      // Auto-detect document format from file extension
      const extension = selectedFile.name.split(".").pop()?.toLowerCase();
      if (extension === "pdf") {
        setDocumentFormat(DocumentFormat.PDF);
      } else if (extension === "docx" || extension === "doc") {
        setDocumentFormat(DocumentFormat.DOCX);
      } else if (extension === "txt") {
        setDocumentFormat(DocumentFormat.TXT);
      } else if (extension === "html" || extension === "htm") {
        setDocumentFormat(DocumentFormat.HTML);
      }
    }
  };

  // Handle file drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      setFile(droppedFile);

      // Auto-detect document format from file extension
      const extension = droppedFile.name.split(".").pop()?.toLowerCase();
      if (extension === "pdf") {
        setDocumentFormat(DocumentFormat.PDF);
      } else if (extension === "docx" || extension === "doc") {
        setDocumentFormat(DocumentFormat.DOCX);
      } else if (extension === "txt") {
        setDocumentFormat(DocumentFormat.TXT);
      } else if (extension === "html" || extension === "htm") {
        setDocumentFormat(DocumentFormat.HTML);
      }
    }
  };

  // Prevent default behavior for drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // Clear selected file
  const handleClearFile = () => {
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Upload file to storage (simulated)
  const uploadFileToStorage = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
          // Return a simulated document URI
          resolve(`document://${uuidv4()}`);
        }
      }, 200);
    });
  };

  // Process document mutation
  const processDocumentMutation = useMutation({
    mutationFn: async (documentUri: string) => {
      // Generate a document ID if not provided
      const docId = documentId || uuidv4();
      setDocumentId(docId);

      // Process document
      const response = await apiClient.post("/api/ai/document", {
        documentId: docId,
        documentFormat,
        contractId,
        options,
        documentUri,
      });

      return { jobId: response.data.jobId, documentId: docId };
    },
    onSuccess: (data) => {
      toast.success("Document uploaded successfully");
      if (onUploadComplete) {
        onUploadComplete(data.jobId, data.documentId);
      }
    },
    onError: (error) => {
      toast.error("Failed to process document. Please try again.");
    },
  });

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      toast.error("No file selected");
      return;
    }

    try {
      // Upload file to storage
      const documentUri = await uploadFileToStorage(file);

      // Process document
      processDocumentMutation.mutate(documentUri);
    } catch (error) {
      toast.error("Failed to upload document. Please try again.");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Document</CardTitle>
        <CardDescription>
          Upload a document for AI analysis and insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* File Upload */}
            <div className="space-y-2">
              <Label htmlFor="file">Document</Label>
              <div
                className={`border-2 border-dashed rounded-lg p-6 ${
                  file ? "border-primary" : "border-muted-foreground/25"
                } transition-colors hover:border-primary/50 cursor-pointer`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Input
                  id="file"
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.docx,.doc,.txt,.html,.htm"
                />
                {file ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="h-8 w-8 text-primary" />
                      <div>
                        <p className="font-medium">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClearFile();
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-center">
                    <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                    <p className="font-medium">
                      Drag and drop your file here or click to browse
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Supports PDF, DOCX, TXT, and HTML files
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Document Format */}
            <div className="space-y-2">
              <Label htmlFor="format">Document Format</Label>
              <Select
                value={documentFormat}
                onValueChange={(value) =>
                  setDocumentFormat(value as DocumentFormat)
                }
              >
                <SelectTrigger id="format">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={DocumentFormat.PDF}>PDF</SelectItem>
                  <SelectItem value={DocumentFormat.DOCX}>DOCX</SelectItem>
                  <SelectItem value={DocumentFormat.TXT}>TXT</SelectItem>
                  <SelectItem value={DocumentFormat.HTML}>HTML</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Processing Options */}
            <div className="space-y-3">
              <Label>Processing Options</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="extractEntities" className="cursor-pointer">
                    Extract Entities
                  </Label>
                  <Switch
                    id="extractEntities"
                    checked={options.extractEntities}
                    onCheckedChange={(checked) =>
                      setOptions((prev) => ({
                        ...prev,
                        extractEntities: checked,
                      }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="extractClauses" className="cursor-pointer">
                    Extract Clauses
                  </Label>
                  <Switch
                    id="extractClauses"
                    checked={options.extractClauses}
                    onCheckedChange={(checked) =>
                      setOptions((prev) => ({
                        ...prev,
                        extractClauses: checked,
                      }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label
                    htmlFor="generateEmbeddings"
                    className="cursor-pointer"
                  >
                    Generate Embeddings
                  </Label>
                  <Switch
                    id="generateEmbeddings"
                    checked={options.generateEmbeddings}
                    onCheckedChange={(checked) =>
                      setOptions((prev) => ({
                        ...prev,
                        generateEmbeddings: checked,
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Document ID (Optional) */}
            <div className="space-y-2">
              <Label htmlFor="documentId">Document ID (Optional)</Label>
              <Input
                id="documentId"
                value={documentId}
                onChange={(e) => setDocumentId(e.target.value)}
                placeholder="Leave blank to generate automatically"
              />
            </div>

            {/* Upload Progress */}
            {uploadProgress > 0 && uploadProgress < 100 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Uploading...</span>
                  <span className="text-sm">{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
              </div>
            )}
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={
            !file || processDocumentMutation.isPending || uploadProgress > 0
          }
          className="w-full"
        >
          {processDocumentMutation.isPending || uploadProgress > 0 ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            "Upload & Process"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
