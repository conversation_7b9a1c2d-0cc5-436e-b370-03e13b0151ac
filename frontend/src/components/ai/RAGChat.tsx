/**
 * RAG Chat Component
 * Provides a chat interface for interacting with the RAG system
 */
"use client";

import React, { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Send, RefreshCw } from "lucide-react";
import { apiClient } from "@/lib/api-client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

/**
 * Message interface
 */
interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

/**
 * Conversation interface
 */
interface Conversation {
  id: string;
  title?: string;
  tenantId: string;
  userId: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Citation interface
 */
interface Citation {
  text: string;
  documentId: string;
  metadata: Record<string, any>;
}

/**
 * RAG response interface
 */
interface RAGResponse {
  message: Message;
  citations: Citation[];
}

/**
 * Props for RAGChat component
 */
interface RAGChatProps {
  conversationId?: string;
  onConversationCreated?: (conversationId: string) => void;
}

/**
 * RAG Chat Component
 */
export function RAGChat({
  conversationId: initialConversationId,
  onConversationCreated,
}: RAGChatProps) {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const [query, setQuery] = useState("");
  const [conversationId, setConversationId] = useState<string | undefined>(
    initialConversationId
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversation if ID is provided
  const { data: conversation, isLoading: isLoadingConversation } = useQuery({
    queryKey: ["conversation", conversationId],
    queryFn: async () => {
      if (!conversationId) return null;
      const response = await apiClient.get(
        `/api/ai/conversation/${conversationId}`
      );
      return response.data.conversation as Conversation;
    },
    enabled: !!conversationId,
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async () => {
      const response = await apiClient.post("/api/ai/conversation");
      return response.data.conversation as Conversation;
    },
    onSuccess: (data) => {
      setConversationId(data.id);
      if (onConversationCreated) {
        onConversationCreated(data.id);
      }
    },
    onError: (error) => {
      toast.error("Failed to create conversation");
    },
  });

  // Process query mutation
  const processQueryMutation = useMutation({
    mutationFn: async (query: string) => {
      const response = await apiClient.post("/api/ai/query", {
        query,
        conversationId,
      });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["conversation", data.conversationId],
      });
      setConversationId(data.conversationId);
      if (!initialConversationId && onConversationCreated) {
        onConversationCreated(data.conversationId);
      }
    },
    onError: (error) => {
      toast.error("I'm having trouble processing your question");
    },
  });

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [conversation?.messages]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    // Create conversation if needed
    if (!conversationId) {
      createConversationMutation.mutate();
      return;
    }

    // Process query
    processQueryMutation.mutate(query);
    setQuery("");
  };

  // Render message content with citations
  const renderMessageContent = (message: Message) => {
    if (message.role !== "assistant" || !message.metadata?.citations) {
      return <p className="whitespace-pre-wrap">{message.content}</p>;
    }

    // Replace citation markers with links
    let content = message.content;
    const citations = message.metadata.citations as Citation[];

    citations.forEach((citation, index) => {
      const marker = citation.text;
      content = content.replace(
        new RegExp(marker, "g"),
        `<a href="#citation-${index}" class="text-blue-500 hover:underline">${marker}</a>`
      );
    });

    return (
      <p
        className="whitespace-pre-wrap"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  };

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader>
        <CardTitle>AI Assistant</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto">
        {isLoadingConversation ? (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : conversation?.messages.length ? (
          <div className="space-y-4">
            {conversation.messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`flex gap-3 max-w-[80%] ${
                    message.role === "user" ? "flex-row-reverse" : "flex-row"
                  }`}
                >
                  <Avatar className="h-8 w-8">
                    {message.role === "user" ? (
                      <>
                        <AvatarImage src="/placeholder-user.jpg" />
                        <AvatarFallback>
                          {user?.name?.[0] || user?.userId?.[0] || "U"}
                        </AvatarFallback>
                      </>
                    ) : (
                      <>
                        <AvatarImage src="/ai-avatar.png" />
                        <AvatarFallback>CA</AvatarFallback>
                      </>
                    )}
                  </Avatar>
                  <div
                    className={`rounded-lg p-3 ${
                      message.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                    }`}
                  >
                    {renderMessageContent(message)}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground">
            <p className="mb-2">
              I'm here to help you understand your contracts and licenses.
            </p>
            <p className="text-sm">
              I'll search through your documents to provide accurate answers to
              your questions.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <form onSubmit={handleSubmit} className="w-full flex gap-2">
          <Textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="What would you like to know about your contracts?"
            className="flex-1 min-h-[60px] max-h-[120px]"
            disabled={
              processQueryMutation.isPending ||
              createConversationMutation.isPending
            }
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
          <Button
            type="submit"
            disabled={
              !query.trim() ||
              processQueryMutation.isPending ||
              createConversationMutation.isPending
            }
          >
            {processQueryMutation.isPending ||
            createConversationMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="sr-only">Send</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
