/**
 * Chat Suggestions Component
 * Displays contextual suggestions for the AI chatbot
 */

import React from "react";
import { SuggestionChip } from "./SuggestionChip";

export interface Suggestion {
  id: string;
  text: string;
  category:
    | "contract"
    | "general"
    | "followup"
    | "proactive"
    | "discovery"
    | "analysis"
    | "management"
    | "review"
    | "renewals"
    | "compliance"
    | "obligations"
    | "financials"
    | "analytics"
    | "admin";
  icon?: string; // Icon name instead of JSX element
  priority?: number;
}

interface ChatSuggestionsProps {
  suggestions: Suggestion[];
  onSuggestionClick: (suggestion: Suggestion) => void;
  isLoading?: boolean;
  isExpanded?: boolean;
  className?: string;
}

export function ChatSuggestions({
  suggestions,
  onSuggestionClick,
  isLoading = false,
  isExpanded = false,
  className = "",
}: ChatSuggestionsProps) {
  if (isLoading || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`${className}`}>
      {/* Simple chip layout without cards */}
      <div className="flex flex-wrap gap-2">
        {suggestions.slice(0, isExpanded ? 6 : 2).map((suggestion) => (
          <SuggestionChip
            key={suggestion.id}
            suggestion={suggestion}
            onClick={() => onSuggestionClick(suggestion)}
          />
        ))}
      </div>
    </div>
  );
}
