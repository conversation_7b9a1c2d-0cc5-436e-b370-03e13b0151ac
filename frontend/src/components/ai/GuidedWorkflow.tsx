/**
 * Guided Workflow Component
 * Provides step-by-step guidance for common tasks with AI assistance
 */

"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { RAGChat } from "./RAGChat";
import { DocumentUpload } from "./DocumentUpload";
import {
  ArrowRight,
  CheckCircle2,
  ChevronRight,
  FileText,
  HelpCircle,
  Lightbulb,
  Loader2,
  MessageSquare,
  Upload,
} from "lucide-react";

/**
 * Workflow step interface
 */
export interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  component: React.ReactNode;
  completed: boolean;
  optional?: boolean;
}

/**
 * Workflow interface
 */
export interface Workflow {
  id: string;
  title: string;
  description: string;
  steps: WorkflowStep[];
  aiAssistant?: boolean;
}

/**
 * Props for GuidedWorkflow component
 */
interface GuidedWorkflowProps {
  workflows: Workflow[];
  onComplete?: (workflowId: string) => void;
}

/**
 * Guided Workflow Component
 */
export function GuidedWorkflow({ workflows, onComplete }: GuidedWorkflowProps) {
  const [activeWorkflow, setActiveWorkflow] = useState<string>(
    workflows[0]?.id || ""
  );
  const [activeStep, setActiveStep] = useState<string>("");
  const [completedSteps, setCompletedSteps] = useState<
    Record<string, string[]>
  >({});
  const [conversationId, setConversationId] = useState<string>("");

  // Get current workflow
  const currentWorkflow = workflows.find((w) => w.id === activeWorkflow);

  // Get current step
  const currentStep = currentWorkflow?.steps.find((s) => s.id === activeStep);

  // Calculate progress
  const calculateProgress = (workflowId: string) => {
    const workflow = workflows.find((w) => w.id === workflowId);
    if (!workflow) return 0;

    const completed = completedSteps[workflowId]?.length || 0;
    const total = workflow.steps.filter((s) => !s.optional).length;

    return Math.round((completed / total) * 100);
  };

  // Mark step as completed
  const completeStep = (workflowId: string, stepId: string) => {
    setCompletedSteps((prev) => {
      const workflowSteps = prev[workflowId] || [];
      if (workflowSteps.includes(stepId)) {
        return prev;
      }

      return {
        ...prev,
        [workflowId]: [...workflowSteps, stepId],
      };
    });

    // Check if workflow is completed
    const workflow = workflows.find((w) => w.id === workflowId);
    if (workflow) {
      const requiredSteps = workflow.steps
        .filter((s) => !s.optional)
        .map((s) => s.id);
      const completed = [...(completedSteps[workflowId] || []), stepId];
      const isCompleted = requiredSteps.every((s) => completed.includes(s));

      if (isCompleted && onComplete) {
        onComplete(workflowId);
      }
    }
  };

  // Start workflow
  const startWorkflow = (workflowId: string) => {
    const workflow = workflows.find((w) => w.id === workflowId);
    if (workflow && workflow.steps.length > 0) {
      setActiveWorkflow(workflowId);
      setActiveStep(workflow.steps[0].id);
    }
  };

  // Navigate to next step
  const nextStep = () => {
    if (!currentWorkflow) return;

    const currentIndex = currentWorkflow.steps.findIndex(
      (s) => s.id === activeStep
    );
    if (currentIndex < currentWorkflow.steps.length - 1) {
      setActiveStep(currentWorkflow.steps[currentIndex + 1].id);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (!currentWorkflow) return;

    const currentIndex = currentWorkflow.steps.findIndex(
      (s) => s.id === activeStep
    );
    if (currentIndex > 0) {
      setActiveStep(currentWorkflow.steps[currentIndex - 1].id);
    }
  };

  // Check if step is completed
  const isStepCompleted = (workflowId: string, stepId: string) => {
    return completedSteps[workflowId]?.includes(stepId) || false;
  };

  // Handle conversation creation
  const handleConversationCreated = (id: string) => {
    setConversationId(id);
  };

  return (
    <div className="space-y-6">
      {/* Workflow Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Guided Workflows</CardTitle>
          <CardDescription>
            Step-by-step guidance for common tasks with AI assistance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {workflows.map((workflow) => {
              const progress = calculateProgress(workflow.id);
              const isActive = activeWorkflow === workflow.id;

              return (
                <Card
                  key={workflow.id}
                  className={`cursor-pointer transition-all ${
                    isActive ? "border-primary" : "hover:border-primary/50"
                  }`}
                  onClick={() => startWorkflow(workflow.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-base">
                        {workflow.title}
                      </CardTitle>
                      {progress === 100 && (
                        <Badge
                          variant="outline"
                          className="ml-2 bg-green-800/10 text-green-900 dark:text-green-300"
                        >
                          Completed
                        </Badge>
                      )}
                    </div>
                    <CardDescription>{workflow.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Progress</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <Button
                      variant={isActive ? "default" : "outline"}
                      size="sm"
                      className="w-full"
                    >
                      {progress === 0 ? (
                        <>Start Workflow</>
                      ) : progress === 100 ? (
                        <>View Details</>
                      ) : (
                        <>Continue</>
                      )}
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Active Workflow */}
      {currentWorkflow && activeStep && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>{currentWorkflow.title}</CardTitle>
              <Badge variant="outline">
                Step{" "}
                {currentWorkflow.steps.findIndex((s) => s.id === activeStep) +
                  1}{" "}
                of {currentWorkflow.steps.length}
              </Badge>
            </div>
            <CardDescription>{currentWorkflow.description}</CardDescription>
            <div className="mt-2">
              <Progress
                value={
                  ((currentWorkflow.steps.findIndex(
                    (s) => s.id === activeStep
                  ) +
                    1) /
                    currentWorkflow.steps.length) *
                  100
                }
                className="h-2"
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex gap-6">
              {/* Steps Sidebar */}
              <div className="hidden md:block w-64 shrink-0 border-r pr-6">
                <h3 className="font-medium mb-3">Steps</h3>
                <ul className="space-y-2">
                  {currentWorkflow.steps.map((step, index) => {
                    const isCompleted = isStepCompleted(
                      currentWorkflow.id,
                      step.id
                    );
                    const isCurrent = step.id === activeStep;

                    return (
                      <li key={step.id}>
                        <button
                          className={`flex items-center w-full text-left p-2 rounded-md text-sm ${
                            isCurrent
                              ? "bg-primary/10 text-primary"
                              : isCompleted
                              ? "text-muted-foreground hover:bg-muted"
                              : "text-muted-foreground hover:bg-muted"
                          }`}
                          onClick={() => setActiveStep(step.id)}
                        >
                          <div className="flex items-center justify-center w-6 h-6 rounded-full mr-2 text-xs border">
                            {isCompleted ? (
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            ) : (
                              <span>{index + 1}</span>
                            )}
                          </div>
                          <span>{step.title}</span>
                          {step.optional && (
                            <Badge
                              variant="outline"
                              className="ml-auto text-xs"
                            >
                              Optional
                            </Badge>
                          )}
                        </button>
                      </li>
                    );
                  })}
                </ul>
              </div>

              {/* Step Content */}
              <div className="flex-1">
                {currentStep && (
                  <div className="space-y-4">
                    <div>
                      <h2 className="text-xl font-semibold">
                        {currentStep.title}
                      </h2>
                      <p className="text-muted-foreground mt-1">
                        {currentStep.description}
                      </p>
                    </div>

                    <Separator />

                    <div>
                      {/* Step Component */}
                      {currentStep.component}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentWorkflow.steps[0].id === activeStep}
            >
              Previous
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => completeStep(currentWorkflow.id, activeStep)}
              >
                Mark as Completed
              </Button>
              <Button
                onClick={nextStep}
                disabled={
                  currentWorkflow.steps[currentWorkflow.steps.length - 1].id ===
                  activeStep
                }
              >
                Next Step
              </Button>
            </div>
          </CardFooter>
        </Card>
      )}

      {/* AI Assistant */}
      {currentWorkflow?.aiAssistant && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              Contract Assistant
            </CardTitle>
            <CardDescription>
              I'm here to help you with your current task and answer any
              questions
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            <RAGChat
              conversationId={conversationId}
              onConversationCreated={handleConversationCreated}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
