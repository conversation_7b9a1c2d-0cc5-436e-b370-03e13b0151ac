/**
 * AI Assistant Chat Component
 * A chat interface for interacting with Google's Gemini AI
 * with document upload and comparison capabilities
 */
"use client";

import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Send, Upload, X, FileText, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import ReactMarkdown from "react-markdown";
import { v4 as uuidv4 } from "uuid";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { EnhancedChatInput } from "../chat/EnhancedChatInput";
import { ChatSuggestions, Suggestion } from "./ChatSuggestions";
import { useChatSuggestions } from "@/hooks/useChatSuggestions";

/**
 * Document interface
 */
interface Document {
  id: string;
  name: string;
  file: File;
  size: string;
}

/**
 * Message interface
 */
interface Message {
  id?: string;
  role: "user" | "assistant";
  content: string;
  documents?: Document[];
}

/**
 * AI Assistant Chat Component
 */
/**
 * Format file size in a human-readable format
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export function AIAssistantChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [uploadingDocuments, setUploadingDocuments] = useState(false);

  // References
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize chat suggestions
  const { suggestions, triggerContextualSuggestions } = useChatSuggestions({
    messages,
    pinnedContractId: undefined, // No contract pinning in this component
    contractTitle: undefined,
    isLoading,
  });

  // Handle suggestion clicks
  const handleSuggestionClick = (suggestion: Suggestion) => {
    // Send the suggestion text as a message
    handleMessageSubmit(suggestion.text);
  };

  /**
   * Auto-scroll to bottom when messages change or loading state changes
   */
  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  /**
   * Scrolls to the bottom of the messages container
   */
  const scrollToBottom = () => {
    // Use setTimeout to ensure the scroll happens after the DOM is updated
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);
  };

  /**
   * Handle file selection
   */
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newDocuments: Document[] = Array.from(e.target.files).map(
        (file) => ({
          id: uuidv4(),
          name: file.name,
          file: file,
          size: formatFileSize(file.size),
        })
      );

      setDocuments((prev) => [...prev, ...newDocuments]);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  /**
   * Remove a document
   */
  const removeDocument = (id: string) => {
    setDocuments((prev) => prev.filter((doc) => doc.id !== id));
  };

  /**
   * Clear all documents
   */
  const clearDocuments = () => {
    setDocuments([]);
  };

  /**
   * Upload documents to server
   */
  const uploadDocuments = async (docs: Document[]): Promise<string[]> => {
    if (docs.length === 0) return [];

    // Check file size limits
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const MAX_TOTAL_SIZE = 20 * 1024 * 1024; // 20MB

    let totalSize = 0;
    const oversizedFiles: string[] = [];

    docs.forEach((doc) => {
      totalSize += doc.file.size;
      if (doc.file.size > MAX_FILE_SIZE) {
        oversizedFiles.push(doc.name);
      }
    });

    if (oversizedFiles.length > 0) {
      toast.error(
        `Some files exceed the 10MB limit: ${oversizedFiles.join(", ")}`
      );
      return [];
    }

    if (totalSize > MAX_TOTAL_SIZE) {
      toast.error(
        `Total file size (${formatFileSize(totalSize)}) exceeds the 20MB limit`
      );
      return [];
    }

    setUploadingDocuments(true);
    toast.info(`Uploading ${docs.length} document(s)...`);

    try {
      const formData = new FormData();
      docs.forEach((doc) => {
        formData.append("files", doc.file);
      });

      // Use a longer timeout for large uploads
      const response = await apiClient.post("/api/gemini/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 60000, // 60 seconds
      });

      if (response && response.success && response.documentIds) {
        toast.success(
          `Successfully uploaded ${response.documentIds.length} document(s)`
        );
        return response.documentIds;
      } else {
        throw new Error("Failed to upload documents");
      }
    } catch (error: any) {
      console.error("Error uploading documents:", error);

      // Provide more specific error messages
      if (error.response && error.response.status === 413) {
        toast.error("Documents are too large. Please try smaller files.");
      } else if (
        error.code === "ECONNABORTED" ||
        (error.message && error.message.includes("timeout"))
      ) {
        toast.error("Upload timed out. Please try with smaller documents.");
      } else {
        toast.error("Failed to upload documents. Please try again.");
      }

      throw error;
    } finally {
      setUploadingDocuments(false);
    }
  };

  /**
   * Handles message submission with optional tagged contracts
   */
  const handleMessageSubmit = async (
    message: string,
    taggedContracts?: string[]
  ) => {
    if (!message.trim() && documents.length === 0) {
      toast.error("Please enter a message or upload documents");
      return;
    }

    let documentIds: string[] = [];

    // Upload documents if any
    if (documents.length > 0) {
      try {
        documentIds = await uploadDocuments(documents);
      } catch (error) {
        // Error is already handled in uploadDocuments
        return;
      }
    }

    // Add user message to chat
    const userMessage: Message = {
      role: "user",
      content: message,
      documents: documents.length > 0 ? [...documents] : undefined,
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    // Clear documents after sending
    clearDocuments();

    try {
      // Call Gemini API using apiClient with interceptors
      const response = await apiClient.post("/api/gemini/chat", {
        message: message.trim(),
        documentIds: documentIds.length > 0 ? documentIds : undefined,
        taggedContracts: taggedContracts || undefined,
      });

      // Add assistant response to chat
      if (response && response.success && response.message) {
        const assistantMessage: Message = {
          id: response.id,
          role: response.message.role,
          content: response.message.content,
        };

        setMessages((prev) => [...prev, assistantMessage]);

        // Trigger contextual suggestions after the first user interaction
        triggerContextualSuggestions();
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error: any) {
      console.error("Error calling Gemini API:", error);

      // Determine the appropriate error message
      let errorContent =
        "I apologize, but I'm having trouble processing your request right now. Please try again in a moment, or feel free to rephrase your question.";

      if (error.response) {
        if (error.response.status === 413) {
          errorContent =
            "The documents are too large to process. Please try with smaller documents.";
        } else if (error.response.status === 404) {
          errorContent =
            "One or more documents could not be found. They may have been deleted or moved.";
        } else if (
          error.response.status === 504 ||
          (error.message && error.message.includes("timeout"))
        ) {
          errorContent =
            "The request timed out. This might be due to large documents or server load. Please try again with smaller documents.";
        } else if (error.response.data && error.response.data.message) {
          errorContent = `Error: ${error.response.data.message}`;
        }
      } else if (
        error.code === "ECONNABORTED" ||
        (error.message && error.message.includes("timeout"))
      ) {
        errorContent =
          "The request timed out. Document processing may take longer than expected. Please try with smaller documents.";
      } else if (!navigator.onLine) {
        errorContent =
          "You appear to be offline. Please check your internet connection and try again.";
      }

      toast.error("I'm having trouble responding right now");

      // Add error message to chat
      const errorMessage: Message = {
        role: "assistant",
        content: errorContent,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full h-full flex flex-col overflow-hidden">
      <CardHeader className="px-4 py-3 flex-shrink-0">
        <CardTitle>Contract Assistant</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto p-4 pb-0 h-[calc(100%-8rem)]">
        <div className="space-y-4">
          {messages.length === 0 && !isLoading ? (
            <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground">
              <p className="mb-2">
                I'm here to help you understand your contracts and make informed
                decisions.
              </p>
              <p className="text-sm">
                Upload documents or ask me about contract terms, risks,
                obligations, or any questions about your agreements.
              </p>
            </div>
          ) : (
            <>
              {messages.map((message, index) => (
                <div
                  key={message.id || index}
                  className={`flex ${
                    message.role === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`flex gap-3 max-w-[80%] ${
                      message.role === "user" ? "flex-row-reverse" : "flex-row"
                    }`}
                  >
                    <Avatar className="h-8 w-8">
                      {message.role === "user" ? (
                        <>
                          <AvatarImage src="/placeholder-user.jpg" />
                          <AvatarFallback>U</AvatarFallback>
                        </>
                      ) : (
                        <>
                          <AvatarImage src="/ai-avatar.png" />
                          <AvatarFallback>CA</AvatarFallback>
                        </>
                      )}
                    </Avatar>
                    <div
                      className={`rounded-lg ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground py-2 px-3"
                          : "bg-muted p-3"
                      }`}
                    >
                      {/* Document attachments */}
                      {message.documents && message.documents.length > 0 && (
                        <div className="mb-2 space-y-1">
                          {message.documents.map((doc) => (
                            <div
                              key={doc.id}
                              className="flex items-center gap-1.5 bg-background/20 rounded p-1 text-xs"
                            >
                              <FileText className="h-3 w-3" />
                              <span className="truncate max-w-[180px]">
                                {doc.name}
                              </span>
                              <Badge
                                variant="outline"
                                className="text-[10px] px-1 h-4 ml-auto"
                              >
                                {doc.size}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Message content */}
                      {message.role === "user" ? (
                        <p className="whitespace-pre-wrap text-primary-foreground leading-tight">
                          {message.content}
                        </p>
                      ) : (
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <ReactMarkdown>{message.content}</ReactMarkdown>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex gap-3 max-w-[80%] flex-row">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/ai-avatar.png" />
                      <AvatarFallback>CA</AvatarFallback>
                    </Avatar>
                    <div className="rounded-lg p-3 bg-muted">
                      <div className="flex flex-col gap-1.5 w-[200px]">
                        <div className="h-3 bg-muted-foreground/20 rounded animate-pulse" />
                        <div className="h-3 bg-muted-foreground/20 rounded animate-pulse w-5/6" />
                        <div className="h-3 bg-muted-foreground/20 rounded animate-pulse w-4/6" />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* This empty div is used for auto-scrolling */}
          <div ref={messagesEndRef} />
        </div>

        {/* Chat Suggestions */}
        {suggestions.length > 0 && (
          <div className="px-4 pb-3">
            <ChatSuggestions
              suggestions={suggestions}
              onSuggestionClick={handleSuggestionClick}
              isLoading={isLoading}
            />
          </div>
        )}
      </CardContent>
      <CardFooter className="p-4 pt-2 flex-shrink-0">
        <div className="w-full flex flex-col gap-2">
          {/* Document upload area */}
          {documents.length > 0 && (
            <div className="w-full mb-2">
              <ScrollArea className="h-auto max-h-[120px] w-full rounded-md border p-2">
                <div className="space-y-2">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between bg-muted/50 p-2 rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div className="flex flex-col">
                          <span className="text-sm font-medium truncate max-w-[200px]">
                            {doc.name}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {doc.size}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeDocument(doc.id)}
                        className="h-6 w-6"
                      >
                        <X className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              {documents.length > 0 && (
                <div className="flex justify-end mt-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearDocuments}
                    className="h-6 px-2 text-xs"
                  >
                    Clear all
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Document upload button and enhanced chat input */}
          <div className="flex gap-2 w-full">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading || uploadingDocuments}
              className="flex-shrink-0"
            >
              {uploadingDocuments ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Upload className="h-4 w-4" />
              )}
              <span className="sr-only">Upload document</span>
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileSelect}
              className="hidden"
              multiple
              accept=".pdf,.docx,.doc,.txt,.csv,.xlsx,.xls"
            />

            <div className="flex-1">
              <EnhancedChatInput
                onSendMessage={handleMessageSubmit}
                disabled={isLoading || uploadingDocuments}
                placeholder="Ask me about your contracts or upload documents for analysis..."
              />
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
