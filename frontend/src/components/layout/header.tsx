"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

const mainNavItems = [
  {
    title: "Home",
    href: "/home",
  },
  {
    title: "About",
    href: "/about",
  },
  {
    title: "Pricing",
    href: "/pricing",
  },
  {
    title: "Contact",
    href: "/contact",
  },
  // {
  //   title: "Resources",
  //   href: "/resources",
  //   children: [
  //     {
  //       title: "Blog",
  //       href: "/blog",
  //       description: "Latest articles and updates",
  //     },
  //     {
  //       title: "Documentation",
  //       href: "/documentation",
  //       description: "Detailed guides and API references",
  //     },
  //     {
  //       title: "Case Studies",
  //       href: "/case-studies",
  //       description: "Success stories from our customers",
  //     },
  //   ],
  // },
];

export function Header() {
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = React.useState(false);

  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-200",
        isScrolled
          ? "bg-background/95 backdrop-blur-sm shadow-sm border-b border-primary/10"
          : "bg-transparent"
      )}
    >
      <div className="container flex h-20 items-center justify-between py-4">
        <div className="flex items-center gap-6 md:gap-10">
          <Link href="/home" className="flex items-center space-x-2">
            <img src="/maitlogo.svg" alt="MAIT Logo" className="h-20 pt-2 w-auto" />
            <span className="bg-gradient-to-r from-primary to-secondary text-white text-xs font-semibold px-2 py-1 rounded-full">
              BETA
            </span>
          </Link>
          <nav className="hidden md:flex">
            <NavigationMenu>
              <NavigationMenuList>
                {mainNavItems.map((item) => (
                  <NavigationMenuItem key={item.title}>
                    <NavigationMenuLink
                      asChild
                      active={pathname === item.href}
                    >
                      <Link
                        href={item.href}
                        className={cn(
                          navigationMenuTriggerStyle(),
                          "bg-transparent hover:bg-primary/10 hover:text-primary",
                          pathname === item.href &&
                          "bg-primary/10 text-primary"
                        )}
                      >
                        {item.title}
                      </Link>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>
        </div>
        <div className="flex items-center gap-2">
          {/* <ThemeToggle /> */}
          <div className="hidden md:flex gap-2">
            <Link href="/auth/login">
              <Button
                variant="outline"
                className="border-primary/20 hover:border-primary/40 hover:bg-primary/5"
              >
                Sign In
              </Button>
            </Link>
            <Link href="/auth/register">
              <Button className="bg-primary hover:opacity-90 transition-all">
                Sign Up
              </Button>
            </Link>
          </div>
          <Sheet>
            <SheetTrigger asChild className="md:hidden">
              <Button
                variant="outline"
                size="icon"
                className="border-primary/20 hover:border-primary/40 hover:bg-primary/5"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="bg-gradient-to-br from-background to-background/95 backdrop-blur-sm border-l border-primary/10"
            >
              <div className="flex items-center mb-8">
                <Link href="/home" className="flex items-center space-x-2">
                  <span className="font-extrabold text-xl bg-clip-text text-transparent bg-gradient-to-r from-green-900 to-green-700 logo-text">
                    MAIT
                  </span>
                  <span className="bg-gradient-to-r from-primary to-secondary text-white text-xs font-semibold px-2 py-1 rounded-full">
                    BETA
                  </span>
                </Link>
              </div>
              <nav className="flex flex-col gap-4">
                {mainNavItems.map((item) => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={cn(
                      "text-lg font-semibold transition-colors hover:text-primary relative group",
                      pathname === item.href
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  >
                    {item.title}
                    <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-300"></span>
                  </Link>
                ))}
                <div className="flex flex-col gap-3 mt-6 pt-6 border-t border-primary/10">
                  <Link href="/auth/login">
                    <Button
                      variant="outline"
                      className="w-full border-primary/20 hover:border-primary/40 hover:bg-primary/5"
                    >
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth/register">
                    <Button className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
