/**
 * Document Actions Component
 * Provides actions for documents, including RAG integration
 */

import React from 'react';
import Link from 'next/link';
import { MessageSquareIcon, SearchIcon, DownloadIcon, ShareIcon } from 'lucide-react';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api-client';

interface DocumentActionsProps {
  documentId: string;
  documentType: 'contract' | 'license';
  documentName: string;
  onProcessComplete?: () => void;
}

export function DocumentActions({ 
  documentId, 
  documentType, 
  documentName,
  onProcessComplete
}: DocumentActionsProps) {
  // Process document for RAG
  const processForRAG = async () => {
    try {
      toast.loading('Processing document for AI...');
      
      // Get document text
      const response = await apiClient.get(`/api/${documentType}s/${documentId}/text`);
      const { text } = response;
      
      if (!text) {
        toast.error('No text content found in document');
        return;
      }
      
      // Process document based on type
      const endpoint = `/api/rag/process/${documentType}`;
      const payload = {
        [`${documentType}Id`]: documentId,
        text,
        metadata: {
          title: documentName,
          [`${documentType}Type`]: documentType
        }
      };
      
      await apiClient.post(endpoint, payload);
      
      toast.success('Document processed successfully');
      
      if (onProcessComplete) {
        onProcessComplete();
      }
    } catch (error) {
      console.error('Error processing document:', error);
      toast.error('Failed to process document');
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      {/* Chat with document */}
      <Link
        href={`/chat/document/${documentType}/${documentId}`}
        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
      >
        <MessageSquareIcon size={16} />
        <span>Chat with Document</span>
      </Link>
      
      {/* Search similar */}
      <Link
        href={`/search?documentId=${documentId}&documentType=${documentType}`}
        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors"
      >
        <SearchIcon size={16} />
        <span>Search Similar</span>
      </Link>
      
      {/* Process for RAG */}
      <button
        onClick={processForRAG}
        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
      >
        <SearchIcon size={16} />
        <span>Process for AI</span>
      </button>
      
      {/* Download */}
      <button
        onClick={() => toast.info('Download functionality not implemented')}
        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      >
        <DownloadIcon size={16} />
        <span>Download</span>
      </button>
      
      {/* Share */}
      <button
        onClick={() => toast.info('Share functionality not implemented')}
        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      >
        <ShareIcon size={16} />
        <span>Share</span>
      </button>
    </div>
  );
}
