/**
 * Clause Analysis Card Component
 * Displays AI-extracted clause analysis with confidence indicators and risk levels
 */

"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Scale,
  XCircle,
  Shield,
  Lock,
  Building2,
  AlertTriangle,
  CheckCircle,
  FileText,
  Gavel,
  CreditCard,
  ShieldAlert,
  Users,
  Mail,
  Zap,
} from "lucide-react";
import { FieldConfidenceIndicator } from "./ConfidenceIndicators";
import { getFieldConfidence } from "./ConfidenceIndicators";
import { SectionMetadataInfoTooltip } from "./MetadataInfoTooltip";
import { MetadataFieldKey } from "@/utils/metadata-display";
import { cn } from "@/lib/utils";
import {
  extractClauseAnalysis,
  getClauseStatistics,
  type ClauseAnalysis,
} from "@/utils/clause-analysis-utils";

interface ClauseAnalysisCardProps {
  metadata: any;
  className?: string;
  variant?: "default" | "contract-analysis" | "contract-management";
  showStatistics?: boolean;
  showSectionTooltip?: boolean;
  sectionFields?: MetadataFieldKey[];
  sectionName?: string;
  asAccordion?: boolean;
  accordionValue?: string;
}

// Category icon mapping
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    Payment: CreditCard,
    Termination: XCircle,
    Liability: Shield,
    Confidentiality: Lock,
    "Intellectual Property": Building2,
    "Dispute Resolution": Gavel,
    "Force Majeure": Zap,
    Warranties: CheckCircle,
    Assignment: Users,
    Notices: Mail,
    Others: FileText,
  };

  return iconMap[category] || FileText;
};

// Risk level styling
const getRiskLevelStyling = (riskLevel: string) => {
  switch (riskLevel?.toLowerCase()) {
    case "low":
      return {
        badge:
          "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        icon: CheckCircle,
        color: "text-green-600 dark:text-green-400",
      };
    case "medium":
      return {
        badge:
          "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        icon: AlertTriangle,
        color: "text-yellow-600 dark:text-yellow-400",
      };
    case "high":
      return {
        badge:
          "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        icon: ShieldAlert,
        color: "text-red-600 dark:text-red-400",
      };
    default:
      return {
        badge:
          "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        icon: FileText,
        color: "text-gray-600 dark:text-gray-400",
      };
  }
};

// Category color styling
const getCategoryStyling = (category: string) => {
  const colorMap: Record<string, string> = {
    Payment: "text-emerald-600 dark:text-emerald-400",
    Termination: "text-red-600 dark:text-red-400",
    Liability: "text-blue-600 dark:text-blue-400",
    Confidentiality: "text-purple-600 dark:text-purple-400",
    "Intellectual Property": "text-indigo-600 dark:text-indigo-400",
    "Dispute Resolution": "text-orange-600 dark:text-orange-400",
    "Force Majeure": "text-yellow-600 dark:text-yellow-400",
    Warranties: "text-green-600 dark:text-green-400",
    Assignment: "text-pink-600 dark:text-pink-400",
    Notices: "text-cyan-600 dark:text-cyan-400",
    Others: "text-gray-600 dark:text-gray-400",
  };

  return colorMap[category] || "text-gray-600 dark:text-gray-400";
};

export function ClauseAnalysisCard({
  metadata,
  className = "",
  variant = "default",
  showStatistics = true,
  showSectionTooltip = false,
  sectionFields = ["clauses_analysis"],
  sectionName = "Clause Analysis",
  asAccordion = false,
  accordionValue = "clause-analysis",
}: ClauseAnalysisCardProps) {
  // Extract clause analysis from metadata using utility function
  const clausesAnalysis = extractClauseAnalysis(metadata);

  // Get confidence score for clause analysis
  const confidence = getFieldConfidence(metadata, "clauses_analysis");

  // Get statistics for display
  const stats = getClauseStatistics(clausesAnalysis);

  if (clausesAnalysis.length === 0) {
    return null;
  }

  // Define variant-specific styling
  const getHeaderStyling = () => {
    switch (variant) {
      case "contract-management":
        return "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-[#09260D]/20 dark:to-[#09260D]/10 border-b border-green-200 dark:border-[#09260D]/30";
      case "contract-analysis":
        return ""; // No special header styling for contract analysis view
      default:
        return "";
    }
  };

  const getTitleStyling = () => {
    switch (variant) {
      case "contract-management":
        return "text-lg";
      case "contract-analysis":
        return ""; // Use default title styling
      default:
        return "";
    }
  };

  // Render content based on mode
  const renderContent = () => (
    <div className="space-y-3">
      {clausesAnalysis.map((clause: ClauseAnalysis, index: number) => {
        const CategoryIcon = getCategoryIcon(clause.category);
        const riskStyling = getRiskLevelStyling(clause.riskLevel);
        const RiskIcon = riskStyling.icon;
        const categoryStyling = getCategoryStyling(clause.category);

        const getClauseItemStyling = () => {
          switch (variant) {
            case "contract-management":
              return "border border-gray-200 dark:border-gray-700 rounded-lg p-3 space-y-2 bg-gray-50/50 dark:bg-gray-900/20";
            case "contract-analysis":
              return "border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3 bg-muted/30";
            default:
              return "border border-gray-200 dark:border-gray-700 rounded-lg p-3 space-y-2";
          }
        };

        return (
          <div key={index} className={getClauseItemStyling()}>
            {/* Header with category, title, and risk level */}
            <div className="flex items-start justify-between gap-3">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <CategoryIcon
                  className={cn("h-4 w-4 flex-shrink-0", categoryStyling)}
                />
                <div className="min-w-0 flex-1">
                  <h4 className="font-semibold text-sm text-foreground truncate">
                    {clause.title}
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    {clause.category}
                  </p>
                </div>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={cn(
                        "flex items-center gap-1 text-xs",
                        riskStyling.badge
                      )}
                    >
                      <RiskIcon className="h-3 w-3" />
                      {clause.riskLevel}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent className="bg-primary text-primary-foreground">
                    <p>Risk Level: {clause.riskLevel}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Summary */}
            <p className="text-sm text-muted-foreground leading-relaxed">
              {clause.summary}
            </p>

            {/* Key Points */}
            {clause.keyPoints && clause.keyPoints.length > 0 && (
              <div className="space-y-1">
                <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Key Points
                </p>
                <ul className="space-y-1">
                  {clause.keyPoints.map((point: string, pointIndex: number) => (
                    <li
                      key={pointIndex}
                      className="text-sm text-foreground flex items-start gap-2"
                    >
                      <span className="text-green-600 dark:text-green-400 mt-1">
                        •
                      </span>
                      <span className="flex-1">{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Original Text */}
            {clause.originalText && (
              <div className="space-y-1">
                <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Original Text
                </p>
                <blockquote className="text-xs text-muted-foreground italic border-l-2 border-green-600 dark:border-green-400 pl-3 py-1 bg-gray-50 dark:bg-gray-900/50 rounded-r">
                  "{clause.originalText}"
                </blockquote>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  // Render as accordion or card based on mode
  if (asAccordion) {
    return (
      <AccordionItem
        value={accordionValue}
        className={cn("border rounded-lg", className)}
      >
        <AccordionTrigger className="px-4 py-3 hover:no-underline">
          <div className="flex items-center gap-2 w-full">
            <Scale className="h-5 w-5 text-green-600" />
            <span className="font-medium">Clause Analysis</span>
            {/* <FieldConfidenceIndicator
              confidence={confidence}
              field="clauses_analysis"
              variant="badge"
              showPercentage={true}
              className="text-xs"
            /> */}
            {showSectionTooltip && (
              <SectionMetadataInfoTooltip
                metadata={metadata}
                sectionFields={sectionFields}
                sectionName={sectionName}
                position="header"
              />
            )}
            {showStatistics && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground ml-auto">
                <span>{stats.total} clauses</span>
                {stats.byRiskLevel.High > 0 && (
                  <span className="text-red-600 dark:text-red-400">
                    {stats.byRiskLevel.High} high risk
                  </span>
                )}
              </div>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4">
          {renderContent()}
        </AccordionContent>
      </AccordionItem>
    );
  }

  // Render as card (original mode)
  return (
    <Card className={cn("relative", className)}>
      <CardHeader className={getHeaderStyling()}>
        <CardTitle className={cn("flex items-center gap-2", getTitleStyling())}>
          <Scale className="h-5 w-5 text-green-600" />
          Clause Analysis
          <FieldConfidenceIndicator
            confidence={confidence}
            field="clauses_analysis"
            variant="badge"
            showPercentage={true}
            className="text-xs"
          />
          {showSectionTooltip && (
            <SectionMetadataInfoTooltip
              metadata={metadata}
              sectionFields={sectionFields}
              sectionName={sectionName}
              position="header"
            />
          )}
        </CardTitle>
        {showStatistics && (
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mt-2">
            <span className="font-medium">{stats.total} clauses analyzed</span>
            {stats.byRiskLevel.High > 0 && (
              <span className="text-red-600 dark:text-red-400 font-medium">
                {stats.byRiskLevel.High} high risk
              </span>
            )}
            {stats.byRiskLevel.Medium > 0 && (
              <span className="text-yellow-600 dark:text-yellow-400 font-medium">
                {stats.byRiskLevel.Medium} medium risk
              </span>
            )}
            {stats.byRiskLevel.Low > 0 && (
              <span className="text-green-600 dark:text-green-400 font-medium">
                {stats.byRiskLevel.Low} low risk
              </span>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent className="pt-4">{renderContent()}</CardContent>
    </Card>
  );
}
