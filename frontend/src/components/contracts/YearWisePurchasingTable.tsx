/**
 * Year-wise Purchasing Table Component
 * Displays purchasing data with year-based columns and SKU-based rows
 */

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatContractValue } from "@/lib/format-utils";

interface PurchasingItem {
  [key: string]: string;
}

interface YearWisePurchasingData {
  [year: string]: PurchasingItem[];
}

interface YearWisePurchasingTableProps {
  contractName: string;
  purchasingData: YearWisePurchasingData;
  className?: string;
}

interface SKURow {
  skuId: string;
  yearData: {
    [year: string]: {
      quantity: string;
      unitPrice: string;
      contractValue: string;
    };
  };
}

/**
 * Transform year-based purchasing data into SKU-grouped structure
 * Deduplicates based on license_type + license_value combination
 * Shows all SKUs sorted by average total value (highest first)
 */
function transformPurchasingData(purchasingData: YearWisePurchasingData): SKURow[] {
  const skuMap = new Map<string, SKURow>();

  Object.entries(purchasingData).forEach(([year, items]) => {
    items.forEach((item) => {
      // Try different possible field names for SKU/product identification
      const licenseType = item.license_type || item.product_name || item.sku || item.item_name || "Unknown SKU";
      const licenseValue = item.license_value || item.unit_price || item.price || "";

      // Create unique key combining license type and value to prevent duplicates
      const uniqueKey = `${licenseType}|${licenseValue}`;

      if (!skuMap.has(uniqueKey)) {
        skuMap.set(uniqueKey, {
          skuId: licenseType, // Display name remains the license type
          yearData: {}
        });
      }

      const sku = skuMap.get(uniqueKey)!;

      // If this year already exists for this SKU, aggregate the values
      if (sku.yearData[year]) {
        // Sum quantities if both are numeric
        const existingQty = parseFloat(sku.yearData[year].quantity) || 0;
        const newQty = parseFloat(item.quantity || item.qty || "0") || 0;
        const totalQty = existingQty + newQty;

        // Sum contract values if both are numeric or in currency format
        const existingValue = sku.yearData[year].contractValue;
        const newValue = item.price || item.total_price || item.contract_value || "N/A";
        let totalValue = "N/A";

        if (existingValue !== "N/A" && newValue !== "N/A") {
          // Handle currency format (e.g., "USD:50000")
          const parseValue = (val: string) => {
            if (val.includes(':')) {
              return parseFloat(val.split(':')[1]) || 0;
            }
            return parseFloat(val) || 0;
          };

          const existingAmount = parseValue(existingValue);
          const newAmount = parseValue(newValue);
          const totalAmount = existingAmount + newAmount;

          // Preserve currency format if original had it
          if (existingValue.includes(':')) {
            const currency = existingValue.split(':')[0];
            totalValue = `${currency}:${totalAmount}`;
          } else {
            totalValue = totalAmount.toString();
          }
        }

        sku.yearData[year] = {
          quantity: totalQty > 0 ? totalQty.toString() : "N/A",
          unitPrice: licenseValue || "N/A",
          contractValue: totalValue
        };
      } else {
        sku.yearData[year] = {
          quantity: item.quantity || item.qty || "N/A",
          unitPrice: licenseValue || "N/A",
          contractValue: item.price || item.total_price || item.contract_value || "N/A"
        };
      }
    });
  });

  // Calculate average total value for each SKU and sort by it (but show all SKUs)
  const skuRows = Array.from(skuMap.values()).map(sku => {
    const values: number[] = [];

    Object.values(sku.yearData).forEach(yearData => {
      if (yearData.contractValue && yearData.contractValue !== "N/A") {
        const parseValue = (val: string) => {
          if (val.includes(':')) {
            return parseFloat(val.split(':')[1]) || 0;
          }
          return parseFloat(val) || 0;
        };

        const value = parseValue(yearData.contractValue);
        if (value > 0) {
          values.push(value);
        }
      }
    });

    const averageValue = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;

    return {
      ...sku,
      averageValue
    };
  });

  // Sort by average value (descending) and return ALL SKUs
  return skuRows
    .sort((a, b) => b.averageValue - a.averageValue)
    .map(({ averageValue, ...sku }) => sku); // Remove the temporary averageValue property
}

/**
 * Year-wise Purchasing Table Component
 */
export function YearWisePurchasingTable({
  contractName,
  purchasingData,
  className = ""
}: YearWisePurchasingTableProps) {
  // Transform data into SKU-grouped structure
  const skuRows = transformPurchasingData(purchasingData);
  const years = Object.keys(purchasingData).sort((a, b) => {
    // Sort TOTAL last, otherwise sort alphabetically
    if (a === "TOTAL") return 1;
    if (b === "TOTAL") return -1;
    return a.localeCompare(b);
  });

  if (skuRows.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Year-wise Purchasing Breakdown</CardTitle>
          <p className="text-sm text-muted-foreground">{contractName}</p>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            No purchasing data available
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Year-wise Purchasing Breakdown</CardTitle>
        <p className="text-sm text-muted-foreground">{contractName}</p>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto border rounded-lg">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border border-border p-2 bg-muted text-left font-medium w-[250px] min-w-[200px]">
                  SKU / Product
                </th>
                {years.map((year) => (
                  <th
                    key={year}
                    className="border border-border bg-primary text-primary-foreground text-center font-medium text-sm px-1"
                    colSpan={3}
                  >
                    {year}
                  </th>
                ))}
              </tr>
              <tr>
                <th className="border border-border p-1 bg-muted"></th>
                {years.map((year) => (
                  <React.Fragment key={year}>
                    <th className="border border-border p-1 bg-muted/50 text-xs font-medium text-center min-w-[60px]">
                      Qty
                    </th>
                    <th className="border border-border p-1 bg-muted/50 text-xs font-medium text-center min-w-[80px]">
                      Unit Price
                    </th>
                    <th className="border border-border p-1 bg-muted/50 text-xs font-medium text-center min-w-[90px]">
                      Value
                    </th>
                  </React.Fragment>
                ))}
              </tr>
            </thead>
            <tbody>
              {skuRows.map((sku, index) => (
                <tr key={sku.skuId} className={index % 2 === 0 ? "bg-background" : "bg-muted/20"}>
                  <td className="border border-border p-2 font-medium w-[250px] min-w-[200px]">
                    <div className="max-w-[240px] break-words">
                      <div
                        className="font-medium text-sm leading-tight"
                        title={sku.skuId}
                      >
                        {sku.skuId}
                      </div>
                    </div>
                  </td>
                  {years.map((year) => {
                    const yearData = sku.yearData[year];
                    return (
                      <React.Fragment key={year}>
                        <td className="border border-border p-1 text-center text-xs">
                          {yearData?.quantity || "-"}
                        </td>
                        <td className="border border-border p-1 text-center text-xs font-mono">
                          {yearData?.unitPrice ? (
                            yearData.unitPrice.includes(':') ?
                              formatContractValue(yearData.unitPrice, undefined, true) :
                              yearData.unitPrice
                          ) : "-"}
                        </td>
                        <td className="border border-border p-1 text-center text-xs font-mono font-medium">
                          {yearData?.contractValue ? (
                            yearData.contractValue.includes(':') ?
                              formatContractValue(yearData.contractValue, undefined, true) :
                              yearData.contractValue
                          ) : "-"}
                        </td>
                      </React.Fragment>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
