"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Minus,
  Info,
  Shield,
  AlertCircle,
  FileWarning,
} from "lucide-react";
import { IntegrityAnalysisResult, IntegrityService, IntegrityConfiguration } from "@/services/integrityService";

interface IntegrityResultsDisplayProps {
  analysis: IntegrityAnalysisResult;
  className?: string;
}

export function IntegrityResultsDisplay({
  analysis,
  className = "",
}: IntegrityResultsDisplayProps) {
  const [activeView, setActiveView] = useState<'clauses' | 'risks'>('clauses');

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case "Low":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Medium":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "High":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };



  const getProgressColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "Low":
        return "bg-green-500";
      case "Medium":
        return "bg-yellow-500";
      case "High":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const clauseEntries = Object.entries(analysis.clauses);
  const foundClauses = clauseEntries.filter(([_, clause]) => clause.value !== "N/A");
  const notFoundClauses = clauseEntries.filter(([_, clause]) => clause.value === "N/A");

  return (
    <div className={`space-y-6 pt-4 ${className}`}>
      {/* Combined Summary and Risk Distribution */}
      <Card>
        {/* <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            Integrity Analysis Summary
          </CardTitle>
        </CardHeader> */}
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Overall Integrity Score */}
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-3 mb-3">
                  {/* {getRiskIcon(analysis.riskLevel)} */}
                  <span className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                    {Math.round((analysis.overallRiskScore / analysis.maxPossibleScore) * 100)}%
                  </span>
                </div>
                {/* <div className="text-lg font-medium text-gray-600 dark:text-gray-300 mb-3">
                  Integrity Score
                </div> */}
                <Badge
                  variant="outline"
                  className={`${IntegrityService.getRiskLevelColor(analysis.riskLevel)} text-sm px-4 py-1`}
                >
                  {analysis.riskLevel} Risk
                </Badge>
              </div>
              <div className="w-48">
                <Progress
                  value={(analysis.overallRiskScore / analysis.maxPossibleScore) * 100}
                  className={`h-3 bg-gray-200 dark:bg-gray-700 ${analysis.riskLevel === 'Low' ? '[&>div]:bg-green-500' :
                    analysis.riskLevel === 'Medium' ? '[&>div]:bg-yellow-500' :
                      '[&>div]:bg-red-500'
                    }`}
                />
              </div>
            </div>

            {/* Risk Distribution */}
            <div className="flex flex-col justify-center">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 text-center">
                Risk Distribution
              </h4>
              <div className="space-y-2">
                {['High', 'Medium', 'Low'].map((riskLevel) => {
                  const count = clauseEntries.filter(([_, clause]) => clause.riskLevel === riskLevel).length;
                  const percentage = clauseEntries.length > 0 ? (count / clauseEntries.length) * 100 : 0;

                  return (
                    <div key={riskLevel} className="flex items-center justify-between space-x-4">
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          {getRiskIcon(riskLevel)}
                          <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                            {count}
                          </span>
                        </div>
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {riskLevel}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[3rem] text-right">
                          {percentage.toFixed(0)}%
                        </span>
                        <div className="w-24">
                          <Progress
                            value={percentage}
                            className="h-2 bg-gray-200 dark:bg-gray-700"
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Toggle Switch */}
      <div className="flex justify-center">
        <div className="inline-flex rounded-lg border border-border bg-background p-1">
          <Button
            variant={activeView === 'clauses' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveView('clauses')}
            className="flex items-center gap-2"
          >
            <Shield className="h-4 w-4" />
            Integrity Clauses
          </Button>
          <Button
            variant={activeView === 'risks' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveView('risks')}
            className="flex items-center gap-2"
          >
            <AlertTriangle className="h-4 w-4" />
            Document Risks
          </Button>
        </div>
      </div>

      {/* Clause Analysis Results */}
      {activeView === 'clauses' && (
        <Card>
          {/* <CardHeader>
          <CardTitle>Clause Analysis Details</CardTitle>
        </CardHeader> */}
          <CardContent className="pt-4">
            <div className="space-y-4">
              {/* Found Clauses */}
              {foundClauses.length > 0 && (
                <div>
                  <h4 className="font-semibold text-green-700 mb-3 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Clauses Found ({foundClauses.length})
                  </h4>
                  <div className="space-y-3">
                    {foundClauses.map(([clauseId, clause]) => (
                      <div
                        key={clauseId}
                        className="border rounded-lg p-4 bg-green-50 border-green-200"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="cursor-help">
                                      <Info className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent side="top" className="max-w-sm bg-primary text-primary-foreground">
                                    <div className="text-xs whitespace-pre-wrap break-words">
                                      {clause.description}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <h5 className="font-medium">{clause.clauseName}</h5>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {clause.value}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center justify-end gap-2 mb-1">
                              {getRiskIcon(clause.riskLevel)}
                              {/* <Badge
                              variant="outline"
                              className={IntegrityService.getRiskLevelColor(clause.riskLevel)}
                            >
                              {clause.riskLevel}
                            </Badge> */}
                            </div>
                            <div className="text-sm font-medium">
                              {clause.riskScore}/{clause.maxScore} points
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(clause.confidence * 100).toFixed(0)}% confidence
                            </div>
                          </div>
                        </div>
                        <Progress
                          value={(clause.riskScore / clause.maxScore) * 100}
                          className="h-1"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {foundClauses.length > 0 && notFoundClauses.length > 0 && (
                <Separator />
              )}

              {/* Not Found Clauses */}
              {notFoundClauses.length > 0 && (
                <div>
                  <h4 className="font-semibold text-red-700 mb-3 flex items-center gap-2">
                    <XCircle className="h-4 w-4" />
                    Clauses Missing ({notFoundClauses.length})
                  </h4>
                  <div className="space-y-3">
                    {notFoundClauses.map(([clauseId, clause]) => (
                      <div
                        key={clauseId}
                        className="border rounded-lg p-4 bg-red-50 border-red-200"
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h5 className="font-medium">{clause.clauseName}</h5>
                            <p className="text-sm text-muted-foreground">
                              {clause.description}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center justify-end gap-2 mb-1">
                              {getRiskIcon(clause.riskLevel)}
                              {/* <Badge
                              variant="outline"
                              className={IntegrityService.getRiskLevelColor(clause.riskLevel)}
                            >
                              {clause.riskLevel}
                            </Badge> */}
                            </div>
                            <div className="text-sm font-medium">
                              {clause.riskScore}/{clause.maxScore} points
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(clause.confidence * 100).toFixed(0)}% confidence
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Risks View */}
      {activeView === 'risks' && (
        <Card>
          <CardContent className="pt-4">
            {analysis.risks && analysis.risks.length > 0 ? (
              <div className="space-y-4">
                <h4 className="font-semibold text-orange-700 mb-3 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Document Risks ({analysis.risks.length})
                </h4>
                <div className="space-y-4">
                  {analysis.risks.map((risk) => (
                    <div
                      key={risk.id}
                      className={`border rounded-lg p-4 ${risk.severity === 'Critical' ? 'bg-red-50 border-red-200' :
                        risk.severity === 'High' ? 'bg-orange-50 border-orange-200' :
                          risk.severity === 'Medium' ? 'bg-yellow-50 border-yellow-200' :
                            'bg-blue-50 border-blue-200'
                        }`}
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h5 className="font-medium text-lg">{risk.title}</h5>
                            <Badge
                              variant="outline"
                              className={`${risk.severity === 'Critical' ? 'border-red-500 text-red-700 bg-red-50' :
                                risk.severity === 'High' ? 'border-orange-500 text-orange-700 bg-orange-50' :
                                  risk.severity === 'Medium' ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                    'border-blue-500 text-blue-700 bg-blue-50'
                                }`}
                            >
                              {risk.severity}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {risk.category}
                            </Badge>
                            {/* <Badge variant="default" className="text-xs">
                              {(risk.confidence * 100).toFixed(0)}%
                            </Badge> */}

                            {/* <div className="text-right ml-4">
                          <div className="text-xs text-muted-foreground">
                            {(risk.confidence * 100).toFixed(0)}% confidence
                          </div>
                        </div> */}
                          </div>
                          <p className="text-sm text-gray-700 mb-3">{risk.description}</p>

                          <div className="space-y-2">
                            <div>
                              <span className="font-medium text-sm text-red-600">Impact:</span>
                              <p className="text-sm text-gray-600 mt-1">{risk.impact}</p>
                            </div>
                            <div>
                              <span className="font-medium text-sm text-green-800">Mitigation:</span>
                              <p className="text-sm text-gray-600 mt-1">{risk.mitigation}</p>
                            </div>
                          </div>
                        </div>

                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No Risks Identified
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  No significant risks were identified in this contract analysis.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
