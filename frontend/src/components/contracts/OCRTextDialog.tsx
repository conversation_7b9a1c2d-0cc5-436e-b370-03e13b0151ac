/**
 * OCR Text Dialog Component
 * Displays the extracted OCR text for a contract
 */

"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  FileText,
  Copy,
  Download,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2,
  LetterText
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";

interface OCRData {
  text: string;
  confidence: number;
  pageCount: number;
  processingTimeMs: number;
  processedAt: string;
  status: string;
  ocrUsedForExtraction: boolean;
}

interface OCRTextDialogProps {
  contractId: string;
}

export function OCRTextDialog({ contractId }: OCRTextDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [ocrData, setOcrData] = useState<OCRData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch OCR data when dialog opens
  useEffect(() => {
    if (isOpen && !ocrData && !loading) {
      fetchOCRData();
    }
  }, [isOpen]);

  const fetchOCRData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(`/api/contracts/${contractId}/ocr-text`);

      console.log("OCR API Response:", response.data); // Debug log

      if (response.data) {
        // Ensure all required fields are present with defaults
        const ocrData = {
          text: response.data.text || "",
          confidence: response.data.confidence || 0,
          pageCount: response.data.pageCount || 1,
          processingTimeMs: response.data.processingTimeMs || 0,
          processedAt: response.data.processedAt || new Date().toISOString(),
          status: response.data.status || "SUCCESS",
          ocrUsedForExtraction: response.data.ocrUsedForExtraction || false
        };

        console.log("Processed OCR Data:", ocrData); // Debug log
        setOcrData(ocrData);
      } else {
        setError(response.data.message || "Failed to fetch OCR data");
      }
    } catch (err: any) {
      console.error("Error fetching OCR data:", err);
      if (err.response?.status === 404) {
        setError("OCR text not available for this contract");
      } else {
        setError(err.response?.data?.message || "Failed to fetch OCR data");
      }
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async () => {
    if (!ocrData?.text) return;

    try {
      await navigator.clipboard.writeText(ocrData.text);
      toast.success("OCR text copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy text");
    }
  };

  const downloadAsText = () => {
    if (!ocrData?.text) return;

    const blob = new Blob([ocrData.text], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `contract-${contractId}-ocr-text.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("OCR text downloaded");
  };

  const getStatusIcon = () => {
    if (!ocrData) return <Clock className="h-4 w-4" />;

    switch (ocrData.status) {
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "FAILED":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = () => {
    if (!ocrData) return "bg-gray-100 text-gray-800";

    switch (ocrData.status) {
      case "SUCCESS":
        return "bg-green-100 text-green-800";
      case "FAILED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  const formatConfidence = (confidence: number) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  const formatProcessingTime = (timeMs: number) => {
    if (timeMs < 1000) return `${timeMs}ms`;
    return `${(timeMs / 1000).toFixed(1)}s`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          title="View OCR Extracted Text"
          className="text-green-600 border-green-600 hover:bg-green-50"

        >
          <LetterText className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                OCR Extracted Text
              </DialogTitle>
              <DialogDescription>
                Text extracted from the contract document using Azure OCR
              </DialogDescription>
            </div>
            {/* Action Buttons in Top Right */}
            {ocrData?.status === "SUCCESS" && ocrData.text && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadAsText}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            )}
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading OCR data...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchOCRData}
                className="text-green-600 border-green-600 hover:bg-green-50"
              >
                Retry
              </Button>
            </div>
          </div>
        ) : ocrData ? (
          <div className="flex flex-col space-y-4 flex-1 min-h-0">
            {/* OCR Metadata */}
            {/* <div className="flex flex-wrap items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon()}
                <Badge className={getStatusColor()}>
                  {ocrData.status}
                </Badge>
              </div>

              {ocrData.status === "SUCCESS" && (
                <>
                  <Badge variant="outline">
                    Confidence: {formatConfidence(ocrData.confidence)}
                  </Badge>
                  <Badge variant="outline">
                    {ocrData.pageCount} page{ocrData.pageCount !== 1 ? 's' : ''}
                  </Badge>
                  <Badge variant="outline">
                    Processed in {formatProcessingTime(ocrData.processingTimeMs)}
                  </Badge>
                  <Badge variant="outline">
                    {formatDate(ocrData.processedAt)}
                  </Badge>
                  {ocrData.ocrUsedForExtraction && (
                    <Badge className="bg-blue-100 text-blue-800">
                      Used for AI Extraction
                    </Badge>
                  )}
                </>
              )}
            </div> */}



            {/* OCR Text Content */}
            {ocrData.status === "SUCCESS" && ocrData.text ? (
              <ScrollArea className="flex-1 border rounded-lg p-4 bg-white">
                <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed">
                  {ocrData.text}
                </pre>
              </ScrollArea>
            ) : (
              <div className="flex items-center justify-center py-8 border rounded-lg bg-gray-50">
                <div className="text-center">
                  <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No OCR text available</p>
                  <p className="text-sm text-gray-500">
                    {ocrData.status === "FAILED"
                      ? "OCR processing failed for this document"
                      : "OCR text not found for this contract"
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">No OCR data found</p>
              <p className="text-sm text-gray-500">
                This contract may not have been processed with OCR
              </p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
