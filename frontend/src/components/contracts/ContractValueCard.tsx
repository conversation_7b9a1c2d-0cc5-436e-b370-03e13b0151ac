import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { DollarSign } from "lucide-react";
import { ContractValueCard as ContractValueCardType } from "@/services/contractEntitlementService";

interface ContractValueCardProps {
  contractValue: ContractValueCardType;
}

export const ContractValueCard: React.FC<ContractValueCardProps> = ({
  contractValue,
}) => {
  const formatValue = (value: string, currency: string) => {
    if (value === "N/A" || !value) return { currency: "N/A", amount: "" };

    // Handle currency:amount format
    if (value.includes(":")) {
      const [curr, amount] = value.split(":");
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        return {
          currency: curr,
          amount: Math.round(numAmount).toLocaleString()
        };
      }
    }

    // Handle plain number
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      return {
        currency: currency,
        amount: Math.round(numValue).toLocaleString()
      };
    }

    return { currency: value, amount: "" };
  };

  const getValueColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    return "text-gray-900 dark:text-gray-100";
  };

  return (
    <Card className="h-full border-0 shadow-sm bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-green-100 dark:bg-green-900/30">
              <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Contract Value
            </span>
          </div>

          {/* Value */}
          <div>
            <div className={`text-xl font-bold pt-3 ${getValueColor(contractValue.value)}`}>
              {formatValue(contractValue.value, contractValue.currency).currency}
              {formatValue(contractValue.value, contractValue.currency).amount && (
                <span className="ml-1">
                  {formatValue(contractValue.value, contractValue.currency).amount}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
