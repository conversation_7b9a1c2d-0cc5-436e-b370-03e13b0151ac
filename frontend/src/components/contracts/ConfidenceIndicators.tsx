/**
 * Confidence Indicators Components
 * Displays confidence scores for AI-extracted metadata
 */

"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { TrendingUp, AlertTriangle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ConfidenceScore {
  value: number; // 0-1 or 0-100
  field?: string;
  source?: string;
}

interface FieldConfidenceIndicatorProps {
  confidence: number; // 0-1 (will be converted to percentage)
  field?: string;
  className?: string;
  showPercentage?: boolean;
  variant?: "progress" | "badge";
}

interface AggregatedConfidenceScoreProps {
  scores: ConfidenceScore[];
  className?: string;
}

// Helper function to get confidence level and color
const getConfidenceLevel = (confidence: number) => {
  // Handle manually edited fields
  if (confidence === -1) {
    return {
      level: "manual",
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
      progressColor: "bg-blue-600",
      icon: CheckCircle,
      label: "Manual",
    };
  }

  const percentage = confidence <= 1 ? confidence * 100 : confidence;

  if (percentage >= 80) {
    return {
      level: "high",
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/30",
      progressColor: "bg-green-600",
      icon: CheckCircle,
      label: "High Confidence",
    };
  } else if (percentage >= 50) {
    return {
      level: "medium",
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
      progressColor: "bg-yellow-600",
      icon: AlertTriangle,
      label: "Medium Confidence",
    };
  } else {
    return {
      level: "low",
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900/30",
      progressColor: "bg-red-600",
      icon: TrendingUp,
      label: "Low Confidence",
    };
  }
};

// Helper function to get tooltip content
const getTooltipContent = (confidence: number, field?: string) => {
  // Handle manually edited fields
  if (confidence === -1) {
    return (
      <div className="text-center">
        <p className="font-medium">Manual</p>
        <p className="text-xs opacity-90">This field was manually edited</p>
        {field && <p className="text-xs opacity-75 mt-1">Field: {field}</p>}
        <p className="text-xs opacity-75 mt-1">Manually verified data</p>
      </div>
    );
  }

  const percentage = confidence <= 1 ? confidence * 100 : confidence;
  const { label } = getConfidenceLevel(confidence);

  return (
    <div className="text-center">
      <p className="font-medium">{label}</p>
      <p className="text-xs opacity-90">{percentage.toFixed(1)}% confidence</p>
      {field && <p className="text-xs opacity-75 mt-1">Field: {field}</p>}
      <p className="text-xs opacity-75 mt-1">
        {percentage >= 80
          ? "AI is highly confident in this extraction"
          : percentage >= 50
            ? "AI has moderate confidence - may need review"
            : "AI has low confidence - manual verification recommended"}
      </p>
    </div>
  );
};

/**
 * Individual field confidence indicator
 */
export function FieldConfidenceIndicator({
  confidence,
  field,
  className = "",
  showPercentage = true,
  variant = "progress",
}: FieldConfidenceIndicatorProps) {
  const percentage =
    confidence === -1 ? 0 : confidence <= 1 ? confidence * 100 : confidence;
  const {
    color,
    bgColor,
    progressColor,
    icon: Icon,
    label,
  } = getConfidenceLevel(confidence);

  if (variant === "badge") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="outline"
              className={cn(
                "text-xs font-medium border cursor-help",
                bgColor,
                color,
                className
              )}
            >
              <Icon className="h-3 w-3 mr-1" />
              {showPercentage && confidence !== -1
                ? `${percentage.toFixed(0)}%`
                : label}
            </Badge>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            {getTooltipContent(confidence, field)}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn("flex items-center gap-2 cursor-help", className)}>
            <div className="flex-1">
              <Progress
                value={confidence === -1 ? 100 : percentage}
                className="h-2"
                style={
                  {
                    "--progress-background": progressColor,
                  } as React.CSSProperties
                }
              />
            </div>
            <div
              className={cn(
                "flex items-center gap-1 text-xs font-medium",
                color
              )}
            >
              <Icon className="h-3 w-3" />
              {showPercentage && confidence !== -1 && (
                <span>{percentage.toFixed(0)}%</span>
              )}
              {confidence === -1 && <span>{label}</span>}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          {getTooltipContent(confidence, field)}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Aggregated confidence score for the entire metadata panel
 */
export function AggregatedConfidenceScore({
  scores,
  className = "",
}: AggregatedConfidenceScoreProps) {
  // Calculate weighted average confidence
  const totalScore = scores.reduce((sum, score) => sum + score.value, 0);
  const averageConfidence = scores.length > 0 ? totalScore / scores.length : 0;
  const percentage =
    averageConfidence <= 1 ? averageConfidence * 100 : averageConfidence;

  const {
    color,
    bgColor,
    progressColor,
    icon: Icon,
    label,
  } = getConfidenceLevel(averageConfidence);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card className={cn("cursor-help", className)}>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Icon className={cn("h-5 w-5", color)} />
                <h3 className="font-semibold text-sm m-0">
                  Overall Confidence
                </h3>
                <Badge
                  variant="outline"
                  className={cn("font-medium text-xs", bgColor, color)}
                >
                  {percentage.toFixed(0)}%
                </Badge>
              </div>

              <div className="space-y-2">
                <Progress
                  value={percentage}
                  className="h-3"
                  style={
                    {
                      "--progress-background": progressColor,
                    } as React.CSSProperties
                  }
                />

                {/* <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{label}</span>
                  <span>{scores.length} fields analyzed</span>
                </div> */}
              </div>
            </CardContent>
          </Card>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="max-w-sm bg-white text-primary shadow-md"
        >
          <div>
            <p className="font-medium mb-2">Confidence Scale</p>
            <div className="space-y-1 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-200 rounded-full"></div>
                <span>High: 80% - 100%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-200 rounded-full"></div>
                <span>Medium: 50% - 79%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-200 rounded-full"></div>
                <span>Low: 0% - 49%</span>
              </div>
            </div>
            <p className="text-xs opacity-75 mt-2">
              Based on AI self-evaluation of extracted metadata fields
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Helper function to extract confidence scores from contract metadata
export function extractConfidenceScores(metadata: any): ConfidenceScore[] {
  const scores: ConfidenceScore[] = [];

  if (!metadata) return scores;

  // Check if we have confidence_scores at the root level (new flattened structure)
  if (metadata.confidence_scores) {
    Object.entries(metadata.confidence_scores).forEach(([key, value]) => {
      if (typeof value === "number") {
        // Extract field name from confidence score key (remove '_score' suffix)
        const fieldName = key.replace("_score", "");
        scores.push({
          value: value as number,
          field: fieldName,
          source: "AI Analysis",
        });
      }
    });
    return scores;
  }

  // Fallback: Check in autoExtractedFields for backward compatibility
  if (metadata.autoExtractedFields) {
    const fields = metadata.autoExtractedFields;
    const rawResult = fields.rawResult || {};

    // Check if we have the nested confidence_scores structure in autoExtractedFields
    if (fields.confidence_scores) {
      Object.entries(fields.confidence_scores).forEach(([key, value]) => {
        if (typeof value === "number") {
          // Extract field name from confidence score key (remove '_score' suffix)
          const fieldName = key.replace("_score", "");
          scores.push({
            value: value as number,
            field: fieldName,
            source: "AI Analysis",
          });
        }
      });
      return scores;
    }

    // Extract confidence from various field types with enhanced mapping
    const fieldMappings = [
      { field: "title", paths: ["title", "title_confidence"] },
      {
        field: "contractType",
        paths: ["agreement_type", "agreement_type_confidence"],
      },
      {
        field: "contractValue",
        paths: ["total_amount", "total_amount_confidence"],
      },
      { field: "provider", paths: ["provider", "provider_confidence"] },
      { field: "client", paths: ["client", "client_confidence"] },
      {
        field: "effectiveDate",
        paths: ["effective_date", "effective_date_confidence"],
      },
      { field: "endDate", paths: ["end_date", "end_date_confidence"] },
      { field: "contractId", paths: ["contract_id", "contract_id_confidence"] },
      {
        field: "paymentTerms",
        paths: ["payment_terms", "payment_terms_confidence"],
      },
      {
        field: "governingLaw",
        paths: ["governing_law", "governing_law_confidence"],
      },
      {
        field: "renewalTerms",
        paths: ["renewal_terms", "renewal_terms_confidence"],
      },
      { field: "parties", paths: ["parties", "parties_confidence"] },
      { field: "keyTerms", paths: ["keyTerms", "key_terms_confidence"] },
    ];

    fieldMappings.forEach(({ field, paths }) => {
      const [valuePath, confidencePath] = paths;

      // Check if the field has a value (from rawResult or fields)
      const value = rawResult[valuePath] || fields[valuePath] || fields[field];

      if (
        value !== undefined &&
        value !== null &&
        value !== "N/A" &&
        value !== ""
      ) {
        // Try to get confidence score
        let confidence = 0.8; // Default confidence

        // First try to get confidence from rawResult with _confidence suffix
        if (rawResult[confidencePath]) {
          confidence = rawResult[confidencePath];
        }
        // Try alternative confidence field names
        else if (rawResult[`${valuePath}_confidence`]) {
          confidence = rawResult[`${valuePath}_confidence`];
        }
        // Try confidence from the value object itself
        else if (typeof value === "object" && value.confidence) {
          confidence = value.confidence;
        }
        // For arrays, use a slightly lower default confidence
        else if (Array.isArray(value)) {
          confidence = 0.75;
        }
        // For complex objects, use medium confidence
        else if (typeof value === "object") {
          confidence = 0.7;
        }

        // Ensure confidence is between 0 and 1
        if (confidence > 1) confidence = confidence / 100;
        if (confidence < 0) confidence = 0;
        if (confidence > 1) confidence = 1;

        scores.push({
          value: confidence,
          field,
          source: "AI Analysis",
        });
      }
    });
  }

  return scores;
}

// Helper function to get confidence score for a specific field
export function getFieldConfidence(metadata: any, fieldName: string): number {
  // First check three-tier extraction data
  const extraction = (metadata as any).extraction;
  if (extraction) {
    // Check fixed fields confidence
    if (extraction.fixedFields?.[fieldName]?.confidence !== undefined) {
      return extraction.fixedFields[fieldName].confidence;
    }

    // Check dynamic fields confidence
    if (extraction.dynamicFields?.[fieldName]?.confidence !== undefined) {
      return extraction.dynamicFields[fieldName].confidence;
    }

    // Check special fields confidence (Oracle, Microsoft, SAP)
    if (extraction.specialFields) {
      for (const vendor of ["oracle", "microsoft", "sap"]) {
        if (
          extraction.specialFields[vendor]?.[fieldName]?.confidence !==
          undefined
        ) {
          return extraction.specialFields[vendor][fieldName].confidence;
        }
      }
    }
  }

  // Fallback to legacy structure
  if (!metadata?.autoExtractedFields) return 0.8;

  const fields = metadata.autoExtractedFields;

  // Check if we have the new centralized confidence_scores structure at metadata root level
  if (metadata.confidence_scores) {
    // Map frontend field names to backend field names
    const fieldMapping: Record<string, string> = {
      contractType: "agreement_type",
      contractValue: "contract_value",
      contract_value: "contract_value",
      total_amount: "total_amount",
      effectiveDate: "effective_date",
      effective_date: "effective_date",
      endDate: "end_date",
      end_date: "end_date",
      startDate: "start_date",
      start_date: "start_date",
      contractId: "contract_id",
      contract_id: "contract_id",
      paymentTerms: "payment_terms",
      payment_terms: "payment_terms",
      governingLaw: "governing_law",
      governing_law: "governing_law",
      renewalTerms: "renewal_terms",
      renewal_terms: "renewal_terms",
      keyTerms: "key_terms",
      key_terms: "key_terms",
      provider: "provider",
      client: "client",
      title: "title",
      contract_classification: "contract_classification",
      agreement_type: "agreement_type",
      sla: "sla",
      signatures: "signatures",
      liability: "liability",
      termination: "termination",
      licensing: "licensing",
      data_privacy: "data_privacy",
      parties: "parties",
      dates: "dates",
      lineItems: "line_items",
      line_items: "line_items",
      payment_terms_detailed: "payment_terms_detailed",
      true_up_down: "true_up_down",
      service_credits: "service_credits",
      governance: "governance",
      reporting_obligations: "reporting_obligations",
      ipr: "ipr",
      data_breach: "data_breach",
      indemnification: "indemnification",
    };

    const backendFieldName = fieldMapping[fieldName] || fieldName;
    const confidenceScoreKey = `${backendFieldName}_score`;

    if (metadata.confidence_scores[confidenceScoreKey] !== undefined) {
      let confidence = metadata.confidence_scores[confidenceScoreKey];
      if (confidence > 1) confidence = confidence / 100;
      return Math.max(0, Math.min(1, confidence));
    }
  }

  // Check if we have the new nested confidence_scores structure
  if (fields.confidence_scores) {
    // Map frontend field names to backend field names
    const fieldMapping: Record<string, string> = {
      contractType: "agreement_type",
      contractValue: "contract_value",
      contract_value: "contract_value",
      total_amount: "total_amount",
      effectiveDate: "effective_date",
      effective_date: "effective_date",
      endDate: "end_date",
      end_date: "end_date",
      startDate: "start_date",
      start_date: "start_date",
      contractId: "contract_id",
      contract_id: "contract_id",
      paymentTerms: "payment_terms",
      payment_terms: "payment_terms",
      governingLaw: "governing_law",
      governing_law: "governing_law",
      renewalTerms: "renewal_terms",
      renewal_terms: "renewal_terms",
      keyTerms: "key_terms",
      key_terms: "key_terms",
      provider: "provider",
      client: "client",
      title: "title",
      contract_classification: "contract_classification",
      agreement_type: "agreement_type",
      sla: "sla",
      signatures: "signatures",
      liability: "liability",
      termination: "termination",
      licensing: "licensing",
      data_privacy: "data_privacy",
      parties: "parties",
      dates: "dates",
      lineItems: "line_items",
      line_items: "line_items",
      payment_terms_detailed: "payment_terms_detailed",
      true_up_down: "true_up_down",
      service_credits: "service_credits",
      governance: "governance",
      reporting_obligations: "reporting_obligations",
      ipr: "ipr",
      data_breach: "data_breach",
      indemnification: "indemnification",
    };

    const backendFieldName = fieldMapping[fieldName] || fieldName;
    const confidenceScoreKey = `${backendFieldName}_score`;

    if (fields.confidence_scores[confidenceScoreKey] !== undefined) {
      let confidence = fields.confidence_scores[confidenceScoreKey];
      if (confidence > 1) confidence = confidence / 100;
      return Math.max(0, Math.min(1, confidence));
    }
  }

  // Fallback to old structure
  const rawResult = fields.rawResult || {};

  // Map frontend field names to backend field names
  const fieldMapping: Record<string, string> = {
    contractType: "agreement_type",
    contractValue: "contract_value",
    contract_value: "contract_value",
    total_amount: "contract_value",
    effectiveDate: "effective_date",
    effective_date: "effective_date",
    endDate: "end_date",
    end_date: "end_date",
    startDate: "start_date",
    start_date: "start_date",
    contractId: "contract_id",
    contract_id: "contract_id",
    paymentTerms: "payment_terms",
    payment_terms: "payment_terms",
    governingLaw: "governing_law",
    governing_law: "governing_law",
    renewalTerms: "renewal_terms",
    renewal_terms: "renewal_terms",
    keyTerms: "key_terms",
    key_terms: "key_terms",
    provider: "provider",
    client: "client",
    title: "title",
    contract_classification: "contract_classification",
    agreement_type: "agreement_type",
    sla: "sla",
    signatures: "signatures",
    liability: "liability",
    termination: "termination",
    licensing: "licensing",
    data_privacy: "data_privacy",
    parties: "parties",
    dates: "dates",
  };

  const backendFieldName = fieldMapping[fieldName] || fieldName;
  const confidenceField = `${backendFieldName}_confidence`;

  // Try to get confidence from rawResult
  if (rawResult[confidenceField]) {
    let confidence = rawResult[confidenceField];
    if (confidence > 1) confidence = confidence / 100;
    return Math.max(0, Math.min(1, confidence));
  }

  // Return default confidence based on field type
  const defaultConfidences: Record<string, number> = {
    title: 0.9,
    contractType: 0.85,
    contractValue: 0.9,
    provider: 0.85,
    client: 0.85,
    effectiveDate: 0.75,
    endDate: 0.75,
    contractId: 0.8,
    paymentTerms: 0.7,
    governingLaw: 0.7,
    renewalTerms: 0.7,
    parties: 0.8,
    keyTerms: 0.7,
    dates: 0.75,
    contract_classification: 0.85,
    agreement_type: 0.85,
    sla: 0.7,
    payment_terms: 0.7,
    renewal_terms: 0.7,
    signatures: 0.8,
    liability: 0.7,
    termination: 0.75,
    licensing: 0.75,
    data_privacy: 0.75,
  };

  return defaultConfidences[fieldName] || 0.8;
}
