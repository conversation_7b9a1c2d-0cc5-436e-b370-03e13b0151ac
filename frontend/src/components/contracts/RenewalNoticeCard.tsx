import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Bell, RefreshCw } from "lucide-react";
import { RenewalNoticeCard as RenewalNoticeCardType } from "@/services/contractEntitlementService";

interface RenewalNoticeCardProps {
  renewalNotice: RenewalNoticeCardType;
}

export const RenewalNoticeCard: React.FC<RenewalNoticeCardProps> = ({
  renewalNotice,
}) => {
  const getValueColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    return "text-gray-900 dark:text-gray-100";
  };

  const getAutoRenewalColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    if (value.toLowerCase().includes("yes") || value.toLowerCase().includes("true")) {
      return "text-orange-600 dark:text-orange-400";
    }
    if (value.toLowerCase().includes("no") || value.toLowerCase().includes("false")) {
      return "text-green-600 dark:text-green-400";
    }
    return "text-gray-900 dark:text-gray-100";
  };

  const formatNoticePeriod = (period: string) => {
    if (period === "N/A" || !period) return "N/A";
    return period;
  };

  const formatAutoRenewal = (renewal: string) => {
    if (renewal === "N/A" || !renewal) return "N/A";

    // Standardize common values
    const lower = renewal.toLowerCase();
    if (lower.includes("yes") || lower.includes("true")) return "Yes";
    if (lower.includes("no") || lower.includes("false")) return "No";

    return renewal;
  };

  return (
    <Card className="h-full border-0 shadow-sm bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-orange-100 dark:bg-orange-900/30">
              <Bell className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Renewal Notice
            </span>
          </div>

          {/* Main Value - Notice Period */}
          <div className="space-y-2">
            <div className={`text-xl font-bold ${getValueColor(renewalNotice.noticePeriod)}`}>
              {formatNoticePeriod(renewalNotice.noticePeriod)}
            </div>
            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Notice Period
              </div>
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-400">Auto:</span>
                <span className={`text-xs font-medium px-1.5 py-0.5 rounded-full ${renewalNotice.autoRenewal.toLowerCase().includes('yes') || renewalNotice.autoRenewal.toLowerCase().includes('true')
                  ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300'
                  : 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                  }`}>
                  {formatAutoRenewal(renewalNotice.autoRenewal)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
