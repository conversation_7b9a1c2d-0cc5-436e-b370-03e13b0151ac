/**
 * Extraction Fields Table Component
 * Displays flattened extraction fields (fixed and special) in a table format
 */

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  getAllExtractionFields,
  filterFieldsByConfidence,
  FlattenedField,
} from "@/utils/extraction-utils";

interface ExtractionFieldsTableProps {
  extraction: {
    fixedFields?: { [key: string]: { value: string; confidence: number } };
    specialFields?: {
      [vendor: string]: {
        [key: string]: { value: string; confidence: number };
      };
    };
  };
  className?: string;
  showCard?: boolean;
  minConfidence?: number;
  showConfidence?: boolean;
}

export function ExtractionFieldsTable({
  extraction,
  className = "",
  showCard = true,
  minConfidence = 0.3,
  showConfidence = true,
}: ExtractionFieldsTableProps) {
  // Get all flattened fields
  const allFields = getAllExtractionFields(extraction);

  // Filter by confidence if specified
  const filteredFields = filterFieldsByConfidence(allFields, minConfidence);

  // Sort fields: fixed fields first, then special fields by vendor
  const sortedFields = filteredFields.sort((a, b) => {
    if (a.fieldType !== b.fieldType) {
      return a.fieldType === "fixed" ? -1 : 1;
    }
    if (a.vendor && b.vendor && a.vendor !== b.vendor) {
      return a.vendor.localeCompare(b.vendor);
    }
    return a.displayName.localeCompare(b.displayName);
  });

  const getConfidenceColor = (confidence: number): string => {
    // Handle manually edited fields
    if (confidence === -1)
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    if (confidence >= 0.8)
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    if (confidence >= 0.5)
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
  };

  const formatValue = (value: string): string => {
    if (!value || value === "N/A") return "N/A";

    // Handle currency values
    if (value.includes(":")) {
      const [currency, amount] = value.split(":");
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: currency || "USD",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(numAmount);
      }
    }

    return value;
  };

  const tableContent = (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead className="font-semibold text-foreground w-1/3">
              Field
            </TableHead>
            <TableHead className="font-semibold text-foreground">
              Value
            </TableHead>
            {showConfidence && (
              <TableHead className="font-semibold text-foreground w-24 text-center">
                Confidence
              </TableHead>
            )}
            <TableHead className="font-semibold text-foreground w-20 text-center">
              Type
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedFields.length > 0 ? (
            sortedFields.map((field, index) => (
              <TableRow
                key={`${field.fieldType}-${field.key}-${index}`}
                className="hover:bg-muted/30 transition-colors"
              >
                <TableCell className="font-medium text-muted-foreground border-r">
                  {field.displayName}
                </TableCell>
                <TableCell className="text-foreground">
                  <div className="max-w-md">
                    <span className="break-words">
                      {formatValue(field.value)}
                    </span>
                  </div>
                </TableCell>
                {showConfidence && (
                  <TableCell className="text-center">
                    <Badge
                      variant="secondary"
                      className={`text-xs ${getConfidenceColor(
                        field.confidence
                      )}`}
                    >
                      {field.confidence === -1
                        ? "Manual"
                        : `${Math.round(field.confidence * 100)}%`}
                    </Badge>
                  </TableCell>
                )}
                <TableCell className="text-center">
                  <Badge
                    variant={
                      field.fieldType === "fixed" ? "default" : "outline"
                    }
                    className="text-xs"
                  >
                    {field.fieldType === "fixed" ? "Fixed" : field.vendor}
                  </Badge>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={showConfidence ? 4 : 3}
                className="text-center py-8 text-muted-foreground"
              >
                <div className="flex flex-col items-center gap-2">
                  <span>No extraction fields available</span>
                  {minConfidence > 0 && (
                    <span className="text-xs">
                      (Showing fields with confidence ≥{" "}
                      {Math.round(minConfidence * 100)}%)
                    </span>
                  )}
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );

  // Summary footer
  const summaryFooter = sortedFields.length > 0 && (
    <div className="mt-4 text-sm text-muted-foreground text-center">
      {sortedFields.length} extraction fields displayed
      {minConfidence > 0 && (
        <span> (confidence ≥ {Math.round(minConfidence * 100)}%)</span>
      )}
    </div>
  );

  // Return with or without card wrapper based on showCard prop
  if (!showCard) {
    return (
      <div className={className}>
        {tableContent}
        {summaryFooter}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          Extraction Fields
          <Badge variant="secondary" className="ml-auto">
            {sortedFields.length} fields
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {tableContent}
        {summaryFooter}
      </CardContent>
    </Card>
  );
}

/**
 * Simple key-value display component for extraction data
 */
export function ExtractionKeyValueDisplay({
  extraction,
  className = "",
  includeConfidence = false,
}: {
  extraction: {
    fixedFields?: { [key: string]: { value: string; confidence: number } };
    specialFields?: {
      [vendor: string]: {
        [key: string]: { value: string; confidence: number };
      };
    };
  };
  className?: string;
  includeConfidence?: boolean;
}) {
  const allFields = getAllExtractionFields(extraction);
  const filteredFields = filterFieldsByConfidence(allFields, 0.3);

  return (
    <div className={`space-y-2 ${className}`}>
      {filteredFields.map((field, index) => (
        <div
          key={`${field.fieldType}-${field.key}-${index}`}
          className="flex justify-between items-center py-2 border-b border-muted/30 last:border-b-0"
        >
          <span className="font-medium text-muted-foreground text-sm">
            {field.displayName}
          </span>
          <div className="flex items-center gap-2">
            <span className="text-foreground text-sm">
              {field.value === "N/A" ? (
                <span className="text-muted-foreground">N/A</span>
              ) : (
                field.value
              )}
            </span>
            {includeConfidence && (
              <Badge variant="secondary" className="text-xs">
                {Math.round(field.confidence * 100)}%
              </Badge>
            )}
          </div>
        </div>
      ))}
      {filteredFields.length === 0 && (
        <div className="text-center py-4 text-muted-foreground text-sm">
          No extraction fields available
        </div>
      )}
    </div>
  );
}
