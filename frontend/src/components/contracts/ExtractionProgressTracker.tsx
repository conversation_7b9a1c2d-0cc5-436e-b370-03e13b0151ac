/**
 * Extraction Progress Tracker Component
 * Shows real-time progress of analysis fields extraction jobs
 */

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  AlertCircle,
  X,
} from "lucide-react";
import {
  ExtractionJobStatus,
  entitlementAnalysisService,
} from "@/services/entitlementAnalysisService";
import { cn } from "@/lib/utils";

interface ExtractionProgressTrackerProps {
  jobId: string;
  onComplete: (results: ExtractionJobStatus) => void;
  onError: (error: string) => void;
  onClose: () => void;
  className?: string;
}

export function ExtractionProgressTracker({
  jobId,
  onComplete,
  onError,
  onClose,
  className,
}: ExtractionProgressTrackerProps) {
  const [status, setStatus] = useState<ExtractionJobStatus | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    try {
      const jobStatus = await entitlementAnalysisService.getExtractionStatus(jobId);
      setStatus(jobStatus);
      setError(null);

      // Stop polling when job is complete or failed
      if (jobStatus.status === 'completed' || jobStatus.status === 'failed') {
        setIsPolling(false);
        if (jobStatus.status === 'completed') {
          onComplete(jobStatus);
        } else {
          onError('Extraction job failed');
        }
      }
    } catch (err) {
      console.error('Error fetching extraction status:', err);
      setError((err as Error).message);
      setIsPolling(false);
      onError((err as Error).message);
    }
  }, [jobId, onComplete, onError]);

  useEffect(() => {
    if (!isPolling) return;

    // Initial fetch
    fetchStatus();

    // Poll every 2 seconds
    const interval = setInterval(fetchStatus, 2000);

    return () => clearInterval(interval);
  }, [fetchStatus, isPolling]);

  if (error) {
    return (
      <Card className={cn("border-red-200", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-500" />
              Extraction Failed
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-red-600">{error}</div>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Loading extraction status...
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressPercentage = status.progress.total > 0 
    ? Math.round((status.progress.completed / status.progress.total) * 100)
    : 0;

  const getStatusIcon = () => {
    switch (status.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (status.status) {
      case 'pending':
        return 'Waiting to start...';
      case 'processing':
        return status.progress.current 
          ? `Processing contract ${status.progress.completed + 1} of ${status.progress.total}`
          : 'Processing contracts...';
      case 'completed':
        return 'Extraction completed';
      case 'failed':
        return 'Extraction failed';
      default:
        return 'Unknown status';
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            {getStatusIcon()}
            Analysis Fields Extraction
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{getStatusText()}</span>
            <span>{progressPercentage}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Progress Stats */}
        <div className="flex gap-4 text-sm">
          <div className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span>{status.progress.completed} completed</span>
          </div>
          {status.progress.failed > 0 && (
            <div className="flex items-center gap-1">
              <XCircle className="h-3 w-3 text-red-500" />
              <span>{status.progress.failed} failed</span>
            </div>
          )}
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">
              {status.progress.total - status.progress.completed - status.progress.failed} remaining
            </span>
          </div>
        </div>

        {/* Current Contract */}
        {status.progress.current && status.status === 'processing' && (
          <div className="text-xs text-muted-foreground">
            Current: {status.progress.current}
          </div>
        )}

        {/* Results Summary (when completed) */}
        {status.status === 'completed' && status.results.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Results:</div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {status.results.map((result) => (
                <div
                  key={result.contractId}
                  className="flex items-center gap-2 text-xs"
                >
                  {result.status === 'success' ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <XCircle className="h-3 w-3 text-red-500" />
                  )}
                  <span className="truncate flex-1">
                    {result.contractId}
                  </span>
                  {result.status === 'success' && result.fieldsExtracted && (
                    <Badge variant="secondary" className="text-xs">
                      {result.fieldsExtracted} fields
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
