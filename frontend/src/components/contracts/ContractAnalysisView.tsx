/**
 * Agreement Analysis View Component
 * Displays contract metadata and document in a split-view layout
 */

"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { TabbedExtractionDisplay } from "./TabbedExtractionDisplay";
import {
  FileText,
  AlertCircle,
  Download,
  RefreshCw,
  Maximize2,
  Minimize2,
  Copy,
  Check,
  ArrowLeft,
  Braces,
  Trash2,
  Eye,
  EyeOff,
  Expand,
  SquareArrowOutUpRight,
} from "lucide-react";
import { AggregatedConfidenceScore } from "./ConfidenceIndicators";
import {
  contractExtractionService,
  ContractExtractionData,
} from "@/services/contractExtractionService";
import { toast } from "sonner";
import { ContractStatusBadge } from "./ContractStatusBadge";
import { DeleteConfirmationModal } from "@/components/ui/delete-confirmation-modal";
import { contractService } from "@/services/contractService";
import { OCRTextDialog } from "./OCRTextDialog";

interface ContractAnalysisViewProps {
  contractId: string;
  className?: string;
}

export function ContractAnalysisView({
  contractId,
  className = "",
}: ContractAnalysisViewProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showJsonModal, setShowJsonModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDocumentHidden, setIsDocumentHidden] = useState(false);
  const [refreshModalOpen, setRefreshModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Determine if delete button should be shown based on URL path
  // Show delete button only when coming from repository page (contract-management path)
  const shouldShowDeleteButton = pathname?.includes('/contract-management/contracts/analysis/');

  const [documentBlobUrl, setDocumentBlobUrl] = useState<string | null>(null);
  const [documentLoading, setDocumentLoading] = useState(false);
  const [documentError, setDocumentError] = useState<string | null>(null);
  const [extractionData, setExtractionData] =
    useState<ContractExtractionData | null>(null);
  const [extractionLoading, setExtractionLoading] = useState(true);
  const [extractionError, setExtractionError] = useState<string | null>(null);

  // Fetch extraction data
  const fetchExtractionData = async () => {
    try {
      setExtractionLoading(true);
      setExtractionError(null);
      const data = await contractExtractionService.getContractExtraction(
        contractId
      );
      setExtractionData(data);
    } catch (error) {
      console.error("Error fetching extraction data:", error);
      setExtractionError("Failed to load contract data");
      toast.error("Failed to load contract extraction data");
    } finally {
      setExtractionLoading(false);
    }
  };

  useEffect(() => {
    fetchExtractionData();
  }, [contractId]);

  // Handle fullscreen mode
  useEffect(() => {
    if (isFullscreen) {
      // Prevent body scroll when in fullscreen
      document.body.style.overflow = "hidden";

      // Add escape key listener
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          setIsFullscreen(false);
        }
      };

      document.addEventListener("keydown", handleEscape);

      return () => {
        document.body.style.overflow = "unset";
        document.removeEventListener("keydown", handleEscape);
      };
    } else {
      document.body.style.overflow = "unset";
    }
  }, [isFullscreen]);

  const handleDownloadContract = async () => {
    try {
      // Import the download function
      const { downloadContractDocument } = await import("@/lib/download-utils");
      // Use original filename from extraction data, then fallback to contract ID
      const originalFilename = extractionData?.fixedFields?.original_filename?.value;
      const filename = originalFilename || `Contract-${contractId}`;
      await downloadContractDocument(contractId, filename);
      toast.success("Document downloaded successfully");
    } catch (error) {
      console.error("Error downloading document:", error);
      toast.error("Failed to download document");
    }
  };

  // Use the overall confidence score from extraction data
  const confidenceScores = React.useMemo(() => {
    if (!extractionData?.overallConfidence) return [];

    return [
      {
        value: extractionData.overallConfidence,
        field: "Overall Extraction",
        source: "Three-Tier Analysis",
      },
    ];
  }, [extractionData]);

  // Get document URL from contract ID
  const documentUrl = `${process.env.NEXT_PUBLIC_API_URL || ""
    }/api/contracts/${contractId}/document`;

  // Get contract info from extraction data
  const contractInfo = extractionData?.contractInfo;

  const handleEditToggle = () => {
    setIsEditMode(!isEditMode);
  };

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Handle delete contract
  const handleDeleteContract = () => {
    setDeleteModalOpen(true);
  };

  const confirmDeleteContract = async () => {
    if (!contractId) return;

    setIsDeleting(true);
    try {
      await contractService.deleteContract(contractId);
      toast.success("Contract deleted successfully");
      router.back();
    } catch (error) {
      console.error("Error deleting contract:", error);
      toast.error("Failed to delete contract");
    } finally {
      setIsDeleting(false);
      setDeleteModalOpen(false);
    }
  };

  // Handle refresh analysis
  const handleRefreshAnalysis = () => {
    setRefreshModalOpen(true);
  };

  const confirmRefreshAnalysis = async () => {
    setIsRefreshing(true);
    try {
      // Call the refresh API
      await contractExtractionService.refreshContractExtraction(contractId);

      // Reload the extraction data to update the UI
      await fetchExtractionData();

      toast.success("Contract analysis refreshed successfully");
    } catch (error) {
      console.error("Error refreshing analysis:", error);
      toast.error("Failed to refresh contract analysis");
    } finally {
      setIsRefreshing(false);
      setRefreshModalOpen(false);
    }
  };

  // Copy JSON to clipboard
  const handleCopyJson = async () => {
    if (!extractionData) return;

    try {
      const jsonString = JSON.stringify(extractionData, null, 2);
      await navigator.clipboard.writeText(jsonString);
      setCopied(true);
      toast.success("JSON copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy JSON:", error);
      toast.error("Failed to copy JSON to clipboard");
    }
  };

  // Load document
  const loadDocument = async () => {
    if (!documentUrl) return;

    setDocumentLoading(true);
    setDocumentError(null);

    try {
      // Get the auth token from localStorage with expiry check
      const getValidAccessToken = (): string | null => {
        // Check if token is expired
        const expiryStr = localStorage.getItem("token_expiry");
        if (expiryStr) {
          const expiry = parseInt(expiryStr, 10);
          if (Date.now() >= expiry) {
            // Token is expired
            return null;
          }
        }
        return localStorage.getItem("access_token");
      };

      const token = getValidAccessToken();
      if (!token) {
        throw new Error(
          "No valid authentication token found. Please log in again."
        );
      }

      const response = await fetch(documentUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load document: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      setDocumentBlobUrl(url);
    } catch (error) {
      console.error("Error loading document:", error);
      setDocumentError(
        error instanceof Error ? error.message : "Failed to load document"
      );
    } finally {
      setDocumentLoading(false);
    }
  };

  useEffect(() => {
    loadDocument();

    // Cleanup blob URL on unmount
    return () => {
      if (documentBlobUrl) {
        URL.revokeObjectURL(documentBlobUrl);
      }
    };
  }, [documentUrl]);

  // Show loading state while fetching extraction data
  if (extractionLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <RefreshCw className="h-8 w-8 text-green-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            Loading contract analysis...
          </p>
        </div>
      </div>
    );
  }

  // Show error state if extraction data failed to load
  if (extractionError || !extractionData) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">
            {extractionError || "Failed to load contract data"}
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchExtractionData}
            className="text-green-600 border-green-600 hover:bg-green-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={
        isFullscreen
          ? "fixed inset-0 z-50 bg-white dark:bg-gray-900 flex flex-col"
          : "h-screen flex flex-col bg-white dark:bg-gray-900 overflow-hidden"
      }
    >
      {/* Top Header - Main Title and Actions */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Back Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>

            <h1 className="text-xl m-0 font-bold ">
              {contractInfo?.fileName || contractInfo?.title || "Document"}
            </h1>
            {contractInfo?.status ? (
              <ContractStatusBadge status={contractInfo?.status} />
            ) : (
              <ContractStatusBadge status="Unknown" />
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDocumentHidden(!isDocumentHidden)}
              className="text-gray-600 border-gray-600 hover:bg-gray-50"
              title={isDocumentHidden ? "Show Document" : "Hide Document"}
            >
              {isDocumentHidden ? (
                <Eye className="h-4 w-4 mr-2" />
              ) : (
                <EyeOff className="h-4 w-4 mr-2" />
              )}
              {/* {isDocumentHidden ? "Show" : "Hide"} Document */}
            </Button>
            {documentBlobUrl && !isDocumentHidden && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(documentBlobUrl, "_blank")}
                className="text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                <SquareArrowOutUpRight className="h-4 w-4 mr-2" />
                {/* Open in New Tab */}
              </Button>
            )}
            <OCRTextDialog contractId={contractId} />

            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownloadContract}
              className="text-green-600 border-green-600 hover:bg-green-50"
            >
              <Download className="h-4 w-4 mr-2" />
              {/* Download */}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefreshAnalysis}
              disabled={extractionLoading || isRefreshing}
              className="text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {/* Refresh */}
            </Button>
            {shouldShowDeleteButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDeleteContract}
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {/* Delete */}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 min-h-0 overflow-hidden">
        {/* Left Panel - Document Viewer */}
        {!isDocumentHidden && (
          <div className="w-1/2 bg-gray-50 dark:bg-gray-800 relative border-r border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
            {/* Document Content */}
            <div className="flex-1 w-full bg-white dark:bg-gray-900 relative overflow-hidden">
              <div className="absolute inset-0 overflow-hidden">
                <div className="w-full h-full overflow-auto scrollbar-thin">
                  {documentLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <RefreshCw className="h-8 w-8 text-green-600 animate-spin mx-auto mb-4" />
                        <p className="text-gray-600 dark:text-gray-400">
                          Loading document...
                        </p>
                      </div>
                    </div>
                  ) : documentError ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">{documentError}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={loadDocument}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Retry
                        </Button>
                      </div>
                    </div>
                  ) : documentBlobUrl ? (
                    <iframe
                      src={`${documentBlobUrl}#zoom=100&view=FitH&toolbar=0&navpanes=0&scrollbar=0`}
                      className="w-full h-full border-0"
                      title="Agreement"
                      style={{
                        minHeight: "100%",
                        width: "100%",
                        border: "none",
                        overflow: "hidden",
                      }}
                      allow="fullscreen"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <FileText className="h-8 w-8 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600 dark:text-gray-400">
                          No document available
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Right Panel - Analysis Content */}
        <div
          className={`${isDocumentHidden ? "w-full" : "w-1/2"
            } flex flex-col overflow-hidden`}
        >
          <div className="flex-1 overflow-y-auto scrollbar-thin">
            <div className="p-6 space-y-6">
              {/* Header */}

              {/* <div className="flex items-end justify-between"> */}
              {/* <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Agreement Analysis
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Intrusive examination of your agreements
                  </p>
                </div> */}
              {/* <div className="flex gap-2"> */}
              {/* OCR Text View Button */}

              {/* JSON View Button */}
              {/* <Dialog open={showJsonModal} onOpenChange={setShowJsonModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        title="View Raw JSON Data"
                      >
                        <Braces className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                      <DialogHeader>
                        <DialogTitle className="flex items-center justify-between">
                          <span>Raw JSON Data</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleCopyJson}
                            className="ml-2"
                          >
                            {copied ? (
                              <Check className="h-4 w-4 mr-2" />
                            ) : (
                              <Copy className="h-4 w-4 mr-2" />
                            )}
                            {copied ? "Copied!" : "Copy"}
                          </Button>
                        </DialogTitle>
                      </DialogHeader>
                      <div className="overflow-auto max-h-[60vh]">
                        <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-sm overflow-auto">
                          <code>{JSON.stringify(extractionData, null, 2)}</code>
                        </pre>
                      </div>
                    </DialogContent>
                  </Dialog> */}

              {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsFullscreen(!isFullscreen)}
                  >
                    {isFullscreen ? (
                      <Minimize2 className="h-4 w-4" />
                    ) : (
                      <Maximize2 className="h-4 w-4" />
                    )}
                  </Button> */}
              {/* </div> */}
              {/* </div> */}

              {/* Aggregated Confidence Score */}
              {confidenceScores.length > 0 && (
                <AggregatedConfidenceScore
                  scores={confidenceScores}
                  className="mb-6"
                />
              )}

              {/* Content Area - Tabbed Extraction Display */}
              <div className="flex-1 overflow-y-auto">
                <TabbedExtractionDisplay
                  contractId={contractId}
                  className="h-full"
                  isEditMode={isEditMode}
                  onEditToggle={handleEditToggle}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onConfirm={confirmDeleteContract}
        title="Delete Contract"
        description={`Are you sure you want to delete "${contractInfo?.fileName || contractInfo?.title || "this contract"
          }"? This action cannot be undone and will permanently remove the contract and all associated data.`}
        itemName={contractInfo?.fileName || contractInfo?.title}
        isLoading={isDeleting}
      />

      {/* Refresh Confirmation Modal */}
      <DeleteConfirmationModal
        open={refreshModalOpen}
        onOpenChange={setRefreshModalOpen}
        onConfirm={confirmRefreshAnalysis}
        title="Refresh Contract Analysis"
        description={`Are you sure you want to refresh the analysis for "${contractInfo?.fileName || contractInfo?.title || "this contract"
          }"? This will clear all existing analysis data and re-run the complete analysis workflow.`}
        itemName={contractInfo?.fileName || contractInfo?.title}
        isLoading={isRefreshing}
        confirmText="Refresh Analysis"
      />
    </div>
  );
}
