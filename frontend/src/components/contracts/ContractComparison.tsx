"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  HandCoins,
  FileText,
  AlertTriangle,
  Building,
  Folder,
} from "lucide-react";
import { KeyDeviationsDisplay } from "../benchmark/KeyDeviationsDisplay";
import { Contract } from "@/services/contractService";
import {
  formatContractValue,
  formatCurrency,
  getContractTerm,
} from "@/lib/format-utils";
import { currencyConverter } from "@/lib/currency-utils";

interface ContractComparisonProps {
  contracts: Contract[];
}

/**
 * Process year-wise purchasing data for contract comparison
 * - Return year-wise data structure similar to YearWisePurchasingTable
 * - Maintain year separation instead of aggregating
 */
function processYearWisePurchasingForComparison(purchasingData: any): { years: string[], products: any[] } {
  // Debug: Log the raw purchasing data structure
  console.log('Debug - Raw purchasing data:', purchasingData);
  console.log('Debug - Years in data:', Object.keys(purchasingData));

  const productMap = new Map<string, {
    licenseType: string;
    yearData: { [year: string]: { quantity: string; unitPrice: string; contractValue: string; } };
  }>();

  const years = Object.keys(purchasingData).sort((a, b) => {
    // Sort TOTAL last, otherwise sort alphabetically
    if (a === "TOTAL") return 1;
    if (b === "TOTAL") return -1;
    return a.localeCompare(b);
  });

  // Process each year's data
  Object.entries(purchasingData).forEach(([year, items]) => {
    if (!Array.isArray(items)) return;

    console.log(`Debug - Processing year ${year} with ${items.length} items`);

    // Process all items for this year
    items.forEach((item: any) => {
      const licenseType = item.license_type || item.product_name || item.sku || 'Unknown Product';
      const licenseValue = item.license_value || item.unit_price || item.price || '';

      // Create unique key combining license type and value to prevent duplicates
      const productKey = `${licenseType}|${licenseValue}`;

      if (!productMap.has(productKey)) {
        productMap.set(productKey, {
          licenseType: licenseType,
          yearData: {}
        });
      }

      const product = productMap.get(productKey)!;

      // If this year already exists for this product, aggregate the values
      if (product.yearData[year]) {
        // Sum quantities if both are numeric
        const existingQty = parseFloat(product.yearData[year].quantity) || 0;
        const newQty = parseFloat(item.quantity || item.qty || "0") || 0;
        const totalQty = existingQty + newQty;

        // Sum contract values if both are numeric or in currency format
        const existingValue = product.yearData[year].contractValue;
        const newValue = item.price || item.total_price || item.contract_value || "N/A";
        let totalValue = "N/A";

        if (existingValue !== "N/A" && newValue !== "N/A") {
          // Handle currency format (e.g., "USD:50000")
          const parseValue = (val: string) => {
            if (val.includes(':')) {
              return parseFloat(val.split(':')[1]) || 0;
            }
            return parseFloat(val) || 0;
          };

          const existingAmount = parseValue(existingValue);
          const newAmount = parseValue(newValue);
          const totalAmount = existingAmount + newAmount;

          // Preserve currency format if original had it
          if (existingValue.includes(':')) {
            const currency = existingValue.split(':')[0];
            totalValue = `${currency}:${totalAmount}`;
          } else {
            totalValue = totalAmount.toString();
          }
        }

        product.yearData[year] = {
          quantity: totalQty > 0 ? totalQty.toString() : "N/A",
          unitPrice: licenseValue || "N/A",
          contractValue: totalValue
        };
      } else {
        product.yearData[year] = {
          quantity: item.quantity || item.qty || "N/A",
          unitPrice: licenseValue || "N/A",
          contractValue: item.price || item.total_price || item.contract_value || "N/A"
        };
      }
    });
  });

  // Calculate average total value for each product and sort by it
  const productsWithAverage = Array.from(productMap.values()).map(product => {
    const values: number[] = [];

    Object.values(product.yearData).forEach(yearData => {
      if (yearData.contractValue && yearData.contractValue !== "N/A") {
        const parseValue = (val: string) => {
          if (val.includes(':')) {
            return parseFloat(val.split(':')[1]) || 0;
          }
          return parseFloat(val) || 0;
        };

        const value = parseValue(yearData.contractValue);
        if (value > 0) {
          values.push(value);
        }
      }
    });

    const averageValue = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;

    return {
      ...product,
      averageValue
    };
  });

  // Sort by average value (descending) and return top 10
  const products = productsWithAverage
    .sort((a, b) => b.averageValue - a.averageValue)
    .slice(0, 10)
    .map(({ averageValue, ...product }) => product); // Remove the temporary averageValue property

  return {
    years,
    products
  };
}

export function ContractComparison({ contracts }: ContractComparisonProps) {
  // Helper function to get price change color and percentage based on percentage difference
  // Since we use minimum price as baseline, all other prices will be increases (red shades only)
  const getPriceChangeInfo = (currentPrice: number, basePrice: number): { colorClass: string; percentChange: number } => {
    if (basePrice === 0 || currentPrice === basePrice) return { colorClass: "", percentChange: 0 };

    const percentChange = ((currentPrice - basePrice) / basePrice) * 100;
    const absChange = Math.abs(percentChange);

    let colorClass = "";
    // Since we use minimum as baseline, percentChange will always be positive (price increase)
    // Apply red shades based on how much higher the price is from minimum
    if (absChange >= 50) colorClass = "bg-red-200 text-red-900";
    else if (absChange >= 20) colorClass = "bg-red-100 text-red-800";
    else if (absChange >= 10) colorClass = "bg-red-50 text-red-700";
    else if (absChange > 0) colorClass = "bg-red-25 text-red-600";

    return { colorClass, percentChange };
  };

  // Helper functions to extract data from three-tier extraction format
  const getClientName = (contract: Contract) => {
    // Safety check for contract
    if (!contract) return "N/A";

    // Use three-tier extraction data first
    const extraction = contract.extraction;
    if (extraction?.fixedFields?.client?.value) {
      return extraction.fixedFields.client.value;
    }

    // Fallback to legacy metadata for backward compatibility
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.parties) {
      const client = metadata.autoExtractedFields.parties.find(
        (p: any) => p.role === "Client"
      );
      if (client) return client.name;
    }
    if (metadata?.autoExtractedFields?.rawResult?.client) {
      return metadata.autoExtractedFields.rawResult.client;
    }
    return contract.counterparty || "N/A";
  };

  const getSupplierName = (contract: Contract) => {
    // Safety check for contract
    if (!contract) return "N/A";

    // Use three-tier extraction data first
    const extraction = contract.extraction;
    if (extraction?.fixedFields?.provider?.value) {
      return extraction.fixedFields.provider.value;
    }

    // Fallback to legacy metadata for backward compatibility
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.parties) {
      const provider = metadata.autoExtractedFields.parties.find(
        (p: any) => p.role === "Provider"
      );
      if (provider) return provider.name;
    }
    if (metadata?.autoExtractedFields?.rawResult?.provider) {
      return metadata.autoExtractedFields.rawResult.provider;
    }
    return contract.provider || contract.counterparty || "N/A";
  };

  const getContractTermValue = (contract: Contract) => {
    // Safety check for contract and extraction
    if (!contract) return "N/A";

    // Get LLM-extracted term from extraction data only
    const extraction = (contract as any).extraction;
    const extractedTerm = extraction?.fixedFields?.contract_term?.value;

    // Use only LLM-extracted term, no calculation fallback
    return getContractTerm(extractedTerm);
  };

  const getLicenseResourceCount = (contract: Contract) => {
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.rawResult?.line_items) {
      const lineItems = metadata.autoExtractedFields.rawResult.line_items;
      if (Array.isArray(lineItems)) {
        return lineItems.length.toString();
      }
    }
    return "N/A";
  };

  const getUnitRate = (contract: Contract) => {
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.rawResult?.line_items) {
      const lineItems = metadata.autoExtractedFields.rawResult.line_items;
      if (Array.isArray(lineItems) && lineItems.length > 0) {
        const firstItem = lineItems[0];
        if (
          typeof firstItem === "object" &&
          firstItem !== null &&
          firstItem.unit_price
        ) {
          return formatCurrency(firstItem.unit_price, metadata.currency);
        }
      }
    }
    return "N/A";
  };

  const getAutoRenewal = (contract: Contract) => {
    // Safety check for contract
    if (!contract) return "N/A";

    // Use three-tier extraction data first
    const extraction = contract.extraction;
    if (extraction?.fixedFields?.auto_renewal?.value) {
      return extraction.fixedFields.auto_renewal.value === "Yes" ? "Yes" : "No";
    }

    // Use contract property if available
    if (contract.isAutoRenew !== undefined) {
      return contract.isAutoRenew ? "Yes" : "No";
    }

    // Fallback to legacy metadata for backward compatibility
    const metadata = (contract as any).metadata;
    if (metadata) {
      // Check detailed renewal terms from AI analysis (new schema)
      if (
        metadata.renewalTermsDetailed &&
        Array.isArray(metadata.renewalTermsDetailed)
      ) {
        const renewalTerm = metadata.renewalTermsDetailed[0];
        if (renewalTerm?.auto_renewal) {
          return renewalTerm.auto_renewal === "Yes" ? "Yes" : "No";
        }
      }

      // Check AI-extracted renewal terms (normalized schema)
      if (metadata.renewalTerms && typeof metadata.renewalTerms === "object") {
        if (metadata.renewalTerms.isAutoRenew !== undefined) {
          return metadata.renewalTerms.isAutoRenew ? "Yes" : "No";
        }
      }

      // Check raw AI result for renewal terms
      if (metadata.analysisResult?.rawResult?.renewal_terms) {
        const renewalTerms = metadata.analysisResult.rawResult.renewal_terms;
        if (Array.isArray(renewalTerms) && renewalTerms.length > 0) {
          const renewalTerm = renewalTerms[0];
          if (renewalTerm?.auto_renewal) {
            return renewalTerm.auto_renewal === "Yes" ? "Yes" : "No";
          }
        }
      }
    }

    return "N/A";
  };

  const getPaymentTerms = (contract: Contract) => {
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.rawResult?.payment_terms_detailed) {
      const paymentTerms =
        metadata.autoExtractedFields.rawResult.payment_terms_detailed;
      if (typeof paymentTerms === "object" && paymentTerms !== null) {
        if (paymentTerms.timeline) {
          return `${paymentTerms.timeline} days`;
        }
        // If it's an object but no timeline field, return a summary
        return "See payment terms";
      }
      // If it's a string, return it
      return paymentTerms;
    }
    if (metadata?.paymentTerms) {
      return metadata.paymentTerms;
    }
    return "N/A";
  };

  const getServiceLevels = (contract: Contract) => {
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.rawResult?.sla) {
      const sla = metadata.autoExtractedFields.rawResult.sla;
      if (typeof sla === "object" && sla !== null) {
        if (
          Object.keys(sla).length > 0 &&
          Object.values(sla).some(
            (v) => v !== null && v !== undefined && v !== ""
          )
        ) {
          return "Mentioned";
        }
      } else if (typeof sla === "string" && sla.trim() !== "") {
        return "Mentioned";
      }
    }
    return "Not mentioned";
  };

  const getTerminationForConvenience = (contract: Contract) => {
    const metadata = (contract as any).metadata;
    if (metadata?.autoExtractedFields?.rawResult?.termination) {
      const termination = metadata.autoExtractedFields.rawResult.termination;
      if (typeof termination === "object" && termination !== null) {
        if (termination.t4c !== undefined) {
          return termination.t4c ? "Yes" : "No";
        }
      }
    }
    return "N/A";
  };

  const getIndexationHold = (_contract: Contract) => {
    // const metadata = (contract as any).metadata;
    // This would need to be extracted from contract terms - placeholder for now
    return "N/A";
  };

  // Helper function to parse contract value (handles three-tier extraction format)
  const parseContractValue = (value: string | null | undefined): number => {
    if (!value) return 0;
    // Handle both currency format (e.g., "USD:100000") and plain numbers
    let numericValue = value;
    if (value.includes(":")) {
      numericValue = value.split(":")[1] || "0";
    }
    const cleanValue = numericValue.replace(/[^0-9.-]+/g, "");
    return parseFloat(cleanValue) || 0;
  };

  // Helper function to parse annually_amount into structured data
  const parseAnnuallyAmount = (annuallyAmount: string | null | undefined) => {
    if (!annuallyAmount || annuallyAmount === "N/A") {
      return [];
    }

    const years: Array<{ year: string; amount: string }> = [];

    // Split by comma and process each year entry
    const yearEntries = annuallyAmount.split(',').map(entry => entry.trim());

    for (const entry of yearEntries) {
      // Match patterns like "Year 1: USD:60000.00" or "Year 2 (6 months): USD:50000.00"
      const yearMatch = entry.match(/Year\s+(\d+)(?:\s*\([^)]+\))?\s*:\s*(.+)/i);
      if (yearMatch) {
        const yearNum = yearMatch[1];
        const amount = yearMatch[2].trim();
        years.push({ year: `Year ${yearNum}`, amount });
        continue;
      }

      // Match patterns like "Annual rate: USD:100000.00 (6 months actual: USD:50000.00)"
      const annualRateMatch = entry.match(/Annual\s+rate\s*:\s*([^(]+)(?:\s*\([^)]+\))?/i);
      if (annualRateMatch) {
        const amount = annualRateMatch[1].trim();
        years.push({ year: "Annual Rate", amount });
        continue;
      }
    }

    return years;
  };

  // Prepare data for charts
  const contractValueData = contracts.map((contract) => ({
    name: contract.title.length > 15 ? `${contract.title.substring(0, 15)}...` : contract.title,
    fullName: contract.title,
    value: parseContractValue(contract.value),
    originalValue: contract.value || "N/A",
  }));

  const contractStatusData = contracts.reduce((acc, contract) => {
    const status = contract.status || "Unknown";
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusChartData = Object.entries(contractStatusData).map(
    ([status, count]) => ({
      name: status,
      value: count,
      color:
        status === "Active"
          ? "#09260D"
          : status === "Inactive"
            ? "#ef4444"
            : "#6b7280", // Unknown status
    })
  );

  const contractDurationData = contracts.map((contract) => {
    const startDate = contract.startDate ? new Date(contract.startDate) : null;
    const endDate = contract.endDate ? new Date(contract.endDate) : null;
    const duration =
      startDate && endDate
        ? Math.ceil(
          (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
        )
        : 365; // Default to 1 year if no dates available for visualization

    // Calculate years and months for better display
    const years = Math.floor(duration / 365);
    const months = Math.floor((duration % 365) / 30);
    const durationLabel =
      years > 0
        ? `${years}y ${months}m`
        : months > 0
          ? `${months}m`
          : `${duration}d`;

    return {
      name: contract.title.length > 20 ? `${contract.title.substring(0, 20)}...` : contract.title,
      fullName: contract.title,
      duration,
      durationLabel,
      years,
      months,
      status: contract.status || "Unknown",
      value: parseContractValue(contract.value),
      startDate: startDate?.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) || "N/A",
      endDate: endDate?.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) || "N/A",
      isActive: contract.status === "Active",
      // Calculate efficiency score (value per day)
      efficiency:
        duration > 0 ? parseContractValue(contract.value) / duration : 0,
    };
  });

  const timelineData = contracts.map((contract) => {
    const startDate = contract.startDate ? new Date(contract.startDate) : null;
    const endDate = contract.endDate ? new Date(contract.endDate) : null;

    return {
      name: contract.title.length > 15 ? `${contract.title.substring(0, 15)}...` : contract.title,
      fullName: contract.title,
      start: startDate ? startDate.getTime() : 0,
      end: endDate ? endDate.getTime() : 0,
      startFormatted: startDate?.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) || "N/A",
      endFormatted: endDate?.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      }) || "N/A",
    };
  });

  return (
    <Tabs defaultValue="details" className="space-y-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="details">Detailed</TabsTrigger>
        <TabsTrigger value="pricing">Pricing</TabsTrigger>
        <TabsTrigger value="deviations">Key deviations: T&Cs</TabsTrigger>
      </TabsList>

      {/* Details Tab */}
      <TabsContent value="details" className="space-y-6">
        {/* Fixed Fields Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" style={{ color: "#09260D" }} />
              Basic Comparison
            </CardTitle>
            <CardDescription>
              Standard contract fields extracted from all contracts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Field</TableHead>
                    {contracts.map((contract, index) => {
                      // Calculate equal width for contract columns based on number of contracts
                      const getContractWidth = () => {
                        switch (contracts.length) {
                          case 2: return "w-[400px]";
                          case 3: return "w-[266px]";
                          case 4: return "w-[200px]";
                          case 5: return "w-[160px]";
                          default: return "w-[200px]";
                        }
                      };

                      return (
                        <TableHead key={index} className={`${getContractWidth()} text-left pl-6`}>
                          <div className="font-semibold text-sm">
                            {contract.title}
                          </div>
                        </TableHead>
                      );
                    })}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(() => {
                    // Get all unique fixed field keys from all contracts
                    const allFixedFields = new Set<string>();
                    contracts.forEach((contract) => {
                      const extraction = (contract as any).extraction;
                      if (extraction?.fixedFields) {
                        Object.keys(extraction.fixedFields).forEach((key) =>
                          allFixedFields.add(key)
                        );
                      }
                    });

                    // Filter fields to only include those with at least one non-empty value
                    const fieldsWithValues = Array.from(allFixedFields).filter(
                      (fieldKey) => {
                        return contracts.some((contract) => {
                          const extraction = (contract as any).extraction;
                          const fieldValue =
                            extraction?.fixedFields?.[fieldKey];
                          return (
                            fieldValue &&
                            fieldValue.value &&
                            fieldValue.value.trim() !== "" &&
                            fieldValue.value.trim().toLowerCase() !== "n/a"
                          );
                        });
                      }
                    );

                    return fieldsWithValues.map((fieldKey) => (
                      <TableRow key={fieldKey}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <span className="capitalize">
                              {fieldKey.replace(/_/g, " ")}
                            </span>
                          </div>
                        </TableCell>
                        {contracts.map((contract, index) => {
                          const extraction = (contract as any).extraction;
                          const fieldValue =
                            extraction?.fixedFields?.[fieldKey];

                          // Calculate equal width for contract columns based on number of contracts
                          const getContractWidth = () => {
                            switch (contracts.length) {
                              case 2: return "w-[400px]";
                              case 3: return "w-[266px]";
                              case 4: return "w-[200px]";
                              case 5: return "w-[160px]";
                              default: return "w-[200px]";
                            }
                          };

                          return (
                            <TableCell key={index} className={`${getContractWidth()} text-left pl-6`}>
                              {fieldValue &&
                                fieldValue.value &&
                                fieldValue.value.trim() !== "" &&
                                fieldValue.value.trim().toLowerCase() !==
                                "n/a" ? (
                                <div className="whitespace-normal break-words">
                                  {(() => {
                                    // Format date fields to dd/mm/yyyy
                                    if (fieldKey === "start_date" || fieldKey === "end_date") {
                                      try {
                                        const date = new Date(fieldValue.value);
                                        if (!isNaN(date.getTime())) {
                                          return date.toLocaleDateString("en-GB", {
                                            day: "2-digit",
                                            month: "2-digit",
                                            year: "numeric"
                                          });
                                        }
                                      } catch (error) {
                                        // If date parsing fails, return original value
                                      }
                                    }

                                    // Format currency fields (detect currency pattern like "USD:134414.11")
                                    if (fieldKey.includes("total_amount") || fieldKey.includes("value") || fieldKey.includes("price") || fieldKey.includes("cost") || fieldKey.includes("fee") || fieldKey.includes("total")) {
                                      if (fieldValue.value.includes(":")) {
                                        return formatCurrency(fieldValue.value);
                                      }
                                    }

                                    return fieldValue.value;
                                  })()}
                                </div>
                              ) : (
                                <span className="text-muted-foreground">
                                  N/A
                                </span>
                              )}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ));
                  })()}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Special Fields Section */}
        {(() => {
          // Get all unique categories across all suppliers and contracts
          const allCategories = new Set<string>();
          contracts.forEach((contract) => {
            const extraction = (contract as any).extraction;
            if (extraction?.specialFields) {
              Object.values(extraction.specialFields).forEach(
                (supplierData: any) => {
                  if (supplierData) {
                    Object.keys(supplierData).forEach((category) =>
                      allCategories.add(category)
                    );
                  }
                }
              );
            }
          });

          if (allCategories.size === 0) return null;

          return (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" style={{ color: "#09260D" }} />
                  Supplier Based
                </CardTitle>
                <CardDescription>
                  Supplier-specific fields from all contracts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {(() => {
                  // Define the desired order for categories
                  const categoryOrder = [
                    "Use rights & restrictions",
                    "General",
                    "Legal terms",
                    "Commercial terms",
                    "Data protection",
                    "Others",
                  ];

                  // Sort categories according to the predefined order
                  const sortedCategories = categoryOrder.filter((category) =>
                    allCategories.has(category)
                  );

                  return sortedCategories.map((categoryName) => {
                    // Get all unique fields for this category across all suppliers and contracts
                    const allFields = new Set<string>();
                    contracts.forEach((contract) => {
                      const extraction = (contract as any).extraction;
                      if (extraction?.specialFields) {
                        Object.values(extraction.specialFields).forEach(
                          (supplierData: any) => {
                            const categoryData = supplierData?.[categoryName];
                            if (categoryData) {
                              Object.keys(categoryData).forEach((field) =>
                                allFields.add(field)
                              );
                            }
                          }
                        );
                      }
                    });

                    // Filter fields to only include those with at least one non-empty value
                    const fieldsWithValues = Array.from(allFields).filter(
                      (fieldKey) => {
                        return contracts.some((contract) => {
                          const extraction = (contract as any).extraction;
                          if (extraction?.specialFields) {
                            return Object.values(extraction.specialFields).some(
                              (supplierData: any) => {
                                const fieldValue =
                                  supplierData?.[categoryName]?.[fieldKey];
                                return (
                                  fieldValue &&
                                  fieldValue.value &&
                                  fieldValue.value.trim() !== "" &&
                                  fieldValue.value.trim().toLowerCase() !==
                                  "n/a"
                                );
                              }
                            );
                          }
                          return false;
                        });
                      }
                    );

                    if (fieldsWithValues.length === 0) return null;

                    return (
                      <div key={categoryName} className="space-y-3">
                        <div className="flex items-center gap-2 pb-2 border-b border-border">
                          <Folder
                            className="h-4 w-4"
                            style={{ color: "#09260D" }}
                          />
                          <h4 className="font-semibold m-0 text-base">
                            {categoryName}
                          </h4>
                        </div>

                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="w-[200px]">
                                  Field
                                </TableHead>
                                {contracts.map((contract, index) => {
                                  // Calculate equal width for contract columns based on number of contracts
                                  const getContractWidth = () => {
                                    switch (contracts.length) {
                                      case 2: return "w-[400px]";
                                      case 3: return "w-[266px]";
                                      case 4: return "w-[200px]";
                                      case 5: return "w-[160px]";
                                      default: return "w-[200px]";
                                    }
                                  };

                                  return (
                                    <TableHead
                                      key={index}
                                      className={`${getContractWidth()} text-left pl-6`}
                                    >
                                      <div className="font-semibold text-sm">
                                        {contract.title}
                                      </div>
                                    </TableHead>
                                  );
                                })}
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {fieldsWithValues.map((fieldKey) => (
                                <TableRow key={fieldKey}>
                                  <TableCell className="font-medium">
                                    <span className="capitalize">
                                      {fieldKey.replace(/_/g, " ")}
                                    </span>
                                  </TableCell>
                                  {contracts.map((contract, index) => {
                                    const extraction = (contract as any)
                                      .extraction;

                                    // Find the field value from any supplier for this contract
                                    let fieldValue: any = null;
                                    if (extraction?.specialFields) {
                                      Object.values(
                                        extraction.specialFields
                                      ).forEach((supplierData: any) => {
                                        const value =
                                          supplierData?.[categoryName]?.[
                                          fieldKey
                                          ];
                                        if (
                                          value &&
                                          value.value &&
                                          value.value.trim() !== "" &&
                                          value.value.trim().toLowerCase() !==
                                          "n/a"
                                        ) {
                                          fieldValue = value;
                                        }
                                      });
                                    }

                                    // Calculate equal width for contract columns based on number of contracts
                                    const getContractWidth = () => {
                                      switch (contracts.length) {
                                        case 2: return "w-[400px]";
                                        case 3: return "w-[266px]";
                                        case 4: return "w-[200px]";
                                        case 5: return "w-[160px]";
                                        default: return "w-[200px]";
                                      }
                                    };

                                    return (
                                      <TableCell
                                        key={index}
                                        className={`${getContractWidth()} text-left pl-6`}
                                      >
                                        {fieldValue ? (
                                          <div className="whitespace-normal break-words">
                                            {(() => {
                                              // Format currency fields in supplier-based sections
                                              if (fieldKey.includes("amount") || fieldKey.includes("value") || fieldKey.includes("price") || fieldKey.includes("cost") || fieldKey.includes("fee") || fieldKey.includes("total")) {
                                                if (fieldValue.value.includes(":")) {
                                                  return formatCurrency(fieldValue.value);
                                                }
                                              }
                                              return fieldValue.value;
                                            })()}
                                          </div>
                                        ) : (
                                          <span className="text-muted-foreground">
                                            N/A
                                          </span>
                                        )}
                                      </TableCell>
                                    );
                                  })}
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    );
                  });
                })()}
              </CardContent>
            </Card>
          );
        })()}
      </TabsContent>

      {/* Pricing Tab */}
      <TabsContent value="pricing" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" style={{ color: "#09260D" }} />
              Governing Terms and Conditions
            </CardTitle>
            <CardDescription>
              Key contract terms and conditions across selected agreements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[200px]">Term</TableHead>
                    {contracts.map((contract, index) => {
                      // Calculate equal width for contract columns based on number of contracts
                      const getContractWidth = () => {
                        switch (contracts.length) {
                          case 2: return "w-[400px]";
                          case 3: return "w-[266px]";
                          case 4: return "w-[200px]";
                          case 5: return "w-[160px]";
                          default: return "w-[200px]";
                        }
                      };

                      return (
                        <TableHead key={index} className={`${getContractWidth()} text-left pl-6`}>
                          <div className="font-semibold text-sm">
                            {contract.title}
                          </div>
                        </TableHead>
                      );
                    })}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Total Contract Value */}
                  <TableRow>
                    <TableCell className="font-medium">Total Contract Value</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      const annuallyAmount = extraction?.fixedFields?.annually_amount?.value;
                      const yearlyBreakdown = parseAnnuallyAmount(annuallyAmount);

                      return (
                        <TableCell key={index}>
                          <div className="space-y-2">
                            {/* Total Value */}
                            <div className="font-medium">
                              {formatContractValue(
                                contract.value,
                                (contract as any).metadata?.currency,
                                true
                              )}
                            </div>

                            {/* Annual Breakdown - Compact Visual */}
                            {yearlyBreakdown.length > 0 && (
                              <div className="mt-3 mr-9">
                                <div className="text-xs text-muted-foreground mb-2">Annual Breakdown:</div>
                                <div className="space-y-2">
                                  {yearlyBreakdown.map((yearData, yearIndex) => {
                                    // Calculate percentage for visual bar
                                    const totalValue = yearlyBreakdown.reduce((sum, year) => {
                                      const amount = year.amount.includes(':')
                                        ? parseFloat(year.amount.split(':')[1])
                                        : parseFloat(year.amount.replace(/[^0-9.-]/g, ''));
                                      return sum + (isNaN(amount) ? 0 : amount);
                                    }, 0);

                                    const currentAmount = yearData.amount.includes(':')
                                      ? parseFloat(yearData.amount.split(':')[1])
                                      : parseFloat(yearData.amount.replace(/[^0-9.-]/g, ''));

                                    const percentage = totalValue > 0 ? (currentAmount / totalValue) * 100 : 0;

                                    return (
                                      <div key={yearIndex} className="flex items-center gap-2">
                                        <div className="flex-1">
                                          <div className="flex justify-between items-center mb-1">
                                            <span className="text-xs font-medium text-muted-foreground">
                                              {yearData.year}
                                            </span>
                                            <span className="text-xs font-mono font-medium">
                                              {yearData.amount.includes(':')
                                                ? formatContractValue(yearData.amount, undefined, true)
                                                : yearData.amount
                                              }
                                            </span>
                                          </div>
                                          <div className="w-full bg-muted rounded-full h-1.5">
                                            <div
                                              className="bg-primary h-1.5 rounded-full transition-all duration-300"
                                              style={{ width: `${Math.max(percentage, 5)}%` }}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Contract Term */}
                  <TableRow>
                    <TableCell className="font-medium">Contract Term (months)</TableCell>
                    {contracts.map((contract, index) => {
                      // Get term from extraction data - use the helper function
                      const termValue = getContractTermValue(contract);

                      return (
                        <TableCell key={index}>
                          {termValue}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Discount Category/Customer Profile */}
                  <TableRow>
                    <TableCell className="font-medium">Discount Category/Customer Profile</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let discountCategory = "N/A";

                      // Try to find in special fields only (these fields are supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              if (categoryData.customer_profile?.value) {
                                discountCategory = categoryData.customer_profile.value;
                              } else if (categoryData.discount_category?.value) {
                                discountCategory = categoryData.discount_category.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing discount category:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {discountCategory}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Payment Terms */}
                  <TableRow>
                    <TableCell className="font-medium">Payment Terms</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let paymentTerms = "N/A";

                      // Try to find in special fields only (payment terms are supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              // Check for payment terms in various field names
                              if (categoryData.payment_terms?.value) {
                                paymentTerms = categoryData.payment_terms.value;
                              }
                              // Check for fees___payment_terms in Financial category
                              else if (categoryKey === "Commercial terms" && categoryData.fees___payment_terms?.value) {
                                paymentTerms = categoryData.fees___payment_terms.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing payment terms:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {paymentTerms}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Year of Purchase */}
                  <TableRow>
                    <TableCell className="font-medium">Year of Purchase</TableCell>
                    {contracts.map((contract, index) => {
                      const startDate = contract.startDate ? new Date(contract.startDate) : null;
                      const year = startDate ? startDate.getFullYear() : "N/A";

                      return (
                        <TableCell key={index}>
                          {year}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Invoice Schedule */}
                  <TableRow>
                    <TableCell className="font-medium">Invoice Schedule</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let invoiceSchedule = "N/A";

                      // Try to find in special fields only (invoice schedule is supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              if (categoryData.invoice_schedule?.value) {
                                invoiceSchedule = categoryData.invoice_schedule.value;
                              }
                              // Also check for billing_schedule which might contain the same info
                              if (invoiceSchedule === "N/A" && categoryData.billing_schedule?.value) {
                                invoiceSchedule = categoryData.billing_schedule.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing invoice schedule:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {invoiceSchedule}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Territorial Scope */}
                  <TableRow>
                    <TableCell className="font-medium">Territorial Scope</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let territorialScope = "N/A";

                      // Try to find in special fields only (territorial scope is supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Check specifically in usage_and_restrictions category
                            if (providerData.usage_and_restrictions?.territorial_scope?.value) {
                              territorialScope = providerData.usage_and_restrictions.territorial_scope.value;
                            }

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              if (categoryData.territorial_scope?.value) {
                                territorialScope = categoryData.territorial_scope.value;
                              }
                              // Also check for territory which might contain the same info
                              if (territorialScope === "N/A" && categoryData.territory?.value) {
                                territorialScope = categoryData.territory.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing territorial scope:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {territorialScope}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Third-party Usage */}
                  <TableRow>
                    <TableCell className="font-medium">Third-party Usage</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let thirdPartyUsage = "N/A";

                      // Try to find in special fields only (third-party usage is supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Check specifically in usage_and_restrictions category
                            if (providerData.usage_and_restrictions?.third_party_usage?.value) {
                              thirdPartyUsage = providerData.usage_and_restrictions.third_party_usage.value;
                            }

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              if (categoryData.third_party_usage?.value) {
                                thirdPartyUsage = categoryData.third_party_usage.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing third-party usage:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {thirdPartyUsage}
                        </TableCell>
                      );
                    })}
                  </TableRow>

                  {/* Ramped-pricing Concession */}
                  <TableRow>
                    <TableCell className="font-medium">Ramped-pricing Concession</TableCell>
                    {contracts.map((contract, index) => {
                      const extraction = contract.extraction;
                      let rampedPricing = "N/A";

                      // Try to find in special fields only (ramped pricing is supplier-specific)
                      if (extraction?.specialFields) {
                        try {
                          // First level is provider name, second level is category, third level is field
                          Object.values(extraction.specialFields).forEach((providerData: any) => {
                            if (!providerData) return;

                            // Check specifically in pricing_terms category
                            if (providerData.pricing_terms?.ramped_pricing?.value) {
                              rampedPricing = providerData.pricing_terms.ramped_pricing.value;
                            }

                            // Look in all categories for this provider
                            Object.keys(providerData).forEach((categoryKey) => {
                              const categoryData = providerData[categoryKey];
                              if (!categoryData) return;

                              if (categoryData.ramped_pricing?.value) {
                                rampedPricing = categoryData.ramped_pricing.value;
                              }
                              // Also check for price_ramp which might contain the same info
                              if (rampedPricing === "N/A" && categoryData.price_ramp?.value) {
                                rampedPricing = categoryData.price_ramp.value;
                              }
                            });
                          });
                        } catch (error) {
                          console.error("Error processing ramped pricing:", error);
                        }
                      }

                      return (
                        <TableCell key={index}>
                          {rampedPricing}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HandCoins className="h-5 w-5" style={{ color: "#09260D" }} />
              Commercial Entitlements
            </CardTitle>
            <CardDescription>
              Purchased items and entitlements across selected contracts.
              Showing year-wise breakdown of quantities, unit prices, and contract values for each product, with average columns showing averaged quantities/prices and total values across all years.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              {(() => {
                // Extract all purchase items from all contracts
                const contractsWithItems = contracts.map((contract, contractIndex) => {
                  const extraction = contract.extraction;
                  let purchaseItems: any[] = [];

                  // Try to find purchase items in analysis fields
                  if (extraction?.analysisFields?.purchases) {
                    purchaseItems = Array.isArray(extraction.analysisFields.purchases)
                      ? extraction.analysisFields.purchases
                      : [extraction.analysisFields.purchases];
                  }

                  // Check for purchasing data in analysis fields (new object format or legacy array)
                  if (purchaseItems.length === 0 && extraction?.analysisFields?.purchasing) {
                    const purchasingData = extraction.analysisFields.purchasing;

                    if (Array.isArray(purchasingData)) {
                      // Legacy array format
                      purchaseItems = purchasingData;
                    } else if (typeof purchasingData === 'object') {
                      // New object format - get year-wise data structure
                      const yearWiseData = processYearWisePurchasingForComparison(purchasingData);
                      // Store the year-wise structure for later use
                      (contract as any)._yearWiseData = yearWiseData;
                      // For now, flatten to maintain compatibility with existing code
                      purchaseItems = yearWiseData.products.map(product => ({
                        license_type: product.licenseType,
                        ...product.yearData
                      }));
                    }
                  }

                  // If no items found, check for line_items in analysis fields
                  if (purchaseItems.length === 0 && extraction?.analysisFields?.line_items) {
                    purchaseItems = Array.isArray(extraction.analysisFields.line_items)
                      ? extraction.analysisFields.line_items
                      : [extraction.analysisFields.line_items];
                  }

                  // If no items found, check special fields
                  if (purchaseItems.length === 0 && extraction?.specialFields) {
                    try {
                      Object.values(extraction.specialFields).forEach((providerData: any) => {
                        if (!providerData) return;

                        // Check in pricing_terms category first
                        if (providerData.pricing_terms?.line_items) {
                          purchaseItems = Array.isArray(providerData.pricing_terms.line_items)
                            ? providerData.pricing_terms.line_items
                            : [providerData.pricing_terms.line_items];
                        }

                        // Look in all categories
                        if (purchaseItems.length === 0) {
                          Object.keys(providerData).forEach((categoryKey) => {
                            const categoryData = providerData[categoryKey];
                            if (!categoryData) return;

                            if (categoryData.line_items) {
                              purchaseItems = Array.isArray(categoryData.line_items)
                                ? categoryData.line_items
                                : [categoryData.line_items];
                            }
                          });
                        }
                      });
                    } catch (error) {
                      console.error("Error processing purchase items from special fields:", error);
                    }
                  }

                  // If still no items, check legacy metadata
                  if (purchaseItems.length === 0) {
                    const metadata = (contract as any).metadata;
                    if (metadata?.autoExtractedFields?.rawResult?.line_items) {
                      purchaseItems = metadata.autoExtractedFields.rawResult.line_items;
                    }
                  }

                  return {
                    contract,
                    items: purchaseItems,
                    contractIndex
                  };
                });

                // Collect all year-wise data from contracts that have it
                const contractsWithYearWiseData = contractsWithItems
                  .map(({ contract, items, contractIndex }) => {
                    const yearWiseData = (contract as any)._yearWiseData;
                    if (yearWiseData) {
                      return {
                        contract,
                        contractIndex,
                        years: yearWiseData.years,
                        products: yearWiseData.products
                      };
                    }
                    return null;
                  })
                  .filter(Boolean);

                // Get all unique product names across all contracts
                const allProductNames = new Set<string>();
                contractsWithYearWiseData.forEach((contractData) => {
                  if (contractData) {
                    contractData.products.forEach((product: any) => {
                      allProductNames.add(product.licenseType);
                    });
                  }
                });

                // Fallback to simple product names for contracts without year-wise data
                contractsWithItems.forEach(({ items }) => {
                  items.forEach((item) => {
                    const productName = item?.license_type || item?.name || item?.description ||
                      item?.product_name || item?.item_name || item?.service || "Unnamed Item";
                    allProductNames.add(productName);
                  });
                });

                // Sort products by total value (highest to lowest)
                const uniqueProducts = Array.from(allProductNames).sort((a, b) => {
                  // Calculate total value for product A across all contracts
                  let totalValueA = 0;
                  contractsWithYearWiseData.forEach((contractData) => {
                    if (contractData) {
                      const product = contractData.products.find((p: any) => p.licenseType === a);
                      if (product && product.yearData) {
                        const yearDataEntries = Object.entries(product.yearData);
                        const validYearData = yearDataEntries.filter(([_, data]: [string, any]) =>
                          data && data.contractValue && data.contractValue !== "N/A"
                        );
                        if (validYearData.length > 0) {
                          const productTotal = validYearData.reduce((sum, [_, data]: [string, any]) => {
                            let value = 0;
                            if (data.contractValue && data.contractValue !== "N/A") {
                              // Convert currency to USD for proper comparison
                              value = currencyConverter.convertToUSD(data.contractValue);
                            }
                            return sum + value;
                          }, 0);
                          totalValueA += productTotal;
                        }
                      }
                    }
                  });

                  // Calculate total value for product B across all contracts
                  let totalValueB = 0;
                  contractsWithYearWiseData.forEach((contractData) => {
                    if (contractData) {
                      const product = contractData.products.find((p: any) => p.licenseType === b);
                      if (product && product.yearData) {
                        const yearDataEntries = Object.entries(product.yearData);
                        const validYearData = yearDataEntries.filter(([_, data]: [string, any]) =>
                          data && data.contractValue && data.contractValue !== "N/A"
                        );
                        if (validYearData.length > 0) {
                          const productTotal = validYearData.reduce((sum, [_, data]: [string, any]) => {
                            let value = 0;
                            if (data.contractValue && data.contractValue !== "N/A") {
                              // Convert currency to USD for proper comparison
                              value = currencyConverter.convertToUSD(data.contractValue);
                            }
                            return sum + value;
                          }, 0);
                          totalValueB += productTotal;
                        }
                      }
                    }
                  });

                  // Sort by total value (highest to lowest)
                  return totalValueB - totalValueA;
                });

                // Helper function to get item data for a specific product in a specific contract
                const getItemDataForProduct = (contractItems: any[], productName: string) => {
                  const item = contractItems.find((item) => {
                    const itemProductName = item?.license_type || item?.name || item?.description ||
                      item?.product_name || item?.item_name || item?.service || "Unnamed Item";
                    return itemProductName === productName;
                  });
                  return item;
                };

                // Helper function to format currency - converts all currencies to USD
                const formatItemCurrency = (value: any, contract: any, item?: any) => {
                  if (!value) return "N/A";

                  // Handle currency:amount format (e.g., "PHP:8855748.00")
                  if (typeof value === 'string' && value.includes(':')) {
                    // Use currency converter to convert to USD
                    return currencyConverter.formatAsUSD(value);
                  } else {
                    // Handle plain number - assume USD or get currency from contract/item
                    const currency = (contract as any)?.metadata?.currency ||
                      (item?.currency ? item.currency : "USD");
                    const currencyValue = `${currency}:${value}`;
                    return currencyConverter.formatAsUSD(currencyValue);
                  }
                };

                // Helper function to calculate total
                const calculateTotal = (item: any, contract: any) => {
                  if (!item) return "N/A";

                  try {
                    let total = null;
                    let currency = null;

                    // First check if total is already provided
                    if (item.total || item.total_price || item.total_amount) {
                      total = item.total || item.total_price || item.total_amount;
                      currency = (contract as any)?.metadata?.currency ||
                        (item.currency ? item.currency : undefined);
                    }
                    // Handle price in format "USD:26019.39" with quantity
                    else if (item.price && typeof item.price === 'string' && item.price.includes(':') && item.quantity) {
                      const parts = item.price.split(':');
                      if (parts.length === 2) {
                        currency = parts[0];
                        const unitPrice = parseFloat(parts[1]);
                        const qty = parseFloat(item.quantity);
                        if (!isNaN(unitPrice) && !isNaN(qty)) {
                          total = unitPrice * qty;
                        }
                      }
                    }
                    // Handle other price formats with quantity
                    else {
                      const qty = parseFloat(item.quantity || item.qty || "1");
                      if (!isNaN(qty)) {
                        if (item.unit_price && !isNaN(parseFloat(item.unit_price))) {
                          total = parseFloat(item.unit_price) * qty;
                        } else if (item.price && !isNaN(parseFloat(item.price))) {
                          total = parseFloat(item.price) * qty;
                        } else if (item.rate && !isNaN(parseFloat(item.rate))) {
                          total = parseFloat(item.rate) * qty;
                        } else if (item.amount && !isNaN(parseFloat(item.amount))) {
                          total = parseFloat(item.amount) * qty;
                        }
                      }

                      // If still no total, use the unit price
                      if (total === null) {
                        total = item.price || item.rate || item.amount;
                      }

                      currency = (contract as any)?.metadata?.currency ||
                        (item.currency ? item.currency : undefined);
                    }

                    return formatCurrency(total, currency);
                  } catch (error) {
                    console.error("Error calculating total price:", error, item);
                    return "N/A";
                  }
                };

                if (uniqueProducts.length === 0) {
                  return (
                    <div className="text-center text-muted-foreground py-8">
                      No line items found in any contract
                    </div>
                  );
                }

                return (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px] font-semibold border-r">Product/SKU name</TableHead>
                        {contractsWithYearWiseData.map((contractData) => {
                          if (!contractData) return null;

                          // Show only average column for each contract
                          const averageColumn = (
                            <TableHead key={`${contractData.contractIndex}-average`} className="text-center min-w-[300px] border-l bg-muted/30">
                              <div className="font-semibold pb-2 border-b text-primary">
                                {contractData.contract.title}
                              </div>
                              <div className="grid grid-cols-3 gap-2 mt-2 text-xs font-medium text-muted-foreground">
                                <div>Avg Qty</div>
                                <div>Avg Price</div>
                                <div>Total Value</div>
                              </div>
                            </TableHead>
                          );

                          return averageColumn;
                        })}
                        {/* Fallback columns for contracts without year-wise data */}
                        {contractsWithItems
                          .filter(({ contract }) => !(contract as any)._yearWiseData)
                          .map(({ contract }, index) => (
                            <TableHead key={`fallback-${index}`} className="text-center min-w-[300px] border-l">
                              <div className="font-semibold pb-2 border-b">{contract.title}</div>
                              <div className="grid grid-cols-3 gap-2 mt-2 text-xs font-medium text-muted-foreground">
                                <div>Quantity</div>
                                <div>Unit price</div>
                                <div>Total value</div>
                              </div>
                            </TableHead>
                          ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {uniqueProducts.map((productName, productIndex) => {
                        // First, collect all unit prices for this product across all contracts
                        const productUnitPrices: number[] = [];
                        contractsWithYearWiseData.forEach((contractData) => {
                          if (!contractData) {
                            productUnitPrices.push(0);
                            return;
                          }

                          const product = contractData.products.find((p: any) => p.licenseType === productName);

                          if (product && product.yearData) {
                            const yearDataEntries = Object.entries(product.yearData);

                            const validYearData = yearDataEntries.filter(([_, data]: [string, any]) =>
                              data && data.unitPrice && data.unitPrice !== "N/A"
                            );

                            if (validYearData.length > 0) {
                              const totalUnitPrice = validYearData.reduce((sum, [_, data]: [string, any]) => {
                                let price = 0;
                                if (data.unitPrice && data.unitPrice !== "N/A") {
                                  // Convert currency to USD for proper comparison
                                  price = currencyConverter.convertToUSD(data.unitPrice);
                                }
                                return sum + price;
                              }, 0);
                              const avgUnitPrice = totalUnitPrice / validYearData.length;
                              productUnitPrices.push(avgUnitPrice);
                            } else {
                              productUnitPrices.push(0);
                            }
                          } else {
                            productUnitPrices.push(0);
                          }
                        });

                        // Find the minimum non-zero unit price as the base for comparison
                        const nonZeroPrices = productUnitPrices.filter(price => price > 0);
                        const baseUnitPrice = nonZeroPrices.length > 0 ? Math.min(...nonZeroPrices) : 0;
                        const baseContractIndex = productUnitPrices.findIndex(price => price === baseUnitPrice);

                        return (
                          <TableRow key={productIndex}>
                            <TableCell className="font-medium border-r">{productName}</TableCell>
                            {/* Average data columns only */}
                            {contractsWithYearWiseData.map((contractData, contractIndex) => {
                              if (!contractData) return null;

                              // Find the product in this contract's year-wise data
                              const product = contractData.products.find((p: any) => p.licenseType === productName);

                              // Calculate and show only average column
                              const averageColumn = (() => {
                                if (!product) {
                                  return (
                                    <TableCell key={`${contractData.contractIndex}-average`} className="text-center border-l bg-muted/20">
                                      <div className="grid grid-cols-3 gap-2 text-sm text-muted-foreground">
                                        <div>-</div>
                                        <div>-</div>
                                        <div>-</div>
                                      </div>
                                    </TableCell>
                                  );
                                }

                                // Calculate averages across all years for this product
                                const yearDataEntries = Object.entries(product.yearData);
                                const validYearData = yearDataEntries.filter(([_, data]: [string, any]) =>
                                  data && data.quantity !== "N/A" && data.contractValue !== "N/A"
                                );

                                if (validYearData.length === 0) {
                                  return (
                                    <TableCell key={`${contractData.contractIndex}-average`} className="text-center border-l bg-muted/20">
                                      <div className="grid grid-cols-3 gap-2 text-sm text-muted-foreground">
                                        <div>-</div>
                                        <div>-</div>
                                        <div>-</div>
                                      </div>
                                    </TableCell>
                                  );
                                }

                                // Calculate average quantity
                                const totalQuantity = validYearData.reduce((sum, [_, data]: [string, any]) => {
                                  const qty = parseFloat(data.quantity) || 0;
                                  return sum + qty;
                                }, 0);
                                const avgQuantity = Math.round(totalQuantity / validYearData.length);

                                // Calculate average unit price - preserve currency format for display
                                let avgUnitPriceFormatted = "N/A";
                                let avgUnitPriceNumeric = 0;

                                if (validYearData.length > 0) {
                                  // Get the first valid unit price to determine currency
                                  const firstValidPrice = (validYearData[0][1] as any).unitPrice;
                                  let currency = "USD";

                                  if (firstValidPrice && firstValidPrice.includes(':')) {
                                    currency = firstValidPrice.split(':')[0];
                                  }

                                  // Calculate average in original currency
                                  const totalUnitPrice = validYearData.reduce((sum, [_, data]: [string, any]) => {
                                    let price = 0;
                                    if (data.unitPrice && data.unitPrice !== "N/A") {
                                      if (data.unitPrice.includes(':')) {
                                        price = parseFloat(data.unitPrice.split(':')[1]) || 0;
                                      } else {
                                        price = parseFloat(data.unitPrice) || 0;
                                      }
                                    }
                                    return sum + price;
                                  }, 0);

                                  avgUnitPriceNumeric = totalUnitPrice / validYearData.length;
                                  avgUnitPriceFormatted = `${currency}:${avgUnitPriceNumeric}`;
                                }

                                // Calculate total value across all years (sum, not average) - preserve currency format
                                let totalValueFormatted = "N/A";
                                let totalValueNumeric = 0;

                                if (validYearData.length > 0) {
                                  // Get the first valid contract value to determine currency
                                  const firstValidValue = (validYearData[0][1] as any).contractValue;
                                  let currency = "USD";

                                  if (firstValidValue && firstValidValue.includes(':')) {
                                    currency = firstValidValue.split(':')[0];
                                  }

                                  // Calculate total in original currency
                                  totalValueNumeric = validYearData.reduce((sum, [_, data]: [string, any]) => {
                                    let value = 0;
                                    if (data.contractValue && data.contractValue !== "N/A") {
                                      if (data.contractValue.includes(':')) {
                                        value = parseFloat(data.contractValue.split(':')[1]) || 0;
                                      } else {
                                        value = parseFloat(data.contractValue) || 0;
                                      }
                                    }
                                    return sum + value;
                                  }, 0);

                                  totalValueFormatted = `${currency}:${totalValueNumeric}`;
                                }

                                // Get color coding for unit price based on comparison with base unit price
                                const currentUnitPrice = productUnitPrices[contractIndex] || 0;
                                const isBaseContract = contractIndex === baseContractIndex;
                                const shouldShowColorCoding = !isBaseContract && baseUnitPrice > 0 && currentUnitPrice > 0;

                                const { colorClass: unitPriceColorClass, percentChange } = shouldShowColorCoding
                                  ? getPriceChangeInfo(currentUnitPrice, baseUnitPrice)
                                  : { colorClass: "", percentChange: 0 };

                                // Apply greyish background for baseline contract
                                const baselineClass = isBaseContract && baseUnitPrice > 0 ? "bg-gray-100 text-gray-600" : "";

                                return (
                                  <TableCell key={`${contractData.contractIndex}-average`} className="text-center border-l bg-muted/20">
                                    <div className="grid grid-cols-3 gap-2 text-sm">
                                      <div className="font-medium">{avgQuantity > 0 ? avgQuantity.toString() : "-"}</div>
                                      <div className={`px-2 py-1 rounded ${unitPriceColorClass || baselineClass}`}>
                                        <div className="flex flex-col items-center">
                                          <div>{avgUnitPriceNumeric > 0 ? formatItemCurrency(avgUnitPriceFormatted, contractData.contract) : "-"}</div>
                                          {shouldShowColorCoding && percentChange !== 0 && (
                                            <div className="text-xs font-medium">
                                              {percentChange > 0 ? '+' : ''}{percentChange.toFixed(1)}%
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      <div className="font-medium">{totalValueNumeric > 0 ? formatItemCurrency(totalValueFormatted, contractData.contract) : "-"}</div>
                                    </div>
                                  </TableCell>
                                );
                              })();

                              return averageColumn;
                            })}
                            {/* Fallback columns for contracts without year-wise data */}
                            {contractsWithItems
                              .filter(({ contract }) => !(contract as any)._yearWiseData)
                              .map(({ contract, items }, contractIndex) => {
                                const item = getItemDataForProduct(items, productName);

                                if (!item) {
                                  return (
                                    <TableCell key={`fallback-${contractIndex}`} className="text-center border-l">
                                      <div className="grid grid-cols-3 gap-2 text-sm text-muted-foreground">
                                        <div>-</div>
                                        <div>-</div>
                                        <div>-</div>
                                      </div>
                                    </TableCell>
                                  );
                                }

                                const quantity = item?.quantity || item?.qty || "1";
                                const unitPrice = (() => {
                                  if (item.price && typeof item.price === 'string' && item.price.includes(':')) {
                                    // Pass the full currency string for conversion
                                    return formatItemCurrency(item.price, contract, item);
                                  }
                                  const price = item.unit_price || item.price || item.rate || item.amount;
                                  return formatItemCurrency(price, contract, item);
                                })();
                                const totalValue = calculateTotal(item, contract);

                                return (
                                  <TableCell key={`fallback-${contractIndex}`} className="text-center border-l">
                                    <div className="grid grid-cols-3 gap-2 text-sm">
                                      <div className="font-medium">{quantity}</div>
                                      <div>{unitPrice}</div>
                                      <div className="font-medium">{totalValue}</div>
                                    </div>
                                  </TableCell>
                                );
                              })}
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      {/* Deviations Tab */}
      <TabsContent value="deviations" className="space-y-6">
        <KeyDeviationsDisplay contracts={contracts} />
      </TabsContent>


    </Tabs>
  );
}
