/**
 * Comprehensive Clause Analysis Table Component
 * Displays detailed clause-by-clause analysis across contract documents in a bundle
 */

import React, { useState, useMemo } from 'react';
import { DataTable, DataTableColumn } from '@/components/ui/data-table';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Badge,
} from '@/components/ui/badge';
import {
  Button,
} from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Star,
  X,
  Minus,
} from 'lucide-react';
import {
  ComprehensiveClauseAnalysis,
  GroupDocument,
} from '@/services/bundleAnalysisService';

interface ComprehensiveClauseAnalysisTableProps {
  documents: GroupDocument[];
  clauseAnalysis: ComprehensiveClauseAnalysis;
  className?: string;
}

// Interface for flattened clause data
interface ClauseRowData {
  id: string;
  clauseName: string;
  category: string;
  analysis?: string;
  precedenceOrder?: string[];
  documentData: Record<string, any>;
}

const CATEGORY_LABELS = {
  'Use rights & restrictions': 'Use rights & restrictions',
  'General': 'General',
  'Legal terms': 'Legal terms',
  'Commercial terms': 'Commercial terms',
  'Data protection': 'Data Protection',
  'Others': 'Others',
} as const;

const CATEGORY_ORDER = [
  'Use rights & restrictions',
  'General',
  'Legal terms',
  'Commercial terms',
  'Data protection',
  'Others'
] as const;

// Status icon component for compact view
const StatusIcon = ({ status }: { status?: string }) => {
  if (!status) {
    return (
      <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center" title="No data">
        <Minus className="h-3 w-3 text-gray-400" />
      </div>
    );
  }

  const iconConfig = {
    'SAME': {
      icon: <CheckCircle className="h-3 w-3 text-white" />,
      bg: 'bg-green-500',
      title: 'Same across documents'
    },
    'CONFLICT': {
      icon: <AlertTriangle className="h-3 w-3 text-white" />,
      bg: 'bg-red-500',
      title: 'Conflicting terms'
    },
    'SUPERSEDING': {
      icon: <RefreshCw className="h-3 w-3 text-white" />,
      bg: 'bg-blue-500',
      title: 'Superseding clause'
    },
    'UNIQUE': {
      icon: <Star className="h-3 w-3 text-white" />,
      bg: 'bg-purple-500',
      title: 'Unique to document'
    },
    'MISSING': {
      icon: <X className="h-3 w-3 text-white" />,
      bg: 'bg-gray-400',
      title: 'Missing from document'
    }
  };

  const config = iconConfig[status as keyof typeof iconConfig];
  if (!config) {
    return (
      <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center" title="Unknown status">
        <Minus className="h-3 w-3 text-gray-400" />
      </div>
    );
  }

  return (
    <div className={`w-6 h-6 rounded-full ${config.bg} flex items-center justify-center`} title={config.title}>
      {config.icon}
    </div>
  );
};

// Helper function for status badge colors
const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case 'CONFLICT':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'SAME':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'SUPERSEDING':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'UNIQUE':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'MISSING':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const RISK_COLORS = {
  LOW: 'bg-green-100 text-green-800',
  MEDIUM: 'bg-yellow-100 text-yellow-800',
  HIGH: 'bg-red-100 text-red-800',
};

export function ComprehensiveClauseAnalysisTable({
  documents,
  clauseAnalysis,
  className = '',
}: ComprehensiveClauseAnalysisTableProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const toggleRowExpansion = (clauseKey: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(clauseKey)) {
      newExpanded.delete(clauseKey);
    } else {
      newExpanded.add(clauseKey);
    }
    setExpandedRows(newExpanded);
  };

  // Transform clause analysis data into flat rows for DataTable
  const clauseRows = useMemo(() => {
    const rows: ClauseRowData[] = [];

    Object.entries(clauseAnalysis).forEach(([categoryKey, categoryData]: [string, any]) => {
      // Filter out category_risk_assessment from clause data
      const clauseData = Object.fromEntries(
        Object.entries(categoryData).filter(([key]) => key !== 'category_risk_assessment')
      );

      Object.entries(clauseData).forEach(([clauseKey, clauseDataItem]: [string, any]) => {
        rows.push({
          id: `${categoryKey}-${clauseKey}`,
          clauseName: clauseKey,
          category: categoryKey,
          analysis: clauseDataItem.analysis,
          precedenceOrder: clauseDataItem.precedence_order,
          documentData: clauseDataItem,
        });
      });
    });

    return rows;
  }, [clauseAnalysis]);

  // Define DataTable columns
  const columns = useMemo((): DataTableColumn<ClauseRowData>[] => {
    const cols: DataTableColumn<ClauseRowData>[] = [
      {
        key: 'clauseName',
        header: 'Clause',
        className: 'min-w-[200px] max-w-[300px]',
        render: (_, row) => {
          const isExpanded = expandedRows.has(row.id);
          return (
            <div className="space-y-2">
              {/* Compact Row */}
              <div
                className="flex items-center gap-3 cursor-pointer py-1"
                onClick={() => toggleRowExpansion(row.id)}
              >
                <div className="flex-1 text-sm font-medium">{row.clauseName}</div>
                <div className="flex items-center gap-1.5">
                  {row.analysis && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full" title="Analysis available" />
                  )}
                  {row.precedenceOrder && (
                    <div className="w-2 h-2 bg-purple-500 rounded-full" title="Precedence defined" />
                  )}
                </div>
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-500 transition-transform duration-200" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500 transition-transform duration-200" />
                )}
              </div>

              {/* Expanded Content */}
              {isExpanded && (
                <div className="space-y-3 pt-2 border-t border-gray-100">
                  {row.analysis && (
                    <div>
                      <div className="text-xs font-medium text-gray-700 mb-1">Analysis</div>
                      <p className="text-xs text-gray-600 leading-relaxed">{row.analysis}</p>
                    </div>
                  )}

                  {row.precedenceOrder && (
                    <div>
                      <div className="text-xs font-medium text-gray-700 mb-1">Precedence Order</div>
                      <p className="text-xs text-gray-500">{row.precedenceOrder.join(' → ')}</p>
                    </div>
                  )}

                  {!row.analysis && !row.precedenceOrder && (
                    <div className="text-xs text-gray-400 italic">No additional analysis available</div>
                  )}
                </div>
              )}
            </div>
          );
        },
      },
    ];

    // Add document columns
    documents.forEach(doc => {
      cols.push({
        key: doc.name,
        header: () => (
          <div
            className="truncate text-center"
            title={doc.name}
            style={{ maxWidth: '150px' }}
          >
            {doc.name.length > 12 ? `${doc.name.substring(0, 12)}...` : doc.name}
          </div>
        ),
        className: 'text-center min-w-[120px] max-w-[150px]',
        render: (_, row) => {
          const isExpanded = expandedRows.has(row.id);
          const docData = row.documentData[doc.name];

          return (
            <div className="space-y-2">
              {/* Compact View - Just Icon */}
              <div className="flex justify-center">
                <StatusIcon status={docData?.status} />
              </div>

              {/* Expanded Content */}
              {isExpanded && docData && (
                <div className="space-y-2 pt-2 border-t border-gray-100">
                  <Badge variant="outline" className={getStatusBadgeColor(docData.status)}>
                    {docData.status}
                  </Badge>
                  {docData.value && (
                    <p className="text-xs text-gray-600 break-words leading-relaxed">
                      {docData.value}
                    </p>
                  )}
                  {/* {docData.confidence && (
                    <div className="text-xs text-gray-500">
                      Confidence: {Math.round(docData.confidence * 100)}%
                    </div>
                  )} */}
                </div>
              )}

              {isExpanded && !docData && (
                <div className="text-xs text-gray-400 pt-2 border-t border-gray-100">No data</div>
              )}
            </div>
          );
        },
      });
    });

    return cols;
  }, [documents, expandedRows, toggleRowExpansion]);

  const renderCategorySection = (categoryKey: string, categoryData: any) => {
    const isExpanded = expandedCategories.has(categoryKey);
    const riskData = categoryData.category_risk_assessment;

    // Filter out category_risk_assessment from clause data
    const clauseData = Object.fromEntries(
      Object.entries(categoryData).filter(([key]) => key !== 'category_risk_assessment')
    );

    // Get rows for this category
    const categoryRows = clauseRows.filter(row => row.category === categoryKey);

    return (
      <Card key={categoryKey} className="mb-4">
        <Collapsible open={isExpanded} onOpenChange={() => toggleCategory(categoryKey)}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <span>{CATEGORY_LABELS[categoryKey as keyof typeof CATEGORY_LABELS]}</span>
                  <Badge variant="outline" className="ml-2">
                    {Object.keys(clauseData).length} clauses
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  {riskData && (
                    <>
                      <Badge className={RISK_COLORS[riskData.risk_level as keyof typeof RISK_COLORS]}>
                        {riskData.risk_level} Risk
                      </Badge>
                      {riskData.conflicts > 0 && (
                        <Badge variant="destructive">
                          {riskData.conflicts} conflicts
                        </Badge>
                      )}
                    </>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent>
              <DataTable
                data={categoryRows}
                columns={columns}
                className="border-0"
              />
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Comprehensive Clause Analysis</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExpandedCategories(new Set(Object.keys(clauseAnalysis)))}
          >
            Expand All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExpandedCategories(new Set())}
          >
            Collapse All
          </Button>
        </div>
      </div>

      {CATEGORY_ORDER
        .filter(category => clauseAnalysis[category as keyof typeof clauseAnalysis])
        .map(categoryKey =>
          renderCategorySection(categoryKey, clauseAnalysis[categoryKey as keyof typeof clauseAnalysis])
        )}
    </div>
  );
}
