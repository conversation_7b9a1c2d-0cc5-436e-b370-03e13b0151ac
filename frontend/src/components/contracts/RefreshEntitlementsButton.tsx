/**
 * Refresh Entitlements Button Component
 * Allows users to trigger analysis fields extraction for contracts that don't have them
 */

"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Loader2 } from "lucide-react";
import {
  ContractForAnalysis,
  entitlementAnalysisService,
  ExtractionJobResponse,
} from "@/services/entitlementAnalysisService";
import { cn } from "@/lib/utils";

interface RefreshEntitlementsButtonProps {
  contracts: ContractForAnalysis[];
  selectedContracts: string[];
  onRefreshStart: (jobId: string) => void;
  onRefreshComplete: () => void;
  className?: string;
}

export function RefreshEntitlementsButton({
  contracts,
  selectedContracts,
  onRefreshStart,
  onRefreshComplete,
  className,
}: RefreshEntitlementsButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Filter contracts that need extraction
  const contractsNeedingExtraction = contracts.filter(
    (contract) =>
      selectedContracts.includes(contract.id) &&
      !contract.hasAnalysisFields
  );

  const handleRefresh = async () => {
    if (selectedContracts.length === 0 || contractsNeedingExtraction.length === 0) return;

    setIsLoading(true);
    try {
      const contractsToProcess = contractsNeedingExtraction.map(c => c.id);

      // Use the standard batch extraction method that uses OCR text from database
      const response = await entitlementAnalysisService.extractAnalysisFields({
        contractIds: contractsToProcess,
        forceReExtraction: true
      });

      if (response.jobId) {
        // Job started successfully, refresh the data
        onRefreshComplete();
      } else {
        throw new Error("Failed to start analysis fields extraction job");
      }
    } catch (error) {
      console.error("Error starting extraction:", error);
      // TODO: Add toast notification for error
    } finally {
      setIsLoading(false);
    }
  };

  if (selectedContracts.length === 0 || contractsNeedingExtraction.length === 0) {
    return null;
  }

  return (
    <Button
      onClick={handleRefresh}
      variant="outline"
      size="sm"
      className={cn("gap-2", className)}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4" />
      )}
      {isLoading ? "Extracting..." : "Refresh Entitlements"}
      {!isLoading && contractsNeedingExtraction.length > 0 && (
        <Badge variant="secondary" className="ml-1">
          {contractsNeedingExtraction.length}
        </Badge>
      )}
    </Button>
  );
}
