/**
 * Tabbed Extraction Display Component
 * Displays three-tier contract extraction data in organized tabs
 */

"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  FileText,
  Info,
  Building2,
  IdCard,
  Calendar,
  HandCoins,
  Tag,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Shield,
  Edit,
  Sparkles,
  ShieldCheck,
  ShieldAlert,
  ShieldX,
  Link,
  Hand,
  Activity,
  Timer,
  FileSpreadsheet,
  CheckSquare,
  Search,
  X,
} from "lucide-react";

import {
  contractExtractionService,
  ContractExtractionData,
  FieldValue,
  DynamicField,
  DynamicCategoricalFields,
  SupplierCategoricalFields,
} from "@/services/contractExtractionService";
import { contractService } from "@/services/contractService";
import {
  entitlementAnalysisService,
  ContractForAnalysis
} from "@/services/entitlementAnalysisService";
import { toast } from "sonner";
import { FieldEditDialog, type FieldEditData } from "./FieldEditDialog";
import { formatCurrency } from "@/lib/format-utils";
import { ContractEntitlementDashboard } from "./ContractEntitlementDashboard";
import { IntegrityCheckButton } from "./IntegrityCheckButton";
import { IntegrityResultsDisplay } from "./IntegrityResultsDisplay";
import { RefreshEntitlementsButton } from "./RefreshEntitlementsButton";

interface TabbedExtractionDisplayProps {
  contractId: string;
  className?: string;
  isEditMode?: boolean;
  onEditToggle?: () => void;
}

const getConfidenceColor = (confidence: number) => {
  if (confidence === -1) return "text-blue-600 dark:text-blue-400";
  if (confidence >= 0.8) return "text-green-600 dark:text-green-400";
  if (confidence >= 0.5) return "text-yellow-600 dark:text-yellow-400";
  return "text-red-600 dark:text-red-400";
};

// All dynamic fields are now in categorized format after migration

// Category icons mapping
const getCategoryIcon = (category: string) => {
  switch (category) {
    case "Use rights & restrictions":
      return AlertCircle;
    case "General":
      return Info;
    case "Legal terms":
      return FileText;
    case "Commercial terms":
      return HandCoins;
    case "Data protection":
      return Shield;
    case "Others":
      return Building2;
    default:
      return Info;
  }
};

// Helper function to format field values
const formatFieldValue = (value: string, fieldKey: string, category?: string): string => {
  if (!value || value === "N/A") return value;

  // Special formatting for annually_amount field
  if (fieldKey === "annually_amount") {
    // Handle complex year-by-year format (e.g., "Year 1: EUR:2564034.86, Year 2: EUR:2559225.88, Year 3: EUR:2559225.88")
    if (value.includes("Year") && value.includes(":")) {
      const yearMatches = value.match(/Year\s+\d+:\s*([A-Z]{3}):(\d+(?:\.\d+)?)/gi);
      if (yearMatches && yearMatches.length > 0) {
        let currencyCode = "";
        let totalAmount = 0;
        let validYears = 0;

        yearMatches.forEach(match => {
          const yearMatch = match.match(/Year\s+\d+:\s*([A-Z]{3}):(\d+(?:\.\d+)?)/i);
          if (yearMatch) {
            currencyCode = yearMatch[1]; // Use currency from any match (should be consistent)
            const amount = parseFloat(yearMatch[2]);
            if (!isNaN(amount)) {
              totalAmount += amount;
              validYears++;
            }
          }
        });

        if (validYears > 0) {
          const averageAmount = totalAmount / validYears;
          // Use the existing formatCurrency function with the average amount
          const formattedAverage = formatCurrency(`${currencyCode}:${averageAmount}`);
          return `${formattedAverage}`;
        }
      }
    }
    // Handle simple currency:amount format
    if (value.includes(":") && !value.includes("Year")) {
      return formatCurrency(value);
    }
    return value;
  }

  // Special formatting for relationships field
  if (fieldKey === "relationships") {
    if (value.includes(",")) {
      // Split comma-separated values and format as a clean list
      const relationships = value
        .split(",")
        .map((rel) => rel.trim())
        .filter((rel) => rel);
      return relationships.join(", ");
    }
    return value;
  }

  // Check if this is a date field and format accordingly
  const lowerFieldKey = fieldKey.toLowerCase();
  const isDateField = lowerFieldKey.includes("date");

  if (isDateField) {
    try {
      const date = new Date(value);
      // Check if the date is valid
      if (!isNaN(date.getTime())) {
        // Format as dd/mm/yyyy
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      }
    } catch (error) {
      // If date parsing fails, return original value
      return value;
    }
  }

  // Check if this is a currency field and format accordingly
  const isCurrencyField =
    lowerFieldKey.includes("value") ||
    lowerFieldKey.includes("price") ||
    lowerFieldKey.includes("amount") ||
    lowerFieldKey.includes("cost") ||
    lowerFieldKey.includes("fee") ||
    lowerFieldKey.includes("total");

  // For Commercial terms category, check for currency pattern regardless of field name
  // Pattern: CURRENCY_CODE:AMOUNT (e.g., "USD:4343", "EUR:1234.56")
  const currencyPattern = /^[A-Z]{3}:\d+(?:\.\d+)?$/;

  if ((isCurrencyField || (category === "Commercial terms" && currencyPattern.test(value))) && value.includes(":")) {
    return formatCurrency(value);
  }

  return value;
};

const getConfidenceIcon = (confidence: number) => {
  if (confidence === -1) return Hand;
  if (confidence >= 0.8) return CheckCircle;
  if (confidence >= 0.5) return AlertCircle;
  return XCircle;
};

const FieldDisplay: React.FC<{
  label: string;
  field: FieldValue | undefined;
  icon?: React.ComponentType<{ className?: string }>;
  isEditMode?: boolean;
  onEdit?: (fieldData: FieldEditData) => void;
  fieldKey: string;
  fieldType: "fixed" | "dynamic" | "special";
  vendor?: string;
  isHighlighted?: boolean;
  category?: string;
}> = ({
  label,
  field,
  icon: Icon,
  isEditMode,
  onEdit,
  fieldKey,
  fieldType,
  vendor,
  isHighlighted = false,
  category,
}) => {
    const rawValue = field?.value || "N/A";
    const confidence = field?.confidence || 0;
    const formattedValue = formatFieldValue(rawValue, fieldKey, category);

    if (rawValue === "N/A" && confidence < 0.3 && confidence !== -1) {
      return null;
    }

    const handleEditClick = () => {
      if (onEdit) {
        onEdit({
          fieldType,
          fieldKey,
          currentKey: fieldKey,
          currentValue: rawValue,
          vendor,
        });
      }
    };

    const ConfidenceIcon = getConfidenceIcon(confidence);

    return (
      <div className={`flex items-start justify-between py-2 px-3 rounded-lg transition-colors ${isHighlighted
        ? "bg-yellow-100 dark:bg-yellow-900/30 border-2 border-yellow-300 dark:border-yellow-600"
        : "hover:bg-muted/50"
        }`}>
        <div className="flex items-start gap-2.5 flex-1 min-w-0">
          {Icon ? (
            <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
          ) : (
            <Info className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-0.5">
              <p className="text-sm font-medium text-card-foreground">
                {label}
              </p>
              {/* {fieldType === "special" && vendor && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  Supplier
                </Badge>
              )} */}
            </div>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap break-words">
              {formattedValue}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0 mt-0.5">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${confidence === -1
              ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
              : confidence >= 0.8
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                : confidence >= 0.5
                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
              }`}
          >
            <ConfidenceIcon className="h-3 w-3" />
            {confidence === -1 ? "Manual" : `${Math.round(confidence * 100)}%`}
          </span>
          {isEditMode && (
            <button
              onClick={handleEditClick}
              className="p-1 rounded hover:bg-muted transition-colors"
              title="Edit field"
            >
              <Edit className="h-3 w-3 text-muted-foreground" />
            </button>
          )}
        </div>
      </div>
    );
  };

const DynamicFieldDisplay: React.FC<{
  label: string;
  field: DynamicField;
  isEditMode?: boolean;
  onEdit?: (fieldData: FieldEditData) => void;
  fieldKey: string;
  isHighlighted?: boolean;
  category?: string;
}> = ({ label, field, isEditMode, onEdit, fieldKey, isHighlighted = false, category }) => {
  const rawValue = field?.value || "N/A";
  const confidence = field?.confidence || 0;
  const description = field?.description || "";

  // Format the value using the same logic as FieldDisplay
  const value = typeof rawValue === "string" ? formatFieldValue(rawValue, fieldKey, category) : rawValue;

  if (value === "N/A" && confidence < 0.3 && confidence !== -1) {
    return null;
  }

  const handleEditClick = () => {
    if (onEdit) {
      onEdit({
        fieldType: "dynamic",
        fieldKey,
        currentKey: fieldKey,
        currentValue: Array.isArray(value) ? JSON.stringify(value) : value,
        currentDescription: description,
      });
    }
  };

  const ConfidenceIcon = getConfidenceIcon(confidence);

  // Check if value is an array for tabular display
  const isArrayValue = Array.isArray(value);
  const arrayData = isArrayValue ? value : null;

  return (
    <div className={`flex items-start justify-between py-3 px-3 rounded-lg transition-colors ${isHighlighted
      ? "bg-yellow-100 dark:bg-yellow-900/30 border-2 border-yellow-300 dark:border-yellow-600"
      : "hover:bg-muted/50"
      }`}>
      <div className="flex items-start gap-3 flex-1 min-w-0">
        {description ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <Info className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-sm bg-white text-primary shadow-md">
                <div className="text-xs whitespace-pre-wrap break-words">
                  {description}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <Info className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
        )}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-card-foreground mb-1">
            {label}
          </p>

          {/* Display content based on type */}
          {isArrayValue && arrayData && arrayData.length > 0 ? (
            <div className="mb-2 border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    {Object.keys(arrayData[0]).map((key) => (
                      <TableHead key={key} className="font-medium">
                        {key
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {arrayData.map((item: any, index: number) => (
                    <TableRow key={index} className="hover:bg-muted/30">
                      {Object.entries(item).map(([key, cellValue]) => (
                        <TableCell key={key} className="text-sm">
                          {cellValue === null ||
                            cellValue === undefined ||
                            cellValue === "N/A" ? (
                            <span className="text-muted-foreground">N/A</span>
                          ) : (
                            String(cellValue)
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground whitespace-pre-wrap break-words">
              {typeof value === "string"
                ? value
                : JSON.stringify(value, null, 2)}
            </p>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2 flex-shrink-0 mt-0.5">
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${confidence === -1
            ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
            : confidence >= 0.8
              ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
              : confidence >= 0.5
                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
            }`}
        >
          <ConfidenceIcon className="h-3 w-3" />
          {confidence === -1 ? "Manual" : `${Math.round(confidence * 100)}%`}
        </span>
        {isEditMode && (
          <button
            onClick={handleEditClick}
            className="p-1 rounded hover:bg-muted transition-colors"
            title="Edit field"
          >
            <Edit className="h-3 w-3 text-muted-foreground" />
          </button>
        )}
      </div>
    </div>
  );
};

export function TabbedExtractionDisplay({
  contractId,
  className = "",
  isEditMode = false,
  onEditToggle,
}: TabbedExtractionDisplayProps) {
  const [extractionData, setExtractionData] =
    useState<ContractExtractionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingField, setEditingField] = useState<FieldEditData | null>(null);
  const [activeTab, setActiveTab] = useState("summary");
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  const [isGeneratingCompliance, setIsGeneratingCompliance] = useState(false);
  const [contractForAnalysis, setContractForAnalysis] = useState<ContractForAnalysis | null>(null);

  // Search functionality state
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<{
    section: string;
    fieldKey: string;
    label: string;
  }[]>([]);
  const [highlightedFields, setHighlightedFields] = useState<string[]>([]);
  const [openAccordions, setOpenAccordions] = useState<string[]>([]);

  useEffect(() => {
    fetchExtractionData();
  }, [contractId]);

  // Search functionality with debounce
  useEffect(() => {
    if (!searchQuery || searchQuery.length < 2) {
      setSearchResults([]);
      setHighlightedFields([]);
      return;
    }

    const timeoutId = setTimeout(() => {
      performSearch(searchQuery);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchQuery, extractionData]);

  const performSearch = (query: string) => {
    if (!extractionData) return;

    const results: { section: string; fieldKey: string; label: string }[] = [];
    const lowerQuery = query.toLowerCase();

    // Search in Basic Info fields
    const basicInfoFields = [
      { key: "contract_id", label: "Contract ID" },
      { key: "agreement_type", label: "Agreement Type" },
      { key: "provider", label: "Provider" },
      { key: "client", label: "Client" },
      { key: "total_amount", label: "Total Contract Value" },
      { key: "annually_amount", label: "Annual Contract Value" },
      { key: "start_date", label: "Start Date" },
      { key: "end_date", label: "End Date" },
      { key: "contract_status", label: "Contract Status" },
      { key: "contract_term", label: "Contract Term" },
      { key: "contract_classification", label: "Contract Classification" },
      { key: "auto_renewal", label: "Auto Renewal" },
      { key: "renewal_notice_period", label: "Renewal Notice Period" },
      { key: "relationships", label: "Referenced Documents" },
    ];

    basicInfoFields.forEach(({ key, label }) => {
      const fieldValue = extractionData.fixedFields?.[key as keyof typeof extractionData.fixedFields]?.value || "";
      if (label.toLowerCase().includes(lowerQuery) && fieldValue !== "N/A" && fieldValue !== "") {
        results.push({
          section: "basic-info",
          fieldKey: key,
          label,
        });
      }
    });

    // Search in Dynamic and Special fields
    const categories = [
      "Use rights & restrictions",
      "General",
      "Legal terms",
      "Commercial terms",
      "Data protection",
      "Others",
    ];

    categories.forEach((category) => {
      // Search dynamic fields
      const dynamicFields = extractionData.dynamicFields?.[category as keyof DynamicCategoricalFields] || {};
      Object.entries(dynamicFields).forEach(([fieldKey, field]) => {
        const label = fieldKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        const fieldValue = (field as any)?.value || "";
        if (label.toLowerCase().includes(lowerQuery) && fieldValue !== "N/A" && fieldValue !== "") {
          results.push({
            section: category,
            fieldKey,
            label,
          });
        }
      });

      // Search special fields
      if (extractionData.specialFields) {
        Object.entries(extractionData.specialFields).forEach(([supplierName, supplierData]) => {
          const categoryData = supplierData?.[category as keyof SupplierCategoricalFields];
          if (categoryData) {
            Object.entries(categoryData).forEach(([fieldKey, field]) => {
              const label = fieldKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
              const fieldValue = (field as any)?.value || "";
              if (label.toLowerCase().includes(lowerQuery) && fieldValue !== "N/A" && fieldValue !== "") {
                results.push({
                  section: category,
                  fieldKey: `${supplierName}-${fieldKey}`,
                  label,
                });
              }
            });
          }
        });
      }
    });

    setSearchResults(results);

    // Auto-open accordions and highlight all results
    if (results.length > 0) {
      // Highlight all matching fields
      const allFieldKeys = results.map(result => result.fieldKey);
      setHighlightedFields(allFieldKeys);

      // Open all accordions containing search results
      const sectionsToOpen = Array.from(new Set(results.map(result => result.section)));
      setOpenAccordions(prev => {
        const newOpenAccordions = [...prev];
        sectionsToOpen.forEach(section => {
          if (!newOpenAccordions.includes(section)) {
            newOpenAccordions.push(section);
          }
        });
        return newOpenAccordions;
      });
    }
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
    setHighlightedFields([]);
  };



  const fetchExtractionData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await contractExtractionService.getContractExtraction(
        contractId
      );

      setExtractionData(data);
    } catch (error) {
      console.error("Error fetching extraction data:", error);
      setError("Failed to load extraction data");
    } finally {
      setLoading(false);
    }
  };



  const handleFieldEdit = (fieldData: FieldEditData) => {
    setEditingField(fieldData);
    setEditDialogOpen(true);
  };

  const handleGenerateSummary = async () => {
    if (!contractId) {
      toast.error("Contract ID not available");
      return;
    }

    setIsGeneratingSummary(true);
    try {
      const response = await contractService.generateContractSummary(
        contractId
      );

      if (response.success && response.summary) {
        // Refresh the extraction data to get the updated summary
        await fetchExtractionData();
        toast.success("AI summary generated successfully!");
      } else {
        toast.error("Failed to generate summary");
      }
    } catch (error) {
      console.error("Error generating summary:", error);
      toast.error("Failed to generate AI summary. Please try again.");
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  const handleGenerateCompliance = async () => {
    if (!contractId) {
      toast.error("Contract ID not available");
      return;
    }

    setIsGeneratingCompliance(true);
    try {
      const response = await contractService.generateDORACompliance(contractId);

      if (response.success && response.compliance) {
        // Refresh the extraction data to get the updated compliance analysis
        await fetchExtractionData();
        toast.success("DORA compliance analysis generated successfully!");
      } else {
        toast.error("Failed to generate compliance analysis");
      }
    } catch (error: any) {
      console.error("Error generating compliance analysis:", error);

      // Show specific error message from backend if available
      const errorMessage =
        error?.response?.data?.error ||
        error?.message ||
        "Failed to generate DORA compliance analysis. Please try again.";

      toast.error(errorMessage);
    } finally {
      setIsGeneratingCompliance(false);
    }
  };

  const handleFieldSave = async (updates: {
    fieldType: string;
    fieldKey: string;
    newKey: string;
    newValue: string;
    description?: string;
    vendor?: string;
  }) => {
    try {
      await contractExtractionService.patchContractExtraction(contractId, {
        fieldUpdates: [
          {
            fieldType: updates.fieldType,
            fieldKey: updates.fieldKey,
            newKey: updates.newKey,
            newValue: updates.newValue,
            description: updates.description,
            vendor: updates.vendor,
          },
        ],
      });

      toast.success("Field updated successfully");
      await fetchExtractionData();
      setEditDialogOpen(false);
      setEditingField(null);
    } catch (error) {
      console.error("Error updating field:", error);
      toast.error("Failed to update field");
    }
  };

  // Create contract for analysis data
  const createContractForAnalysis = (): ContractForAnalysis | null => {
    if (!extractionData) return null;

    return {
      id: contractId,
      title: extractionData.fixedFields?.original_filename?.value || "Unknown Contract",
      fileName: extractionData.fixedFields?.original_filename?.value || "Unknown Contract",
      provider: extractionData.fixedFields?.provider?.value || "Unknown Supplier",
      hasAnalysisFields: !!extractionData.analysisFields && Object.keys(extractionData.analysisFields).length > 0
    };
  };

  // Update contract for analysis when extraction data changes
  useEffect(() => {
    setContractForAnalysis(createContractForAnalysis());
  }, [extractionData, contractId]);

  const handleRefreshStart = (jobId: string) => {
    toast.info("Analysis fields extraction started...");
  };

  const handleRefreshComplete = () => {
    // Refresh extraction data to get updated analysis fields
    fetchExtractionData();
    toast.success("Analysis fields extraction completed!");
  };



  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <Skeleton className="h-4 w-4" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !extractionData) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No Extraction Data Available
              </h3>
              <p className="text-muted-foreground mb-4">
                {error ||
                  "This contract hasn't been processed with the three-tier extraction system yet."}
              </p>
              <p className="text-sm text-muted-foreground">
                Upload a new contract to see the three-tier extraction in
                action, or re-process this contract to generate extraction data.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }





  // Helper function to get risk color
  const getRiskColor = (risk: "Low" | "Medium" | "High") => {
    switch (risk) {
      case "Low":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30";
      case "Medium":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30";
      case "High":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30";
    }
  };

  // Helper function to get risk icon
  const getRiskIcon = (risk: "Low" | "Medium" | "High") => {
    switch (risk) {
      case "Low":
        return ShieldCheck;
      case "Medium":
        return ShieldAlert;
      case "High":
        return ShieldX;
      default:
        return ShieldAlert;
    }
  };

  // Helper function to render DORA compliance analysis
  const renderDORACompliance = (dora: any) => {
    const clauses = [
      { key: "criticalFunctions", label: "Critical / Important Functions" },
      { key: "subcontracting", label: "Subcontracting" },
      { key: "auditRights", label: "Audit, Access & Inspection Rights" },
      { key: "terminationRights", label: "Termination Rights" },
      { key: "exitStrategy", label: "Exit Strategy / Transition Support" },
      { key: "incidentNotification", label: "Incident Notification" },
      { key: "dataLocation", label: "Data Location & Processing" },
      {
        key: "businessContinuity",
        label: "Business Continuity & Disaster Recovery",
      },
      { key: "securityMeasures", label: "Security Measures" },
      { key: "regulatoryCooperation", label: "Regulatory Cooperation" },
      { key: "liability", label: "Liability & Insurance" },
      { key: "serviceLevelAgreements", label: "Service Level Agreements" },
    ];

    // Calculate summary statistics
    const validClauses = clauses.filter(({ key }) => dora[key]);
    const presentCount = validClauses.filter(
      ({ key }) => dora[key]?.present === "Yes"
    ).length;
    const absentCount = validClauses.filter(
      ({ key }) => dora[key]?.present === "No"
    ).length;
    const unclearCount = validClauses.filter(
      ({ key }) => dora[key]?.present === "Unclear"
    ).length;
    const highRiskCount = validClauses.filter(
      ({ key }) => dora[key]?.complianceRisk === "High"
    ).length;
    const mediumRiskCount = validClauses.filter(
      ({ key }) => dora[key]?.complianceRisk === "Medium"
    ).length;
    const lowRiskCount = validClauses.filter(
      ({ key }) => dora[key]?.complianceRisk === "Low"
    ).length;

    const compliancePercentage =
      validClauses.length > 0
        ? Math.round((presentCount / validClauses.length) * 100)
        : 0;

    return (
      <div className="space-y-6">
        {/* Summary Section */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-lg p-6 border border-green-200 dark:border-green-800">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
              <ShieldCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                DORA Compliance Overview
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Digital Operational Resilience Act compliance assessment
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {presentCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Present
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {unclearCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Unclear
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {absentCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Absent
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {compliancePercentage}%
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Compliance
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">
                High Risk: {highRiskCount}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">
                Medium Risk: {mediumRiskCount}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600 dark:text-gray-400">
                Low Risk: {lowRiskCount}
              </span>
            </div>
          </div>
        </div>

        {/* Detailed Clauses */}
        <div className="space-y-4">
          {clauses.map(({ key, label }) => {
            const clause = dora[key];
            if (!clause) return null;

            const RiskIcon = getRiskIcon(clause.complianceRisk);
            const riskColorClass = getRiskColor(clause.complianceRisk);

            return (
              <div
                key={key}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {label}
                  </h4>
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${clause.present === "Yes"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                        : clause.present === "No"
                          ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                        }`}
                    >
                      {clause.present === "Yes" && (
                        <CheckCircle className="h-3 w-3" />
                      )}
                      {clause.present === "No" && (
                        <XCircle className="h-3 w-3" />
                      )}
                      {clause.present === "Unclear" && (
                        <AlertCircle className="h-3 w-3" />
                      )}
                      {clause.present}
                    </span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${riskColorClass}`}
                    >
                      <RiskIcon className="h-3 w-3" />
                      {clause.complianceRisk} Risk
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap leading-relaxed">
                  {clause.summary}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Summary
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Detailed
          </TabsTrigger>
          <TabsTrigger value="entitlement" className="flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            Entitlements
          </TabsTrigger>
          <TabsTrigger value="integrity" className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            Integrity
          </TabsTrigger>
          <TabsTrigger value="compliance" className="flex items-center gap-2">
            <ShieldCheck className="h-4 w-4" />
            Compliance
          </TabsTrigger>
        </TabsList>

        {/* Summary Tab */}
        <TabsContent value="summary" className="space-y-4">
          <Card>
            {/* <CardHeader className="bg-primary border-b">
              <CardTitle className="text-lg flex items-center justify-between text-white">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-white" />
                  Document Summary
                  {extractionData?.documentSummary && (
                    <Badge variant="secondary">AI Generated</Badge>
                  )}
                </div>
                {extractionData?.documentSummary && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-500 dark:text-gray-400">
                      Confidence:
                    </span>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${(extractionData.documentSummary?.confidence || 0) >= 0.8
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                          : (extractionData.documentSummary?.confidence || 0) >=
                            0.5
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                            : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                        }`}
                    >
                      {(extractionData.documentSummary?.confidence || 0) === -1
                        ? "Manual"
                        : `${Math.round(
                          (extractionData.documentSummary?.confidence || 0) * 100
                        )}%`}
                    </span>
                  </div>
                )}
              </CardTitle>
            </CardHeader> */}
            <CardContent className="pt-6">
              {extractionData?.documentSummary ? (
                <div className="space-y-4">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                      {extractionData.documentSummary?.value}
                    </p>
                  </div>
                  {extractionData.documentSummary?.extractionDate && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Generated on{" "}
                        {new Date(
                          extractionData.documentSummary.extractionDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    No Summary Available
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Generate an AI-powered summary of this document to get key
                    insights and highlights.
                  </p>
                  <Button
                    onClick={handleGenerateSummary}
                    disabled={isGeneratingSummary}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isGeneratingSummary ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Generating Summary...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Generate Summary
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Details Tab - Unified View */}
        <TabsContent value="details" className="space-y-4">
          {/* Search Bar */}
          <div className="flex items-center gap-2 mb-4 ml-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search fields (minimum 2 characters)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-10"
              />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onEditToggle}
              className="flex-shrink-0"
            >
              <Edit className="h-4 w-4 mr-2" />
              {isEditMode ? "Exit Edit" : "Edit Fields"}
            </Button>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="mb-4 p-3 bg-muted/50 rounded-lg">
              <div className="text-sm font-medium mb-2">
                Found {searchResults.length} field{searchResults.length !== 1 ? 's' : ''} matching "{searchQuery}":
              </div>
              <div className="space-y-1">
                {searchResults.slice(0, 5).map((result, index) => (
                  <div
                    key={`${result.section}-${result.fieldKey}`}
                    className="text-sm text-muted-foreground"
                  >
                    <span className="font-medium">{result.label}</span> in{" "}
                    <span className="capitalize">{result.section === "basic-info" ? "Basic Info" : result.section}</span>
                  </div>
                ))}
                {searchResults.length > 5 && (
                  <div className="text-sm text-muted-foreground">
                    ...and {searchResults.length - 5} more
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Basic Info Accordion */}
          <Accordion
            type="multiple"
            className="w-full"
            value={openAccordions}
            onValueChange={setOpenAccordions}
          >
            <AccordionItem value="basic-info">
              <AccordionTrigger className="text-sm font-medium">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Basic Info
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-1">
                <FieldDisplay
                  label="Contract ID"
                  field={extractionData.fixedFields?.contract_id}
                  icon={IdCard}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="contract_id"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("contract_id")}
                />
                <FieldDisplay
                  label="Agreement Type"
                  field={extractionData.fixedFields?.agreement_type}
                  icon={FileText}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="agreement_type"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("agreement_type")}
                />
                <FieldDisplay
                  label="Provider"
                  field={extractionData.fixedFields?.provider}
                  icon={Building2}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="provider"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("provider")}
                />
                <FieldDisplay
                  label="Client"
                  field={extractionData.fixedFields?.client}
                  icon={Building2}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="client"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("client")}
                />
                <FieldDisplay
                  label="Total Contract Value"
                  field={extractionData.fixedFields?.total_amount}
                  icon={HandCoins}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="total_amount"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("total_amount")}
                />
                <FieldDisplay
                  label="Annual Contract Value (avg)"
                  field={extractionData.fixedFields?.annually_amount}
                  icon={HandCoins}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="annually_amount"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("annually_amount")}
                />
                <FieldDisplay
                  label="Start Date"
                  field={extractionData.fixedFields?.start_date}
                  icon={Calendar}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="start_date"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("start_date")}
                />
                <FieldDisplay
                  label="End Date"
                  field={extractionData.fixedFields?.end_date}
                  icon={Calendar}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="end_date"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("end_date")}
                />
                <FieldDisplay
                  label="Contract Status"
                  field={extractionData.fixedFields?.contract_status}
                  icon={Activity}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="contract_status"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("contract_status")}
                />
                <FieldDisplay
                  label="Contract Term"
                  field={extractionData.fixedFields?.contract_term}
                  icon={Timer}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="contract_term"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("contract_term")}
                />
                <FieldDisplay
                  label="Contract Classification"
                  field={
                    extractionData.fixedFields?.contract_classification
                  }
                  icon={Tag}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="contract_classification"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("contract_classification")}
                />
                <FieldDisplay
                  label="Auto Renewal"
                  field={extractionData.fixedFields?.auto_renewal}
                  icon={RefreshCw}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="auto_renewal"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("auto_renewal")}
                />
                <FieldDisplay
                  label="Renewal Notice Period"
                  field={extractionData.fixedFields?.renewal_notice_period}
                  icon={Clock}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="renewal_notice_period"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("renewal_notice_period")}
                />
                <FieldDisplay
                  label="Referenced Documents"
                  field={extractionData.fixedFields?.relationships}
                  icon={Link}
                  isEditMode={isEditMode}
                  onEdit={handleFieldEdit}
                  fieldKey="relationships"
                  fieldType="fixed"
                  isHighlighted={highlightedFields.includes("relationships")}
                />
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Combined Dynamic and Supplier Fields */}
          {(() => {
            const categories = [
              "Use rights & restrictions",
              "General",
              "Legal terms",
              "Commercial terms",
              "Data protection",
              "Others",
            ];

            return (
              <Accordion
                type="multiple"
                className="w-full !m-0"
                value={openAccordions}
                onValueChange={setOpenAccordions}
              >
                {categories.map((category) => {
                  // Get dynamic fields for this category
                  const dynamicCategoryFields = extractionData.dynamicFields?.[category as keyof DynamicCategoricalFields] || {};

                  // Get special fields for this category from all suppliers
                  const specialCategoryFields: Array<{
                    supplierName: string;
                    fields: Record<string, any>;
                  }> = [];

                  if (extractionData.specialFields) {
                    Object.entries(extractionData.specialFields).forEach(([supplierName, supplierData]) => {
                      const categoryData = supplierData?.[category as keyof SupplierCategoricalFields];
                      if (categoryData && Object.keys(categoryData).length > 0) {
                        specialCategoryFields.push({
                          supplierName,
                          fields: categoryData
                        });
                      }
                    });
                  }

                  // Skip category if no fields in either dynamic or special
                  const hasDynamicFields = Object.keys(dynamicCategoryFields).length > 0;
                  const hasSpecialFields = specialCategoryFields.length > 0;

                  if (!hasDynamicFields && !hasSpecialFields) return null;

                  const CategoryIcon = getCategoryIcon(category);

                  return (
                    <AccordionItem key={category} value={category}>
                      <AccordionTrigger className="text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <CategoryIcon className="h-4 w-4" />
                          {category}
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        {/* Dynamic Fields */}
                        {hasDynamicFields && Object.entries(dynamicCategoryFields)
                          .filter(([_, field]) => {
                            const dynamicField = field as DynamicField;
                            return (
                              dynamicField?.value !== "N/A" &&
                              ((dynamicField?.confidence || 0) > 0.3 ||
                                (dynamicField?.confidence || 0) === -1)
                            );
                          })
                          .map(([fieldKey, field]) => (
                            <DynamicFieldDisplay
                              key={fieldKey}
                              label={fieldKey
                                .replace(/_/g, " ")
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                              field={field as DynamicField}
                              isEditMode={isEditMode}
                              onEdit={handleFieldEdit}
                              fieldKey={fieldKey}
                              isHighlighted={highlightedFields.includes(fieldKey)}
                              category={category}
                            />
                          ))}

                        {/* Special/Supplier Fields */}
                        {specialCategoryFields.map(({ supplierName, fields }) =>
                          Object.entries(fields)
                            .filter(
                              ([_, field]) =>
                                field?.value !== "N/A" &&
                                ((field?.confidence || 0) > 0.3 ||
                                  (field?.confidence || 0) === -1)
                            )
                            .map(([fieldKey, field]) => (
                              <FieldDisplay
                                key={`${supplierName}-${fieldKey}`}
                                label={fieldKey
                                  .replace(/_/g, " ")
                                  .replace(/\b\w/g, (l) => l.toUpperCase())}
                                field={field}
                                isEditMode={isEditMode}
                                onEdit={handleFieldEdit}
                                fieldKey={fieldKey}
                                fieldType="special"
                                vendor={supplierName}
                                isHighlighted={highlightedFields.includes(`${supplierName}-${fieldKey}`)}
                                category={category}
                              />
                            ))
                        )}
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            );
          })()}
        </TabsContent>

        {/* Entitlement Tab */}
        <TabsContent value="entitlement" className="space-y-4">
          {/* Header with Refresh Button */}
          <div className="flex items-center justify-between">
            {contractForAnalysis && (
              <RefreshEntitlementsButton
                contracts={[contractForAnalysis]}
                selectedContracts={[contractId]}
                onRefreshStart={handleRefreshStart}
                onRefreshComplete={handleRefreshComplete}
                className="shrink-0"
              />
            )}
          </div>

          <ContractEntitlementDashboard contractId={contractId} />
        </TabsContent>

        {/* Integrity Tab */}
        <TabsContent value="integrity" className="space-y-4">
          <Card>

            <CardContent>
              {extractionData?.integrityAnalysis ? (
                <IntegrityResultsDisplay analysis={extractionData.integrityAnalysis} />
              ) : (
                <div className="text-center py-8">
                  {/* <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" /> */}
                  {/* <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    No Integrity Analysis Available
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Click "Check Integrity" to analyze this contract's risk factors and compliance with your integrity criteria.
                  </p> */}
                  <div className="flex justify-center">
                    <IntegrityCheckButton
                      contractId={contractId}
                      onAnalysisComplete={fetchExtractionData}
                      disabled={loading}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>



        {/* Clause Compliance Tab */}
        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardContent>
              <div className="space-y-6">
                {/* DORA Compliance Section */}
                <div>
                  <div className="text-center py-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      DORA (Digital Operational Resilience Act)
                    </h3>
                    {!extractionData?.complianceAnalysis?.dora && (
                      <div className="flex justify-center">
                        <Button
                          onClick={handleGenerateCompliance}
                          disabled={isGeneratingCompliance}
                          variant="default"
                          size="lg"
                          className="flex items-center gap-2 px-6 text-base font-medium"
                        >
                          {isGeneratingCompliance ? (
                            <>
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              Analyzing...
                            </>
                          ) : (
                            <>
                              <ShieldCheck className="h-4 w-4" />
                              Check Compliance
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>

                  {extractionData?.complianceAnalysis?.dora ? (
                    <div className="space-y-4">
                      <Accordion type="single" collapsible className="w-full">
                        <AccordionItem value="dora-compliance">
                          <AccordionTrigger className="text-left">
                            <div className="flex items-center gap-2">
                              <ShieldCheck className="h-4 w-4" />
                              <span className="font-medium">
                                DORA Compliance Assessment
                              </span>
                              <Badge variant="outline" className="ml-2">
                                {(() => {
                                  const doraData =
                                    (
                                      extractionData.complianceAnalysis
                                        .dora as any
                                    )?.dora ||
                                    extractionData.complianceAnalysis.dora;
                                  const clauses = [
                                    "criticalFunctions",
                                    "subcontracting",
                                    "auditRights",
                                    "terminationRights",
                                    "exitStrategy",
                                    "incidentNotification",
                                    "dataLocation",
                                    "businessContinuity",
                                    "securityMeasures",
                                    "regulatoryCooperation",
                                    "liability",
                                    "serviceLevelAgreements",
                                  ];
                                  const validCount = clauses.filter(
                                    (key) => doraData[key]
                                  ).length;
                                  return `${validCount} Categories`;
                                })()}
                              </Badge>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="pt-4">
                              {renderDORACompliance(
                                (extractionData.complianceAnalysis.dora as any)
                                  ?.dora ||
                                extractionData.complianceAnalysis.dora
                              )}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                      {extractionData.complianceAnalysis?.extractionDate && (
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Generated on{" "}
                            {new Date(
                              extractionData.complianceAnalysis.extractionDate
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : null}
                </div>

                {/* ESG Compliance Section (Disabled) */}
                <div>
                  <div className="text-center py-8">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
                      ESG (Environmental, Social, and Governance)
                    </h3>
                    <div className="flex justify-center">
                      <Button
                        disabled={true}
                        variant="default"
                        size="lg"
                        className="flex items-center gap-2 px-6 py-3 text-base font-medium bg-gray-400 cursor-not-allowed text-white hover:bg-gray-400"
                      >
                        <ShieldX className="h-4 w-4" />
                        Coming Soon
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Field Edit Dialog */}
      <FieldEditDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        fieldData={editingField}
        onSave={handleFieldSave}
      />
    </div>
  );
}
