"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Loader2, ArrowLeft, ArrowRight, Check } from "lucide-react";
import contractAssessmentService, {
  ContractAssessmentData,
} from "@/services/contractAssessmentService";
import { contractService } from "@/services/contractService";
import { toast } from "sonner";

interface ContractAssessmentWizardProps {
  contractId: string;
  onComplete?: () => void;
  onCancel?: () => void;
}

export function ContractAssessmentWizard({
  contractId,
  onComplete,
  onCancel,
}: ContractAssessmentWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [contract, setContract] = useState<any>(null);
  const [formData, setFormData] = useState<ContractAssessmentData>({
    contractId,
    annualContractValue: undefined,
    totalContractValue: undefined,
    currency: "USD",
    terminationForConvenience: false,
    terminationNoticeDays: undefined,
    autoRenewal: false,
    licenseItems: [],
    geographicLimitations: "",
    customerDefinition: "",
    consumptionReporting: false,
    auditRequirements: "",
    activeUsersPercentage: undefined,
    featureUtilization: undefined,
    usageFrequency: "",
    volumeChangeForecast: "",
    additionalProducts: "",
    redundantProducts: "",
    downgradePotential: false,
    preferredContractLength: "",
    paymentFlexibility: false,
    vendorSwitchWillingness: false,
    satisfactionRating: undefined,
    impactRating: undefined,
    isNicheOffering: false,
  });

  // Fetch contract details
  useEffect(() => {
    const fetchContract = async () => {
      try {
        setLoading(true);
        const contractData = await contractService.getContract(contractId);
        setContract(contractData);

        // Try to fetch existing assessment
        try {
          const assessment = await contractAssessmentService.getAssessment(
            contractId
          );
          if (assessment) {
            setFormData((prev) => ({
              ...prev,
              ...assessment,
            }));
          }
        } catch (error) {
          // No existing assessment, continue with default values
        }
      } catch (error) {
        console.error("Error fetching contract:", error);

        toast.error("Failed to load contract details");
      } finally {
        setLoading(false);
      }
    };

    fetchContract();
  }, [contractId]);

  // Handle form input changes
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      await contractAssessmentService.createOrUpdateAssessment(
        contractId,
        formData
      );
      toast.success("Contract assessment saved successfully");
      if (onComplete) {
        onComplete();
      } else {
        router.push(`/contracts/${contractId}`);
      }
    } catch (error) {
      console.error("Error saving contract assessment:", error);
      toast.error("Failed to save contract assessment");
    } finally {
      setLoading(false);
    }
  };

  // Define wizard steps
  const steps = [
    {
      title: "Contract Metadata",
      description: "Basic contract financial and termination details",
    },
    {
      title: "License & Usage Details",
      description: "Information about license items and usage limitations",
    },
    {
      title: "Usage Evaluation",
      description: "Evaluate current product usage and utilization",
    },
    {
      title: "Strategic Review",
      description: "Future needs and strategic considerations",
    },
  ];

  // Navigate to next step
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (loading && !contract) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Contract Assessment</CardTitle>
          <div className="text-sm text-muted-foreground">
            Step {currentStep + 1} of {steps.length}
          </div>
        </div>
        <CardDescription>
          {contract && (
            <div className="mt-1">
              <span className="font-medium">{contract.title}</span> - Complete
              the assessment to optimize your contract
            </div>
          )}
        </CardDescription>
        <Progress
          value={((currentStep + 1) / steps.length) * 100}
          className="mt-2"
        />
      </CardHeader>

      <CardContent>
        {/* Step 1: Contract Metadata */}
        {currentStep === 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{steps[currentStep].title}</h3>
            <p className="text-sm text-muted-foreground">
              {steps[currentStep].description}
            </p>
            <Separator />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="annualContractValue">
                  Annual Contract Value
                </Label>
                <Input
                  id="annualContractValue"
                  type="number"
                  value={formData.annualContractValue || ""}
                  onChange={(e) =>
                    handleChange(
                      "annualContractValue",
                      parseFloat(e.target.value) || undefined
                    )
                  }
                  placeholder="Enter annual value"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="totalContractValue">Total Contract Value</Label>
                <Input
                  id="totalContractValue"
                  type="number"
                  value={formData.totalContractValue || ""}
                  onChange={(e) =>
                    handleChange(
                      "totalContractValue",
                      parseFloat(e.target.value) || undefined
                    )
                  }
                  placeholder="Enter total value"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.currency || "USD"}
                  onValueChange={(value) => handleChange("currency", value)}
                >
                  <SelectTrigger id="currency">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="terminationNoticeDays">
                  Termination Notice (Days)
                </Label>
                <Input
                  id="terminationNoticeDays"
                  type="number"
                  value={formData.terminationNoticeDays || ""}
                  onChange={(e) =>
                    handleChange(
                      "terminationNoticeDays",
                      parseInt(e.target.value) || undefined
                    )
                  }
                  placeholder="Enter days required for notice"
                />
              </div>

              <div className="flex items-center space-x-2 pt-4">
                <Switch
                  id="terminationForConvenience"
                  checked={formData.terminationForConvenience || false}
                  onCheckedChange={(checked) =>
                    handleChange("terminationForConvenience", checked)
                  }
                />
                <Label htmlFor="terminationForConvenience">
                  Termination for Convenience
                </Label>
              </div>

              <div className="flex items-center space-x-2 pt-4">
                <Switch
                  id="autoRenewal"
                  checked={formData.autoRenewal || false}
                  onCheckedChange={(checked) =>
                    handleChange("autoRenewal", checked)
                  }
                />
                <Label htmlFor="autoRenewal">Auto-Renewal</Label>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: License & Usage Details */}
        {currentStep === 1 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{steps[currentStep].title}</h3>
            <p className="text-sm text-muted-foreground">
              {steps[currentStep].description}
            </p>
            <Separator />

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="geographicLimitations">
                  Geographic Limitations
                </Label>
                <Input
                  id="geographicLimitations"
                  value={formData.geographicLimitations || ""}
                  onChange={(e) =>
                    handleChange("geographicLimitations", e.target.value)
                  }
                  placeholder="Enter geographic limitations (e.g., North America only)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="customerDefinition">
                  Definition of "Customer"
                </Label>
                <Textarea
                  id="customerDefinition"
                  value={formData.customerDefinition || ""}
                  onChange={(e) =>
                    handleChange("customerDefinition", e.target.value)
                  }
                  placeholder="How is 'customer' defined in the contract?"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="consumptionReporting"
                  checked={formData.consumptionReporting || false}
                  onCheckedChange={(checked) =>
                    handleChange("consumptionReporting", checked)
                  }
                />
                <Label htmlFor="consumptionReporting">
                  Consumption Reporting Required
                </Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="auditRequirements">Audit Requirements</Label>
                <Textarea
                  id="auditRequirements"
                  value={formData.auditRequirements || ""}
                  onChange={(e) =>
                    handleChange("auditRequirements", e.target.value)
                  }
                  placeholder="Describe any audit requirements in the contract"
                  rows={3}
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Usage Evaluation */}
        {currentStep === 2 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{steps[currentStep].title}</h3>
            <p className="text-sm text-muted-foreground">
              {steps[currentStep].description}
            </p>
            <Separator />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="activeUsersPercentage">Active Users (%)</Label>
                <Input
                  id="activeUsersPercentage"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.activeUsersPercentage || ""}
                  onChange={(e) =>
                    handleChange(
                      "activeUsersPercentage",
                      parseInt(e.target.value) || undefined
                    )
                  }
                  placeholder="Percentage of active users"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="featureUtilization">
                  Feature Utilization (%)
                </Label>
                <Input
                  id="featureUtilization"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.featureUtilization || ""}
                  onChange={(e) =>
                    handleChange(
                      "featureUtilization",
                      parseInt(e.target.value) || undefined
                    )
                  }
                  placeholder="Percentage of features utilized"
                />
              </div>

              <div className="space-y-2 sm:col-span-2">
                <Label htmlFor="usageFrequency">Usage Frequency</Label>
                <Select
                  value={formData.usageFrequency || ""}
                  onValueChange={(value) =>
                    handleChange("usageFrequency", value)
                  }
                >
                  <SelectTrigger id="usageFrequency">
                    <SelectValue placeholder="Select usage frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="rarely">Rarely</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Step 4: Strategic Review */}
        {currentStep === 3 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{steps[currentStep].title}</h3>
            <p className="text-sm text-muted-foreground">
              {steps[currentStep].description}
            </p>
            <Separator />

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="volumeChangeForecast">
                  Volume Change Forecast (Next 2-3 Years)
                </Label>
                <Select
                  value={formData.volumeChangeForecast || ""}
                  onValueChange={(value) =>
                    handleChange("volumeChangeForecast", value)
                  }
                >
                  <SelectTrigger id="volumeChangeForecast">
                    <SelectValue placeholder="Select expected change" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="significant-increase">
                      Significant Increase ({">"}25%)
                    </SelectItem>
                    <SelectItem value="moderate-increase">
                      Moderate Increase (10-25%)
                    </SelectItem>
                    <SelectItem value="slight-increase">
                      Slight Increase (1-10%)
                    </SelectItem>
                    <SelectItem value="no-change">No Change</SelectItem>
                    <SelectItem value="slight-decrease">
                      Slight Decrease (1-10%)
                    </SelectItem>
                    <SelectItem value="moderate-decrease">
                      Moderate Decrease (10-25%)
                    </SelectItem>
                    <SelectItem value="significant-decrease">
                      Significant Decrease ({">"}25%)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="additionalProducts">
                  Additional Product Interests
                </Label>
                <Textarea
                  id="additionalProducts"
                  value={formData.additionalProducts || ""}
                  onChange={(e) =>
                    handleChange("additionalProducts", e.target.value)
                  }
                  placeholder="List any additional products you're interested in"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="redundantProducts">Redundant Products</Label>
                <Textarea
                  id="redundantProducts"
                  value={formData.redundantProducts || ""}
                  onChange={(e) =>
                    handleChange("redundantProducts", e.target.value)
                  }
                  placeholder="List any redundant products that could be removed"
                  rows={2}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="downgradePotential"
                  checked={formData.downgradePotential || false}
                  onCheckedChange={(checked) =>
                    handleChange("downgradePotential", checked)
                  }
                />
                <Label htmlFor="downgradePotential">
                  Potential for License Downgrade
                </Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="preferredContractLength">
                  Preferred Contract Length
                </Label>
                <Select
                  value={formData.preferredContractLength || ""}
                  onValueChange={(value) =>
                    handleChange("preferredContractLength", value)
                  }
                >
                  <SelectTrigger id="preferredContractLength">
                    <SelectValue placeholder="Select preferred length" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-year">1 Year</SelectItem>
                    <SelectItem value="2-year">2 Years</SelectItem>
                    <SelectItem value="3-year">3 Years</SelectItem>
                    <SelectItem value="5-year">5+ Years</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="satisfactionRating">
                    Satisfaction Rating (1-5)
                  </Label>
                  <Select
                    value={formData.satisfactionRating?.toString() || ""}
                    onValueChange={(value) =>
                      handleChange("satisfactionRating", parseInt(value))
                    }
                  >
                    <SelectTrigger id="satisfactionRating">
                      <SelectValue placeholder="Rate satisfaction" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 - Very Dissatisfied</SelectItem>
                      <SelectItem value="2">2 - Dissatisfied</SelectItem>
                      <SelectItem value="3">3 - Neutral</SelectItem>
                      <SelectItem value="4">4 - Satisfied</SelectItem>
                      <SelectItem value="5">5 - Very Satisfied</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="impactRating">
                    Business Impact Rating (1-5)
                  </Label>
                  <Select
                    value={formData.impactRating?.toString() || ""}
                    onValueChange={(value) =>
                      handleChange("impactRating", parseInt(value))
                    }
                  >
                    <SelectTrigger id="impactRating">
                      <SelectValue placeholder="Rate business impact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 - Minimal Impact</SelectItem>
                      <SelectItem value="2">2 - Low Impact</SelectItem>
                      <SelectItem value="3">3 - Moderate Impact</SelectItem>
                      <SelectItem value="4">4 - High Impact</SelectItem>
                      <SelectItem value="5">5 - Critical Impact</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isNicheOffering"
                  checked={formData.isNicheOffering || false}
                  onCheckedChange={(checked) =>
                    handleChange("isNicheOffering", checked)
                  }
                />
                <Label htmlFor="isNicheOffering">
                  Vendor Provides Niche Offering
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="vendorSwitchWillingness"
                  checked={formData.vendorSwitchWillingness || false}
                  onCheckedChange={(checked) =>
                    handleChange("vendorSwitchWillingness", checked)
                  }
                />
                <Label htmlFor="vendorSwitchWillingness">
                  Willing to Switch Vendors
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="paymentFlexibility"
                  checked={formData.paymentFlexibility || false}
                  onCheckedChange={(checked) =>
                    handleChange("paymentFlexibility", checked)
                  }
                />
                <Label htmlFor="paymentFlexibility">
                  Need Payment Flexibility
                </Label>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={currentStep === 0 ? onCancel : prevStep}
        >
          {currentStep === 0 ? "Cancel" : "Previous"}
        </Button>
        <Button
          onClick={currentStep === steps.length - 1 ? handleSubmit : nextStep}
          disabled={loading}
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {currentStep === steps.length - 1 ? "Complete Assessment" : "Next"}
        </Button>
      </CardFooter>
    </Card>
  );
}
