/**
 * Commercial Entitlements Card
 * Displays total contract value, yearly breakdown, and discount information
 */

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  TrendingUp,
  Percent,
  Building2,
} from "lucide-react";
import { CommercialEntitlements } from "@/services/contractEntitlementService";
import { formatCurrency } from "@/lib/format-utils";

interface CommercialEntitlementsCardProps {
  commercialEntitlements: CommercialEntitlements;
}

export function CommercialEntitlementsCard({
  commercialEntitlements,
}: CommercialEntitlementsCardProps) {
  const formatValue = (value: string) => {
    if (value === "N/A" || !value) return "N/A";

    // Handle currency:amount format
    if (value.includes(":")) {
      const [currency, amount] = value.split(":");
      return formatCurrency(parseFloat(amount), currency);
    }

    return value;
  };

  const getValueColor = (value: string) => {
    if (value === "N/A" || !value) return "text-gray-500";
    return "text-gray-900 dark:text-white";
  };

  return (
    <Card>
      <CardHeader className="bg-primary border-b">
        <CardTitle className="text-lg flex items-center gap-2 text-white">
          <Building2 className="h-5 w-5" />
          Commercial Entitlements
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-6">
        {/* Total Contract Value */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Total Contract Value
            </span>
          </div>
          <div className={`text-2xl font-bold ${getValueColor(commercialEntitlements.totalContractValue)}`}>
            {formatValue(commercialEntitlements.totalContractValue)}
          </div>
        </div>

        {/* Supplier Discount Level */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Supplier Discount Level
            </span>
          </div>
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className={`text-lg font-semibold ${getValueColor(commercialEntitlements.supplierDiscountLevel)}`}>
              {commercialEntitlements.supplierDiscountLevel}
            </div>
          </div>
        </div>


      </CardContent>
    </Card>
  );
}
