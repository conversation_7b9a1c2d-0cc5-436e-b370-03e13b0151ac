/**
 * Entitlement Analysis Modal Component
 * Modal for selecting providers, contracts, and viewing/exporting entitlement analysis
 */

"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

import { Loader2, Download, Eye, FileSpreadsheet } from "lucide-react";
import { toast } from "sonner";
import {
  entitlementAnalysisService,
  ContractForAnalysis,
  AnalysisData,
  ExtractionJobStatus,
} from "@/services/entitlementAnalysisService";
import {
  EntitlementAnalysisTable,
  AnalysisTableStats,
} from "./EntitlementAnalysisTable";
import { RefreshEntitlementsButton } from "./RefreshEntitlementsButton";
import { ExtractionProgressTracker } from "./ExtractionProgressTracker";

interface EntitlementAnalysisModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  preSelectedProvider?: string;
}

export function EntitlementAnalysisModal({
  open,
  onOpenChange,
  preSelectedProvider,
}: EntitlementAnalysisModalProps) {
  const [providers, setProviders] = useState<string[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [contracts, setContracts] = useState<ContractForAnalysis[]>([]);
  const [selectedContracts, setSelectedContracts] = useState<string[]>([]);
  const [analysisData, setAnalysisData] = useState<AnalysisData[]>([]);
  const [analysisFields, setAnalysisFields] = useState<string[]>([]);

  const [loadingProviders, setLoadingProviders] = useState(false);
  const [loadingContracts, setLoadingContracts] = useState(false);
  const [loadingAnalysis, setLoadingAnalysis] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [updatingField, setUpdatingField] = useState(false);

  // Extraction job state
  const [extractionJobId, setExtractionJobId] = useState<string | null>(null);
  const [showExtractionProgress, setShowExtractionProgress] = useState(false);

  const [activeTab, setActiveTab] = useState("selection");

  // Load providers on modal open
  useEffect(() => {
    if (open) {
      loadProviders();
      // Set pre-selected provider if provided
      if (preSelectedProvider) {
        setSelectedProvider(preSelectedProvider);
      }
    } else {
      // Reset state when modal closes
      setSelectedProvider("");
      setContracts([]);
      setSelectedContracts([]);
      setAnalysisData([]);
      setAnalysisFields([]);
      setActiveTab("selection");
    }
  }, [open, preSelectedProvider]);

  // Load contracts when provider changes
  useEffect(() => {
    if (selectedProvider) {
      loadContracts(selectedProvider);
    } else {
      setContracts([]);
      setSelectedContracts([]);
    }
  }, [selectedProvider]);

  const loadProviders = async () => {
    setLoadingProviders(true);
    try {
      const providerList = await entitlementAnalysisService.getProviders();
      setProviders(providerList);
    } catch (error) {
      console.error("Error loading providers:", error);
      toast.error("Failed to load providers");
    } finally {
      setLoadingProviders(false);
    }
  };

  const loadContracts = async (provider: string) => {
    setLoadingContracts(true);
    try {
      const contractList =
        await entitlementAnalysisService.getContractsByProvider(provider);
      setContracts(contractList);
      // Select all contracts by default
      setSelectedContracts(contractList.map((c) => c.id));
    } catch (error) {
      console.error("Error loading contracts:", error);
      toast.error("Failed to load contracts");
    } finally {
      setLoadingContracts(false);
    }
  };

  const handleContractToggle = (contractId: string, checked: boolean) => {
    if (checked) {
      setSelectedContracts((prev) => [...prev, contractId]);
    } else {
      setSelectedContracts((prev) => prev.filter((id) => id !== contractId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedContracts(contracts.map((c) => c.id));
    } else {
      setSelectedContracts([]);
    }
  };

  const handlePreview = async () => {
    if (selectedContracts.length === 0) {
      toast.error("Please select at least one contract");
      return;
    }

    setLoadingAnalysis(true);
    try {
      const response = await entitlementAnalysisService.getAnalysisData(
        selectedContracts
      );
      console.log("API Response:", response);
      console.log("Analysis Data:", response.data);
      console.log("Analysis Fields:", response.fields);
      setAnalysisData(response.data);
      setAnalysisFields(response.fields);
      setActiveTab("preview");
    } catch (error) {
      console.error("Error loading analysis data:", error);
      toast.error("Failed to load analysis data");
    } finally {
      setLoadingAnalysis(false);
    }
  };

  const handleFieldUpdate = async (
    contractId: string,
    fieldKey: string,
    newValue: string,
    isContractLevel: boolean,
    purchasingItemIndex?: number
  ) => {
    setUpdatingField(true);
    try {
      await entitlementAnalysisService.updateAnalysisField({
        contractId,
        fieldKey,
        newValue,
        isContractLevel,
        purchasingItemIndex,
      });

      // Update the local data to reflect the change
      setAnalysisData((prevData) =>
        prevData.map((row) => {
          if (row.contractId === contractId) {
            // For contract-level fields, update all rows for this contract
            if (isContractLevel) {
              return {
                ...row,
                [fieldKey]: newValue,
                [`${fieldKey}_confidence`]: -1,
              };
            }

            // For item-level fields, only update the specific purchasing item row
            if (!isContractLevel && purchasingItemIndex !== undefined) {
              if (row._purchasingItemIndex === purchasingItemIndex) {
                return {
                  ...row,
                  [fieldKey]: newValue,
                  [`${fieldKey}_confidence`]: -1,
                };
              }
              return row; // Don't update other purchasing item rows
            }

            // For contracts without purchasing arrays (single item)
            if (
              purchasingItemIndex === undefined &&
              row._purchasingItemIndex === undefined
            ) {
              return {
                ...row,
                [fieldKey]: newValue,
                [`${fieldKey}_confidence`]: -1,
              };
            }
          }
          return row;
        })
      );
    } catch (error) {
      console.error("Error updating field:", error);
      throw error; // Re-throw to let the component handle the error
    } finally {
      setUpdatingField(false);
    }
  };

  const handleExport = async () => {
    if (selectedContracts.length === 0) {
      toast.error("Please select at least one contract");
      return;
    }

    setExporting(true);
    try {
      const blob = await entitlementAnalysisService.exportAnalysisData(
        selectedContracts
      );
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = `entitlement-analysis-${selectedProvider}-${timestamp}.xlsx`;
      entitlementAnalysisService.downloadFile(blob, filename);
      toast.success("Analysis data exported successfully");
    } catch (error) {
      console.error("Error exporting analysis data:", error);
      toast.error("Failed to export analysis data");
    } finally {
      setExporting(false);
    }
  };

  // Extraction job handlers
  const handleRefreshStart = (jobId: string) => {
    setExtractionJobId(jobId);
    setShowExtractionProgress(true);
    toast.success("Analysis fields extraction started");
  };

  const handleRefreshComplete = (results: ExtractionJobStatus) => {
    setShowExtractionProgress(false);
    setExtractionJobId(null);

    // Refresh contracts list to show updated hasAnalysisFields status
    if (selectedProvider) {
      loadContracts(selectedProvider);
    }

    // Show completion toast
    const successCount = results.progress.completed;
    const failedCount = results.progress.failed;

    if (failedCount === 0) {
      toast.success(`Successfully extracted analysis fields for ${successCount} contracts`);
    } else {
      toast.warning(`Extraction completed: ${successCount} successful, ${failedCount} failed`);
    }
  };

  const handleRefreshError = (error: string) => {
    setShowExtractionProgress(false);
    setExtractionJobId(null);
    toast.error(`Extraction failed: ${error}`);
  };

  const handleCloseProgress = () => {
    setShowExtractionProgress(false);
    setExtractionJobId(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-green-600" />
            Entitlement Analysis
          </DialogTitle>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col min-h-0"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="selection">Selection</TabsTrigger>
            <TabsTrigger value="preview" disabled={analysisData.length === 0}>
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="selection" className="space-y-4">
            {/* Provider Selection */}
            <div className="space-y-2">
              <Label htmlFor="provider-select">Select Provider</Label>
              <Select
                value={selectedProvider}
                onValueChange={setSelectedProvider}
                disabled={loadingProviders}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a provider..." />
                </SelectTrigger>
                <SelectContent>
                  {providers.length === 0 ? (
                    <div className="p-2 text-sm text-muted-foreground">
                      No providers found
                    </div>
                  ) : (
                    providers.map((provider) => (
                      <SelectItem key={provider} value={provider}>
                        {provider.split(" ")[0]}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {loadingProviders && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading providers...
                </div>
              )}
            </div>

            {/* Contract Selection */}
            {selectedProvider && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Select Contracts</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="select-all"
                      checked={
                        selectedContracts.length === contracts.length &&
                        contracts.length > 0
                      }
                      onCheckedChange={handleSelectAll}
                      disabled={loadingContracts}
                    />
                    <Label htmlFor="select-all" className="text-sm">
                      Select All ({contracts.length})
                    </Label>
                    {contracts.length > 0 && (
                      <div className="text-xs text-muted-foreground ml-4">
                        {contracts.filter((c) => c.hasAnalysisFields).length}{" "}
                        with analysis data
                      </div>
                    )}
                  </div>
                </div>

                <ScrollArea className="h-64 border rounded-md p-4">
                  {loadingContracts ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading contracts...
                      </div>
                    </div>
                  ) : contracts.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No contracts found for this provider
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {contracts.map((contract) => (
                        <div
                          key={contract.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={contract.id}
                            checked={selectedContracts.includes(contract.id)}
                            onCheckedChange={(checked) =>
                              handleContractToggle(
                                contract.id,
                                checked as boolean
                              )
                            }
                          />
                          <Label
                            htmlFor={contract.id}
                            className="flex-1 cursor-pointer"
                          >
                            <div>
                              <div className="font-medium">
                                {contract.title}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {contract.fileName}
                                {contract.hasAnalysisFields && (
                                  <span className="ml-2 text-green-600">
                                    • Has Analysis Data
                                  </span>
                                )}
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            )}

            {/* Warning for contracts without analysis data */}
            {selectedProvider &&
              contracts.length > 0 &&
              contracts.filter((c) => c.hasAnalysisFields).length === 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="text-sm text-yellow-800">
                    <strong>Note:</strong> None of the contracts for this
                    provider have analysis fields. Use the "Refresh Entitlements"
                    button to extract analysis fields for these contracts.
                  </div>
                </div>
              )}

            {/* Warning for selected contracts without analysis data */}
            {selectedContracts.length > 0 && (
              (() => {
                const selectedContractsData = contracts.filter(c => selectedContracts.includes(c.id));
                const contractsWithoutFields = selectedContractsData.filter(c => !c.hasAnalysisFields);

                if (contractsWithoutFields.length > 0) {
                  return (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                      <div className="text-sm text-yellow-800">
                        <strong>Note:</strong> {contractsWithoutFields.length} of the selected contracts do not have analysis fields. Use the "Refresh Entitlements" button to extract analysis fields for these contracts.
                      </div>
                    </div>
                  );
                }
                return null;
              })()
            )}

            {/* Extraction Progress Tracker */}
            {showExtractionProgress && extractionJobId && (
              <ExtractionProgressTracker
                jobId={extractionJobId}
                onComplete={handleRefreshComplete}
                onError={handleRefreshError}
                onClose={handleCloseProgress}
                className="mb-4"
              />
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4">
              <div className="flex gap-2">
                <RefreshEntitlementsButton
                  contracts={contracts}
                  selectedContracts={selectedContracts}
                  onRefreshStart={handleRefreshStart}
                  onRefreshComplete={() => {
                    // Refresh contracts list after completion
                    if (selectedProvider) {
                      loadContracts(selectedProvider);
                    }
                  }}
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handlePreview}
                  disabled={selectedContracts.length === 0 || loadingAnalysis}
                >
                  {loadingAnalysis ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Eye className="h-4 w-4 mr-2" />
                  )}
                  Preview
                </Button>
                <Button
                  onClick={handleExport}
                  disabled={selectedContracts.length === 0 || exporting}
                >
                  {exporting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  Export
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent
            value="preview"
            className="space-y-4 flex-1 flex flex-col min-h-0"
          >
            <div className="flex items-center justify-between">
              <AnalysisTableStats
                data={analysisData}
                fields={analysisFields}
                className="flex-1"
              />
              <Button onClick={handleExport} disabled={exporting} size="sm">
                {exporting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export
              </Button>
            </div>

            <EntitlementAnalysisTable
              data={analysisData}
              fields={analysisFields}
              loading={loadingAnalysis || updatingField}
              onFieldUpdate={handleFieldUpdate}
              className="flex-1"
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
