/**
 * Contract Relationship Visualization Component
 * Interactive React Flow diagram showing contract relationships within a supplier's portfolio
 */

"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import ReactFlow, {
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Connection,
  ConnectionMode,
  ReactFlowProvider,
  MarkerType,
  BackgroundVariant,
  Handle,
  Position,
} from "reactflow";
import "reactflow/dist/style.css";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON>it<PERSON>ranch, Loader2, AlertCircle, X } from "lucide-react";
import { toast } from "sonner";

import { ContractRelationshipService } from "@/services/contractRelationshipService";
import { ContractReportingService } from "@/services/contractReportingService";
import { ContractHierarchyService } from "@/services/contractHierarchyService";
import {
  ContractRelationshipVisualizationProps,
  ContractNode,
  ContractRelationship,
  FlowNode,
  SupplierContractRelationshipsResponse,
} from "@/types/contract-relationships";
import { cn } from "@/lib/utils";

// Custom node component for contracts
const ContractNodeComponent = ({ data }: { data: any }) => {
  const { contract, isSelected, onSelect } = data;

  const getNodeColor = (agreementType: string) => {
    const type = agreementType.toUpperCase();
    if (type.includes("MSA") || type.includes("MASTER")) return "#09260D";
    if (type.includes("SOW") || type.includes("STATEMENT")) return "#3b82f6";
    if (type.includes("PO") || type.includes("PURCHASE")) return "#f97316";
    if (type.includes("SLA") || type.includes("SERVICE")) return "#10b981";
    if (type.includes("DPA") || type.includes("DATA")) return "#8b5cf6";
    return "#6b7280";
  };

  const nodeColor = getNodeColor(contract.agreementType);
  const isMSA = contract.agreementType.toUpperCase().includes("MSA");

  // Get display name - prefer filename, fallback to contract number, then ID
  const displayName =
    contract.title && !contract.title.startsWith("Contract ")
      ? contract.title.replace(/\.(pdf|docx?|txt)$/i, "") // Remove file extension
      : contract.contractNumber
        ? `Contract ${contract.contractNumber}`
        : `Contract ${contract.id.slice(-8)}...`; // Show last 8 chars of ID

  return (
    <div
      className={cn(
        "px-4 py-3 shadow-lg rounded-lg border-2 border-white bg-white cursor-pointer transition-all duration-200",
        "w-[240px] h-[160px] flex flex-col", // Fixed dimensions to prevent overlap
        "hover:shadow-xl hover:scale-[1.02]", // Reduced scale to prevent layout issues
        isSelected && "ring-2 ring-blue-500 ring-offset-2",
        isMSA && "ring-2 ring-[#09260D]/30"
      )}
      style={{ borderLeftColor: nodeColor, borderLeftWidth: "4px" }}
      onClick={() => onSelect?.(contract.id)}
    >
      {/* Handle for incoming connections (top) */}
      <Handle
        type="target"
        position={Position.Top}
        style={{
          background: nodeColor,
          border: "2px solid white",
          width: "10px",
          height: "10px",
        }}
      />

      {/* Handle for outgoing connections (bottom) */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: nodeColor,
          border: "2px solid white",
          width: "10px",
          height: "10px",
        }}
      />

      {/* Additional handles for side connections */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: nodeColor,
          border: "2px solid white",
          width: "8px",
          height: "8px",
        }}
      />

      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: nodeColor,
          border: "2px solid white",
          width: "8px",
          height: "8px",
        }}
      />
      <div
        className="text-sm font-semibold text-gray-900 mb-2 truncate"
        title={displayName}
      >
        {displayName}
      </div>
      <div className="text-xs text-gray-600 mb-2 font-medium">
        {contract.agreementType}
      </div>
      {contract.value && contract.value !== "N/A" && (
        <div className="text-xs text-green-600 font-medium mb-1">
          {contract.value}
        </div>
      )}
      {contract.startDate && (
        <div className="text-xs text-gray-500">
          Start: {new Date(contract.startDate).toLocaleDateString()}
        </div>
      )}
      {contract.endDate && (
        <div className="text-xs text-gray-500">
          End: {new Date(contract.endDate).toLocaleDateString()}
        </div>
      )}
    </div>
  );
};

// Node types for React Flow
const nodeTypes = {
  contract: ContractNodeComponent,
};

export function ContractRelationshipVisualization({
  supplierName,
  isOpen,
  onClose,
  criteria = {},
}: ContractRelationshipVisualizationProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] =
    useState<SupplierContractRelationshipsResponse | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [selectedEdges, setSelectedEdges] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const loadingRef = useRef(false);
  const currentRequestRef = useRef<string | null>(null);
  const lastLoadedSupplierRef = useRef<string | null>(null);

  // Memoize criteria to prevent unnecessary re-renders
  const memoizedCriteria = useMemo(() => {
    return (
      criteria || {
        contractIdMatching: true,
        startDateProximity: true,
        executionDateCorrelation: true,
        signatureDateAlignment: true,
        signatureNameMatching: true,
        referencedDocuments: true,
        agreementTypeHierarchy: true,
      }
    );
  }, [
    criteria?.contractIdMatching,
    criteria?.startDateProximity,
    criteria?.executionDateCorrelation,
    criteria?.signatureDateAlignment,
    criteria?.signatureNameMatching,
    criteria?.referencedDocuments,
    criteria?.agreementTypeHierarchy,
  ]);

  // Load contract relationships data
  const loadData = useCallback(async () => {
    if (!isOpen || !supplierName || loadingRef.current) return;

    const requestKey = `${supplierName}-${Date.now()}`;
    currentRequestRef.current = requestKey;
    loadingRef.current = true;
    setIsLoading(true);
    setError(null);

    try {
      const response =
        await ContractRelationshipService.getSupplierContractRelationships(
          supplierName,
          memoizedCriteria
        );

      // Only update state if this is still the current request
      if (currentRequestRef.current === requestKey) {
        setData(response);
      }
    } catch (err) {
      // Only update error if this is still the current request
      if (currentRequestRef.current === requestKey) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to load contract relationships"
        );
      }
    } finally {
      if (currentRequestRef.current === requestKey) {
        setIsLoading(false);
      }
      loadingRef.current = false;
    }
  }, [isOpen, supplierName, memoizedCriteria]);

  // Convert data to React Flow nodes and edges
  const { flowNodes, flowEdges } = useMemo(() => {
    if (!data) return { flowNodes: [], flowEdges: [] };

    const { nodes: contractNodes, relationships } = data.analysis;

    // Create nodes with hierarchical layout (MSA at top, Schedule below)

    // Separate connected and unconnected nodes for better layout
    const connectedNodeIds = new Set<string>();
    relationships.forEach((rel) => {
      connectedNodeIds.add(rel.fromContractId);
      connectedNodeIds.add(rel.toContractId);
    });

    const connectedNodes = contractNodes.filter((node) =>
      connectedNodeIds.has(node.id)
    );
    const unconnectedNodes = contractNodes.filter(
      (node) => !connectedNodeIds.has(node.id)
    );

    const flowNodes: FlowNode[] = contractNodes.map((contract, index) => {
      // Layout constants - match the fixed node dimensions with generous spacing
      const nodeWidth = 240;
      const nodeHeight = 160;
      const horizontalSpacing = 400; // Increased spacing to prevent overlap
      const verticalSpacing = 280; // Increased vertical spacing

      let x: number;
      let y: number;

      if (connectedNodeIds.has(contract.id)) {
        // Connected nodes - create hierarchical layout
        const hierarchyLevel = contract.hierarchyLevel || 1;

        if (hierarchyLevel === 1) {
          // MSA at top center - check if multiple MSAs exist
          const msaNodes = connectedNodes.filter(
            (n) => (n.hierarchyLevel || 1) === 1
          );
          const msaIndex = msaNodes.findIndex((n) => n.id === contract.id);

          if (msaNodes.length === 1) {
            x = 0; // Single MSA at center
          } else {
            // Multiple MSAs - spread them horizontally
            x = (msaIndex - (msaNodes.length - 1) / 2) * horizontalSpacing;
          }
          y = 0;
        } else if (contract.agreementType === "OTHER") {
          // OTHER types at bottom - spread horizontally if multiple
          const otherNodes = connectedNodes.filter(
            (n) => n.agreementType === "OTHER"
          );
          const otherIndex = otherNodes.findIndex((n) => n.id === contract.id);
          x =
            (otherIndex - (otherNodes.length - 1) / 2) *
            (horizontalSpacing * 0.8);
          y = 2 * verticalSpacing;
        } else {
          // Child contracts (SCHEDULE, DPA) - symmetric under MSA
          const childNodes = connectedNodes.filter(
            (n) => (n.hierarchyLevel || 1) > 1 && n.agreementType !== "OTHER"
          );
          const childIndex = childNodes.findIndex((n) => n.id === contract.id);

          // Position symmetrically: distribute evenly around center with more spacing
          if (childNodes.length === 1) {
            x = 0; // Single child at center
          } else if (childNodes.length === 2) {
            x =
              childIndex === 0
                ? -horizontalSpacing * 0.8
                : horizontalSpacing * 0.8;
          } else {
            // For more than 2 children, distribute evenly with full spacing
            x = (childIndex - (childNodes.length - 1) / 2) * horizontalSpacing;
          }
          y = verticalSpacing;
        }
      } else {
        // Unconnected nodes - group by agreement type and arrange horizontally
        const sameTypeNodes = unconnectedNodes.filter(
          (n) => n.agreementType === contract.agreementType
        );
        const typeIndex = sameTypeNodes.findIndex((n) => n.id === contract.id);

        // Get vertical position based on agreement type hierarchy
        let typeLevel = 0;
        switch (contract.agreementType) {
          case "MSA":
          case "NDA":
          case "GMSA":
          case "OMA":
          case "GTC":
          case "EULA":
            typeLevel = 0; // Level 1 - Master agreements
            break;
          case "SCHEDULE":
          case "DPA":
          case "SLA":
            typeLevel = 1; // Level 2 - Schedules and policies
            break;
          case "TSA":
            typeLevel = 1.5; // Level 3 - Support and governance
            break;
          case "SOW":
          case "ORDER":
            typeLevel = 2; // Level 4-6 - Work orders and purchase orders
            break;
          case "INVOICE":
            typeLevel = 3; // Level 7 - Invoices (bottom)
            break;
          case "OTHER":
          default:
            typeLevel = 2.5; // Between orders and invoices
            break;
        }

        // Position horizontally to the right, with generous spacing
        const baseOffset = horizontalSpacing * 3.5; // Move further right to avoid connected nodes
        x = baseOffset + typeIndex * (nodeWidth + 120); // Increased spacing between same-type nodes
        y = typeLevel * verticalSpacing;
      }

      // Add tiny random offset to prevent exact overlaps (reduced since we have better spacing)
      const randomOffset = 2;
      x += (Math.random() - 0.5) * randomOffset;
      y += (Math.random() - 0.5) * randomOffset;

      return {
        id: contract.id,
        type: "contract",
        position: { x, y },
        data: {
          contract,
          isSelected: selectedNode === contract.id,
          onSelect: setSelectedNode,
        },
        draggable: true, // Always allow dragging
      };
    });

    // Create edges from relationships
    const flowEdges = relationships.map((relationship, index) => {
      const getEdgeColor = (type: string, strength: number) => {
        // Base colors by relationship type
        let baseColor: string;
        switch (type) {
          case "HIERARCHY":
            baseColor = "#09260D";
            break;
          case "CONTRACT_ID":
            baseColor = "#dc2626";
            break;
          case "REFERENCE":
            baseColor = "#3b82f6";
            break;
          case "TEMPORAL":
            baseColor = "#10b981";
            break;
          case "SIGNATURE":
            baseColor = "#8b5cf6";
            break;
          default:
            baseColor = "#6b7280";
        }

        // Use lighter colors for weaker relationships
        if (strength >= 0.8) return baseColor; // High confidence - full color
        if (strength >= 0.5) return baseColor + "B3"; // Medium confidence - 70% opacity
        if (strength >= 0.3) return baseColor + "80"; // Low confidence - 50% opacity
        return baseColor + "4D"; // Very low confidence - 30% opacity
      };

      // Calculate visual properties based on confidence
      const strength = relationship.strength || 0;
      const edgeColor = getEdgeColor(relationship.relationshipType, strength);

      // Stroke width based on confidence: 1-5px range
      const strokeWidth = Math.max(
        1,
        Math.min(5, Math.round(strength * 4 + 1))
      );

      // Check if this is a manual relationship (based on reportingTo field)
      const isManual =
        relationship.relationshipType === "MANUAL" ||
        relationship.criteria?.includes("MANUAL_ASSIGNMENT");

      return {
        id: `edge-${index}`,
        source: relationship.fromContractId,
        target: relationship.toContractId,
        type: "smoothstep",
        data: {
          relationship,
          isManual,
        },
        style: {
          stroke: isManual ? "#10b981" : edgeColor,
          strokeWidth: isManual ? 4 : strokeWidth, // Manual relationships get thicker lines
          opacity: isManual ? 0.9 : 1.0, // Let the color opacity handle confidence
          strokeDasharray: isManual
            ? "0"
            : relationship.relationshipType === "HIERARCHY"
              ? "0"
              : "5,5",
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: isManual ? "#10b981" : edgeColor,
          width: 20,
          height: 20,
        },
      };
    });

    return { flowNodes, flowEdges };
  }, [data, selectedNode]);

  // Update React Flow nodes and edges when data changes
  useEffect(() => {
    setNodes(flowNodes);
    setEdges(flowEdges);
  }, [flowNodes, flowEdges, setNodes, setEdges]);

  // Load data when dialog opens
  useEffect(() => {
    if (
      isOpen &&
      supplierName &&
      !loadingRef.current &&
      lastLoadedSupplierRef.current !== supplierName
    ) {
      lastLoadedSupplierRef.current = supplierName;
      loadData();
    }
  }, [isOpen, supplierName, loadData]);

  // Reset data when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setData(null);
      setSelectedNode(null);
      setSelectedEdges([]);
      setError(null);
      loadingRef.current = false;
      currentRequestRef.current = null;
      lastLoadedSupplierRef.current = null;
    }
  }, [isOpen]);

  const onConnect = useCallback(
    async (params: Connection) => {
      if (!params.source || !params.target) return;

      try {
        setIsUpdating(true);

        // Check for basic validation (circular dependencies only)
        if (params.target === params.source) {
          alert("A contract cannot report to itself");
          return;
        }

        // Update the reporting relationship
        await ContractReportingService.updateReportingRelationship(
          params.target,
          params.source
        );

        // Check hierarchy rules and show warning toast if violated
        try {
          // Find the contract nodes to get their agreement types
          const sourceNode = data?.analysis.nodes.find(
            (n) => n.id === params.source
          );
          const targetNode = data?.analysis.nodes.find(
            (n) => n.id === params.target
          );

          if (sourceNode && targetNode) {
            const hierarchyService = new ContractHierarchyService();
            const canBeParentResult = await hierarchyService.canBeParent(
              sourceNode.agreementType,
              targetNode.agreementType
            );

            if (!canBeParentResult.canBeParent) {
              toast.warning(
                `Relationship created successfully, but note: ${sourceNode.agreementType} is not typically a parent of ${targetNode.agreementType} according to standard hierarchy rules.`,
                {
                  duration: 5000,
                }
              );
            }
          }
        } catch (error) {
          // Don't block the relationship creation if hierarchy check fails
        }

        // Add the edge to the visualization
        const newEdge = {
          id: `manual-${params.source}-${params.target}`,
          source: params.source,
          target: params.target,
          type: "smoothstep",
          style: {
            stroke: "#10b981", // Green for manual relationships
            strokeWidth: 3,
            opacity: 0.9,
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: "#10b981",
            width: 20,
            height: 20,
          },
          data: {
            isManual: true,
            relationship: {
              fromContractId: params.source,
              toContractId: params.target,
              relationshipType: "MANUAL",
              strength: 1.0,
              criteria: ["MANUAL_ASSIGNMENT"],
            },
          },
        };

        setEdges((eds) => addEdge(newEdge, eds));

        // Reload data to get updated relationships
        await loadData();
      } catch (error) {
        alert("Failed to create relationship. Please try again.");
      } finally {
        setIsUpdating(false);
      }
    },
    [setEdges, loadData]
  );

  // Handle edge deletion (right-click or delete key)
  const onEdgeDelete = useCallback(
    async (edgeId: string) => {
      const edge = edges.find((e) => e.id === edgeId);
      if (!edge) return;

      // Only allow deletion of manual relationships
      if (!edge.data?.isManual) {
        alert(
          "Only manual relationships can be deleted. Auto-detected relationships cannot be removed."
        );
        return;
      }

      try {
        setIsUpdating(true);

        // Remove the reporting relationship
        await ContractRelationshipService.deleteRelationship(edge.target);

        // Remove the edge from visualization
        setEdges((eds) => eds.filter((e) => e.id !== edgeId));

        // Reload data to get updated relationships
        await loadData();
      } catch (error) {
        alert("Failed to delete relationship. Please try again.");
      } finally {
        setIsUpdating(false);
      }
    },
    [edges, setEdges, loadData]
  );

  // Handle selection changes
  const onSelectionChange = useCallback(({ edges }: { edges: any[] }) => {
    const selectedEdgeIds = edges.map((edge) => edge.id);
    setSelectedEdges(selectedEdgeIds);
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Delete selected edges with Delete or Backspace key
      if (
        (event.key === "Delete" || event.key === "Backspace") &&
        selectedEdges.length > 0
      ) {
        event.preventDefault();

        // Filter to only manual relationships
        const manualEdges = selectedEdges.filter((edgeId) => {
          const edge = edges.find((e) => e.id === edgeId);
          return edge?.data?.isManual;
        });

        if (manualEdges.length === 0) {
          alert(
            "Only manual relationships can be deleted. Auto-detected relationships cannot be removed."
          );
          return;
        }

        const confirmMessage =
          manualEdges.length === 1
            ? "Delete this manual relationship?"
            : `Delete ${manualEdges.length} manual relationships?`;

        if (confirm(confirmMessage)) {
          manualEdges.forEach((edgeId) => {
            onEdgeDelete(edgeId);
          });
          setSelectedEdges([]);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedEdges, edges, onEdgeDelete]);

  const selectedContract = useMemo(() => {
    if (!selectedNode || !data) return null;
    return data.analysis.nodes.find((node) => node.id === selectedNode);
  }, [selectedNode, data]);

  const selectedRelationships = useMemo(() => {
    if (!selectedNode || !data) return [];
    return data.analysis.relationships.filter(
      (rel) =>
        rel.fromContractId === selectedNode || rel.toContractId === selectedNode
    );
  }, [selectedNode, data]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl h-[80vh] max-h-[80vh] p-0 flex flex-col">
        <DialogHeader className="p-6 pb-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" style={{ color: "#09260D" }} />
                Mapping - {supplierName}
              </DialogTitle>
              <DialogDescription>
                Interactive visualization showing how contracts are connected
                within this supplier's portfolio. Drag from one contract to
                another to create relationships. Click on any contract to view
                and delete its relationships.
              </DialogDescription>

              {/* Confidence Legend */}
              <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                <span className="font-medium">Connection Confidence:</span>
                <div className="flex items-center gap-1">
                  <div className="w-6 h-0.5 bg-[#09260D]"></div>
                  <span>High (80%+)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div
                    className="w-6 h-0.5 bg-[#09260D]"
                    style={{ opacity: 0.7 }}
                  ></div>
                  <span>Medium (50-80%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div
                    className="w-6 h-0.5 bg-[#09260D]"
                    style={{ opacity: 0.5 }}
                  ></div>
                  <span>Low (30-50%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-6 h-0.5 bg-[#10b981]"></div>
                  <span>Manual</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isUpdating && (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0 relative">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Analyzing contract relationships...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
                <p className="text-red-600 mb-4">{error}</p>
                <Button variant="outline" onClick={loadData}>
                  Retry
                </Button>
              </div>
            </div>
          ) : data ? (
            <ReactFlowProvider>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onSelectionChange={onSelectionChange}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                fitView
                fitViewOptions={{ padding: 0.3, minZoom: 0.1, maxZoom: 1.5 }}
                nodesDraggable={true} // Always allow dragging
                nodesConnectable={true}
                elementsSelectable={true}
                selectNodesOnDrag={false}
                panOnDrag={true} // Allow panning with any mouse button
                zoomOnScroll={true}
                zoomOnPinch={true}
                zoomOnDoubleClick={false}
                minZoom={0.1}
                maxZoom={2}
                defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
              >
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    const contract = node.data?.contract;
                    if (!contract) return "#6b7280";
                    const type = contract.agreementType.toUpperCase();
                    if (type.includes("MSA")) return "#09260D";
                    if (type.includes("SOW")) return "#3b82f6";
                    if (type.includes("PO")) return "#f97316";
                    return "#6b7280";
                  }}
                  maskColor="rgb(240, 240, 240, 0.6)"
                />
                <Background
                  variant={BackgroundVariant.Dots}
                  gap={12}
                  size={1}
                />
              </ReactFlow>
            </ReactFlowProvider>
          ) : null}

          {/* Node Details Panel for Selected Node */}
          {selectedNode && selectedContract && (
            <div className="absolute top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-10 min-w-[320px] max-w-[400px]">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-sm">Contract Relationships</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedNode(null)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-3 pb-3 border-b">
                <div className="font-medium text-sm">
                  {selectedContract.title}
                </div>
                <div className="text-xs text-gray-600">
                  {selectedContract.agreementType} • {selectedContract.provider}
                </div>
              </div>

              <ScrollArea className="max-h-[400px]">
                <div className="space-y-4">
                  {/* Incoming Relationships */}
                  {(() => {
                    const incomingRels =
                      data?.analysis.relationships.filter(
                        (rel) => rel.toContractId === selectedNode
                      ) || [];

                    return (
                      incomingRels.length > 0 && (
                        <div>
                          <h4 className="font-medium text-xs text-gray-700 mb-2">
                            Incoming Links ({incomingRels.length})
                          </h4>
                          <div className="space-y-2">
                            {incomingRels.map((rel, idx) => {
                              const fromContract = data?.analysis.nodes.find(
                                (n) => n.id === rel.fromContractId
                              );
                              const isManual =
                                rel.relationshipType === "MANUAL";

                              return (
                                <div
                                  key={idx}
                                  className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                                >
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium truncate">
                                      {fromContract?.title ||
                                        "Unknown Contract"}
                                    </div>
                                    <div className="text-gray-600 flex items-center gap-2">
                                      <span>{fromContract?.agreementType}</span>
                                      <Badge
                                        variant={
                                          isManual ? "default" : "secondary"
                                        }
                                        className="text-xs"
                                      >
                                        {isManual ? "Manual" : "Auto"}
                                      </Badge>
                                    </div>
                                  </div>
                                  {isManual && (
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => {
                                        ContractRelationshipService.deleteRelationship(
                                          selectedNode
                                        )
                                          .then(() => {
                                            loadData();
                                            toast.success(
                                              "Relationship deleted successfully"
                                            );
                                          })
                                          .catch(() => {
                                            toast.error(
                                              "Failed to delete relationship"
                                            );
                                          });
                                      }}
                                      disabled={isUpdating}
                                      className="h-6 w-6 p-0 ml-2"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )
                    );
                  })()}

                  {/* Outgoing Relationships */}
                  {(() => {
                    const outgoingRels =
                      data?.analysis.relationships.filter(
                        (rel) => rel.fromContractId === selectedNode
                      ) || [];

                    return (
                      outgoingRels.length > 0 && (
                        <div>
                          <h4 className="font-medium text-xs text-gray-700 mb-2">
                            Outgoing Links ({outgoingRels.length})
                          </h4>
                          <div className="space-y-2">
                            {outgoingRels.map((rel, idx) => {
                              const toContract = data?.analysis.nodes.find(
                                (n) => n.id === rel.toContractId
                              );
                              const isManual =
                                rel.relationshipType === "MANUAL";

                              return (
                                <div
                                  key={idx}
                                  className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                                >
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium truncate">
                                      {toContract?.title || "Unknown Contract"}
                                    </div>
                                    <div className="text-gray-600 flex items-center gap-2">
                                      <span>{toContract?.agreementType}</span>
                                      <Badge
                                        variant={
                                          isManual ? "default" : "secondary"
                                        }
                                        className="text-xs"
                                      >
                                        {isManual ? "Manual" : "Auto"}
                                      </Badge>
                                    </div>
                                  </div>
                                  {isManual && (
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => {
                                        ContractRelationshipService.deleteRelationship(
                                          rel.toContractId
                                        )
                                          .then(() => {
                                            loadData();
                                            toast.success(
                                              "Relationship deleted successfully"
                                            );
                                          })
                                          .catch(() => {
                                            toast.error(
                                              "Failed to delete relationship"
                                            );
                                          });
                                      }}
                                      disabled={isUpdating}
                                      className="h-6 w-6 p-0 ml-2"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )
                    );
                  })()}

                  {/* No Relationships Message */}
                  {(() => {
                    const incomingCount =
                      data?.analysis.relationships.filter(
                        (rel) => rel.toContractId === selectedNode
                      ).length || 0;
                    const outgoingCount =
                      data?.analysis.relationships.filter(
                        (rel) => rel.fromContractId === selectedNode
                      ).length || 0;

                    return (
                      incomingCount === 0 &&
                      outgoingCount === 0 && (
                        <div className="text-center text-gray-500 text-xs py-4">
                          No relationships found for this contract
                        </div>
                      )
                    );
                  })()}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
