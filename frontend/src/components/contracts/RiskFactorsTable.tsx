/**
 * Risk Factors Table Component
 * Displays risk factors identified in a contract
 */

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertTriangle,
  AlertCircle,
  AlertOctagon,
  Info,
  Shield,
  Banknote,
  Briefcase,
  FileCheck,
  Lock,
  Users,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';

/**
 * Risk factor interface
 */
export interface RiskFactor {
  id: string;
  category: string;
  level: string;
  description: string;
  clause: string;
  clauseLocation: string;
  mitigation: string;
}

/**
 * Props for RiskFactorsTable component
 */
interface RiskFactorsTableProps {
  riskFactors: RiskFactor[];
}

/**
 * Risk Factors Table Component
 */
export function RiskFactorsTable({ riskFactors }: RiskFactorsTableProps) {
  const [expandedRows, setExpandedRows] = React.useState<Record<string, boolean>>({});

  // Toggle row expansion
  const toggleRow = (id: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Get risk level icon
  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return <AlertOctagon className="h-5 w-5 text-red-500" />;
      case 'HIGH':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'MEDIUM':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'LOW':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get risk category icon
  const getRiskCategoryIcon = (category: string) => {
    switch (category) {
      case 'LEGAL':
        return <FileCheck className="h-5 w-5 text-purple-500" />;
      case 'FINANCIAL':
        return <Banknote className="h-5 w-5 text-green-500" />;
      case 'OPERATIONAL':
        return <Briefcase className="h-5 w-5 text-blue-500" />;
      case 'COMPLIANCE':
        return <Shield className="h-5 w-5 text-yellow-500" />;
      case 'SECURITY':
        return <Lock className="h-5 w-5 text-red-500" />;
      case 'REPUTATION':
        return <Users className="h-5 w-5 text-pink-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get risk level badge variant
  const getRiskLevelBadgeVariant = (level: string) => {
    switch (level) {
      case 'CRITICAL':
        return 'destructive';
      case 'HIGH':
        return 'destructive';
      case 'MEDIUM':
        return 'warning';
      case 'LOW':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Sort risk factors by level (critical first)
  const sortedRiskFactors = [...riskFactors].sort((a, b) => {
    const levelOrder = { CRITICAL: 0, HIGH: 1, MEDIUM: 2, LOW: 3 };
    return levelOrder[a.level as keyof typeof levelOrder] - levelOrder[b.level as keyof typeof levelOrder];
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">Level</TableHead>
            <TableHead className="w-[100px]">Category</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[100px]">Location</TableHead>
            <TableHead className="w-[80px]">Details</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedRiskFactors.length > 0 ? (
            sortedRiskFactors.map((factor) => (
              <React.Fragment key={factor.id}>
                <TableRow>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-1">
                            {getRiskLevelIcon(factor.level)}
                            <Badge variant={getRiskLevelBadgeVariant(factor.level) as any}>
                              {factor.level}
                            </Badge>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{factor.level} risk level</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-1">
                            {getRiskCategoryIcon(factor.category)}
                            <span className="text-xs">{factor.category}</span>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{factor.category} risk category</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>{factor.description}</TableCell>
                  <TableCell>{factor.clauseLocation}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleRow(factor.id)}
                      className="h-8 w-8 p-0"
                    >
                      {expandedRows[factor.id] ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                      <span className="sr-only">Toggle details</span>
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow className={expandedRows[factor.id] ? '' : 'hidden'}>
                  <TableCell colSpan={5} className="bg-muted/50 p-4">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium">Clause Text</h4>
                        <div className="mt-1 rounded-md bg-muted p-3 text-sm">
                          {factor.clause}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium">Recommended Mitigation</h4>
                        <div className="mt-1 text-sm">
                          {factor.mitigation}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </React.Fragment>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                No risk factors found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
