/**
 * Table Confidence Indicator Component
 * Displays confidence scores in table cells for entitlement analysis
 */

"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { CheckCircle, AlertTriangle, XCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface TableConfidenceIndicatorProps {
  confidence: number;
  field?: string;
  className?: string;
  showPercentage?: boolean;
  size?: "sm" | "md";
}

/**
 * Gets confidence level styling and icon based on confidence score
 */
const getConfidenceLevel = (confidence: number) => {
  // Handle manually edited fields (confidence = -1)
  if (confidence === -1) {
    return {
      color: "text-blue-600 dark:text-blue-400",
      bgColor:
        "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",
      icon: CheckCircle,
      label: "Manual",
      tooltip: "This field was manually edited",
    };
  }

  const percentage = confidence <= 1 ? confidence * 100 : confidence;

  if (percentage >= 80) {
    return {
      color: "text-green-600 dark:text-green-400",
      bgColor:
        "bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800",
      icon: CheckCircle,
      label: "High",
      tooltip: `High confidence (${percentage.toFixed(0)}%)`,
    };
  }

  if (percentage >= 50) {
    return {
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor:
        "bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800",
      icon: AlertTriangle,
      label: "Medium",
      tooltip: `Medium confidence (${percentage.toFixed(0)}%)`,
    };
  }

  return {
    color: "text-red-600 dark:text-red-400",
    bgColor: "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800",
    icon: XCircle,
    label: "Low",
    tooltip: `Low confidence (${percentage.toFixed(0)}%)`,
  };
};

/**
 * Table Confidence Indicator Component
 */
export function TableConfidenceIndicator({
  confidence,
  field,
  className = "",
  showPercentage = true,
  size = "sm",
}: TableConfidenceIndicatorProps) {
  // Ensure confidence is a number
  const safeConfidence = typeof confidence === "number" ? confidence : 0.8;

  const {
    color,
    bgColor,
    icon: Icon,
    label,
    tooltip,
  } = getConfidenceLevel(safeConfidence);

  // Only calculate percentage for non-manual fields
  const percentage =
    safeConfidence === -1
      ? 0
      : safeConfidence <= 1
      ? safeConfidence * 100
      : safeConfidence;
  const sizeClasses = size === "sm" ? "text-xs h-5" : "text-sm h-6";
  const iconSize = size === "sm" ? "h-3 w-3" : "h-4 w-4";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              "font-medium border cursor-help inline-flex items-center gap-1",
              bgColor,
              color,
              sizeClasses,
              className
            )}
          >
            <Icon className={iconSize} />
            {showPercentage && safeConfidence !== -1 ? (
              <span>{percentage.toFixed(0)}%</span>
            ) : (
              <span>{label}</span>
            )}
          </Badge>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          className="max-w-xs bg-primary text-primary-foreground"
        >
          <div className="text-center">
            <div className="font-medium">
              {field
                ? `${field.replace(/_/g, " ").toUpperCase()}`
                : "Field Confidence"}
            </div>
            <div className="text-sm opacity-90">{tooltip}</div>
            {safeConfidence === -1 && (
              <div className="text-xs opacity-75 mt-1">
                Manually verified data
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Compact version for use in tight spaces
 */
export function CompactConfidenceIndicator({
  confidence,
  className = "",
}: {
  confidence: number;
  className?: string;
}) {
  // Ensure confidence is a number
  const safeConfidence = typeof confidence === "number" ? confidence : 0.8;

  const { color, icon: Icon } = getConfidenceLevel(safeConfidence);
  const percentage =
    safeConfidence === -1
      ? 0
      : safeConfidence <= 1
      ? safeConfidence * 100
      : safeConfidence;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn("inline-flex items-center cursor-help", className)}
          >
            <Icon className={cn("h-3 w-3", color)} />
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          className="bg-primary text-primary-foreground"
        >
          {safeConfidence === -1
            ? "Manually edited"
            : `${percentage.toFixed(0)}% confidence`}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Helper function to get confidence value from analysis data
 */
export function getFieldConfidence(
  data: any,
  fieldKey: string,
  defaultConfidence: number = 0.8
): number {
  const confidenceKey = `${fieldKey}_confidence`;
  const confidenceValue = data[confidenceKey];

  // Handle different confidence value formats
  if (confidenceValue === undefined || confidenceValue === null) {
    return defaultConfidence;
  }

  // If it's already a number, return it
  if (typeof confidenceValue === "number") {
    return confidenceValue;
  }

  // If it's an object with a confidence property (from backend metadata)
  if (
    typeof confidenceValue === "object" &&
    confidenceValue.confidence !== undefined
  ) {
    return typeof confidenceValue.confidence === "number"
      ? confidenceValue.confidence
      : defaultConfidence;
  }

  // Try to parse as number if it's a string
  if (typeof confidenceValue === "string") {
    const parsed = parseFloat(confidenceValue);
    return !isNaN(parsed) ? parsed : defaultConfidence;
  }

  return defaultConfidence;
}

/**
 * Helper function to check if field is contract-level
 */
export function isContractLevelField(data: any, fieldKey: string): boolean {
  const contractLevelKey = `${fieldKey}_isContractLevel`;
  return data[contractLevelKey] === true;
}
