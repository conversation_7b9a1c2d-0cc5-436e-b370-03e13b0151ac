/**
 * Contract Status Badge Component
 * Displays calculated contract status with appropriate styling
 */

import { Badge } from "@/components/ui/badge";
import { ContractStatus } from "@/services/contractService";
import { cn } from "@/lib/utils";

interface ContractStatusBadgeProps {
  status: ContractStatus;
  className?: string;
  variant?: "default" | "compact";
}

export function ContractStatusBadge({
  status,
  className,
  variant = "default",
}: ContractStatusBadgeProps) {
  const getStatusConfig = (status: ContractStatus) => {
    switch (status) {
      case "Active":
        return {
          text: "Active",
          className:
            "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        };
      case "Inactive":
        return {
          text: "Inactive",
          className:
            "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        };
      case "Unknown":
        return {
          text: "Unknown",
          className:
            "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        };
      default:
        return {
          text: "Unknown",
          className:
            "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        };
    }
  };

  const config = getStatusConfig(status);
  const isCompact = variant === "compact";

  return (
    <Badge
      variant="outline"
      className={cn(
        "font-medium border",
        config.className,
        isCompact ? "text-xs px-2 py-0.5" : "text-sm px-2.5 py-1",
        className
      )}
    >
      {config.text}
    </Badge>
  );
}

/**
 * Contract Status Indicator - Simple dot indicator for compact displays
 */
interface ContractStatusIndicatorProps {
  status: ContractStatus;
  className?: string;
  showText?: boolean;
}

export function ContractStatusIndicator({
  status,
  className,
  showText = false,
}: ContractStatusIndicatorProps) {
  const getStatusConfig = (status: ContractStatus) => {
    switch (status) {
      case "Active":
        return {
          text: "Active",
          dotColor: "bg-green-500",
          textColor: "text-green-700 dark:text-green-400",
        };
      case "Inactive":
        return {
          text: "Inactive",
          dotColor: "bg-red-500",
          textColor: "text-red-700 dark:text-red-400",
        };
      case "Unknown":
        return {
          text: "Unknown",
          dotColor: "bg-gray-500",
          textColor: "text-gray-700 dark:text-gray-400",
        };
      default:
        return {
          text: "Unknown",
          dotColor: "bg-gray-500",
          textColor: "text-gray-700 dark:text-gray-400",
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className={cn("w-2 h-2 rounded-full", config.dotColor)} />
      {showText && (
        <span className={cn("text-sm font-medium", config.textColor)}>
          {config.text}
        </span>
      )}
    </div>
  );
}
