"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Shield, Loader2, AlertTriangle } from "lucide-react";
import { contractService } from "@/services/contractService";
import { IntegrityService } from "@/services/integrityService";


interface IntegrityCheckButtonProps {
  contractId: string;
  onAnalysisComplete?: () => void;
  disabled?: boolean;
  className?: string;
}

export function IntegrityCheckButton({
  contractId,
  onAnalysisComplete,
  disabled = false,
  className = "",
}: IntegrityCheckButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasActiveConfig, setHasActiveConfig] = useState(true);

  const checkActiveConfiguration = async () => {
    try {
      await IntegrityService.getActiveConfiguration();
      setHasActiveConfig(true);
      return true;
    } catch (error) {
      setHasActiveConfig(false);
      return false;
    }
  };

  const handleIntegrityCheck = async () => {
    // First check if user has an active configuration
    const hasConfig = await checkActiveConfiguration();

    if (!hasConfig) {
      toast.error("No active integrity configuration found. Please configure your integrity criteria in the Personas section first.");
      return;
    }

    setIsGenerating(true);
    try {
      const response = await contractService.generateIntegrityAnalysis(contractId);

      if (response.success && response.analysis) {
        toast.success("Integrity analysis generated successfully!");
        onAnalysisComplete?.();
      } else {
        toast.error("Failed to generate integrity analysis");
      }
    } catch (error: any) {
      console.error("Error generating integrity analysis:", error);

      // Show specific error message from backend if available
      const errorMessage =
        error?.response?.data?.error ||
        error?.message ||
        "Failed to generate integrity analysis. Please try again.";

      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };



  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button
        onClick={handleIntegrityCheck}
        disabled={disabled || isGenerating}
        variant="default"
        size="lg"
        className="flex items-center gap-2 px-6 py-3 text-base font-medium"
      >
        {isGenerating ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Shield className="h-4 w-4" />
        )}
        {isGenerating ? "Analyzing..." : "Check Integrity"}
      </Button>

      {!hasActiveConfig && (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          No Config
        </Badge>
      )}
    </div>
  );
}
