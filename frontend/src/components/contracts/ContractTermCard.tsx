import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar } from "lucide-react";
import { ContractTermCard as ContractTermCardType } from "@/services/contractEntitlementService";

interface ContractTermCardProps {
  contractTerm: ContractTermCardType;
}

export const ContractTermCard: React.FC<ContractTermCardProps> = ({
  contractTerm,
}) => {
  const getValueColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    return "text-gray-900 dark:text-gray-100";
  };

  const formatTerm = (term: string) => {
    if (term === "N/A" || !term) return { number: "N/A", unit: "" };

    // Extract number and unit
    const match = term.match(/^(\d+)\s*(month|year|day)s?/i);
    if (match) {
      const number = parseInt(match[1]);
      const baseUnit = match[2].toLowerCase();

      // Use singular form for 1, plural for others
      const unit = number === 1 ? baseUnit : baseUnit + "s";

      return {
        number: match[1],
        unit: unit
      };
    }

    // Handle special cases
    if (term.toLowerCase() === "perpetual") {
      return { number: "Perpetual", unit: "" };
    }

    // Default case
    return { number: term, unit: "" };
  };

  return (
    <Card className="h-full border-0 shadow-sm bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-blue-100 dark:bg-blue-900/30">
              <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Contract Term
            </span>
          </div>

          {/* Term */}
          <div>
            <div className={`text-xl font-bold pt-3 ${getValueColor(contractTerm.term)}`}>
              {formatTerm(contractTerm.term).number}
              {formatTerm(contractTerm.term).unit && (
                <span className="ml-1">
                  {formatTerm(contractTerm.term).unit}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
