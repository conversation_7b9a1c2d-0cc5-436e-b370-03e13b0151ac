/**
 * Field Edit Dialog Component
 * Allows editing both key and value of contract extraction fields
 */

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Save, X } from "lucide-react";
import { toast } from "sonner";

export interface FieldEditData {
  fieldType: "fixed" | "dynamic" | "special";
  fieldKey: string;
  currentKey: string;
  currentValue: string;
  currentDescription?: string;
  vendor?: string; // For special fields
}

interface FieldEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fieldData: FieldEditData | null;
  onSave: (updates: {
    fieldType: string;
    fieldKey: string;
    newKey: string;
    newValue: string;
    description?: string;
    vendor?: string;
  }) => Promise<void>;
}

export function FieldEditDialog({
  open,
  onOpenChange,
  fieldData,
  onSave,
}: FieldEditDialogProps) {
  const [newKey, setNewKey] = useState("");
  const [newValue, setNewValue] = useState("");
  const [newDescription, setNewDescription] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // Reset form when dialog opens/closes or fieldData changes
  useEffect(() => {
    if (fieldData && open) {
      setNewKey(fieldData.currentKey);
      setNewValue(fieldData.currentValue);
      setNewDescription(fieldData.currentDescription || "");
    } else {
      setNewKey("");
      setNewValue("");
      setNewDescription("");
    }
  }, [fieldData, open]);

  const handleSave = async () => {
    if (!fieldData) return;

    // Validation
    if (!newKey.trim()) {
      toast.error("Field key cannot be empty");
      return;
    }

    if (!newValue.trim()) {
      toast.error("Field value cannot be empty");
      return;
    }

    try {
      setIsSaving(true);

      await onSave({
        fieldType: fieldData.fieldType,
        fieldKey: fieldData.fieldKey,
        newKey: newKey.trim(),
        newValue: newValue.trim(),
        description:
          fieldData.fieldType === "dynamic" ? newDescription.trim() : undefined,
        vendor: fieldData.vendor,
      });

      onOpenChange(false);
      toast.success("Field updated successfully");
    } catch (error) {
      console.error("Error saving field:", error);
      toast.error("Failed to update field");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!fieldData) return null;

  const getFieldTypeLabel = (type: string) => {
    switch (type) {
      case "fixed":
        return "Essential Contract Information";
      case "dynamic":
        return "Contract-Specific Details";
      case "special":
        return "Vendor-Specific Information";
      default:
        return "Field";
    }
  };

  const getFieldTitle = () => {
    let title = `Edit ${getFieldTypeLabel(fieldData.fieldType)}`;
    if (fieldData.vendor) {
      title += ` (${
        fieldData.vendor.charAt(0).toUpperCase() + fieldData.vendor.slice(1)
      })`;
    }
    return title;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{getFieldTitle()}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-y-auto min-h-0">
          {/* Field Key */}
          <div className="space-y-2">
            <Label htmlFor="field-key">Field Key</Label>
            <Input
              id="field-key"
              value={newKey}
              onChange={(e) => setNewKey(e.target.value)}
              placeholder="Enter field key"
              disabled={isSaving}
            />
          </div>

          {/* Field Value */}
          <div className="space-y-2">
            <Label htmlFor="field-value">Field Value</Label>
            <Textarea
              id="field-value"
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              placeholder="Enter field value"
              disabled={isSaving}
              rows={3}
            />
          </div>

          {/* Description for Dynamic Fields */}
          {fieldData.fieldType === "dynamic" && (
            <div className="space-y-2">
              <Label htmlFor="field-description">Description (Optional)</Label>
              <Textarea
                id="field-description"
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
                placeholder="Enter field description"
                disabled={isSaving}
                rows={2}
              />
            </div>
          )}

          {/* Current vs New Preview */}
          {/* <div className="border rounded-lg p-3 bg-muted/30">
            <h4 className="text-sm font-medium mb-2">Preview Changes</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Key:</span>
                <span
                  className={
                    newKey !== fieldData.currentKey
                      ? "text-orange-600 font-medium"
                      : ""
                  }
                >
                  {newKey || "—"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Value:</span>
                <span
                  className={
                    newValue !== fieldData.currentValue
                      ? "text-orange-600 font-medium"
                      : ""
                  }
                >
                  {newValue || "—"}
                </span>
              </div>
              {fieldData.fieldType === "dynamic" && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Description:</span>
                  <span
                    className={
                      newDescription !== (fieldData.currentDescription || "")
                        ? "text-orange-600 font-medium"
                        : ""
                    }
                  >
                    {newDescription || "—"}
                  </span>
                </div>
              )}
            </div>
          </div> */}
        </div>

        <DialogFooter className="flex-shrink-0 mt-4">
          <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !newKey.trim() || !newValue.trim()}
          >
            {isSaving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
