/**
 * Reporting Relationship Editor Component
 * Allows editing of contract reporting relationships
 */

"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { 
  Edit3, 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  X, 
  ArrowUp,
  ArrowDown,
  Link,
  Unlink
} from "lucide-react";

import { ContractReportingService, ContractSummary } from "@/services/contractReportingService";
import { cn } from "@/lib/utils";

interface ReportingRelationshipEditorProps {
  contractId: string;
  currentReportingTo?: string;
  contractTitle: string;
  agreementType: string;
  onUpdate?: () => void;
}

export function ReportingRelationshipEditor({
  contractId,
  currentReportingTo,
  contractTitle,
  agreementType,
  onUpdate,
}: ReportingRelationshipEditorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [availableContracts, setAvailableContracts] = useState<ContractSummary[]>([]);
  const [selectedParent, setSelectedParent] = useState<string>(currentReportingTo || "");
  const [childContracts, setChildContracts] = useState<ContractSummary[]>([]);
  const [reportingChain, setReportingChain] = useState<ContractSummary[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen, contractId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [contracts, children, chain] = await Promise.all([
        ContractReportingService.getContractsForSelection(),
        ContractReportingService.getChildContracts(contractId),
        ContractReportingService.getReportingChain(contractId),
      ]);

      // Filter out the current contract and its children from available parents
      const childIds = new Set(children.children.map(c => c.id));
      const filteredContracts = contracts.filter(
        c => c.id !== contractId && !childIds.has(c.id)
      );

      setAvailableContracts(filteredContracts);
      setChildContracts(children.children);
      setReportingChain(chain.reportingChain);
      setSelectedParent(currentReportingTo || "");

    } catch (error) {
      console.error("Error loading reporting data:", error);
      setError("Failed to load reporting relationship data");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      setError(null);

      if (selectedParent && selectedParent !== currentReportingTo) {
        // Validate first
        const validation = await ContractReportingService.validateReportingRelationship(
          contractId,
          selectedParent
        );

        if (!validation.isValid) {
          setError(`Cannot create relationship: ${validation.reason}`);
          return;
        }

        // Update the relationship
        await ContractReportingService.updateReportingRelationship(
          contractId,
          selectedParent
        );
      } else if (!selectedParent && currentReportingTo) {
        // Remove the relationship
        await ContractReportingService.removeReportingRelationship(contractId);
      }

      setIsOpen(false);
      onUpdate?.();

    } catch (error) {
      console.error("Error updating reporting relationship:", error);
      setError("Failed to update reporting relationship");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    setSelectedParent(currentReportingTo || "");
    setError(null);
    setIsOpen(false);
  };

  const hasChanges = selectedParent !== (currentReportingTo || "");

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Edit3 className="h-4 w-4 mr-2" />
          Edit Reporting
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            Edit Reporting Relationship
          </DialogTitle>
          <DialogDescription>
            Manage how "{contractTitle}" reports to other contracts
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading relationship data...
          </div>
        ) : (
          <div className="space-y-6">
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}

            {/* Current Contract Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Current Contract</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{contractTitle}</div>
                    <div className="text-sm text-muted-foreground">{agreementType}</div>
                  </div>
                  <Badge variant="outline">{contractId}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Parent Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Reports To</label>
              <Select value={selectedParent} onValueChange={setSelectedParent}>
                <SelectTrigger>
                  <SelectValue placeholder="Select parent contract (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No parent contract</SelectItem>
                  {availableContracts.map((contract) => (
                    <SelectItem key={contract.id} value={contract.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{contract.title}</span>
                        <Badge variant="secondary" className="ml-2">
                          {contract.agreementType}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Reporting Chain */}
            {reportingChain.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <ArrowUp className="h-4 w-4" />
                    Current Reporting Chain
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {reportingChain.map((contract, index) => (
                      <div key={contract.id} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-600 rounded-full" />
                        <span className="text-sm">{contract.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {contract.agreementType}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Child Contracts */}
            {childContracts.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <ArrowDown className="h-4 w-4" />
                    Contracts Reporting to This Contract ({childContracts.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {childContracts.map((contract) => (
                      <div key={contract.id} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-600 rounded-full" />
                        <span className="text-sm">{contract.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {contract.agreementType}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <Separator />

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={!hasChanges || isUpdating}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
