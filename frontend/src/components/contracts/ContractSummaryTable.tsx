/**
 * Contract Summary Table Component
 * Displays contract summary information in a structured table format
 */

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Table as TableIcon } from "lucide-react";

interface ContractSummaryTableProps {
  tabularData: Record<string, any>[];
  className?: string;
  showCard?: boolean; // Whether to wrap in a card or just render the table
}

export function ContractSummaryTable({
  tabularData,
  className = "",
  showCard = true,
}: ContractSummaryTableProps) {
  // Render table content without card wrapper
  const tableContent = (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-light text-foreground w-1/3">
                Aspect
              </TableHead>
              <TableHead className="font-light text-foreground">
                Details
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tabularData && tabularData.length > 0 ? (
              tabularData.map((item, index) => (
                <TableRow
                  key={index}
                  className="hover:bg-muted/30 transition-colors"
                >
                  <TableCell className="font-light text-muted-foreground border-r">
                    {item.Aspect}
                  </TableCell>
                  <TableCell className="text-foreground">
                    {item.Details}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  className="text-center py-8 text-muted-foreground"
                >
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No tabular summary data available</p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Summary footer */}
      {tabularData && tabularData.length > 0 && (
        <div className="mt-4 text-sm text-muted-foreground text-center">
          {tabularData.length} key aspects extracted from contract analysis
        </div>
      )}
    </>
  );

  // Return with or without card wrapper based on showCard prop
  if (!showCard) {
    return <div className={className}>{tableContent}</div>;
  }

  return (
    <Card className={`relative ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TableIcon className="h-5 w-5 text-green-600" />
          Contract Summary Table
        </CardTitle>
      </CardHeader>
      <CardContent>{tableContent}</CardContent>
    </Card>
  );
}
