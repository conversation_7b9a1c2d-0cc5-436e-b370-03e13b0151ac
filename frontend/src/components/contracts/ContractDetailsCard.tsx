/**
 * Contract Details Card
 * Displays contract terms, pricing, and payment information
 */

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Calendar,
  CreditCard,
  RefreshCw,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { ContractDetails } from "@/services/contractEntitlementService";

interface ContractDetailsCardProps {
  contractDetails: ContractDetails;
}

export function ContractDetailsCard({
  contractDetails,
}: ContractDetailsCardProps) {
  const getValueColor = (value: string) => {
    if (value === "N/A" || !value) return "text-gray-500";
    return "text-gray-900 dark:text-white";
  };

  const getBooleanBadge = (value: string) => {
    if (value === "N/A" || !value) {
      return <Badge variant="outline">N/A</Badge>;
    }
    
    const isYes = value.toLowerCase().includes("yes") || value.toLowerCase().includes("y");
    const isNo = value.toLowerCase().includes("no") || value.toLowerCase().includes("n");
    
    if (isYes) {
      return (
        <Badge variant="outline" className="text-green-600 border-green-600">
          <CheckCircle className="h-3 w-3 mr-1" />
          Yes
        </Badge>
      );
    } else if (isNo) {
      return (
        <Badge variant="outline" className="text-red-600 border-red-600">
          <XCircle className="h-3 w-3 mr-1" />
          No
        </Badge>
      );
    }
    
    return <Badge variant="outline">{value}</Badge>;
  };

  const getScheduleBadge = (value: string) => {
    if (value === "N/A" || !value) {
      return <Badge variant="outline">N/A</Badge>;
    }
    
    const isAdvance = value.toLowerCase().includes("advance");
    const isArrears = value.toLowerCase().includes("arrears");
    
    if (isAdvance) {
      return (
        <Badge variant="outline" className="text-blue-600 border-blue-600">
          Advance
        </Badge>
      );
    } else if (isArrears) {
      return (
        <Badge variant="outline" className="text-orange-600 border-orange-600">
          Arrears
        </Badge>
      );
    }
    
    return <Badge variant="outline">{value}</Badge>;
  };

  return (
    <Card>
      <CardHeader className="bg-primary border-b">
        <CardTitle className="text-lg flex items-center gap-2 text-white">
          <FileText className="h-5 w-5" />
          Contract Details
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-6">
        {/* Contract Term */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Contract Term
            </span>
          </div>
          <div className={`text-lg font-semibold ${getValueColor(contractDetails.contractTerm)}`}>
            {contractDetails.contractTerm}
          </div>
        </div>

        {/* Fixed Pricing */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <CreditCard className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Fixed Pricing for Term
            </span>
          </div>
          <div>
            {getBooleanBadge(contractDetails.fixedPricing)}
          </div>
        </div>

        {/* True-up / Annual Order */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              True-up / Annual Order
            </span>
          </div>
          <div className={`text-sm ${getValueColor(contractDetails.trueUpOrder)}`}>
            {contractDetails.trueUpOrder}
          </div>
        </div>

        {/* Invoice Schedule */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-orange-600" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Invoice Schedule
            </span>
          </div>
          <div>
            {getScheduleBadge(contractDetails.invoiceSchedule)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
