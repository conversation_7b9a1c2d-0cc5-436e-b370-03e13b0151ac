/**
 * SKU Breakdown Table
 * Displays purchasing array data dynamically with alphabetically sorted columns
 */

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Package, Info } from "lucide-react";
import { SKUItem } from "@/services/contractEntitlementService";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface SKUBreakdownTableProps {
  skuItems: SKUItem[];
}

export function SKUBreakdownTable({ skuItems }: SKUBreakdownTableProps) {
  const formatValue = (value: string) => {
    if (value === "N/A" || !value) return "N/A";

    // Handle currency:amount format
    if (value.includes(":")) {
      const [currency, amount] = value.split(":");
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        return `${currency} ${numAmount.toLocaleString()}`;
      }
    }

    return value;
  };

  const getValueColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    return "text-gray-900 dark:text-gray-100";
  };

  // Get all unique column keys from all items, sorted alphabetically
  const getColumnKeys = () => {
    const allKeys = new Set<string>();
    skuItems.forEach(item => {
      Object.keys(item).forEach(key => allKeys.add(key));
    });
    return Array.from(allKeys).sort();
  };

  // Format column header for display
  const formatColumnHeader = (key: string) => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (!skuItems || skuItems.length === 0) {
    return (
      <Card>
        <CardHeader className="bg-primary border-b">
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="h-5 w-5" />
            SKU-Level Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No SKU Data Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              This contract doesn't have detailed SKU breakdown information.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const columnKeys = getColumnKeys();

  return (
    <Card>
      <CardHeader className="bg-primary border-b">
        <CardTitle className="text-white flex items-center gap-2">
          <Package className="h-5 w-5" />
          SKU-Level Breakdown
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-gray-800">
                {columnKeys.map((key) => (
                  <TableHead key={key} className="min-w-[150px]">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-600" />
                      {formatColumnHeader(key)}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {skuItems.map((item, index) => (
                <TableRow key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  {columnKeys.map((key) => (
                    <TableCell key={key} className="font-medium">
                      <span className={getValueColor(item[key] || "N/A")}>
                        {formatValue(item[key] || "N/A")}
                      </span>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
