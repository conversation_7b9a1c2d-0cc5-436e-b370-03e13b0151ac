/**
 * Use Rights & Legal Card
 * Displays territorial scope, usage rights, IP ownership, and legal terms
 */

"use client";

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  Gavel,
  MapPin,
  UserCheck,
  ArrowRightLeft,
  Copyright,
  TrendingDown,
  AlertTriangle,
} from "lucide-react";
import { UseRightsLegal } from "@/services/contractEntitlementService";

interface UseRightsLegalCardProps {
  useRightsLegal: UseRightsLegal;
}

export function UseRightsLegalCard({
  useRightsLegal,
}: UseRightsLegalCardProps) {
  const getValueColor = (value: string) => {
    if (value === "N/A" || !value || value === "—") return "text-gray-500";
    return "text-gray-900 dark:text-white";
  };

  const formatValue = (value: string) => {
    if (value === "N/A" || !value || value === "—") return "N/A";
    return value;
  };

  const getFieldIcon = (fieldName: string) => {
    switch (fieldName) {
      case "territorialScope":
        return MapPin;
      case "thirdPartyUsage":
        return UserCheck;
      case "licenceMobility":
        return ArrowRightLeft;
      case "ipOwnership":
        return Copyright;
      case "rampedPricingConcession":
        return TrendingDown;
      case "warrantyDisclaimer":
        return AlertTriangle;
      default:
        return Shield;
    }
  };

  const fields = [
    // Only the 6 special fields from mapping.json
    {
      key: "territorialScope",
      label: "Territorial Scope",
      value: useRightsLegal.territorialScope,
      description: "Geographical restrictions on service usage",
    },
    {
      key: "thirdPartyUsage",
      label: "Third-party Usage",
      value: useRightsLegal.thirdPartyUsage,
      description: "Rights for third-party or affiliate usage",
    },
    {
      key: "licenceMobility",
      label: "Licence Mobility (SA)",
      value: useRightsLegal.licenceMobility,
      description: "Software Assurance licence mobility rights",
    },
    {
      key: "ipOwnership",
      label: "IP Ownership",
      value: useRightsLegal.ipOwnership,
      description: "Intellectual property ownership terms",
    },
    {
      key: "rampedPricingConcession",
      label: "Ramped-pricing Concession",
      value: useRightsLegal.rampedPricingConcession,
      description: "Pricing concessions or ramp-up terms",
    },
    {
      key: "warrantyDisclaimer",
      label: "Warranty / Disclaimer",
      value: useRightsLegal.warrantyDisclaimer,
      description: "Warranty terms and disclaimers",
    },
  ];

  return (
    <Card>
      {/* <CardHeader className="bg-primary border-b">
        <CardTitle className="text-lg flex items-center gap-2 text-white">
          <Gavel className="h-5 w-5" />
          Use Rights & Legal Terms
        </CardTitle>
      </CardHeader> */}
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {fields.map((field) => {
            const IconComponent = getFieldIcon(field.key);
            return (
              <div key={field.key} className="space-y-3">
                <div className="flex items-center gap-2">
                  <IconComponent className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {field.label}
                  </span>
                </div>

                <div className="pl-6 space-y-2">
                  <div className={`text-sm ${getValueColor(field.value)}`}>
                    {formatValue(field.value)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {field.description}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Summary Badge */}
        {/* <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Legal Terms Coverage
            </span>
            <Badge variant="outline" className="text-xs">
              {fields.filter(f => f.value !== "N/A" && f.value !== "—" && f.value).length} / {fields.length} fields available
            </Badge>
          </div>
        </div> */}
      </CardContent>
    </Card>
  );
}
