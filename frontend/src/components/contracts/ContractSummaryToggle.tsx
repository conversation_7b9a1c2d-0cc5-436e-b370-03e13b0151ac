/**
 * Contract Summary Toggle Component
 * Provides toggle between paragraph and table view for contract summaries
 */

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Table, Sparkles, Loader2 } from "lucide-react";
import { ContractSummaryTable } from "./ContractSummaryTable";
import { contractService } from "@/services/contractService";
import { toast } from "sonner";

interface ContractSummaryToggleProps {
  contract: any;
  className?: string;
  onSummaryGenerated?: (
    summary: string,
    tabularSummary: Record<string, any>[]
  ) => void;
}

type ViewMode = "paragraph" | "table";

export function ContractSummaryToggle({
  contract,
  className = "",
  onSummaryGenerated,
}: ContractSummaryToggleProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("paragraph");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSummary, setGeneratedSummary] = useState<string | null>(null);
  const [generatedTabularSummary, setGeneratedTabularSummary] = useState<
    Record<string, any>[] | null
  >(null);

  // Load view mode preference from localStorage on component mount
  useEffect(() => {
    const savedViewMode = localStorage.getItem(
      "contractSummaryViewMode"
    ) as ViewMode;
    if (
      savedViewMode &&
      (savedViewMode === "paragraph" || savedViewMode === "table")
    ) {
      setViewMode(savedViewMode);
    }
  }, []);

  // Save view mode preference to localStorage when it changes
  const handleViewModeChange = (newViewMode: ViewMode) => {
    setViewMode(newViewMode);
    localStorage.setItem("contractSummaryViewMode", newViewMode);
  };

  // Check if we have AI-generated summaries
  const hasAINarrative =
    contract.metadata?.aiGeneratedSummary &&
    contract.metadata.aiGeneratedSummary.trim().length > 0;
  const hasAITabular =
    contract.metadata?.aiGeneratedTabularSummary &&
    Array.isArray(contract.metadata.aiGeneratedTabularSummary) &&
    contract.metadata.aiGeneratedTabularSummary.length > 0;

  const narrativeSummary =
    generatedSummary ||
    contract.metadata?.aiGeneratedSummary ||
    generateFallbackSummary();
  const tabularSummary =
    generatedTabularSummary ||
    contract.metadata?.aiGeneratedTabularSummary ||
    [];

  const handleGenerateSummary = async () => {
    if (!contract.id) {
      toast.error("Contract ID not available");
      return;
    }

    setIsGenerating(true);
    try {
      const response = await contractService.generateContractSummary(
        contract.id
      );

      if (response.success && response.summary) {
        setGeneratedSummary(response.summary);

        if (response.tabularSummary) {
          setGeneratedTabularSummary(response.tabularSummary);
        }

        onSummaryGenerated?.(response.summary, response.tabularSummary || []);
        toast.success("AI summaries generated successfully!");
      } else {
        toast.error("Failed to generate summaries");
      }
    } catch (error) {
      console.error("Error generating summaries:", error);
      toast.error("Failed to generate AI summaries. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  function generateFallbackSummary(): string {
    const agreementType = contract.agreementType || "contract";
    const title = contract.title || "Untitled Contract";
    const counterparty = contract.counterparty || "unknown counterparty";
    const status = contract.status?.toLowerCase() || "active";

    let summary = `This ${agreementType.toLowerCase()} titled "${title}" involves ${counterparty} as the counterparty. `;
    summary += `The agreement is currently ${status}`;

    if (contract.value) {
      summary += ` with a contract value of ${contract.value}`;
    }

    if (contract.startDate) {
      summary += ` and became effective on ${new Date(
        contract.startDate
      ).toLocaleDateString()}`;
    }

    if (contract.endDate) {
      summary += `, extending until ${new Date(
        contract.endDate
      ).toLocaleDateString()}`;
    }

    summary += ". This summary was generated from available contract metadata.";

    return summary;
  }

  return (
    <Card className={`relative ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            Contract Summary
            {/* {(hasAINarrative ||
              hasAITabular ||
              generatedSummary ||
              generatedTabularSummary) && (
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                AI Generated
              </Badge>
            )} */}
          </div>

          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-muted/50 rounded-lg p-1 border">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleViewModeChange("paragraph")}
                className={`h-8 px-3 transition-all duration-200 ${
                  viewMode === "paragraph"
                    ? "bg-background text-foreground shadow-sm border hover:bg-background"
                    : "hover:bg-muted text-muted-foreground hover:text-foreground"
                }`}
                title="Paragraph View"
              >
                <FileText className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleViewModeChange("table")}
                className={`h-8 px-3 transition-all duration-200 ${
                  viewMode === "table"
                    ? "bg-background text-foreground shadow-sm border hover:bg-background"
                    : "hover:bg-muted text-muted-foreground hover:text-foreground"
                }`}
                title="Table View"
              >
                <Table className="h-4 w-4" />
              </Button>
            </div>

            {/* Generate AI Summary Button */}
            {!hasAINarrative && !generatedSummary && (
              <Button
                onClick={handleGenerateSummary}
                disabled={isGenerating}
                size="sm"
                variant="outline"
                className="flex items-center gap-2"
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Sparkles className="h-4 w-4" />
                )}
                {isGenerating ? "Generating..." : "Generate AI Summary"}
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {viewMode === "paragraph" ? (
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <div className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
              {narrativeSummary}
            </div>
          </div>
        ) : (
          <ContractSummaryTable tabularData={tabularSummary} showCard={false} />
        )}
      </CardContent>
    </Card>
  );
}
