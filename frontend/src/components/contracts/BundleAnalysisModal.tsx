/**
 * Bundle Analysis Modal
 * Displays bundle interconnection analysis for contract folders
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import {
  Loader2,
  Network,
  AlertTriangle,
  CheckCircle,
  Clock,
  GitBranch,
  Zap,
  Info,
  Minus,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  bundleAnalysisService,
  BundleAnalysisResponse,
  BundleAnalysisData,
  ContractGroup,
} from '@/services/bundleAnalysisService';
import { ComprehensiveClauseAnalysisTable } from './ComprehensiveClauseAnalysisTable';

interface BundleAnalysisModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  folderId: string;
  folderName: string;
  currentContractCount?: number; // Current number of contracts in the folder
}

export function BundleAnalysisModal({
  open,
  onOpenChange,
  folderId,
  folderName,
  currentContractCount = 0,
}: BundleAnalysisModalProps) {
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [analysisData, setAnalysisData] = useState<BundleAnalysisData | null>(null);
  const [analysisExists, setAnalysisExists] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [processingTime, setProcessingTime] = useState<number | null>(null);
  const [analysisContractCount, setAnalysisContractCount] = useState<number>(0);

  // Check if analysis is outdated (contract count mismatch)
  const isAnalysisOutdated = analysisExists && currentContractCount > 0 && analysisContractCount !== currentContractCount;

  // Fetch existing analysis when modal opens
  useEffect(() => {
    if (open && folderId) {
      fetchAnalysis();
    }
  }, [open, folderId]);



  const fetchAnalysis = async () => {
    setLoading(true);
    try {
      const response: BundleAnalysisResponse = await bundleAnalysisService.getBundleAnalysis(folderId);

      if (response?.exists && response?.analysis) {
        setAnalysisData(response.analysis);
        setAnalysisExists(true);
        setLastUpdated(response.updatedAt || response.extractionDate || null);
        setProcessingTime(response.processingTimeMs || null);
        setAnalysisContractCount(response.contractIds?.length || 0);
      } else {
        setAnalysisExists(false);
        setAnalysisData(null);
        setAnalysisContractCount(0);
      }
    } catch (error) {
      console.error('Error fetching bundle analysis:', error);
      toast.error('Failed to load bundle analysis');
      setAnalysisExists(false);
      setAnalysisData(null);
      setAnalysisContractCount(0);
    } finally {
      setLoading(false);
    }
  };

  const generateAnalysis = async () => {
    setGenerating(true);
    try {
      const response = await bundleAnalysisService.generateBundleAnalysis(folderId);

      if (response.success && response.analysis) {
        setAnalysisData(response.analysis);
        setAnalysisExists(true);
        setLastUpdated(response.extractionDate);
        setProcessingTime(response.processingTimeMs);
        setAnalysisContractCount(response.contractIds?.length || 0);
        toast.success('Bundle analysis generated successfully');
      }
    } catch (error) {
      console.error('Error generating bundle analysis:', error);
      toast.error('Failed to generate bundle analysis');
    } finally {
      setGenerating(false);
    }
  };

  const renderContractGroup = (group: ContractGroup) => (
    <Card key={group.group_id} className="mb-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <GitBranch className="h-5 w-5 text-primary" />
          Bundle {group.group_id}
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {group.documents.length} Documents
          </Badge>

          {/* Info button with hierarchy tooltip */}
          <div className="relative group">
            <Info className="h-4 w-4 text-gray-500 hover:text-gray-700 cursor-help" />

            {/* Tooltip on hover */}
            <div className="absolute left-0 top-6 z-50 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200">
              <div className="bg-gray-900 text-white text-xs rounded-lg p-3 shadow-lg min-w-[300px] max-w-[400px]">
                <div className="font-semibold mb-2">Bundle Documents:</div>
                <div className="space-y-1">
                  {group.documents.map((doc) => {
                    const isPrimary = doc.is_primary || doc.name === group.hierarchy?.primary_document;
                    return (
                      <div key={doc.name} className="flex items-center gap-2 text-xs">
                        <div className={`w-2 h-2 rounded-full ${isPrimary ? 'bg-blue-400' : 'bg-gray-400'
                          }`}></div>
                        <span className="truncate flex-1" title={doc.name}>
                          {doc.name}
                        </span>
                        <span className="text-gray-300">
                          ({doc.agreementType})
                        </span>
                      </div>
                    );
                  })}
                </div>

                {/* Arrow pointing to the info icon */}
                <div className="absolute -top-1 left-2 w-2 h-2 bg-gray-900 transform rotate-45"></div>
              </div>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Document Hierarchy */}
        {group.hierarchy && (
          <div>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Network className="h-4 w-4" />
              Document Hierarchy
            </h4>
            <div className="space-y-3">
              {/* Hierarchy Tree */}
              <div className="relative">
                {group.documents.map((doc, index) => {
                  const docName = doc.name;

                  return (
                    <div key={docName} className="flex items-center gap-3 mb-2">
                      {/* Hierarchy Level Indicator */}
                      <div className="flex items-center">
                        {index > 0 && (
                          <div className="flex items-center">
                            <div className="w-6 h-px bg-gray-300"></div>
                            <div className="w-2 h-2 bg-gray-300 rounded-full mx-1"></div>
                          </div>
                        )}
                        <div className={`w-3 h-3 rounded-full ${index === 0 ? 'bg-blue-500' : 'bg-gray-400'
                          }`}></div>
                      </div>

                      {/* Document Info */}
                      <div className="flex-1 p-3 border rounded-lg bg-white">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-sm truncate" title={docName}>
                              {docName}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {doc?.agreementType || 'Unknown'}
                              </Badge>
                              {doc?.is_primary && (
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                                  Primary
                                </Badge>
                              )}
                              {doc?.hierarchy_level && (
                                <Badge variant="outline" className="bg-gray-100 text-gray-800 text-xs">
                                  Level {doc.hierarchy_level}
                                </Badge>
                              )}
                            </div>
                          </div>

                          {/* Relationship Info */}
                          {doc?.relationship_type && (
                            <div className="ml-3 text-right">
                              <Badge
                                variant="outline"
                                className={doc.relationship_type === 'explicit'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                                }
                              >
                                {doc.relationship_type}
                              </Badge>

                            </div>
                          )}
                        </div>

                        {/* Linked By Information */}
                        {doc?.linked_by && doc.linked_by.length > 0 && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <div className="text-xs text-gray-600">
                              <strong>Linked by:</strong>
                              <ul className="mt-1 space-y-1">
                                {doc.linked_by.map((reason, idx) => (
                                  <li key={idx} className="flex items-start gap-1">
                                    <span className="text-gray-400">•</span>
                                    <span>{reason}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Summary */}
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-sm text-blue-800">
                  <strong>Bundle Summary:</strong> {group.documents.length} documents in bundle,
                  with <strong>{group.hierarchy?.primary_document || 'N/A'}</strong> as the primary governing document.
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Comprehensive Clause Analysis */}
        {group.comprehensive_clause_analysis && (
          <ComprehensiveClauseAnalysisTable
            documents={group.documents}
            clauseAnalysis={group.comprehensive_clause_analysis}
          />
        )}

        {/* Business Implications */}
        {group.business_implications && (
          <div>
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <Info className="h-4 w-4" />
              Business Implications
            </h4>
            <div className="space-y-3">
              {group.business_implications.critical_conflicts.length > 0 && (
                <div className="p-3 border border-red-200 rounded-lg bg-red-50">
                  <h5 className="font-medium text-red-800 mb-2">Critical Conflicts</h5>
                  <ul className="text-sm text-red-700 space-y-1">
                    {group.business_implications.critical_conflicts.map((conflict, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        {conflict}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {group.business_implications.missing_clauses.length > 0 && (
                <div className="p-3 border border-yellow-200 rounded-lg bg-yellow-50">
                  <h5 className="font-medium text-yellow-800 mb-2">Missing Clauses</h5>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {group.business_implications.missing_clauses.map((clause, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Minus className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        {clause}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {group.business_implications.recommendations.length > 0 && (
                <div className="p-3 border border-blue-200 rounded-lg bg-blue-50">
                  <h5 className="font-medium text-blue-800 mb-2">Recommendations</h5>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {group.business_implications.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        {recommendation}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Network className="h-5 w-5 text-primary" />
            Integrated Analysis - {folderName}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                <p className="text-muted-foreground">Loading bundle analysis...</p>
              </div>
            </div>
          ) : !analysisExists && !analysisData ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center space-y-4">
                <Network className="h-16 w-16 mx-auto text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-semibold mb-2">No Analysis Found</h3>
                  <p className="text-muted-foreground mb-4">
                    Generate AI-powered analysis to discover contract relationships, hierarchies, and clause conflicts.
                  </p>
                  <Button
                    onClick={generateAnalysis}
                    disabled={generating}
                    className="bg-primary hover:bg-primary/90"
                  >
                    {generating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating Analysis...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Fetch Data
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ) : analysisData ? (
            <div className="flex-1 flex flex-col min-h-0">
              {/* Analysis Metadata */}
              <div className="flex-shrink-0 mb-4 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Analysis Complete</span>
                    </div>
                    {lastUpdated && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        {new Date(lastUpdated).toLocaleString()}
                      </div>
                    )}
                    {processingTime && (
                      <div className="text-sm text-muted-foreground">
                        Processing: {bundleAnalysisService.formatProcessingTime(processingTime)}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Confidence:</span>
                    <span className={`text-sm font-medium ${bundleAnalysisService.getConfidenceColor(analysisData.overall_analysis?.confidence_score)}`}>
                      {bundleAnalysisService.formatConfidence(analysisData.overall_analysis?.confidence_score)}
                    </span>
                    <Button
                      variant={isAnalysisOutdated ? "destructive" : "outline"}
                      size="sm"
                      onClick={generateAnalysis}
                      disabled={generating}
                      className={isAnalysisOutdated ? "bg-red-600 hover:bg-red-700 text-white" : ""}
                    >
                      {generating ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4 mr-2" />
                      )}
                      Regenerate
                      {isAnalysisOutdated && (
                        <span className="ml-1 text-xs">({analysisContractCount} → {currentContractCount})</span>
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Analysis Content */}
              <div className="flex-1 overflow-y-auto">
                {analysisData.contract_groups.length > 0 || analysisData.standalone_documents.length > 0 ? (
                  <Tabs defaultValue={analysisData.contract_groups.length > 0 ? analysisData.contract_groups[0].group_id : 'standalone'} className="h-full">
                    <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${analysisData.contract_groups.length + (analysisData.standalone_documents.length > 0 ? 1 : 0)}, 1fr)` }}>
                      {/* Dynamic tabs for each contract group */}
                      {analysisData.contract_groups.map((group, index) => {
                        const primaryDoc = group.hierarchy?.primary_document;
                        const displayName = primaryDoc
                          ? primaryDoc.replace(/\.(pdf|docx?|txt)$/i, '')
                          : `Bundle ${index + 1}`;
                        const truncatedName = displayName.length > 15 ? displayName.substring(0, 15) + '...' : displayName;

                        return (
                          <TabsTrigger
                            key={group.group_id}
                            value={group.group_id}
                            className="text-xs sm:text-sm"
                            title={primaryDoc ? `${primaryDoc} (${group.documents.length} documents)` : `Bundle ${index + 1} (${group.documents.length} documents)`}
                          >
                            <div className="flex items-center gap-1.5">
                              <span className="truncate max-w-[120px]">
                                {truncatedName}
                              </span>
                              <Badge variant="secondary" className="text-xs px-1 py-0">
                                {group.documents.length}
                              </Badge>
                            </div>
                          </TabsTrigger>
                        );
                      })}

                      {/* Standalone documents tab if any exist */}
                      {analysisData.standalone_documents.length > 0 && (
                        <TabsTrigger value="standalone" className="text-xs sm:text-sm">
                          <div className="flex items-center gap-1.5">
                            <span>Standalone</span>
                            <Badge variant="secondary" className="text-xs px-1 py-0">
                              {analysisData.standalone_documents.length}
                            </Badge>
                          </div>
                        </TabsTrigger>
                      )}
                    </TabsList>

                    {/* Tab content for each contract group */}
                    {analysisData.contract_groups.map((group) => (
                      <TabsContent key={group.group_id} value={group.group_id} className="space-y-4 mt-4">
                        {renderContractGroup(group)}
                      </TabsContent>
                    ))}

                    {/* Standalone documents tab content */}
                    {analysisData.standalone_documents.length > 0 && (
                      <TabsContent value="standalone" className="space-y-4 mt-4">
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">Standalone Documents</h3>
                            <Badge variant="outline">
                              {analysisData.standalone_documents.length} {analysisData.standalone_documents.length === 1 ? 'document' : 'documents'}
                            </Badge>
                          </div>
                          <div className="space-y-2">
                            {analysisData.standalone_documents.map((doc, index) => (
                              <div key={index} className="p-3 border rounded-lg">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <span className="font-medium">{doc.name}</span>
                                    <Badge
                                      variant="outline"
                                      className={bundleAnalysisService.getDocumentTypeBadgeColor(doc.agreementType)}
                                    >
                                      {doc.agreementType}
                                    </Badge>
                                    {doc.is_orphan && (
                                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                        Orphan
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                    )}
                  </Tabs>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No contract analysis data found</p>
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default BundleAnalysisModal;
