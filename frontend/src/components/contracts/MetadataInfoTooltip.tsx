/**
 * MetadataInfoTooltip Component
 * Displays an info icon with tooltip showing attempted but empty fields
 */

"use client";

import React from "react";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import {
  getAttemptedButEmptyFields,
  getAllAttemptedButEmptyFields,
  MetadataFieldKey,
  ALL_METADATA_FIELDS,
} from "@/utils/metadata-display";

interface MetadataInfoTooltipProps {
  metadata: any;
  className?: string;
  size?: "sm" | "md" | "lg";
  position?: "header";
}

export function MetadataInfoTooltip({
  metadata,
  className = "",
  size = "sm",
  position = "header",
}: MetadataInfoTooltipProps) {
  const emptyFields = getAttemptedButEmptyFields(metadata);

  // Don't render if no metadata OR if there are no attempted but empty fields
  if (!metadata || emptyFields.length === 0) {
    return null;
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  const positionClasses = {
    header: "inline-flex items-center justify-center",
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              positionClasses[position],
              "cursor-help opacity-60 hover:opacity-100 transition-opacity",
              className
            )}
          >
            <Info className={cn("text-muted-foreground", iconSizes[size])} />
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-sm">
          <div className="space-y-2">
            <p className="font-medium text-sm">Missing/Not Found Fields</p>
            <p className="text-xs text-white">
              The AI attempted to extract the following fields but could not
              find them in the contract:
            </p>
            {emptyFields.length > 0 && (
              <div className="space-y-1">
                <div className="grid grid-cols-1 gap-1 text-xs">
                  {emptyFields.slice(0, 10).map((field) => (
                    <span key={field} className="text-muted-foreground">
                      • {ALL_METADATA_FIELDS[field]}
                    </span>
                  ))}
                  {emptyFields.length > 10 && (
                    <span className="text-muted-foreground italic">
                      ... and {emptyFields.length - 10} more fields
                    </span>
                  )}
                </div>
              </div>
            )}
            <p className="text-xs text-muted-foreground border-t pt-2">
              This provides transparency about what information the AI looked
              for but couldn't extract.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Compact version for use in card headers
 */
export function CompactMetadataInfoTooltip({
  metadata,
  className = "",
}: {
  metadata: any;
  className?: string;
}) {
  // Check if there are any attempted but empty fields before rendering
  const emptyFields = getAttemptedButEmptyFields(metadata);

  // Don't render if no attempted but empty fields
  if (!metadata || emptyFields.length === 0) {
    return null;
  }

  return (
    <MetadataInfoTooltip
      metadata={metadata}
      size="sm"
      position="header"
      className={cn("ml-auto", className)}
    />
  );
}

/**
 * Corner positioned tooltip for metadata panels
 * Displays in the bottom right corner of the parent container
 */
export function CornerMetadataInfoTooltip({
  metadata,
  className = "",
}: {
  metadata: any;
  className?: string;
}) {
  // Check if there are any attempted but empty fields before rendering
  const emptyFields = getAttemptedButEmptyFields(metadata);

  // Don't render if no attempted but empty fields
  if (!metadata || emptyFields.length === 0) {
    return null;
  }

  return (
    <MetadataInfoTooltip
      metadata={metadata}
      size="sm"
      position="header"
      className={className}
    />
  );
}

/**
 * Section-specific info tooltip that shows only fields relevant to a specific section
 */
export function SectionMetadataInfoTooltip({
  metadata,
  sectionFields,
  sectionName,
  className = "",
  position = "header",
}: {
  metadata: any;
  sectionFields: MetadataFieldKey[];
  sectionName: string;
  className?: string;
  position?: "header";
}) {
  const allEmptyFields = getAttemptedButEmptyFields(metadata);
  const sectionEmptyFields = allEmptyFields.filter((field) =>
    sectionFields.includes(field)
  );

  // Only render if there are attempted but empty fields in this section
  if (!metadata || sectionEmptyFields.length === 0) {
    return null;
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  const positionClasses = {
    header: "inline-flex items-center justify-center",
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              positionClasses[position],
              "cursor-help opacity-60 hover:opacity-100 transition-opacity",
              className
            )}
          >
            <Info className={cn("text-muted-foreground", iconSizes.sm)} />
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-sm">
          <div className="space-y-2">
            <p className="font-medium text-sm">Missing {sectionName} Fields</p>
            <p className="text-xs text-white">
              The AI attempted to extract the following{" "}
              {sectionName.toLowerCase()} fields but could not find them:
            </p>
            {sectionEmptyFields.length > 0 && (
              <div className="space-y-1">
                <div className="grid grid-cols-1 gap-1 text-xs">
                  {sectionEmptyFields.map((field) => (
                    <span key={field} className="text-white">
                      • {ALL_METADATA_FIELDS[field]}
                    </span>
                  ))}
                </div>
              </div>
            )}
            <p className="text-xs text-white border-t pt-2">
              {sectionEmptyFields.length} field(s) analyzed but no data found in
              this section.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Global metadata info tooltip that shows all attempted but empty fields
 * Positioned in the bottom right corner of the metadata panel
 */
export function GlobalMetadataInfoTooltip({
  metadata,
  className = "",
}: {
  metadata: any;
  className?: string;
}) {
  const {
    fields: emptyFields,
    totalAttempted,
    totalSuccessful,
    totalPossible,
  } = getAllAttemptedButEmptyFields(metadata);

  // Don't render if no metadata
  if (!metadata) {
    return null;
  }

  // Always show the tooltip if there's metadata, even if no empty fields
  // This provides transparency about the AI analysis process

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "fixed bottom-4 right-4 z-20 cursor-help opacity-60 hover:opacity-100 transition-opacity",
              "bg-background/90 backdrop-blur-sm rounded-full p-2 shadow-lg border border-border",
              "hover:shadow-xl hover:scale-105 transition-all duration-200",
              className
            )}
          >
            <Info className="h-4 w-4 text-muted-foreground" />
          </div>
        </TooltipTrigger>
        <TooltipContent side="left" className="max-w-md">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <p className="font-medium text-sm">AI Extraction Summary</p>
              <div className="text-xs text-muted-foreground">
                {totalSuccessful}/{totalAttempted} fields extracted
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs">
                  {totalSuccessful} fields successfully extracted
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-xs">
                  {emptyFields.length} fields attempted but not found
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-xs">
                  {totalPossible - totalAttempted} fields not attempted
                </span>
              </div>
            </div>

            {/* Success Rate Progress Bar */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Success Rate</span>
                <span>
                  {totalAttempted > 0
                    ? Math.round((totalSuccessful / totalAttempted) * 100)
                    : 0}
                  %
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width:
                      totalAttempted > 0
                        ? `${(totalSuccessful / totalAttempted) * 100}%`
                        : "0%",
                  }}
                ></div>
              </div>
            </div>

            {emptyFields.length > 0 && (
              <div className="space-y-2 border-t pt-2">
                <p className="text-xs font-medium text-muted-foreground">
                  Fields attempted but not found:
                </p>
                <div className="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
                  {emptyFields.slice(0, 15).map((field) => (
                    <span key={field} className="text-xs text-muted-foreground">
                      • {ALL_METADATA_FIELDS[field]}
                    </span>
                  ))}
                  {emptyFields.length > 15 && (
                    <span className="text-xs text-muted-foreground italic">
                      ... and {emptyFields.length - 15} more fields
                    </span>
                  )}
                </div>
              </div>
            )}

            <p className="text-xs text-muted-foreground border-t pt-2">
              This provides transparency about the AI's analysis process. The AI
              attempted to extract {totalAttempted} out of {totalPossible}{" "}
              possible fields.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
