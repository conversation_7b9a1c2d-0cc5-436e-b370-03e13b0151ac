/**
 * Entitlement Analysis Table Component
 * Enhanced DataTable with inline editing and confidence indicators
 */

"use client";

import React from "react";
import { DataTable, DataTableColumn } from "@/components/ui/data-table";
import { InlineEditableCell } from "./InlineEditableCell";
import {
  TableConfidenceIndicator,
  getFieldConfidence,
  isContractLevelField,
} from "./TableConfidenceIndicator";
import { AnalysisData } from "@/services/entitlementAnalysisService";
import { cn } from "@/lib/utils";

interface EntitlementAnalysisTableProps {
  data: AnalysisData[];
  fields: string[];
  loading?: boolean;
  onFieldUpdate: (
    contractId: string,
    fieldKey: string,
    newValue: string,
    isContractLevel: boolean,
    purchasingItemIndex?: number
  ) => Promise<void>;
  className?: string;
}

/**
 * Creates table columns with inline editing and confidence indicators
 */
function createAnalysisColumns(
  fields: string[],
  onFieldUpdate: EntitlementAnalysisTableProps["onFieldUpdate"]
): DataTableColumn<AnalysisData>[] {
  const columns: DataTableColumn<AnalysisData>[] = [];

  // Add basic info columns (non-editable)
  columns.push({
    key: "contractName",
    header: "Contract Name",
    className: "min-w-[200px]",
    render: (value, row) => (
      <div className="flex flex-col gap-1">
        <span className="font-medium truncate" title={value}>
          {value}
        </span>
        <span
          className="text-xs text-muted-foreground truncate"
          title={row.fileName}
        >
          {row.fileName}
        </span>
      </div>
    ),
  });

  // Add editable analysis field columns
  fields.forEach((field) => {
    // Skip metadata fields
    if (
      field.startsWith("_") ||
      field === "contractId" ||
      field === "contractName" ||
      field === "fileName" ||
      field === "purchasingMetadata"
    ) {
      return;
    }

    columns.push({
      key: field,
      header: field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
      className: "min-w-[220px]",
      render: (value, row) => {
        const confidence = getFieldConfidence(row, field);
        const isContractLevel = isContractLevelField(row, field);
        const contractId = row.contractId;
        const purchasingItemIndex = row._purchasingItemIndex;

        // Ensure value is a string for display
        let displayValue = "N/A";
        if (value != null) {
          if (typeof value === "object") {
            // If it's an object with a 'value' property, extract that
            const objValue = value as any;
            if (objValue.value !== undefined) {
              displayValue =
                objValue.value != null ? String(objValue.value) : "N/A";
            } else {
              // Show JSON representation for debugging
              try {
                displayValue = JSON.stringify(value);
              } catch {
                displayValue = "[Complex Object]";
              }
            }
          } else {
            displayValue = String(value);
          }
        }

        return (
          <div className="flex items-center gap-2 w-full">
            <div className="flex-1 min-w-0">
              <InlineEditableCell
                value={displayValue}
                fieldKey={field}
                isContractLevel={isContractLevel}
                purchasingItemIndex={purchasingItemIndex}
                onUpdate={(
                  fieldKey,
                  newValue,
                  isContractLevel,
                  purchasingItemIndex
                ) =>
                  onFieldUpdate(
                    contractId,
                    fieldKey,
                    newValue,
                    isContractLevel,
                    purchasingItemIndex
                  )
                }
                className="w-full"
              />
            </div>
            <div className="flex-shrink-0">
              <TableConfidenceIndicator
                confidence={confidence}
                field={field}
                size="sm"
              />
            </div>
          </div>
        );
      },
    });
  });

  return columns;
}

/**
 * Entitlement Analysis Table Component
 */
export function EntitlementAnalysisTable({
  data,
  fields,
  loading = false,
  onFieldUpdate,
  className,
}: EntitlementAnalysisTableProps) {
  const columns = React.useMemo(
    () => createAnalysisColumns(fields, onFieldUpdate),
    [fields, onFieldUpdate]
  );

  // Calculate minimum table width based on columns
  const baseWidth = 200; // Contract name column
  const fieldWidth = 240; // Each field column (220px + padding + confidence indicator)
  const visibleFields = fields.filter(
    (field) =>
      !field.startsWith("_") &&
      field !== "contractId" &&
      field !== "contractName" &&
      field !== "fileName" &&
      field !== "purchasingMetadata"
  );
  const dynamicWidth = baseWidth + visibleFields.length * fieldWidth;
  const minTableWidth = `${dynamicWidth}px`;

  return (
    <div className={cn("w-full", className)}>
      <div className="border rounded-lg overflow-hidden">
        <div
          className="overflow-x-auto overflow-y-auto max-h-[500px]"
          style={{
            scrollbarGutter: "stable",
          }}
        >
          <div style={{ minWidth: minTableWidth, width: "max-content" }}>
            <DataTable
              data={data}
              columns={columns}
              loading={loading}
              emptyMessage="No analysis data available for the selected contracts"
              containerClassName="border-0"
              stickyHeader={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Helper component for field statistics
 */
export function AnalysisTableStats({
  data,
  fields,
  className,
}: {
  data: AnalysisData[];
  fields: string[];
  className?: string;
}) {
  const contractCount = new Set(data.map((row) => row.contractId)).size;
  const totalRows = data.length;
  const multiItemContracts = data.filter((row) => row._isMultiItem).length;

  // Calculate confidence statistics
  const confidenceStats = React.useMemo(() => {
    const allConfidences: number[] = [];

    data.forEach((row) => {
      fields.forEach((field) => {
        if (
          !field.startsWith("_") &&
          field !== "contractId" &&
          field !== "contractName" &&
          field !== "fileName" &&
          field !== "purchasingMetadata"
        ) {
          const confidence = getFieldConfidence(row, field);
          if (confidence !== -1) {
            // Exclude manually edited fields
            allConfidences.push(confidence);
          }
        }
      });
    });

    const avgConfidence =
      allConfidences.length > 0
        ? allConfidences.reduce((sum, conf) => sum + conf, 0) /
          allConfidences.length
        : 0;

    const highConfidence = allConfidences.filter((conf) => conf >= 0.8).length;
    const mediumConfidence = allConfidences.filter(
      (conf) => conf >= 0.5 && conf < 0.8
    ).length;
    const lowConfidence = allConfidences.filter((conf) => conf < 0.5).length;

    return {
      average: avgConfidence,
      high: highConfidence,
      medium: mediumConfidence,
      low: lowConfidence,
      total: allConfidences.length,
    };
  }, [data, fields]);

  return (
    <div
      className={cn(
        "flex items-center gap-4 text-sm text-muted-foreground",
        className
      )}
    >
      <span>{contractCount} contracts</span>
      <span>{totalRows} rows</span>
      {multiItemContracts > 0 && (
        <span>{multiItemContracts} multi-item rows</span>
      )}
      <span>{fields.length} fields</span>
      <span>Avg confidence: {(confidenceStats.average * 100).toFixed(0)}%</span>
      <div className="flex items-center gap-2">
        <span className="text-green-600">{confidenceStats.high} high</span>
        <span className="text-yellow-600">{confidenceStats.medium} medium</span>
        <span className="text-red-600">{confidenceStats.low} low</span>
      </div>
    </div>
  );
}
