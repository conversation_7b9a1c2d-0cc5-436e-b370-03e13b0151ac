/**
 * Inline Editable Cell Component
 * Allows inline editing of analysis fields in entitlement analysis table
 */

"use client";

import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Edit, Check, X, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface InlineEditableCellProps {
  value: string;
  fieldKey: string;
  isContractLevel: boolean;
  purchasingItemIndex?: number;
  onUpdate: (
    fieldKey: string,
    newValue: string,
    isContractLevel: boolean,
    purchasingItemIndex?: number
  ) => Promise<void>;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

/**
 * Inline Editable Cell Component
 */
export function InlineEditableCell({
  value,
  fieldKey,
  isContractLevel,
  purchasingItemIndex,
  onUpdate,
  className = "",
  disabled = false,
  placeholder = "Enter value...",
  maxLength = 500,
}: InlineEditableCellProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value || "");
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update edit value when prop value changes
  useEffect(() => {
    setEditValue(value || "");
  }, [value]);

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    if (disabled) return;
    setIsEditing(true);
    setEditValue(value || "");
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditValue(value || "");
  };

  const handleSaveEdit = async () => {
    if (editValue.trim() === (value || "").trim()) {
      setIsEditing(false);
      return;
    }

    // Validate the field value
    const validationError = validateFieldValue(fieldKey, editValue.trim());
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setIsLoading(true);
    try {
      await onUpdate(
        fieldKey,
        editValue.trim(),
        isContractLevel,
        purchasingItemIndex
      );
      setIsEditing(false);
      toast.success(`${fieldDisplayName} updated successfully`);
    } catch (error) {
      console.error("Error updating field:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update field";
      toast.error(`Failed to update ${fieldDisplayName}: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  const displayValue = value || "N/A";
  const fieldDisplayName = fieldKey
    .replace(/_/g, " ")
    .replace(/\b\w/g, (l) => l.toUpperCase());

  if (isEditing) {
    return (
      <div className="flex items-center gap-1 w-full min-w-[200px]">
        <Input
          ref={inputRef}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          maxLength={maxLength}
          className="h-8 text-sm flex-1 min-w-[120px]"
          disabled={isLoading}
        />
        <div className="flex items-center gap-1 flex-shrink-0">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleSaveEdit}
            disabled={isLoading}
            className="h-8 w-8 p-0 hover:bg-green-100 hover:text-green-700 dark:hover:bg-green-900 dark:hover:text-green-300"
          >
            {isLoading ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleCancelEdit}
            disabled={isLoading}
            className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900 dark:hover:text-red-300"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("group flex items-center gap-2 min-w-0", className)}>
      <span
        className={cn(
          "flex-1 min-w-0 truncate",
          displayValue === "N/A" && "text-muted-foreground italic"
        )}
        title={displayValue}
      >
        {displayValue}
      </span>
      {!disabled && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleStartEdit}
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted flex-shrink-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="bg-primary text-primary-foreground"
            >
              <div className="text-center">
                <div className="font-medium">Edit {fieldDisplayName}</div>
                <div className="text-xs opacity-75">
                  {isContractLevel
                    ? "Contract-level field"
                    : "Item-level field"}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

/**
 * Validation helper functions
 */
export const validateFieldValue = (
  fieldKey: string,
  value: string
): string | null => {
  if (!value.trim()) {
    return "Field value cannot be empty";
  }

  const trimmedValue = value.trim();
  const lowerFieldKey = fieldKey.toLowerCase();

  // Skip validation for N/A values
  if (trimmedValue.toLowerCase() === "n/a") {
    return null;
  }

  // Currency validation
  if (lowerFieldKey.includes("value") || lowerFieldKey.includes("price")) {
    const currencyPattern = /^[A-Z]{3}:\d+(\.\d{2})?$/;
    if (trimmedValue.includes(":")) {
      if (!currencyPattern.test(trimmedValue)) {
        return "Currency format should be 'CURRENCY:AMOUNT' (e.g., USD:50000)";
      }
      // Validate currency code
      const [currency, amount] = trimmedValue.split(":");
      const validCurrencies = [
        "USD",
        "EUR",
        "GBP",
        "JPY",
        "CAD",
        "AUD",
        "CHF",
        "CNY",
        "INR",
      ];
      if (!validCurrencies.includes(currency)) {
        return `Invalid currency code. Supported: ${validCurrencies.join(
          ", "
        )}`;
      }
      // Validate amount
      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount < 0) {
        return "Amount must be a positive number";
      }
    }
  }

  // Date validation
  if (lowerFieldKey.includes("date")) {
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(trimmedValue)) {
      return "Date format should be YYYY-MM-DD";
    }
    // Validate actual date
    const date = new Date(trimmedValue);
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }
    // Check reasonable date range (1900-2100)
    const year = date.getFullYear();
    if (year < 1900 || year > 2100) {
      return "Date must be between 1900 and 2100";
    }
  }

  // Quantity validation
  if (lowerFieldKey.includes("quantity")) {
    const num = parseInt(trimmedValue);
    if (isNaN(num) || num < 0) {
      return "Quantity must be a positive number";
    }
    if (num > 1000000) {
      return "Quantity seems too large (max: 1,000,000)";
    }
  }

  // Email validation
  if (lowerFieldKey.includes("email")) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(trimmedValue)) {
      return "Invalid email format";
    }
  }

  // Relationships field validation
  if (fieldKey === "relationships") {
    // Allow comma-separated document references with flexible format
    // Allow letters, numbers, spaces, common punctuation for document names
    const relationshipPattern = /^[a-zA-Z0-9\-_.,\s()\/]+$/;
    if (!relationshipPattern.test(trimmedValue)) {
      return "Document references should contain only letters, numbers, spaces, and common punctuation";
    }
    // Check each relationship reference
    const relationships = trimmedValue
      .split(",")
      .map((rel) => rel.trim())
      .filter((rel) => rel);
    for (const rel of relationships) {
      if (rel.length < 1) {
        return "Each document reference should not be empty";
      }
      if (rel.length > 100) {
        return "Each document reference should be at most 100 characters long";
      }
    }
  }

  // General length validation
  if (trimmedValue.length > 500) {
    return "Value is too long (max: 500 characters)";
  }

  // Check for potentially dangerous content
  const dangerousPatterns = [/<script/i, /javascript:/i, /on\w+=/i];
  if (dangerousPatterns.some((pattern) => pattern.test(trimmedValue))) {
    return "Invalid characters detected";
  }

  return null;
};

/**
 * Format display value based on field type
 */
export const formatDisplayValue = (fieldKey: string, value: string): string => {
  if (!value || value === "N/A") return value;

  // Format currency values
  if (fieldKey.includes("value") || fieldKey.includes("price")) {
    if (value.includes(":")) {
      const [currency, amount] = value.split(":");
      const numAmount = parseFloat(amount);
      if (!isNaN(numAmount)) {
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: currency || "USD",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(numAmount);
      }
    }
  }

  // Format quantities
  if (fieldKey.includes("quantity")) {
    const num = parseInt(value);
    if (!isNaN(num)) {
      return num.toLocaleString();
    }
  }

  return value;
};
