"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ContractStatus } from "@/services/contractService";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ContractStatusBadge } from "./ContractStatusBadge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, CheckCircle, AlertCircle, ArrowRight } from "lucide-react";

interface ImportedContract {
  id: string;
  title: string;
  status: ContractStatus;
  metadata?: any;
}

interface ImportedContractsViewProps {
  contracts: ImportedContract[];
  onClose: () => void;
}

export function ImportedContractsView({
  contracts,
  onClose,
}: ImportedContractsViewProps) {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState("all");

  const viewContract = (id: string) => {
    router.push(`/contracts/${id}`);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Imported Contracts</CardTitle>
        <CardDescription>
          {contracts.length} contracts were successfully imported
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all" onClick={() => setSelectedTab("all")}>
              All ({contracts.length})
            </TabsTrigger>
            <TabsTrigger
              value="with-metadata"
              onClick={() => setSelectedTab("with-metadata")}
            >
              With AI Metadata ({contracts.filter((c) => c.metadata).length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {contracts.map((contract) => (
                <div
                  key={contract.id}
                  className="border rounded-md p-4 hover:bg-muted/50 cursor-pointer"
                  onClick={() => viewContract(contract.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <h3 className="font-medium">{contract.title}</h3>
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="text-sm text-muted-foreground flex items-center gap-2">
                          Status:
                          <ContractStatusBadge
                            status={
                              (contract as any).calculatedStatus || "Unknown"
                            }
                            variant="compact"
                          />
                        </span>
                        {contract.metadata && (
                          <Badge variant="outline" className="ml-2">
                            AI Analyzed
                          </Badge>
                        )}
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="with-metadata" className="space-y-4">
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {contracts
                .filter((contract) => contract.metadata)
                .map((contract) => (
                  <div
                    key={contract.id}
                    className="border rounded-md p-4 hover:bg-muted/50 cursor-pointer"
                    onClick={() => viewContract(contract.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <h3 className="font-medium">{contract.title}</h3>
                        </div>
                        <div className="mt-1">
                          <span className="text-sm text-muted-foreground flex items-center gap-2">
                            Status:
                            <ContractStatusBadge
                              status={
                                (contract as any).calculatedStatus || "Unknown"
                              }
                              variant="compact"
                            />
                          </span>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1">
                          {contract.metadata?.contractType && (
                            <div className="text-xs">
                              <span className="font-medium">Type:</span>{" "}
                              {contract.metadata.contractType}
                            </div>
                          )}
                          {contract.metadata?.dates?.startDate && (
                            <div className="text-xs">
                              <span className="font-medium">Start Date:</span>{" "}
                              {new Date(
                                contract.metadata.dates.startDate
                              ).toLocaleDateString()}
                            </div>
                          )}
                          {contract.metadata?.dates?.endDate && (
                            <div className="text-xs">
                              <span className="font-medium">End Date:</span>{" "}
                              {new Date(
                                contract.metadata.dates.endDate
                              ).toLocaleDateString()}
                            </div>
                          )}
                          {contract.metadata?.value?.amount && (
                            <div className="text-xs">
                              <span className="font-medium">Value:</span>{" "}
                              {contract.metadata.value.amount}{" "}
                              {contract.metadata.value.currency || ""}
                            </div>
                          )}
                          {contract.metadata?.parties &&
                            contract.metadata.parties.length > 0 && (
                              <div className="text-xs">
                                <span className="font-medium">Parties:</span>{" "}
                                {contract.metadata.parties
                                  .map((p: { name: string }) => p.name)
                                  .join(", ")}
                              </div>
                            )}
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={onClose}>Close</Button>
      </CardFooter>
    </Card>
  );
}
