/**
 * Contract Summary Narrative Component
 * Generates a comprehensive, human-readable summary of contract details
 */

import React, { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Spa<PERSON><PERSON>, Loader2 } from "lucide-react";
import { formatCurrency } from "@/lib/format-utils";
import { format, formatDistanceToNow } from "date-fns";
import { contractService } from "@/services/contractService";
import { toast } from "sonner";

interface ContractSummaryNarrativeProps {
  contract: any;
  className?: string;
  onSummaryGenerated?: (summary: string) => void;
}

// Helper function to check if a value is meaningful
const isValueMeaningful = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === "string") {
    const trimmed = value.trim().toLowerCase();
    return (
      trimmed !== "" &&
      trimmed !== "n/a" &&
      trimmed !== "null" &&
      trimmed !== "undefined"
    );
  }
  if (typeof value === "number") return !isNaN(value) && value !== 0;
  if (Array.isArray(value)) return value.length > 0;
  if (typeof value === "object") return Object.keys(value).length > 0;
  return Boolean(value);
};

// Helper function to get metadata value
const getMetadataValue = (metadata: any, field: string): any => {
  if (!metadata?.autoExtractedFields) return null;
  return metadata.autoExtractedFields[field];
};

// Helper function to format agreement type
const formatAgreementType = (type: string): string => {
  if (!type) return "";
  return type.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

// Helper function to format dates safely
const formatDateSafely = (dateValue: any): string => {
  if (!dateValue) return "";
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) return "";
    return format(date, "MMM dd, yyyy");
  } catch {
    return "";
  }
};

// Helper function to generate contract value text
const getContractValueText = (contract: any): string => {
  const metadata = contract.metadata?.autoExtractedFields;

  if (contract.value && isValueMeaningful(contract.value)) {
    return formatCurrency(contract.value);
  }
  if (metadata?.value?.amount && isValueMeaningful(metadata.value.amount)) {
    return formatCurrency(
      metadata.value.amount,
      metadata.value.currency || metadata?.currency || "USD"
    );
  }
  if (
    contract.metadata?.totalValue &&
    isValueMeaningful(contract.metadata.totalValue)
  ) {
    return formatCurrency(
      contract.metadata.totalValue,
      metadata?.currency || "USD"
    );
  }
  if (metadata?.contractValue && isValueMeaningful(metadata.contractValue)) {
    return formatCurrency(metadata.contractValue, metadata?.currency || "USD");
  }
  return "";
};

// Helper function to get parties information
const getPartiesText = (
  contract: any
): { provider: string; client: string } => {
  const provider =
    getMetadataValue(contract.metadata, "provider") ||
    contract.counterparty ||
    "";

  const client = getMetadataValue(contract.metadata, "client") || "";

  return { provider, client };
};

// Helper function to get key dates
const getKeyDates = (contract: any) => {
  const metadata = contract.metadata?.autoExtractedFields;

  return {
    effective:
      formatDateSafely(
        metadata?.dates?.effectiveDate || metadata?.effectiveDate || ""
      ) || "",
    end:
      formatDateSafely(
        metadata?.dates?.endDate || metadata?.endDate || contract.endDate || ""
      ) || "",
    execution:
      formatDateSafely(
        metadata?.dates?.executionDate || metadata?.executionDate || ""
      ) || "",
    renewal: formatDateSafely(metadata?.renewalDate || "") || "",
  };
};

// Main summary generation function
const generateContractSummary = (contract: any): string => {
  // Check if we have a pre-generated summary stored in the database
  if (
    contract.metadata?.aiGeneratedSummary &&
    isValueMeaningful(contract.metadata.aiGeneratedSummary)
  ) {
    return contract.metadata.aiGeneratedSummary;
  }

  const metadata = contract.metadata?.autoExtractedFields;
  const agreementType = formatAgreementType(
    getMetadataValue(contract.metadata, "agreement_type") ||
      contract.agreementType ||
      ""
  );
  const contractValue = getContractValueText(contract);
  const { provider, client } = getPartiesText(contract);
  const dates = getKeyDates(contract);
  const paymentTerms =
    getMetadataValue(contract.metadata, "payment_terms") || "";
  const keyTerms =
    metadata?.keyTerms?.filter((term: string) => isValueMeaningful(term)) || [];
  const sla = getMetadataValue(contract.metadata, "sla");
  const renewalTerms = getMetadataValue(contract.metadata, "renewal_terms");
  const liability = getMetadataValue(contract.metadata, "liability");
  const termination = getMetadataValue(contract.metadata, "termination");
  const description =
    contract.description && isValueMeaningful(contract.description)
      ? contract.description
      : "";
  const deliverables = getMetadataValue(contract.metadata, "deliverables");
  const performanceMetrics = getMetadataValue(
    contract.metadata,
    "performance_metrics"
  );
  const compliance = getMetadataValue(contract.metadata, "compliance");
  const indemnification = getMetadataValue(
    contract.metadata,
    "indemnification"
  );
  const confidentiality = getMetadataValue(
    contract.metadata,
    "confidentiality"
  );
  const intellectualProperty = getMetadataValue(
    contract.metadata,
    "intellectual_property"
  );
  const disputeResolution = getMetadataValue(
    contract.metadata,
    "dispute_resolution"
  );
  const governingLaw = getMetadataValue(contract.metadata, "governing_law");
  const amendments = getMetadataValue(contract.metadata, "amendments");
  const attachments = getMetadataValue(contract.metadata, "attachments");

  let summary = "";

  // Paragraph 1: Comprehensive Contract Overview and Parties
  summary += `This comprehensive ${
    agreementType ? agreementType.toLowerCase() : "contractual agreement"
  } titled "${contract.title}" `;

  if (description) {
    summary += `${
      description.toLowerCase().endsWith(".") ? description : description + "."
    } `;
  }

  // Detailed party information
  if (provider && client) {
    summary += `The agreement establishes a formal business relationship between ${client} serving as the client organization and ${provider} acting as the service provider. `;
  } else if (provider) {
    summary += `The contract engages ${provider} as the primary service provider, responsible for delivering the specified services and meeting all contractual obligations. `;
  } else if (client) {
    summary += `The agreement involves ${client} as the client organization, with responsibilities for payment and cooperation as outlined in the contract terms. `;
  } else if (contract.counterparty) {
    summary += `The contract establishes a business relationship with ${contract.counterparty} as the primary counterparty. `;
  }

  // Contract status and value
  summary += `The agreement is currently ${
    contract.status?.toLowerCase() || "active"
  }`;
  if (contractValue) {
    summary += ` with a total contract value of ${contractValue}`;
  }

  // Effective dates and timeline
  if (dates.effective || dates.execution) {
    const startDate = dates.effective || dates.execution;
    summary += ` and became effective on ${startDate}`;
  }
  if (dates.end) {
    summary += `, with the contract term extending until ${dates.end}`;
  }
  summary += ". ";

  // Additional context about contract scope
  if (agreementType && agreementType.toLowerCase().includes("master")) {
    summary += `As a master agreement, this contract establishes the overarching terms and conditions that will govern future work orders, statements of work, or subsidiary agreements between the parties. `;
  }

  // Paragraph 2: Comprehensive Financial Terms, Timeline, and Scope of Services
  summary += "\n\n";

  // Financial details section
  if (contractValue || paymentTerms) {
    if (contractValue && paymentTerms) {
      summary += `The financial framework of this agreement establishes a comprehensive contract value of ${contractValue} with specific payment terms defined as ${paymentTerms}. `;
    } else if (contractValue) {
      summary += `The contract specifies a total financial commitment of ${contractValue}. `;
    } else if (paymentTerms) {
      summary += `The agreement outlines detailed payment terms defined as ${paymentTerms}. `;
    }
  }

  // Timeline and duration details
  if (dates.execution || dates.effective || dates.end || dates.renewal) {
    summary += `The contractual timeline `;

    if (dates.execution) {
      summary += `begins with execution on ${dates.execution}`;
      if (dates.effective && dates.effective !== dates.execution) {
        summary += ` and becomes effective on ${dates.effective}`;
      }
      summary += `, `;
    } else if (dates.effective) {
      summary += `commences with an effective date of ${dates.effective}, `;
    }

    if (dates.end) {
      summary += `extending through ${dates.end}`;
      if (dates.renewal) {
        summary += ` with renewal considerations on ${dates.renewal}`;
      }
      summary += `. `;
    } else if (dates.renewal) {
      summary += `and includes renewal provisions scheduled for ${dates.renewal}. `;
    } else {
      summary += `with no specified end date. `;
    }
  }

  // Scope of services and deliverables
  if (deliverables || performanceMetrics) {
    if (deliverables && typeof deliverables === "object") {
      const deliverablesList = Object.values(deliverables).filter((d) =>
        isValueMeaningful(d)
      );
      if (deliverablesList.length > 0) {
        summary += `The scope of work encompasses specific deliverables including ${deliverablesList.join(
          ", "
        )}. `;
      }
    } else if (deliverables && typeof deliverables === "string") {
      summary += `The contract specifies deliverables as follows: ${deliverables}. `;
    }

    if (performanceMetrics && typeof performanceMetrics === "object") {
      const metricsList = Object.values(performanceMetrics).filter((m) =>
        isValueMeaningful(m)
      );
      if (metricsList.length > 0) {
        summary += `Performance will be measured against defined metrics including ${metricsList.join(
          ", "
        )}. `;
      }
    } else if (performanceMetrics && typeof performanceMetrics === "string") {
      summary += `Performance metrics are defined as: ${performanceMetrics}. `;
    }
  }

  // Paragraph 3: Comprehensive Key Terms, Service Level Agreements, and Contractual Obligations
  summary += "\n\n";

  // Key contractual terms and conditions
  if (keyTerms.length > 0) {
    summary += `The agreement incorporates comprehensive contractual terms and conditions, with key provisions including ${keyTerms.join(
      ", "
    )}. `;
  }

  // Service Level Agreements and performance requirements
  if (sla && typeof sla === "object") {
    summary += `Service level commitments are rigorously defined within the contract, establishing `;
    const slaDetails = [];

    if (sla.availability) {
      slaDetails.push(
        `system availability requirements of ${sla.availability}`
      );
    }
    if (sla.response_time_p1) {
      slaDetails.push(
        `Priority 1 incident response times of ${sla.response_time_p1}`
      );
    }
    if (sla.response_time_p2) {
      slaDetails.push(
        `Priority 2 incident response times of ${sla.response_time_p2}`
      );
    }
    if (sla.response_time_p3) {
      slaDetails.push(
        `Priority 3 incident response times of ${sla.response_time_p3}`
      );
    }
    if (sla.uptime_guarantee) {
      slaDetails.push(`uptime guarantees of ${sla.uptime_guarantee}`);
    }
    if (sla.resolution_time) {
      slaDetails.push(`issue resolution timeframes of ${sla.resolution_time}`);
    }
    if (sla.performance_standards) {
      slaDetails.push(
        `performance standards defined as ${sla.performance_standards}`
      );
    }

    if (slaDetails.length > 0) {
      summary += `${slaDetails.join(", ")}. `;
    }

    if (sla.penalties || sla.credits) {
      summary += `The agreement includes provisions for service level penalties or credits in the event of non-compliance with these standards. `;
    }
  }

  // Confidentiality and intellectual property provisions
  if (confidentiality || intellectualProperty) {
    if (confidentiality && typeof confidentiality === "object") {
      summary += `Confidentiality obligations are established `;
      if (confidentiality.duration) {
        summary += `with a confidentiality period of ${confidentiality.duration} `;
      }
      if (confidentiality.scope) {
        summary += `covering ${confidentiality.scope} `;
      }
      summary += `to protect sensitive information exchanged between the parties. `;
    } else if (confidentiality && typeof confidentiality === "string") {
      summary += `Confidentiality requirements are defined as: ${confidentiality}. `;
    }

    if (intellectualProperty && typeof intellectualProperty === "object") {
      summary += `Intellectual property rights are addressed `;
      if (intellectualProperty.ownership) {
        summary += `with ownership provisions stating ${intellectualProperty.ownership} `;
      }
      if (intellectualProperty.licensing) {
        summary += `and licensing terms of ${intellectualProperty.licensing} `;
      }
      summary += `to ensure proper protection and usage of intellectual assets. `;
    } else if (
      intellectualProperty &&
      typeof intellectualProperty === "string"
    ) {
      summary += `Intellectual property provisions specify: ${intellectualProperty}. `;
    }
  }

  // Compliance and regulatory requirements
  if (compliance) {
    if (typeof compliance === "object") {
      const complianceItems = Object.values(compliance).filter((c) =>
        isValueMeaningful(c)
      );
      if (complianceItems.length > 0) {
        summary += `The contract mandates compliance with specific regulatory and industry standards, including ${complianceItems.join(
          ", "
        )}, ensuring adherence to applicable laws and regulations. `;
      }
    } else if (typeof compliance === "string") {
      summary += `Compliance requirements are specified as: ${compliance}. `;
    }
  }

  // Paragraph 4: Risk Management, Liability, Termination, and Governance Provisions
  summary += "\n\n";

  // Comprehensive liability and risk management
  if (liability && typeof liability === "object") {
    summary += `Risk management and liability provisions are comprehensively addressed within the agreement. `;

    if (liability.capping) {
      summary += `Liability limitations are established with ${liability.capping}, providing financial protection for both parties. `;
    }
    if (liability.exclusions && Array.isArray(liability.exclusions)) {
      summary += `Specific liability exclusions are defined, including ${liability.exclusions.join(
        ", "
      )}, to clarify the scope of responsibility. `;
    } else if (liability.exclusions) {
      summary += `Liability exclusions are specified as: ${liability.exclusions}. `;
    }
    if (liability.mutual_indemnification) {
      summary += `The contract includes mutual indemnification provisions to protect both parties from third-party claims. `;
    }
  }

  // Indemnification provisions
  if (indemnification) {
    if (typeof indemnification === "object") {
      summary += `Indemnification obligations are clearly defined `;
      if (indemnification.scope) {
        summary += `with coverage extending to ${indemnification.scope} `;
      }
      if (indemnification.limitations) {
        summary += `subject to limitations of ${indemnification.limitations} `;
      }
      summary += `to ensure appropriate risk allocation between the parties. `;
    } else if (typeof indemnification === "string") {
      summary += `Indemnification terms are specified as: ${indemnification}. `;
    }
  }

  // Termination conditions and procedures
  if (termination && typeof termination === "object") {
    summary += `Termination provisions establish clear procedures for contract conclusion. `;

    if (termination.t4c) {
      summary += `Termination for convenience is ${termination.t4c}`;
      if (termination.notice_t4c) {
        summary += ` requiring ${termination.notice_t4c} advance notice`;
      }
      summary += `. `;
    }
    if (termination.t4cause) {
      summary += `Termination for cause is ${termination.t4cause}`;
      if (termination.notice_t4cause) {
        summary += ` with ${termination.notice_t4cause} notice requirements`;
      }
      summary += `. `;
    }
    if (termination.survival_clauses) {
      summary += `Certain contractual obligations will survive termination, including ${termination.survival_clauses}. `;
    }
  }

  // Renewal and extension provisions
  if (renewalTerms && Array.isArray(renewalTerms) && renewalTerms.length > 0) {
    const renewal = renewalTerms[0];
    summary += `Contract renewal mechanisms are established `;

    if (renewal.auto_renewal) {
      summary += `with ${renewal.auto_renewal} automatic renewal provisions`;
      if (renewal.notice_period) {
        summary += ` requiring ${renewal.notice_period} notice for non-renewal`;
      }
      if (renewal.pricing_conditionality) {
        summary += ` and ${renewal.pricing_conditionality} pricing adjustment conditions`;
      }
      summary += `. `;
    }
    if (renewal.renewal_terms) {
      summary += `Renewal terms specify ${renewal.renewal_terms}. `;
    }
  }

  // Dispute resolution and governing law
  if (disputeResolution || governingLaw) {
    if (disputeResolution && typeof disputeResolution === "object") {
      summary += `Dispute resolution procedures are established `;
      if (disputeResolution.method) {
        summary += `utilizing ${disputeResolution.method} `;
      }
      if (disputeResolution.jurisdiction) {
        summary += `under the jurisdiction of ${disputeResolution.jurisdiction} `;
      }
      if (disputeResolution.arbitration) {
        summary += `with arbitration provisions of ${disputeResolution.arbitration} `;
      }
      summary += `to ensure efficient resolution of any contractual disputes. `;
    } else if (disputeResolution && typeof disputeResolution === "string") {
      summary += `Dispute resolution is governed by: ${disputeResolution}. `;
    }

    if (governingLaw) {
      summary += `The agreement is governed by and construed in accordance with ${governingLaw}. `;
    }
  }

  // Amendments and modifications
  if (amendments) {
    if (typeof amendments === "object") {
      const amendmentDetails = Object.values(amendments).filter((a) =>
        isValueMeaningful(a)
      );
      if (amendmentDetails.length > 0) {
        summary += `Contract modification procedures are defined, with amendments requiring ${amendmentDetails.join(
          ", "
        )}. `;
      }
    } else if (typeof amendments === "string") {
      summary += `Amendment procedures are specified as: ${amendments}. `;
    }
  }

  // Attachments and related documents
  if (attachments) {
    if (Array.isArray(attachments) && attachments.length > 0) {
      summary += `The contract incorporates ${attachments.length} attachment${
        attachments.length > 1 ? "s" : ""
      } or exhibit${
        attachments.length > 1 ? "s" : ""
      }, including ${attachments.join(
        ", "
      )}, which form integral parts of the agreement. `;
    } else if (typeof attachments === "string") {
      summary += `Contract attachments include: ${attachments}. `;
    }
  }

  // Paragraph 5: System Context and Analysis
  if (contract.createdAt || contract.metadata?.lastExtractedAt) {
    summary += "\n\n";

    if (contract.createdAt) {
      const timeAgo = formatDistanceToNow(new Date(contract.createdAt), {
        addSuffix: true,
      });
      summary += `This contract was added to the system ${timeAgo}`;
      if (contract.metadata?.lastExtractedAt) {
        const analysisTime = formatDistanceToNow(
          new Date(contract.metadata.lastExtractedAt),
          { addSuffix: true }
        );
        summary += ` and was last analyzed by AI ${analysisTime}`;
      }
      summary += ". ";
    }

    // Add confidence context if available
    if (contract.metadata?.confidence_scores) {
      const scores = Object.values(
        contract.metadata.confidence_scores
      ) as number[];
      if (scores.length > 0) {
        const avgConfidence = scores.reduce((a, b) => a + b, 0) / scores.length;
        if (avgConfidence > 0.8) {
          summary +=
            "The AI analysis shows high confidence in the extracted information.";
        } else if (avgConfidence > 0.6) {
          summary +=
            "The AI analysis shows moderate confidence in the extracted information.";
        } else {
          summary +=
            "The AI analysis indicates some uncertainty in the extracted information and manual review may be beneficial.";
        }
      }
    }
  }

  return summary.trim();
};

export function ContractSummaryNarrative({
  contract,
  className = "",
  onSummaryGenerated,
}: ContractSummaryNarrativeProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSummary, setGeneratedSummary] = useState<string | null>(null);

  const summary = generatedSummary || generateContractSummary(contract);
  const hasAISummary =
    contract.metadata?.aiGeneratedSummary &&
    isValueMeaningful(contract.metadata.aiGeneratedSummary);

  const handleGenerateSummary = async () => {
    if (!contract.id) {
      toast.error("Contract ID not available");
      return;
    }

    setIsGenerating(true);
    try {
      const response = await contractService.generateContractSummary(
        contract.id
      );
      if (response.success && response.summary) {
        setGeneratedSummary(response.summary);
        onSummaryGenerated?.(response.summary);
        toast.success("AI summary generated successfully!");
      } else {
        toast.error("Failed to generate summary");
      }
    } catch (error) {
      console.error("Error generating summary:", error);
      toast.error("Failed to generate AI summary. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className={`relative ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            Contract Summary
            {/* {hasAISummary && (
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                AI Generated
              </span>
            )} */}
          </div>
          {!hasAISummary && !generatedSummary && (
            <Button
              onClick={handleGenerateSummary}
              disabled={isGenerating}
              size="sm"
              variant="outline"
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4" />
              )}
              {isGenerating ? "Generating..." : "Generate AI Summary"}
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <div className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
            {summary}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
