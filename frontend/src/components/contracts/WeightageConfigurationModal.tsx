"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Plus,
  Trash2,
  Save,
  Download,
  Upload,
  AlertCircle,
  Info,
} from "lucide-react";
import {
  IntegrityService,
  IntegrityConfigurationData,
  Integrity<PERSON>lause,
  IntegrityClauseValue,
} from "@/services/integrityService";

interface WeightageConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (configuration: IntegrityConfigurationData) => void;
  initialConfiguration?: IntegrityConfigurationData;
}

export function WeightageConfigurationModal({
  isOpen,
  onClose,
  onSave,
  initialConfiguration,
}: WeightageConfigurationModalProps) {
  const [configuration, setConfiguration] = useState<IntegrityConfigurationData>({
    configurationName: "",
    description: "",
    clauses: [],
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (initialConfiguration) {
      setConfiguration(initialConfiguration);
    } else {
      // Load default template
      loadDefaultTemplate();
    }
  }, [initialConfiguration, isOpen]);

  const loadDefaultTemplate = async () => {
    try {
      const template = await IntegrityService.getDefaultTemplate();
      setConfiguration(template);
    } catch (error) {
      console.error("Error loading default template:", error);
      toast.error("Failed to load default template");
    }
  };

  const validateConfiguration = () => {
    const validationErrors = IntegrityService.validateConfiguration(configuration);
    setErrors(validationErrors);
    return validationErrors.length === 0;
  };

  const handleSave = async () => {
    if (!validateConfiguration()) {
      toast.error("Please fix the validation errors before saving");
      return;
    }

    setIsLoading(true);
    try {
      await onSave(configuration);
      toast.success("Configuration saved successfully");
      onClose();
    } catch (error) {
      console.error("Error saving configuration:", error);
      toast.error("Failed to save configuration");
    } finally {
      setIsLoading(false);
    }
  };

  const addClause = () => {
    const newClause: IntegrityClause = {
      id: `clause_${Date.now()}`,
      name: "",
      description: "",
      category: "General",
      possibleValues: [
        {
          value: "",
          weightage: 0,
          riskLevel: "High",
          description: "",
        },
      ],
    };
    setConfiguration(prev => ({
      ...prev,
      clauses: [...prev.clauses, newClause],
    }));
  };

  const removeClause = (clauseIndex: number) => {
    setConfiguration(prev => ({
      ...prev,
      clauses: prev.clauses.filter((_, index) => index !== clauseIndex),
    }));
  };

  const updateClause = (clauseIndex: number, updates: Partial<IntegrityClause>) => {
    setConfiguration(prev => ({
      ...prev,
      clauses: prev.clauses.map((clause, index) =>
        index === clauseIndex ? { ...clause, ...updates } : clause
      ),
    }));
  };

  const addPossibleValue = (clauseIndex: number) => {
    const newValue: IntegrityClauseValue = {
      value: "",
      weightage: 0,
      riskLevel: "High",
      description: "",
    };

    setConfiguration(prev => ({
      ...prev,
      clauses: prev.clauses.map((clause, index) =>
        index === clauseIndex
          ? { ...clause, possibleValues: [...clause.possibleValues, newValue] }
          : clause
      ),
    }));
  };

  const removePossibleValue = (clauseIndex: number, valueIndex: number) => {
    setConfiguration(prev => ({
      ...prev,
      clauses: prev.clauses.map((clause, index) =>
        index === clauseIndex
          ? {
            ...clause,
            possibleValues: clause.possibleValues.filter((_, vIndex) => vIndex !== valueIndex),
          }
          : clause
      ),
    }));
  };

  const updatePossibleValue = (
    clauseIndex: number,
    valueIndex: number,
    updates: Partial<IntegrityClauseValue>
  ) => {
    setConfiguration(prev => ({
      ...prev,
      clauses: prev.clauses.map((clause, index) =>
        index === clauseIndex
          ? {
            ...clause,
            possibleValues: clause.possibleValues.map((value, vIndex) =>
              vIndex === valueIndex ? { ...value, ...updates } : value
            ),
          }
          : clause
      ),
    }));
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "Low":
        return "bg-green-100 text-green-800 border-green-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "High":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const maxScore = IntegrityService.calculateMaxScore(configuration);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-primary" />
            Configure Integrity Analysis
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="clauses">Clauses Configuration</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="configName">Configuration Name</Label>
                <Input
                  id="configName"
                  value={configuration.configurationName}
                  onChange={(e) =>
                    setConfiguration(prev => ({
                      ...prev,
                      configurationName: e.target.value,
                    }))
                  }
                  placeholder="Enter configuration name"
                />
              </div>
              <div>
                <Label htmlFor="version">Version</Label>
                <Input
                  id="version"
                  value={configuration.version || ""}
                  onChange={(e) =>
                    setConfiguration(prev => ({
                      ...prev,
                      version: e.target.value,
                    }))
                  }
                  placeholder="e.g., 1.0"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={configuration.description || ""}
                onChange={(e) =>
                  setConfiguration(prev => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Describe this configuration..."
                rows={3}
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Configuration Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary">
                      {configuration.clauses.length}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Clauses</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">{maxScore}</div>
                    <div className="text-sm text-muted-foreground">Max Score</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">
                      {new Set(configuration.clauses.map(c => c.category)).size}
                    </div>
                    <div className="text-sm text-muted-foreground">Categories</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {errors.length > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-800 flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    Validation Errors
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-1 text-red-700">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="clauses" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Clauses Configuration</h3>
              <Button onClick={addClause} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Clause
              </Button>
            </div>

            <ScrollArea className="h-[400px]">
              <div className="space-y-4">
                {configuration.clauses.map((clause, clauseIndex) => (
                  <Card key={clause.id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex justify-between items-start">
                        <div className="grid grid-cols-2 gap-4 flex-1">
                          <div>
                            <Label>Clause Name</Label>
                            <Input
                              value={clause.name}
                              onChange={(e) =>
                                updateClause(clauseIndex, { name: e.target.value })
                              }
                              placeholder="Enter clause name"
                            />
                          </div>
                          <div>
                            <Label>Category</Label>
                            <Input
                              value={clause.category}
                              onChange={(e) =>
                                updateClause(clauseIndex, { category: e.target.value })
                              }
                              placeholder="Enter category"
                            />
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeClause(clauseIndex)}
                          className="ml-4"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div>
                        <Label>Description</Label>
                        <Textarea
                          value={clause.description}
                          onChange={(e) =>
                            updateClause(clauseIndex, { description: e.target.value })
                          }
                          placeholder="Describe this clause..."
                          rows={2}
                        />
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <Label>Possible Values</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => addPossibleValue(clauseIndex)}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Value
                          </Button>
                        </div>

                        <div className="space-y-2">
                          {clause.possibleValues.map((value, valueIndex) => (
                            <div
                              key={valueIndex}
                              className="grid grid-cols-12 gap-2 items-center p-2 border rounded"
                            >
                              <div className="col-span-4">
                                <Input
                                  value={value.value}
                                  onChange={(e) =>
                                    updatePossibleValue(clauseIndex, valueIndex, {
                                      value: e.target.value,
                                    })
                                  }
                                  placeholder="Value"
                                />
                              </div>
                              <div className="col-span-2">
                                <Input
                                  type="number"
                                  value={value.weightage}
                                  onChange={(e) =>
                                    updatePossibleValue(clauseIndex, valueIndex, {
                                      weightage: parseFloat(e.target.value) || 0,
                                    })
                                  }
                                  placeholder="Weight"
                                />
                              </div>
                              <div className="col-span-2">
                                <select
                                  value={value.riskLevel}
                                  onChange={(e) =>
                                    updatePossibleValue(clauseIndex, valueIndex, {
                                      riskLevel: e.target.value as 'Low' | 'Medium' | 'High',
                                    })
                                  }
                                  className="w-full px-2 py-1 border rounded text-sm"
                                >
                                  <option value="Low">Low</option>
                                  <option value="Medium">Medium</option>
                                  <option value="High">High</option>
                                </select>
                              </div>
                              <div className="col-span-3">
                                <Input
                                  value={value.description}
                                  onChange={(e) =>
                                    updatePossibleValue(clauseIndex, valueIndex, {
                                      description: e.target.value,
                                    })
                                  }
                                  placeholder="Description"
                                />
                              </div>
                              <div className="col-span-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removePossibleValue(clauseIndex, valueIndex)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Saving..." : "Save Configuration"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
