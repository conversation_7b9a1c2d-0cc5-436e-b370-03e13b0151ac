/**
 * Contract Entitlement Dashboard
 * Displays individual contract entitlements with commercial, SKU, and legal details
 */

"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Building2,
  DollarSign,
  Package,
  FileText,
  AlertCircle,
  RefreshCw,
  TrendingUp,
  Calendar,
  Shield,
  Globe,
  Users,
  Key,
  Gavel,
  CreditCard,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  contractEntitlementService,
  ContractEntitlements,
} from "@/services/contractEntitlementService";
import { ContractValueCard } from "./ContractValueCard";
import { ContractTermCard } from "./ContractTermCard";
import { RenewalNoticeCard } from "./RenewalNoticeCard";
import { DiscountCard } from "./DiscountCard";
import { YearWisePurchasingTable } from "./YearWisePurchasingTable";
import { UseRightsLegalCard } from "./UseRightsLegalCard";

interface ContractEntitlementDashboardProps {
  contractId: string;
  className?: string;
}

export function ContractEntitlementDashboard({
  contractId,
  className = "",
}: ContractEntitlementDashboardProps) {
  const [entitlements, setEntitlements] = useState<ContractEntitlements | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEntitlements = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await contractEntitlementService.getContractEntitlements(contractId);
      setEntitlements(data);
    } catch (err) {
      console.error("Error fetching entitlements:", err);
      setError("Failed to load entitlement data");
      toast.error("Failed to load entitlement data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (contractId) {
      fetchEntitlements();
    }
  }, [contractId]);

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Error Loading Entitlement Data
              </h3>
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
              <Button
                onClick={fetchEntitlements}
                variant="outline"
                size="sm"
                className="text-green-600 border-green-600 hover:bg-green-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!entitlements) {
    return (
      <div className={`${className}`}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Entitlement Data Available
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                This contract doesn't have entitlement data available.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Contract Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <ContractValueCard contractValue={entitlements.contractValue} />
        <ContractTermCard contractTerm={entitlements.contractTerm} />
        <RenewalNoticeCard renewalNotice={entitlements.renewalNotice} />
        <DiscountCard discount={entitlements.discount} />
      </div>

      {/* Year-wise Purchasing Breakdown */}
      {Object.keys(entitlements.yearWisePurchasing).length > 0 && (
        <YearWisePurchasingTable
          contractName={`Contract Entitlements`}
          purchasingData={entitlements.yearWisePurchasing}
        />
      )}

      {/* Use Rights & Legal Terms */}
      <UseRightsLegalCard
        useRightsLegal={entitlements.useRightsLegal}
      />
    </div>
  );
}
