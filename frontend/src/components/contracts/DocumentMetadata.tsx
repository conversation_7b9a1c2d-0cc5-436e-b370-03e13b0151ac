"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  FileText,
  Download,
  Calendar,
  HardDrive,
  FileType,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import { downloadContractDocument } from "@/lib/download-utils";

interface DocumentMetadata {
  fileName: string;
  mimeType: string;
  size: number;
  uploadDate: string;
  versionNumber: number;
  hasDocument: boolean;
}

interface DocumentMetadataProps {
  contractId: string;
  onDownload?: () => void;
  className?: string;
}

/**
 * Formats file size in human readable format
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Gets file type display name from MIME type
 */
function getFileTypeDisplay(mimeType: string): string {
  const typeMap: Record<string, string> = {
    "application/pdf": "PDF Document",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      "Word Document",
    "application/msword": "Word Document",
    "text/plain": "Text Document",
    "application/rtf": "RTF Document",
    "text/html": "HTML Document",
    "text/markdown": "Markdown Document",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      "Excel Spreadsheet",
    "application/vnd.ms-excel": "Excel Spreadsheet",
    "text/csv": "CSV File",
  };

  return typeMap[mimeType] || "Document";
}

/**
 * Gets appropriate icon for file type
 */
function getFileIcon(mimeType: string) {
  if (mimeType.includes("pdf")) {
    return <FileText className="h-4 w-4 text-red-500" />;
  } else if (mimeType.includes("word") || mimeType.includes("document")) {
    return <FileText className="h-4 w-4 text-blue-500" />;
  } else if (
    mimeType.includes("spreadsheet") ||
    mimeType.includes("excel") ||
    mimeType.includes("csv")
  ) {
    return <FileText className="h-4 w-4 text-green-500" />;
  } else {
    return <FileText className="h-4 w-4 text-gray-500" />;
  }
}

export function DocumentMetadata({
  contractId,
  onDownload,
  className,
}: DocumentMetadataProps) {
  const [metadata, setMetadata] = useState<DocumentMetadata | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDocumentMetadata();
  }, [contractId]);

  const fetchDocumentMetadata = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiClient.get(
        `/api/contracts/${contractId}/document/metadata`
      );
      setMetadata(data);
    } catch (error) {
      console.error("Error fetching document metadata:", error);
      setError("Failed to load document information");
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download behavior using authenticated download
      try {
        await downloadContractDocument(contractId, metadata?.fileName);
        toast.success("Document downloaded successfully");
      } catch (error) {
        console.error("Error downloading document:", error);
        toast.error("Failed to download document");
      }
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Document Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchDocumentMetadata}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metadata?.hasDocument) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">
                No document available
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Upload a document to view it here
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Document Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* File Name and Type */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {getFileIcon(metadata.mimeType)}
            <div className="min-w-0 flex-1">
              <p className="font-medium text-sm truncate">
                {metadata.fileName}
              </p>
              <p className="text-xs text-muted-foreground">
                {getFileTypeDisplay(metadata.mimeType)}
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="ml-2">
            v{metadata.versionNumber}
          </Badge>
        </div>

        {/* File Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <HardDrive className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Size</p>
              <p className="font-medium">{formatFileSize(metadata.size)}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Uploaded</p>
              <p className="font-medium">
                {new Date(metadata.uploadDate).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* MIME Type */}
        <div className="flex items-center gap-2 text-sm">
          <FileType className="h-4 w-4 text-muted-foreground" />
          <div>
            <p className="text-muted-foreground">Format</p>
            <p className="font-mono text-xs bg-muted px-2 py-1 rounded">
              {metadata.mimeType}
            </p>
          </div>
        </div>

        {/* Download Button */}
        <Button onClick={handleDownload} className="w-full" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Download Document
        </Button>
      </CardContent>
    </Card>
  );
}
