/**
 * Contract Hierarchy Timeline Component
 * Enhanced timeline visualization showing both chronological order and document hierarchy
 * Displays contracts with vertical hierarchy levels and connecting lines
 */

"use client";

import React, { useMemo } from "react";
import { FileText, Award, Calendar, GitBranch } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { formatRelativeDate, formatContractValue } from "@/lib/format-utils";

// Contract hierarchy levels
export enum ContractHierarchyLevel {
  LEVEL_1 = 1, // Master agreements (MSA, GMSA, OMA, GTC)
  LEVEL_2 = 2, // Schedules (SLA, DPA, Pricing, Governance)
  LEVEL_3 = 3, // Program Charter, Support Terms, Cloud Addenda
  LEVEL_4 = 4, // Statements of Work (SOW), Ordering Documents
  LEVEL_5 = 5, // Order Forms, Engagement Letters, Product Use Rights
  LEVEL_6 = 6, // Purchase Orders (PO)
  LEVEL_7 = 7, // Invoices
}

export interface ContractWithHierarchy {
  id: string;
  title: string;
  agreementType: string;
  startDate?: string;
  endDate?: string;
  value?: string;
  provider?: string;
  hierarchyLevel?: ContractHierarchyLevel;
  parentContracts?: string[];
  childContracts?: string[];
  groupId?: string;
}

interface ContractHierarchyTimelineProps {
  contracts: ContractWithHierarchy[];
  groupId: string;
  className?: string;
  showConnectors?: boolean;
  timelineRange?: { start: Date; end: Date };
}

interface TimelinePosition {
  contract: ContractWithHierarchy;
  x: number; // Horizontal position (time-based)
  y: number; // Vertical position (hierarchy-based)
  level: ContractHierarchyLevel;
  hasParent: boolean;
  hasChildren: boolean;
}

export function ContractHierarchyTimeline({
  contracts,
  groupId,
  className,
  showConnectors = true,
  timelineRange,
}: ContractHierarchyTimelineProps) {
  // Calculate timeline positions
  const timelinePositions = useMemo(() => {
    if (contracts.length === 0) return [];

    // Determine time range
    const dates = contracts
      .flatMap(c => [c.startDate, c.endDate])
      .filter(Boolean)
      .map(d => new Date(d!));
    
    const minDate = timelineRange?.start || (dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : new Date());
    const maxDate = timelineRange?.end || (dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : new Date());
    const timeSpan = maxDate.getTime() - minDate.getTime();

    // Calculate positions
    const positions: TimelinePosition[] = contracts.map(contract => {
      const contractDate = contract.startDate ? new Date(contract.startDate) : minDate;
      const x = timeSpan > 0 ? ((contractDate.getTime() - minDate.getTime()) / timeSpan) * 100 : 0;
      
      // Determine hierarchy level
      const level = contract.hierarchyLevel || ContractHierarchyLevel.LEVEL_1;
      const y = (level - 1) * 80; // 80px spacing between levels

      return {
        contract,
        x,
        y,
        level,
        hasParent: (contract.parentContracts?.length || 0) > 0,
        hasChildren: (contract.childContracts?.length || 0) > 0,
      };
    });

    // Sort by hierarchy level, then by date
    return positions.sort((a, b) => {
      if (a.level !== b.level) return a.level - b.level;
      const dateA = a.contract.startDate ? new Date(a.contract.startDate).getTime() : 0;
      const dateB = b.contract.startDate ? new Date(b.contract.startDate).getTime() : 0;
      return dateA - dateB;
    });
  }, [contracts, timelineRange]);

  // Calculate timeline dimensions
  const maxLevel = Math.max(...timelinePositions.map(p => p.level), 1);
  const timelineHeight = maxLevel * 80 + 100; // Extra space for padding

  if (contracts.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <GitBranch className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            No Hierarchy Data
          </h3>
          <p className="text-sm text-muted-foreground text-center">
            No contracts found for this group or hierarchy relationships could not be determined.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitBranch className="h-5 w-5" style={{ color: "#09260D" }} />
          Contract Hierarchy Timeline
          <Badge variant="outline" className="ml-2">
            {contracts.length} contracts
          </Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Group: {groupId} • Showing document hierarchy and chronological relationships
        </p>
      </CardHeader>
      <CardContent>
        <div className="relative w-full overflow-x-auto">
          {/* Timeline Container */}
          <div 
            className="relative min-w-[800px] bg-muted/20 rounded-lg p-6"
            style={{ height: `${timelineHeight}px` }}
          >
            {/* Hierarchy Level Labels */}
            <div className="absolute left-0 top-0 w-20 h-full">
              {Array.from({ length: maxLevel }, (_, i) => (
                <div
                  key={i + 1}
                  className="absolute left-0 text-xs font-medium text-muted-foreground"
                  style={{ top: `${i * 80 + 20}px` }}
                >
                  Level {i + 1}
                </div>
              ))}
            </div>

            {/* Timeline Grid Lines */}
            {Array.from({ length: maxLevel }, (_, i) => (
              <div
                key={`grid-${i}`}
                className="absolute left-24 right-6 border-t border-dashed border-muted-foreground/20"
                style={{ top: `${i * 80 + 40}px` }}
              />
            ))}

            {/* Contract Nodes */}
            {timelinePositions.map((position, index) => (
              <ContractNode
                key={position.contract.id}
                position={position}
                showConnectors={showConnectors}
                allPositions={timelinePositions}
              />
            ))}

            {/* Connection Lines */}
            {showConnectors && (
              <ConnectionLines positions={timelinePositions} />
            )}
          </div>

          {/* Legend */}
          <div className="mt-6 flex flex-wrap gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[#09260D]" />
              <span>MSA/Master Agreement</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500" />
              <span>SOW/Work Statement</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-orange-500" />
              <span>PO/Purchase Order</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gray-500" />
              <span>Other Documents</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Contract Node Component
interface ContractNodeProps {
  position: TimelinePosition;
  showConnectors: boolean;
  allPositions: TimelinePosition[];
}

function ContractNode({ position }: ContractNodeProps) {
  const { contract, x, y } = position;
  
  // Determine node color based on agreement type
  const getNodeColor = (agreementType: string) => {
    const type = agreementType.toUpperCase();
    if (type.includes('MSA') || type.includes('MASTER')) return '#09260D';
    if (type.includes('SOW') || type.includes('STATEMENT')) return '#3b82f6';
    if (type.includes('PO') || type.includes('PURCHASE')) return '#f97316';
    return '#6b7280';
  };

  const nodeColor = getNodeColor(contract.agreementType);
  const isMSA = contract.agreementType.toUpperCase().includes('MSA');

  return (
    <div
      className="absolute group"
      style={{
        left: `calc(24px + ${x}%)`,
        top: `${y + 20}px`,
        transform: 'translateX(-50%)',
      }}
    >
      {/* Contract Node */}
      <div
        className={cn(
          "relative w-4 h-4 rounded-full border-2 border-white shadow-lg cursor-pointer transition-all duration-200",
          "hover:scale-125 hover:shadow-xl",
          isMSA && "ring-2 ring-[#09260D]/30"
        )}
        style={{ backgroundColor: nodeColor }}
      />

      {/* Contract Info Tooltip */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
        <div className="bg-popover border rounded-lg shadow-lg p-3 min-w-[200px] max-w-[300px]">
          <div className="space-y-2">
            <div className="font-medium text-sm">{contract.title}</div>
            <div className="text-xs text-muted-foreground">
              Type: {contract.agreementType.replace(/_/g, ' ')}
            </div>
            {contract.provider && (
              <div className="text-xs text-muted-foreground">
                Provider: {contract.provider}
              </div>
            )}
            {contract.startDate && (
              <div className="text-xs text-muted-foreground">
                Start: {new Date(contract.startDate).toLocaleDateString()}
              </div>
            )}
            {contract.value && (
              <div className="text-xs text-muted-foreground">
                Value: {formatContractValue(contract.value)}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Connection Lines Component
interface ConnectionLinesProps {
  positions: TimelinePosition[];
}

function ConnectionLines({ positions }: ConnectionLinesProps) {
  const connections = useMemo(() => {
    const lines: Array<{ from: TimelinePosition; to: TimelinePosition }> = [];
    
    positions.forEach(position => {
      if (position.contract.parentContracts) {
        position.contract.parentContracts.forEach(parentId => {
          const parentPosition = positions.find(p => p.contract.id === parentId);
          if (parentPosition) {
            lines.push({ from: parentPosition, to: position });
          }
        });
      }
    });
    
    return lines;
  }, [positions]);

  return (
    <svg className="absolute inset-0 w-full h-full pointer-events-none">
      {connections.map((connection, index) => {
        const fromX = 24 + (connection.from.x / 100) * (100 - 6); // Adjust for padding
        const fromY = connection.from.y + 28; // Center of node
        const toX = 24 + (connection.to.x / 100) * (100 - 6);
        const toY = connection.to.y + 28;

        return (
          <g key={index}>
            {/* Vertical line from parent */}
            <line
              x1={fromX}
              y1={fromY}
              x2={fromX}
              y2={fromY + 40}
              stroke="#09260D"
              strokeWidth="2"
              strokeDasharray="4 4"
              opacity="0.6"
            />
            {/* Horizontal line */}
            <line
              x1={fromX}
              y1={fromY + 40}
              x2={toX}
              y2={fromY + 40}
              stroke="#09260D"
              strokeWidth="2"
              strokeDasharray="4 4"
              opacity="0.6"
            />
            {/* Vertical line to child */}
            <line
              x1={toX}
              y1={fromY + 40}
              x2={toX}
              y2={toY}
              stroke="#09260D"
              strokeWidth="2"
              strokeDasharray="4 4"
              opacity="0.6"
            />
            {/* Arrow head */}
            <polygon
              points={`${toX-3},${toY-6} ${toX+3},${toY-6} ${toX},${toY}`}
              fill="#09260D"
              opacity="0.6"
            />
          </g>
        );
      })}
    </svg>
  );
}
