import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Percent, Tag } from "lucide-react";
import { DiscountCard as DiscountCardType } from "@/services/contractEntitlementService";

interface DiscountCardProps {
  discount: DiscountCardType;
}

export const DiscountCard: React.FC<DiscountCardProps> = ({
  discount,
}) => {
  const getValueColor = (value: string) => {
    if (value === "N/A") return "text-gray-500 dark:text-gray-400";
    return "text-gray-900 dark:text-gray-100";
  };

  const formatFieldKey = (key: string) => {
    if (key === "N/A") return "N/A";
    return key.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const hasDiscountData = discount.discountLevel !== "N/A" || discount.discountPercentage !== "N/A";

  return (
    <Card className="h-full border-0 shadow-sm bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-purple-100 dark:bg-purple-900/30">
              <Percent className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Discount
            </span>
          </div>

          {hasDiscountData ? (
            <>
              {/* Main Value - Discount Percentage or Level */}
              <div className="space-y-1">
                {discount.discountPercentage !== "N/A" ? (
                  <>
                    <div className={`text-xl font-bold ${getValueColor(discount.discountPercentage)}`}>
                      {discount.discountPercentage}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {formatFieldKey(discount.discountFieldKey)}
                    </div>
                  </>
                ) : (
                  <>
                    <div className={`text-xl font-bold ${getValueColor(discount.discountLevel)}`}>
                      {discount.discountLevel}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Discount Level
                    </div>
                  </>
                )}

              </div>
            </>
          ) : (
            /* No Discount Data */
            <div className="space-y-1">
              <div className="text-xl font-bold text-gray-400 dark:text-gray-500">
                N/A
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                No discount available
              </div>

            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
