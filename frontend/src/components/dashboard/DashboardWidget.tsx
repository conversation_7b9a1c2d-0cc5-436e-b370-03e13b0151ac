/**
 * Dashboard Widget Base Component
 * Provides a consistent layout and styling for dashboard widgets
 */

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

export interface DashboardWidgetProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  onConfigure?: () => void;
  onRemove?: () => void;
  actions?: React.ReactNode;
}

export const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  title,
  description,
  children,
  className,
  loading = false,
  error,
  onRefresh,
  onConfigure,
  onRemove,
  actions,
}) => {
  return (
    <Card className={cn("dashboard-widget", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {description && (
            <CardDescription className="text-xs">{description}</CardDescription>
          )}
        </div>
        <div className="flex items-center gap-1">
          {actions}
          {onRefresh && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          )}
          {(onConfigure || onRemove) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onConfigure && (
                  <DropdownMenuItem onClick={onConfigure}>
                    Configure
                  </DropdownMenuItem>
                )}
                {onRemove && (
                  <DropdownMenuItem
                    onClick={onRemove}
                    className="text-destructive"
                  >
                    Remove
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="flex items-center justify-center h-32 text-center">
            <div className="text-sm text-muted-foreground">
              <p className="font-medium text-destructive">Error loading data</p>
              <p className="mt-1">{error}</p>
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={onRefresh}
                >
                  Try Again
                </Button>
              )}
            </div>
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin" />
              Loading...
            </div>
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Widget Skeleton for loading states
 */
export const DashboardWidgetSkeleton: React.FC<{ className?: string }> = ({
  className,
}) => {
  return (
    <Card className={cn("dashboard-widget", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <div className="h-4 w-32 bg-muted rounded animate-pulse" />
          <div className="h-3 w-48 bg-muted rounded animate-pulse" />
        </div>
        <div className="h-8 w-8 bg-muted rounded animate-pulse" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="h-8 w-full bg-muted rounded animate-pulse" />
          <div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
          <div className="h-4 w-1/2 bg-muted rounded animate-pulse" />
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * KPI Widget for displaying key metrics
 */
export interface KPIWidgetProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    period: string;
  };
  icon?: React.ReactNode;
  className?: string;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

export const KPIWidget: React.FC<KPIWidgetProps> = ({
  title,
  value,
  change,
  icon,
  className,
  loading,
  error,
  onRefresh,
}) => {
  return (
    <DashboardWidget
      title={title}
      className={className}
      loading={loading}
      error={error}
      onRefresh={onRefresh}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold">{value}</div>
          {change && (
            <p className="text-xs text-muted-foreground">
              <span
                className={cn(
                  "font-medium",
                  change.value > 0
                    ? "text-green-600"
                    : change.value < 0
                    ? "text-red-600"
                    : ""
                )}
              >
                {change.value > 0 ? "+" : ""}
                {change.value}
              </span>{" "}
              from {change.period}
            </p>
          )}
        </div>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>
    </DashboardWidget>
  );
};

/**
 * Chart Widget wrapper for chart components
 */
export interface ChartWidgetProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  height?: number;
}

export const ChartWidget: React.FC<ChartWidgetProps> = ({
  title,
  description,
  children,
  className,
  loading,
  error,
  onRefresh,
  height = 300,
}) => {
  return (
    <DashboardWidget
      title={title}
      description={description}
      className={className}
      loading={loading}
      error={error}
      onRefresh={onRefresh}
    >
      <div
        style={{ height: `${height}px` }}
        className="w-full flex items-center justify-center"
      >
        <div className="w-full h-full">{children}</div>
      </div>
    </DashboardWidget>
  );
};
