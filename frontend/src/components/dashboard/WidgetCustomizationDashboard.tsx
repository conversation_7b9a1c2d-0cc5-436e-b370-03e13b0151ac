/**
 * Widget Customization Dashboard
 * Advanced dashboard customization interface using the new widget system
 */

import React, { useState } from 'react';
import { ArrowLeft, Save, RotateCcw, Eye, EyeOff, Settings } from 'lucide-react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

// Hooks and utilities
import { useWidgets } from '@/hooks/useWidgets';
import { getWidgetInfo } from '@/constants/widgetTypes';
import { DashboardWidget } from '@/types/dashboard';

interface WidgetCardProps {
  widget: DashboardWidget;
  onToggleVisibility: (widgetId: string, isVisible: boolean) => void;
  onConfigure?: (widget: DashboardWidget) => void;
}

const WidgetCard: React.FC<WidgetCardProps> = ({
  widget,
  onToggleVisibility,
  onConfigure
}) => {
  const widgetInfo = getWidgetInfo(widget.widgetType as any);

  return (
    <Card className={`transition-all ${widget.isVisible ? 'border-green-200 bg-green-50/50' : 'border-gray-200 bg-gray-50/50'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base font-medium">{widget.name}</CardTitle>
            <CardDescription className="text-sm mt-1">
              {widget.description || widgetInfo?.description}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <Switch
              checked={widget.isVisible}
              onCheckedChange={(checked) => onToggleVisibility(widget.id, checked)}
            />
            {widget.isVisible ? (
              <Eye className="h-4 w-4 text-green-600" />
            ) : (
              <EyeOff className="h-4 w-4 text-gray-400" />
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {widget.category}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Order: {widget.order}
            </Badge>
          </div>
          {onConfigure && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onConfigure(widget)}
              className="h-8 px-2"
            >
              <Settings className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface CategorySectionProps {
  title: string;
  description: string;
  widgets: DashboardWidget[];
  onToggleVisibility: (widgetId: string, isVisible: boolean) => void;
  onConfigure?: (widget: DashboardWidget) => void;
}

const CategorySection: React.FC<CategorySectionProps> = ({
  title,
  description,
  widgets,
  onToggleVisibility,
  onConfigure
}) => {
  const visibleCount = widgets.filter(w => w.isVisible).length;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        <Badge variant="outline">
          {visibleCount} of {widgets.length} visible
        </Badge>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {widgets
          .sort((a, b) => a.order - b.order)
          .map((widget) => (
            <WidgetCard
              key={widget.id}
              widget={widget}
              onToggleVisibility={onToggleVisibility}
              onConfigure={onConfigure}
            />
          ))}
      </div>
    </div>
  );
};

export const WidgetCustomizationDashboard: React.FC = () => {
  const {
    widgets,
    loading,
    error,
    updateWidgetVisibility,
    resetWidgets
  } = useWidgets();

  const [activeTab, setActiveTab] = useState('priority');
  const [saving, setSaving] = useState(false);

  const handleToggleVisibility = async (widgetId: string, isVisible: boolean) => {
    await updateWidgetVisibility(widgetId, isVisible);
  };

  const handleConfigure = (widget: DashboardWidget) => {
    // TODO: Open configuration modal
    toast.info(`Configuration for ${widget.name} coming soon!`);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Changes are automatically saved when toggling visibility
      toast.success('Dashboard configuration saved successfully!');
    } catch (error) {
      toast.error('Failed to save dashboard configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    if (confirm('Are you sure you want to reset the dashboard to default settings? This will make all widgets visible and reset their positions.')) {
      await resetWidgets();
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Customize Dashboard</h1>
              <p className="text-muted-foreground">Loading widgets...</p>
            </div>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-32 bg-gray-100 animate-pulse rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-red-600">Error Loading Dashboard</h2>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Button asChild className="mt-4">
            <Link href="/cockpit/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!widgets) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold">No Widgets Found</h2>
          <p className="text-muted-foreground mt-2">Unable to load dashboard widgets</p>
          <Button asChild className="mt-4">
            <Link href="/cockpit/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Customize Dashboard</h1>
            <p className="text-muted-foreground">
              Configure which widgets are visible on your dashboard
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleReset}>
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset to Defaults
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="mr-2 h-4 w-4" />
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button variant="outline" asChild>
              <Link href="/cockpit/dashboard">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
        </div>

        {/* Widget Categories */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="priority">
              Priorities ({widgets.priority.filter(w => w.isVisible).length}/{widgets.priority.length})
            </TabsTrigger>
            <TabsTrigger value="portfolio">
              Portfolio ({widgets.portfolio.filter(w => w.isVisible).length}/{widgets.portfolio.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="priority" className="space-y-6">
            <CategorySection
              title="Priority Widgets"
              description="Critical contract actions requiring immediate attention"
              widgets={widgets.priority}
              onToggleVisibility={handleToggleVisibility}
              onConfigure={handleConfigure}
            />
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-6">
            <CategorySection
              title="Portfolio Widgets"
              description="Comprehensive overview of your contract portfolio"
              widgets={widgets.portfolio}
              onToggleVisibility={handleToggleVisibility}
              onConfigure={handleConfigure}
            />
          </TabsContent>
        </Tabs>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Dashboard Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {widgets.priority.filter(w => w.isVisible).length}
                </div>
                <div className="text-sm text-muted-foreground">Priority Widgets</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {widgets.portfolio.filter(w => w.isVisible).length}
                </div>
                <div className="text-sm text-muted-foreground">Portfolio Widgets</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {widgets.priority.filter(w => w.isVisible).length + widgets.portfolio.filter(w => w.isVisible).length}
                </div>
                <div className="text-sm text-muted-foreground">Total Visible</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-600">
                  {widgets.priority.length + widgets.portfolio.length}
                </div>
                <div className="text-sm text-muted-foreground">Total Available</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
