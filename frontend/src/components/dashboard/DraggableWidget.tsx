/**
 * Draggable Widget Component
 * Wraps dashboard widgets with DnD Kit drag-and-drop functionality and resize capability
 */

"use client";

import React, { useState, useRef, useCallback } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DashboardWidget as DashboardWidgetType } from "@/types/dashboard";
import { DashboardWidget } from "./DashboardWidget";
import { WidgetRenderer } from "./WidgetRenderer";
import { MultiResizeHandles, ResizeDirection } from "./ResizeHandle";
import { cn } from "@/lib/utils";

interface DraggableWidgetProps {
  widget: DashboardWidgetType;
  isEditMode: boolean;
  isSelected: boolean;
  onClick: () => void;
  onConfigure: () => void;
  onRemove: () => void;
  onResize?: (widgetId: string, newSize: { w: number; h: number }) => void;
}

export const DraggableWidget: React.FC<DraggableWidgetProps> = ({
  widget,
  isEditMode,
  isSelected,
  onClick,
  onConfigure,
  onRemove,
  onResize,
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStartPos, setResizeStartPos] = useState({ x: 0, y: 0 });
  const [resizeStartSize, setResizeStartSize] = useState({ w: 0, h: 0 });
  const widgetRef = useRef<HTMLDivElement>(null);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: widget.id,
    disabled: !isEditMode || isResizing,
  });

  // Create drag handle that excludes resize handles
  const dragHandleListeners = isEditMode && !isResizing ? listeners : {};

  const handleResizeStart = useCallback(
    (e: React.MouseEvent, direction: ResizeDirection) => {
      e.preventDefault();
      e.stopPropagation();

      const startPos = { x: e.clientX, y: e.clientY };
      const startSize = { w: widget.position.w, h: widget.position.h };

      setIsResizing(true);
      setResizeStartPos(startPos);
      setResizeStartSize(startSize);

      let lastUpdateTime = 0;
      const throttleDelay = 16; // ~60fps

      const handleMouseMove = (e: MouseEvent) => {
        if (!widgetRef.current) return;

        const now = Date.now();
        if (now - lastUpdateTime < throttleDelay) return;
        lastUpdateTime = now;

        const deltaX = e.clientX - startPos.x;
        const deltaY = e.clientY - startPos.y;

        // Calculate grid cell size
        const gridContainer = widgetRef.current.parentElement;
        if (!gridContainer) return;

        const containerWidth = gridContainer.clientWidth;
        const cellWidth = containerWidth / 12; // 12-column grid
        const cellHeight = 100; // Approximate row height

        const deltaW = Math.round(deltaX / cellWidth);
        const deltaH = Math.round(deltaY / cellHeight);

        let newW = startSize.w;
        let newH = startSize.h;

        // Calculate new dimensions based on resize direction
        switch (direction) {
          case "se": // bottom-right
            newW = Math.max(1, Math.min(12, startSize.w + deltaW));
            newH = Math.max(1, startSize.h + deltaH);
            break;
          case "sw": // bottom-left
            newW = Math.max(1, Math.min(12, startSize.w - deltaW));
            newH = Math.max(1, startSize.h + deltaH);
            break;
          case "ne": // top-right
            newW = Math.max(1, Math.min(12, startSize.w + deltaW));
            newH = Math.max(1, startSize.h - deltaH);
            break;
          case "nw": // top-left
            newW = Math.max(1, Math.min(12, startSize.w - deltaW));
            newH = Math.max(1, startSize.h - deltaH);
            break;
          case "n": // top
            newH = Math.max(1, startSize.h - deltaH);
            break;
          case "s": // bottom
            newH = Math.max(1, startSize.h + deltaH);
            break;
          case "e": // right
            newW = Math.max(1, Math.min(12, startSize.w + deltaW));
            break;
          case "w": // left
            newW = Math.max(1, Math.min(12, startSize.w - deltaW));
            break;
        }

        if (
          onResize &&
          (newW !== widget.position.w || newH !== widget.position.h)
        ) {
          onResize(widget.id, { w: newW, h: newH });
        }
      };

      const handleMouseUp = () => {
        setIsResizing(false);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    },
    [widget.id, widget.position.w, widget.position.h, onResize]
  );

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isResizing ? "none" : transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Calculate grid positioning and sizing
  const gridStyle = {
    gridColumn: `span ${widget.position.w}`,
    gridRow: `span ${Math.max(1, Math.floor(widget.position.h / 2))}`,
    minHeight: `${Math.max(200, widget.position.h * 60)}px`,
  };

  return (
    <div
      ref={(node) => {
        setNodeRef(node);
        if (node) {
          (widgetRef as React.MutableRefObject<HTMLDivElement | null>).current =
            node;
        }
      }}
      style={{
        ...style,
        ...gridStyle,
      }}
      className={cn(
        "draggable-widget relative group",
        "transition-all duration-200",
        isEditMode && !isResizing && "cursor-move",
        isSelected && "ring-2 ring-blue-500 ring-offset-2",
        isResizing && "ring-2 ring-green-500 ring-offset-2 resizing",
        isDragging && "z-50 shadow-2xl scale-105",
        !isEditMode && "cursor-default",
        isEditMode &&
        "border-2 border-dashed border-transparent hover:border-gray-300 dark:hover:border-gray-600",
        isSelected && "border-blue-500 dark:border-blue-400"
      )}
      data-widget-id={widget.id}
      onClick={onClick}
      {...attributes}
    >
      {/* Drag Handle - Only active in edit mode */}
      {isEditMode && (
        <div
          className="absolute top-2 left-2 w-6 h-6 cursor-move z-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          {...dragHandleListeners}
          title="Drag to move widget"
        >
          <div className="w-full h-full bg-blue-500 hover:bg-blue-600 rounded flex items-center justify-center text-white shadow-md">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
            </svg>
          </div>
        </div>
      )}

      <DashboardWidget
        title={widget.name}
        description={widget.description}
        loading={false}
        onRefresh={() => {
          // TODO: Implement widget refresh
          console.log("Refresh widget:", widget.id);
        }}
        onConfigure={isEditMode ? onConfigure : undefined}
        onRemove={isEditMode ? onRemove : undefined}
        className="h-full"
      >
        <WidgetRenderer widget={widget} />
      </DashboardWidget>

      {/* Multi-directional Resize Handles */}
      {isEditMode && isSelected && (
        <MultiResizeHandles
          onResizeStart={handleResizeStart}
          isResizing={isResizing}
          showAllHandles={true}
        />
      )}
    </div>
  );
};
