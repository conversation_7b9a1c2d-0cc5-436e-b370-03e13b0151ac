/**
 * Summary Dashboard Widgets
 * KPI widgets for dashboard summary data
 */

import React from "react";
import { FileText, CheckCircle, Clock, DollarSign } from "lucide-react";
import { KPIWidget } from "../DashboardWidget";
import { DashboardSummary, DashboardUtils } from "@/services/dashboardService";

interface SummaryWidgetsProps {
  data: DashboardSummary | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const SummaryWidgets: React.FC<SummaryWidgetsProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <KPIWidget
        title="Total Contracts"
        value={data?.totalContracts || 0}
        icon={<FileText className="h-4 w-4" />}
        loading={loading}
        error={error || undefined}
        onRefresh={onRefresh}
      />

      <KPIWidget
        title="Active Contracts"
        value={data?.activeContracts || 0}
        icon={<CheckCircle className="h-4 w-4" />}
        loading={loading}
        error={error || undefined}
        onRefresh={onRefresh}
      />

      <KPIWidget
        title="Upcoming Renewals"
        value={data?.upcomingRenewals || 0}
        icon={<Clock className="h-4 w-4" />}
        loading={loading}
        error={error || undefined}
        onRefresh={onRefresh}
      />

      <KPIWidget
        title="Total Value"
        value={
          data?.totalValue
            ? DashboardUtils.formatCurrency(
                data.totalValue,
                data.totalValueCurrency
              )
            : "$0"
        }
        icon={<DollarSign className="h-4 w-4" />}
        loading={loading}
        error={error || undefined}
        onRefresh={onRefresh}
      />
    </div>
  );
};

/**
 * Confidence Score Widget
 */
interface ConfidenceWidgetProps {
  confidence: number;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const ConfidenceWidget: React.FC<ConfidenceWidgetProps> = ({
  confidence,
  loading,
  error,
  onRefresh,
}) => {
  const confidencePercentage =
    confidence === -1 ? 0 : Math.round(confidence * 100);
  const displayValue =
    confidence === -1 ? "Manual" : `${confidencePercentage}%`;

  return (
    <div className="h-64 flex items-center">
      <KPIWidget
        title="Avg. Confidence"
        value={displayValue}
        icon={
          <div
            className={`h-4 w-4 rounded-full ${DashboardUtils.getConfidenceColor(
              confidence
            )}`}
          >
            <div className="h-full w-full rounded-full bg-current opacity-20" />
          </div>
        }
        loading={loading}
        error={error || undefined}
        onRefresh={onRefresh}
      />
    </div>
  );
};
