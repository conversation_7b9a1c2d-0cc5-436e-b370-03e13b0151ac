/**
 * Chart Dashboard Widgets
 * Various chart components for dashboard data visualization
 */

import React from "react";
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { ChartWidget } from "../DashboardWidget";
import {
  SpendAnalysisData,
  DashboardUtils,
  ActiveContractsPerSupplier,
} from "@/services/dashboardService";

// Custom label component for pie chart with connecting lines
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  outerRadius,
  percent,
  name,
}: any) => {
  const RADIAN = Math.PI / 180;

  // Calculate line points with responsive sizing
  const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
  const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
  const lineEndX = cx + (outerRadius + 15) * Math.cos(-midAngle * RADIAN);
  const lineEndY = cy + (outerRadius + 15) * Math.sin(-midAngle * RADIAN);

  // Calculate label position (outside the pie) - responsive
  const labelRadius = outerRadius + 25;
  const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);

  // Determine text anchor based on position
  const textAnchor = labelX > cx ? "start" : "end";

  // Only show label if percentage is significant (>3%)
  if (percent < 0.03) return null;

  return (
    <g>
      {/* Connecting line */}
      <line
        x1={lineStartX}
        y1={lineStartY}
        x2={lineEndX}
        y2={lineEndY}
        stroke="#666"
        strokeWidth={1}
        strokeDasharray="2,2"
      />
      {/* Horizontal line extension */}
      <line
        x1={lineEndX}
        y1={lineEndY}
        x2={labelX > cx ? lineEndX + 10 : lineEndX - 10}
        y2={lineEndY}
        stroke="#666"
        strokeWidth={1}
      />
      {/* Label text */}
      <text
        x={labelX > cx ? lineEndX + 12 : lineEndX - 12}
        y={lineEndY - 4}
        fill="#333"
        textAnchor={textAnchor}
        dominantBaseline="central"
        fontSize={11}
        fontWeight="500"
      >
        {name.length > 12 ? `${name.substring(0, 12)}...` : name}
      </text>
      {/* Percentage text */}
      <text
        x={labelX > cx ? lineEndX + 12 : lineEndX - 12}
        y={lineEndY + 8}
        fill="#666"
        textAnchor={textAnchor}
        dominantBaseline="central"
        fontSize={9}
      >
        {`${(percent * 100).toFixed(1)}%`}
      </text>
    </g>
  );
};

interface SpendByProviderWidgetProps {
  data: SpendAnalysisData | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const SpendByProviderWidget: React.FC<SpendByProviderWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  // Process data and truncate long provider names for better display
  const processedData = data?.spendByProvider
    ? data.spendByProvider.map((item) => ({
        ...item,
        name:
          item.name.length > 25
            ? `${item.name.substring(0, 25)}...`
            : item.name,
        fullName: item.name, // Keep full name for tooltip
      }))
    : [];

  const chartData = DashboardUtils.limitChartData(processedData, 6);
  const colors = DashboardUtils.generateChartColors(chartData.length);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">
            {data.payload.fullName || data.payload.name}
          </p>
          <p className="text-sm text-muted-foreground">
            Value: {DashboardUtils.formatCurrency(data.value)}
          </p>
          <p className="text-xs text-muted-foreground">
            {(
              (data.value /
                chartData.reduce((sum, item) => sum + item.value, 0)) *
              100
            ).toFixed(1)}
            % of total
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Contract Value by Provider"
      description="Top providers by total contract value"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={320}
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            outerRadius="65%"
            innerRadius="0%"
            fill="#8884d8"
            dataKey="value"
            label={renderCustomizedLabel}
            labelLine={false}
          >
            {chartData.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={colors[index % colors.length]}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

interface AgreementTypeWidgetProps {
  data: SpendAnalysisData | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const AgreementTypeWidget: React.FC<AgreementTypeWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const chartData = data?.spendByAgreementType || [];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-muted-foreground">
            Value: {DashboardUtils.formatCurrency(payload[0].value)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Agreement Types Distribution"
      description="Contract value by agreement type"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={320}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 20,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="name"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => DashboardUtils.formatLargeNumber(value)}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" fill="#10B981" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Confidence Distribution Widget
 */
interface ConfidenceDistributionWidgetProps {
  data: {
    high: { count: number; percentage: number };
    medium: { count: number; percentage: number };
    low: { count: number; percentage: number };
    total: number;
  } | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const ConfidenceDistributionWidget: React.FC<
  ConfidenceDistributionWidgetProps
> = ({ data, loading, error, onRefresh }) => {
  const chartData = data
    ? [
        {
          name: "High (>80%)",
          value: data.high.count,
          percentage: data.high.percentage,
          color: "#10B981",
        },
        {
          name: "Medium (50-80%)",
          value: data.medium.count,
          percentage: data.medium.percentage,
          color: "#F59E0B",
        },
        {
          name: "Low (<50%)",
          value: data.low.count,
          percentage: data.low.percentage,
          color: "#EF4444",
        },
      ]
    : [];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">{data.payload.name}</p>
          <p className="text-sm text-muted-foreground">
            Count: {data.value} ({data.payload.percentage.toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Extraction Confidence"
      description="Distribution of AI extraction confidence scores"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={320}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis dataKey="name" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

interface ContractStatusWidgetProps {
  data: any;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const ContractStatusWidget: React.FC<ContractStatusWidgetProps> = ({
  data: _data,
  loading,
  error,
  onRefresh,
}) => {
  // Mock data for contract status - this should come from the API
  const statusData = [
    { name: "Active", value: 45, color: "#10B981" },
    { name: "Inactive", value: 25, color: "#EF4444" },
    { name: "Expiring Soon", value: 15, color: "#F59E0B" },
    { name: "Unknown", value: 15, color: "#6B7280" },
  ];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">{data.payload.name}</p>
          <p className="text-sm text-muted-foreground">
            Count: {data.value} contracts
          </p>
          <p className="text-xs text-muted-foreground">
            {(
              (data.value /
                statusData.reduce((sum, item) => sum + item.value, 0)) *
              100
            ).toFixed(1)}
            % of total
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Contract Status Distribution"
      description="Current status of all contracts"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={320}
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={statusData}
            cx="50%"
            cy="50%"
            outerRadius="65%"
            innerRadius="0%"
            fill="#8884d8"
            dataKey="value"
            label={renderCustomizedLabel}
            labelLine={false}
          >
            {statusData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Active Contracts per Supplier Widget
 */
interface ActiveContractsPerSupplierWidgetProps {
  data: ActiveContractsPerSupplier[] | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const ActiveContractsPerSupplierWidget: React.FC<
  ActiveContractsPerSupplierWidgetProps
> = ({ data, loading, error, onRefresh }) => {
  // Process and limit data for better visualization
  const chartData = data
    ? DashboardUtils.limitChartData(
        data.map((item) => ({
          name:
            item.name.length > 20
              ? `${item.name.substring(0, 20)}...`
              : item.name,
          value: item.activeContracts,
          fullName: item.name,
        })),
        8
      )
    : [];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">
            {data.payload.fullName || data.payload.name}
          </p>
          <p className="text-sm text-muted-foreground">
            Active Contracts: {data.value}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Active Contracts per Supplier"
      description="Number of active contracts by supplier"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={320}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 20,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="name"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => value.toString()}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" fill="#10B981" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};
