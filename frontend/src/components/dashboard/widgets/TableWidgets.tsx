/**
 * Table Dashboard Widgets
 * Table components for displaying contract data
 */

import React from "react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { DashboardWidget } from "../DashboardWidget";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TopContract, DashboardUtils } from "@/services/dashboardService";

interface TopContractsWidgetProps {
  data: TopContract[] | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const TopContractsWidget: React.FC<TopContractsWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  return (
    <DashboardWidget
      title="Top Contracts by Value"
      description="Highest value contracts in your portfolio"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      className="lg:col-span-2"
    >
      <div className="space-y-4">
        {data && data.length > 0 ? (
          <>
            <div className="space-y-3">
              {data.slice(0, 8).map((contract, index) => {
                const status = DashboardUtils.getContractStatus(
                  contract.endDate
                );
                const statusVariant =
                  DashboardUtils.getStatusBadgeVariant(status);

                return (
                  <div
                    key={contract.id}
                    className="flex items-center justify-between border-b pb-3 last:border-0 last:pb-0"
                  >
                    <div className="flex items-center gap-4 flex-1 min-w-0">
                      <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-medium text-primary">
                          {index + 1}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="text-sm font-medium truncate">
                            {contract.provider}
                          </p>
                          <Badge variant="outline" className="text-xs">
                            {contract.agreementType}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground truncate">
                          {contract.product}
                        </p>
                        {contract.endDate && (
                          <p className="text-xs text-muted-foreground">
                            Expires:{" "}
                            {new Date(contract.endDate).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-3 flex-shrink-0">
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {DashboardUtils.formatCurrency(contract.totalAmount)}
                        </p>
                        <Badge variant={statusVariant} className="text-xs">
                          {status}
                        </Badge>
                      </div>
                      <Link
                        href={`/contract-management/contracts/view/${contract.contractId}`}
                      >
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                );
              })}
            </div>
            {data.length > 8 && (
              <div className="pt-3 border-t">
                <Link href="/contract-management/contracts">
                  <Button variant="outline" className="w-full">
                    View All Contracts ({data.length} total)
                  </Button>
                </Link>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground">No contracts found</p>
          </div>
        )}
      </div>
    </DashboardWidget>
  );
};

/**
 * Renewal Timeline Widget
 */
interface RenewalTimelineWidgetProps {
  data: { [key: string]: number } | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

export const RenewalTimelineWidget: React.FC<RenewalTimelineWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const timelineData = data
    ? [
        {
          period: "Next 30 days",
          count: data["30days"] || 0,
          color: "bg-red-500",
        },
        {
          period: "Next 60 days",
          count: data["60days"] || 0,
          color: "bg-orange-500",
        },
        {
          period: "Next 90 days",
          count: data["90days"] || 0,
          color: "bg-yellow-500",
        },
        {
          period: "Next 180 days",
          count: data["180days"] || 0,
          color: "bg-green-500",
        },
      ]
    : [];

  return (
    <DashboardWidget
      title="Renewal Timeline"
      description="Contracts expiring in upcoming periods"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
    >
      <div className="h-64 flex flex-col justify-center">
        <div className="space-y-4">
          {timelineData.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${item.color}`} />
                <span className="text-sm font-medium">{item.period}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-bold">{item.count}</span>
                <span className="text-xs text-muted-foreground">contracts</span>
              </div>
            </div>
          ))}

          {timelineData.length > 0 && (
            <div className="pt-3 border-t">
              <Link href="/contract-management/renewals">
                <Button variant="outline" size="sm" className="w-full">
                  View Renewal Calendar
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </DashboardWidget>
  );
};
