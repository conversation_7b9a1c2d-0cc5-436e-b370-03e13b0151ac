/**
 * Contract Details Modal Component
 * Modal for displaying contract details when clicking on chart elements
 */

import React from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ExternalLink, Calendar, DollarSign, FileText } from "lucide-react";
import { ContractDetail } from "@/services/dashboardService";
import { formatCurrency } from "@/lib/format-utils";
import { useRouter } from "next/navigation";

interface ContractDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  contracts: ContractDetail[];
  totalTCV?: number;
  currency?: string;
  totalCount?: number; // Total number of contracts (if showing subset)
}

export const ContractDetailsModal: React.FC<ContractDetailsModalProps> = ({
  open,
  onOpenChange,
  title,
  description,
  contracts,
  totalTCV,
  currency = "USD",
  totalCount,
}) => {
  const router = useRouter();

  const handleContractClick = (contractId: string) => {
    router.push(`/contract-management/contracts/analysis/${contractId}`);
    onOpenChange(false);
  };

  // Check if we're showing a subset of contracts
  const isShowingSubset = totalCount && totalCount > contracts.length;
  const displayedDescription = isShowingSubset
    ? `${description} (Showing ${contracts.length} of ${totalCount} contracts)`
    : description;

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            {title}
          </DialogTitle>
          {displayedDescription && (
            <p className="text-sm text-muted-foreground">{displayedDescription}</p>
          )}
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 gap-4 mb-4 flex-shrink-0">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Contracts
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-2xl font-bold">{contracts.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Value
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-2xl font-bold">
                  {totalTCV ? formatCurrency(totalTCV, currency) : "N/A"}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contract List - Scrollable */}
          <div className="flex-1 overflow-auto">
            <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
              {contracts.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No contracts found
                </div>
              ) : (
                contracts.map((contract) => (
                  <div
                    key={contract.contractId}
                    className="group bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg hover:border-green-300 transition-all duration-200 cursor-pointer"
                    onClick={() => handleContractClick(contract.contractId)}
                  >
                    {/* Header with contract name and ID */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900 truncate text-base">
                            {contract.contractName}
                          </h3>
                          <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors flex-shrink-0" />
                        </div>
                      </div>
                      <div className="ml-3 flex-shrink-0">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {contract.contractId}
                        </span>
                      </div>
                    </div>

                    {/* Contract details */}
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="font-medium">Expires:</span>
                        <span className="ml-1">
                          {contract.endDate
                            ? formatDate(contract.endDate)
                            : "Invalid Date"}
                        </span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <DollarSign className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="font-medium">Value:</span>
                        <span className="ml-1 font-semibold text-gray-900">
                          {contract.totalTCV
                            ? formatCurrency(contract.totalTCV, currency)
                            : "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
