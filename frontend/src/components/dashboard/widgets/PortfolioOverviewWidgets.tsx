/**
 * Portfolio Overview Widgets for Dashboard
 * Chart components for the 8 portfolio overview charts
 */

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  RadialBar<PERSON>hart,
  <PERSON>dialBar,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { ChartWidget } from "../DashboardWidget";
import { PortfolioOverviewData, DashboardService } from "@/services/dashboardService";
import { ContractDetailsModal } from "./ContractDetailsModal";

// Color palette for charts
const COLORS = {
  primary: "#10B981", // Green
  secondary: "#3B82F6", // Blue
  warning: "#F59E0B", // Amber
  danger: "#EF4444", // Red
  purple: "#8B5CF6", // Purple
  gray: "#6B7280", // Gray
};

const PIE_COLORS = ["#10B981", "#3B82F6", "#8B5CF6", "#F59E0B", "#EF4444", "#6B7280"];

// Custom Legend Component
interface CustomLegendProps {
  payload?: any[];
  className?: string;
  maxItems?: number;
}

const CustomLegend: React.FC<CustomLegendProps> = ({ payload, className = "", maxItems = 6 }) => {
  if (!payload || payload.length === 0) return null;

  const visibleItems = payload.slice(0, maxItems);
  const remainingCount = payload.length - maxItems;

  return (
    <div className={`flex flex-wrap justify-center gap-4 mt-2 h-12 items-center ${className}`}>
      {visibleItems.map((entry, index) => (
        <div key={index} className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-sm"
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-xs font-medium text-foreground">
            {entry.value}
          </span>
        </div>
      ))}
      {remainingCount > 0 && (
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm bg-gray-400" />
          <span className="text-xs font-medium text-muted-foreground">
            +{remainingCount} more
          </span>
        </div>
      )}
    </div>
  );
};



interface PortfolioOverviewWidgetProps {
  data: PortfolioOverviewData | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

/**
 * Chart 1: Total Agreements - Radial Chart
 */
export const TotalAgreementsWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  const chartData = data ? [
    { name: "Inactive", value: data.totalAgreements.inactive.count, totalTCV: data.totalAgreements.inactive.totalTCV, fill: COLORS.gray },
    { name: "Active", value: data.totalAgreements.active.count, totalTCV: data.totalAgreements.active.totalTCV, fill: COLORS.primary },
  ] : [];

  const handleSegmentClick = async (_data: any, index: number) => {
    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real contracts from API
        const status = segment.name.toLowerCase() as 'active' | 'inactive';
        const contracts = await DashboardService.getContractsByStatus(status, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.value, // Store the actual total count
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.value,
        });
        setModalOpen(true);
      }
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  // Custom label function for radial chart with external positioning
  const renderCustomLabel = (props: any) => {
    const { cx, cy, midAngle, outerRadius, name, value } = props;
    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 25) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 25) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the radial chart)
    const labelRadius = outerRadius + 40;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);

    const textAnchor = labelX > cx ? 'start' : 'end';

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {value}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Total Agreements"
        description="Active vs Inactive contracts"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={360}
      >
        <ResponsiveContainer width="100%" height='100%'>
          <RadialBarChart
            cx="50%"
            cy="50%"
            innerRadius="40%"
            outerRadius="80%"
            data={chartData}
            startAngle={90}
            endAngle={360}
          >
            <RadialBar
              dataKey="value"
              cornerRadius={4}
              fill="#8884d8"
              background={{ fill: '#f3f4f6' }}
              onClick={handleSegmentClick}
              style={{ cursor: 'pointer' }}
              label={{ position: 'insideStart', fill: '#000', clockWise: true, angle: 0, offset: -10, formatter: (value: number) => `${value}`, }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
          </RadialBarChart>
        </ResponsiveContainer>
      </ChartWidget>

      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`${selectedSegment.name} Agreements`}
          description={`${selectedSegment.count} ${selectedSegment.name.toLowerCase()} contracts`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </>
  );
};

/**
 * Chart 2: High Value Agreements - Radial Chart (Dummy Data)
 */
export const HighValueAgreementsWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data: _data,
  loading,
  error,
  onRefresh,
}) => {
  // Use dummy data for chart 2 - ordered from largest to smallest for innermost to outermost
  const chartData = [
    { name: "Low", value: 25, totalTCV: 800000, fill: COLORS.primary },
    { name: "Medium", value: 15, totalTCV: 1200000, fill: COLORS.warning },
    { name: "High", value: 8, totalTCV: 2500000, fill: COLORS.danger },
  ];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name} Value</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  // Custom label function for radial chart with external positioning
  const renderCustomLabel = (props: any) => {
    const { cx, cy, midAngle, outerRadius, name, value } = props;
    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 25) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 25) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the radial chart)
    const labelRadius = outerRadius + 40;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);

    const textAnchor = labelX > cx ? 'start' : 'end';

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {value}
        </text>
      </g>
    );
  };

  return (
    <ChartWidget
      title="High Value Agreements"
      description="Top 10% of your contract portfolio"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={360}
    >
      <ResponsiveContainer width="100%" height='100%'>
        <RadialBarChart
          cx="50%"
          cy="50%"
          innerRadius="20%"
          outerRadius="80%"
          data={chartData}
          startAngle={90}
          endAngle={360}
        >
          <RadialBar
            dataKey="value"
            cornerRadius={4}
            background={{ fill: '#f3f4f6' }}
            label={{ position: 'insideStart', fill: '#000', clockWise: true, angle: 0, offset: -10, formatter: (value: number) => `${value}`, }}
            fill="#8884d8" />
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </RadialBarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Chart 3: Critical Agreements by Service Type - Pie Chart
 */
export const CriticalAgreementsWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  // Define colors for different service types based on contract_classification
  const getServiceTypeColor = (serviceType: string) => {
    // Normalize the service type name for matching
    const normalizedType = serviceType.toUpperCase().replace(/\s+/g, '_');

    switch (normalizedType) {
      case "SW_SAAS":
      case "SW/SAAS":
        return "#3b82f6"; // Blue
      case "PROFESSIONAL_SERVICES":
      case "PROFESSIONAL SERVICES":
        return "#10b981"; // Green
      case "MANAGED_SERVICES":
      case "MANAGED SERVICES":
        return "#8b5cf6"; // Purple
      case "OTHER":
        return "#6b7280"; // Gray
      default:
        return "#f59e0b"; // Orange for unknown types
    }
  };

  const chartData = data ? data.criticalAgreements.map((item) => ({
    name: item.name,
    value: item.count,
    totalTCV: item.totalTCV,
    color: getServiceTypeColor(item.name)
  })) : [];

  const handlePieClick = async (_data: any, index: number) => {
    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real critical contracts from API by service type
        const contracts = await DashboardService.getCriticalContractsByServiceType(segment.name, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.value,
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching critical contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.value,
        });
        setModalOpen(true);
      }
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);

    const textAnchor = labelX > cx ? 'start' : 'end';

    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Critical Agreements"
        description="Critical contracts (>$500k) by service type"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={360}
      >
        <ResponsiveContainer width="100%" height='100%'>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius="70%"
              innerRadius="0%"
              fill="#8884d8"
              dataKey="value"
              label={renderCustomizedLabel}
              labelLine={false}
              onClick={handlePieClick}
              style={{ cursor: 'pointer' }}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartWidget>

      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`Critical ${selectedSegment.name} Agreements`}
          description={`${selectedSegment.count} critical contracts (>$500k) of service type ${selectedSegment.name}`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </>
  );
};

/**
 * Chart 4: Expired Agreements by Service Type - Pie Chart
 */
export const ExpiredAgreementsWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  // Define colors for different service types based on contract_classification
  const getServiceTypeColor = (serviceType: string) => {
    // Normalize the service type name for matching
    const normalizedType = serviceType.toUpperCase().replace(/\s+/g, '_');

    switch (normalizedType) {
      case "SW_SAAS":
      case "SW/SAAS":
        return "#3b82f6"; // Blue
      case "PROFESSIONAL_SERVICES":
      case "PROFESSIONAL SERVICES":
        return "#10b981"; // Green
      case "MANAGED_SERVICES":
      case "MANAGED SERVICES":
        return "#8b5cf6"; // Purple
      case "OTHER":
        return "#6b7280"; // Gray
      default:
        return "#f59e0b"; // Orange for unknown types
    }
  };

  const chartData = data ? data.expiredAgreements.map((item, index) => ({
    name: item.name,
    value: item.count,
    totalTCV: item.totalTCV,
    color: getServiceTypeColor(item.name)
  })) : [];

  const handlePieClick = async (_data: any, index: number) => {
    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real expired contracts from API by service type
        const contracts = await DashboardService.getExpiredContractsByServiceType(segment.name, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.value,
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching expired contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.value,
        });
        setModalOpen(true);
      }
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const textAnchor = labelX > cx ? 'start' : 'end';

    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Expired Agreements"
        description="Expired contracts by service type"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={360}
      >
        <ResponsiveContainer width="100%" height='100%'>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius="70%"
              innerRadius="0%"
              fill="#8884d8"
              dataKey="value"
              label={renderCustomizedLabel}
              labelLine={false}
              onClick={handlePieClick}
              style={{ cursor: 'pointer' }}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartWidget>

      {/* Contract Details Modal */}
      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`Expired ${selectedSegment.name} Agreements`}
          description={`${selectedSegment.count} expired contracts of service type ${selectedSegment.name}`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </>
  );
};

/**
 * Chart 5: Aging Contracts - Three Categories (>4Y, 3-4Y, <3Y)
      */
export const AgingContractsWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  const totalContracts = data ?
    data.agingContracts.olderThan4Y.count +
    data.agingContracts.approaching4Y.count : 0;

  const olderThan4YCount = data ? data.agingContracts.olderThan4Y.count : 0;
  const approaching4YCount = data ? data.agingContracts.approaching4Y.count : 0;

  const olderThan4YPercentage = totalContracts > 0 ? (olderThan4YCount / totalContracts) * 100 : 0;
  const approaching4YPercentage = totalContracts > 0 ? (approaching4YCount / totalContracts) * 100 : 0;

  const handlePieClick = async (_data: any, index: number) => {
    const chartData = [
      { name: "Outdated Agreements", value: olderThan4YPercentage, count: olderThan4YCount, totalTCV: data?.agingContracts.olderThan4Y.totalTCV || 0, agingCategory: 'olderThan4Y' },
      { name: "Approaching 4Y", value: approaching4YPercentage, count: approaching4YCount, totalTCV: data?.agingContracts.approaching4Y.totalTCV || 0, agingCategory: 'approaching4Y' }
    ];

    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real contracts from API
        const agingCategory = segment.agingCategory as 'olderThan4Y' | 'approaching4Y';
        const contracts = await DashboardService.getContractsByAging(agingCategory, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.count,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.count,
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.count,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.count,
        });
        setModalOpen(true);
      }
    }
  };



  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      let count = 0;

      if (data.name === "Outdated Agreements") {
        count = olderThan4YCount;
      } else if (data.name === "Approaching 4Y") {
        count = approaching4YCount;
      }

      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{count}</span></p>
          <p className="text-sm">Percentage: <span className="font-medium">{data.value.toFixed(1)}%</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices < 5%

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const textAnchor = labelX > cx ? 'start' : 'end';

    // Get count based on the segment name
    let count = 0;
    if (payload.name === "Outdated Agreements") {
      count = olderThan4YCount;
    } else if (payload.name === "Approaching 4Y") {
      count = approaching4YCount;
    }

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {count}
        </text>
      </g>
    );
  };

  return (
    <ChartWidget
      title="Contract Aging"
      description="Agreements older than 4 years"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={360}
    >
      <ResponsiveContainer width="100%" height='100%'>
        <PieChart>
          <Pie
            data={[
              { name: "Outdated Agreements", value: olderThan4YPercentage, fill: "#dc2626" }, // Red for outdated
              { name: "Approaching 4Y", value: approaching4YPercentage, fill: "#f59e0b" } // Orange for approaching
            ]}
            dataKey="value"
            cx="50%"
            cy="50%"
            innerRadius="55%"
            outerRadius="70%"
            startAngle={0}
            endAngle={360}
            stroke="none"
            onClick={handlePieClick}
            style={{ cursor: 'pointer' }}
            label={renderCustomizedLabel}
            labelLine={false}
          />
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>

      {/* Contract Details Modal */}
      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`${selectedSegment.name} Agreements`}
          description={`${selectedSegment.count} contracts in ${selectedSegment.name.toLowerCase()} category`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </ChartWidget>
  );
};

/**
 * Chart 6: Customer vs Supplier Paper - Pie Chart (Dummy Data)
 */
export const CustomerSupplierPaperWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data: _data,
  loading,
  error,
  onRefresh,
}) => {
  // Use dummy data for chart 6
  const chartData = [
    { name: "Customer", value: 20, totalTCV: 1500000, color: COLORS.secondary },
    { name: "Supplier", value: 28, totalTCV: 2200000, color: COLORS.primary },
  ];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name} Paper</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const textAnchor = labelX > cx ? 'start' : 'end';

    // Format TCV value
    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <ChartWidget
      title="Customer Vs. Supplier Paper"
      description="Contract origination breakdown"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={360}
    >
      <ResponsiveContainer width="100%" height='100%'>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            outerRadius="70%"
            innerRadius="0%"
            fill="#8884d8"
            dataKey="value"
            label={renderCustomizedLabel}
            labelLine={false}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Chart 7: Agreement Type - Pie Chart
 */
export const AgreementTypeWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  const chartData = data?.agreementType.map((item, index) => ({
    name: item.name,
    value: item.count,
    totalTCV: item.totalTCV,
    color: PIE_COLORS[index % PIE_COLORS.length],
  })) || [];

  const handlePieClick = async (_data: any, index: number) => {
    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real contracts from API
        const contracts = await DashboardService.getContractsByType(segment.name, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.value,
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.value,
        });
        setModalOpen(true);
      }
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const textAnchor = labelX > cx ? 'start' : 'end';

    // Format TCV value
    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Agreement Type"
        description="Contracts by agreement type"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={360}
      >
        <ResponsiveContainer width="100%" height='100%'>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius="70%"
              innerRadius="0%"
              fill="#8884d8"
              dataKey="value"
              label={renderCustomizedLabel}
              labelLine={false}
              onClick={handlePieClick}
              style={{ cursor: 'pointer' }}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartWidget>

      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`${selectedSegment.name} Agreements`}
          description={`${selectedSegment.count} contracts of type ${selectedSegment.name}`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </>
  );
};

/**
 * Chart 8: Service Type - Pie Chart
 */
export const ServiceTypeWidget: React.FC<PortfolioOverviewWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<{
    name: string;
    count: number;
    totalTCV: number;
    contracts: any[];
    totalCount: number;
  } | null>(null);

  const chartData = data?.serviceType.map((item, index) => ({
    name: item.name,
    value: item.count,
    totalTCV: item.totalTCV,
    color: PIE_COLORS[index % PIE_COLORS.length],
  })) || [];

  const handlePieClick = async (_data: any, index: number) => {
    const segment = chartData[index];
    if (segment) {
      try {
        // Fetch real contracts from API using service type
        const contracts = await DashboardService.getContractsByServiceType(segment.name, 10);

        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: contracts,
          totalCount: segment.value,
        });
        setModalOpen(true);
      } catch (error) {
        console.error('Error fetching contracts:', error);
        // Fallback to showing modal with empty contracts
        setSelectedSegment({
          name: segment.name,
          count: segment.value,
          totalTCV: segment.totalTCV,
          contracts: [],
          totalCount: segment.value,
        });
        setModalOpen(true);
      }
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">Count: <span className="font-medium">{data.value}</span></p>
          <p className="text-sm">TCV: <span className="font-medium">${data.totalTCV.toLocaleString()}</span></p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const textAnchor = labelX > cx ? 'start' : 'end';

    // Format TCV value
    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Service Type"
        description="Contracts by service type"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={360}
      >
        <ResponsiveContainer width="100%" height='100%'>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius="70%"
              innerRadius="0%"
              fill="#8884d8"
              dataKey="value"
              label={renderCustomizedLabel}
              labelLine={false}
              onClick={handlePieClick}
              style={{ cursor: 'pointer' }}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartWidget>

      {selectedSegment && (
        <ContractDetailsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title={`${selectedSegment.name} Service Contracts`}
          description={`${selectedSegment.count} contracts of service type ${selectedSegment.name}`}
          contracts={selectedSegment.contracts}
          totalTCV={selectedSegment.totalTCV}
          currency="USD"
          totalCount={selectedSegment.totalCount}
        />
      )}
    </>
  );
};
