/**
 * Action Chart Widgets for Dashboard
 * Chart components for the Actions section of the redesigned dashboard
 */

import React, { useState } from "react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  RadialBar<PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";
import { ChartWidget } from "../DashboardWidget";
import {
  ExpiringContractsData,
  AutoRenewalsData,
  AutoRenewalsByClassification,
  PortfolioOverviewData,
  ContractDetail,
} from "@/services/dashboardService";
import { ContractDetailsModal } from "./ContractDetailsModal";

// Color palette for charts
const COLORS = {
  primary: "#10B981", // Green
  secondary: "#3B82F6", // Blue
  warning: "#F59E0B", // Amber
  danger: "#EF4444", // Red
  safe: "#10B981", // Green
  notSafe: "#EF4444", // Red with stripes
};

// Custom Legend Component
interface CustomLegendProps {
  payload?: any[];
  className?: string;
  maxItems?: number;
}

const CustomLegend: React.FC<CustomLegendProps> = ({ payload, className = "", maxItems = 6 }) => {
  if (!payload || payload.length === 0) return null;

  const visibleItems = payload.slice(0, maxItems);
  const remainingCount = payload.length - maxItems;

  return (
    <div className={`flex flex-wrap justify-center gap-6 mt-3 min-h-[48px] items-center ${className}`}>
      {visibleItems.map((entry, index) => (
        <div key={index} className="flex items-center gap-2.5">
          <div
            className="w-4 h-4 rounded-sm shadow-sm border border-gray-200"
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-sm font-medium text-foreground whitespace-nowrap">
            {entry.value}
          </span>
        </div>
      ))}
      {remainingCount > 0 && (
        <div className="flex items-center gap-2.5">
          <div className="w-4 h-4 rounded-sm bg-gray-400 shadow-sm border border-gray-200" />
          <span className="text-sm font-medium text-muted-foreground">
            +{remainingCount} more
          </span>
        </div>
      )}
    </div>
  );
};

// Enhanced Auto-Renewals Legend Component
interface AutoRenewalsLegendProps {
  className?: string;
  safeTotal?: number;
  notSafeTotal?: number;
  safeCount?: number;
  notSafeCount?: number;
}

const AutoRenewalsLegend: React.FC<AutoRenewalsLegendProps> = ({
  className = "",
  safeTotal = 0,
  notSafeTotal = 0,
  safeCount = 0,
  notSafeCount = 0
}) => {

  return (
    <div className={`flex justify-center gap-8 mt-3 min-h-[48px] items-center ${className}`}>
      <div className="flex items-center gap-3">
        <div className="w-4 h-4 rounded-sm shadow-sm border border-green-300"
          style={{ backgroundColor: '#10B981' }}
        />
        <span className="text-sm font-medium text-foreground">Safe</span>
      </div>
      <div className="flex items-center gap-3">
        <div
          className="w-4 h-4 rounded-sm shadow-sm border border-red-300"
          style={{ backgroundColor: '#EF4444' }}
        />
        <span className="text-sm font-medium text-foreground">Action Needed</span>
      </div>
      <div className="flex items-center gap-3">
        <div
          className="w-4 h-4 rounded-sm shadow-sm border border-orange-300"
          style={{ backgroundColor: '#F59E0B' }}
        />
        <span className="text-sm font-medium text-foreground">Critical</span>
      </div>
    </div>
  );
};

// Enhanced color palette for pie charts with better contrast
const PIE_COLORS = [
  "#10B981", // Green
  "#3B82F6", // Blue
  "#8B5CF6", // Purple
  "#F59E0B", // Amber
  "#EF4444", // Red
  "#06B6D4", // Cyan
  "#EC4899", // Pink
  "#14B8A6", // Teal
  "#F97316", // Orange
  "#6366F1"  // Indigo
];

interface ExpiringContractsWidgetProps {
  data: ExpiringContractsData[] | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

interface AutoRenewalsWidgetProps {
  data: AutoRenewalsData[] | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

interface AutoRenewalsByClassificationWidgetProps {
  data: AutoRenewalsByClassification[] | null;
  loading: boolean;
  error: string | undefined;
  onRefresh: () => void;
}

/**
 * Expiring Contracts Chart Widget
 * Horizontal bar chart showing contracts expiring in 30/60/90 day periods
 */
export const ExpiringContractsWidget: React.FC<ExpiringContractsWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<{
    period: string;
    type: 'total' | 'critical';
    contracts: any[];
    totalTCV: number;
    currency: string;
    count: number;
  } | null>(null);

  // Transform data for horizontal bar chart
  let chartData = data?.map((item) => ({
    period: item.period,
    count: item.count,
    totalTCV: item.totalTCV,
    contracts: item.contracts,
    currency: item.currency,
    // Format TCV for display
    tcvFormatted: `$${(item.totalTCV / 1000).toFixed(0)}K`,
    // Add dummy critical contracts data
    criticalCount: Math.floor(item.count * 0.3), // 30% of total contracts are critical
    criticalTCV: Math.floor(item.totalTCV * 0.4), // 40% of total TCV is critical
    criticalContracts: item.contracts.slice(0, Math.floor(item.count * 0.3)).map(contract => ({
      ...contract,
      contractName: `Critical ${contract.contractName}`,
      totalTCV: Math.floor(contract.totalTCV * 0.4)
    })),
  })) || [];



  // Add test data if no real data or all values are zero
  if ((chartData.length === 0 || chartData.every(item => item.totalTCV === 0)) && !loading) {
    chartData = [
      {
        period: "30 DAYS",
        count: 5,
        totalTCV: 150000,
        tcvFormatted: "$150K",
        contracts: [
          { contractId: "C001", contractName: "Test Contract 1", endDate: "2024-02-15", totalTCV: 75000 },
          { contractId: "C002", contractName: "Test Contract 2", endDate: "2024-02-20", totalTCV: 75000 }
        ],
        currency: "USD",
        // Critical contracts dummy data
        criticalCount: 2,
        criticalTCV: 60000,
        criticalContracts: [
          { contractId: "C001C", contractName: "Critical Test Contract 1", endDate: "2024-02-15", totalTCV: 30000 },
          { contractId: "C002C", contractName: "Critical Test Contract 2", endDate: "2024-02-20", totalTCV: 30000 }
        ]
      },
      {
        period: "60 DAYS",
        count: 3,
        totalTCV: 75000,
        tcvFormatted: "$75K",
        contracts: [
          { contractId: "C003", contractName: "Test Contract 3", endDate: "2024-03-15", totalTCV: 75000 }
        ],
        currency: "USD",
        // Critical contracts dummy data
        criticalCount: 1,
        criticalTCV: 30000,
        criticalContracts: [
          { contractId: "C003C", contractName: "Critical Test Contract 3", endDate: "2024-03-15", totalTCV: 30000 }
        ]
      },
      {
        period: "90 DAYS",
        count: 2,
        totalTCV: 50000,
        tcvFormatted: "$50K",
        contracts: [
          { contractId: "C004", contractName: "Test Contract 4", endDate: "2024-04-15", totalTCV: 50000 }
        ],
        currency: "USD",
        // Critical contracts dummy data
        criticalCount: 1,
        criticalTCV: 20000,
        criticalContracts: [
          { contractId: "C004C", contractName: "Critical Test Contract 4", endDate: "2024-04-15", totalTCV: 20000 }
        ]
      },
    ];
    console.log("ExpiringContractsWidget - Using test data:", chartData);
  }

  const handleBarClick = (data: any, dataKey: string) => {
    const periodData = chartData.find(item => item.period === data.period);
    if (periodData) {
      const type = dataKey === 'totalTCV' ? 'total' : 'critical';
      setSelectedPeriod({
        period: data.period,
        type,
        contracts: type === 'total' ? periodData.contracts : periodData.criticalContracts,
        totalTCV: type === 'total' ? periodData.totalTCV : periodData.criticalTCV,
        currency: periodData.currency,
        count: type === 'total' ? periodData.count : periodData.criticalCount,
      });
      setModalOpen(true);
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          <div className="space-y-1">
            <p className="text-sm flex items-center gap-2">
              <span className="w-3 h-3 bg-green-500 rounded"></span>
              Total: <span className="font-medium">{data.count}</span> contracts
              (${Math.round(data.totalTCV).toLocaleString()})
            </p>
            <p className="text-sm flex items-center gap-2">
              <span className="w-3 h-3 bg-orange-500 rounded"></span>
              Critical: <span className="font-medium">{data.criticalCount}</span> contracts
              (${Math.round(data.criticalTCV).toLocaleString()})
            </p>
          </div>
        </div>
      );
    }
    return null;
  };



  return (
    <>
      <ChartWidget
        title="Expiring Contracts"
        description="Contracts expiring in upcoming periods"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={420}
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            layout="vertical"
            margin={{ left: -50 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              type="number"
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
            />
            <YAxis
              dataKey="period"
              type="category"
              tick={{ fontSize: 12 }}
              width={100}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend
              content={() => (
                <CustomLegend
                  payload={[
                    { value: "Total Contract Value", color: COLORS.primary },
                    { value: "Critical Contract Value", color: COLORS.warning }
                  ]}
                />
              )}
            />
            <Bar
              dataKey="totalTCV"
              fill={COLORS.primary}
              radius={[0, 4, 4, 0]}
              onClick={(data) => handleBarClick(data, 'totalTCV')}
              style={{ cursor: 'pointer' }}
            />
            <Bar
              dataKey="criticalTCV"
              fill={COLORS.warning}
              radius={[0, 4, 4, 0]}
              onClick={(data) => handleBarClick(data, 'criticalTCV')}
              style={{ cursor: 'pointer' }}
            />
          </BarChart>
        </ResponsiveContainer>
      </ChartWidget>

      <ContractDetailsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        title={`${selectedPeriod?.type === 'critical' ? 'Critical ' : ''}Contracts Expiring in ${selectedPeriod?.period || ''}`}
        description={`${selectedPeriod?.count || 0} ${selectedPeriod?.type === 'critical' ? 'critical ' : ''}contracts expiring in this period`}
        contracts={selectedPeriod?.contracts || []}
        totalTCV={selectedPeriod?.totalTCV}
        currency={selectedPeriod?.currency}
      />
    </>
  );
};

/**
 * Auto-renewals Chart Widget
 * Horizontal stacked bar chart showing safe vs not safe auto-renewals
 */
export const AutoRenewalsWidget: React.FC<AutoRenewalsWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedData, setSelectedData] = useState<{
    period: string;
    type: 'safe' | 'notSafe' | 'critical';
    contracts: ContractDetail[];
    totalTCV: number;
    currency: string;
  } | null>(null);

  // Transform data for stacked bar chart
  let chartData = data?.map((item) => ({
    period: item.period,
    safe: item.safe.totalTCV,
    notSafe: item.notSafe.totalTCV,
    safeCount: item.safe.count,
    notSafeCount: item.notSafe.count,
    safeContracts: item.safe.contracts,
    notSafeContracts: item.notSafe.contracts,
    currency: item.currency,
    // Add critical contract data (independent of safe/notSafe classification)
    critical: Math.floor((item.safe.totalTCV + item.notSafe.totalTCV) * 0.25), // 25% of total contracts are critical
    criticalCount: Math.floor((item.safe.count + item.notSafe.count) * 0.25),
    criticalContracts: [...item.safe.contracts, ...item.notSafe.contracts]
      .slice(0, Math.floor((item.safe.count + item.notSafe.count) * 0.25))
      .map(contract => ({
        ...contract,
        contractName: `Critical ${contract.contractName}`,
        totalTCV: Math.floor(contract.totalTCV * 0.25)
      })),
  })) || [];

  // Debug logging
  console.log("AutoRenewalsWidget - Raw data:", data);
  console.log("AutoRenewalsWidget - Chart data:", chartData);

  // Add test data if no real data or all values are zero
  if ((chartData.length === 0 || chartData.every(item => item.safe === 0 && item.notSafe === 0)) && !loading) {
    chartData = [
      {
        period: "30 DAYS",
        safe: 80000,
        notSafe: 20000,
        safeCount: 4,
        notSafeCount: 1,
        safeContracts: [
          { contractId: "C005", contractName: "Safe Contract 1", endDate: "2024-02-15", totalTCV: 40000 },
          { contractId: "C006", contractName: "Safe Contract 2", endDate: "2024-02-20", totalTCV: 40000 }
        ],
        notSafeContracts: [
          { contractId: "C007", contractName: "Non Safe Contract 1", endDate: "2024-02-25", totalTCV: 20000 }
        ],
        currency: "USD",
        // Critical contract data (independent category)
        critical: 25000,
        criticalCount: 2,
        criticalContracts: [
          { contractId: "C005C", contractName: "Critical Contract 1", endDate: "2024-02-15", totalTCV: 15000 },
          { contractId: "C007C", contractName: "Critical Contract 2", endDate: "2024-02-25", totalTCV: 10000 }
        ]
      },
      {
        period: "60 DAYS",
        safe: 60000,
        notSafe: 40000,
        safeCount: 3,
        notSafeCount: 2,
        safeContracts: [
          { contractId: "C008", contractName: "Safe Contract 3", endDate: "2024-03-15", totalTCV: 60000 }
        ],
        notSafeContracts: [
          { contractId: "C009", contractName: "Non Safe Contract 2", endDate: "2024-03-20", totalTCV: 40000 }
        ],
        currency: "USD",
        // Critical contract data (independent category)
        critical: 20000,
        criticalCount: 2,
        criticalContracts: [
          { contractId: "C008C", contractName: "Critical Contract 3", endDate: "2024-03-15", totalTCV: 12000 },
          { contractId: "C009C", contractName: "Critical Contract 4", endDate: "2024-03-20", totalTCV: 8000 }
        ]
      },
      {
        period: "90 DAYS",
        safe: 30000,
        notSafe: 10000,
        safeCount: 2,
        notSafeCount: 1,
        safeContracts: [
          { contractId: "C010", contractName: "Safe Contract 4", endDate: "2024-04-15", totalTCV: 30000 }
        ],
        notSafeContracts: [
          { contractId: "C011", contractName: "Non Safe Contract 3", endDate: "2024-04-20", totalTCV: 10000 }
        ],
        currency: "USD",
        // Critical contract data (independent category)
        critical: 10000,
        criticalCount: 1,
        criticalContracts: [
          { contractId: "C010C", contractName: "Critical Contract 5", endDate: "2024-04-15", totalTCV: 10000 }
        ]
      },
    ];
    console.log("AutoRenewalsWidget - Using test data:", chartData);
  }

  const handleBarClick = (data: any, dataKey: string) => {
    const periodData = chartData.find(item => item.period === data.period);
    if (periodData) {
      let type: 'safe' | 'notSafe' | 'critical';
      let contracts: any[];
      let totalTCV: number;

      switch (dataKey) {
        case 'safe':
          type = 'safe';
          contracts = periodData.safeContracts;
          totalTCV = periodData.safe;
          break;
        case 'notSafe':
          type = 'notSafe';
          contracts = periodData.notSafeContracts;
          totalTCV = periodData.notSafe;
          break;
        case 'critical':
          type = 'critical';
          contracts = periodData.criticalContracts;
          totalTCV = periodData.critical;
          break;
        default:
          return;
      }

      setSelectedData({
        period: data.period,
        type,
        contracts,
        totalTCV,
        currency: periodData.currency,
      });
      setModalOpen(true);
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{label}</p>
          <div className="space-y-1.5">
            <div className="text-sm flex items-center gap-2">
              <span className="w-3 h-3 bg-green-500 rounded flex-shrink-0"></span>
              <span>Safe: <span className="font-medium">{data.safeCount}</span> contracts (${Math.round(data.safe).toLocaleString()})</span>
            </div>
            <div className="text-sm flex items-center gap-2">
              <span className="w-3 h-3 bg-red-500 rounded flex-shrink-0"></span>
              <span>Action Needed: <span className="font-medium">{data.notSafeCount}</span> contracts (${Math.round(data.notSafe).toLocaleString()})</span>
            </div>
            <div className="text-sm flex items-center gap-2">
              <span className="w-3 h-3 bg-orange-500 rounded flex-shrink-0"></span>
              <span>Critical: <span className="font-medium">{data.criticalCount}</span> contracts (${Math.round(data.critical).toLocaleString()})</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <>
      <ChartWidget
        title="Auto Renewals"
        description="Safe vs Action Needed auto-renewals by period"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={420}
      >
        <div className="space-y-3">
          <ResponsiveContainer width="100%" height={360}
          >
            <BarChart
              data={chartData}
              layout="vertical"
              margin={{ left: -50 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
              />
              <YAxis
                dataKey="period"
                type="category"
                tick={{ fontSize: 12 }}
                width={100}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="safe"
                stackId="a"
                fill={COLORS.safe}
                onClick={(data) => handleBarClick(data, 'safe')}
                style={{ cursor: 'pointer' }}
              />
              <Bar
                dataKey="notSafe"
                stackId="a"
                fill={COLORS.notSafe}
                onClick={(data) => handleBarClick(data, 'notSafe')}
                style={{ cursor: 'pointer' }}
              />
              <Bar
                dataKey="critical"
                stackId="b"
                fill={COLORS.warning}
                onClick={(data) => handleBarClick(data, 'critical')}
                style={{ cursor: 'pointer' }}
              />
            </BarChart>

          </ResponsiveContainer>
          {/* Enhanced Legend with TCV and count values */}
          <AutoRenewalsLegend
            safeTotal={chartData.reduce((sum, item) => sum + item.safe, 0)}
            notSafeTotal={chartData.reduce((sum, item) => sum + item.notSafe, 0)}
            safeCount={chartData.reduce((sum, item) => sum + item.safeCount, 0)}
            notSafeCount={chartData.reduce((sum, item) => sum + item.notSafeCount, 0)}
          />
        </div>
      </ChartWidget>

      <ContractDetailsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        title={`${selectedData?.type === 'safe' ? 'Safe' :
          selectedData?.type === 'notSafe' ? 'Action Needed' :
            selectedData?.type === 'critical' ? 'Critical' : ''
          } Auto-renewals - ${selectedData?.period || ''}`}
        description={`${selectedData?.contracts.length || 0} ${selectedData?.type === 'safe' ? 'safe' :
          selectedData?.type === 'notSafe' ? 'action needed' :
            selectedData?.type === 'critical' ? 'critical' : ''
          } auto-renewal contracts in this period`}
        contracts={selectedData?.contracts || []}
        totalTCV={selectedData?.totalTCV}
        currency={selectedData?.currency}
      />
    </>
  );
};

/**
 * Auto-renewals by Classification Pie Chart Widget
 */
export const AutoRenewalsByClassificationWidget: React.FC<AutoRenewalsByClassificationWidgetProps> = ({
  data,
  loading,
  error,
  onRefresh,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedClassification, setSelectedClassification] = useState<AutoRenewalsByClassification | null>(null);

  let chartData = data?.map((item, index) => ({
    name: item.name,
    value: item.count,
    totalTCV: item.totalTCV,
    contracts: item.contracts,
    currency: item.currency,
    color: PIE_COLORS[index % PIE_COLORS.length],
  })) || [];

  // Add test data if no real data
  if (chartData.length === 0 && !loading) {
    chartData = [
      {
        name: "MSA",
        value: 5,
        totalTCV: 200000,
        color: PIE_COLORS[0],
        contracts: [
          { contractId: "C012", contractName: "MSA Contract 1", endDate: "2024-02-15", totalTCV: 100000 },
          { contractId: "C013", contractName: "MSA Contract 2", endDate: "2024-03-20", totalTCV: 100000 }
        ],
        currency: "USD"
      },
      {
        name: "SOW",
        value: 3,
        totalTCV: 150000,
        color: PIE_COLORS[1],
        contracts: [
          { contractId: "C014", contractName: "SOW Contract 1", endDate: "2024-02-25", totalTCV: 75000 },
          { contractId: "C015", contractName: "SOW Contract 2", endDate: "2024-04-15", totalTCV: 75000 }
        ],
        currency: "USD"
      },
      {
        name: "ORDER",
        value: 2,
        totalTCV: 75000,
        color: PIE_COLORS[2],
        contracts: [
          { contractId: "C016", contractName: "ORDER Contract 1", endDate: "2024-03-10", totalTCV: 75000 }
        ],
        currency: "USD"
      },
    ];
    console.log("AutoRenewalsByClassificationWidget - Using test data:", chartData);
  }

  const handlePieClick = (data: any, index: number) => {
    const clickedItem = chartData[index];
    if (clickedItem) {
      setSelectedClassification({
        name: clickedItem.name,
        count: clickedItem.value,
        totalTCV: clickedItem.totalTCV,
        contracts: clickedItem.contracts,
        currency: clickedItem.currency
      });
      setModalOpen(true);
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">
            Contracts: <span className="font-medium">{data.value}</span>
          </p>
          <p className="text-sm">
            TCV: <span className="font-medium">${Math.round(data.totalTCV).toLocaleString()}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, payload }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices < 5%

    const RADIAN = Math.PI / 180;

    // Calculate line points with external positioning
    const lineStartX = cx + outerRadius * Math.cos(-midAngle * RADIAN);
    const lineStartY = cy + outerRadius * Math.sin(-midAngle * RADIAN);
    const lineEndX = cx + (outerRadius + 20) * Math.cos(-midAngle * RADIAN);
    const lineEndY = cy + (outerRadius + 20) * Math.sin(-midAngle * RADIAN);

    // Calculate label position (outside the pie)
    const labelRadius = outerRadius + 35;
    const labelX = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const labelY = cy + labelRadius * Math.sin(-midAngle * RADIAN);

    const textAnchor = labelX > cx ? 'start' : 'end';

    // Format TCV value
    const tcv = payload.totalTCV;
    const formattedTCV = tcv >= 1000000
      ? `$${(tcv / 1000000).toFixed(1)}M`
      : tcv >= 1000
        ? `$${(tcv / 1000).toFixed(0)}K`
        : `$${tcv.toLocaleString()}`;

    return (
      <g>
        {/* Connecting line */}
        <line
          x1={lineStartX}
          y1={lineStartY}
          x2={lineEndX}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
          strokeDasharray="2,2"
        />
        {/* Horizontal line extension */}
        <line
          x1={lineEndX}
          y1={lineEndY}
          x2={labelX > cx ? lineEndX + 15 : lineEndX - 15}
          y2={lineEndY}
          stroke="#666"
          strokeWidth={1}
        />
        {/* Label text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY - 4}
          fill="#333"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={11}
          fontWeight="500"
        >
          {payload.name.length > 12 ? `${payload.name.substring(0, 12)}...` : payload.name}
        </text>
        {/* Value text */}
        <text
          x={labelX > cx ? lineEndX + 17 : lineEndX - 17}
          y={lineEndY + 8}
          fill="#666"
          textAnchor={textAnchor}
          dominantBaseline="central"
          fontSize={9}
        >
          {formattedTCV}
        </text>
      </g>
    );
  };

  return (
    <>
      <ChartWidget
        title="Auto Renewals by Classification"
        description="Distribution of auto-renewals by contract classification"
        loading={loading}
        error={error}
        onRefresh={onRefresh}
        height={420}
      >
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius="60%"
              innerRadius="0%"
              fill="#8884d8"
              dataKey="value"
              label={renderCustomizedLabel}
              labelLine={false}
              onClick={handlePieClick}
              style={{ cursor: 'pointer' }}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartWidget>

      <ContractDetailsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        title={`${selectedClassification?.name || ''} Auto-renewals`}
        description={`${selectedClassification?.count || 0} auto-renewal contracts of type ${selectedClassification?.name || ''}`}
        contracts={selectedClassification?.contracts || []}
        totalTCV={selectedClassification?.totalTCV}
        currency={selectedClassification?.currency}
      />
    </>
  );
};

// Savings Widget Props Interface
interface SavingsWidgetProps {
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

/**
 * Realised Savings Radial Chart Widget
 * Full circle radial chart showing actual vs potential savings
 */
export const RealisedSavingsWidget: React.FC<SavingsWidgetProps> = ({
  loading = false,
  error,
  onRefresh,
}) => {
  // Dummy data for realised savings - radial bar format
  // Outer ring = Potential, Inner ring = Actual
  const data = [
    { name: "Actual", value: 60, fill: COLORS.primary },
    { name: "Potential", value: 100, fill: COLORS.secondary },
  ];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">
            Value: <span className="font-medium">{data.value}%</span>
          </p>
        </div>
      );
    }
    return null;
  };



  return (
    <ChartWidget
      title="Realised Savings"
      description="Comparison of potential savings vs. actual savings realised"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={440}
    >
      <ResponsiveContainer width="100%" height={380}>
        <RadialBarChart
          cx="50%"
          cy="50%"
          innerRadius="40%"
          outerRadius="80%"
          data={data}
          startAngle={90}
          endAngle={450}
        >
          <RadialBar
            dataKey="value"
            cornerRadius={8}
            fill="#8884d8"
            label={{ position: 'insideStart', fill: '#000', clockWise: true, offset: -10, formatter: (value: number) => `${value}%`, }} background={{ fill: '#f3f4f6' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </RadialBarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Potential Savings by Category Radial Chart Widget
 * Full circle radial chart showing savings breakdown by category
 */
export const PotentialSavingsWidget: React.FC<SavingsWidgetProps> = ({
  loading = false,
  error,
  onRefresh,
}) => {
  // Dummy data for potential savings by category - radial bar format
  const data = [
    { name: "SW SaaS", value: 85, fill: COLORS.warning },
    { name: "Category A", value: 65, fill: COLORS.secondary },
    { name: "Category B", value: 45, fill: COLORS.primary },
  ];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm">
            Savings: <span className="font-medium">${data.value}K</span>
          </p>
        </div>
      );
    }
    return null;
  };



  return (
    <ChartWidget
      title="Potential Savings"
      description="Savings opportunity per category"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={440}
    >
      <ResponsiveContainer width="100%" height={380}>
        <RadialBarChart
          cx="50%"
          cy="50%"
          innerRadius="20%"
          outerRadius="80%"
          data={data}
          startAngle={90}
          endAngle={360}
        >
          <RadialBar
            dataKey="value"
            cornerRadius={8}
            label={{ position: 'insideStart', fill: '#000', clockWise: true, angle: 0, offset: -10, formatter: (value: number) => `$${value}K` }}
            fill="#8884d8"
            background={{ fill: '#f3f4f6' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </RadialBarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};

/**
 * Key Obligations Stacked Bar Chart Widget
 * Stacked bar chart showing obligations by supplier category
 */
export const KeyObligationsWidget: React.FC<SavingsWidgetProps> = ({
  loading = false,
  error,
  onRefresh,
}) => {
  // Dummy data for key obligations
  const data = [
    {
      category: "Key Obligations",
      supplier1: 15,
      supplier2: 25,
      supplier3: 12,
    },
    {
      category: "Critical Obligations",
      supplier1: 35,
      supplier2: 45,
      supplier3: 28,
    },
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm">
              <span style={{ color: entry.color }}>●</span> {entry.dataKey}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ChartWidget
      title="Key Obligations"
      description="Obligations breakdown by supplier category"
      loading={loading}
      error={error}
      onRefresh={onRefresh}
      height={440}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}

        >
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="category"
            tick={{ fontSize: 12 }}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            content={() => (
              <CustomLegend
                payload={[
                  { value: "Supplier 1", color: COLORS.warning },
                  { value: "Supplier 2", color: COLORS.primary },
                  { value: "Supplier 3", color: COLORS.secondary }
                ]}
              />
            )}
            wrapperStyle={{ paddingTop: '10px' }}
          />
          <Bar
            dataKey="supplier1"
            stackId="a"
            fill={COLORS.warning}
            name="Supplier 1"
          />
          <Bar
            dataKey="supplier2"
            stackId="a"
            fill={COLORS.primary}
            name="Supplier 2"
          />
          <Bar
            dataKey="supplier3"
            stackId="a"
            fill={COLORS.secondary}
            name="Supplier 3"
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartWidget>
  );
};
