/**
 * Widget Renderer Component
 * Renders different widget types based on widget configuration
 */

"use client";

import React, { useState, useEffect } from "react";
import { DashboardWidget } from "@/types/dashboard";
import { DashboardService } from "@/services/dashboardService";

// Import existing widget components
import { SummaryWidgets, ConfidenceWidget } from "./widgets/SummaryWidgets";
import {
  SpendByProviderWidget,
  AgreementTypeWidget,
  ConfidenceDistributionWidget,
  ContractStatusWidget,
} from "./widgets/ChartWidgets";
import {
  TopContractsWidget,
  RenewalTimelineWidget,
} from "./widgets/TableWidgets";

interface WidgetRendererProps {
  widget: DashboardWidget;
}

export const WidgetRenderer: React.FC<WidgetRendererProps> = ({ widget }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>(undefined);

  // Fetch data based on widget type
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log(`Fetching data for widget: ${widget.id} (${widget.widgetType})`);
        setLoading(true);
        setError(undefined);

        switch (widget.widgetType) {
          case "expiring-contracts":
            const expiringContractsData = await DashboardService.getExpiringContracts();
            setData(expiringContractsData);
            break;

          case "auto-renewals":
            const autoRenewalsData = await DashboardService.getAutoRenewals();
            setData(autoRenewalsData);
            break;

          case "auto-renewals-classification":
            const autoRenewalsClassificationData = await DashboardService.getAutoRenewalsByClassification();
            setData(autoRenewalsClassificationData);
            break;

          case "realised-savings":
          case "potential-savings":
          case "key-obligations":
            // These would need specific API endpoints - for now use summary data
            const prioritySummaryData = await DashboardService.getSummary();
            setData(prioritySummaryData);
            break;

          case "total-agreements":
          case "high-value-agreements":
          case "critical-agreements":
          case "expired-agreements":
          case "aging-contracts":
          case "customer-supplier-paper":
          case "service-types":
            // All portfolio widgets use the same portfolio overview API
            const portfolioData = await DashboardService.getPortfolioOverview();
            setData(portfolioData);
            break;

          case "summary-kpi":
            const summaryData = await DashboardService.getSummary();
            setData(summaryData);
            break;

          case "spend-by-provider":
          case "agreement-types":
            const spendData = await DashboardService.getSpendAnalysis();
            setData(spendData);
            break;

          case "confidence-distribution":
            const confidenceData =
              await DashboardService.getConfidenceDistribution();
            setData(confidenceData);
            break;

          case "top-contracts":
            const topContractsData = await DashboardService.getTopContracts(
              widget.configuration.pageSize || 10
            );
            setData(topContractsData);
            break;

          case "renewal-timeline":
            const renewalData = await DashboardService.getRenewalTimeline();
            setData(renewalData);
            break;

          case "confidence":
            const confidenceSummaryData = await DashboardService.getSummary();
            setData(confidenceSummaryData);
            break;

          default:
            setError(`Unknown widget type: ${widget.widgetType}`);
        }
        console.log(`Data fetched successfully for widget ${widget.id}:`, data);
      } catch (err) {
        console.error(`Error fetching data for widget ${widget.id}:`, err);
        setError("Failed to load widget data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up auto-refresh if configured
    const refreshInterval = widget.configuration.refreshInterval;
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchData, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [widget.id, widget.widgetType, widget.configuration]);

  // Refresh function
  const handleRefresh = () => {
    // Re-trigger the effect by updating a dependency
    setLoading(true);
  };

  // Render widget content based on type
  const renderWidgetContent = () => {
    switch (widget.widgetType) {
      case "summary-kpi":
        return (
          <div className="h-full">
            <SummaryWidgets
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "spend-by-provider":
        return (
          <div className="h-full">
            <SpendByProviderWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "agreement-types":
        return (
          <div className="h-full">
            <AgreementTypeWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "confidence-distribution":
        return (
          <div className="h-full">
            <ConfidenceDistributionWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "top-contracts":
        return (
          <div className="h-full">
            <TopContractsWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "renewal-timeline":
        return (
          <div className="h-full">
            <RenewalTimelineWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "confidence":
        return (
          <div className="h-full">
            <ConfidenceWidget
              confidence={data?.averageConfidence || 0}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "contract-status":
        return (
          <div className="h-full">
            <ContractStatusWidget
              data={data}
              loading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </div>
        );

      case "recent-activity":
        return (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="font-medium">Recent Activity Widget</p>
              <p className="text-sm">Coming soon...</p>
            </div>
          </div>
        );

      case "custom-chart":
        return (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="font-medium">Custom Chart Widget</p>
              <p className="text-sm">Configure your custom visualization</p>
            </div>
          </div>
        );

      case "custom-table":
        return (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="font-medium">Custom Table Widget</p>
              <p className="text-sm">Configure your custom data table</p>
            </div>
          </div>
        );

      default:
        return (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="font-medium">Unknown Widget Type</p>
              <p className="text-sm">{widget.widgetType}</p>
            </div>
          </div>
        );
    }
  };

  return <div className="widget-renderer h-full">{renderWidgetContent()}</div>;
};
