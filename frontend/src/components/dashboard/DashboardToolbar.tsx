/**
 * Dashboard Toolbar Component
 * Provides controls for dashboard customization
 */

"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Edit3,
  Plus,
  Save,
  RotateCcw,
  Settings,
  Eye,
  MoreHorizontal,
  Layout,
  Download,
  Upload,
} from "lucide-react";
import { DashboardWidget } from "@/types/dashboard";

interface DashboardToolbarProps {
  isEditMode: boolean;
  onToggleEditMode: () => void;
  onAddWidget: () => void;
  onSaveLayout: () => Promise<void>;
  onResetLayout: () => Promise<void>;
  selectedWidget?: DashboardWidget | null;
  onConfigureWidget?: () => void;
  onExportLayout?: () => void;
  onImportLayout?: () => void;
}

export const DashboardToolbar: React.FC<DashboardToolbarProps> = ({
  isEditMode,
  onToggleEditMode,
  onAddWidget,
  onSaveLayout,
  onResetLayout,
  selectedWidget,
  onConfigureWidget,
  onExportLayout,
  onImportLayout,
}) => {
  return (
    <div className="flex items-center justify-between p-4 bg-background border-b">
      {/* Left Section - Title and Mode */}
      <div className="flex items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-sm text-muted-foreground">
            Real-time insights from your contract portfolio
            {isEditMode &&
              " • Click to select • Drag to move • Resize from any edge or corner"}
          </p>
        </div>

        {isEditMode && (
          <Badge
            variant="secondary"
            className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
          >
            <Edit3 className="h-3 w-3 mr-1" />
            Edit Mode
          </Badge>
        )}

        {selectedWidget && (
          <Badge variant="outline" className="border-blue-200 text-blue-700">
            Selected: {selectedWidget.name}
          </Badge>
        )}
      </div>

      {/* Right Section - Actions */}
      <div className="flex items-center gap-2">
        {/* Edit Mode Toggle */}
        <Button
          variant={isEditMode ? "default" : "outline"}
          onClick={onToggleEditMode}
          className={isEditMode ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          {isEditMode ? (
            <>
              <Eye className="h-4 w-4 mr-2" />
              View Mode
            </>
          ) : (
            <>
              <Edit3 className="h-4 w-4 mr-2" />
              Edit Dashboard
            </>
          )}
        </Button>

        {/* Edit Mode Actions */}
        {isEditMode && (
          <>
            <Button variant="outline" onClick={onAddWidget}>
              <Plus className="h-4 w-4 mr-2" />
              Add Widget
            </Button>

            {selectedWidget && onConfigureWidget && (
              <Button variant="outline" onClick={onConfigureWidget}>
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </Button>
            )}

            <Button
              variant="outline"
              onClick={async () => {
                try {
                  await onSaveLayout();
                } catch (error) {
                  console.error("Failed to save layout:", error);
                }
              }}
            >
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </>
        )}

        {/* More Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {isEditMode && (
              <>
                <DropdownMenuItem onClick={() => onResetLayout()}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset Layout
                </DropdownMenuItem>
                <DropdownMenuSeparator />
              </>
            )}

            <DropdownMenuItem onClick={onExportLayout}>
              <Download className="h-4 w-4 mr-2" />
              Export Layout
            </DropdownMenuItem>

            <DropdownMenuItem onClick={onImportLayout}>
              <Upload className="h-4 w-4 mr-2" />
              Import Layout
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem>
              <Layout className="h-4 w-4 mr-2" />
              Manage Layouts
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
