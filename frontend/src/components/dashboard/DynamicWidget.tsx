/**
 * Dynamic Widget Renderer
 * Renders widgets dynamically based on widget type
 */

import React from 'react';
import { DashboardWidget } from '@/types/dashboard';
import { getWidgetInfo } from '@/constants/widgetTypes';

// Import all widget components
import {
  ExpiringContractsWidget,
  AutoRenewalsWidget,
  AutoRenewalsByClassificationWidget,
  RealisedSavingsWidget,
  PotentialSavingsWidget,
  KeyObligationsWidget,
} from '@/components/dashboard/widgets/ActionChartWidgets';

import {
  TotalAgreementsWidget,
  HighValueAgreementsWidget,
  CriticalAgreementsWidget,
  ExpiredAgreementsWidget,
  AgingContractsWidget,
  CustomerSupplierPaperWidget,
  AgreementTypeWidget as PortfolioAgreementTypeWidget,
  ServiceTypeWidget,
} from '@/components/dashboard/widgets/PortfolioOverviewWidgets';

interface DynamicWidgetProps {
  widget: DashboardWidget;
  data?: any;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  showControls?: boolean;
  onToggleVisibility?: (widgetId: string, isVisible: boolean) => void;
  onConfigure?: (widget: DashboardWidget) => void;
}

/**
 * Widget component mapping
 */
const WIDGET_COMPONENTS = {
  'expiring-contracts': ExpiringContractsWidget,
  'auto-renewals': AutoRenewalsWidget,
  'auto-renewals-classification': AutoRenewalsByClassificationWidget,
  'realised-savings': RealisedSavingsWidget,
  'potential-savings': PotentialSavingsWidget,
  'key-obligations': KeyObligationsWidget,
  'total-agreements': TotalAgreementsWidget,
  'high-value-agreements': HighValueAgreementsWidget,
  'critical-agreements': CriticalAgreementsWidget,
  'expired-agreements': ExpiredAgreementsWidget,
  'aging-contracts': AgingContractsWidget,
  'customer-supplier-paper': CustomerSupplierPaperWidget,
  'agreement-types': PortfolioAgreementTypeWidget,
  'service-types': ServiceTypeWidget,
} as const;

export const DynamicWidget: React.FC<DynamicWidgetProps> = ({
  widget,
  data,
  loading = false,
  error,
  onRefresh,
  showControls = false,
  onToggleVisibility,
  onConfigure,
}) => {
  const widgetInfo = getWidgetInfo(widget.widgetType as any);
  const WidgetComponent = WIDGET_COMPONENTS[widget.widgetType as keyof typeof WIDGET_COMPONENTS];

  if (!widget.isVisible) {
    return null;
  }

  if (!WidgetComponent) {
    console.warn(`Unknown widget type: ${widget.widgetType}`);
    return (
      <div className="p-4 border border-dashed border-gray-300 rounded-lg">
        <p className="text-gray-500 text-center">
          Unknown widget type: {widget.widgetType}
        </p>
      </div>
    );
  }

  if (!widgetInfo) {
    console.warn(`No widget info found for type: ${widget.widgetType}`);
  }

  // Widget wrapper with controls
  const widgetContent = (
    <div className="relative group">
      {/* Widget Controls */}
      {showControls && (
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex gap-1 bg-white dark:bg-gray-800 rounded-md shadow-md p-1">
            {onToggleVisibility && (
              <button
                onClick={() => onToggleVisibility(widget.id, !widget.isVisible)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-xs"
                title={widget.isVisible ? 'Hide widget' : 'Show widget'}
              >
                {widget.isVisible ? '👁️' : '👁️‍🗨️'}
              </button>
            )}
            {onConfigure && (
              <button
                onClick={() => onConfigure(widget)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-xs"
                title="Configure widget"
              >
                ⚙️
              </button>
            )}
          </div>
        </div>
      )}

      {/* Actual Widget Component */}
      <WidgetComponent
        data={data}
        loading={loading}
        error={error}
        onRefresh={onRefresh || (() => { })}
        // Pass widget configuration as props
        {...widget.configuration}
      />
    </div>
  );

  return widgetContent;
};

/**
 * Widget Grid Container
 * Renders a collection of widgets in a grid layout
 */
interface WidgetGridProps {
  widgets: DashboardWidget[];
  category: 'priority' | 'portfolio';
  data?: Record<string, any>;
  loading?: Record<string, boolean>;
  errors?: Record<string, string>;
  onRefresh?: Record<string, () => void>;
  showControls?: boolean;
  onToggleVisibility?: (widgetId: string, isVisible: boolean) => void;
  onConfigure?: (widget: DashboardWidget) => void;
}

export const WidgetGrid: React.FC<WidgetGridProps> = ({
  widgets,
  category,
  data = {},
  loading = {},
  errors = {},
  onRefresh = {},
  showControls = false,
  onToggleVisibility,
  onConfigure,
}) => {
  const visibleWidgets = widgets.filter(widget => widget.isVisible);

  if (visibleWidgets.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <p>No visible widgets in this section.</p>
        {showControls && (
          <p className="text-sm mt-2">
            Use the customize button to add widgets to this section.
          </p>
        )}
      </div>
    );
  }

  // Determine grid layout based on category
  const gridClass = category === 'priority'
    ? 'grid gap-6 lg:grid-cols-3' // 3x2 grid for priorities
    : 'grid gap-6 lg:grid-cols-4'; // 4x2 grid for portfolio

  return (
    <div className={gridClass}>
      {visibleWidgets
        .sort((a, b) => a.order - b.order)
        .map((widget) => (
          <DynamicWidget
            key={widget.id}
            widget={widget}
            data={data[widget.widgetType]}
            loading={loading[widget.widgetType]}
            error={errors[widget.widgetType]}
            onRefresh={onRefresh[widget.widgetType]}
            showControls={showControls}
            onToggleVisibility={onToggleVisibility}
            onConfigure={onConfigure}
          />
        ))}
    </div>
  );
};

/**
 * Widget Section
 * Renders a section with title and widgets
 */
interface WidgetSectionProps {
  title: string;
  description?: string;
  widgets: DashboardWidget[];
  category: 'priority' | 'portfolio';
  data?: Record<string, any>;
  loading?: Record<string, boolean>;
  errors?: Record<string, string>;
  onRefresh?: Record<string, () => void>;
  showControls?: boolean;
  onToggleVisibility?: (widgetId: string, isVisible: boolean) => void;
  onConfigure?: (widget: DashboardWidget) => void;
}

export const WidgetSection: React.FC<WidgetSectionProps> = ({
  title,
  description,
  widgets,
  category,
  data,
  loading,
  errors,
  onRefresh,
  showControls,
  onToggleVisibility,
  onConfigure,
}) => {
  const visibleWidgets = widgets.filter(widget => widget.isVisible);

  // Don't render section if no visible widgets and not in control mode
  if (visibleWidgets.length === 0 && !showControls) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">{title}</h3>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        {showControls && (
          <div className="text-sm text-gray-500">
            {visibleWidgets.length} of {widgets.length} widgets visible
          </div>
        )}
      </div>

      <WidgetGrid
        widgets={widgets}
        category={category}
        data={data}
        loading={loading}
        errors={errors}
        onRefresh={onRefresh}
        showControls={showControls}
        onToggleVisibility={onToggleVisibility}
        onConfigure={onConfigure}
      />
    </div>
  );
};
