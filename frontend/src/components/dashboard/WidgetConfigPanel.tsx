/**
 * Widget Configuration Panel
 * Side panel for configuring widget settings and properties
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useDashboard } from '@/contexts/DashboardContext';
import { DashboardWidget, WidgetConfig } from '@/types/dashboard';
import { getWidgetTemplate } from '@/data/widgetTemplates';
import { Save, RotateCcw, Settings } from 'lucide-react';

interface WidgetConfigPanelProps {
  widget: DashboardWidget;
  isOpen: boolean;
  onClose: () => void;
}

export const WidgetConfigPanel: React.FC<WidgetConfigPanelProps> = ({
  widget,
  isOpen,
  onClose,
}) => {
  const { updateWidget } = useDashboard();
  const [config, setConfig] = useState<WidgetConfig>(widget.configuration);
  const [title, setTitle] = useState(widget.name);
  const [description, setDescription] = useState(widget.description || '');

  const template = getWidgetTemplate(widget.widgetType as any);

  // Reset form when widget changes
  useEffect(() => {
    setConfig(widget.configuration);
    setTitle(widget.name);
    setDescription(widget.description || '');
  }, [widget]);

  const handleSave = () => {
    updateWidget(widget.id, {
      name: title,
      description,
      configuration: config,
    });
    onClose();
  };

  const handleReset = () => {
    if (template) {
      setConfig(template.defaultConfig);
      setTitle(template.name);
      setDescription(template.description);
    }
  };

  const updateConfig = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderConfigField = (field: any) => {
    const value = config[field.key as keyof WidgetConfig];

    switch (field.type) {
      case 'text':
        return (
          <Input
            value={value as string || ''}
            onChange={(e) => updateConfig(field.key, e.target.value)}
            placeholder={field.default}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value as number || ''}
            onChange={(e) => updateConfig(field.key, parseInt(e.target.value) || 0)}
            min={field.validation?.min}
            max={field.validation?.max}
            placeholder={field.default?.toString()}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={value as boolean || false}
            onCheckedChange={(checked) => updateConfig(field.key, checked)}
          />
        );

      case 'select':
        return (
          <Select
            value={value as string || field.default}
            onValueChange={(newValue) => updateConfig(field.key, newValue)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select option..." />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect':
        const selectedValues = (value as string[]) || field.default || [];
        return (
          <div className="space-y-2">
            {field.options?.map((option: any) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Switch
                  checked={selectedValues.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const newValues = checked
                      ? [...selectedValues, option.value]
                      : selectedValues.filter(v => v !== option.value);
                    updateConfig(field.key, newValues);
                  }}
                />
                <Label className="text-sm">{option.label}</Label>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <Input
            value={value as string || ''}
            onChange={(e) => updateConfig(field.key, e.target.value)}
            placeholder={field.default}
          />
        );
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configure Widget
          </SheetTitle>
          <SheetDescription>
            Customize the appearance and behavior of your widget
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 py-6">
          {/* Basic Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Settings</h3>

            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Widget title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Widget description"
                rows={2}
              />
            </div>
          </div>

          <Separator />

          {/* Widget-Specific Configuration */}
          {template?.configSchema?.fields && template.configSchema.fields.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Widget Configuration</h3>

              {template.configSchema.fields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <Label htmlFor={field.key}>
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {renderConfigField(field)}
                </div>
              ))}
            </div>
          )}

          <Separator />

          {/* Common Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Display Settings</h3>

            <div className="flex items-center justify-between">
              <Label htmlFor="showHeader">Show Header</Label>
              <Switch
                id="showHeader"
                checked={config.showHeader !== false}
                onCheckedChange={(checked) => updateConfig('showHeader', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="showDescription">Show Description</Label>
              <Switch
                id="showDescription"
                checked={config.showDescription !== false}
                onCheckedChange={(checked) => updateConfig('showDescription', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="refreshInterval">Refresh Interval (seconds)</Label>
              <Input
                id="refreshInterval"
                type="number"
                value={config.refreshInterval || 300}
                onChange={(e) => updateConfig('refreshInterval', parseInt(e.target.value) || 300)}
                min={30}
                max={3600}
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between pt-6 border-t">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
