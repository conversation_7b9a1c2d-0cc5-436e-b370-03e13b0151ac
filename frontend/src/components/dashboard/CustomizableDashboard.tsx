/**
 * Customizable Dashboard Component
 * Provides drag-and-drop dashboard with widget management using DnD Kit
 */

"use client";

import React, { useState, useCallback } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import { SortableContext, rectSortingStrategy } from "@dnd-kit/sortable";
import { useDashboard } from "@/contexts/DashboardContext";
import { DashboardWidget as DashboardWidgetType } from "@/types/dashboard";
import { DashboardWidget } from "./DashboardWidget";
import { WidgetRenderer } from "./WidgetRenderer";
import { DashboardToolbar } from "./DashboardToolbar";
import { WidgetLibrary } from "./WidgetLibrary";
import { WidgetConfigPanel } from "./WidgetConfigPanel";
import { DraggableWidget } from "./DraggableWidget";
import { Button } from "@/components/ui/button";
import { Plus, Settings, Save, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";

interface CustomizableDashboardProps {
  className?: string;
}

export const CustomizableDashboard: React.FC<CustomizableDashboardProps> = ({
  className,
}) => {
  const {
    widgets,
    isEditMode,
    selectedWidget,
    setEditMode,
    moveWidget,
    selectWidget,
    removeWidget,
    reorderWidgets,
    saveLayout,
    resetLayout,
    updateWidget,
  } = useDashboard();

  const [showWidgetLibrary, setShowWidgetLibrary] = useState(false);
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isAnyWidgetResizing, setIsAnyWidgetResizing] = useState(false);

  // Configure sensors for drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
    setIsDragging(true);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (over && active.id !== over.id) {
        const oldIndex = widgets.findIndex((w) => w.id === active.id);
        const newIndex = widgets.findIndex((w) => w.id === over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          // Reorder widgets using the context function
          reorderWidgets(oldIndex, newIndex);
        }
      }

      setActiveId(null);
      setIsDragging(false);
    },
    [widgets, reorderWidgets]
  );

  // Handle widget click in edit mode
  const handleWidgetClick = useCallback(
    (widget: DashboardWidgetType) => {
      if (isEditMode) {
        selectWidget(selectedWidget?.id === widget.id ? null : widget);
      }
    },
    [isEditMode, selectedWidget, selectWidget]
  );

  // Handle widget configuration
  const handleConfigureWidget = useCallback(
    (widget: DashboardWidgetType) => {
      selectWidget(widget);
      setShowConfigPanel(true);
    },
    [selectWidget]
  );

  // Handle widget removal
  const handleRemoveWidget = useCallback(
    (widgetId: string) => {
      removeWidget(widgetId);
      if (selectedWidget?.id === widgetId) {
        selectWidget(null);
      }
    },
    [removeWidget, selectedWidget, selectWidget]
  );

  // Handle widget resize
  const handleWidgetResize = useCallback(
    (widgetId: string, newSize: { w: number; h: number }) => {
      updateWidget(widgetId, {
        position: {
          ...(widgets.find((w) => w.id === widgetId)?.position || {
            x: 0,
            y: 0,
            w: 4,
            h: 3,
          }),
          w: newSize.w,
          h: newSize.h,
        },
      });
    },
    [updateWidget, widgets]
  );

  // Track resize state across all widgets
  const checkResizeState = useCallback(() => {
    const anyResizing = widgets.some((w) =>
      document
        .querySelector(`[data-widget-id="${w.id}"]`)
        ?.classList.contains("resizing")
    );
    setIsAnyWidgetResizing(anyResizing);
  }, [widgets]);

  return (
    <div className={cn("customizable-dashboard", className)}>
      {/* Dashboard Toolbar */}
      <DashboardToolbar
        isEditMode={isEditMode}
        onToggleEditMode={() => setEditMode(!isEditMode)}
        onAddWidget={() => setShowWidgetLibrary(true)}
        onSaveLayout={saveLayout}
        onResetLayout={resetLayout}
        selectedWidget={selectedWidget}
        onConfigureWidget={() =>
          selectedWidget && handleConfigureWidget(selectedWidget)
        }
      />

      {/* Edit Mode Overlay */}
      {isEditMode && (
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">
                Edit Mode Active
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Drag widgets to rearrange or click to select
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowWidgetLibrary(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Widget
              </Button>
              <Button variant="outline" size="sm" onClick={saveLayout}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm" onClick={resetLayout}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* DnD Context for Drag and Drop */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={widgets.map((w) => w.id)}
          strategy={rectSortingStrategy}
        >
          <div
            className={cn(
              "dashboard-grid",
              activeId && "dragging",
              isDragging && "drag-active",
              isAnyWidgetResizing && "resize-active"
            )}
          >
            {widgets.map((widget) => (
              <DraggableWidget
                key={widget.id}
                widget={widget}
                isEditMode={isEditMode}
                isSelected={selectedWidget?.id === widget.id}
                onClick={() => handleWidgetClick(widget)}
                onConfigure={() => handleConfigureWidget(widget)}
                onRemove={() => handleRemoveWidget(widget.id)}
                onResize={handleWidgetResize}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      {/* Dashboard Grid Styles */}
      <style jsx>{`
        .dashboard-grid {
          display: grid;
          grid-template-columns: repeat(12, 1fr);
          gap: 0.75rem;
          min-height: 400px;
          padding: 0.75rem;
          background: ${isEditMode
            ? "rgba(59, 130, 246, 0.05)"
            : "transparent"};
          border: ${isEditMode ? "2px dashed rgba(59, 130, 246, 0.3)" : "none"};
          border-radius: 8px;
          transition: all 0.2s ease;
          grid-auto-rows: minmax(60px, auto);
        }

        .dashboard-grid.dragging {
          background: rgba(59, 130, 246, 0.1);
        }

        .dashboard-grid.drag-active {
          background: rgba(59, 130, 246, 0.08);
          border-color: rgba(59, 130, 246, 0.4);
        }

        .dashboard-grid.resize-active {
          background: rgba(34, 197, 94, 0.05);
          border-color: rgba(34, 197, 94, 0.3);
        }

        .draggable-widget {
          position: relative;
          transition: all 0.2s ease;
        }

        .draggable-widget:hover .resize-handle {
          opacity: 1;
        }

        .draggable-widget.resizing {
          z-index: 30;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        /* Grid overlay for better visual feedback */
        .dashboard-grid.drag-active::before,
        .dashboard-grid.resize-active::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: linear-gradient(
              to right,
              rgba(59, 130, 246, 0.1) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(59, 130, 246, 0.1) 1px,
              transparent 1px
            );
          background-size: calc(100% / 12) 100px;
          pointer-events: none;
          z-index: 1;
        }

        .dashboard-grid.resize-active::before {
          background-image: linear-gradient(
              to right,
              rgba(34, 197, 94, 0.15) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(34, 197, 94, 0.15) 1px,
              transparent 1px
            );
        }

        @media (max-width: 768px) {
          .dashboard-grid {
            grid-template-columns: repeat(6, 1fr);
          }
        }

        @media (max-width: 480px) {
          .dashboard-grid {
            grid-template-columns: repeat(4, 1fr);
          }
        }
      `}</style>

      {/* Widget Library Modal */}
      {showWidgetLibrary && (
        <WidgetLibrary
          isOpen={showWidgetLibrary}
          onClose={() => setShowWidgetLibrary(false)}
        />
      )}

      {/* Widget Configuration Panel */}
      {showConfigPanel && selectedWidget && (
        <WidgetConfigPanel
          widget={selectedWidget}
          isOpen={showConfigPanel}
          onClose={() => setShowConfigPanel(false)}
        />
      )}
    </div>
  );
};
