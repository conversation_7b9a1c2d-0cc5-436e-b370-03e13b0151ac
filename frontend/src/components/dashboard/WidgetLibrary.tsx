/**
 * Widget Library Component
 * Modal for browsing and adding new widgets to the dashboard
 */

'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDashboard } from '@/contexts/DashboardContext';
import { WIDGET_TEMPLATES, getAllCategories, getWidgetsByCategory } from '@/data/widgetTemplates';
import { WidgetTemplate } from '@/types/dashboard';
import { 
  Search, 
  Plus, 
  BarChart3, 
  PieChart, 
  Table, 
  TrendingUp, 
  Calendar,
  Activity,
  Settings,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface WidgetLibraryProps {
  isOpen: boolean;
  onClose: () => void;
}

// Icon mapping for widget types
const getWidgetIcon = (iconName: string) => {
  const icons = {
    BarChart3,
    PieChart,
    Table,
    TrendingUp,
    Calendar,
    Activity,
    Settings,
    BarChart: BarChart3,
  };
  return icons[iconName as keyof typeof icons] || BarChart3;
};

export const WidgetLibrary: React.FC<WidgetLibraryProps> = ({
  isOpen,
  onClose,
}) => {
  const { addWidget } = useDashboard();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', ...getAllCategories()];

  // Filter widgets based on search and category
  const filteredWidgets = WIDGET_TEMPLATES.filter(widget => {
    const matchesSearch = widget.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         widget.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || widget.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAddWidget = (template: WidgetTemplate) => {
    addWidget(template);
    onClose();
  };

  const getCategoryDisplayName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      overview: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      analytics: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      contracts: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      financial: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      operational: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      custom: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    };
    return colors[category as keyof typeof colors] || colors.custom;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Widget Library</DialogTitle>
          <DialogDescription>
            Choose from our collection of pre-built widgets to customize your dashboard
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search widgets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              {categories.slice(1).map(category => (
                <TabsTrigger key={category} value={category}>
                  {getCategoryDisplayName(category)}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={selectedCategory} className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[400px] overflow-y-auto">
                {filteredWidgets.map((template) => {
                  const IconComponent = getWidgetIcon(template.icon);
                  
                  return (
                    <Card 
                      key={template.type} 
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => handleAddWidget(template)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <CardTitle className="text-sm">{template.name}</CardTitle>
                          </div>
                          <Badge 
                            variant="secondary" 
                            className={cn('text-xs', getCategoryColor(template.category))}
                          >
                            {getCategoryDisplayName(template.category)}
                          </Badge>
                        </div>
                        <CardDescription className="text-xs">
                          {template.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-muted-foreground">
                            Size: {template.defaultSize.w} × {template.defaultSize.h}
                          </div>
                          <Button size="sm" variant="outline">
                            <Plus className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {filteredWidgets.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No widgets found matching your criteria.</p>
                  <p className="text-sm">Try adjusting your search or category filter.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
