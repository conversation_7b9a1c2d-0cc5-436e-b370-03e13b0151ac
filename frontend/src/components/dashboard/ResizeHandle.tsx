/**
 * Resize Handle Component
 * Provides multiple resize handles for dashboard widgets
 */

"use client";

import React from "react";
import { GripHorizontal, Move } from "lucide-react";
import { cn } from "@/lib/utils";

export type ResizeDirection =
  | "se" // bottom-right
  | "sw" // bottom-left
  | "ne" // top-right
  | "nw" // top-left
  | "n" // top
  | "s" // bottom
  | "e" // right
  | "w"; // left

interface ResizeHandleProps {
  direction: ResizeDirection;
  onResizeStart: (e: React.MouseEvent, direction: ResizeDirection) => void;
  isResizing: boolean;
  className?: string;
}

const getHandleStyles = (direction: ResizeDirection) => {
  const baseStyles =
    "absolute w-3 h-3 bg-blue-500 hover:bg-blue-600 border border-blue-600 transition-all duration-200 z-20";

  switch (direction) {
    case "se":
      return `${baseStyles} bottom-0 right-0 cursor-se-resize rounded-tl-md`;
    case "sw":
      return `${baseStyles} bottom-0 left-0 cursor-sw-resize rounded-tr-md`;
    case "ne":
      return `${baseStyles} top-0 right-0 cursor-ne-resize rounded-bl-md`;
    case "nw":
      return `${baseStyles} top-0 left-0 cursor-nw-resize rounded-br-md`;
    case "n":
      return `${baseStyles} top-0 left-1/2 transform -translate-x-1/2 cursor-n-resize rounded-b-md w-6 h-2`;
    case "s":
      return `${baseStyles} bottom-0 left-1/2 transform -translate-x-1/2 cursor-s-resize rounded-t-md w-6 h-2`;
    case "e":
      return `${baseStyles} top-1/2 right-0 transform -translate-y-1/2 cursor-e-resize rounded-l-md w-2 h-6`;
    case "w":
      return `${baseStyles} top-1/2 left-0 transform -translate-y-1/2 cursor-w-resize rounded-r-md w-2 h-6`;
    default:
      return baseStyles;
  }
};

const getHandleIcon = (direction: ResizeDirection) => {
  if (["se", "sw", "ne", "nw"].includes(direction)) {
    return <GripHorizontal className="h-2 w-2 rotate-45" />;
  }
  return <Move className="h-2 w-2" />;
};

export const ResizeHandle: React.FC<ResizeHandleProps> = ({
  direction,
  onResizeStart,
  isResizing,
  className,
}) => {
  return (
    <div
      className={cn(
        getHandleStyles(direction),
        "opacity-0 group-hover:opacity-100",
        "shadow-sm",
        isResizing &&
          "opacity-100 bg-green-500 border-green-600 shadow-lg scale-110",
        className
      )}
      onMouseDown={(e) => onResizeStart(e, direction)}
      onClick={(e) => e.stopPropagation()}
      title={`Resize widget (${direction.toUpperCase()})`}
    >
      <div className="flex items-center justify-center w-full h-full text-white">
        {getHandleIcon(direction)}
      </div>
    </div>
  );
};

interface MultiResizeHandlesProps {
  onResizeStart: (e: React.MouseEvent, direction: ResizeDirection) => void;
  isResizing: boolean;
  showAllHandles?: boolean;
}

export const MultiResizeHandles: React.FC<MultiResizeHandlesProps> = ({
  onResizeStart,
  isResizing,
  showAllHandles = true,
}) => {
  const handles: ResizeDirection[] = showAllHandles
    ? ["nw", "n", "ne", "w", "e", "sw", "s", "se"]
    : ["se"]; // Only show corner handle if not all handles

  return (
    <>
      {handles.map((direction) => (
        <ResizeHandle
          key={direction}
          direction={direction}
          onResizeStart={onResizeStart}
          isResizing={isResizing}
        />
      ))}
    </>
  );
};
