"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Shield,
  Plus,
  Save,
  RotateCcw,
  Info,
  AlertTriangle,
  CheckCircle,
  Trash2,
  Edit3,
} from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import {
  IntegrityService,
  IntegrityConfiguration,
  IntegrityConfigurationData,
  IntegrityClause
} from "@/services/integrityService";

// Simplified interface for the modal - matches service structure
interface SimpleIntegrityClause {
  id: string;
  name: string;
  description: string;
  category: string;
  possibleValues: SimpleIntegrityClauseValue[];
}

interface SimpleIntegrityClauseValue {
  value: string;
  weightage: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  description: string;
}

interface SimpleConfigurationData {
  configurationName: string;
  description: string;
  clauses: SimpleIntegrityClause[];
}

interface IntegrityConfigurationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function IntegrityConfigurationModal({
  open,
  onOpenChange,
}: IntegrityConfigurationModalProps) {
  const [activeConfiguration, setActiveConfiguration] = useState<IntegrityConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingConfiguration, setEditingConfiguration] = useState<SimpleConfigurationData | null>(null);

  // Load active configuration when modal opens
  useEffect(() => {
    if (open) {
      loadActiveConfiguration();
    }
  }, [open]);

  const loadActiveConfiguration = async () => {
    setIsLoading(true);
    try {
      const config = await IntegrityService.getActiveConfiguration();
      setActiveConfiguration(config);

      // Automatically start editing the active configuration
      if (config) {
        const simpleConfig: SimpleConfigurationData = {
          configurationName: config.configurationName,
          description: config.clauses.description || "",
          clauses: config.clauses.clauses || [],
        };
        setEditingConfiguration(simpleConfig);
      } else {
        // If no active config exists, load default template
        await handleCreateNew();
      }
    } catch (error) {
      console.error("Error loading active configuration:", error);
      // If no active config, create from default template
      await handleCreateNew();
    } finally {
      setIsLoading(false);
    }
  };

  const loadDefaultTemplate = async () => {
    try {
      const template = await IntegrityService.getDefaultTemplate();
      return template;
    } catch (error) {
      console.error("Error loading default template:", error);
      toast.error("Failed to load default template");
      return null;
    }
  };

  const handleCreateNew = async () => {
    const template = await loadDefaultTemplate();
    if (!template) return;

    // Convert template clauses to simple format
    const simpleClauses: SimpleIntegrityClause[] = Array.isArray(template.clauses)
      ? template.clauses.map(clause => ({
        id: clause.id,
        name: clause.name,
        description: clause.description,
        category: clause.category,
        possibleValues: clause.possibleValues.map(value => ({
          value: value.value,
          weightage: value.weightage,
          riskLevel: value.riskLevel,
          description: value.description,
        })),
      }))
      : [];

    const newConfigurationData: SimpleConfigurationData = {
      configurationName: "Default Integrity Configuration",
      description: "Standard integrity assessment based on key contractual clauses",
      clauses: simpleClauses,
    };

    setEditingConfiguration(newConfigurationData);
  };



  const handleSave = async () => {
    if (!editingConfiguration) return;

    setIsSaving(true);
    try {
      // Convert simple format to service format (already in correct format)
      const serviceFormat: IntegrityConfigurationData = {
        configurationName: editingConfiguration.configurationName,
        description: editingConfiguration.description,
        clauses: editingConfiguration.clauses,
      };

      if (activeConfiguration) {
        // Update existing configuration
        await IntegrityService.updateConfiguration(activeConfiguration.id!, serviceFormat);
        toast.success("Configuration updated successfully");
      } else {
        // Create new configuration
        await IntegrityService.createConfiguration(serviceFormat);
        toast.success("Configuration created successfully");
      }

      // Reload the active configuration
      await loadActiveConfiguration();
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving configuration:", error);
      toast.error("Failed to save configuration");
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetToTemplate = async () => {
    setIsSaving(true);
    try {
      await IntegrityService.resetToTemplate();
      toast.success("Configuration reset to updated template successfully!");
      await loadActiveConfiguration();
      onOpenChange(false);
    } catch (error) {
      console.error("Error resetting configuration:", error);
      toast.error("Failed to reset configuration. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // Add new clause
  const handleAddClause = () => {
    if (!editingConfiguration) return;

    const newClause: SimpleIntegrityClause = {
      id: `clause_${Date.now()}`,
      name: "New Clause",
      description: "Description for the new clause",
      category: "Legal/contractual",
      possibleValues: [
        {
          value: "Not mentioned",
          weightage: 0.0,
          riskLevel: "High",
          description: "High risk - clause not specified"
        }
      ]
    };

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: [newClause, ...editingConfiguration.clauses]
    });
  };

  // Remove clause
  const handleRemoveClause = (clauseId: string) => {
    if (!editingConfiguration) return;

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: editingConfiguration.clauses.filter(clause => clause.id !== clauseId)
    });
  };

  // Update clause details
  const handleUpdateClause = (clauseId: string, field: string, value: string) => {
    if (!editingConfiguration) return;

    const updatedClauses = editingConfiguration.clauses.map(clause =>
      clause.id === clauseId ? { ...clause, [field]: value } : clause
    );

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: updatedClauses
    });
  };

  // Add new possible value to a clause
  const handleAddPossibleValue = (clauseId: string) => {
    if (!editingConfiguration) return;

    const newValue: SimpleIntegrityClauseValue = {
      value: "New Value",
      weightage: 0.0,
      riskLevel: "High",
      description: "Description for the new value"
    };

    const updatedClauses = editingConfiguration.clauses.map(clause =>
      clause.id === clauseId
        ? { ...clause, possibleValues: [...clause.possibleValues, newValue] }
        : clause
    );

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: updatedClauses
    });
  };

  // Remove possible value from a clause
  const handleRemovePossibleValue = (clauseId: string, valueIndex: number) => {
    if (!editingConfiguration) return;

    const updatedClauses = editingConfiguration.clauses.map(clause =>
      clause.id === clauseId
        ? {
          ...clause,
          possibleValues: clause.possibleValues.filter((_, index) => index !== valueIndex)
        }
        : clause
    );

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: updatedClauses
    });
  };

  // Update possible value
  const handleUpdatePossibleValue = (clauseId: string, valueIndex: number, field: string, value: any) => {
    if (!editingConfiguration) return;

    const updatedClauses = editingConfiguration.clauses.map(clause => {
      if (clause.id === clauseId) {
        const updatedValues = clause.possibleValues.map((val, index) =>
          index === valueIndex ? { ...val, [field]: value } : val
        );
        return { ...clause, possibleValues: updatedValues };
      }
      return clause;
    });

    setEditingConfiguration({
      ...editingConfiguration,
      clauses: updatedClauses
    });
  };



  const getTotalWeight = () => {
    if (!editingConfiguration || !editingConfiguration.clauses) return 0;
    // Calculate maximum possible score (sum of max weightage from each clause)
    return editingConfiguration.clauses.reduce((sum, clause) => {
      if (clause.possibleValues.length === 0) return sum;
      const maxWeight = Math.max(...clause.possibleValues.map(v => v.weightage));
      return sum + maxWeight;
    }, 0);
  };

  const getWeightColor = (weight: number) => {
    if (weight >= 8) return "text-red-600";
    if (weight >= 5) return "text-yellow-600";
    return "text-green-600";
  };

  if (editingConfiguration) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Integrity Configuration
            </DialogTitle>
            <DialogDescription>
              Configure clause weights and requirements for integrity analysis
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Configuration Details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="configName">Configuration Name</Label>
                <Input
                  id="configName"
                  value={editingConfiguration.configurationName}
                  onChange={(e) =>
                    setEditingConfiguration({
                      ...editingConfiguration,
                      configurationName: e.target.value,
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="configDesc">Description</Label>
                <Input
                  id="configDesc"
                  value={editingConfiguration.description}
                  onChange={(e) =>
                    setEditingConfiguration({
                      ...editingConfiguration,
                      description: e.target.value,
                    })
                  }
                />
              </div>
            </div>

            {/* Total Weight Display */}
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <span className="font-medium">Maximum Possible Score:</span>
              <span className={`font-bold ${getWeightColor(getTotalWeight())}`}>
                {getTotalWeight().toFixed(1)} points
              </span>
            </div>

            {/* Add New Clause Button */}
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Integrity Clauses</h3>
              <Button onClick={handleAddClause} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Clause
              </Button>
            </div>

            {/* Clauses Configuration */}
            <ScrollArea className="h-96">
              <div className="space-y-6 pr-4">
                {editingConfiguration.clauses?.map((clause) => (
                  <Card key={clause.id} className="p-4">
                    <div className="space-y-4">
                      {/* Clause Header with Edit/Delete */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs">Clause Name</Label>
                              <Input
                                value={clause.name}
                                onChange={(e) => handleUpdateClause(clause.id, 'name', e.target.value)}
                                className="text-sm"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Category</Label>
                              <Select
                                value={clause.category}
                                onValueChange={(value) => handleUpdateClause(clause.id, 'category', value)}
                              >
                                <SelectTrigger className="text-sm">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Legal terms">Legal terms</SelectItem>
                                  <SelectItem value="Commercial terms">Financial</SelectItem>
                                  <SelectItem value="Operational">Operational</SelectItem>
                                  <SelectItem value="Use rights & restrictions">Use rights & restrictions</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div>
                            <Label className="text-xs">Description</Label>
                            <Textarea
                              value={clause.description}
                              onChange={(e) => handleUpdateClause(clause.id, 'description', e.target.value)}
                              className="text-sm"
                              rows={2}
                            />
                          </div>
                        </div>
                        <Button
                          onClick={() => handleRemoveClause(clause.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Possible Values Section */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label className="text-sm font-medium">Possible Values & Risk Scoring</Label>
                          <Button
                            onClick={() => handleAddPossibleValue(clause.id)}
                            variant="outline"
                            size="sm"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Value
                          </Button>
                        </div>

                        {clause.possibleValues.map((value, valueIndex) => (
                          <div key={valueIndex} className="space-y-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border">
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <Label className="text-xs">Value</Label>
                                <Input
                                  value={value.value}
                                  onChange={(e) => handleUpdatePossibleValue(clause.id, valueIndex, 'value', e.target.value)}
                                  className="text-sm"
                                />
                              </div>
                              <div>
                                <Label className="text-xs">Risk Level</Label>
                                <Select
                                  value={value.riskLevel}
                                  onValueChange={(val) => handleUpdatePossibleValue(clause.id, valueIndex, 'riskLevel', val)}
                                >
                                  <SelectTrigger className="text-sm">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Low">Low</SelectItem>
                                    <SelectItem value="Medium">Medium</SelectItem>
                                    <SelectItem value="High">High</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            <div>
                              <Label className="text-xs">Description</Label>
                              <Input
                                value={value.description}
                                onChange={(e) => handleUpdatePossibleValue(clause.id, valueIndex, 'description', e.target.value)}
                                className="text-sm"
                              />
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <Label className="text-xs">Weightage (Points)</Label>
                                <div className="flex items-center gap-2">
                                  <span className={`text-sm font-bold ${getWeightColor(value.weightage)}`}>
                                    {value.weightage}
                                  </span>
                                  {clause.possibleValues.length > 1 && (
                                    <Button
                                      onClick={() => handleRemovePossibleValue(clause.id, valueIndex)}
                                      variant="ghost"
                                      size="sm"
                                      className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                              <Slider
                                value={[value.weightage]}
                                onValueChange={(newValue) => handleUpdatePossibleValue(clause.id, valueIndex, 'weightage', newValue[0])}
                                max={10}
                                min={0}
                                step={0.1}
                                className="w-full"
                              />
                              <div className="flex justify-between text-xs text-gray-500">
                                <span>0 points</span>
                                <span>10 points</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>

          <DialogFooter className="flex justify-between">
            <Button
              variant="destructive"
              onClick={handleResetToTemplate}
              disabled={isSaving}
              className="mr-auto"
            >
              Reset to Template
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Configuration"}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Integrity Configuration
            </DialogTitle>
            <DialogDescription>
              Configure clause weights and requirements for integrity analysis
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">Loading configuration...</div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // If no editing configuration, something went wrong
  return null;
}
