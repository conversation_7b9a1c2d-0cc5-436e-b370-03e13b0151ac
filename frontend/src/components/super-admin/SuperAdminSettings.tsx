/**
 * Super Admin Settings Component
 * Platform configuration and settings management
 */

import React, { useState, useEffect } from 'react';
import {
  SettingsIcon,
  ShieldIcon,
  DatabaseIcon,
  BellIcon,
  MailIcon,
  KeyIcon,
  ServerIcon,
  GlobeIcon,
  SaveIcon,
  RefreshCwIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { superAdminService } from '@/services/superAdminService';

interface PlatformConfig {
  features: {
    multiTenant: boolean;
    aiExtraction: boolean;
    analytics: boolean;
    notifications: boolean;
  };
  limits: {
    maxTenantsPerPlan: {
      basic: number;
      pro: number;
      enterprise: number;
    };
    maxUsersPerTenant: {
      basic: number;
      pro: number;
      enterprise: number;
    };
  };
  security: {
    passwordPolicy: {
      minLength: number;
      requireSpecialChars: boolean;
      requireNumbers: boolean;
    };
    sessionTimeout: number;
    maxLoginAttempts: number;
  };
}

export function SuperAdminSettings() {
  const [config, setConfig] = useState<PlatformConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const configData = await superAdminService.getPlatformConfig();
        setConfig(configData);
      } catch (error) {
        console.error('Error fetching platform config:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  const handleSave = async () => {
    if (!config) return;

    try {
      setSaving(true);
      // TODO: Implement save API call
      // await superAdminService.updatePlatformConfig(config);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Error saving config:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string[], value: any) => {
    if (!config) return;

    const newConfig = { ...config };
    let current: any = newConfig;

    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    current[path[path.length - 1]] = value;
    setConfig(newConfig);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8 space-y-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Platform Settings</h1>
              <p className="text-gray-600">Loading configuration...</p>
            </div>
          </div>
        </div>
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600 text-lg">Loading platform settings...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="min-h-screen bg-gray-50 p-8 space-y-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Platform Settings</h1>
              <p className="text-red-600">Failed to load configuration</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8 space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Platform Settings</h1>
            <p className="text-gray-600">
              Configure platform-wide settings and policies
            </p>
          </div>
          <div className="flex items-center gap-3">
            {lastSaved && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircleIcon className="h-4 w-4" />
                Last saved: {lastSaved.toLocaleTimeString()}
              </div>
            )}
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-primary text-white hover:bg-primary/90"
            >
              {saving ? (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <SaveIcon className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="limits">Limits</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GlobeIcon className="h-5 w-5 text-primary" />
                  Platform Configuration
                </CardTitle>
                <CardDescription>
                  Basic platform settings and information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="platform-name">Platform Name</Label>
                  <Input
                    id="platform-name"
                    defaultValue="Aptio Contract Management"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="platform-url">Platform URL</Label>
                  <Input
                    id="platform-url"
                    defaultValue="https://aptio.com"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support-email">Support Email</Label>
                  <Input
                    id="support-email"
                    type="email"
                    defaultValue="<EMAIL>"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BellIcon className="h-5 w-5 text-primary" />
                  Notification Settings
                </CardTitle>
                <CardDescription>
                  Configure system notifications and alerts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-gray-600">Send system alerts via email</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Slack Integration</Label>
                    <p className="text-sm text-gray-600">Send alerts to Slack channels</p>
                  </div>
                  <Switch />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="alert-threshold">Alert Threshold (%)</Label>
                  <Input
                    id="alert-threshold"
                    type="number"
                    defaultValue="85"
                    min="0"
                    max="100"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShieldIcon className="h-5 w-5 text-primary" />
                  Password Policy
                </CardTitle>
                <CardDescription>
                  Configure password requirements for all users
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="min-length">Minimum Length</Label>
                  <Input
                    id="min-length"
                    type="number"
                    value={config.security.passwordPolicy.minLength}
                    onChange={(e) => updateConfig(['security', 'passwordPolicy', 'minLength'], parseInt(e.target.value))}
                    min="6"
                    max="32"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Special Characters</Label>
                    <p className="text-sm text-gray-600">Include symbols like !@#$%</p>
                  </div>
                  <Switch
                    checked={config.security.passwordPolicy.requireSpecialChars}
                    onCheckedChange={(checked) => updateConfig(['security', 'passwordPolicy', 'requireSpecialChars'], checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Numbers</Label>
                    <p className="text-sm text-gray-600">Include at least one digit</p>
                  </div>
                  <Switch
                    checked={config.security.passwordPolicy.requireNumbers}
                    onCheckedChange={(checked) => updateConfig(['security', 'passwordPolicy', 'requireNumbers'], checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <KeyIcon className="h-5 w-5 text-primary" />
                  Session & Authentication
                </CardTitle>
                <CardDescription>
                  Configure session and login security
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (seconds)</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    value={config.security.sessionTimeout}
                    onChange={(e) => updateConfig(['security', 'sessionTimeout'], parseInt(e.target.value))}
                    min="300"
                    max="86400"
                    className="border-gray-300 focus:border-primary"
                  />
                  <p className="text-xs text-gray-600">Current: {Math.floor(config.security.sessionTimeout / 60)} minutes</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-attempts">Max Login Attempts</Label>
                  <Input
                    id="max-attempts"
                    type="number"
                    value={config.security.maxLoginAttempts}
                    onChange={(e) => updateConfig(['security', 'maxLoginAttempts'], parseInt(e.target.value))}
                    min="3"
                    max="10"
                    className="border-gray-300 focus:border-primary"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-gray-600">Require 2FA for admin users</p>
                  </div>
                  <Switch />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Limits Settings */}
        <TabsContent value="limits" className="space-y-6">
          <div className="grid gap-6">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ServerIcon className="h-5 w-5 text-primary" />
                  Platform Limits
                </CardTitle>
                <CardDescription>
                  Configure resource limits for different subscription plans
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Max Tenants Per Plan</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="basic-tenants">Basic Plan</Label>
                        <Input
                          id="basic-tenants"
                          type="number"
                          value={config.limits.maxTenantsPerPlan.basic}
                          onChange={(e) => updateConfig(['limits', 'maxTenantsPerPlan', 'basic'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="pro-tenants">Pro Plan</Label>
                        <Input
                          id="pro-tenants"
                          type="number"
                          value={config.limits.maxTenantsPerPlan.pro}
                          onChange={(e) => updateConfig(['limits', 'maxTenantsPerPlan', 'pro'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="enterprise-tenants">Enterprise Plan</Label>
                        <Input
                          id="enterprise-tenants"
                          type="number"
                          value={config.limits.maxTenantsPerPlan.enterprise}
                          onChange={(e) => updateConfig(['limits', 'maxTenantsPerPlan', 'enterprise'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Max Users Per Tenant</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="basic-users">Basic Plan</Label>
                        <Input
                          id="basic-users"
                          type="number"
                          value={config.limits.maxUsersPerTenant.basic}
                          onChange={(e) => updateConfig(['limits', 'maxUsersPerTenant', 'basic'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="pro-users">Pro Plan</Label>
                        <Input
                          id="pro-users"
                          type="number"
                          value={config.limits.maxUsersPerTenant.pro}
                          onChange={(e) => updateConfig(['limits', 'maxUsersPerTenant', 'pro'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="enterprise-users">Enterprise Plan</Label>
                        <Input
                          id="enterprise-users"
                          type="number"
                          value={config.limits.maxUsersPerTenant.enterprise}
                          onChange={(e) => updateConfig(['limits', 'maxUsersPerTenant', 'enterprise'], parseInt(e.target.value))}
                          className="w-20 border-gray-300 focus:border-primary"
                          min="1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Features Settings */}
        <TabsContent value="features" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5 text-primary" />
                  Platform Features
                </CardTitle>
                <CardDescription>
                  Enable or disable platform-wide features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Multi-Tenant Support</Label>
                    <p className="text-sm text-gray-600">Allow multiple tenants on the platform</p>
                  </div>
                  <Switch
                    checked={config.features.multiTenant}
                    onCheckedChange={(checked) => updateConfig(['features', 'multiTenant'], checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>AI Contract Extraction</Label>
                    <p className="text-sm text-gray-600">Enable AI-powered contract analysis</p>
                  </div>
                  <Switch
                    checked={config.features.aiExtraction}
                    onCheckedChange={(checked) => updateConfig(['features', 'aiExtraction'], checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Advanced Analytics</Label>
                    <p className="text-sm text-gray-600">Enable detailed analytics and reporting</p>
                  </div>
                  <Switch
                    checked={config.features.analytics}
                    onCheckedChange={(checked) => updateConfig(['features', 'analytics'], checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>System Notifications</Label>
                    <p className="text-sm text-gray-600">Enable platform-wide notifications</p>
                  </div>
                  <Switch
                    checked={config.features.notifications}
                    onCheckedChange={(checked) => updateConfig(['features', 'notifications'], checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DatabaseIcon className="h-5 w-5 text-primary" />
                  System Status
                </CardTitle>
                <CardDescription>
                  Current platform status and health
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Database Status</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    Healthy
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">API Status</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    Online
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">AI Service</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    Active
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Storage</span>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    75% Used
                  </Badge>
                </div>
                <div className="pt-2">
                  <Button variant="outline" size="sm" className="w-full">
                    <RefreshCwIcon className="h-4 w-4 mr-2" />
                    Refresh Status
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
