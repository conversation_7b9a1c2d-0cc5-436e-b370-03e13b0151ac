/**
 * Platform Analytics Component
 * Analytics dashboard for super admin users
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUpIcon,
  UsersIcon,
  BuildingIcon,
  FileTextIcon,
  BarChartIcon,
  CalendarIcon,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { superAdminService, type TenantAnalytics } from '@/services/superAdminService';

export function PlatformAnalytics() {
  const [tenantAnalytics, setTenantAnalytics] = useState<TenantAnalytics[]>([]);
  const [usageTrends, setUsageTrends] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        const [analyticsData, trendsData] = await Promise.all([
          superAdminService.getTenantAnalytics(),
          superAdminService.getUsageTrends(period),
        ]);

        setTenantAnalytics(analyticsData);
        setUsageTrends(trendsData);
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [period]);

  const totalMetrics = tenantAnalytics.reduce(
    (acc, tenant) => ({
      totalUsers: acc.totalUsers + tenant.metrics.totalUsers,
      activeUsers: acc.activeUsers + tenant.metrics.activeUsers,
      totalContracts: acc.totalContracts + tenant.metrics.totalContracts,
      contractsThisMonth: acc.contractsThisMonth + tenant.metrics.contractsThisMonth,
      storageUsed: acc.storageUsed + tenant.metrics.storageUsed,
    }),
    {
      totalUsers: 0,
      activeUsers: 0,
      totalContracts: 0,
      contractsThisMonth: 0,
      storageUsed: 0,
    }
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Platform Analytics</h1>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Platform Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive analytics across all tenants
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={period} onValueChange={(value: '7d' | '30d' | '90d') => setPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Platform Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMetrics.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {totalMetrics.activeUsers} active ({Math.round((totalMetrics.activeUsers / totalMetrics.totalUsers) * 100)}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
            <FileTextIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMetrics.totalContracts}</div>
            <p className="text-xs text-muted-foreground">
              +{totalMetrics.contractsThisMonth} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <BuildingIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tenantAnalytics.length}</div>
            <p className="text-xs text-muted-foreground">
              Platform tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <BarChartIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMetrics.storageUsed.toFixed(1)} GB</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage Trends */}
      {usageTrends && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUpIcon className="h-5 w-5" />
              Usage Trends
            </CardTitle>
            <CardDescription>
              Platform growth over the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {usageTrends.data.map((dataPoint: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{new Date(dataPoint.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-6">
                    <div className="text-center">
                      <div className="text-sm font-medium">{dataPoint.users}</div>
                      <div className="text-xs text-muted-foreground">Users</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium">{dataPoint.contracts}</div>
                      <div className="text-xs text-muted-foreground">Contracts</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium">{dataPoint.tenants}</div>
                      <div className="text-xs text-muted-foreground">Tenants</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tenant Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Tenant Performance</CardTitle>
          <CardDescription>
            Individual tenant metrics and activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tenantAnalytics.map((tenant) => (
              <div key={tenant.tenantId} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">{tenant.tenantName}</h3>
                  <Badge variant="outline">
                    Last active: {new Date(tenant.metrics.lastActivity).toLocaleDateString()}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <div className="font-medium">{tenant.metrics.totalUsers}</div>
                    <div className="text-muted-foreground">Total Users</div>
                  </div>
                  <div>
                    <div className="font-medium">{tenant.metrics.activeUsers}</div>
                    <div className="text-muted-foreground">Active Users</div>
                  </div>
                  <div>
                    <div className="font-medium">{tenant.metrics.totalContracts}</div>
                    <div className="text-muted-foreground">Total Contracts</div>
                  </div>
                  <div>
                    <div className="font-medium">+{tenant.metrics.contractsThisMonth}</div>
                    <div className="text-muted-foreground">This Month</div>
                  </div>
                  <div>
                    <div className="font-medium">{tenant.metrics.storageUsed.toFixed(1)} GB</div>
                    <div className="text-muted-foreground">Storage Used</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
