/**
 * Super Admin Dashboard Component
 * Main dashboard for super admin users showing all platform users
 */

import React, { useState, useEffect } from 'react';
import {
  BuildingIcon,
  UsersIcon,
  FileTextIcon,
  TrendingUpIcon,
  ServerIcon,
  ActivityIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  MoreHorizontalIcon,
  SearchIcon,
  FilterIcon,
  ShieldIcon,
  MailIcon,
  CalendarIcon,
  EyeIcon,
  EditIcon,
  UserXIcon,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable, DataTableColumn } from '@/components/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { superAdminService, PlatformOverview, UserInfo } from '@/services/superAdminService';

interface UserActionsProps {
  user: UserInfo;
  onViewDetails: (user: UserInfo) => void;
  onEditUser: (user: UserInfo) => void;
  onSuspendUser: (user: UserInfo) => void;
}

function UserActions({ user, onViewDetails, onEditUser, onSuspendUser }: UserActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontalIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => onViewDetails(user)}>
          <EyeIcon className="h-4 w-4 mr-2" />
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEditUser(user)}>
          <EditIcon className="h-4 w-4 mr-2" />
          Edit User
        </DropdownMenuItem>
        <DropdownMenuItem>
          <MailIcon className="h-4 w-4 mr-2" />
          Reset Password
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="text-destructive"
          onClick={() => onSuspendUser(user)}
        >
          <UserXIcon className="h-4 w-4 mr-2" />
          Suspend User
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function getStatusBadge(status: string) {
  switch (status.toLowerCase()) {
    case 'active':
      return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>;
    case 'inactive':
      return <Badge variant="secondary">Inactive</Badge>;
    case 'suspended':
      return <Badge className="bg-red-100 text-red-800 border-red-200">Suspended</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}

function getRoleBadge(role: string) {
  switch (role.toLowerCase()) {
    case 'super_admin':
      return <Badge className="bg-purple-100 text-purple-800 border-purple-200">
        <ShieldIcon className="h-3 w-3 mr-1" />
        Super Admin
      </Badge>;
    case 'admin':
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">
        <ShieldIcon className="h-3 w-3 mr-1" />
        Admin
      </Badge>;
    case 'user':
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200">User</Badge>;
    default:
      return <Badge variant="outline">{role}</Badge>;
  }
}

export function SuperAdminDashboard() {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [overview, setOverview] = useState<PlatformOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [usersData, overviewData] = await Promise.all([
          superAdminService.getAllUsers(),
          superAdminService.getPlatformOverview(),
        ]);

        setUsers(usersData.users);
        setOverview(overviewData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesStatus && matchesRole;
  });

  // User action handlers
  const handleViewDetails = (user: UserInfo) => {
    console.log('View details for user:', user);
    // TODO: Implement user details modal
  };

  const handleEditUser = (user: UserInfo) => {
    console.log('Edit user:', user);
    // TODO: Implement user edit modal
  };

  const handleSuspendUser = (user: UserInfo) => {
    console.log('Suspend user:', user);
    // TODO: Implement user suspension
  };

  // Define DataTable columns
  const columns: DataTableColumn<UserInfo>[] = [
    {
      key: 'user',
      header: 'User',
      render: (_, user) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
            <span className="text-sm font-medium text-primary">
              {(user.name || user.email).charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium">{user.name || user.email}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
            <div className="text-xs text-muted-foreground">ID: {user.id}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      header: 'Role',
      render: (_, user) => getRoleBadge(user.role),
    },
    {
      key: 'status',
      header: 'Status',
      render: (_, user) => getStatusBadge(user.status),
    },
    {
      key: 'tenants',
      header: 'Tenants',
      render: (_, user) => (
        <div className="space-y-1">
          {user.tenantUsers && user.tenantUsers.length > 0 ? (
            user.tenantUsers.map((tenantUser, index) => (
              <div key={index} className="flex items-center gap-2">
                <BuildingIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-sm font-medium">{tenantUser.tenant.name}</span>
                <Badge variant="outline" className="text-xs">
                  {tenantUser.tenantRole}
                </Badge>
              </div>
            ))
          ) : (
            <span className="text-sm text-muted-foreground">No tenants</span>
          )}
        </div>
      ),
    },
    {
      key: 'created',
      header: 'Created',
      render: (_, user) => (
        <div className="text-sm">
          <div className="flex items-center gap-1">
            <CalendarIcon className="h-3 w-3 text-muted-foreground" />
            {new Date(user.createdAt).toLocaleDateString()}
          </div>
          <div className="text-xs text-muted-foreground">
            {new Date(user.createdAt).toLocaleTimeString()}
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, user) => (
        <UserActions
          user={user}
          onViewDetails={handleViewDetails}
          onEditUser={handleEditUser}
          onSuspendUser={handleSuspendUser}
        />
      ),
    },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8 space-y-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
              <p className="text-gray-600">Loading user data...</p>
            </div>
          </div>
        </div>
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardContent className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600 text-lg">Loading platform users...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8 space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
            <p className="text-gray-600">
              Manage all users across all tenants
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button className="bg-white border border-primary text-primary px-4 py-2 rounded-lg hover:bg-primary hover:text-white transition-colors flex items-center gap-2">
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add User
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700">Total Users</CardTitle>
            <div className="p-2 bg-blue-50 rounded-lg">
              <UsersIcon className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{users.length}</div>
            <p className="text-xs text-gray-600 mt-1">
              {users.filter(u => u.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700">Admins</CardTitle>
            <div className="p-2 bg-green-50 rounded-lg">
              <ShieldIcon className="h-5 w-5 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {users.filter(u => u.role === 'admin' || u.role === 'ADMIN').length}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Administrative users
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700">Multi-Tenant Users</CardTitle>
            <div className="p-2 bg-purple-50 rounded-lg">
              <BuildingIcon className="h-5 w-5 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {users.filter(u => u.tenantUsers && u.tenantUsers.length > 1).length}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Users in multiple tenants
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700">Super Admins</CardTitle>
            <div className="p-2 bg-red-50 rounded-lg">
              <ShieldIcon className="h-5 w-5 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {users.filter(u => u.role === 'SUPER_ADMIN').length}
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Platform administrators
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-lg font-semibold text-gray-900">All Users</CardTitle>
          <CardDescription className="text-gray-600 text-sm">
            View and manage all users across the platform
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6 flex-wrap">
            <div className="relative flex-1 min-w-[300px]">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-10 border-gray-300 focus:border-primary focus:ring-1 focus:ring-primary"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40 h-10 border-gray-300 focus:border-primary">
                <SelectValue placeholder="Status: All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>

            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-40 h-10 border-gray-300 focus:border-primary">
                <SelectValue placeholder="Role: All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="user">User</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <DataTable
              data={filteredUsers}
              columns={columns}
              loading={loading}
              emptyMessage="No users found matching your criteria"
              maxHeight="500px"
              stickyHeader={true}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
