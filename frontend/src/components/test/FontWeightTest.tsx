"use client";

import React from "react";

export function FontWeightTest() {
  return (
    <div className="p-8 space-y-4 bg-background">
      <h2 className="text-2xl font-bold mb-6">Font Weight Test - Aptos Font</h2>

      <div className="mb-6 p-4 bg-card rounded-md border">
        <h3 className="text-lg font-semibold mb-2">Theme Test</h3>
        <p className="text-foreground">
          This text should be dark green in light mode and white in dark mode.
        </p>
        <p className="text-muted-foreground">
          This is muted text that adapts to the theme.
        </p>
      </div>

      <div className="space-y-2">
        <p className="font-light text-lg">
          Font Light (300) - This is Aptos Light
        </p>
        <p className="font-normal text-lg">
          Font Normal (400) - This is Aptos Normal
        </p>
        <p className="font-medium text-lg">
          Font Medium (500) - This is Aptos Medium
        </p>
        <p className="font-semibold text-lg">
          Font Semibold (600) - This is Aptos Semibold
        </p>
        <p className="font-bold text-lg">
          Font Bold (700) - This is Aptos Bold
        </p>
        <p className="font-extrabold text-lg">
          Font Extrabold (800) - This is Aptos Extrabold
        </p>
      </div>

      <div className="mt-8 space-y-2">
        <h3 className="text-lg font-semibold mb-4">
          Sidebar Navigation Simulation
        </h3>
        <div className="space-y-1">
          <div className="flex items-center gap-2 px-3 py-2 rounded-md bg-muted">
            <span className="font-medium text-foreground">
              Regular Navigation Item (font-medium)
            </span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-md bg-sidebar-accent">
            <span className="font-semibold text-sidebar-accent-foreground">
              Active Navigation Item (font-semibold)
            </span>
          </div>
          <div className="px-3 py-1">
            <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Section Header (font-semibold)
            </span>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-4">Logo Test</h3>
        <div className="text-xl font-extrabold text-foreground logo-text">
          MAIT (font-extrabold)
        </div>
      </div>

      <div className="mt-8 space-y-4">
        <h3 className="text-lg font-semibold mb-4">Button Tests</h3>
        <div className="flex gap-4 flex-wrap">
          <button className="bg-primary text-primary-foreground px-4 py-2 rounded font-medium">
            Primary Button
          </button>
          <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded font-medium">
            Secondary Button
          </button>
        </div>
      </div>

      <div className="mt-8 p-4 bg-muted rounded-md border">
        <h4 className="font-semibold mb-2 text-foreground">
          Dark Mode Test Info
        </h4>
        <p className="text-sm text-muted-foreground mb-2">
          Toggle dark mode to verify the theme changes:
        </p>
        <ul className="text-sm text-muted-foreground mt-2 space-y-1">
          <li>• Light mode: Dark green (#09260D) text on white background</li>
          <li>
            • Dark mode: White text on dark green (#09260D) background (NO white
            anywhere)
          </li>
          <li>• Font family should be "Aptos" in both modes</li>
          <li>• Font weights should be 300, 400, 500, 600, 700, 800</li>
          <li>• Each weight should render visibly different</li>
          <li>
            • All buttons, cards, and components use dark green shades in dark
            mode
          </li>
        </ul>
      </div>
    </div>
  );
}
