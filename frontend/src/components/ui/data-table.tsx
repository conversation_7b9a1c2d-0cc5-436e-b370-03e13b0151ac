"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import {
  ColumnDef,
  SortingState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";

// Column definition interface
export interface DataTableColumn<T = any> {
  key: string;
  header: string | (() => React.ReactNode);
  className?: string;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  accessor?: string | ((row: T) => any);
  sortable?: boolean;
}

// Props interface for the DataTable component
export interface DataTableProps<T = any> {
  data: T[];
  columns: DataTableColumn<T>[];
  className?: string;
  minWidth?: string;
  maxHeight?: string;
  loading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  emptyAction?: React.ReactNode;
  onRowClick?: (row: T, index: number) => void;
  rowClassName?: string | ((row: T, index: number) => string);
  selectedRows?: Set<string | number>;
  onRowSelect?: (rowId: string | number, selected: boolean) => void;
  selectAll?: boolean;
  onSelectAll?: (selected: boolean) => void;
  rowIdAccessor?: string | ((row: T) => string | number);
  stickyHeader?: boolean;
  containerClassName?: string;
}

// Loading skeleton component
function TableSkeleton({
  columns,
  rows = 5,
}: {
  columns: DataTableColumn[];
  rows?: number;
}) {
  return (
    <>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {columns.map((column, colIndex) => (
            <TableCell
              key={colIndex}
              className={cn("align-top", column.className)}
            >
              <Skeleton className="h-4 w-full" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );
}

// Empty state component
function EmptyState({
  message,
  icon,
  action,
  colSpan,
}: {
  message: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  colSpan: number;
}) {
  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="h-24 text-center">
        <div className="flex flex-col items-center justify-center gap-2 py-8">
          {icon && <div className="rounded-full bg-muted p-3">{icon}</div>}
          <h3 className="mt-2 text-lg font-semibold">{message}</h3>
          {action && <div className="mt-4">{action}</div>}
        </div>
      </TableCell>
    </TableRow>
  );
}

// Main DataTable component
export function DataTable<T = any>({
  data,
  columns,
  className,
  minWidth = "auto",
  maxHeight,
  loading = false,
  emptyMessage = "No data available",
  emptyIcon,
  emptyAction,
  onRowClick,
  rowClassName,
  selectedRows,
  onRowSelect,
  selectAll,
  onSelectAll,
  rowIdAccessor = "id",
  stickyHeader = false,
  containerClassName,
}: DataTableProps<T>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Helper function to get row ID
  const getRowId = React.useCallback(
    (row: T): string | number => {
      if (typeof rowIdAccessor === "function") {
        return rowIdAccessor(row);
      }
      return (row as any)[rowIdAccessor];
    },
    [rowIdAccessor]
  );

  // Convert our column format to TanStack Table format
  const tanstackColumns = React.useMemo<ColumnDef<T>[]>(() => {
    const cols: ColumnDef<T>[] = [];

    // Add selection column if needed
    if (selectedRows && onRowSelect && onSelectAll) {
      cols.push({
        id: "select",
        header: () => (
          <div className="flex items-center justify-center w-full">
            <Checkbox
              checked={selectAll}
              onCheckedChange={onSelectAll}
              aria-label="Select all rows"
            />
          </div>
        ),
        cell: ({ row }) => {
          const rowId = getRowId(row.original);
          return (
            <div className="flex items-center justify-center w-full">
              <Checkbox
                checked={selectedRows.has(rowId)}
                onCheckedChange={(checked) =>
                  onRowSelect(rowId, checked as boolean)
                }
                onClick={(e) => e.stopPropagation()}
                aria-label={`Select row ${row.index + 1}`}
              />
            </div>
          );
        },
        enableSorting: false,
        enableHiding: false,
        size: 60,
      });
    }

    // Add data columns
    columns.forEach((column) => {
      cols.push({
        id: column.key,
        accessorFn: (row) => {
          if (column.accessor) {
            if (typeof column.accessor === "function") {
              return column.accessor(row);
            }
            return (row as any)[column.accessor];
          }
          return (row as any)[column.key];
        },
        header: column.header,
        cell: ({ getValue, row }) => {
          const value = getValue();
          if (column.render) {
            return column.render(value, row.original, row.index);
          }
          // Ensure value is safe to render - convert objects to strings
          if (value === null || value === undefined) {
            return "N/A";
          }
          if (typeof value === "object") {
            // If it's an object with a 'value' property, extract that
            const objValue = value as any;
            if (objValue.value !== undefined) {
              return objValue.value != null ? String(objValue.value) : "N/A";
            }
            // Otherwise, try to show a meaningful representation
            try {
              return JSON.stringify(value);
            } catch {
              return "[Complex Object]";
            }
          }
          return String(value);
        },
        enableSorting: column.sortable ?? false,
      });
    });

    return cols;
  }, [columns, selectedRows, onRowSelect, onSelectAll, selectAll, getRowId]);

  // Create the table instance
  const table = useReactTable({
    data,
    columns: tanstackColumns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  // Helper function to get row class name
  const getRowClassName = React.useCallback(
    (row: T, index: number): string => {
      let baseClassName = "border-b transition-colors hover:bg-muted/50";

      if (selectedRows && onRowSelect) {
        const rowId = getRowId(row);
        if (selectedRows.has(rowId)) {
          baseClassName += " bg-blue-50 dark:bg-blue-950/20";
        }
      }

      if (onRowClick) {
        baseClassName += " cursor-pointer";
      }

      if (rowClassName) {
        if (typeof rowClassName === "function") {
          baseClassName += ` ${rowClassName(row, index)}`;
        } else {
          baseClassName += ` ${rowClassName}`;
        }
      }

      return baseClassName;
    },
    [selectedRows, onRowSelect, onRowClick, rowClassName, getRowId]
  );

  return (
    <div
      className={cn(
        "rounded-md border bg-background overflow-auto",
        containerClassName
      )}
    >
      <div
        className="overflow-x-auto overflow-y-auto scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400 dark:scrollbar-track-gray-800 dark:scrollbar-thumb-gray-600 dark:hover:scrollbar-thumb-gray-500"
        style={{
          ...(maxHeight ? { maxHeight } : {}),
          scrollbarGutter: "stable both-edges",
          scrollbarWidth: "thin",
        }}
      >
        <Table
          className={cn("w-full", className)}
          style={
            minWidth !== "auto"
              ? { minWidth, tableLayout: "auto" }
              : { tableLayout: "auto", width: "100%" }
          }
        >
          <TableHeader
            className={stickyHeader ? "sticky top-0 z-10 bg-background" : ""}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const column = columns.find((col) => col.key === header.id);
                  const isSelectColumn = header.id === "select";
                  const selectColumnClass = isSelectColumn
                    ? "w-[60px] min-w-[60px] max-w-[60px] px-3 py-4"
                    : "px-6 py-4";

                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        "font-medium",
                        selectColumnClass,
                        column?.className
                      )}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableSkeleton columns={columns} />
            ) : table.getRowModel().rows?.length === 0 ? (
              <EmptyState
                message={emptyMessage}
                icon={emptyIcon}
                action={emptyAction}
                colSpan={tanstackColumns.length}
              />
            ) : (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className={getRowClassName(row.original, row.index)}
                  onClick={() => onRowClick?.(row.original, row.index)}
                >
                  {row.getVisibleCells().map((cell) => {
                    const column = columns.find(
                      (col) => col.key === cell.column.id
                    );
                    const isSelectColumn = cell.column.id === "select";
                    const selectColumnClass = isSelectColumn
                      ? "w-[60px] min-w-[60px] max-w-[60px] px-3 py-4"
                      : "px-6 py-4";

                    return (
                      <TableCell
                        key={cell.id}
                        className={cn(
                          "align-top",
                          selectColumnClass,
                          column?.className
                        )}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
