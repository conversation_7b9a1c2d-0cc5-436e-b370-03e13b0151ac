/**
 * Timeline UI Component
 * Professional timeline visualization with tree-like structure
 * Consistent with Contract Management interface design standards
 */

"use client";

import React from "react";
import { Calendar, FileText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface TimelineItem {
  id: string;
  title: string;
  description?: string;
  timestamp: Date;
  icon?: React.ReactNode;
  metadata?: Record<string, any>;
  isMSA?: boolean; // Flag to indicate MSA contracts for special styling
}

interface TimelineProps {
  items: TimelineItem[];
  className?: string;
  showConnectors?: boolean;
  variant?: "default" | "compact";
  groupByDate?: boolean;
}

export function Timeline({
  items,
  className,
  showConnectors = true,
  variant = "default",
  groupByDate = false,
}: TimelineProps) {
  if (items.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
        <h3 className="text-base font-medium text-foreground mb-2">
          No Timeline Data
        </h3>
        <p className="text-sm text-muted-foreground">
          No contracts or timeline events to display.
        </p>
      </div>
    );
  }

  // Sort items by timestamp (newest first)
  const sortedItems = [...items].sort(
    (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
  );

  if (groupByDate) {
    return (
      <GroupedTimeline
        items={sortedItems}
        className={className}
        variant={variant}
      />
    );
  }

  return (
    <div className={cn("relative px-3 py-4", className)}>
      {sortedItems.map((item, index) => {
        const isLast = index === sortedItems.length - 1;
        const isCompact = variant === "compact";
        const isMSA = item.isMSA || false;

        return (
          <div
            key={item.id}
            className={cn(
              "relative flex gap-4 px-3 py-3",
              isCompact ? "pb-3" : "pb-4",
              !isLast && "mb-4",
              // MSA highlighting with subtle border and background
              isMSA &&
                "border border-[#09260D]/20 dark:border-[#4ade80]/20 rounded-lg bg-[#09260D]/5 dark:bg-[#0a2f0f]/10"
            )}
          >
            {/* Timeline Connector Line */}
            {showConnectors && !isLast && (
              <div
                className={cn(
                  "absolute left-6 w-px bg-border",
                  isCompact ? "top-8 h-8" : "top-10 h-10"
                )}
                style={{ transform: "translateX(-50%)" }}
              />
            )}

            {/* Timeline Node */}
            <div className="relative flex-shrink-0 pt-1">
              <div
                className={cn(
                  "flex items-center justify-center rounded-full border shadow-sm",
                  // MSA contracts get enhanced styling with golden accent
                  isMSA
                    ? "bg-gradient-to-br from-[#09260D] to-[#0a2f0f] border-[#fbbf24]/30 dark:border-[#fbbf24]/40 shadow-[#fbbf24]/20"
                    : "bg-[#09260D] dark:bg-[#0a2f0f] border-background",
                  isCompact ? "h-6 w-6" : "h-8 w-8"
                )}
              >
                {item.icon ? (
                  <div className="text-white">
                    {React.cloneElement(item.icon as React.ReactElement, {
                      className: cn(
                        "text-white",
                        isCompact ? "h-3 w-3" : "h-4 w-4"
                      ),
                    })}
                  </div>
                ) : (
                  <FileText
                    className={cn(
                      "text-white",
                      isCompact ? "h-3 w-3" : "h-4 w-4"
                    )}
                  />
                )}
              </div>
            </div>

            {/* Timeline Content */}
            <div className="flex-1 min-w-0 pt-0 px-3">
              <div className="flex items-start justify-between gap-4 mb-2">
                <div className="flex-1 min-w-0">
                  <h4
                    className={cn(
                      "font-medium text-foreground leading-tight mb-1",
                      isCompact ? "text-sm" : "text-base"
                    )}
                  >
                    {item.title}
                  </h4>
                  {item.description && (
                    <p
                      className={cn(
                        "text-muted-foreground mt-1 leading-normal",
                        isCompact ? "text-xs" : "text-sm"
                      )}
                    >
                      {item.description}
                    </p>
                  )}
                </div>
                <time
                  className={cn(
                    "text-muted-foreground font-normal flex-shrink-0 text-right",
                    isCompact ? "text-xs" : "text-sm"
                  )}
                  dateTime={item.timestamp.toISOString()}
                >
                  {item.timestamp.toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  })}
                </time>
              </div>

              {/* Metadata */}
              {item.metadata && Object.keys(item.metadata).length > 0 && (
                <div className="mt-3 flex flex-wrap gap-2">
                  {Object.entries(item.metadata).map(([key, value]) => {
                    // Use the value as-is since it's already formatted by the parent component
                    const displayValue = String(value);

                    // Special styling for MSA badge
                    const isMSABadge = key === "MSA";

                    return (
                      <span
                        key={key}
                        className={cn(
                          "inline-flex items-center rounded-md px-2 py-1 border",
                          isMSABadge
                            ? "bg-gradient-to-r from-[#09260D]/10 to-[#0a2f0f]/10 text-[#09260D] dark:text-[#4ade80] border-[#09260D]/30 dark:border-[#4ade80]/30 font-medium"
                            : "bg-muted/50 text-muted-foreground",
                          isCompact ? "text-xs" : "text-sm"
                        )}
                      >
                        <span className="font-medium">{key}:</span>
                        <span className="ml-1 font-normal">{displayValue}</span>
                      </span>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

// Grouped Timeline Component for date-based grouping
interface GroupedTimelineProps {
  items: TimelineItem[];
  className?: string;
  variant?: "default" | "compact";
}

function GroupedTimeline({
  items,
  className,
  variant = "default",
}: GroupedTimelineProps) {
  // Group timeline items by date
  const groupedItems = items.reduce((groups, item) => {
    const dateKey = item.timestamp.toDateString();
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(item);
    return groups;
  }, {} as Record<string, TimelineItem[]>);

  const sortedDateKeys = Object.keys(groupedItems).sort(
    (a, b) => new Date(b).getTime() - new Date(a).getTime()
  );

  return (
    <div className={cn("space-y-6", className)}>
      {sortedDateKeys.map((dateKey, groupIndex) => {
        const date = new Date(dateKey);
        const items = groupedItems[dateKey];
        const isLast = groupIndex === sortedDateKeys.length - 1;

        return (
          <div key={dateKey} className={cn("space-y-4", !isLast && "mb-6")}>
            {/* Date Header */}
            <div className="flex items-center gap-3">
              <div className="h-px bg-border flex-1" />
              <div className="flex items-center gap-2 px-3 py-2 bg-muted/50 rounded-md border text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-foreground">
                  {date.toLocaleDateString("en-US", {
                    weekday: "short",
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  })}
                </span>
                <Badge
                  variant="secondary"
                  className="text-xs font-medium bg-[#09260D]/10 text-[#09260D] dark:bg-[#0a2f0f]/20 dark:text-[#4ade80] px-1.5 py-0.5"
                >
                  {items.length}
                </Badge>
              </div>
              <div className="h-px bg-border flex-1" />
            </div>

            {/* Timeline for this date */}
            <Timeline
              items={items}
              showConnectors={true}
              variant={variant}
              groupByDate={false}
              className="px-0"
            />
          </div>
        );
      })}
    </div>
  );
}

// Timeline Header Component
interface TimelineHeaderProps {
  title: string;
  subtitle?: string;
  itemCount: number;
  className?: string;
}

export function TimelineHeader({
  title,
  subtitle,
  itemCount,
  className,
}: TimelineHeaderProps) {
  return (
    <div className={cn("border-b pb-4 mb-4", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-1">
            {title}
          </h3>
          {subtitle && (
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          )}
        </div>
        <div className="text-right">
          <div className="text-sm font-medium text-foreground bg-muted/50 px-3 py-1.5 rounded-md border">
            {itemCount} {itemCount === 1 ? "contract" : "contracts"}
          </div>
        </div>
      </div>
    </div>
  );
}

// Timeline Container Component with professional styling
interface TimelineContainerProps {
  children: React.ReactNode;
  className?: string;
  maxHeight?: string;
}

export function TimelineContainer({
  children,
  className,
  maxHeight = "500px",
}: TimelineContainerProps) {
  return (
    <div
      className={cn(
        "bg-card border rounded-lg shadow-sm",
        // Refined padding for professional appearance
        "p-4",
        // Professional scrolling
        "overflow-y-auto",
        className
      )}
      style={{ maxHeight }}
    >
      {children}
    </div>
  );
}

// Timeline Modal Component (replaces FolderTimeline dialog logic)
interface TimelineModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
}

export function TimelineModal({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  className,
}: TimelineModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/80 animate-in fade-in-0 duration-300"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div
        className={cn(
          "relative z-50 w-full max-w-4xl max-h-[85vh] bg-background border rounded-lg shadow-lg",
          "animate-in fade-in-0 zoom-in-95 duration-300",
          "flex flex-col overflow-hidden",
          className
        )}
      >
        {/* Header with single close button */}
        <div className="flex-shrink-0 px-6 py-4 border-b bg-muted/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-md bg-[#09260D]/10 dark:bg-[#0a2f0f]/20">
                <Calendar className="h-5 w-5 text-[#09260D] dark:text-[#4ade80]" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">
                  {title}
                </h2>
                {subtitle && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-1.5 rounded-md hover:bg-muted transition-colors"
              aria-label="Close timeline"
            >
              <svg
                className="h-4 w-4 text-muted-foreground"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4">{children}</div>
      </div>
    </div>
  );
}
