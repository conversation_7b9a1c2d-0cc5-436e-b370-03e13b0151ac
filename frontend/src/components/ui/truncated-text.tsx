/**
 * TruncatedText Component
 * Provides text truncation with ellipsis and tooltip functionality
 */

"use client";

import React, { useState, useRef, useEffect } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TruncatedTextProps {
  children: React.ReactNode;
  maxWidth?: string;
  maxLines?: number;
  className?: string;
  tooltipClassName?: string;
  showTooltip?: boolean;
}

export function TruncatedText({
  children,
  maxWidth = "200px",
  maxLines = 2,
  className = "",
  tooltipClassName = "",
  showTooltip = true,
}: TruncatedTextProps) {
  const [isTruncated, setIsTruncated] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const fullText = String(children || "");

  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current) {
        const element = textRef.current;
        // Check if content is truncated either by width or height
        const isWidthTruncated = element.scrollWidth > element.clientWidth;
        const isHeightTruncated = element.scrollHeight > element.clientHeight;
        setIsTruncated(isWidthTruncated || isHeightTruncated);
      }
    };

    checkTruncation();

    // Re-check on window resize
    window.addEventListener("resize", checkTruncation);
    return () => window.removeEventListener("resize", checkTruncation);
  }, [children]);

  const truncatedStyle = {
    maxWidth,
    overflow: "hidden",
    display: "-webkit-box",
    WebkitLineClamp: maxLines,
    WebkitBoxOrient: "vertical" as const,
    wordBreak: "break-word" as const,
    lineHeight: "1.4",
    minHeight: "1.4em", // Ensure consistent height even for empty content
  };

  const content = (
    <div
      ref={textRef}
      className={`${className}`}
      style={truncatedStyle}
      title={!showTooltip ? fullText : undefined}
    >
      {children}
    </div>
  );

  if (!showTooltip || !isTruncated || !fullText) {
    return content;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{content}</TooltipTrigger>
        <TooltipContent
          className={`max-w-sm p-3 text-sm bg-primary text-primary-foreground ${tooltipClassName}`}
          side="top"
        >
          <div className="whitespace-pre-wrap break-words">{fullText}</div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Specialized variants for common use cases
export function TruncatedTextSingleLine({
  children,
  maxWidth = "200px",
  className = "",
  tooltipClassName = "",
  showTooltip = true,
}: Omit<TruncatedTextProps, "maxLines">) {
  const fullText = String(children || "");
  const [isTruncated, setIsTruncated] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current) {
        const element = textRef.current;
        setIsTruncated(element.scrollWidth > element.clientWidth);
      }
    };

    checkTruncation();
    window.addEventListener("resize", checkTruncation);
    return () => window.removeEventListener("resize", checkTruncation);
  }, [children]);

  const singleLineStyle = {
    maxWidth,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap" as const,
    minHeight: "1.4em", // Ensure consistent height
  };

  const content = (
    <div
      ref={textRef}
      className={className}
      style={singleLineStyle}
      title={!showTooltip ? fullText : undefined}
    >
      {children}
    </div>
  );

  if (!showTooltip || !isTruncated || !fullText) {
    return content;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{content}</TooltipTrigger>
        <TooltipContent
          className={`max-w-sm p-3 text-sm bg-primary text-primary-foreground ${tooltipClassName}`}
          side="top"
        >
          <div className="whitespace-pre-wrap break-words">{fullText}</div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Specialized variant for table cells with responsive behavior
export function TruncatedTableCell({
  children,
  maxWidth = "200px",
  maxLines = 2,
  className = "",
  tooltipClassName = "",
  showTooltip = true,
}: TruncatedTextProps) {
  return (
    <TruncatedText
      maxWidth={maxWidth}
      maxLines={maxLines}
      className={`text-sm ${className}`}
      tooltipClassName={tooltipClassName}
      showTooltip={showTooltip}
    >
      {children}
    </TruncatedText>
  );
}
