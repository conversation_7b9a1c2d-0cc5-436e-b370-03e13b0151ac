/**
 * Spinner Component
 * Displays a loading spinner
 */

import React from 'react';

interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'gray' | 'white';
}

export function Spinner({ size = 'md', color = 'blue' }: SpinnerProps) {
  // Determine size class
  const sizeClass = {
    sm: 'w-4 h-4 border-2',
    md: 'w-6 h-6 border-2',
    lg: 'w-8 h-8 border-3'
  }[size];
  
  // Determine color class
  const colorClass = {
    blue: 'border-blue-500 border-t-transparent',
    gray: 'border-gray-500 border-t-transparent',
    white: 'border-white border-t-transparent'
  }[color];
  
  return (
    <div className={`${sizeClass} ${colorClass} rounded-full animate-spin`} role="status">
      <span className="sr-only">Loading...</span>
    </div>
  );
}
