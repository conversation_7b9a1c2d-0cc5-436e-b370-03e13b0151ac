/**
 * Widget Management Hooks
 * React hooks for managing dashboard widgets
 */

import { useState, useEffect, useCallback } from 'react';
import { DashboardWidget, GroupedWidgets } from '@/types/dashboard';
import { DashboardLayoutService } from '@/services/dashboardLayoutService';
import { toast } from 'sonner';

/**
 * Hook for managing all user widgets
 */
export function useWidgets() {
  const [widgets, setWidgets] = useState<GroupedWidgets | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWidgets = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔍 Fetching widgets...');
      const data = await DashboardLayoutService.getWidgets();
      console.log('📊 Widgets received:', data);
      console.log('📊 Priority widgets:', data?.priority?.length || 0);
      console.log('📊 Portfolio widgets:', data?.portfolio?.length || 0);
      setWidgets(data);
    } catch (err) {
      console.error('Error fetching widgets:', err);
      setError('Failed to load dashboard widgets');
      toast.error('Failed to load dashboard widgets');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchWidgets();
  }, [fetchWidgets]);

  const updateWidgetVisibility = useCallback(async (widgetId: string, isVisible: boolean) => {
    try {
      await DashboardLayoutService.updateWidgetVisibility(widgetId, isVisible);
      await fetchWidgets(); // Refresh widgets
      toast.success(`Widget ${isVisible ? 'shown' : 'hidden'} successfully`);
    } catch (err) {
      console.error('Error updating widget visibility:', err);
      toast.error('Failed to update widget visibility');
    }
  }, [fetchWidgets]);

  const updateWidgetConfiguration = useCallback(async (widgetId: string, configuration: any) => {
    try {
      await DashboardLayoutService.updateWidgetConfiguration(widgetId, configuration);
      await fetchWidgets(); // Refresh widgets
      toast.success('Widget configuration updated successfully');
    } catch (err) {
      console.error('Error updating widget configuration:', err);
      toast.error('Failed to update widget configuration');
    }
  }, [fetchWidgets]);

  const bulkUpdateWidgets = useCallback(async (updates: Array<{ id: string; data: Partial<DashboardWidget> }>) => {
    try {
      await DashboardLayoutService.bulkUpdateWidgets(updates);
      await fetchWidgets(); // Refresh widgets
      toast.success('Widgets updated successfully');
    } catch (err) {
      console.error('Error bulk updating widgets:', err);
      toast.error('Failed to update widgets');
    }
  }, [fetchWidgets]);

  const resetWidgets = useCallback(async () => {
    try {
      const data = await DashboardLayoutService.resetWidgets();
      setWidgets(data);
      toast.success('Dashboard reset to defaults successfully');
    } catch (err) {
      console.error('Error resetting widgets:', err);
      toast.error('Failed to reset dashboard');
    }
  }, []);

  return {
    widgets,
    loading,
    error,
    refetch: fetchWidgets,
    updateWidgetVisibility,
    updateWidgetConfiguration,
    bulkUpdateWidgets,
    resetWidgets
  };
}

/**
 * Hook for managing widgets by category
 */
export function useWidgetsByCategory(category: 'priority' | 'portfolio') {
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWidgets = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await DashboardLayoutService.getWidgetsByCategory(category);
      console.log(`Widgets for hahaaa ${category}:`, data);
      setWidgets(data);
    } catch (err) {
      console.error(`Error fetching ${category} widgets:`, err);
      setError(`Failed to load ${category} widgets`);
      toast.error(`Failed to load ${category} widgets`);
    } finally {
      setLoading(false);
    }
  }, [category]);

  useEffect(() => {
    fetchWidgets();
  }, [fetchWidgets]);

  return {
    widgets,
    loading,
    error,
    refetch: fetchWidgets
  };
}

/**
 * Hook for managing individual widget visibility
 */
export function useWidgetVisibility(widgetId: string, initialVisibility: boolean = true) {
  const [isVisible, setIsVisible] = useState(initialVisibility);
  const [updating, setUpdating] = useState(false);

  const toggleVisibility = useCallback(async () => {
    try {
      setUpdating(true);
      const newVisibility = !isVisible;
      await DashboardLayoutService.updateWidgetVisibility(widgetId, newVisibility);
      setIsVisible(newVisibility);
      toast.success(`Widget ${newVisibility ? 'shown' : 'hidden'} successfully`);
    } catch (err) {
      console.error('Error toggling widget visibility:', err);
      toast.error('Failed to update widget visibility');
    } finally {
      setUpdating(false);
    }
  }, [widgetId, isVisible]);

  const setVisibility = useCallback(async (visible: boolean) => {
    try {
      setUpdating(true);
      await DashboardLayoutService.updateWidgetVisibility(widgetId, visible);
      setIsVisible(visible);
      toast.success(`Widget ${visible ? 'shown' : 'hidden'} successfully`);
    } catch (err) {
      console.error('Error setting widget visibility:', err);
      toast.error('Failed to update widget visibility');
    } finally {
      setUpdating(false);
    }
  }, [widgetId]);

  return {
    isVisible,
    updating,
    toggleVisibility,
    setVisibility
  };
}

/**
 * Hook for managing widget configuration
 */
export function useWidgetConfiguration(widgetId: string, initialConfiguration: any = {}) {
  const [configuration, setConfiguration] = useState(initialConfiguration);
  const [updating, setUpdating] = useState(false);

  const updateConfiguration = useCallback(async (newConfiguration: any) => {
    try {
      setUpdating(true);
      await DashboardLayoutService.updateWidgetConfiguration(widgetId, newConfiguration);
      setConfiguration(newConfiguration);
      toast.success('Widget configuration updated successfully');
    } catch (err) {
      console.error('Error updating widget configuration:', err);
      toast.error('Failed to update widget configuration');
    } finally {
      setUpdating(false);
    }
  }, [widgetId]);

  const updatePartialConfiguration = useCallback(async (partialConfiguration: Partial<any>) => {
    const newConfiguration = { ...configuration, ...partialConfiguration };
    await updateConfiguration(newConfiguration);
  }, [configuration, updateConfiguration]);

  return {
    configuration,
    updating,
    updateConfiguration,
    updatePartialConfiguration
  };
}

/**
 * Hook for dashboard initialization
 */
export function useDashboardInitialization() {
  const [initialized, setInitialized] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        setInitializing(true);
        setError(null);
        // This will ensure widgets exist and return them
        await DashboardLayoutService.getWidgets();
        setInitialized(true);
      } catch (err) {
        console.error('Error initializing dashboard:', err);
        setError('Failed to initialize dashboard');
        toast.error('Failed to initialize dashboard');
      } finally {
        setInitializing(false);
      }
    };

    initializeDashboard();
  }, []);

  return {
    initialized,
    initializing,
    error
  };
}
