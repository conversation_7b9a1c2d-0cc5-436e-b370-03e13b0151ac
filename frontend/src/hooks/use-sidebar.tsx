"use client"

import { useState, createContext, useContext } from "react"
import { useIsMobile } from "./use-mobile"

interface SidebarContextType {
  isMobile: boolean
  state: "open" | "closed"
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  toggle: () => void
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

export function SidebarProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const isMobile = useIsMobile()
  const [state, setState] = useState<"open" | "closed">("open")
  const [openMobile, setOpenMobile] = useState(false)

  const toggle = () => {
    setState(state === "open" ? "closed" : "open")
  }

  return (
    <SidebarContext.Provider
      value={{
        isMobile,
        state,
        openMobile,
        setOpenMobile,
        toggle,
      }}
    >
      {children}
    </SidebarContext.Provider>
  )
}

export function useSidebar() {
  const context = useContext(SidebarContext)
  
  if (context === undefined) {
    throw new Error("useSidebar must be used within a SidebarProvider")
  }
  
  return context
}
