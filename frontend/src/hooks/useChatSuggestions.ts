/**
 * Custom hook for managing chat suggestions
 * Provides static FAQ suggestions initially, then context-based suggestions after user interaction
 */

import { useState, useCallback } from "react";
import { usePathname } from "next/navigation";
import { Suggestion } from "@/components/ai/ChatSuggestions";
import { apiClient } from "@/lib/api-client";

interface Message {
  role: "user" | "assistant" | "system";
  content: string;
}

interface UseChatSuggestionsProps {
  messages: Message[];
  pinnedContractId?: string;
  contractTitle?: string;
  isLoading?: boolean;
}

// Static FAQ suggestions shown initially (no API calls)
const STATIC_FAQ_SUGGESTIONS: Suggestion[] = [
  {
    id: "faq-contract-analysis",
    text: "Help me understand my contract terms",
    category: "general",
    icon: "FileText",
    priority: 1,
  },
  {
    id: "faq-risk-assessment",
    text: "What should I watch out for in this agreement?",
    category: "general",
    icon: "Shield",
    priority: 2,
  },
  {
    id: "faq-key-terms",
    text: "Explain the key terms in simple language",
    category: "general",
    icon: "CheckSquare",
    priority: 3,
  },
  {
    id: "faq-compliance",
    text: "How do I stay compliant with my contracts?",
    category: "general",
    icon: "Settings",
    priority: 4,
  },
  {
    id: "faq-renewals",
    text: "When do my contracts expire?",
    category: "general",
    icon: "Calendar",
    priority: 5,
  },
];

export function useChatSuggestions({
  messages,
  pinnedContractId,
  contractTitle,
  isLoading = false,
}: UseChatSuggestionsProps) {
  const pathname = usePathname();
  const [suggestions, setSuggestions] = useState<Suggestion[]>(
    STATIC_FAQ_SUGGESTIONS
  );
  const [fetchingSuggestions, setFetchingSuggestions] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // Analyze current page context for better suggestions
  const getPageContext = useCallback(() => {
    const context = {
      path: pathname,
      stage: "general",
      description: "",
    };

    if (pathname.includes("/contract-concierge/discovery")) {
      context.stage = "discovery";
      context.description = "Contract discovery and initial analysis";
    } else if (pathname.includes("/contract-concierge/benchmark")) {
      context.stage = "benchmarking";
      context.description = "Contract benchmarking and comparison";
    } else if (
      pathname.includes("/contract-management/contracts") &&
      !pathname.includes("/view/")
    ) {
      context.stage = "portfolio_management";
      context.description = "Contract portfolio management and organization";
    } else if (pathname.includes("/contract-management/contracts/view/")) {
      context.stage = "contract_review";
      context.description = "Individual contract review and analysis";
    } else if (pathname.includes("/contract-management/renewals")) {
      context.stage = "renewals";
      context.description = "Contract renewals and lifecycle management";
    } else if (pathname.includes("/contract-management/compliance")) {
      context.stage = "compliance";
      context.description = "Compliance monitoring and risk management";
    } else if (pathname.includes("/contract-management/obligations")) {
      context.stage = "obligations";
      context.description = "Obligation tracking and performance monitoring";
    } else if (pathname.includes("/contract-management/financials")) {
      context.stage = "financials";
      context.description = "Financial management and cost optimization";
    } else if (pathname.includes("/cockpit")) {
      context.stage = "analytics";
      context.description = "Strategic analytics and insights";
    } else if (pathname.includes("/administration")) {
      context.stage = "administration";
      context.description = "System administration and configuration";
    }

    return context;
  }, [pathname]);

  // Get enhanced fallback suggestions based on page context
  const getFallbackSuggestions = useCallback(
    (pageContext: any, contractId?: string): Suggestion[] => {
      const baseId = `fallback-${pageContext.stage}`;

      if (contractId) {
        // Contract-specific fallbacks based on page context
        switch (pageContext.stage) {
          case "contract_review":
            return [
              {
                id: `${baseId}-1`,
                text: "Summarize the key terms for me",
                category: "review",
                icon: "FileText",
                priority: 1,
              },
              {
                id: `${baseId}-2`,
                text: "What should I be concerned about?",
                category: "review",
                icon: "AlertTriangle",
                priority: 2,
              },
            ];
          case "renewals":
            return [
              {
                id: `${baseId}-1`,
                text: "When does this contract expire?",
                category: "renewals",
                icon: "Calendar",
                priority: 1,
              },
            ];
          default:
            return [
              {
                id: `${baseId}-1`,
                text: "Help me understand this contract",
                category: "followup",
                icon: "FileText",
                priority: 1,
              },
            ];
        }
      } else {
        // General fallbacks based on page context
        switch (pageContext.stage) {
          case "discovery":
            return [
              {
                id: `${baseId}-1`,
                text: "How do I get started with contract analysis?",
                category: "discovery",
                icon: "Upload",
                priority: 1,
              },
            ];
          case "portfolio_management":
            return [
              {
                id: `${baseId}-1`,
                text: "Help me organize my contracts better",
                category: "management",
                icon: "BarChart3",
                priority: 1,
              },
            ];
          case "renewals":
            return [
              {
                id: `${baseId}-1`,
                text: "Which of my contracts are expiring soon?",
                category: "renewals",
                icon: "Calendar",
                priority: 1,
              },
            ];
          case "compliance":
            return [
              {
                id: `${baseId}-1`,
                text: "How do I stay compliant with my agreements?",
                category: "compliance",
                icon: "Shield",
                priority: 1,
              },
            ];
          default:
            return [
              {
                id: `${baseId}-1`,
                text: "What can you help me with?",
                category: "general",
                icon: "MessageSquare",
                priority: 1,
              },
            ];
        }
      }
    },
    []
  );

  // Fetch AI-generated suggestions from backend (only called manually after user interaction)
  const fetchContextualSuggestions = useCallback(async () => {
    if (fetchingSuggestions || isLoading) return;

    setFetchingSuggestions(true);
    try {
      const pageContext = getPageContext();

      const response = await apiClient.post("/api/gemini/suggestions", {
        context: pathname,
        contractId: pinnedContractId,
        conversationHistory: messages.slice(-4), // Send last 4 messages for context
        pageContext: pageContext, // Additional context for better suggestions
      });

      if (response && response.success && response.suggestions) {
        // Add icons to suggestions based on category and limit to 3 suggestions
        const suggestionsWithIcons = response.suggestions
          .slice(0, 3) // Limit to maximum 3 suggestions
          .map((suggestion: any) => ({
            ...suggestion,
            icon: getIconForCategory(suggestion.category, pageContext.stage),
          }));

        setSuggestions(suggestionsWithIcons);
        setHasUserInteracted(true);
      } else {
        // Fallback to context-based suggestions
        const pageContext = getPageContext();
        const fallbackSuggestions: Suggestion[] = getFallbackSuggestions(
          pageContext,
          pinnedContractId
        ).slice(0, 3); // Limit to 3 suggestions
        setSuggestions(fallbackSuggestions);
        setHasUserInteracted(true);
      }
    } catch (error) {
      console.error("Error fetching suggestions:", error);
      // Enhanced fallback suggestions based on page context
      const pageContext = getPageContext();
      const fallbackSuggestions: Suggestion[] = getFallbackSuggestions(
        pageContext,
        pinnedContractId
      ).slice(0, 3); // Limit to 3 suggestions
      setSuggestions(fallbackSuggestions);
      setHasUserInteracted(true);
    } finally {
      setFetchingSuggestions(false);
    }
  }, [
    fetchingSuggestions,
    isLoading,
    pathname,
    pinnedContractId,
    messages,
    getPageContext,
  ]);

  // Helper function to assign icons based on category and stage
  const getIconForCategory = useCallback(
    (category: string, stage?: string): string => {
      // Stage-specific icons take precedence
      if (stage) {
        switch (stage) {
          case "discovery":
            return "Search";
          case "benchmarking":
            return "BarChart3";
          case "portfolio_management":
            return "FolderOpen";
          case "contract_review":
            return "FileText";
          case "renewals":
            return "Calendar";
          case "compliance":
            return "Shield";
          case "obligations":
            return "CheckSquare";
          case "financials":
            return "DollarSign";
          case "analytics":
            return "TrendingUp";
          case "administration":
            return "Settings";
        }
      }

      // Fallback to category-based icons
      switch (category) {
        case "discovery":
          return "Search";
        case "analysis":
          return "BarChart3";
        case "management":
          return "FolderOpen";
        case "review":
          return "FileText";
        case "renewals":
          return "Calendar";
        case "compliance":
          return "Shield";
        case "obligations":
          return "CheckSquare";
        case "financials":
          return "DollarSign";
        case "analytics":
          return "TrendingUp";
        case "admin":
          return "Settings";
        case "contract":
          return "FileText";
        case "general":
          return "MessageSquare";
        case "followup":
          return "Lightbulb";
        case "proactive":
          return "BarChart3";
        default:
          return "MessageSquare";
      }
    },
    []
  );

  // Function to trigger contextual suggestions after user interaction
  const triggerContextualSuggestions = useCallback(() => {
    // Fetch contextual suggestions after any user message (not just the first one)
    const userMessages = messages.filter((msg) => msg.role === "user");
    if (userMessages.length > 0) {
      fetchContextualSuggestions();
    }
  }, [messages, fetchContextualSuggestions]);

  return {
    suggestions,
    setSuggestions,
    fetchContextualSuggestions,
    triggerContextualSuggestions,
    hasUserInteracted,
  };
}
