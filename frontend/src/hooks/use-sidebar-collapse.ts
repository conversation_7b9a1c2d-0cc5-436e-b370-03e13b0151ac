"use client";

import { useState, useEffect } from "react";

const SIDEBAR_COLLAPSED_KEY = "sidebar-collapsed";

export function useSidebarCollapse() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load initial state from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(SIDEBAR_COLLAPSED_KEY);
      if (stored !== null) {
        setIsCollapsed(JSON.parse(stored));
      } else {
        // Default to expanded state if no preference is stored
        setIsCollapsed(false);
      }
    } catch (error) {
      console.error("Error loading sidebar state:", error);
      // Default to expanded state on error
      setIsCollapsed(false);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(
          SIDEBAR_COLLAPSED_KEY,
          JSON.stringify(isCollapsed)
        );
      } catch (error) {
        console.error("Error saving sidebar state:", error);
      }
    }
  }, [isCollapsed, isLoaded]);

  const toggle = () => {
    setIsCollapsed((prev) => !prev);
  };

  const collapse = () => {
    setIsCollapsed(true);
  };

  const expand = () => {
    setIsCollapsed(false);
  };

  return {
    isCollapsed,
    isLoaded,
    toggle,
    collapse,
    expand,
  };
}
