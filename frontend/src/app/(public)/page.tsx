import Image from "next/image";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
  FileText,
  LineChart,
  Users,
  Key,
  Shield,
  Zap,
  BarChart4,
  Clock,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-32 bg-gradient-to-br from-background via-background to-primary/5">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-secondary/5 rounded-full blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/2 w-1/4 h-1/4 bg-accent/5 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
            <div className="flex flex-col justify-center space-y-4">
              <Badge className="w-fit mb-2 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
                AI-Powered B2B2B Platform
              </Badge>
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                  Unified Contract & License Management
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  The complete platform for managing your contracts and licenses
                  from creation to renewal with AI-powered insights and
                  compliance monitoring.
                </p>
              </div>
              <div className="flex flex-wrap gap-4 py-4">
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-primary/10 p-1">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium">
                    Contract Lifecycle
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-primary/10 p-1">
                    <Key className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium">
                    License Management
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-primary/10 p-1">
                    <Shield className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium">
                    Compliance Monitoring
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-primary/10 p-1">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium">
                    AI-Powered Analysis
                  </span>
                </div>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Link href="/auth/register">
                  <Button size="lg" className="w-full min-[400px]:w-auto">
                    Start Free Trial
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/features">
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full min-[400px]:w-auto"
                  >
                    Explore Features
                  </Button>
                </Link>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1.5">
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                  <span>Free 14-day trial</span>
                </div>
                <div className="flex items-center space-x-1.5">
                  <CheckCircle2 className="h-4 w-4 text-primary" />
                  <span>No credit card required</span>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[350px] w-full md:h-[420px] lg:h-[650px]">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg shadow-lg overflow-hidden">
                  <div className="absolute inset-0 bg-muted/80 backdrop-blur-sm flex items-center justify-center">
                    <div className="w-4/5 h-4/5 bg-background rounded-lg shadow-lg p-4 flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <div className="h-3 w-3 rounded-full bg-primary"></div>
                          <div className="text-sm font-medium">
                            Unified Dashboard
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <div className="h-2 w-2 rounded-full bg-muted-foreground/30"></div>
                          <div className="h-2 w-2 rounded-full bg-muted-foreground/30"></div>
                          <div className="h-2 w-2 rounded-full bg-muted-foreground/30"></div>
                        </div>
                      </div>
                      <div className="flex-1 grid grid-cols-2 gap-3">
                        <div className="bg-muted/50 rounded p-2">
                          <div className="flex items-center gap-1 mb-2">
                            <FileText className="h-3 w-3 text-primary" />
                            <div className="h-2 w-1/2 bg-muted-foreground/20 rounded"></div>
                          </div>
                          <div className="h-8 bg-primary/20 rounded"></div>
                        </div>
                        <div className="bg-muted/50 rounded p-2">
                          <div className="flex items-center gap-1 mb-2">
                            <Key className="h-3 w-3 text-secondary" />
                            <div className="h-2 w-1/2 bg-muted-foreground/20 rounded"></div>
                          </div>
                          <div className="h-8 bg-secondary/20 rounded"></div>
                        </div>
                        <div className="bg-muted/50 rounded p-2 col-span-2">
                          <div className="flex items-center gap-1 mb-2">
                            <BarChart4 className="h-3 w-3 text-muted-foreground" />
                            <div className="h-2 w-1/3 bg-muted-foreground/20 rounded"></div>
                          </div>
                          <div className="h-20 bg-muted-foreground/10 rounded flex items-center justify-center">
                            <div className="w-4/5 h-4/5 flex flex-col space-y-1">
                              <div className="h-2 w-full bg-muted-foreground/20 rounded"></div>
                              <div className="h-2 w-4/5 bg-muted-foreground/20 rounded"></div>
                              <div className="h-2 w-3/5 bg-muted-foreground/20 rounded"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 py-20 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/4 right-1/4 w-1/3 h-1/3 bg-secondary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <div className="inline-block rounded-lg bg-secondary/10 text-secondary px-3 py-1 text-sm font-medium">
                Key Features
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                Unified Contract & License Management
              </h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Our platform provides a comprehensive set of tools to manage
                your contracts and licenses throughout their lifecycle.
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-primary/20 to-primary/10">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Contract Lifecycle</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Manage contracts from creation to renewal with centralized
                  storage, version history, and automated workflows.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#contract"
                  className="text-primary hover:text-primary/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-secondary/20 to-secondary/10">
                  <Key className="h-6 w-6 text-secondary" />
                </div>
                <CardTitle>License Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Track licenses, monitor compliance, manage entitlements, and
                  automate renewals in one unified platform.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#license"
                  className="text-secondary hover:text-secondary/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-accent/20 to-accent/10">
                  <LineChart className="h-6 w-6 text-accent" />
                </div>
                <CardTitle>AI-Powered Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Extract insights, identify risks, and generate summaries from
                  contracts and licenses using advanced AI.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#ai"
                  className="text-accent hover:text-accent/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-primary/20 to-primary/10">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Compliance Monitoring</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Ensure compliance with automated monitoring, alerts for
                  potential issues, and comprehensive audit trails.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#security"
                  className="text-primary hover:text-primary/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-secondary/20 to-secondary/10">
                  <Users className="h-6 w-6 text-secondary" />
                </div>
                <CardTitle>Enterprise Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Manage users, roles, departments, and security settings with
                  comprehensive enterprise administration tools.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#enterprise"
                  className="text-secondary hover:text-secondary/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-accent/20 to-accent/10">
                  <BarChart4 className="h-6 w-6 text-accent" />
                </div>
                <CardTitle>Advanced Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Gain insights with customizable dashboards, reports, and
                  forecasting for contracts and licenses.
                </p>
              </CardContent>
              <CardFooter>
                <Link
                  href="/features#analytics"
                  className="text-accent hover:text-accent/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">
                Unified Platform
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                How It Works
              </h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Our platform simplifies contract and license management with a
                unified approach.
              </p>
            </div>
          </div>

          <div className="mx-auto max-w-6xl py-12">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-center md:text-left">
                  Contract Lifecycle
                </h3>
                <div className="space-y-8">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        1
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">Create & Author</h4>
                      <p className="text-muted-foreground">
                        Create contracts from templates or upload existing ones.
                        Use AI-assisted authoring and clause recommendations.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        2
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">
                        Negotiate & Approve
                      </h4>
                      <p className="text-muted-foreground">
                        Collaborate on redlines, track changes, and route for
                        approvals through customizable workflows.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        3
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">Execute & Store</h4>
                      <p className="text-muted-foreground">
                        Securely execute with e-signatures and store in the
                        centralized repository with version control.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        4
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">Monitor & Renew</h4>
                      <p className="text-muted-foreground">
                        Track obligations, receive renewal alerts, and analyze
                        performance with AI-powered insights.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-center md:text-left">
                  License Management
                </h3>
                <div className="space-y-8">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        1
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">
                        Catalog & Organize
                      </h4>
                      <p className="text-muted-foreground">
                        Import and catalog licenses with metadata extraction.
                        Organize by type, vendor, department, or custom
                        categories.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        2
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">
                        Track Entitlements
                      </h4>
                      <p className="text-muted-foreground">
                        Manage license entitlements, track usage, and allocate
                        licenses to users or departments.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        3
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">
                        Monitor Compliance
                      </h4>
                      <p className="text-muted-foreground">
                        Ensure compliance with license terms, receive alerts for
                        potential violations, and maintain audit trails.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-primary-foreground">
                        4
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold">
                        Optimize & Renew
                      </h4>
                      <p className="text-muted-foreground">
                        Identify optimization opportunities, forecast needs, and
                        manage renewals with automated workflows.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-16 text-center">
              <Link href="/features">
                <Button size="lg">
                  Explore All Features
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 py-20 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/3 left-1/4 w-1/3 h-1/3 bg-accent/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <div className="inline-block rounded-lg bg-accent/10 text-accent px-3 py-1 text-sm font-medium">
                Testimonials
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                Trusted by Leading Companies
              </h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                See what our customers have to say about our unified contract
                and license management platform.
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                    <span className="text-primary font-semibold">SJ</span>
                  </div>
                  <div>
                    <CardTitle className="text-base">Sarah Johnson</CardTitle>
                    <CardDescription>Legal Director, TechCorp</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  "Aptio has transformed how we manage contracts. The AI
                  analysis saves us hours of review time and has helped us
                  identify risks we would have missed. The unified platform
                  gives us complete visibility across our entire portfolio."
                </p>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-secondary/20 to-secondary/10 flex items-center justify-center">
                    <span className="text-secondary font-semibold">MC</span>
                  </div>
                  <div>
                    <CardTitle className="text-base">Michael Chen</CardTitle>
                    <CardDescription>COO, Global Services Inc.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  "The license management capabilities have been a game-changer
                  for our IT department. We've reduced compliance risks and
                  optimized our software spend by identifying unused licenses.
                  The integration with our contract management workflow is
                  seamless."
                </p>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-accent/20 to-accent/10 flex items-center justify-center">
                    <span className="text-accent font-semibold">ER</span>
                  </div>
                  <div>
                    <CardTitle className="text-base">Emily Rodriguez</CardTitle>
                    <CardDescription>
                      Procurement Manager, Retail Co.
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  "The renewal management features have eliminated missed
                  renewals for both contracts and licenses, saving us thousands
                  in unnecessary costs. The enterprise admin tools make it easy
                  to manage permissions across our global organization."
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/3 right-1/4 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 left-1/4 w-1/3 h-1/3 bg-secondary/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="space-y-3">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary via-secondary to-accent">
                Ready to Unify Your Contract & License Management?
              </h2>
              <p className="max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Start your 14-day free trial today and experience the power of
                our unified platform with enterprise and platform administration
                capabilities.
              </p>
            </div>
            <div className="flex flex-col gap-3 min-[400px]:flex-row mt-4">
              <Link href="/auth/register">
                <Button
                  size="lg"
                  className="w-full min-[400px]:w-auto bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all"
                >
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full min-[400px]:w-auto border-primary/20 hover:border-primary/40 transition-all"
                >
                  Schedule a Demo
                </Button>
              </Link>
            </div>
            <div className="mt-4 flex flex-wrap justify-center gap-4">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-primary" />
                <span className="text-sm">Contract Lifecycle Management</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-primary" />
                <span className="text-sm">License Management</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-primary" />
                <span className="text-sm">Enterprise Administration</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-primary" />
                <span className="text-sm">Platform Administration</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
