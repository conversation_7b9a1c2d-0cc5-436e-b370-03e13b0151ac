import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Check, HelpCircle } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export const metadata = {
  title: "Pricing - Aptio Contract Lifecycle Management",
  description: "Flexible pricing plans for businesses of all sizes",
};

export default function PricingPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-28 bg-gradient-to-br from-background via-background to-primary/10">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-0 right-0 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl transform translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl transform -translate-x-1/2 translate-y-1/2"></div>
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                Coming Soon!
              </h1>
              <p className="max-w-[800px] text-muted-foreground md:text-xl">
                Choose the plan that's right for your business, with no hidden
                fees or surprises.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      {/* <section className="py-12 md:py-16"> */}
      {/* <div className="container px-4 md:px-6"> */}
      {/* <Tabs defaultValue="monthly" className="w-full"> */}
      {/* <div className="flex justify-center mb-8">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
                <TabsTrigger value="annual">Annual (Save 20%)</TabsTrigger>
              </TabsList>
            </div> */}

      {/* Monthly Pricing */}
      {/* <TabsContent value="monthly" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card className="border-0 shadow-md">
                  <CardHeader>
                    <CardTitle>Starter</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">$49</span>
                      <span className="text-muted-foreground">
                        /month per user
                      </span>
                    </div>
                    <CardDescription>
                      Perfect for small teams and startups
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Up to 50 contracts</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic contract repository</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Standard templates</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic approval workflows</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Email notifications</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>5GB storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Standard support</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link href="/auth/register?plan=starter" className="w-full">
                      <Button size="lg" variant="outline" className="w-full">
                        Start Free Trial
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-0 shadow-lg relative before:absolute before:inset-0 before:rounded-lg before:p-[1px] before:bg-gradient-to-r before:from-primary/20 before:via-primary/40 before:to-secondary/20 before:-z-10">
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-primary to-secondary text-primary-foreground px-3 py-1 text-xs font-medium rounded-bl-lg rounded-tr-lg">
                    Most Popular
                  </div>
                  <CardHeader>
                    <CardTitle>Professional</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">$99</span>
                      <span className="text-muted-foreground">
                        /month per user
                      </span>
                    </div>
                    <CardDescription>
                      Ideal for growing businesses
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Unlimited contracts</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced contract repository</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom templates</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced approval workflows</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic AI analysis</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>20GB storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Priority support</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic analytics</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link
                      href="/auth/register?plan=professional"
                      className="w-full"
                    >
                      <Button size="lg" className="w-full">
                        Start Free Trial
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-0 shadow-md">
                  <CardHeader>
                    <CardTitle>Enterprise</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">Custom</span>
                    </div>
                    <CardDescription>
                      For large organizations with complex needs
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Everything in Professional</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Multi-tenant architecture</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced AI analysis</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom integrations</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced analytics</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Unlimited storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Dedicated account manager</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>24/7 premium support</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom deployment options</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link href="/contact" className="w-full">
                      <Button size="lg" variant="outline" className="w-full">
                        Contact Sales
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent> */}

      {/* Annual Pricing */}
      {/* <TabsContent value="annual" className="space-y-4">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card className="border-0 shadow-md">
                  <CardHeader>
                    <CardTitle>Starter</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">$39</span>
                      <span className="text-muted-foreground">
                        /month per user
                      </span>
                    </div>
                    <CardDescription>
                      Perfect for small teams and startups
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Up to 50 contracts</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic contract repository</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Standard templates</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic approval workflows</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Email notifications</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>5GB storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Standard support</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link
                      href="/auth/register?plan=starter-annual"
                      className="w-full"
                    >
                      <Button size="lg" variant="outline" className="w-full">
                        Start Free Trial
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-0 shadow-lg relative before:absolute before:inset-0 before:rounded-lg before:p-[1px] before:bg-gradient-to-r before:from-primary/20 before:via-primary/40 before:to-secondary/20 before:-z-10">
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-primary to-secondary text-primary-foreground px-3 py-1 text-xs font-medium rounded-bl-lg rounded-tr-lg">
                    Most Popular
                  </div>
                  <CardHeader>
                    <CardTitle>Professional</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">$79</span>
                      <span className="text-muted-foreground">
                        /month per user
                      </span>
                    </div>
                    <CardDescription>
                      Ideal for growing businesses
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Unlimited contracts</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced contract repository</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom templates</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced approval workflows</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic AI analysis</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>20GB storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Priority support</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Basic analytics</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link
                      href="/auth/register?plan=professional-annual"
                      className="w-full"
                    >
                      <Button size="lg" className="w-full">
                        Start Free Trial
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-0 shadow-md">
                  <CardHeader>
                    <CardTitle>Enterprise</CardTitle>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">Custom</span>
                    </div>
                    <CardDescription>
                      For large organizations with complex needs
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Everything in Professional</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Multi-tenant architecture</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced AI analysis</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom integrations</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Advanced analytics</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Unlimited storage</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Dedicated account manager</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>24/7 premium support</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="mr-2 h-4 w-4 text-primary mt-0.5" />
                        <span>Custom deployment options</span>
                      </li>
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Link href="/contact" className="w-full">
                      <Button size="lg" variant="outline" className="w-full">
                        Contact Sales
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent> */}
      {/* </Tabs> */}
      {/* </div> */}
      {/* </section> */}

      {/* FAQ Section */}
      {/* <section className="py-20 bg-gradient-to-br from-background via-background to-primary/5 relative">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/3 right-1/4 w-1/4 h-1/4 bg-primary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 left-1/4 w-1/4 h-1/4 bg-secondary/5 rounded-full blur-3xl"></div>
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                Frequently Asked Questions
              </h2>
              <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Find answers to common questions about our pricing and plans.
              </p>
            </div>
          </div>

          <div className="mx-auto max-w-3xl space-y-8">
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-primary">
                Can I change plans later?
              </h3>
              <p className="text-muted-foreground">
                Yes, you can upgrade or downgrade your plan at any time. When
                upgrading, the new features will be available immediately. When
                downgrading, changes will take effect at the start of your next
                billing cycle.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-primary">
                Is there a limit to the number of users?
              </h3>
              <p className="text-muted-foreground">
                No, all plans allow for unlimited users. Pricing is based on a
                per-user basis, so you only pay for the users you add to the
                system.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-primary">
                Do you offer discounts for non-profits or educational
                institutions?
              </h3>
              <p className="text-muted-foreground">
                Yes, we offer special pricing for non-profit organizations and
                educational institutions. Please contact our sales team for more
                information.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-primary">
                What payment methods do you accept?
              </h3>
              <p className="text-muted-foreground">
                We accept all major credit cards, including Visa, Mastercard,
                American Express, and Discover. For Enterprise plans, we also
                offer invoicing options.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-bold text-primary">
                Can I cancel my subscription at any time?
              </h3>
              <p className="text-muted-foreground">
                Yes, you can cancel your subscription at any time. If you
                cancel, you'll have access to your plan until the end of your
                current billing period.
              </p>
            </div>
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      {/* <section className="py-20 bg-gradient-to-br from-primary/10 via-background to-secondary/10 relative">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/3 right-1/4 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-1/3 left-1/4 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl"></div>
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                Ready to Get Started?
              </h2>
              <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Start your 14-day free trial today with no credit card required.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/auth/register">
                <Button size="lg" className="w-full min-[400px]:w-auto">
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full min-[400px]:w-auto"
                >
                  Contact Sales
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section> */}
    </>
  );
}
