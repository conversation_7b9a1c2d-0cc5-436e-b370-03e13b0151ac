import Link from "next/link";
import {
  Users,
  Shield,
  Zap,
  Globe,
  Brain,
  CandlestickChart,
  CircleUserRound,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const metadata = {
  title: "About Us - MAIT Contract Lifecycle Management",
  description:
    "Learn about our mission and the team behind MAIT's Contract Lifecycle Management platform",
};

export default function AboutPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-28 bg-gradient-to-br from-background via-background to-primary/10">
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-0 right-0 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl transform translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl transform -translate-x-1/2 translate-y-1/2"></div>
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h3 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                About MAIT
              </h3>
              <p className="max-w-[800px] text-muted-foreground md:text-xl">
                Smarter sourcing. Contract intelligence. Optimal renewals.
                <br></br>
                Live insights, all in one platform.

              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About MAIT */}
      <section className="py-20" >
        <div className="container px-4 md:px-6">
          <div className="space-y-6">
            <p className="text-muted-foreground text-lg">
              Multistrat's AI Tool (MAIT) transforms procurement practices. MAIT's AI powered capabilities uncover hidden value, reduce risk, and deliver real-time insights, optimising organisations' IT contracts. Designed for larger teams, procurement leaders, vendor managers, and individual consultants alike, MAIT turns static agreements into strategic assets, bridging the gap between what was signed and what is actually delivered.
            </p>

            <p className="text-muted-foreground text-lg">
              Behind the development of MAIT is Multistrat's industry experts, leveraging over 10 years of experience. Established in 2014, Multistrat is a boutique IT procurement consulting and managed services firm, operating across the full breadth of technology sourcing and supplier management activities.
            </p>

            <p className="text-muted-foreground text-lg">
              The DNA of Multistrat is to identify, cultivate, and realise optimal value. Through our various engagements we log the contracts we address and the savings we realise.
            </p>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="bg-muted/40 py-20">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter">
                Our Values
              </h2>
              {/* <p className="max-w-[800px] text-muted-foreground md:text-xl">
                The principles that guide everything we do
              </p> */}
            </div>
          </div>


          <div className="mx-auto flex max-w-7xl gap-6 py-12 overflow-x-auto">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <CandlestickChart className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Focus on Value</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Identifying and delivering sustained value is paramount in our approach and execution for our customers.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Brain className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Democratise Insight</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Enabling efficient and accessible market and supplier side insights to enable customers to make better informed decisions.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <CircleUserRound className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Customer First</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We build our product based on real customer needs and
                  feedback, ensuring that every feature delivers tangible value.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Shield className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Security & Trust</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We treat your data with the utmost care, implementing
                  enterprise-grade security measures to protect your sensitive
                  contract information.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Zap className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Innovation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We continuously push the boundaries of what's possible in
                  contract management, leveraging the latest technologies to
                  solve complex problems.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Globe className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">Accessibility</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We believe sophisticated contract management should be
                  accessible to businesses of all sizes, not just large
                  enterprises with extensive resources.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      {/* <section className="py-20">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter">
                Our Leadership Team
              </h2>
              <p className="max-w-[800px] text-muted-foreground md:text-xl">
                Meet the experienced team behind MAIT
              </p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex flex-col items-center text-center">
              <div className="mb-4 h-32 w-32 overflow-hidden rounded-full bg-muted">
                <div className="flex h-full items-center justify-center">
                  <span className="text-muted-foreground">Photo</span>
                </div>
              </div>
              <h3 className="text-xl font-bold">Sarah Johnson</h3>
              <p className="text-muted-foreground">CEO & Co-Founder</p>
              <p className="mt-2 text-sm text-muted-foreground">
                Former legal tech executive with 15+ years of experience in
                contract management solutions.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="mb-4 h-32 w-32 overflow-hidden rounded-full bg-muted">
                <div className="flex h-full items-center justify-center">
                  <span className="text-muted-foreground">Photo</span>
                </div>
              </div>
              <h3 className="text-xl font-bold">Michael Chen</h3>
              <p className="text-muted-foreground">CTO & Co-Founder</p>
              <p className="mt-2 text-sm text-muted-foreground">
                AI and machine learning expert who previously led engineering
                teams at leading tech companies.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="mb-4 h-32 w-32 overflow-hidden rounded-full bg-muted">
                <div className="flex h-full items-center justify-center">
                  <span className="text-muted-foreground">Photo</span>
                </div>
              </div>
              <h3 className="text-xl font-bold">Emily Rodriguez</h3>
              <p className="text-muted-foreground">Chief Product Officer</p>
              <p className="mt-2 text-sm text-muted-foreground">
                Product leader with a background in legal operations and SaaS
                platform development.
              </p>
            </div>
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="bg-muted/40 py-20">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Join Us on Our Mission
              </h2>
              <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Experience how MAIT can transform your contract management
                processes.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/auth/register">
                <Button
                  size="lg"
                  className="w-full min-[400px]:w-auto "
                >
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full min-[400px]:w-auto border-green-700 text-green-700 hover:bg-green-50"
                >
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
