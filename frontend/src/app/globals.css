@tailwind base;
@tailwind components;
@tailwind utilities;

/* Aptos font family declarations - Use only light weight to prevent artificial boldness */
@font-face {
  font-family: "Aptos";
  src: url("/aptos-light.ttf") format("truetype"), local("Aptos Light"),
    local("Aptos-Light"), local("Aptos");
  font-weight: 100 900; /* Map all weights to the same light font */
  font-style: normal;
  font-display: swap;
}

@layer base {
  h1 {
    @apply text-4xl font-light mb-6;
  }

  h2 {
    @apply text-3xl font-light mb-4;
  }

  h3 {
    @apply text-2xl font-light mb-3;
  }

  h4 {
    @apply text-xl font-light mb-2;
  }

  p {
    @apply mb-4;
  }

  a {
    @apply text-custom-green hover:text-custom-green/80;
  }

  :root {
    /* White background with custom green text (#09260D) */
    --background: 0 0% 100%; /* White background */
    --foreground: 142 85% 8%; /* Custom green #09260D converted to HSL */
    --card: 0 0% 100%; /* White cards */
    --card-foreground: 142 85% 8%; /* Custom green text */
    --popover: 0 0% 100%; /* White popover */
    --popover-foreground: 142 85% 8%; /* Custom green text */
    --primary: 142 85% 8%; /* Custom green primary */
    --primary-foreground: 0 0% 98%; /* White text on primary */
    --secondary: 142 20% 90%; /* Light green secondary for better contrast */
    --secondary-foreground: 142 85% 8%; /* Dark green text on light secondary */
    --muted: 142 10% 96%; /* Very light muted background */
    --muted-foreground: 142 40% 25%; /* Darker green for muted text */
    --accent: 142 20% 90%; /* Light green accent */
    --accent-foreground: 142 85% 8%; /* Custom green text */
    --destructive: 0 84.2% 60.2%; /* Keep red for destructive */
    --destructive-foreground: 0 0% 98%; /* White text on destructive */
    --border: 142 15% 85%; /* Light green borders */
    --input: 142 10% 95%; /* Very light green input background */
    --ring: 142 85% 8%; /* Custom green focus ring */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 100%; /* White sidebar background */
    --sidebar-foreground: 142 85% 8%; /* Custom green text on sidebar */
    --sidebar-primary: 142 85% 8%; /* Custom green primary */
    --sidebar-primary-foreground: 0 0% 98%; /* White text on primary */
    --sidebar-accent: 142 20% 90%; /* Light green accent for proper contrast */
    --sidebar-accent-foreground: 142 85% 8%; /* Dark green text on light accent */
    --sidebar-border: 142 15% 85%; /* Light green border */
    --sidebar-ring: 142 85% 8%; /* Custom green ring */
    --sidebar-disabled: 142 30% 60%; /* Muted green for disabled */
  }

  /* Dark mode - single dark green color everywhere */
  .dark {
    --background: 142 85% 8%; /* Dark green background */
    --foreground: 0 0% 98%; /* White text */
    --card: 142 85% 8%; /* Same dark green for cards - no variation */
    --card-foreground: 0 0% 98%; /* White text on cards */
    --popover: 142 85% 8%; /* Same dark green for popover */
    --popover-foreground: 0 0% 98%; /* White text on popover */
    --primary: 142 85% 8%; /* Same dark green for primary */
    --primary-foreground: 0 0% 98%; /* White text on primary */
    --secondary: 142 85% 8%; /* Same dark green for secondary */
    --secondary-foreground: 0 0% 98%; /* White text on secondary */
    --muted: 142 85% 8%; /* Same dark green for muted */
    --muted-foreground: 0 0% 70%; /* Light gray muted text */
    --accent: 142 85% 8%; /* Same dark green for accent */
    --accent-foreground: 0 0% 98%; /* White text on accent */
    --destructive: 0 84.2% 60.2%; /* Keep red for destructive */
    --destructive-foreground: 0 0% 98%; /* White text on destructive */
    --border: 142 60% 20%; /* Slightly lighter border for visibility */
    --input: 142 85% 8%; /* Same dark green for input */
    --ring: 142 60% 20%; /* Slightly lighter ring for visibility */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 142 85% 8%; /* Same dark green for sidebar */
    --sidebar-foreground: 0 0% 98%; /* White text on sidebar */
    --sidebar-primary: 142 85% 8%; /* Same dark green for sidebar primary */
    --sidebar-primary-foreground: 0 0% 98%; /* White text on primary */
    --sidebar-accent: 142 85% 8%; /* Same dark green for sidebar accent */
    --sidebar-accent-foreground: 0 0% 98%; /* White text on accent */
    --sidebar-border: 142 60% 20%; /* Slightly lighter border for visibility */
    --sidebar-ring: 142 60% 20%; /* Slightly lighter ring for visibility */
    --sidebar-disabled: 142 30% 40%; /* Muted green for disabled */
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90;
  }

  .btn-success {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
  }

  .card {
    @apply bg-card rounded-lg shadow-md p-6;
  }

  .form-input {
    @apply mt-1 block w-full rounded-md border-border shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50;
  }

  .form-label {
    @apply block text-sm text-foreground mb-1;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  .animate-slide-in {
    animation: slideIn 0.5s ease-out;
  }

  .animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
  }

  /* Ultra-light font weights for Aptos font to prevent artificial boldness */
  .font-light {
    font-weight: 300;
  }

  .font-normal {
    font-weight: 300; /* Use light for normal */
  }

  .font-medium {
    font-weight: 300; /* Use light for medium */
  }

  .font-semibold {
    font-weight: 400; /* Use normal for semibold */
  }

  .font-bold {
    font-weight: 500; /* Use medium for bold */
  }

  .font-extrabold {
    font-weight: 600; /* Use semibold for extrabold */
  }

  /* Logo specific styling - slightly heavier for logo only */
  .logo-text {
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  /* Override any emerald/light green colors to use dark green theme */
  .bg-emerald-500,
  .bg-emerald-600,
  .bg-green-500,
  .bg-green-600 {
    background-color: hsl(var(--primary)) !important;
  }

  .hover\:bg-emerald-500:hover,
  .hover\:bg-emerald-600:hover,
  .hover\:bg-green-500:hover,
  .hover\:bg-green-600:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
  }

  .text-emerald-500,
  .text-emerald-600,
  .text-green-500,
  .text-green-600 {
    color: hsl(var(--primary)) !important;
  }

  .border-emerald-500,
  .border-emerald-600,
  .border-green-500,
  .border-green-600 {
    border-color: hsl(var(--primary)) !important;
  }

  /* Ensure proper contrast for sidebar navigation hover states */
  .sidebar-nav-item:hover {
    background-color: hsl(var(--sidebar-accent)) !important;
    color: hsl(var(--sidebar-accent-foreground)) !important;
  }

  /* Fix for sub-navigation items to ensure visibility on hover */
  [data-sidebar="sub-item"]:hover {
    background-color: hsl(var(--sidebar-accent)) !important;
    color: hsl(var(--sidebar-accent-foreground)) !important;
  }

  /* Dark mode specific overrides */
  .dark .bg-emerald-500,
  .dark .bg-emerald-600,
  .dark .bg-green-500,
  .dark .bg-green-600 {
    background-color: hsl(var(--primary)) !important;
  }

  .dark .hover\:bg-emerald-500:hover,
  .dark .hover\:bg-emerald-600:hover,
  .dark .hover\:bg-green-500:hover,
  .dark .hover\:bg-green-600:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
  }

  .dark .text-emerald-500,
  .dark .text-emerald-600,
  .dark .text-green-500,
  .dark .text-green-600 {
    color: hsl(var(--primary)) !important;
  }

  /* Ensure logo text is visible in both modes */
  .dark .logo-text {
    color: hsl(var(--foreground)) !important;
    text-shadow: 0 0 1.5px currentColor;
  }

  /* Dark mode gradient overrides for header logos */
  .dark .bg-gradient-to-r.from-green-900.to-green-700 {
    background: linear-gradient(
      to right,
      hsl(var(--foreground)),
      hsl(var(--foreground))
    ) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
  }

  /* Ensure NO white backgrounds in dark mode */
  .dark .bg-white {
    background-color: hsl(var(--background)) !important;
  }

  .dark .bg-gray-50,
  .dark .bg-gray-100,
  .dark .bg-slate-50,
  .dark .bg-slate-100 {
    background-color: hsl(var(--muted)) !important;
  }

  /* Override any potential white backgrounds in components */
  .dark [class*="bg-white"],
  .dark [style*="background-color: white"],
  .dark [style*="background-color: #fff"],
  .dark [style*="background-color: #ffffff"] {
    background-color: hsl(var(--background)) !important;
  }

  /* Ensure dropdowns and modals don't have white backgrounds */
  .dark .dropdown-content,
  .dark .modal-content,
  .dark .popover-content,
  .dark .tooltip-content {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
  }

  /* Override specific shadcn/ui components that might have white backgrounds */
  .dark [data-radix-popper-content-wrapper],
  .dark [data-radix-dropdown-menu-content],
  .dark [data-radix-select-content],
  .dark [data-radix-dialog-content],
  .dark [data-radix-popover-content] {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Ensure sheets and dialogs use dark backgrounds */
  .dark [data-radix-dialog-overlay],
  .dark .sheet-content,
  .dark .dialog-content {
    background-color: hsl(var(--background)) !important;
  }

  /* Override any remaining white backgrounds in cards or containers */
  .dark .card,
  .dark .container,
  .dark .panel,
  .dark .section {
    background-color: hsl(var(--card)) !important;
    color: hsl(var(--card-foreground)) !important;
  }

  /* Force single dark green color for all backgrounds in dark mode */
  .dark *[class*="bg-"],
  .dark .bg-muted,
  .dark .bg-card,
  .dark .bg-secondary,
  .dark .bg-accent {
    background-color: hsl(142 85% 8%) !important;
  }

  /* Only borders and rings can be slightly different for visibility */
  .dark .border,
  .dark [class*="border-"] {
    border-color: hsl(142 60% 20%) !important;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  /* Sidebar collapse transitions */
  .sidebar-transition {
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-collapsed {
    width: 4rem !important; /* 64px */
    min-width: 4rem !important;
  }

  .sidebar-expanded {
    width: 16rem !important; /* 256px */
    min-width: 16rem !important;
  }

  .main-content-transition {
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .main-content-collapsed {
    margin-left: 4rem; /* 64px */
  }

  .main-content-expanded {
    margin-left: 16rem; /* 256px */
  }

  /* Responsive main content classes */
  @media (min-width: 768px) {
    .md\\:main-content-collapsed {
      margin-left: 4rem !important; /* 64px */
    }

    .md\\:main-content-expanded {
      margin-left: 16rem !important; /* 256px */
    }
  }

  /* Hide text in collapsed sidebar */
  .sidebar-collapsed .sidebar-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
    transition: opacity 0.2s ease-in-out;
  }

  .sidebar-expanded .sidebar-text {
    opacity: 1;
    width: auto;
    transition: opacity 0.2s ease-in-out 0.1s;
  }

  /* Tooltip positioning for collapsed sidebar */
  .sidebar-collapsed .sidebar-tooltip {
    left: 4.5rem; /* Position tooltip to the right of collapsed sidebar */
  }

  /* Ensure smooth transitions for all sidebar elements */
  .sidebar-transition * {
    transition: all 0.2s ease-in-out;
  }

  /* Mobile responsive - always show expanded sidebar on mobile */
  @media (max-width: 768px) {
    .sidebar-collapsed {
      width: 16rem; /* Always expanded on mobile */
    }

    .main-content-collapsed {
      margin-left: 0; /* No margin on mobile */
    }

    .main-content-expanded {
      margin-left: 0; /* No margin on mobile */
    }
  }

  /* Ensure icons are properly centered in collapsed state */
  .sidebar-collapsed .flex.items-center.justify-center {
    min-height: 2.5rem; /* Ensure consistent height */
    width: 100%; /* Full width for better click area */
  }

  /* Ensure proper spacing between collapsed navigation items */
  .sidebar-collapsed nav > div > div {
    margin-bottom: 0.25rem; /* Add small gap between items */
  }

  /* Better visual feedback for collapsed items */
  .sidebar-collapsed button:hover,
  .sidebar-collapsed a:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease-in-out;
  }

  /* Smooth opacity transitions for text elements */
  .sidebar-text {
    transition: opacity 0.2s ease-in-out, width 0.2s ease-in-out;
  }

  /* Hide chevron icons in collapsed state */
  /* .sidebar-collapsed .h-4.w-4:not(.sidebar-toggle-icon) {
    display: none;
  } */

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--border) / 0.8);
  }

  /* Dark mode scrollbar adjustments */
  .dark .scrollbar-thin {
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--border) / 0.8);
  }

  /* Text wrapping utilities for table cells */
  .overflow-wrap-anywhere {
    overflow-wrap: anywhere;
  }

  .word-break-all {
    word-break: break-all;
  }

  .text-wrap-force {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
    white-space: normal;
  }

  /* Table cell text wrapping for fixed layouts */
  .table-cell-wrap {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
    white-space: normal;
    max-width: 0; /* Force width constraint */
    overflow: hidden;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Aptos", var(--font-sans), Inter, system-ui, sans-serif;
    font-weight: 300; /* Use light weight as default */
  }

  /* Utility class to explicitly use Aptos */
  .font-aptos {
    font-family: "Aptos", Inter, system-ui, sans-serif !important;
  }
}

/* Custom price change color classes */
@layer utilities {
  /* Green shades for price increases */
  .bg-green-25 {
    background-color: rgb(240 253 244); /* Very light green */
  }
  .text-green-600 {
    color: rgb(22 163 74);
  }

  /* Red shades for price decreases */
  .bg-red-25 {
    background-color: rgb(254 242 242); /* Very light red */
  }
  .text-red-600 {
    color: rgb(220 38 38);
  }
}
