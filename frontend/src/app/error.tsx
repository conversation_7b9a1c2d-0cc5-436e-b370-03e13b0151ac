"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeProvider } from "next-themes";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Create a client
const queryClient = new QueryClient();

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Unhandled error:", error);
  }, [error]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-background">
          <div className="w-full max-w-md p-6 bg-card text-card-foreground rounded-lg shadow-md">
            <h2 className="text-2xl font-bold text-destructive mb-4">
              Something went wrong
            </h2>
            <p className="text-muted-foreground mb-4">
              An unexpected error occurred. Our team has been notified.
            </p>
            {error.message && (
              <pre className="bg-muted text-muted-foreground p-4 rounded text-sm overflow-auto max-h-60 mb-4">
                {error.message}
              </pre>
            )}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button onClick={() => reset()} className="w-full">
                Try again
              </Button>
              <Button
                variant="outline"
                onClick={() => (window.location.href = "/")}
                className="w-full"
              >
                Go Home
              </Button>
            </div>
          </div>
        </div>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
