/**
 * Super Admin Layout
 * Layout for all super admin pages
 */

'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { redirect } from 'next/navigation';
import { SuperAdminLayout } from '@/components/layouts/SuperAdminLayout';

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

export default function SuperAdminLayoutWrapper({ children }: SuperAdminLayoutProps) {
  const { user, isLoading } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect if not super admin
  if (!user?.isSuperAdmin && user?.role !== 'SUPER_ADMIN') {
    redirect('/');
    return null;
  }

  return (
    <SuperAdminLayout>
      {children}
    </SuperAdminLayout>
  );
}
