"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

export default function ProjectsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Sourcing Projects
        </h1>
        <p className="text-muted-foreground">
          Manage and track sourcing projects and initiatives
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Projects Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Sourcing project management tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
