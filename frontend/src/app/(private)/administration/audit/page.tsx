"use client";

import Link from "next/link";
import {
  ArrowRight,
  BarChart3,
  Building,
  FileText,
  Key,
  Settings,
  Shield,
  Users,
  Globe,
  Server,
  Palette,
  Database,
  Mail,
  AlertTriangle,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

export default function AdminPage() {
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Platform Administration
          </h2>
          <p className="text-muted-foreground">
            Manage tenants, system settings, and platform configuration.
          </p>
        </div>
        <div className="flex gap-2">
          <div className="px-4 py-2 border rounded-md flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            System Settings
          </div>
          <div className="px-4 py-2 bg-primary text-primary-foreground rounded-md flex items-center">
            <Building className="mr-2 h-4 w-4" />
            Add Tenant
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">Across all tenants</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Healthy</div>
            <div className="flex items-center gap-1 mt-1">
              <Badge variant="outline" className="text-xs bg-green-500/10">
                All Systems Operational
              </Badge>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <div className="flex items-center gap-1 mt-1">
              <Badge variant="outline" className="text-xs bg-amber-500/10">
                Requires Attention
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="tenants" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tenants">Tenants</TabsTrigger>
          <TabsTrigger value="settings">System Settings</TabsTrigger>
          <TabsTrigger value="analytics">Platform Analytics</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="tenants" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Tenant Management</CardTitle>
                  <CardDescription>
                    Manage organizations using the platform
                  </CardDescription>
                </div>
                <div className="px-4 py-2 bg-primary text-primary-foreground rounded-md flex items-center">
                  <Building className="mr-2 h-4 w-4" />
                  Add Tenant
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-[1fr_auto_auto_auto_auto] gap-4 p-4 font-medium border-b">
                  <div>Organization</div>
                  <div>Users</div>
                  <div>Plan</div>
                  <div>Status</div>
                  <div className="text-right">Actions</div>
                </div>
                {[
                  {
                    name: "Acme Corporation",
                    users: 124,
                    plan: "Enterprise",
                    status: "Active",
                  },
                  {
                    name: "TechCorp Inc.",
                    users: 87,
                    plan: "Professional",
                    status: "Active",
                  },
                  {
                    name: "Global Services Ltd.",
                    users: 156,
                    plan: "Enterprise",
                    status: "Active",
                  },
                  {
                    name: "Retail Co.",
                    users: 42,
                    plan: "Professional",
                    status: "Active",
                  },
                  {
                    name: "Startup Innovations",
                    users: 18,
                    plan: "Starter",
                    status: "Trial",
                  },
                  {
                    name: "Manufacturing Group",
                    users: 95,
                    plan: "Enterprise",
                    status: "Active",
                  },
                  {
                    name: "Healthcare Solutions",
                    users: 73,
                    plan: "Professional",
                    status: "Active",
                  },
                  {
                    name: "Education Systems",
                    users: 64,
                    plan: "Professional",
                    status: "Payment Issue",
                  },
                ].map((tenant, i) => (
                  <div
                    key={i}
                    className="grid grid-cols-[1fr_auto_auto_auto_auto] gap-4 p-4 items-center border-b last:border-0"
                  >
                    <div className="font-medium">{tenant.name}</div>
                    <div className="text-sm">{tenant.users}</div>
                    <Badge variant="outline">{tenant.plan}</Badge>
                    <Badge
                      variant={
                        tenant.status === "Active"
                          ? "outline"
                          : tenant.status === "Trial"
                          ? "secondary"
                          : "destructive"
                      }
                    >
                      {tenant.status}
                    </Badge>
                    <div className="flex justify-end gap-2">
                      <Link
                        href={`/admin/tenants/${i + 1}`}
                        className="px-3 py-1 text-sm rounded-md hover:bg-muted"
                      >
                        Manage
                      </Link>
                      <div className="px-3 py-1 text-sm rounded-md hover:bg-muted cursor-pointer">
                        Login As
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing 8 of 18 tenants
              </div>
              <div className="flex gap-2">
                <div className="px-3 py-1 text-sm border rounded-md opacity-50 cursor-not-allowed">
                  Previous
                </div>
                <div className="px-3 py-1 text-sm border rounded-md cursor-pointer hover:bg-muted">
                  Next
                </div>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
                <CardDescription>
                  Configure global platform settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Platform URL
                        </span>
                      </div>
                      <span className="text-sm">app.aptio.com</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Database className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Database Version
                        </span>
                      </div>
                      <span className="text-sm">v4.2.1</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">API Version</span>
                      </div>
                      <span className="text-sm">v2.8.0</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Email Provider
                        </span>
                      </div>
                      <span className="text-sm">SendGrid</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Default Security Policy
                        </span>
                      </div>
                      <span className="text-sm">Enterprise</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full px-4 py-2 border rounded-md flex items-center justify-center cursor-pointer hover:bg-muted">
                  <Settings className="mr-2 h-4 w-4" />
                  Edit System Settings
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Default Configurations</CardTitle>
                <CardDescription>
                  Default settings for new tenants
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Contract Templates
                        </span>
                      </div>
                      <span className="text-sm">12 templates</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          License Templates
                        </span>
                      </div>
                      <span className="text-sm">8 templates</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Default Roles
                        </span>
                      </div>
                      <span className="text-sm">6 roles</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Security Settings
                        </span>
                      </div>
                      <span className="text-sm">MFA Required</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Palette className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">
                          Default Theme
                        </span>
                      </div>
                      <span className="text-sm">System Default</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full px-4 py-2 border rounded-md flex items-center justify-center cursor-pointer hover:bg-muted">
                  <Settings className="mr-2 h-4 w-4" />
                  Edit Default Configurations
                </div>
              </CardFooter>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Email Configuration</CardTitle>
                <CardDescription>
                  Configure email settings and templates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Provider</span>
                    <span className="text-sm font-medium">SendGrid</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">From Email</span>
                    <span className="text-sm font-medium">
                      <EMAIL>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Templates</span>
                    <span className="text-sm font-medium">12 templates</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full px-3 py-1 text-sm flex items-center justify-center cursor-pointer hover:bg-muted rounded-md">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Configure
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Configure platform security settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Password Policy</span>
                    <span className="text-sm font-medium">Strong</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">MFA</span>
                    <span className="text-sm font-medium">Required</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Session Timeout</span>
                    <span className="text-sm font-medium">30 minutes</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full px-3 py-1 text-sm flex items-center justify-center cursor-pointer hover:bg-muted rounded-md">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Configure
                </div>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Integrations</CardTitle>
                <CardDescription>
                  Configure global integration settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Available Integrations</span>
                    <span className="text-sm font-medium">12</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Default Enabled</span>
                    <span className="text-sm font-medium">4</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">API Rate Limits</span>
                    <span className="text-sm font-medium">Configured</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="w-full">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Configure
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Platform Analytics</CardTitle>
              <CardDescription>
                Overview of platform usage and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md">
                <div className="flex flex-col items-center text-center">
                  <BarChart3 className="h-10 w-10 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    Platform Analytics Dashboard
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Detailed analytics about platform usage, performance
                    metrics, and tenant activity would be displayed here.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Tenant Growth</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-2xl font-bold">+12.4%</div>
                <p className="text-xs text-muted-foreground">
                  Year-over-year growth
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">User Growth</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-2xl font-bold">+24.8%</div>
                <p className="text-xs text-muted-foreground">
                  Year-over-year growth
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Average Response Time</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-2xl font-bold">124ms</div>
                <p className="text-xs text-muted-foreground">Last 24 hours</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">System Uptime</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-2xl font-bold">99.98%</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Platform Audit Logs</CardTitle>
                  <CardDescription>
                    Track system-level activities and changes
                  </CardDescription>
                </div>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Export Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-[auto_1fr_1fr_auto] gap-4 p-4 font-medium border-b">
                  <div>Date/Time</div>
                  <div>User/System</div>
                  <div>Action</div>
                  <div>Tenant</div>
                </div>
                {[
                  {
                    date: "2024-06-15 14:32:45",
                    user: "Admin (System)",
                    action: "Added tenant: Startup Innovations",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-15 13:21:10",
                    user: "Admin (System)",
                    action: "Updated system settings: Email configuration",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-15 11:45:22",
                    user: "System",
                    action: "Scheduled maintenance completed",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-14 16:12:33",
                    user: "Admin (System)",
                    action: "Added integration: DocuSign",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-14 15:05:17",
                    user: "Admin (System)",
                    action: "Updated security policy: Password requirements",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-14 10:22:41",
                    user: "System",
                    action: "Database backup completed",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-13 17:33:09",
                    user: "Admin (System)",
                    action: "Added default contract template",
                    tenant: "System",
                  },
                  {
                    date: "2024-06-13 14:18:55",
                    user: "System",
                    action: "System update v2.8.0 deployed",
                    tenant: "System",
                  },
                ].map((log, i) => (
                  <div
                    key={i}
                    className="grid grid-cols-[auto_1fr_1fr_auto] gap-4 p-4 items-center border-b last:border-0"
                  >
                    <div className="text-xs whitespace-nowrap">{log.date}</div>
                    <div className="text-sm">{log.user}</div>
                    <div className="text-sm">{log.action}</div>
                    <div className="text-sm">{log.tenant}</div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing 8 of 1,245 logs
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
