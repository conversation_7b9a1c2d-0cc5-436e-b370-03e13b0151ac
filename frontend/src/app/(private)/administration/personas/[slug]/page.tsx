"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Monitor,
  Megaphone,
  Briefcase,
  Plane,
  Users,
  ArrowLeft,
  Settings,
  Shield,
  FileText,
  BarChart3,
  CheckCircle,
  Cog,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import IntegrityConfigurationModal from "@/components/integrity/IntegrityConfigurationModal";

interface ConfigurationCard {
  title: string;
  description: string;
  icon: React.ReactNode;
  status: "active" | "inactive" | "configured";
  onClick: () => void;
}

export default function PersonaDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { updatePersona } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showIntegrityModal, setShowIntegrityModal] = useState(false);

  const slug = params.slug as string;
  const personaTitle = slug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  const handleSetAsActive = async () => {
    setIsLoading(true);
    try {
      await updatePersona(personaTitle);
      toast.success(`Persona "${personaTitle}" has been set successfully!`);
      
      // Redirect to discovery page after a short delay
      setTimeout(() => {
        router.push("/contract-concierge/discovery");
      }, 1000);
    } catch (error) {
      console.error("Error updating persona:", error);
      toast.error("Failed to update persona. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getPersonaIcon = (title: string) => {
    switch (title) {
      case "Information Technology":
        return <Monitor className="h-6 w-6" />;
      case "Marketing":
        return <Megaphone className="h-6 w-6" />;
      case "Legal":
        return <Briefcase className="h-6 w-6" />;
      case "Travel":
        return <Plane className="h-6 w-6" />;
      case "Human Resources":
        return <Users className="h-6 w-6" />;
      default:
        return <Settings className="h-6 w-6" />;
    }
  };

  const getPersonaColor = (title: string) => {
    switch (title) {
      case "Information Technology":
        return "from-blue-500 to-blue-600";
      case "Marketing":
        return "from-purple-500 to-purple-600";
      case "Legal":
        return "from-amber-500 to-amber-600";
      case "Travel":
        return "from-teal-500 to-teal-600";
      case "Human Resources":
        return "from-indigo-500 to-indigo-600";
      default:
        return "from-gray-500 to-gray-600";
    }
  };

  const configurationCards: ConfigurationCard[] = [
    {
      title: "Contract Templates",
      description: "Customize contract templates and clauses specific to your persona",
      icon: <FileText className="h-5 w-5" />,
      status: "inactive",
      onClick: () => {
        toast.info("Contract templates configuration coming soon!");
      },
    },
    {
      title: "Integrity Analysis",
      description: "Configure integrity scoring and risk assessment parameters",
      icon: <Shield className="h-5 w-5" />,
      status: "configured",
      onClick: () => setShowIntegrityModal(true),
    },
    {
      title: "Compliance Rules",
      description: "Set up compliance monitoring and regulatory requirements",
      icon: <CheckCircle className="h-5 w-5" />,
      status: "inactive",
      onClick: () => {
        toast.info("Compliance rules configuration coming soon!");
      },
    },
    {
      title: "Analytics & Reporting",
      description: "Configure dashboards and reports tailored to your role",
      icon: <BarChart3 className="h-5 w-5" />,
      status: "inactive",
      onClick: () => {
        toast.info("Analytics configuration coming soon!");
      },
    },
  ];

  const getStatusBadge = (status: ConfigurationCard["status"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case "configured":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Configured</Badge>;
      case "inactive":
        return <Badge variant="secondary">Not Configured</Badge>;
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Personas
          </Button>
        </div>

        <div className="flex items-center gap-4">
          <div className={`p-3 bg-gradient-to-br ${getPersonaColor(personaTitle)} rounded-lg`}>
            <div className="text-white">
              {getPersonaIcon(personaTitle)}
            </div>
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
              {personaTitle}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Configure and customize your {personaTitle.toLowerCase()} persona settings
            </p>
          </div>
          <Button
            onClick={handleSetAsActive}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700"
          >
            {isLoading ? "Setting..." : "Set as Active Persona"}
          </Button>
        </div>
      </div>

      {/* Configuration Cards */}
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Configuration Options</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {configurationCards.map((card, index) => (
              <Card
                key={index}
                className="group cursor-pointer hover:shadow-lg transition-all duration-300 border-gray-200 dark:border-gray-800 hover:border-green-300 dark:hover:border-green-700"
                onClick={card.onClick}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                        <div className="text-green-600 dark:text-green-400">
                          {card.icon}
                        </div>
                      </div>
                      <CardTitle className="text-lg">{card.title}</CardTitle>
                    </div>
                    {getStatusBadge(card.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {card.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Integrity Configuration Modal */}
      <IntegrityConfigurationModal
        open={showIntegrityModal}
        onOpenChange={setShowIntegrityModal}
      />
    </div>
  );
}
