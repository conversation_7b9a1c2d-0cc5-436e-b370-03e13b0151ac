"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Monitor,
  Megaphone,
  Briefcase,
  Plane,
  Users,
  ArrowRight,
  CheckCircle,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

interface PersonaCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  color: string;
  onClick: () => void;
  disabled?: boolean;
  selected?: boolean;
}

function PersonaCard({
  title,
  description,
  icon,
  features,
  color,
  onClick,
  disabled = false,
  selected = false,
}: PersonaCardProps) {
  return (
    <Card
      className={`group relative overflow-hidden border-gray-200 dark:border-gray-800 transition-all duration-300 ${disabled
        ? "opacity-50 cursor-not-allowed"
        : "hover:border-green-300 dark:hover:border-green-700 hover:shadow-lg cursor-pointer"
        } ${selected
          ? "ring-2 ring-green-500 ring-offset-2 bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-700"
          : ""
        }`}
      onClick={disabled ? undefined : onClick}
    >
      {/* Gradient Background */}
      <div
        className={`absolute inset-0 bg-gradient-to-br ${color} opacity-5 ${disabled ? "" : "group-hover:opacity-10"
          } transition-opacity duration-300`}
      />

      <CardHeader className="relative">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            {icon}
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              {title}
              {selected && (
                <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                  ACTIVE
                </Badge>
              )}
            </CardTitle>
          </div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 leading-relaxed">
          {description}
        </p>
      </CardHeader>

      <CardContent className="relative space-y-4">
        {/* Features List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Key Features:
          </h4>
          <ul className="space-y-1.5">
            {features.map((feature, index) => (
              <li
                key={index}
                className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
              >
                <CheckCircle className="h-3.5 w-3.5 text-green-500 flex-shrink-0" />
                {feature}
              </li>
            ))}
          </ul>
        </div>

        {/* Action Button */}
        <Button
          onClick={disabled ? undefined : onClick}
          disabled={disabled}
          className={`w-full transition-colors ${disabled
            ? "bg-gray-400 dark:bg-gray-600 text-gray-600 dark:text-gray-400 cursor-not-allowed"
            : selected
              ? "bg-green-600 hover:bg-green-700 text-white"
              : "bg-green-600 hover:bg-green-700 text-white group-hover:bg-green-700"
            }`}
        >
          {disabled ? "Coming Soon" : selected ? "Active Persona" : "Configure Persona"}
          {selected ? (
            <CheckCircle className="ml-2 h-4 w-4" />
          ) : (
            <ArrowRight className="ml-2 h-4 w-4" />
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

export default function PersonasPage() {
  const router = useRouter();
  const { user } = useAuth();

  const handlePersonaSelect = (personaTitle: string) => {
    // Navigate to persona detail page instead of showing confirmation dialog
    const personaSlug = personaTitle.toLowerCase().replace(/\s+/g, '-');
    router.push(`/administration/personas/${personaSlug}`);
  };

  // Check if a persona is currently active
  const isPersonaActive = (personaTitle: string) => {
    if (!user?.persona) return false;
    return user.persona.toLowerCase().replace(/\s+/g, '-') === personaTitle.toLowerCase().replace(/\s+/g, '-');
  };

  const personas = [
    {
      title: "Information Technology",
      description:
        "Specialized contract management for technology services, software licensing, and IT infrastructure agreements.",
      icon: <Monitor className="h-6 w-6" />,
      features: [
        "Software license management",
        "SLA monitoring and compliance",
        "Technology vendor relationships",
        "Security and data protection clauses",
        "Cloud service agreements",
      ],
      color: "from-blue-500 to-blue-600",
      onClick: () => handlePersonaSelect("Information Technology"),
    },
    {
      title: "Marketing & Advertising",
      description:
        "Comprehensive contract solutions for marketing campaigns, advertising partnerships, and creative services.",
      icon: <Megaphone className="h-6 w-6" />,
      features: [
        "Campaign contract management",
        "Media buying agreements",
        "Creative services contracts",
        "Influencer partnership deals",
        "Brand licensing agreements",
      ],
      color: "from-purple-500 to-purple-600",
      onClick: () => handlePersonaSelect("Marketing & Advertising"),
    },
    {
      title: "Professional Services",
      description:
        "Tailored contract management for consulting, legal, and other professional service engagements.",
      icon: <Briefcase className="h-6 w-6" />,
      features: [
        "Consulting agreement templates",
        "Statement of work management",
        "Professional liability tracking",
        "Billing and payment terms",
        "Intellectual property clauses",
      ],
      color: "from-green-500 to-green-600",
      onClick: () => handlePersonaSelect("Professional Services"),
    },
    {
      title: "Travel & Expense",
      description:
        "Streamlined management for travel bookings, expense policies, and corporate travel agreements.",
      icon: <Plane className="h-6 w-6" />,
      features: [
        "Corporate travel agreements",
        "Expense policy compliance",
        "Vendor rate negotiations",
        "Travel insurance coverage",
        "Booking platform integrations",
      ],
      color: "from-orange-500 to-orange-600",
      onClick: () => handlePersonaSelect("Travel & Expense"),
    },
    {
      title: "Human Resources",
      description:
        "Comprehensive HR contract management for employment agreements, vendor services, and compliance documentation.",
      icon: <Users className="h-6 w-6" />,
      features: [
        "Employment contract templates",
        "Vendor service agreements",
        "Compliance documentation",
        "Benefits administration",
        "Training and development contracts",
      ],
      color: "from-indigo-500 to-indigo-600",
      onClick: () => handlePersonaSelect("Human Resources"),
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
                Contract Personas
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Configure specialized contract management solutions tailored to
                your industry and use case
              </p>
            </div>
          </div>

          {/* Active Persona Indicator */}
          {user?.persona && (
            <div className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg px-4 py-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="text-sm">
                <span className="text-gray-600 dark:text-gray-400">Active:</span>
                <span className="font-medium text-green-700 dark:text-green-300 ml-1">
                  {user.persona}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Description */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <p className="text-sm text-green-800 dark:text-green-200">
            <strong>Personas</strong> allow you to customize the contract
            management experience for different departments and use cases within
            your organization. Each persona provides specialized templates,
            workflows, and insights tailored to specific business needs.
          </p>
        </div>
      </div>

      {/* Personas Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {personas.map((persona, index) => (
          <PersonaCard
            key={index}
            title={persona.title}
            description={persona.description}
            icon={persona.icon}
            features={persona.features}
            color={persona.color}
            onClick={persona.onClick}
            selected={isPersonaActive(persona.title)}
          />
        ))}
      </div>



      {/* Additional Information */}
      {/* <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          Getting Started with Personas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Configuration
            </h4>
            <ul className="space-y-1">
              <li>• Set up department-specific templates</li>
              <li>• Configure approval workflows</li>
              <li>• Define compliance requirements</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Benefits
            </h4>
            <ul className="space-y-1">
              <li>• Streamlined contract creation</li>
              <li>• Industry-specific insights</li>
              <li>• Improved compliance tracking</li>
            </ul>
          </div>
        </div>
      </div> */}
    </div>
  );
}
