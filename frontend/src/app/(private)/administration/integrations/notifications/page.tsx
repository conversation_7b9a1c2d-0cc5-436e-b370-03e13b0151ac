"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  ArrowLeft, 
  Bell, 
  Mail, 
  MessageSquare, 
  Save, 
  RefreshCw,
  Calendar,
  FileText,
  Key,
  Users,
  Shield,
  Clock,
  Smartphone
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"

export default function NotificationsPage() {
  const [isLoading, setIsLoading] = useState(false)
  
  const handleSave = () => {
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast.success("Notification settings saved successfully")
    }, 1500)
  }
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Link href="/settings">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Notification Settings</h2>
            <p className="text-muted-foreground">
              Manage how and when you receive notifications
            </p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
      
      <Tabs defaultValue="channels" className="space-y-4">
        <TabsList>
          <TabsTrigger value="channels">Notification Channels</TabsTrigger>
          <TabsTrigger value="contracts">Contract Notifications</TabsTrigger>
          <TabsTrigger value="licenses">License Notifications</TabsTrigger>
          <TabsTrigger value="system">System Notifications</TabsTrigger>
        </TabsList>
        
        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Channels</CardTitle>
              <CardDescription>
                Configure how you want to receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-medium">Email Notifications</div>
                      <div className="text-sm text-muted-foreground">Receive notifications via email</div>
                    </div>
                  </div>
                  <Switch id="email-notifications" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Bell className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-medium">In-App Notifications</div>
                      <div className="text-sm text-muted-foreground">Receive notifications within the platform</div>
                    </div>
                  </div>
                  <Switch id="in-app-notifications" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Smartphone className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-medium">Mobile Push Notifications</div>
                      <div className="text-sm text-muted-foreground">Receive push notifications on your mobile device</div>
                    </div>
                  </div>
                  <Switch id="push-notifications" />
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-medium">Slack Integration</div>
                      <div className="text-sm text-muted-foreground">Receive notifications in your Slack workspace</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">Configure</Button>
                    <Switch id="slack-notifications" />
                  </div>
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5 text-primary" />
                    <div>
                      <div className="font-medium">Microsoft Teams Integration</div>
                      <div className="text-sm text-muted-foreground">Receive notifications in Microsoft Teams</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">Configure</Button>
                    <Switch id="teams-notifications" />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Notification Preferences</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="notification-frequency">Email Notification Frequency</Label>
                  <Select defaultValue="realtime">
                    <SelectTrigger id="notification-frequency" className="w-full max-w-xs">
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realtime">Real-time (Immediate)</SelectItem>
                      <SelectItem value="hourly">Hourly Digest</SelectItem>
                      <SelectItem value="daily">Daily Digest</SelectItem>
                      <SelectItem value="weekly">Weekly Digest</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="digest-time">Digest Delivery Time</Label>
                  <Select defaultValue="9am">
                    <SelectTrigger id="digest-time" className="w-full max-w-xs">
                      <SelectValue placeholder="Select time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="9am">9:00 AM</SelectItem>
                      <SelectItem value="12pm">12:00 PM</SelectItem>
                      <SelectItem value="3pm">3:00 PM</SelectItem>
                      <SelectItem value="6pm">6:00 PM</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    This applies to daily and weekly digest emails
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="do-not-disturb" />
                  <div>
                    <Label htmlFor="do-not-disturb">Do Not Disturb</Label>
                    <p className="text-xs text-muted-foreground">
                      Pause all notifications outside of working hours
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Notifications</CardTitle>
              <CardDescription>
                Configure notifications related to contracts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Contract Events</h3>
                
                <div className="space-y-2">
                  {[
                    { id: "contract-created", label: "Contract Created", description: "When a new contract is created" },
                    { id: "contract-updated", label: "Contract Updated", description: "When a contract is modified" },
                    { id: "contract-approval", label: "Approval Required", description: "When your approval is required for a contract" },
                    { id: "contract-approved", label: "Contract Approved", description: "When a contract you created is approved" },
                    { id: "contract-rejected", label: "Contract Rejected", description: "When a contract you created is rejected" },
                    { id: "contract-executed", label: "Contract Executed", description: "When a contract is fully executed" },
                    { id: "contract-expiring", label: "Contract Expiring", description: "When a contract is approaching expiration" },
                    { id: "contract-expired", label: "Contract Expired", description: "When a contract has expired" },
                    { id: "contract-renewed", label: "Contract Renewed", description: "When a contract is renewed" },
                    { id: "contract-comment", label: "New Comment", description: "When someone comments on a contract" },
                  ].map((notification, i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-2 last:border-0">
                      <div>
                        <Label htmlFor={notification.id} className="font-medium">{notification.label}</Label>
                        <p className="text-xs text-muted-foreground">{notification.description}</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-email`} defaultChecked={i < 7} />
                        </div>
                        <div className="flex items-center gap-2">
                          <Bell className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-app`} defaultChecked />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Reminder Settings</h3>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="expiration-reminder">Contract Expiration Reminders</Label>
                    <Select defaultValue="30,60,90">
                      <SelectTrigger id="expiration-reminder">
                        <SelectValue placeholder="Select reminder days" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">30 days before</SelectItem>
                        <SelectItem value="30,60">30 and 60 days before</SelectItem>
                        <SelectItem value="30,60,90">30, 60, and 90 days before</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="renewal-reminder">Contract Renewal Reminders</Label>
                    <Select defaultValue="30,60,90">
                      <SelectTrigger id="renewal-reminder">
                        <SelectValue placeholder="Select reminder days" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">30 days before</SelectItem>
                        <SelectItem value="30,60">30 and 60 days before</SelectItem>
                        <SelectItem value="30,60,90">30, 60, and 90 days before</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="escalate-reminders" defaultChecked />
                  <div>
                    <Label htmlFor="escalate-reminders">Escalate Reminders</Label>
                    <p className="text-xs text-muted-foreground">
                      Increase reminder frequency as the expiration date approaches
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="licenses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Notifications</CardTitle>
              <CardDescription>
                Configure notifications related to licenses
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">License Events</h3>
                
                <div className="space-y-2">
                  {[
                    { id: "license-created", label: "License Created", description: "When a new license is created" },
                    { id: "license-updated", label: "License Updated", description: "When a license is modified" },
                    { id: "license-approval", label: "Approval Required", description: "When your approval is required for a license" },
                    { id: "license-approved", label: "License Approved", description: "When a license you created is approved" },
                    { id: "license-expiring", label: "License Expiring", description: "When a license is approaching expiration" },
                    { id: "license-expired", label: "License Expired", description: "When a license has expired" },
                    { id: "license-renewed", label: "License Renewed", description: "When a license is renewed" },
                    { id: "license-compliance", label: "Compliance Issue", description: "When a compliance issue is detected" },
                    { id: "license-utilization", label: "Utilization Alert", description: "When license utilization exceeds thresholds" },
                  ].map((notification, i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-2 last:border-0">
                      <div>
                        <Label htmlFor={notification.id} className="font-medium">{notification.label}</Label>
                        <p className="text-xs text-muted-foreground">{notification.description}</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-email`} defaultChecked={i < 6 || i === 7} />
                        </div>
                        <div className="flex items-center gap-2">
                          <Bell className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-app`} defaultChecked />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Compliance & Utilization Alerts</h3>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="utilization-threshold">License Utilization Threshold</Label>
                    <Select defaultValue="80">
                      <SelectTrigger id="utilization-threshold">
                        <SelectValue placeholder="Select threshold" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="70">70% utilization</SelectItem>
                        <SelectItem value="80">80% utilization</SelectItem>
                        <SelectItem value="90">90% utilization</SelectItem>
                        <SelectItem value="95">95% utilization</SelectItem>
                        <SelectItem value="custom">Custom threshold</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Receive alerts when license utilization exceeds this threshold
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="compliance-check">Compliance Check Frequency</Label>
                    <Select defaultValue="weekly">
                      <SelectTrigger id="compliance-check">
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      How often to run automated compliance checks
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="critical-alerts" defaultChecked />
                  <div>
                    <Label htmlFor="critical-alerts">Critical Compliance Alerts</Label>
                    <p className="text-xs text-muted-foreground">
                      Send immediate alerts for critical compliance issues regardless of other settings
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Notifications</CardTitle>
              <CardDescription>
                Configure notifications related to system events
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">User & Security Events</h3>
                
                <div className="space-y-2">
                  {[
                    { id: "user-added", label: "User Added", description: "When a new user is added to your organization" },
                    { id: "user-removed", label: "User Removed", description: "When a user is removed from your organization" },
                    { id: "role-changed", label: "Role Changed", description: "When a user's role is changed" },
                    { id: "login-attempt", label: "Failed Login Attempts", description: "When there are multiple failed login attempts" },
                    { id: "password-changed", label: "Password Changed", description: "When your password is changed" },
                    { id: "security-alert", label: "Security Alerts", description: "Important security-related notifications" },
                  ].map((notification, i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-2 last:border-0">
                      <div>
                        <Label htmlFor={notification.id} className="font-medium">{notification.label}</Label>
                        <p className="text-xs text-muted-foreground">{notification.description}</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-email`} defaultChecked={i === 3 || i === 4 || i === 5} />
                        </div>
                        <div className="flex items-center gap-2">
                          <Bell className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-app`} defaultChecked />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">System & Maintenance Events</h3>
                
                <div className="space-y-2">
                  {[
                    { id: "system-update", label: "System Updates", description: "When system updates are scheduled or completed" },
                    { id: "maintenance", label: "Scheduled Maintenance", description: "When maintenance is scheduled" },
                    { id: "service-disruption", label: "Service Disruptions", description: "When there are service disruptions" },
                    { id: "feature-release", label: "New Features", description: "When new features are released" },
                    { id: "announcement", label: "System Announcements", description: "Important announcements about the platform" },
                  ].map((notification, i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-2 last:border-0">
                      <div>
                        <Label htmlFor={notification.id} className="font-medium">{notification.label}</Label>
                        <p className="text-xs text-muted-foreground">{notification.description}</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-email`} defaultChecked={i === 1 || i === 2} />
                        </div>
                        <div className="flex items-center gap-2">
                          <Bell className="h-4 w-4 text-muted-foreground" />
                          <Switch id={`${notification.id}-app`} defaultChecked />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Administrative Notifications</h3>
                
                <div className="flex items-center space-x-2">
                  <Switch id="admin-only" defaultChecked />
                  <div>
                    <Label htmlFor="admin-only">Admin-Only Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Receive administrative notifications only if you have admin privileges
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="billing-notifications" defaultChecked />
                  <div>
                    <Label htmlFor="billing-notifications">Billing Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Receive notifications about billing events and invoices
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
