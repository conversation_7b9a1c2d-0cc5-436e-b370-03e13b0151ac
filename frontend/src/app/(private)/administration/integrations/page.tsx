/**
 * Settings Page
 * User and account settings
 */

import { Metadata } from "next";
import Link from "next/link";
import {
  User,
  Bell,
  Shield,
  CreditCard,
  Key,
  Palette,
  Globe,
  FileText,
  ChevronRight,
  Mail,
  Building,
  Users,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

export const metadata: Metadata = {
  title: "Settings",
  description: "Manage your account settings and preferences",
};

// Settings categories with their respective links
const settingsCategories = [
  {
    title: "Account",
    description: "Manage your account settings and preferences",
    items: [
      {
        title: "Profile",
        description: "Manage your personal information",
        icon: <User className="h-5 w-5" />,
        href: "/settings/profile",
      },
      {
        title: "Notifications",
        description: "Configure how you receive notifications",
        icon: <Bell className="h-5 w-5" />,
        href: "/settings/notifications",
      },
      {
        title: "Security",
        description: "Manage your password and security settings",
        icon: <Shield className="h-5 w-5" />,
        href: "/settings/security",
      },
    ],
  },
  {
    title: "Billing",
    description: "Manage your subscription and billing information",
    items: [
      {
        title: "Subscription",
        description: "Manage your subscription plan",
        icon: <CreditCard className="h-5 w-5" />,
        href: "/settings/subscription",
      },
      {
        title: "Payment Methods",
        description: "Manage your payment methods",
        icon: <CreditCard className="h-5 w-5" />,
        href: "/settings/payment-methods",
      },
      {
        title: "Billing History",
        description: "View your billing history and invoices",
        icon: <FileText className="h-5 w-5" />,
        href: "/settings/billing-history",
      },
    ],
  },
  {
    title: "Appearance",
    description: "Customize the appearance of the application",
    items: [
      {
        title: "Theme",
        description: "Customize the application theme and colors",
        icon: <Palette className="h-5 w-5" />,
        href: "/settings/theme",
      },
      {
        title: "White Labeling",
        description: "Customize branding and white labeling options",
        icon: <Globe className="h-5 w-5" />,
        href: "/settings/white-labeling",
      },
    ],
  },
  {
    title: "Organization",
    description: "Manage your organization settings",
    items: [
      {
        title: "Company Profile",
        description: "Manage your company information",
        icon: <Building className="h-5 w-5" />,
        href: "/settings/company",
      },
      {
        title: "Team Members",
        description: "Manage users and permissions",
        icon: <Users className="h-5 w-5" />,
        href: "/settings/team",
      },
      {
        title: "Email Templates",
        description: "Customize email templates and notifications",
        icon: <Mail className="h-5 w-5" />,
        href: "/settings/email-templates",
      },
    ],
  },
  {
    title: "Advanced",
    description: "Advanced settings and configurations",
    items: [
      {
        title: "API Keys",
        description: "Manage API keys and integrations",
        icon: <Key className="h-5 w-5" />,
        href: "/settings/api-keys",
      },
      {
        title: "Webhooks",
        description: "Configure webhooks for external integrations",
        icon: <Globe className="h-5 w-5" />,
        href: "/settings/webhooks",
      },
    ],
  },
];

export default function SettingsPage() {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      <div className="space-y-6">
        {settingsCategories.map((category, index) => (
          <div key={index} className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">{category.title}</h3>
              <p className="text-sm text-muted-foreground">
                {category.description}
              </p>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {category.items.map((item, itemIndex) => (
                <Link key={itemIndex} href={item.href}>
                  <Card className="h-full transition-colors hover:bg-muted/50">
                    <CardContent className="p-6 flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-2 rounded-full bg-primary/10 text-primary">
                          {item.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {item.title}
                          </CardTitle>
                          <CardDescription className="text-sm">
                            {item.description}
                          </CardDescription>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-muted-foreground" />
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            {index < settingsCategories.length - 1 && <Separator />}
          </div>
        ))}
      </div>
    </div>
  );
}
