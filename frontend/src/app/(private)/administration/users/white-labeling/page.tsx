"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  ArrowLeft, 
  Check, 
  Palette, 
  Upload, 
  Save,
  Eye,
  RefreshCw,
  Image as ImageIcon,
  Type,
  Layout,
  Mail
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

export default function WhiteLabelingPage() {
  const [isLoading, setIsLoading] = useState(false)
  
  const handleSave = () => {
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast.success("White-labeling settings saved successfully")
    }, 1500)
  }
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Link href="/organization">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">White-Labeling Configuration</h2>
            <p className="text-muted-foreground">
              Customize the platform with your organization's branding
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="branding" className="space-y-4">
        <TabsList>
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="theme">Theme & Colors</TabsTrigger>
          <TabsTrigger value="email">Email Templates</TabsTrigger>
          <TabsTrigger value="domain">Custom Domain</TabsTrigger>
        </TabsList>
        
        <TabsContent value="branding" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Logo & Branding</CardTitle>
              <CardDescription>
                Customize your organization's logo and branding elements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="company-name">Company Name</Label>
                  <Input id="company-name" defaultValue="Acme Corporation" className="max-w-md" />
                  <p className="text-xs text-muted-foreground mt-1">
                    This will be displayed in the platform header and emails
                  </p>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <Label>Primary Logo (Light Background)</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-48 rounded border flex items-center justify-center bg-background">
                      <div className="text-xl font-bold">ACME</div>
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Logo
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Recommended size: 240x80px, PNG or SVG format
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Secondary Logo (Dark Background)</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-48 rounded border flex items-center justify-center bg-slate-800 text-white">
                      <div className="text-xl font-bold">ACME</div>
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Logo
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Recommended size: 240x80px, PNG or SVG format
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Favicon</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded border flex items-center justify-center bg-primary text-primary-foreground">
                      <div className="text-sm font-bold">A</div>
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Favicon
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Recommended size: 32x32px, ICO or PNG format
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Login Page Customization</CardTitle>
              <CardDescription>
                Customize the login page appearance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="login-title">Login Page Title</Label>
                  <Input id="login-title" defaultValue="Welcome to Acme Contract Management" className="max-w-md" />
                </div>
                
                <div>
                  <Label htmlFor="login-message">Welcome Message</Label>
                  <Textarea 
                    id="login-message" 
                    defaultValue="Sign in to access your contract and license management platform." 
                    className="max-w-md"
                  />
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <Label>Background Image</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-32 w-48 rounded border flex items-center justify-center bg-gradient-to-r from-slate-100 to-slate-200">
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Image
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Recommended size: 1920x1080px, JPG or PNG format
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="show-powered-by" defaultChecked />
                  <Label htmlFor="show-powered-by">Show "Powered by Aptio" badge</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="theme" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Theme Configuration</CardTitle>
              <CardDescription>
                Customize colors and theme settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <Label>Primary Color</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-primary border"></div>
                    <Input type="text" defaultValue="#0284c7" className="w-32" />
                    <Input type="color" defaultValue="#0284c7" className="w-10 h-10 p-1" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Secondary Color</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-secondary border"></div>
                    <Input type="text" defaultValue="#7c3aed" className="w-32" />
                    <Input type="color" defaultValue="#7c3aed" className="w-10 h-10 p-1" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Accent Color</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-accent border"></div>
                    <Input type="text" defaultValue="#f3f4f6" className="w-32" />
                    <Input type="color" defaultValue="#f3f4f6" className="w-10 h-10 p-1" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Background Color</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full bg-background border"></div>
                    <Input type="text" defaultValue="#ffffff" className="w-32" />
                    <Input type="color" defaultValue="#ffffff" className="w-10 h-10 p-1" />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <Label>Default Theme</Label>
                <div className="flex items-center gap-4">
                  <Select defaultValue="system">
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Select default theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="allow-theme-toggle" defaultChecked />
                  <Label htmlFor="allow-theme-toggle">Allow users to change theme</Label>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <Label>Font Settings</Label>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="heading-font" className="text-sm">Heading Font</Label>
                    <Select defaultValue="inter">
                      <SelectTrigger id="heading-font">
                        <SelectValue placeholder="Select heading font" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inter">Inter</SelectItem>
                        <SelectItem value="roboto">Roboto</SelectItem>
                        <SelectItem value="opensans">Open Sans</SelectItem>
                        <SelectItem value="montserrat">Montserrat</SelectItem>
                        <SelectItem value="custom">Custom Font</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="body-font" className="text-sm">Body Font</Label>
                    <Select defaultValue="inter">
                      <SelectTrigger id="body-font">
                        <SelectValue placeholder="Select body font" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inter">Inter</SelectItem>
                        <SelectItem value="roboto">Roboto</SelectItem>
                        <SelectItem value="opensans">Open Sans</SelectItem>
                        <SelectItem value="montserrat">Montserrat</SelectItem>
                        <SelectItem value="custom">Custom Font</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset to Defaults
              </Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Theme Preview</CardTitle>
              <CardDescription>
                Preview your custom theme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6 border rounded-lg p-6">
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold">Theme Preview</h3>
                  <p className="text-muted-foreground">This is how your theme will look to users.</p>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  <Button>Primary Button</Button>
                  <Button variant="secondary">Secondary Button</Button>
                  <Button variant="outline">Outline Button</Button>
                  <Button variant="ghost">Ghost Button</Button>
                  <Button variant="destructive">Destructive Button</Button>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  <Badge>Default Badge</Badge>
                  <Badge variant="secondary">Secondary Badge</Badge>
                  <Badge variant="outline">Outline Badge</Badge>
                  <Badge variant="destructive">Destructive Badge</Badge>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="preview-input">Input Field</Label>
                    <Input id="preview-input" placeholder="Input placeholder" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="preview-select">Select Field</Label>
                    <Select>
                      <SelectTrigger id="preview-select">
                        <SelectValue placeholder="Select an option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="option1">Option 1</SelectItem>
                        <SelectItem value="option2">Option 2</SelectItem>
                        <SelectItem value="option3">Option 3</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Card Title</CardTitle>
                    <CardDescription>Card description text</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>This is sample content inside a card component.</p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" size="sm">Card Action</Button>
                  </CardFooter>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Templates</CardTitle>
              <CardDescription>
                Customize email templates with your branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email-sender-name">Sender Name</Label>
                  <Input id="email-sender-name" defaultValue="Acme Contract Management" className="max-w-md" />
                </div>
                
                <div>
                  <Label htmlFor="email-sender-address">Sender Email Address</Label>
                  <Input id="email-sender-address" defaultValue="<EMAIL>" className="max-w-md" />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="use-custom-smtp" />
                  <Label htmlFor="use-custom-smtp">Use custom SMTP server</Label>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <Label>Email Header Logo</Label>
                <div className="flex items-center gap-4">
                  <div className="h-16 w-48 rounded border flex items-center justify-center bg-background">
                    <div className="text-xl font-bold">ACME</div>
                  </div>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Logo
                    </Button>
                    <p className="text-xs text-muted-foreground">
                      Recommended size: 240x80px, PNG format
                    </p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Email Templates</Label>
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview All
                  </Button>
                </div>
                
                <div className="space-y-2">
                  {[
                    { name: "Welcome Email", description: "Sent to new users" },
                    { name: "Password Reset", description: "Sent when a user requests a password reset" },
                    { name: "Contract Notification", description: "Sent for contract updates and approvals" },
                    { name: "License Notification", description: "Sent for license updates and renewals" },
                    { name: "User Invitation", description: "Sent when inviting new users" },
                    { name: "Weekly Digest", description: "Weekly summary of activities" },
                  ].map((template, i) => (
                    <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-muted-foreground">{template.description}</div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          Preview
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Type className="mr-2 h-4 w-4" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch id="include-footer" defaultChecked />
                <Label htmlFor="include-footer">Include company footer in all emails</Label>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Email Footer Configuration</CardTitle>
              <CardDescription>
                Customize the footer that appears in all emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="footer-text">Footer Text</Label>
                <Textarea 
                  id="footer-text" 
                  defaultValue="© 2024 Acme Corporation. All rights reserved. This email was sent by Acme Contract Management." 
                  className="h-20"
                />
              </div>
              
              <div>
                <Label htmlFor="footer-links">Footer Links</Label>
                <Textarea 
                  id="footer-links" 
                  defaultValue="Privacy Policy: https://acme.com/privacy
Terms of Service: https://acme.com/terms
Unsubscribe: {unsubscribe_link}" 
                  className="h-20 font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Enter one link per line in the format "Link Text: URL"
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch id="include-social" defaultChecked />
                <Label htmlFor="include-social">Include social media links</Label>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="linkedin-url">LinkedIn URL</Label>
                  <Input id="linkedin-url" defaultValue="https://linkedin.com/company/acme" />
                </div>
                <div>
                  <Label htmlFor="twitter-url">Twitter URL</Label>
                  <Input id="twitter-url" defaultValue="https://twitter.com/acme" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="domain" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom Domain Configuration</CardTitle>
              <CardDescription>
                Set up a custom domain for your platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="custom-domain">Custom Domain</Label>
                  <Input id="custom-domain" placeholder="contracts.yourdomain.com" className="max-w-md" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Enter the domain you want to use for your platform
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 rounded-full bg-amber-500"></div>
                  <span className="text-sm">Domain not verified</span>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">DNS Configuration</h3>
                <p className="text-sm text-muted-foreground">
                  To verify your domain, add the following DNS records to your domain's DNS settings:
                </p>
                
                <div className="space-y-4 border rounded-lg p-4 bg-muted/50">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">CNAME Record</div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-xs">Host</Label>
                        <div className="p-2 bg-muted rounded text-sm font-mono">contracts</div>
                      </div>
                      <div>
                        <Label className="text-xs">Points to</Label>
                        <div className="p-2 bg-muted rounded text-sm font-mono">tenant-acme.aptio.com</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm font-medium">TXT Record</div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-xs">Host</Label>
                        <div className="p-2 bg-muted rounded text-sm font-mono">_aptio-verification.contracts</div>
                      </div>
                      <div>
                        <Label className="text-xs">Value</Label>
                        <div className="p-2 bg-muted rounded text-sm font-mono">aptio-verification=abc123def456</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Check Verification
                </Button>
                <Button variant="outline">
                  <Mail className="mr-2 h-4 w-4" />
                  Email Instructions
                </Button>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">SSL Certificate</h3>
                <p className="text-sm text-muted-foreground">
                  Once your domain is verified, we'll automatically provision and manage an SSL certificate for your custom domain.
                </p>
                
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 rounded-full bg-muted"></div>
                  <span className="text-sm">SSL certificate not provisioned</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button disabled>
                <Check className="mr-2 h-4 w-4" />
                Activate Custom Domain
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
