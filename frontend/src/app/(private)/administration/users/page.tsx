"use client";

import Link from "next/link";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  BarChart3,
  FileText,
  Key,
  Settings,
  Shield,
  Users,
  Building,
  Clock,
  Layers,
  Wallet,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { UserManagement } from "@/components/organization/UserManagement";
import { RoleManagement } from "@/components/organization/RoleManagement";

export default function OrganizationPage() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Enterprise Administration
          </h2>
          <p className="text-muted-foreground">
            Manage your organization settings, users, and departments.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button onClick={() => setActiveTab("users")}>
            <Users className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">124</div>
            <p className="text-xs text-muted-foreground">+8 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Across 3 locations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Roles</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">
              Custom permission sets
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subscription</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Enterprise</div>
            <p className="text-xs text-muted-foreground">Renews in 267 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="departments">Departments</TabsTrigger>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Organization Profile</CardTitle>
                <CardDescription>
                  Basic information about your organization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Organization Name</div>
                    <div className="text-sm">Acme Corporation</div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Industry</div>
                    <div className="text-sm">Technology</div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Headquarters</div>
                    <div className="text-sm">San Francisco, CA</div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Account Created</div>
                    <div className="text-sm">January 15, 2023</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  <Settings className="mr-2 h-4 w-4" />
                  Edit Profile
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Usage Summary</CardTitle>
                <CardDescription>
                  Current usage across your organization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Contracts</span>
                      <span className="font-medium">127 / Unlimited</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-full w-[60%] rounded-full bg-primary"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Licenses</span>
                      <span className="font-medium">86 / Unlimited</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-full w-[40%] rounded-full bg-primary"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Storage</span>
                      <span className="font-medium">4.2 GB / 50 GB</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-full w-[8%] rounded-full bg-primary"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Users</span>
                      <span className="font-medium">124 / 150</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-full w-[82%] rounded-full bg-primary"></div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Detailed Usage
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security & Compliance</CardTitle>
                <CardDescription>
                  Security settings and compliance status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Multi-Factor Authentication
                      </span>
                    </div>
                    <Badge variant="outline" className="bg-green-500/10">
                      Enabled
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Single Sign-On (SSO)
                      </span>
                    </div>
                    <Badge variant="outline" className="bg-green-500/10">
                      Enabled
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Password Policy
                      </span>
                    </div>
                    <Badge variant="outline" className="bg-green-500/10">
                      Strong
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Last Security Audit
                      </span>
                    </div>
                    <span className="text-xs">14 days ago</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        Data Encryption
                      </span>
                    </div>
                    <Badge variant="outline" className="bg-green-500/10">
                      AES-256
                    </Badge>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  <Shield className="mr-2 h-4 w-4" />
                  Security Settings
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">User Management</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-xs text-muted-foreground">
                  Add, edit, or remove users from your organization
                </p>
              </CardContent>
              <CardFooter>
                <Link href="/organization/users">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Manage Users
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Department Management</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-xs text-muted-foreground">
                  Organize users into departments and teams
                </p>
              </CardContent>
              <CardFooter>
                <Link href="/organization/departments">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Manage Departments
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Role Management</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-xs text-muted-foreground">
                  Configure roles and permissions for users
                </p>
              </CardContent>
              <CardFooter>
                <Link href="/organization/roles">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Manage Roles
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">White-labeling</CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-xs text-muted-foreground">
                  Customize the platform with your branding
                </p>
              </CardContent>
              <CardFooter>
                <Link href="/organization/white-labeling">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    <ArrowRight className="mr-2 h-4 w-4" />
                    Configure
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <UserManagement />
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Department Management</CardTitle>
                  <CardDescription>
                    Organize users into departments and teams
                  </CardDescription>
                </div>
                <Button>
                  <Building className="mr-2 h-4 w-4" />
                  Add Department
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-[1fr_auto_auto_auto] gap-4 p-4 font-medium border-b">
                  <div>Department</div>
                  <div>Users</div>
                  <div>Head</div>
                  <div className="text-right">Actions</div>
                </div>
                {[
                  { name: "Engineering", users: 42, head: "John Doe" },
                  { name: "Legal", users: 8, head: "Sarah Johnson" },
                  { name: "Finance", users: 15, head: "Michael Chen" },
                  { name: "Procurement", users: 12, head: "Emily Rodriguez" },
                  { name: "IT", users: 18, head: "David Kim" },
                  { name: "Marketing", users: 14, head: "Jessica Lee" },
                  { name: "Sales", users: 25, head: "Robert Wilson" },
                ].map((dept, i) => (
                  <div
                    key={i}
                    className="grid grid-cols-[1fr_auto_auto_auto] gap-4 p-4 items-center border-b last:border-0"
                  >
                    <div className="font-medium">{dept.name}</div>
                    <div className="text-sm">{dept.users}</div>
                    <div className="text-sm">{dept.head}</div>
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <RoleManagement />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div>
                  <CardTitle>Audit Logs</CardTitle>
                  <CardDescription>
                    Track user activity and system changes
                  </CardDescription>
                </div>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Export Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-[auto_1fr_1fr_auto] gap-4 p-4 font-medium border-b">
                  <div>Date/Time</div>
                  <div>User</div>
                  <div>Action</div>
                  <div>IP Address</div>
                </div>
                {[
                  {
                    date: "2024-06-15 14:32:45",
                    user: "John Doe",
                    action: "Created contract: Service Agreement - Acme Inc.",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-15 13:21:10",
                    user: "Sarah Johnson",
                    action: "Added user: David Kim",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-15 11:45:22",
                    user: "Michael Chen",
                    action: "Modified role: Finance Manager",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-14 16:12:33",
                    user: "Emily Rodriguez",
                    action: "Added license: Microsoft 365 E3",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-14 15:05:17",
                    user: "David Kim",
                    action: "Changed security settings: Enabled MFA",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-14 10:22:41",
                    user: "Sarah Johnson",
                    action: "Approved contract: NDA - XYZ Corp",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-13 17:33:09",
                    user: "John Doe",
                    action: "Created department: Marketing",
                    ip: "***********",
                  },
                  {
                    date: "2024-06-13 14:18:55",
                    user: "System",
                    action: "Automated compliance check completed",
                    ip: "127.0.0.1",
                  },
                ].map((log, i) => (
                  <div
                    key={i}
                    className="grid grid-cols-[auto_1fr_1fr_auto] gap-4 p-4 items-center border-b last:border-0"
                  >
                    <div className="text-xs whitespace-nowrap">{log.date}</div>
                    <div className="text-sm">{log.user}</div>
                    <div className="text-sm">{log.action}</div>
                    <div className="text-xs">{log.ip}</div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                Showing 8 of 1,245 logs
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm">
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
