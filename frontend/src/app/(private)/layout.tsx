"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart3,
  FileText,
  LayoutDashboard,
  Lightbulb,
  LogOut,
  Settings,
  Users,
  Search,
  Menu,
  X,
  Key,
  ChevronDown,
  ChevronRight,
  Compass,
  Target,
  Calendar,
  Building,
  Handshake,
  Store,
  FileCheck,
  Clock,
  HandCoins,
  Zap,
  Briefcase,
  Globe,
  Activity,
  Gauge,
  Eye,
  CheckCircle,
  RefreshCw,
  UserPlus,
  FileSearch,
  User,
  PanelLeftClose,
  PanelLeftOpen,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useSidebarCollapse } from "@/hooks/use-sidebar-collapse";
import { NormalUserRoute } from "@/components/auth/RoleBasedRoute";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { ThemeToggle } from "@/components/theme-toggle";
import { NotificationCenter } from "@/components/notification-center";
import { FloatingAIChat } from "@/components/ai/FloatingAIChat";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface NavSubSubItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  disabled?: boolean;
}

interface NavSubItem {
  title: string;
  href?: string;
  icon: React.ReactNode;
  subItems?: NavSubSubItem[];
  disabled?: boolean;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ReactNode;
  subItems?: NavSubItem[];
  disabled?: boolean;
}

const navItems: NavItem[] = [
  {
    title: "Contract Concierge",
    icon: <Compass className="h-4 w-4" />,
    subItems: [
      {
        title: "Discovery",
        href: "/contract-concierge/discovery",
        icon: <FileSearch className="h-4 w-4" />,
      },
      {
        title: "Benchmark",
        href: "/contract-concierge/benchmark",
        icon: <BarChart3 className="h-4 w-4" />,
      },
      {
        title: "Associations",
        href: "/contract-concierge/associations",
        icon: <Building className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Cockpit",
    icon: <Gauge className="h-4 w-4" />,
    href: "/cockpit/dashboard",
    // subItems: [
    //   {
    //     title: "Dashboard",
    //     href: "/cockpit/dashboard",
    //     icon: <LayoutDashboard className="h-4 w-4" />,
    //   },
    //   {
    //     title: "AI Insights",
    //     href: "/cockpit/ai-insights",
    //     icon: <Lightbulb className="h-4 w-4" />,
    //     disabled: true,
    //   },
    //   {
    //     title: "Analytics",
    //     href: "/cockpit/analytics",
    //     icon: <BarChart3 className="h-4 w-4" />,
    //     disabled: true,
    //   },
    // ],
  },
  {
    title: "Source to Contract",
    icon: <Briefcase className="h-4 w-4" />,
    subItems: [
      {
        title: "Sourcing",
        icon: <Search className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Category Strategy",
            href: "/source-to-contract/sourcing/category-strategy",
            icon: <Target className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Market Intelligence",
            href: "/source-to-contract/sourcing/market-intelligence",
            icon: <Activity className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Projects",
            href: "/source-to-contract/sourcing/projects",
            icon: <Briefcase className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
      {
        title: "RFX",
        icon: <FileText className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Creation & Management",
            href: "/rfx/creation",
            icon: <FileText className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Weighting, Analysis, Scoring",
            href: "/rfx/analysis",
            icon: <BarChart3 className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Shortlist, Due Diligence, Key Commercials",
            href: "/rfx/shortlist",
            icon: <CheckCircle className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
      {
        title: "Negotiation & Contracting",
        icon: <Handshake className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Contract Drafting",
            href: "/negotiation/drafting",
            icon: <FileText className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Negotiation",
            href: "/negotiation/process",
            icon: <Handshake className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
      {
        title: "Marketplace",
        icon: <Store className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Marketplace",
            href: "/marketplace",
            icon: <Store className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Catalogues",
            href: "/marketplace/catalogues",
            icon: <Store className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Spot Buying",
            href: "/marketplace/spot-buying",
            icon: <Zap className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
    ],
  },
  {
    title: "Contract Management",
    icon: <FileText className="h-4 w-4" />,
    subItems: [
      {
        title: "Repository",
        href: "/contract-management/contracts",
        icon: <FileText className="h-4 w-4" />,
      },
      {
        title: "Licenses",
        href: "/contract-management/licenses",
        icon: <Key className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Contract Compliance & Integrity",
        href: "/contract-management/compliance",
        icon: <CheckCircle className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Obligations Tracking",
        href: "/contract-management/obligations",
        icon: <Clock className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Change Management",
        href: "/contract-management/changes",
        icon: <RefreshCw className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Financials & Consumption",
        href: "/contract-management/financials",
        icon: <HandCoins className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Invoice Reconciliation",
        href: "/contract-management/invoices",
        icon: <FileCheck className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: "Supplier Management",
        icon: <Building className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Supplier Performance Dashboard",
            href: "/contract-management/suppliers/performance",
            icon: <BarChart3 className="h-4 w-4" />,
            disabled: true,
          },
          {
            title: "Governance & Meetings",
            href: "/contract-management/suppliers/governance",
            icon: <Users className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
      {
        title: "Renewals",
        icon: <RefreshCw className="h-4 w-4" />,
        disabled: true,
        subItems: [
          {
            title: "Renewals Calendar",
            href: "/contract-management/renewals",
            icon: <Calendar className="h-4 w-4" />,
            disabled: true,
          },
        ],
      },
    ],
  },
  {
    title: "Renewals Calendar",
    icon: <Calendar className="h-4 w-4" />,
    disabled: true,
    subItems: [
      {
        title: "Demo",
        href: "/renewals/demo",
        icon: <Eye className="h-4 w-4" />,
        disabled: true,
      },
    ],
  },
  {
    title: "Administration",
    icon: <Settings className="h-4 w-4" />,
    disabled: false,
    subItems: [
      {
        title: "Personas",
        href: "/administration/personas",
        icon: <User className="h-4 w-4" />,
      },
      {
        title: "User & Role Management",
        href: "/administration/users",
        icon: <UserPlus className="h-4 w-4" />,
      },
      {
        title: "Integration Settings",
        href: "/administration/integrations",
        icon: <Globe className="h-4 w-4" />,
      },
      {
        title: "Audit & Logs",
        href: "/administration/audit",
        icon: <Eye className="h-4 w-4" />,
      },
    ],
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = React.useState(false);
  const { isCollapsed, isLoaded, toggle } = useSidebarCollapse();
  const [collapsedSections, setCollapsedSections] = React.useState<Set<string>>(
    new Set([
      "Discovery",
      "Cockpit",
      "Source to Contract",
      "Contract Management",
      "Renewals Calendar",
      "Administration",
    ])
  );
  const [collapsedSubSections, setCollapsedSubSections] = React.useState<
    Set<string>
  >(
    new Set([
      "Sourcing",
      "RFX",
      "Negotiation & Contracting",
      "Marketplace",
      "Supplier Management",
      "Renewals",
    ])
  );
  const { user, logout, isLoading, isAuthenticated } = useAuth();

  // Helper functions - defined before useEffect to avoid dependency issues
  const isActiveItem = React.useCallback(
    (href: string) => {
      return pathname === href || pathname.startsWith(href + "/");
    },
    [pathname]
  );

  const isActiveSectionItem = React.useCallback(
    (item: NavItem) => {
      if (item.href && isActiveItem(item.href)) return true;
      if (item.subItems) {
        return item.subItems.some((subItem) => {
          if (subItem.href && isActiveItem(subItem.href)) return true;
          if (subItem.subItems) {
            return subItem.subItems.some((subSubItem) =>
              isActiveItem(subSubItem.href)
            );
          }
          return false;
        });
      }
      return false;
    },
    [isActiveItem]
  );

  const isActiveSubSectionItem = React.useCallback(
    (subItem: NavSubItem) => {
      if (subItem.href && isActiveItem(subItem.href)) return true;
      if (subItem.subItems) {
        return subItem.subItems.some((subSubItem) =>
          isActiveItem(subSubItem.href)
        );
      }
      return false;
    },
    [isActiveItem]
  );

  // Auto-expand sections that contain the current active page
  React.useEffect(() => {
    const newCollapsed = new Set(collapsedSections);
    let hasChanges = false;

    navItems.forEach((item) => {
      if (isActiveSectionItem(item) && collapsedSections.has(item.title)) {
        newCollapsed.delete(item.title);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      setCollapsedSections(newCollapsed);
    }
  }, [pathname, collapsedSections, isActiveSectionItem]);

  const toggleSection = (sectionTitle: string) => {
    const newCollapsed = new Set(collapsedSections);
    if (newCollapsed.has(sectionTitle)) {
      newCollapsed.delete(sectionTitle);
    } else {
      newCollapsed.add(sectionTitle);
    }
    setCollapsedSections(newCollapsed);
  };

  const toggleSubSection = (subSectionTitle: string) => {
    const newCollapsed = new Set(collapsedSubSections);
    if (newCollapsed.has(subSectionTitle)) {
      newCollapsed.delete(subSectionTitle);
    } else {
      newCollapsed.add(subSectionTitle);
    }
    setCollapsedSubSections(newCollapsed);
  };

  const handleLogout = (e: React.MouseEvent) => {
    e.preventDefault();
    logout();
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="ml-2">Loading...</p>
      </div>
    );
  }

  // If not authenticated, don't render the dashboard layout
  // The middleware should handle the redirect, but this is a fallback
  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-lg">Please log in to access this page.</p>
          <Link href="/auth/login" className="text-primary hover:underline">
            Go to Login
          </Link>
        </div>
      </div>
    );
  }

  // Don't render until sidebar state is loaded to prevent hydration mismatch
  if (!isLoaded) {
    return (
      <div className="flex min-h-screen flex-col">
        <div className="flex flex-1">
          <aside className="hidden w-64 flex-col border-r bg-sidebar border-sidebar-border md:flex fixed h-full z-10">
            <div className="flex h-16 items-center border-b border-sidebar-border px-6 flex-shrink-0">
              <div className="h-6 w-16 bg-sidebar-accent rounded animate-pulse" />
            </div>
            <nav className="flex-1 overflow-y-auto py-6 px-4">
              <div className="flex flex-col gap-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="h-8 bg-sidebar-accent rounded animate-pulse"
                  />
                ))}
              </div>
            </nav>
          </aside>
          <main className="flex-1 md:ml-64">
            <div className="p-4 md:p-6">
              <div className="h-8 bg-muted rounded animate-pulse mb-4" />
              <div className="h-64 bg-muted rounded animate-pulse" />
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <NormalUserRoute>
      <div className="flex min-h-screen flex-col">
        {/* Mobile Header */}
        <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:hidden">
          <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="left"
              className="w-[240px] sm:w-[300px] pr-0 flex flex-col h-full bg-white border-border"
            >
              <div className="px-2 py-6 flex items-center flex-shrink-0">
                <Link
                  href="/"
                  className="flex items-center gap-2 font-extrabold text-lg text-custom-green logo-text"
                >
                  <img
                    src="/maitlogo.svg"
                    alt="MAIT Logo"
                    className="h-8 w-auto"
                  />
                  {/* MAIT */}
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  className="ml-auto"
                  onClick={() => setIsSidebarOpen(false)}
                >
                  <X className="h-5 w-5" />
                  <span className="sr-only">Close</span>
                </Button>
              </div>
              <nav className="flex flex-col gap-1 px-2 flex-1 overflow-y-auto min-h-0 pb-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                {navItems.map((item) => (
                  <div key={item.title}>
                    {item.href ? (
                      item.disabled ? (
                        <div
                          className={cn(
                            "flex items-center gap-2 rounded-lg px-3 py-2 text-sm",
                            "text-sidebar-disabled cursor-not-allowed opacity-75"
                          )}
                        >
                          {item.icon}
                          {item.title}
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          onClick={() => setIsSidebarOpen(false)}
                          className={cn(
                            "flex items-center gap-2 rounded-lg px-3 py-2 text-sm",
                            isActiveItem(item.href)
                              ? "bg-sidebar-accent text-sidebar-accent-foreground"
                              : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                          )}
                        >
                          {item.icon}
                          {item.title}
                        </Link>
                      )
                    ) : (
                      <>
                        <button
                          onClick={() => toggleSection(item.title)}
                          className={cn(
                            "flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-semibold transition-colors",
                            isActiveSectionItem(item)
                              ? "bg-sidebar-accent text-sidebar-accent-foreground"
                              : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                          )}
                        >
                          {item.icon}
                          <span className="flex-1 text-left">{item.title}</span>
                          {collapsedSections.has(item.title) ? (
                            <ChevronRight className="h-4 w-4 transition-transform" />
                          ) : (
                            <ChevronDown className="h-4 w-4 transition-transform" />
                          )}
                        </button>
                        {!collapsedSections.has(item.title) && item.subItems && (
                          <div className="ml-6 mt-1 space-y-1 border-l border-border pl-3">
                            {item.subItems.map((subItem) => (
                              <div key={subItem.title}>
                                {subItem.href ? (
                                  subItem.disabled ? (
                                    <div
                                      className={cn(
                                        "flex items-center gap-2 rounded-md px-2 py-1.5 text-sm font-medium transition-colors",
                                        "text-sidebar-disabled cursor-not-allowed opacity-75"
                                      )}
                                    >
                                      {subItem.icon}
                                      {subItem.title}
                                    </div>
                                  ) : (
                                    <Link
                                      href={subItem.href}
                                      onClick={() => setIsSidebarOpen(false)}
                                      className={cn(
                                        "flex items-center gap-2 rounded-md px-2 py-1.5 text-sm transition-colors",
                                        isActiveItem(subItem.href)
                                          ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                          : "text-sidebar-disabled hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                      )}
                                    >
                                      {subItem.icon}
                                      {subItem.title}
                                    </Link>
                                  )
                                ) : (
                                  <>
                                    <button
                                      onClick={() =>
                                        !subItem.disabled &&
                                        toggleSubSection(subItem.title)
                                      }
                                      disabled={subItem.disabled}
                                      className={cn(
                                        "flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm font-semibold transition-colors",
                                        subItem.disabled
                                          ? "text-sidebar-disabled cursor-not-allowed opacity-75"
                                          : isActiveSubSectionItem(subItem)
                                            ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                            : "text-sidebar-disabled hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                      )}
                                    >
                                      {subItem.icon}
                                      <span className="flex-1 text-left">
                                        {subItem.title}
                                      </span>
                                      {collapsedSubSections.has(subItem.title) ? (
                                        <ChevronRight className="h-4 w-4 transition-transform" />
                                      ) : (
                                        <ChevronDown className="h-4 w-4 transition-transform" />
                                      )}
                                    </button>
                                    {!collapsedSubSections.has(subItem.title) &&
                                      subItem.subItems && (
                                        <div className="ml-4 mt-1 space-y-1 border-l border-border pl-2">
                                          {subItem.subItems.map((subSubItem) =>
                                            subSubItem.disabled ? (
                                              <div
                                                key={subSubItem.href}
                                                className={cn(
                                                  "flex items-center gap-2 rounded-md px-2 py-1 text-sm font-medium transition-colors",
                                                  "text-sidebar-disabled cursor-not-allowed opacity-75"
                                                )}
                                              >
                                                {subSubItem.icon}
                                                {subSubItem.title}
                                              </div>
                                            ) : (
                                              <Link
                                                key={subSubItem.href}
                                                href={subSubItem.href}
                                                onClick={() =>
                                                  setIsSidebarOpen(false)
                                                }
                                                className={cn(
                                                  "flex items-center gap-2 rounded-md px-2 py-1 text-sm font-medium transition-colors",
                                                  isActiveItem(subSubItem.href)
                                                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-semibold"
                                                    : "text-sidebar-disabled hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                                )}
                                              >
                                                {subSubItem.icon}
                                                {subSubItem.title}
                                              </Link>
                                            )
                                          )}
                                        </div>
                                      )}
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                ))}
              </nav>
            </SheetContent>
          </Sheet>
          <Link
            href="/"
            className="flex items-center gap-2 font-extrabold text-lg text-custom-green logo-text"
          >
            <img src="/maitlogo.svg" alt="MAIT Logo" className="h-8 w-auto" />
            {/* MAIT */}
          </Link>
          <div className="ml-auto flex items-center gap-2">
            <NotificationCenter />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src="/placeholder-user.jpg"
                      alt={user?.name || user?.userId || "User"}
                    />
                    <AvatarFallback>
                      {user?.name
                        ? user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .substring(0, 2)
                          .toUpperCase()
                        : user?.userId
                          ? user.userId.substring(0, 2).toUpperCase()
                          : "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {user?.name || "My Account"}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/administration/integrations/profile">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/administration/integrations">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        <div className="flex flex-1">
          {/* Sidebar (desktop) */}
          <TooltipProvider>
            <aside
              className={cn(
                "hidden md:flex flex-col border-r bg-sidebar border-sidebar-border fixed h-full z-10 sidebar-transition",
                isCollapsed ? "sidebar-collapsed" : "sidebar-expanded"
              )}
            >
              <div className="flex h-16 items-center border-b border-sidebar-border px-3 flex-shrink-0 justify-between">
                {!isCollapsed && (
                  <Link
                    href="/"
                    className="flex items-center gap-2 font-semibold text-lg text-sidebar-foreground logo-text"
                  >
                    <img
                      src="/maitlogo.svg"
                      alt="MAIT Logo"
                      className="h-8 w-auto"
                    />
                    {/* MAIT */}
                  </Link>
                )}
                {isCollapsed && (
                  <Link
                    href="/"
                    className="flex items-center justify-center w-full"
                  >
                    <img
                      src="/maitlogo.svg"
                      alt="MAIT Logo"
                      className="h-8 w-auto"
                    />
                  </Link>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggle}
                  className="h-8 w-8 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                  title={`Sidebar is ${isCollapsed ? "collapsed" : "expanded"}`}
                >
                  {isCollapsed ? (
                    <PanelLeftOpen className="h-4 w-4 sidebar-toggle-icon" />
                  ) : (
                    <PanelLeftClose className="h-4 w-4 sidebar-toggle-icon" />
                  )}
                </Button>
              </div>
              <nav
                className={cn(
                  "flex-1 overflow-y-auto py-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent",
                  isCollapsed ? "px-2" : "px-4"
                )}
              >
                <div className="flex flex-col gap-1">
                  {navItems.map((item: NavItem) => (
                    <div key={item.title}>
                      {item.href ? (
                        item.disabled ? (
                          isCollapsed ? (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div
                                  className={cn(
                                    "flex items-center justify-center rounded-lg p-2 text-sm",
                                    "text-sidebar-disabled cursor-not-allowed opacity-75"
                                  )}
                                >
                                  {item.icon}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                className="sidebar-tooltip"
                              >
                                <p>{item.title}</p>
                              </TooltipContent>
                            </Tooltip>
                          ) : (
                            <div
                              className={cn(
                                "flex items-center gap-2 rounded-lg px-3 py-2 text-sm",
                                "text-sidebar-disabled cursor-not-allowed opacity-75"
                              )}
                            >
                              {item.icon}
                              <span className="sidebar-text">{item.title}</span>
                            </div>
                          )
                        ) : isCollapsed ? (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link
                                href={item.href}
                                className={cn(
                                  "flex items-center justify-center rounded-lg p-2 text-sm",
                                  isActiveItem(item.href)
                                    ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                    : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                )}
                              >
                                {item.icon}
                              </Link>
                            </TooltipTrigger>
                            <TooltipContent
                              side="right"
                              className="sidebar-tooltip"
                            >
                              <div className="font-medium">{item.title}</div>
                              {item.disabled && (
                                <div className="mt-1 text-xs opacity-75">
                                  Coming Soon
                                </div>
                              )}
                            </TooltipContent>
                          </Tooltip>
                        ) : (
                          <Link
                            href={item.href}
                            className={cn(
                              "flex items-center gap-2 rounded-lg px-3 py-2 text-sm",
                              isActiveItem(item.href)
                                ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                            )}
                          >
                            {item.icon}
                            <span className="sidebar-text">{item.title}</span>
                          </Link>
                        )
                      ) : (
                        <>
                          {isCollapsed ? (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <button
                                  onClick={() => toggleSection(item.title)}
                                  className={cn(
                                    "flex items-center justify-center rounded-lg p-2 text-sm font-semibold transition-colors",
                                    isActiveSectionItem(item)
                                      ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                  )}
                                >
                                  {item.icon}
                                </button>
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                className="sidebar-tooltip"
                              >
                                <div className="font-medium">{item.title}</div>
                                {item.subItems && item.subItems.length > 0 && (
                                  <div className="mt-1 text-xs opacity-75">
                                    {item.subItems.length} item
                                    {item.subItems.length !== 1 ? "s" : ""}
                                  </div>
                                )}
                              </TooltipContent>
                            </Tooltip>
                          ) : (
                            <button
                              onClick={() => toggleSection(item.title)}
                              className={cn(
                                "flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-semibold transition-colors",
                                isActiveSectionItem(item)
                                  ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                              )}
                            >
                              {item.icon}
                              <span className="flex-1 text-left sidebar-text">
                                {item.title}
                              </span>
                              {collapsedSections.has(item.title) ? (
                                <ChevronRight className="h-4 w-4 transition-transform" />
                              ) : (
                                <ChevronDown className="h-4 w-4 transition-transform" />
                              )}
                            </button>
                          )}
                          {!isCollapsed &&
                            !collapsedSections.has(item.title) &&
                            item.subItems && (
                              <div className="ml-6 mt-1 space-y-1 border-l border-border pl-3">
                                {item.subItems.map((subItem) => (
                                  <div key={subItem.title}>
                                    {subItem.href ? (
                                      subItem.disabled ? (
                                        <div
                                          className={cn(
                                            "flex items-center gap-2 rounded-md px-2 py-1.5 text-sm font-medium transition-colors",
                                            "text-sidebar-disabled cursor-not-allowed opacity-60"
                                          )}
                                        >
                                          {subItem.icon}
                                          {subItem.title}
                                        </div>
                                      ) : (
                                        <Link
                                          href={subItem.href}
                                          className={cn(
                                            "flex items-center gap-2 rounded-md px-2 py-1.5 text-sm transition-colors",
                                            isActiveItem(subItem.href)
                                              ? "bg-sidebar-accent text-sidebar-accent-foreground hover:bg-sidebar-accent/90"
                                              : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                          )}
                                        >
                                          {subItem.icon}
                                          {subItem.title}
                                        </Link>
                                      )
                                    ) : (
                                      <>
                                        <button
                                          onClick={() =>
                                            !subItem.disabled &&
                                            toggleSubSection(subItem.title)
                                          }
                                          disabled={subItem.disabled}
                                          className={cn(
                                            "flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-sm font-semibold transition-colors",
                                            subItem.disabled
                                              ? "text-sidebar-disabled cursor-not-allowed opacity-60"
                                              : isActiveSubSectionItem(subItem)
                                                ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                                : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                          )}
                                        >
                                          {subItem.icon}
                                          <span className="flex-1 text-left">
                                            {subItem.title}
                                          </span>
                                          {collapsedSubSections.has(
                                            subItem.title
                                          ) ? (
                                            <ChevronRight className="h-4 w-4 transition-transform" />
                                          ) : (
                                            <ChevronDown className="h-4 w-4 transition-transform" />
                                          )}
                                        </button>
                                        {!collapsedSubSections.has(
                                          subItem.title
                                        ) &&
                                          subItem.subItems && (
                                            <div className="ml-4 mt-1 space-y-1 border-l border-border pl-2">
                                              {subItem.subItems.map(
                                                (subSubItem) =>
                                                  subSubItem.disabled ? (
                                                    <div
                                                      key={subSubItem.href}
                                                      className={cn(
                                                        "flex items-center gap-2 rounded-md px-2 py-1 text-sm font-medium transition-colors",
                                                        "text-sidebar-disabled cursor-not-allowed opacity-60"
                                                      )}
                                                    >
                                                      {subSubItem.icon}
                                                      {subSubItem.title}
                                                    </div>
                                                  ) : (
                                                    <Link
                                                      key={subSubItem.href}
                                                      href={subSubItem.href}
                                                      className={cn(
                                                        "flex items-center gap-2 rounded-md px-2 py-1 text-sm font-medium transition-colors",
                                                        isActiveItem(
                                                          subSubItem.href
                                                        )
                                                          ? "bg-sidebar-accent text-sidebar-accent-foreground font-semibold hover:bg-sidebar-accent/90"
                                                          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                                      )}
                                                    >
                                                      {subSubItem.icon}
                                                      {subSubItem.title}
                                                    </Link>
                                                  )
                                              )}
                                            </div>
                                          )}
                                      </>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </nav>
            </aside>
          </TooltipProvider>

          {/* Main Content */}
          <main
            className={cn(
              "flex-1 main-content-transition overflow-hidden",
              "md:ml-64", // Default margin for medium screens and up
              isCollapsed
                ? "md:main-content-collapsed"
                : "md:main-content-expanded"
            )}
          >
            {/* Desktop Header */}
            <header className="sticky top-0 z-20 h-16 items-center gap-4 border-b bg-background px-6 hidden md:flex">
              <div className="flex flex-1 items-center gap-4">
                <div className="relative w-full max-w-md">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search..."
                    className="w-full bg-background pl-8 md:w-[300px] lg:w-[400px]"
                  />
                </div>
              </div>
              <div className="flex items-center gap-4">
                <NotificationCenter />
                {/* <ThemeToggle /> */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src="/placeholder-user.jpg"
                          alt={user?.name || user?.userId || "User"}
                        />
                        <AvatarFallback>
                          {user?.name
                            ? user.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .substring(0, 2)
                              .toUpperCase()
                            : user?.userId
                              ? user.userId.substring(0, 2).toUpperCase()
                              : "U"}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>
                      {user?.name || "My Account"}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/administration/integrations/profile">
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/administration/integrations">Settings</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </header>

            {/* Page Content */}
            <div className="p-4 md:p-6">{children}</div>
          </main>
        </div>
        <FloatingAIChat />
      </div>
    </NormalUserRoute>
  );
}
