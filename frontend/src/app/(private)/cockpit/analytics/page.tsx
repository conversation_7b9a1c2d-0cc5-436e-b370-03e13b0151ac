/**
 * Analytics Page
 * Comprehensive analytics dashboard for contracts and licenses
 */

"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Download,
  Calendar,
  Filter,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  HandCoins,
  Clock,
  AlertTriangle,
  CheckCircle2,
  FileText,
  Shield,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

// Sample data for charts
const contractValueData = [
  { month: "Jan", value: 45000 },
  { month: "Feb", value: 52000 },
  { month: "Mar", value: 49000 },
  { month: "Apr", value: 63000 },
  { month: "May", value: 58000 },
  { month: "Jun", value: 72000 },
  { month: "Jul", value: 80000 },
  { month: "Aug", value: 92000 },
  { month: "Sep", value: 86000 },
  { month: "Oct", value: 94000 },
  { month: "Nov", value: 105000 },
  { month: "Dec", value: 112000 },
];

const licenseUsageData = [
  { month: "Jan", actual: 82, allocated: 100 },
  { month: "Feb", actual: 87, allocated: 100 },
  { month: "Mar", actual: 89, allocated: 100 },
  { month: "Apr", actual: 92, allocated: 100 },
  { month: "May", actual: 94, allocated: 100 },
  { month: "Jun", actual: 91, allocated: 100 },
  { month: "Jul", actual: 88, allocated: 100 },
  { month: "Aug", actual: 85, allocated: 100 },
  { month: "Sep", actual: 90, allocated: 100 },
  { month: "Oct", actual: 92, allocated: 100 },
  { month: "Nov", actual: 95, allocated: 100 },
  { month: "Dec", actual: 97, allocated: 100 },
];

const contractTypeDistribution = [
  { type: "Service Agreements", count: 42, percentage: 35 },
  { type: "Software Licenses", count: 28, percentage: 23 },
  { type: "NDAs", count: 18, percentage: 15 },
  { type: "Vendor Agreements", count: 16, percentage: 13 },
  { type: "Employment Contracts", count: 10, percentage: 8 },
  { type: "Other", count: 7, percentage: 6 },
];

const riskDistribution = [
  { level: "High", count: 8, percentage: 7 },
  { level: "Medium", count: 24, percentage: 20 },
  { level: "Low", count: 89, percentage: 73 },
];

const keyInsights = [
  {
    title: "License Optimization Opportunity",
    description:
      "20% of software licenses are underutilized. Potential annual savings of $45,000.",
    type: "cost_saving",
    icon: <HandCoins className="h-5 w-5 text-green-500" />,
  },
  {
    title: "Contract Renewal Risk",
    description:
      "5 high-value contracts expiring in the next 30 days without renewal plans.",
    type: "risk_alert",
    icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
  },
  {
    title: "Compliance Improvement",
    description:
      "Overall compliance score improved by 12% in the last quarter.",
    type: "trend",
    icon: <TrendingUp className="h-5 w-5 text-blue-500" />,
  },
  {
    title: "Negotiation Opportunity",
    description:
      "3 vendor contracts have terms significantly above market rates.",
    type: "optimization",
    icon: <TrendingDown className="h-5 w-5 text-purple-500" />,
  },
];

export default function AnalyticsPage() {
  const [timeframe, setTimeframe] = useState("last_12_months");

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into your contracts and licenses
          </p>
        </div>
        <div className="flex gap-2">
          <Select defaultValue={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_30_days">Last 30 Days</SelectItem>
              <SelectItem value="last_90_days">Last 90 Days</SelectItem>
              <SelectItem value="last_12_months">Last 12 Months</SelectItem>
              <SelectItem value="ytd">Year to Date</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Contract Value
            </CardTitle>
            <HandCoins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1.24M</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">+12.5%</span> from
              previous period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Contracts
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">121</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">+8.2%</span> from
              previous period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              License Utilization
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">+3.1%</span> from
              previous period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Time to Signature
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2 days</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingDown className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">-18.3%</span> from
              previous period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="licenses">Licenses</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Key Insights</CardTitle>
              <CardDescription>
                AI-generated insights based on your contract and license data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {keyInsights.map((insight, index) => (
                  <Card key={index} className="border-l-4 border-l-primary">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        {insight.icon}
                        <CardTitle className="text-base">
                          {insight.title}
                        </CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        {insight.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Charts */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Contract Value Trend</CardTitle>
                <CardDescription>
                  Total contract value over time
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px] flex items-center justify-center">
                <div className="w-full h-full flex items-center justify-center bg-muted/20 rounded-md">
                  <LineChart className="h-16 w-16 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">
                    Contract Value Chart
                  </span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>License Utilization</CardTitle>
                <CardDescription>
                  Actual vs. allocated license usage
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px] flex items-center justify-center">
                <div className="w-full h-full flex items-center justify-center bg-muted/20 rounded-md">
                  <BarChart className="h-16 w-16 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">
                    License Utilization Chart
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Contract Type Distribution</CardTitle>
                <CardDescription>
                  Breakdown of contracts by type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="w-1/3 flex items-center justify-center">
                    <PieChart className="h-24 w-24 text-muted-foreground" />
                  </div>
                  <div className="w-2/3">
                    <ul className="space-y-2">
                      {contractTypeDistribution.map((item, index) => (
                        <li
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <div
                              className={`w-3 h-3 rounded-full bg-primary-${
                                (index % 5) + 1
                              }00 mr-2`}
                            ></div>
                            <span className="text-sm">{item.type}</span>
                          </div>
                          <div className="text-sm font-medium">
                            {item.percentage}%
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Risk Distribution</CardTitle>
                <CardDescription>Contracts by risk level</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="w-1/3 flex items-center justify-center">
                    <PieChart className="h-24 w-24 text-muted-foreground" />
                  </div>
                  <div className="w-2/3">
                    <ul className="space-y-4">
                      {riskDistribution.map((item, index) => (
                        <li key={index} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div
                                className={`w-3 h-3 rounded-full mr-2 ${
                                  item.level === "High"
                                    ? "bg-red-500"
                                    : item.level === "Medium"
                                    ? "bg-amber-500"
                                    : "bg-green-500"
                                }`}
                              ></div>
                              <span className="text-sm">{item.level} Risk</span>
                            </div>
                            <div className="text-sm font-medium">
                              {item.percentage}%
                            </div>
                          </div>
                          <div className="w-full bg-muted rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${
                                item.level === "High"
                                  ? "bg-red-500"
                                  : item.level === "Medium"
                                  ? "bg-amber-500"
                                  : "bg-green-500"
                              }`}
                              style={{ width: `${item.percentage}%` }}
                            ></div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Analytics</CardTitle>
              <CardDescription>
                Detailed analytics for your contract portfolio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center">
                <div className="text-center">
                  <BarChart className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    Contract Analytics Dashboard
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Detailed contract analytics with performance metrics,
                    renewal forecasts, and value analysis would be displayed
                    here.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="licenses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Analytics</CardTitle>
              <CardDescription>
                Detailed analytics for your license portfolio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center">
                <div className="text-center">
                  <BarChart className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    License Analytics Dashboard
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Detailed license analytics with utilization metrics, cost
                    analysis, and optimization recommendations would be
                    displayed here.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Analytics</CardTitle>
              <CardDescription>
                Compliance metrics and risk assessment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center">
                <div className="text-center">
                  <Shield className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Compliance Dashboard</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Detailed compliance analytics with risk assessment,
                    obligation tracking, and regulatory compliance metrics would
                    be displayed here.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Forecasting</CardTitle>
              <CardDescription>
                Predictive analytics for contract renewals and budget planning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px] flex items-center justify-center">
                <div className="text-center">
                  <TrendingUp className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Forecasting Dashboard</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Predictive analytics for contract renewals, budget
                    forecasting, and future license requirements would be
                    displayed here.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Upcoming Renewals */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Renewals</CardTitle>
          <CardDescription>
            Contracts and licenses due for renewal in the next 90 days
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((item) => (
              <div
                key={item}
                className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">
                      Microsoft Azure Enterprise Agreement
                    </h4>
                    <Badge
                      variant={
                        item === 1
                          ? "destructive"
                          : item <= 3
                          ? "default"
                          : "outline"
                      }
                    >
                      {item === 1
                        ? "Due in 7 days"
                        : item === 2
                        ? "Due in 14 days"
                        : item === 3
                        ? "Due in 30 days"
                        : "Due in 60+ days"}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Annual value: $125,000 • Auto-renewal: No
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                  <Button size="sm">Renew Now</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <Button variant="outline" className="w-full">
            View All Renewals
          </Button>
        </CardFooter>
      </Card>

      {/* Optimization Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Optimization Recommendations</CardTitle>
          <CardDescription>
            AI-generated recommendations to optimize your contract and license
            portfolio
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="rounded-lg border p-4">
              <div className="flex items-start gap-4">
                <div className="rounded-full bg-green-100 p-2">
                  <HandCoins className="h-5 w-5 text-green-600" />
                </div>
                <div className="space-y-1">
                  <h4 className="font-medium">
                    License Consolidation Opportunity
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Consolidating 3 separate Adobe Creative Cloud licenses into
                    a single enterprise agreement could save approximately
                    $12,000 annually.
                  </p>
                  <div className="pt-2">
                    <Button variant="outline" size="sm">
                      View Analysis
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            <div className="rounded-lg border p-4">
              <div className="flex items-start gap-4">
                <div className="rounded-full bg-blue-100 p-2">
                  <RefreshCw className="h-5 w-5 text-blue-600" />
                </div>
                <div className="space-y-1">
                  <h4 className="font-medium">Early Renewal Opportunity</h4>
                  <p className="text-sm text-muted-foreground">
                    Renewing the Salesforce agreement 3 months early with a
                    multi-year commitment could lock in current pricing before
                    the announced 12% increase.
                  </p>
                  <div className="pt-2">
                    <Button variant="outline" size="sm">
                      View Analysis
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            <div className="rounded-lg border p-4">
              <div className="flex items-start gap-4">
                <div className="rounded-full bg-amber-100 p-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                </div>
                <div className="space-y-1">
                  <h4 className="font-medium">Compliance Risk Mitigation</h4>
                  <p className="text-sm text-muted-foreground">
                    Two vendor agreements have outdated data protection clauses
                    that don't meet current regulatory requirements. Recommend
                    amendment to ensure compliance.
                  </p>
                  <div className="pt-2">
                    <Button variant="outline" size="sm">
                      View Analysis
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <Button variant="outline" className="w-full">
            View All Recommendations
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
