"use client";

import React, { useState, useCallback } from "react";
import Link from "next/link";
import { BarChart3, FileText, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

// Dynamic widget components
import { WidgetSection } from "@/components/dashboard/DynamicWidget";

// Services
import { DashboardService } from "@/services/dashboardService";

// Hooks
import { useWidgets } from "@/hooks/useWidgets";

export default function DashboardPage() {
  // Widget management
  const { widgets, loading: widgetsLoading, updateWidgetVisibility } = useWidgets();

  // Widget data management
  const [widgetData, setWidgetData] = useState<Record<string, any>>({});
  const [widgetLoading, setWidgetLoading] = useState<Record<string, boolean>>({});
  const [widgetErrors, setWidgetErrors] = useState<Record<string, string>>({});

  // Helper function to get the data key for a widget type
  const getDataKey = (widgetType: string): string => {
    if (['realised-savings', 'potential-savings', 'key-obligations'].includes(widgetType)) {
      return 'summary-shared';
    }
    if (['total-agreements', 'high-value-agreements', 'critical-agreements', 'expired-agreements',
      'aging-contracts', 'customer-supplier-paper', 'agreement-types', 'service-types'].includes(widgetType)) {
      return 'portfolio-shared';
    }
    return widgetType; // For individual widget types
  };

  // Fetch data for a specific widget type
  const fetchWidgetData = useCallback(async (widgetType: string) => {
    try {
      setWidgetLoading(prev => ({ ...prev, [widgetType]: true }));
      setWidgetErrors(prev => ({ ...prev, [widgetType]: '' }));

      let data;
      switch (widgetType) {
        case 'expiring-contracts':
          data = await DashboardService.getExpiringContracts();
          break;
        case 'auto-renewals':
          data = await DashboardService.getAutoRenewals();
          break;
        case 'auto-renewals-classification':
          data = await DashboardService.getAutoRenewalsByClassification();
          break;
        case 'summary-shared':
          data = await DashboardService.getSummary();
          break;
        case 'portfolio-shared':
          data = await DashboardService.getPortfolioOverview();
          break;
        default:
          console.warn(`Unknown widget type: ${widgetType}`);
          return;
      }

      setWidgetData(prev => ({ ...prev, [widgetType]: data }));
    } catch (error) {
      console.error(`Error fetching data for ${widgetType}:`, error);
      setWidgetErrors(prev => ({
        ...prev,
        [widgetType]: `Failed to load ${widgetType} data`
      }));
    } finally {
      setWidgetLoading(prev => ({ ...prev, [widgetType]: false }));
    }
  }, []);

  // Fetch data for all widgets when widgets are loaded
  React.useEffect(() => {
    if (widgets) {
      const allWidgets = [...(widgets.priority || []), ...(widgets.portfolio || [])];
      const uniqueWidgetTypes = Array.from(new Set(allWidgets.map(w => w.widgetType)));

      // Group widget types by their data source to avoid duplicate API calls
      const priorityWidgetTypes = uniqueWidgetTypes.filter(type =>
        ['expiring-contracts', 'auto-renewals', 'auto-renewals-classification'].includes(type)
      );
      const summaryWidgetTypes = uniqueWidgetTypes.filter(type =>
        ['realised-savings', 'potential-savings', 'key-obligations'].includes(type)
      );
      const portfolioWidgetTypes = uniqueWidgetTypes.filter(type =>
        ['total-agreements', 'high-value-agreements', 'critical-agreements', 'expired-agreements',
          'aging-contracts', 'customer-supplier-paper', 'agreement-types', 'service-types'].includes(type)
      );

      // Fetch data for individual widget types (one call each)
      priorityWidgetTypes.forEach(widgetType => {
        fetchWidgetData(widgetType);
      });

      // Fetch summary data once and share across summary widgets
      if (summaryWidgetTypes.length > 0) {
        fetchWidgetData('summary-shared'); // Use a shared key
      }

      // Fetch portfolio data once and share across portfolio widgets
      if (portfolioWidgetTypes.length > 0) {
        fetchWidgetData('portfolio-shared'); // Use a shared key
      }
    }
  }, [widgets, fetchWidgetData]);

  // Refresh all data
  const refreshAllData = () => {
    // Trigger refresh for all widgets by reloading the page
    window.location.reload();
    toast.success("Dashboard data refreshed");
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          {/* <p className="text-muted-foreground">
            Real-time insights from your contract portfolio powered by AI
            extraction
          </p> */}
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshAllData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh All
          </Button>
          <Button variant="outline" asChild>
            <Link href="/cockpit/dashboard/customizable">
              <BarChart3 className="mr-2 h-4 w-4" />
              Customize
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/contract-management/contracts">
              <FileText className="mr-2 h-4 w-4" />
              Contract Repository
            </Link>
          </Button>
        </div>
      </div>



      {/* Priorities Section */}
      {widgetsLoading ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold tracking-tight">Priorities</h3>
              <p className="text-muted-foreground">Loading widgets...</p>
            </div>
          </div>
          <div className="grid gap-6 lg:grid-cols-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-64 bg-gray-100 animate-pulse rounded-lg" />
            ))}
          </div>
        </div>
      ) : widgets && widgets.priority && widgets.priority.length > 0 ? (
        <WidgetSection
          title="Priorities"
          description="Critical contract actions requiring attention"
          widgets={widgets.priority}
          category="priority"
          data={Object.fromEntries(
            widgets.priority.map(w => [w.widgetType, widgetData[getDataKey(w.widgetType)]])
          )}
          loading={Object.fromEntries(
            widgets.priority.map(w => [w.widgetType, widgetLoading[getDataKey(w.widgetType)]])
          )}
          errors={Object.fromEntries(
            widgets.priority.map(w => [w.widgetType, widgetErrors[getDataKey(w.widgetType)]])
          )}
          onRefresh={Object.fromEntries(
            widgets.priority.map(w => [w.widgetType, () => fetchWidgetData(getDataKey(w.widgetType))])
          )}
          onToggleVisibility={updateWidgetVisibility}
        />
      ) : (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold tracking-tight">Priorities</h3>
              <p className="text-muted-foreground">No priority widgets available</p>
            </div>
          </div>
        </div>
      )}

      {/* Portfolio Summary Section */}
      {widgetsLoading ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold tracking-tight">Portfolio Summary</h3>
              <p className="text-muted-foreground">Loading widgets...</p>
            </div>
          </div>
          <div className="grid gap-6 lg:grid-cols-4">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <div key={i} className="h-64 bg-gray-100 animate-pulse rounded-lg" />
            ))}
          </div>
        </div>
      ) : widgets && widgets.portfolio && widgets.portfolio.length > 0 ? (
        <WidgetSection
          title="Portfolio Summary"
          description="Comprehensive overview of your contract portfolio"
          widgets={widgets.portfolio}
          category="portfolio"
          data={Object.fromEntries(
            widgets.portfolio.map(w => [w.widgetType, widgetData[getDataKey(w.widgetType)]])
          )}
          loading={Object.fromEntries(
            widgets.portfolio.map(w => [w.widgetType, widgetLoading[getDataKey(w.widgetType)]])
          )}
          errors={Object.fromEntries(
            widgets.portfolio.map(w => [w.widgetType, widgetErrors[getDataKey(w.widgetType)]])
          )}
          onRefresh={Object.fromEntries(
            widgets.portfolio.map(w => [w.widgetType, () => fetchWidgetData(getDataKey(w.widgetType))])
          )}
          onToggleVisibility={updateWidgetVisibility}
        />
      ) : (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold tracking-tight">Portfolio Summary</h3>
              <p className="text-muted-foreground">No portfolio widgets available</p>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}
