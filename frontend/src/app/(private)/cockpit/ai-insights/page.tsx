/**
 * AI Insights Page
 * Displays AI-generated insights from contracts and licenses
 */

"use client";

import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { InsightsVisualization } from "@/components/ai/InsightsVisualization";
import { DocumentUpload } from "@/components/ai/DocumentUpload";
import { GuidedWorkflow } from "@/components/ai/GuidedWorkflow";

// Sample data for demonstration
const riskDistribution = [
  { category: "Legal", count: 12, color: "#8b5cf6" },
  { category: "Financial", count: 8, color: "#22c55e" },
  { category: "Operational", count: 5, color: "#3b82f6" },
  { category: "Compliance", count: 7, color: "#eab308" },
  { category: "Security", count: 4, color: "#ef4444" },
  { category: "Reputation", count: 3, color: "#ec4899" },
];

const licenseCostData = [
  { name: "Software A", currentCost: 12000, projectedCost: 8000 },
  { name: "Software B", currentCost: 8500, projectedCost: 8500 },
  { name: "Software C", currentCost: 15000, projectedCost: 10000 },
  { name: "Software D", currentCost: 5000, projectedCost: 3000 },
  { name: "Software E", currentCost: 7500, projectedCost: 7500 },
];

const contractExpirationData = [
  { month: "Jan", count: 2 },
  { month: "Feb", count: 1 },
  { month: "Mar", count: 3 },
  { month: "Apr", count: 5 },
  { month: "May", count: 2 },
  { month: "Jun", count: 0 },
  { month: "Jul", count: 4 },
  { month: "Aug", count: 1 },
  { month: "Sep", count: 3 },
  { month: "Oct", count: 2 },
  { month: "Nov", count: 6 },
  { month: "Dec", count: 1 },
];

const topRisks = [
  "Termination clause lacks clear notice period requirements",
  "Indemnification provisions are one-sided and expose to significant liability",
  "Auto-renewal terms could lead to unexpected financial commitments",
  "Data protection clauses don't meet current regulatory requirements",
  "Intellectual property rights are ambiguously defined",
];

const topSavings = [
  "Reduce Software A licenses by 30% based on usage patterns",
  "Consolidate multiple Software C instances to enterprise license",
  "Remove unused Software D licenses (no usage in 90+ days)",
  "Renegotiate Software B pricing based on volume discount eligibility",
  "Switch Software E to usage-based pricing model",
];

// Sample guided workflows
const sampleWorkflows = [
  {
    id: "contract-risk-assessment",
    title: "Contract Risk Assessment",
    description:
      "Analyze a contract for potential risks and get mitigation recommendations",
    aiAssistant: true,
    steps: [
      {
        id: "upload-contract",
        title: "Upload Contract",
        description: "Upload the Agreement Document you want to analyze",
        component: <DocumentUpload />,
        completed: false,
      },
      {
        id: "review-risks",
        title: "Review Identified Risks",
        description: "Review the AI-identified risks in your contract",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Risk Analysis Results</CardTitle>
              <CardDescription>
                The AI has identified the following risks in your contract
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Upload a contract in the previous step to see the analysis
                results here.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
      {
        id: "get-recommendations",
        title: "Get Mitigation Recommendations",
        description: "Get AI recommendations for mitigating identified risks",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Mitigation Recommendations</CardTitle>
              <CardDescription>
                AI-generated recommendations to address the identified risks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Complete the previous steps to get mitigation recommendations.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
      {
        id: "export-report",
        title: "Export Risk Report",
        description: "Export a comprehensive risk assessment report",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Export Risk Report</CardTitle>
              <CardDescription>
                Generate a comprehensive risk assessment report
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Complete the previous steps to generate and export a report.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
        optional: true,
      },
    ],
  },
  {
    id: "license-optimization",
    title: "License Optimization",
    description: "Analyze license usage and get cost-saving recommendations",
    aiAssistant: true,
    steps: [
      {
        id: "analyze-usage",
        title: "Analyze License Usage",
        description: "Analyze current license usage across your organization",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>License Usage Analysis</CardTitle>
              <CardDescription>
                Analyzing your current license usage patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Click "Start Analysis" to begin analyzing your license usage
                data.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
      {
        id: "review-recommendations",
        title: "Review Recommendations",
        description: "Review AI-generated optimization recommendations",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>
                AI-generated recommendations for optimizing your licenses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Complete the previous step to see optimization recommendations.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
      {
        id: "implementation-plan",
        title: "Create Implementation Plan",
        description: "Create a plan to implement the recommended optimizations",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Implementation Plan</CardTitle>
              <CardDescription>
                Plan for implementing the recommended optimizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Complete the previous steps to create an implementation plan.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
    ],
  },
  {
    id: "microsoft-contract-assessment",
    title: "Microsoft Contract Assessment",
    description: "Check if your Microsoft contracts are compliant",
    aiAssistant: true,
    steps: [
      {
        id: "upload-contract",
        title: "Upload Contract",
        description: "Upload the Agreement Document you want to check",
        component: <DocumentUpload />,
        completed: false,
      },
      {
        id: "review-results",
        title: "Review Compliance Results",
        description: "Review the AI-generated compliance check results",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Compliance Check Results</CardTitle>
              <CardDescription>
                The AI has checked your contract for compliance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Upload a contract in the previous step to see the compliance
                check results here.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
    ],
  },
  {
    id: "aws-contract-assessment",
    title: "AWS Contract Assessment",
    description: "Check if your AWS contracts are compliant",
    aiAssistant: true,
    steps: [
      {
        id: "upload-contract",
        title: "Upload Contract",
        description: "Upload the Agreement Document you want to check",
        component: <DocumentUpload />,
        completed: false,
      },
      {
        id: "review-results",
        title: "Review Compliance Results",
        description: "Review the AI-generated compliance check results",
        component: (
          <Card>
            <CardHeader>
              <CardTitle>Compliance Check Results</CardTitle>
              <CardDescription>
                The AI has checked your contract for compliance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Upload a contract in the previous step to see the compliance
                check results here.
              </p>
            </CardContent>
          </Card>
        ),
        completed: false,
      },
    ],
  },
];

export default function AIInsightsPage() {
  // Add a handler for the onViewDetails prop
  const handleViewDetails = (type: string) => {
    console.log(`View details for ${type}`);
  };

  // Add a handler for workflow completion
  const handleWorkflowComplete = (workflowId: string) => {
    console.log(`Workflow ${workflowId} completed`);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">AI Insights</h1>
          <p className="text-muted-foreground">
            AI-generated insights from your contracts and licenses
          </p>
        </div>

        <Tabs defaultValue="dashboard" className="space-y-4">
          <TabsList>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="upload">Upload Document</TabsTrigger>
            <TabsTrigger value="workflows">Guided Workflows</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-4">
            <InsightsVisualization
              riskDistribution={riskDistribution}
              licenseCostData={licenseCostData}
              contractExpirationData={contractExpirationData}
              topRisks={topRisks}
              topSavings={topSavings}
              onViewDetails={handleViewDetails}
            />
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DocumentUpload />

              <Card>
                <CardHeader>
                  <CardTitle>Why Upload Documents?</CardTitle>
                  <CardDescription>
                    Benefits of AI-powered document analysis
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="font-medium">
                      Automated Risk Identification
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      AI automatically identifies potential risks in your
                      contracts, saving hours of manual review.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Intelligent Insights</h3>
                    <p className="text-sm text-muted-foreground">
                      Get AI-powered insights and recommendations based on your
                      specific documents.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Enhanced Searchability</h3>
                    <p className="text-sm text-muted-foreground">
                      Documents are indexed for semantic search, allowing you to
                      find information quickly.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Secure Processing</h3>
                    <p className="text-sm text-muted-foreground">
                      All documents are processed securely with enterprise-grade
                      encryption and data protection.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="workflows" className="space-y-4">
            <GuidedWorkflow
              workflows={sampleWorkflows}
              onComplete={handleWorkflowComplete}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
