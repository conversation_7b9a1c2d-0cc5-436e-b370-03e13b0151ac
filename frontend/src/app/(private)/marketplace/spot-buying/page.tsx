"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

export default function SpotBuyingPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Spot Buying
        </h1>
        <p className="text-muted-foreground">
          Quick procurement for immediate needs
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Spot Buying Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Spot buying and quick procurement tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
