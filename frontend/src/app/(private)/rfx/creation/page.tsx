"use client";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

export default function RFXCreationPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          RFX Creation & Management
        </h1>
        <p className="text-muted-foreground">
          Create and manage RFP, RFQ, and RFI processes
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>RFX Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - RFX creation and management tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
