"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

export default function RFXShortlistPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Shortlist & Due Diligence
        </h1>
        <p className="text-muted-foreground">
          Shortlist suppliers and conduct due diligence
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Shortlist Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Supplier shortlisting and due diligence tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
