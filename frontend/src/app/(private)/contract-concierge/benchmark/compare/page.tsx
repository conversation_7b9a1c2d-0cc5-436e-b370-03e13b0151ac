"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  HandCoins,
  Users,
  AlertTriangle,
  CheckCircle,
  ScrollText,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { contractService, Contract } from "@/services/contractService";
import { useAuth } from "@/contexts/AuthContext";
import { ContractComparison } from "@/components/contracts/ContractComparison";
import { ContractStatusBadge } from "@/components/contracts/ContractStatusBadge";
import { formatContractValue } from "@/lib/format-utils";
import { toast } from "sonner";

function ContractCompareContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading } = useAuth();

  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to get agreement classification
  const getAgreementClassification = (agreementType: string | undefined): string => {
    if (!agreementType) return "Software";

    switch (agreementType) {
      case "MSA":
      case "SLA":
      case "SOW":
      case "NDA":
        return "Service";
      case "PO":
      case "ORDER":
      case "INVOICE":
        return "Hardware";
      default:
        return "Software";
    }
  };

  useEffect(() => {
    if (!user && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (user) {
      const contractIds = searchParams.getAll("ids");

      if (contractIds.length < 2) {
        toast.error("At least 2 contracts are required for comparison");
        router.push("/contract-concierge/benchmark/internal");
        return;
      }

      fetchContractsForComparison(contractIds);
    }
  }, [user, isLoading, searchParams, router]);

  const fetchContractsForComparison = async (ids: string[]) => {
    setLoading(true);
    setError(null);

    try {
      const contractsData = await contractService.getContractsForComparison(
        ids
      );
      setContracts(contractsData);
    } catch (error) {
      console.error("Error fetching contracts for comparison:", error);
      setError("Failed to load contracts for comparison");
      toast.error("Failed to load contracts for comparison");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Skeleton className="h-10 w-32" />
          </div>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-48" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || contracts.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/contract-concierge/benchmark/internal">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Benchmark
              </Link>
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg mb-2">Unable to Load Contracts</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              {error ||
                "The selected contracts could not be loaded for comparison. Please try again."}
            </p>
            <Button asChild className="mt-4">
              <Link href="/contract-concierge/benchmark/internal">
                Return to Benchmark
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/contract-concierge/benchmark/internal">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Benchmark
            </Link>
          </Button>
        </div>
      </div>

      {/* Page Title */}
      <div>
        <h1
          className="text-3xl font-semibold tracking-tight"
          style={{
            background: "linear-gradient(to right, #09260D, #0a2e0f)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Contract Benchmark Comparison
        </h1>
        {/* <p className="text-muted-foreground">
          Benchmarking {contracts.length} contracts to identify performance
          patterns and optimization opportunities
        </p> */}
      </div>

      {/* Contract Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {contracts.map((contract) => (
          <Card
            key={contract.id}
            className="relative transition-all duration-200 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
            style={{
              borderColor: "#09260D",
            }}
          >
            {/* Contract Name Indicator */}
            {/* <div className="absolute top-2 left-2">
              <div
                className="px-2 py-0.5 rounded-md text-xs font-normal flex items-center gap-1 text-white max-w-[calc(100%-4rem)]"
                style={{ backgroundColor: "#09260D" }}
              >
                <CheckCircle className="h-3 w-3" />
                <span className="hidden sm:inline truncate">
                  {contract.title.length > 20 ? `${contract.title.substring(0, 20)}...` : contract.title}
                </span>
              </div>
            </div> */}

            <CardContent className="p-4">
              {/* Contract Title - Main Heading */}
              <h3 className="text-sm font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 min-h-[2.5rem] leading-tight">
                {contract.title}
              </h3>

              {/* Key Metrics - Consistent Layout */}
              <div className="space-y-2 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    Supplier
                  </span>
                  <span className="text-gray-700 dark:text-gray-300 font-medium truncate max-w-[100px]">
                    {contract.provider || contract.counterparty || "N/A"}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <HandCoins className="h-3 w-3" />
                    Value
                  </span>
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    {formatContractValue(
                      contract.value,
                      (contract as any).metadata?.currency,
                      true
                    )}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <ScrollText className="h-3 w-3" />
                    Classification
                  </span>
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    {getAgreementClassification(contract.agreementType)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Status
                  </span>
                  <ContractStatusBadge
                    status={contract.status || "Unknown"}
                    variant="compact"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Comparison Component */}
      <ContractComparison contracts={contracts} />
    </div>
  );
}

export default function ContractComparePage() {
  return (
    <Suspense
      fallback={
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Skeleton className="h-10 w-32" />
            </div>
          </div>

          <div className="space-y-4">
            <Skeleton className="h-8 w-64" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-48" />
              ))}
            </div>
          </div>
        </div>
      }
    >
      <ContractCompareContent />
    </Suspense>
  );
}
