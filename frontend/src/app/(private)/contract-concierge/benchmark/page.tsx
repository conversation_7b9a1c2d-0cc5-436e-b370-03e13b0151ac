"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Building, Globe, ArrowRight, Lock } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface BenchmarkOptionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  isActive: boolean;
  onClick?: () => void;
}

function BenchmarkOptionCard({
  title,
  description,
  icon,
  features,
  isActive,
  onClick,
}: BenchmarkOptionProps) {
  return (
    <Card
      className={`group relative overflow-hidden border-2 transition-all duration-300 ${isActive
          ? "hover:shadow-lg cursor-pointer"
          : "border-gray-200 opacity-60 cursor-not-allowed"
        }`}
      style={{
        borderColor: isActive ? "#09260D" : undefined,
      }}
    >
      <div
        className={`absolute top-0 left-0 w-full h-1 ${isActive ? "" : "bg-gradient-to-r from-gray-300 to-gray-400"
          }`}
        style={{
          background: isActive
            ? "linear-gradient(to right, #09260D, #0a2e0f)"
            : undefined,
        }}
      />
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div
            className={`p-3 rounded-lg shadow-sm ${isActive ? "" : "bg-gradient-to-br from-gray-400 to-gray-500"
              }`}
            style={{
              background: isActive
                ? "linear-gradient(to bottom right, #09260D, #0a2e0f)"
                : undefined,
            }}
          >
            <div className="text-white">{icon}</div>
          </div>
          <Badge
            variant={isActive ? "default" : "secondary"}
            className={
              isActive
                ? "text-white dark:text-white"
                : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
            }
            style={{
              backgroundColor: isActive ? "#09260D" : undefined,
            }}
          >
            {isActive ? "Available" : "Coming Soon"}
          </Badge>
        </div>
        <CardTitle
          className={`text-xl font-bold transition-colors ${isActive
              ? "text-gray-900 dark:text-white"
              : "text-gray-500 dark:text-gray-500"
            }`}
          style={{
            color: isActive ? undefined : undefined,
          }}
        >
          {title}
        </CardTitle>
        <p
          className={`text-sm leading-relaxed ${isActive
              ? "text-gray-600 dark:text-gray-400"
              : "text-gray-400 dark:text-gray-500"
            }`}
        >
          {description}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4
            className={`text-sm font-semibold ${isActive
                ? "text-gray-900 dark:text-white"
                : "text-gray-500 dark:text-gray-500"
              }`}
          >
            Key Features:
          </h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li
                key={index}
                className={`flex items-center gap-2 text-sm ${isActive
                    ? "text-gray-600 dark:text-gray-400"
                    : "text-gray-400 dark:text-gray-500"
                  }`}
              >
                <div
                  className={`w-1.5 h-1.5 rounded-full flex-shrink-0 ${isActive ? "" : "bg-gray-400"
                    }`}
                  style={{
                    backgroundColor: isActive ? "#09260D" : undefined,
                  }}
                />
                {feature}
              </li>
            ))}
          </ul>
        </div>
        <Button
          onClick={onClick}
          disabled={!isActive}
          className={`w-full transition-colors ${isActive
              ? "text-white"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
          style={{
            backgroundColor: isActive ? "#09260D" : undefined,
          }}
        >
          {isActive ? (
            <>
              Start Benchmarking
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          ) : (
            <>
              <Lock className="mr-2 h-4 w-4" />
              Coming Soon
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

export default function BenchmarkPage() {
  const router = useRouter();

  const benchmarkOptions = [
    {
      title: "Internal Benchmark",
      description:
        "Compare agreements within your organization to identify best practices, standardize terms, and optimize performance across different departments and contract types.",
      icon: <Building className="h-6 w-6" />,
      features: [
        "Cross-departmental contract comparison",
        "Performance metrics analysis",
        "Best practice identification",
        "Cost optimization insights",
        "Risk assessment comparison",
      ],
      isActive: true,
      onClick: () => {
        router.push("/contract-concierge/benchmark/internal");
      },
    },
    {
      title: "External Benchmark",
      description:
        "Benchmark your contracts against industry standards and market data to ensure competitive terms, pricing, and compliance with market best practices.",
      icon: <Globe className="h-6 w-6" />,
      features: [
        "Industry standard comparison",
        "Market pricing analysis",
        "Competitive positioning",
        "Regulatory compliance check",
        "Market trend insights",
      ],
      isActive: false,
      onClick: undefined,
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Professional Header */}
      <div className="bg-card border-b border-border">
        <div className="container mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-card-foreground mb-2">
                Agreement Benchmarking
              </h1>
              <p className="text-muted-foreground text-sm font-medium">
                Comprehensive analysis and comparison of your agreements
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-8 py-8">
        {/* Benchmark Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {benchmarkOptions.map((option, index) => (
            <BenchmarkOptionCard
              key={index}
              title={option.title}
              description={option.description}
              icon={option.icon}
              features={option.features}
              isActive={option.isActive}
              onClick={option.onClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
