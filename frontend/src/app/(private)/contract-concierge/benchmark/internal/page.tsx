"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  Building,
  Calendar,
  HandCoins,
  Users,
  FileText,
  CheckCircle,
  Search,
  Filter,
  BarChart3,
  ArrowRight,
  Loader2,
  Clock,
  Plus,
  X,
  Trash2,
  ScrollText,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { contractService, Contract } from "@/services/contractService";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { formatContractValue } from "@/lib/format-utils";
import { ContractStatusBadge } from "@/components/contracts/ContractStatusBadge";

export default function InternalBenchmarkPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  const [searchResults, setSearchResults] = useState<Contract[]>([]);
  const [selectedContracts, setSelectedContracts] = useState<Contract[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  const [searching, setSearching] = useState(false);
  const [filterStatus, setFilterStatus] = useState<
    "Active" | "Inactive" | "Unknown" | "all"
  >("all");
  const [filterProvider, setFilterProvider] = useState<string>("all");
  const [filterAgreementType, setFilterAgreementType] = useState<string>("all");
  const [filterRenewalMonths, setFilterRenewalMonths] = useState<string>("all");

  // Helper function to transform extraction data to Contract format
  const transformExtractionToContract = (extraction: any): Contract => {
    const fixedFields = extraction.fixedFields || {};
    const contractInfo = extraction.contractInfo || {};

    // Extract values from fixed fields with fallbacks
    const agreementType = fixedFields.agreement_type?.value || "OTHER";
    const provider = fixedFields.provider?.value || "";
    const client = fixedFields.client?.value || "";
    const totalAmount = fixedFields.total_amount?.value || "";
    const startDate = fixedFields.start_date?.value || null;
    const endDate = fixedFields.end_date?.value || null;
    const contractId = fixedFields.contract_id?.value || "";
    const autoRenewal = fixedFields.auto_renewal?.value === "Yes";
    const originalFilename = fixedFields.original_filename?.value || "";

    // Use only LLM-extracted status, no calculation
    const extractedStatus = fixedFields.contract_status?.value;
    let status = "Unknown";
    if (
      extractedStatus &&
      (extractedStatus === "Active" ||
        extractedStatus === "Inactive" ||
        extractedStatus === "Unknown")
    ) {
      status = extractedStatus;
    }

    return {
      id: extraction.contractId,
      title:
        originalFilename ||
        contractInfo.title ||
        `Contract ${extraction.contractId}`,
      description: contractInfo.description || "",
      agreementType: agreementType as any,
      status: status as any,
      value: totalAmount,
      provider: provider,
      counterparty: client,
      contractNumber: contractId,
      startDate: startDate,
      endDate: endDate,
      isAutoRenew: autoRenewal,
      version: 1,
      securityClassification: "CONFIDENTIAL" as any,
      isEncrypted: true,
      tenantId: extraction.tenantId || "",
      createdAt: extraction.extractionDate || new Date().toISOString(),
      updatedAt: extraction.updatedAt || new Date().toISOString(),
      // Include extraction data for components that need it
      extraction: {
        fixedFields: extraction.fixedFields,
        dynamicFields: extraction.dynamicFields,
        specialFields: extraction.specialFields,
        extractionDate: extraction.extractionDate,
        extractionVersion: extraction.extractionVersion,
        overallConfidence: extraction.overallConfidence,
        processingTimeMs: extraction.processingTimeMs,
        modelUsed: extraction.modelUsed,
      },
    } as Contract;
  };

  useEffect(() => {
    if (!user && !isLoading) {
      router.push("/auth/login");
      return;
    }
  }, [user, isLoading, router]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.trim()) {
        searchContracts();
      } else {
        setSearchResults([]);
      }
    }, 1000); // 500ms debounce delay

    return () => clearTimeout(timeoutId);
  }, [
    searchTerm,
    filterStatus,
    filterProvider,
    filterAgreementType,
    filterRenewalMonths,
  ]);

  const searchContracts = useCallback(async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    setSearching(true);
    try {
      const params: any = {
        page: 1,
        limit: 50, // Get more results for search
        title: searchTerm.trim(),
        status: filterStatus === "all" ? undefined : filterStatus,
        provider: filterProvider === "all" ? undefined : filterProvider,
        agreementType:
          filterAgreementType === "all" ? undefined : filterAgreementType,
        renewalMonths:
          filterRenewalMonths === "all" ? undefined : filterRenewalMonths,
      };

      // Use the new three-tier extraction API
      const result = await contractService.getContractsWithExtraction(params);
      let transformedContracts = (result.extractions || [])
        .map(transformExtractionToContract)
        .filter((contract) => contract.id); // Filter out any invalid contracts

      // Server-side filtering is now handled by the API

      // Filter out already selected contracts
      const availableContracts = transformedContracts.filter(
        (contract) =>
          !selectedContracts.some((selected) => selected.id === contract.id)
      );

      setSearchResults(availableContracts);
      toast.success(`Found ${availableContracts.length} contracts`);
    } catch (error) {
      console.error("Error searching contracts:", error);
      toast.error("Failed to search contracts");
    } finally {
      setSearching(false);
    }
  }, [
    searchTerm,
    filterStatus,
    filterProvider,
    filterAgreementType,
    filterRenewalMonths,
    selectedContracts,
  ]);

  const handleAddContract = (contract: Contract) => {
    setSelectedContracts((prev) => [...prev, contract]);
    // Remove from search results
    setSearchResults((prev) => prev.filter((c) => c.id !== contract.id));
    // toast.success(`Added "${contract.title}" to comparison`);
  };

  const handleRemoveContract = (contractId: string) => {
    const removedContract = selectedContracts.find((c) => c.id === contractId);
    setSelectedContracts((prev) => prev.filter((c) => c.id !== contractId));

    // Add back to search results if there's an active search
    if (removedContract && searchTerm.trim()) {
      setSearchResults((prev) => [...prev, removedContract]);
    }
    // toast.success("Contract removed from comparison");
  };

  const handleCompareSelected = () => {
    if (selectedContracts.length < 2) {
      toast.error("Please select at least 2 contracts to compare");
      return;
    }

    if (selectedContracts.length > 10) {
      toast.error("Please select no more than 10 contracts to compare");
      return;
    }

    const contractIds = selectedContracts.map((c) => c.id);
    const queryParams = contractIds.map((id) => `ids=${id}`).join("&");
    router.push(`/contract-concierge/benchmark/compare?${queryParams}`);
  };



  const handleClearFilters = () => {
    setFilterStatus("all");
    setFilterProvider("all");
    setFilterAgreementType("all");
    setFilterRenewalMonths("all");
  };



  // Helper function to get agreement classification
  const getAgreementClassification = (agreementType: string | undefined): string => {
    if (!agreementType) return "Software";

    switch (agreementType) {
      case "MSA":
      case "SLA":
      case "SOW":
      case "NDA":
        return "Service";
      case "PO":
      case "ORDER":
      case "INVOICE":
        return "Hardware";
      default:
        return "Software";
    }
  };





  return (
    <div className="space-y-6 pb-24">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/contract-concierge/benchmark">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Benchmark
          </Link>
        </Button>
        {selectedContracts.length > 0 && (
          <Button
            onClick={handleCompareSelected}
            className="text-white"
            style={{ backgroundColor: "#09260D" }}
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            Compare Selected ({selectedContracts.length})
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Page Title */}
      <div className="space-y-4">


        {/* Stats */}
        {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card style={{ borderColor: "#09260D" }}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "#09260D" }}
                >
                  <FileText className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Search Results
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {searchResults.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card style={{ borderColor: "#09260D" }}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "#09260D" }}
                >
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Selected
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {selectedContracts.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card style={{ borderColor: "#09260D" }}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "#09260D" }}
                >
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active in Selection
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {
                      selectedContracts.filter((c) => c.status === "Active")
                        .length
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card style={{ borderColor: "#09260D" }}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "#09260D" }}
                >
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Ready to Compare
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {selectedContracts.length >= 2 ? "Yes" : "No"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>

      {/* Enhanced Search and Filter */}
      <Card className="border-border bg-card shadow-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: "#09260D" }}
              >
                <Search className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-semibold">
                Select Agreements to Compare
              </span>
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Search Bar */}
          <div className="relative">
            <div className="flex gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Search by contract name, supplier, or keywords..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 h-14 text-base border-2 transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20"
                  style={
                    {
                      borderColor: "#e5e7eb",
                      "--tw-ring-color": "#09260D",
                    } as React.CSSProperties
                  }
                />
                {searching && (
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                  </div>
                )}
              </div>
              {/* {(searchTerm || searchResults.length > 0) && (
                <Button
                  variant="outline"
                  onClick={handleClearSearch}
                  className="h-14 px-6 border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200"
                >
                  <X className="mr-2 h-4 w-4" />
                  Clear
                </Button>
              )} */}
            </div>
          </div>

          {/* Advanced Filters Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Advanced Filters
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-all duration-200"
              >
                <X className="mr-1 h-3 w-3" />
                Clear All
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl border border-gray-200 dark:border-gray-700">
              {/* Status Filter */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-4 w-4 text-gray-500" />
                    Contract Status
                  </div>
                </label>
                <Select
                  value={filterStatus}
                  onValueChange={(value) =>
                    setFilterStatus(
                      value as "Active" | "Inactive" | "Unknown" | "all"
                    )
                  }
                >
                  <SelectTrigger className="h-11 border-2 border-gray-200 hover:border-gray-300 focus:border-primary transition-all duration-200">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="Active">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        Active
                      </div>
                    </SelectItem>
                    <SelectItem value="Inactive">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        Inactive
                      </div>
                    </SelectItem>
                    <SelectItem value="Unknown">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                        Unknown
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Provider Filter */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-4 w-4 text-gray-500" />
                    Supplier
                  </div>
                </label>
                <Input
                  placeholder="Enter supplier name..."
                  value={filterProvider === "all" ? "" : filterProvider}
                  onChange={(e) => setFilterProvider(e.target.value || "all")}
                  className="h-11 border-2 border-gray-200 hover:border-gray-300 focus:border-primary transition-all duration-200"
                />
              </div>

              {/* Agreement Type Filter */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="h-4 w-4 text-gray-500" />
                    Agreement Type
                  </div>
                </label>
                <Select
                  value={filterAgreementType}
                  onValueChange={(value) => setFilterAgreementType(value)}
                >
                  <SelectTrigger className="h-11 border-2 border-gray-200 hover:border-gray-300 focus:border-primary transition-all duration-200">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="MSA">MSA</SelectItem>
                    <SelectItem value="NDA">NDA</SelectItem>
                    <SelectItem value="SOW">SOW</SelectItem>
                    <SelectItem value="PO">PO</SelectItem>
                    <SelectItem value="SLA">SLA</SelectItem>
                    <SelectItem value="INVOICE">Invoice</SelectItem>
                    <SelectItem value="SCHEDULE">Schedule</SelectItem>
                    <SelectItem value="ORDER">Order</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Renewal Filter */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    Renewal Due
                  </div>
                </label>
                <Select
                  value={filterRenewalMonths}
                  onValueChange={(value) => setFilterRenewalMonths(value)}
                >
                  <SelectTrigger className="h-11 border-2 border-gray-200 hover:border-gray-300 focus:border-primary transition-all duration-200">
                    <SelectValue placeholder="All Contracts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Contracts</SelectItem>
                    <SelectItem value="3">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-orange-500" />
                        Next 3 Months
                      </div>
                    </SelectItem>
                    <SelectItem value="6">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-yellow-500" />
                        Next 6 Months
                      </div>
                    </SelectItem>
                    <SelectItem value="12">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-green-500" />
                        Next 12 Months
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Active Filters Display */}
            {(filterStatus !== "all" ||
              filterProvider !== "all" ||
              filterAgreementType !== "all" ||
              filterRenewalMonths !== "all") && (
                <div className="flex flex-wrap items-center gap-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Active Filters:
                  </span>
                  {filterStatus !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                      onClick={() => setFilterStatus("all")}
                    >
                      Status: {filterStatus}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                  {filterProvider !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                      onClick={() => setFilterProvider("all")}
                    >
                      Provider: {filterProvider}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                  {filterAgreementType !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                      onClick={() => setFilterAgreementType("all")}
                    >
                      Type: {filterAgreementType}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                  {filterRenewalMonths !== "all" && (
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                      onClick={() => setFilterRenewalMonths("all")}
                    >
                      Renewal: Next {filterRenewalMonths} months
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                </div>
              )}
          </div>
        </CardContent>
      </Card>

      {/* Selected Contracts */}
      {selectedContracts.length > 0 && (
        <Card style={{ borderColor: "#09260D" }}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" style={{ color: "#09260D" }} />
                Selected for Comparison ({selectedContracts.length})
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedContracts([])}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <Trash2 className="mr-1 h-3 w-3" />
                Clear All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {selectedContracts.map((contract) => (
                <Card
                  key={contract.id}
                  className="relative transition-all duration-200 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                  style={{
                    borderColor: "#09260D",
                  }}
                >
                  {/* Remove Button */}
                  <div className="absolute top-2 right-2 z-10">
                    <button
                      onClick={() => handleRemoveContract(contract.id)}
                      className="w-6 h-6 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all shadow-sm hover:scale-105 border border-white dark:border-gray-800"
                    >
                      <X className="h-3 w-3 text-white stroke-[2.5]" />
                    </button>
                  </div>

                  <CardContent className="p-4 pt-4">
                    {/* Contract Title - Main Heading */}
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3 line-clamp-2 min-h-[2.5rem] leading-tight">
                      {contract.title}
                    </h3>

                    {/* Key Metrics - Consistent with search results */}
                    <div className="space-y-2 text-xs">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          Supplier
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium truncate max-w-[100px]">
                          {contract.provider || "N/A"}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <HandCoins className="h-3 w-3" />
                          Value
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium">
                          {formatContractValue(contract.value, undefined, true)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <ScrollText className="h-3 w-3" />
                          Classification
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium">
                          {getAgreementClassification(contract.agreementType)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Status
                        </span>
                        <ContractStatusBadge
                          status={contract.status || "Unknown"}
                          variant="compact"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      <div className="space-y-4">
        {/* <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-medium text-gray-900 dark:text-white">
              Search Results
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-normal">
              {searchResults.length > 0
                ? `Found ${searchResults.length} contract${
                    searchResults.length !== 1 ? "s" : ""
                  } matching your search`
                : "Search for contracts to add to your comparison"}
            </p>
          </div>
        </div> */}

        {/* {searchResults.length === 0 ? (
          <Card className="border-dashed border-2 border-gray-300 dark:border-gray-600">
            <CardContent className="text-center py-16">
              <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                <Search className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                {searchTerm ? "No Contracts Found" : "Start Your Search"}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto font-normal">
                {searchTerm
                  ? "No contracts match your search criteria. Try different keywords or adjust your filters."
                  : "Enter a contract name, supplier, or keyword in the search box above to find contracts for benchmarking."}
              </p>
              {searchTerm && (
                <div className="flex gap-3 justify-center">
                  <Button variant="outline" onClick={handleClearSearch}>
                    Clear Search
                  </Button>
                  <Button
                    onClick={() =>
                      router.push("/contract-management/contracts")
                    }
                    className="text-white"
                    style={{ backgroundColor: "#09260D" }}
                  >
                    Browse All Contracts
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ) : ( */}
        {searchResults?.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {searchResults.map((contract) => {
              return (
                <Card
                  key={contract.id}
                  className="group relative transition-all duration-200 cursor-pointer hover:shadow-md border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                  style={{
                    borderColor: "#09260D",
                  }}
                  onClick={() => handleAddContract(contract)}
                >
                  {/* Add Button */}
                  <div className="absolute top-2 right-2 z-10">
                    <div
                      className="w-7 h-7 rounded-full flex items-center justify-center transition-all shadow-sm group-hover:scale-105 border-2 border-white dark:border-gray-800"
                      style={{ backgroundColor: "#09260D" }}
                    >
                      <Plus className="h-3.5 w-3.5 text-white stroke-[2.5]" />
                    </div>
                  </div>

                  <CardContent className="p-4 pt-4">
                    {/* Contract Title - Main Heading */}
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3 line-clamp-2 min-h-[2.5rem] leading-tight">
                      {contract.title}
                    </h3>

                    {/* Key Metrics - Consistent Layout */}
                    <div className="space-y-2 text-xs">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          Supplier
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium truncate max-w-[100px]">
                          {contract.provider || "N/A"}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <HandCoins className="h-3 w-3" />
                          Value
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium">
                          {formatContractValue(contract.value, undefined, true)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <ScrollText className="h-3 w-3" />
                          Classification
                        </span>
                        <span className="text-gray-700 dark:text-gray-300 font-medium">
                          {getAgreementClassification(contract.agreementType)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Status
                        </span>
                        <ContractStatusBadge
                          status={contract.status || "Unknown"}
                          variant="compact"
                        />
                      </div>
                    </div>

                    {/* Hover Effect */}
                    <div
                      className="absolute inset-0 transition-all duration-200 pointer-events-none rounded-lg opacity-0 group-hover:opacity-5"
                      style={{
                        background:
                          "linear-gradient(to bottom right, #09260D, #0a2e0f)",
                      }}
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
        {/* )} */}
      </div>

      {/* Help Section */}
      {/* <Card className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-600 rounded-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                How to Use Internal Benchmarking
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Search for contracts by name, supplier, or keywords, then
                add them to your comparison set. Once you have 2 or more
                contracts selected, you can analyze their performance, terms,
                and outcomes to identify best practices and optimization
                opportunities.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-blue-600" />
                  <span>Search contracts by name or supplier</span>
                </div>
                <div className="flex items-center gap-2">
                  <Plus className="h-4 w-4 text-blue-600" />
                  <span>Click to add contracts to comparison</span>
                </div>
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  <span>Compare values, terms, and performance</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span>Identify optimization opportunities</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* Fixed Bottom Compare Bar */}
      {selectedContracts.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 z-30 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex -space-x-2">
                  {selectedContracts.slice(0, 4).map((contract, index) => (
                    <div
                      key={contract.id}
                      className="w-10 h-10 rounded-full border-2 border-white dark:border-gray-900 flex items-center justify-center text-white text-sm font-medium shadow-sm"
                      style={{
                        zIndex: 10 - index,
                        backgroundColor: "#09260D",
                      }}
                    >
                      {contract.title.charAt(0).toUpperCase()}
                    </div>
                  ))}
                  {selectedContracts.length > 4 && (
                    <div className="w-10 h-10 rounded-full bg-gray-500 border-2 border-white dark:border-gray-900 flex items-center justify-center text-white text-sm font-medium shadow-sm">
                      +{selectedContracts.length - 4}
                    </div>
                  )}
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedContracts.length} Contract
                    {selectedContracts.length !== 1 ? "s" : ""} Selected
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedContracts.length >= 2
                      ? "Ready to compare and analyze"
                      : "Select at least 2 contracts to compare"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => setSelectedContracts([])}
                  className="text-red-600 border-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Clear All
                </Button>
                <Button
                  onClick={handleCompareSelected}
                  disabled={selectedContracts.length < 2}
                  className="text-white disabled:opacity-50 disabled:cursor-not-allowed px-6 py-2"
                  style={{ backgroundColor: "#09260D" }}
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Compare Agreements
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
