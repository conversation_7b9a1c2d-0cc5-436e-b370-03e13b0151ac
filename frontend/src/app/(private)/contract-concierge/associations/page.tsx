/**
 * Supplier Analysis Page
 * Shows grouped providers with analysis capabilities (entitlements, relationships, bundle analysis)
 */

"use client";

import { useState, useEffect } from "react";

import {
  Search,
  FlaskConical,
  GitBranch,
  Network,
  ArrowRight,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { folderService, FolderWithContractCount } from "@/services/folderService";
import { Contract } from "@/services/contractService";
import { FolderAccordion } from "@/components/folders/FolderAccordion";
import { DataTableColumn } from "@/components/ui/data-table";
import { ContractStatusBadge } from "@/components/contracts/ContractStatusBadge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  formatCurrency,
  formatContractTerm,
  formatNoticePeriod,
} from "@/lib/format-utils";
import {
  formatContractClassification,
  formatAgreementType,
  getClassificationColor,
  getAgreementTypeColor,
} from "@/lib/classification-utils";
import { toast } from "sonner";
import { EntitlementAnalysisModal } from "@/components/contracts/EntitlementAnalysisModal";
import { ContractRelationshipVisualization } from "@/components/contracts/ContractRelationshipVisualization";
import { BundleAnalysisModal } from "@/components/contracts/BundleAnalysisModal";

// Format date for display
function formatDate(dateString: string) {
  // Handle invalid or N/A dates
  if (!dateString || dateString === "N/A" || dateString.trim() === "") {
    return "N/A";
  }

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "N/A";
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  } catch {
    return "N/A";
  }
}

// Get status badge variant - uses calculated status with fallback
function getStatusBadge(contract: Contract) {
  return (
    <ContractStatusBadge
      status={contract.status || "Unknown"}
      variant="compact"
    />
  );
}

// Helper functions for three-tier extraction data
function getThreeTierProvider(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.provider?.value) {
    return extraction.fixedFields.provider.value;
  }

  return "N/A";
}

function getThreeTierContractValue(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.total_amount?.value) {
    return formatCurrency(extraction.fixedFields.total_amount.value);
  }

  // Fallback to existing value logic
  if (
    contract.value &&
    contract.value !== "0" &&
    contract.value !== "N/A" &&
    contract.value.trim() !== ""
  ) {
    return formatCurrency(contract.value);
  }
  return "N/A";
}

function getThreeTierEffectiveDate(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.start_date?.value) {
    const dateValue = extraction.fixedFields.start_date.value;
    // Only format if it's not "N/A" or empty
    if (dateValue && dateValue !== "N/A" && dateValue.trim() !== "") {
      return formatDate(dateValue);
    }
  }

  // Fallback to existing start date logic
  return contract.startDate ? formatDate(contract.startDate) : "N/A";
}

function getThreeTierExpirationDate(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.end_date?.value) {
    const dateValue = extraction.fixedFields.end_date.value;
    // Only format if it's not "N/A" or empty
    if (dateValue && dateValue !== "N/A" && dateValue.trim() !== "") {
      return formatDate(dateValue);
    }
  }

  // Fallback to existing end date logic
  return contract.endDate ? formatDate(contract.endDate) : "N/A";
}

// Helper functions for extracting metadata from contracts
function getAutoRenewal(contract: Contract): string {
  // First check three-tier extraction fixed fields (prioritized)
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.auto_renewal?.value) {
    const value = (extraction.fixedFields as any).auto_renewal.value;
    // Fixed fields should only contain "Yes" or "No"
    if (value === "Yes" || value === "No") {
      return value;
    }
    // Legacy support - normalize to Yes/No
    if (value && value.toLowerCase().includes("yes")) {
      return "Yes";
    }
    // Default to "No" for any other value
    return "No";
  }

  return "N/A";
}

function getRenewalNoticePeriod(contract: Contract): string {
  // First check three-tier extraction fixed fields (prioritized)
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.renewal_notice_period?.value) {
    const value = (extraction.fixedFields as any).renewal_notice_period.value;
    if (value && value !== "N/A" && value.trim() !== "") {
      // If it's already formatted text, return as is
      if (isNaN(Number(value))) {
        return value;
      }
      // If it's a number, format it as days
      return formatNoticePeriod(Number(value));
    }
  }

  return "N/A";
}

function getContractTerm(contract: Contract): string {
  return formatContractTerm(contract.startDate, contract.endDate);
}

// Helper function to extract contract number from contract metadata
function getContractNumber(contract: Contract): string {
  // First check three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.contract_id?.value) {
    return extraction.fixedFields.contract_id.value;
  }

  // Then check the top-level contractNumber field on the contract object
  if (contract.contractNumber && contract.contractNumber !== "N/A") {
    return contract.contractNumber;
  }

  return "N/A";
}

export default function SupplierAnalysisPage() {
  // State for providers/folders
  const [providers, setProviders] = useState<FolderWithContractCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // State for selected provider for analysis
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [selectedFolderId, setSelectedFolderId] = useState<string>("");
  const [selectedContractCount, setSelectedContractCount] = useState<number>(0);

  // Modal states
  const [entitlementModalOpen, setEntitlementModalOpen] = useState(false);
  const [relationshipModalOpen, setRelationshipModalOpen] = useState(false);
  const [bundleAnalysisModalOpen, setBundleAnalysisModalOpen] = useState(false);

  // Fetch providers on component mount
  useEffect(() => {
    fetchProviders();
  }, []);

  const fetchProviders = async () => {
    setLoading(true);
    try {
      // Use the provider-based folders endpoint (same as grouped tab)
      const result = await folderService.getFoldersByProviders();
      setProviders(result.folders || []);
    } catch (error) {
      console.error("Error fetching providers:", error);
      toast.error("Failed to load providers");
      setProviders([]);
    } finally {
      setLoading(false);
    }
  };

  // Handler functions for the top action buttons
  const handleEntitlementAnalysis = () => {
    if (!selectedProvider) {
      toast.error("Please select a supplier first");
      return;
    }
    setEntitlementModalOpen(true);
  };

  const handleRelationshipVisualization = () => {
    if (!selectedProvider) {
      toast.error("Please select a supplier first");
      return;
    }
    setRelationshipModalOpen(true);
  };

  const handleBundleAnalysis = () => {
    if (!selectedProvider || !selectedFolderId) {
      toast.error("Please select a supplier first");
      return;
    }
    setBundleAnalysisModalOpen(true);
  };

  // Handler for accordion selection
  const handleProviderSelection = (provider: FolderWithContractCount) => {
    setSelectedProvider(provider.folder.name);
    setSelectedFolderId(provider.folder.id);
    setSelectedContractCount(provider.contractCount);
  };

  // Handler to clear selection
  const handleClearSelection = () => {
    setSelectedProvider("");
    setSelectedFolderId("");
    setSelectedContractCount(0);
  };

  // Filter providers based on search term
  const filteredProviders = providers.filter((provider) =>
    provider.folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Contract columns for the FolderAccordion - exact same as repository page
  const getContractColumns = (): DataTableColumn<Contract>[] => {
    const columns: DataTableColumn<Contract>[] = [
      // 1. Contract Name (show original filename without extension)
      {
        key: "title",
        header: "Contract Name",
        className:
          "font-light table-cell-wrap w-[280px] min-w-[280px] max-w-[280px]",
        render: (_: any, contract: any) => {
          // Get original filename from extraction fixed fields
          const extraction = (contract as any).extraction;
          const originalFilename =
            extraction?.fixedFields?.original_filename?.value;

          // Use original filename if available, otherwise use title
          const displayName = originalFilename || contract.title || "N/A";

          return (
            <div className="flex flex-col gap-1">
              <span className="font-light text-sm text-foreground leading-tight">
                {displayName}
              </span>
            </div>
          );
        },
      },

      // 2. Status
      {
        key: "status",
        header: "Status",
        className: "text-center w-[90px]",
        render: (_: any, contract: any) => getStatusBadge(contract),
      },

      // 3. Provider
      {
        key: "provider",
        header: "Provider",
        className: "text-center min-w-[250px]",
        render: (_: any, contract: any) => (
          <span className="text-sm">{getThreeTierProvider(contract)}</span>
        ),
      },

      // 4. Contract ID
      {
        key: "contractNumber",
        header: "Contract ID",
        className: "text-center min-w-[120px]",
        sortable: true,
        accessor: (contract: Contract) => getContractNumber(contract),
        render: (_: any, contract: any) => (
          <span className="text-sm font-light">
            {getContractNumber(contract)}
          </span>
        ),
      },

      // 5. Type (Agreement Type)
      {
        key: "agreementType",
        header: "Type",
        className: "text-center w-[140px]",
        render: (_: any, contract: any) => {
          const agreementType = formatAgreementType(contract.agreementType);
          const colorClass = getAgreementTypeColor(contract.agreementType);

          return (
            <div className="flex flex-col items-center gap-1">
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${colorClass}`}
              >
                {agreementType}
              </span>
            </div>
          );
        },
      },

      // 6. Classification
      {
        key: "classification",
        header: "Classification",
        className: "text-center w-[140px]",
        render: (_: any, contract) => {
          // Get the contract classification from extraction data
          const extraction = (contract as any).extraction;
          const classificationValue =
            extraction?.fixedFields?.contract_classification?.value;

          const classification =
            formatContractClassification(classificationValue);
          const colorClass = getClassificationColor(classificationValue);

          return (
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${colorClass}`}
            >
              {classification}
            </span>
          );
        },
      },

      // 7. TCV (Total Contract Value)
      {
        key: "tcv",
        header: "TCV",
        className: "text-center  min-w-[100px]",
        render: (_: any, contract) => {
          const value = getThreeTierContractValue(contract);
          return (
            <span
              className={`font-light text-sm whitespace-nowrap ${value === "N/A" ? "text-muted-foreground" : ""
                }`}
            >
              {value}
            </span>
          );
        },
      },

      // 8. Effective Date
      {
        key: "effectiveDate",
        header: "Effective Date",
        className: "text-center w-[130px]",
        render: (_: any, contract) => {
          const date = getThreeTierEffectiveDate(contract);
          return (
            <span
              className={`text-sm whitespace-nowrap ${date === "N/A" ? "text-muted-foreground" : ""
                }`}
            >
              {date}
            </span>
          );
        },
      },

      // 9. Expiration Date
      {
        key: "expirationDate",
        header: "Expiration Date",
        className: "text-center w-[130px]",
        render: (_: any, contract) => {
          const date = getThreeTierExpirationDate(contract);
          return (
            <span
              className={`text-sm whitespace-nowrap ${date === "N/A" ? "text-muted-foreground" : ""
                }`}
            >
              {date}
            </span>
          );
        },
      },

      // 10. Contract Term
      {
        key: "contractTerm",
        header: "Contract Term",
        className: "text-center max-w-[100px]",
        render: (_: any, contract) => (
          <span className="text-foreground text-sm whitespace-nowrap font-light">
            {getContractTerm(contract)}
          </span>
        ),
      },

      // 11. Auto Renewal
      {
        key: "autoRenewal",
        header: "Auto Renewal",
        className: "text-center w-[100px]",
        render: (_: any, contract) => {
          const autoRenewal = getAutoRenewal(contract);
          return (
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${autoRenewal === "Yes"
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                : autoRenewal === "No"
                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                }`}
            >
              {autoRenewal}
            </span>
          );
        },
      },

      // 12. Renewal Notice
      {
        key: "renewalNotice",
        header: "Renewal Notice",
        className: "text-center max-w-[120px]",
        render: (_: any, contract) => {
          const renewalNotice = getRenewalNoticePeriod(contract);
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-foreground max-w-[120px] text-sm font-light block truncate cursor-help">
                    {renewalNotice}
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="bg-primary text-primary-foreground"
                >
                  <p>{renewalNotice}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
      },
    ];

    return columns;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b border-border">
        <div className="container mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-card-foreground mb-2">
                Associations
              </h1>
              <p className="text-muted-foreground text-sm font-medium">
                Comprehensive examination by mapping your agreement stack (MSAs, amendments, order forms, invoices etc.)              </p>
            </div>
            {/* <div className="hidden md:flex items-center gap-4">
              <div className="bg-primary/10 rounded-lg px-4 py-2">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-primary" />
                  <span className="text-primary text-sm font-medium">
                    {providers.length} Suppliers
                  </span>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Selection Indicator and Action Buttons */}
        {selectedProvider ? (
          <div className="mb-6">
            <div className="flex items-center justify-between bg-muted/30 border border-border rounded-lg px-4 py-3">
              <div className="flex items-center gap-3">
                {/* Selection Count Circle */}
                <div className="flex items-center gap-2">

                  <div>
                    <div className="text-sm font-medium text-foreground">
                      {selectedProvider}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Ready for entitlements, mapping, or integrated analysis
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Clear Selection Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearSelection}
                  className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-3 w-3" />
                  Clear All
                </Button>

                {/* Action Buttons */}
                <Button
                  onClick={handleEntitlementAnalysis}
                  size="sm"
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  <FlaskConical className="h-3 w-3" />
                  Entitlements
                  <ArrowRight className="h-3 w-3" />
                </Button>
                <Button
                  onClick={handleRelationshipVisualization}
                  size="sm"
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  <GitBranch className="h-3 w-3" />
                  Mapping
                  <ArrowRight className="h-3 w-3" />
                </Button>
                <Button
                  onClick={handleBundleAnalysis}
                  size="sm"
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  <Network className="h-3 w-3" />
                  Integrated Analysis
                  <ArrowRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        ) : null}

        {/* Grouped Contracts Display */}
        <Card className="h-full flex flex-col w-full max-w-full overflow-hidden">
          {/* <CardHeader className="px-4 sm:px-6 py-4 flex-shrink-0 border-b">
            <CardTitle className="text-lg font-light text-foreground">
              Grouped Contract Stack
            </CardTitle>
          </CardHeader> */}
          <CardContent className="flex-1 p-0 min-h-0 w-full max-w-full overflow-hidden">
            <div className="h-full w-full overflow-y-auto p-3">
              <FolderAccordion
                folders={filteredProviders}
                contractColumns={getContractColumns()}
                loading={loading}
                onProviderSelect={handleProviderSelection}
                selectedProvider={selectedProvider}
              />
            </div>
          </CardContent>
        </Card>

        {/* Analysis Modals */}
        <EntitlementAnalysisModal
          open={entitlementModalOpen}
          onOpenChange={setEntitlementModalOpen}
          preSelectedProvider={selectedProvider}
        />

        <ContractRelationshipVisualization
          supplierName={selectedProvider}
          isOpen={relationshipModalOpen}
          onClose={() => setRelationshipModalOpen(false)}
        />

        <BundleAnalysisModal
          open={bundleAnalysisModalOpen}
          onOpenChange={setBundleAnalysisModalOpen}
          folderId={selectedFolderId}
          folderName={selectedProvider}
          currentContractCount={selectedContractCount}
        />
      </div>
    </div>
  );
}
