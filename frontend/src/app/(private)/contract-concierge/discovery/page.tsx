/**
 * Discovery Page
 * Modern, professional interface for contract upload and selection
 */

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import {
  FileText,
  Loader2,
  CloudUpload,
  Search,
  Clock,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { contractService } from "@/services/contractService";
// Note: Auto-grouping functionality removed
import { TableConfidenceIndicator } from "@/components/contracts/TableConfidenceIndicator";
import { toast } from "sonner";

export default function DiscoveryPage() {
  const router = useRouter();

  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // Contract selection state
  const [contractSearch, setContractSearch] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedContract, setSelectedContract] = useState<any>(null);
  const [searchLoading, setSearchLoading] = useState(false);

  // Recent contracts state
  const [recentContracts, setRecentContracts] = useState<any[]>([]);
  const [recentContractsLoading, setRecentContractsLoading] = useState(false);

  // Note: Auto-grouping state removed since smart grouping functionality is disabled

  // Fetch recent contracts on component mount
  useEffect(() => {
    fetchRecentContracts();
  }, []);

  // Fetch recent contracts function
  const fetchRecentContracts = async () => {
    setRecentContractsLoading(true);
    try {
      // Get the last 5 recently uploaded contracts with AI assessment
      const result = await contractService.getContractsWithExtraction({
        page: 1,
        limit: 5,
      });

      // Transform extraction data to contract format
      const transformedContracts = (result.extractions || []).map(
        (extraction: any) => {
          const fixedFields = extraction.fixedFields || {};
          const originalFilename = fixedFields.original_filename?.value || "";

          return {
            id: extraction.contractId,
            title: originalFilename || `Contract ${extraction.contractId}`,
            contractNumber: fixedFields.contract_id?.value || "",
            provider: fixedFields.provider?.value || "",
            overallConfidence: extraction.overallConfidence || 0,
            createdAt: extraction.createdAt,
          };
        }
      );

      setRecentContracts(transformedContracts);
    } catch (error) {
      console.error("Error fetching recent contracts:", error);
      setRecentContracts([]);
    } finally {
      setRecentContractsLoading(false);
    }
  };

  // Search for contracts with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (contractSearch) {
        searchContracts(contractSearch);
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [contractSearch]);

  // Search for contracts with debounce
  const searchContracts = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      // Use the extraction API to get contracts with confidence data
      const result = await contractService.getContractsWithExtraction({
        page: 1,
        limit: 10,
        title: searchTerm.trim(),
      });

      // Transform extraction data to contract format
      const transformedContracts = (result.extractions || []).map(
        (extraction: any) => {
          const fixedFields = extraction.fixedFields || {};
          const originalFilename = fixedFields.original_filename?.value || "";

          return {
            id: extraction.contractId,
            title: originalFilename || `Contract ${extraction.contractId}`,
            contractNumber: fixedFields.contract_id?.value || "",
            provider: fixedFields.provider?.value || "",
            overallConfidence: extraction.overallConfidence || 0,
          };
        }
      );

      setSearchResults(transformedContracts);
    } catch (error) {
      console.error("Error searching contracts:", error);
      toast.error("Failed to search contracts");
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (contractSearch) {
        searchContracts(contractSearch);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [contractSearch]);

  // Handle contract selection for analysis
  const handleContractSelection = async (contract: any) => {
    setSelectedContract(contract);
    setUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Get the full contract details for analysis
      const fullContract = await contractService.getContract(contract.id);

      clearInterval(progressInterval);
      setUploadProgress(100);

      toast.success("Contract loaded for analysis!");

      // Navigate to the analysis page with the contract ID
      router.push(`/contract-concierge/discovery/${fullContract.id}`);
    } catch (error) {
      console.error("Error loading contract:", error);
      toast.error("Failed to load contract. Please try again.");
      setSelectedContract(null);
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };

  const handleFileUpload = async (files: File[]) => {
    // Validate files
    for (const file of files) {
      if (file.type !== "application/pdf") {
        toast.error(`Please upload only PDF files. "${file.name}" is not a PDF.`);
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        toast.error(`File size must be less than 10MB. "${file.name}" is too large.`);
        return;
      }
    }

    setSelectedFiles(files);
    setUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      if (files.length === 1) {
        // Single file upload - existing behavior
        const uploadResult = await contractService.uploadContractWithAI(files[0]);
        const fullContract = await contractService.getContract(uploadResult.id);

        clearInterval(progressInterval);
        setUploadProgress(100);

        toast.success("Contract uploaded and analyzed successfully!");

        // Refresh recent contracts list
        fetchRecentContracts();

        // Navigate to analysis page for single contract
        router.push(`/contract-concierge/discovery/${fullContract.id}`);
      } else {
        // Multiple files upload - use import functionality
        const importResult = await contractService.importContracts(files);

        clearInterval(progressInterval);
        setUploadProgress(100);

        // Handle background processing response (202 status)
        if (importResult.processingStarted && importResult.jobId) {
          toast.success(
            `Processing ${importResult.filesReceived || files.length} contracts in the background`,
            {
              description: "You'll be notified when the upload is complete",
              duration: 8000,
            }
          );
        } else {
          // Handle synchronous processing response (200 status)
          toast.success(`Successfully uploaded ${importResult.successful} contracts!`, {
            description: importResult.failed > 0
              ? `${importResult.failed} contracts failed to upload`
              : "All contracts uploaded successfully",
            duration: 5000,
          });
        }

        // Refresh recent contracts list
        fetchRecentContracts();

        // Navigate to repository for multiple contracts
        router.push("/contract-management/contracts");
      }
    } catch (error) {
      console.error("Error uploading contracts:", error);

      // Use the error message from backend
      const errorMessage =
        error instanceof Error ? error.message : "Please try again.";

      toast.error("Failed to upload contracts", {
        description: errorMessage,
        duration: 5000,
      });

      setSelectedFiles([]);
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Convert FileList to Array
      const fileArray = Array.from(files);
      handleFileUpload(fileArray);
    }
    // Reset the input
    event.target.value = "";
  };

  // Note: Auto-grouping handlers removed since smart grouping functionality is disabled

  return (
    <div className="min-h-screen bg-background">
      {/* Professional Header */}
      <div className="bg-card border-b border-border">
        <div className="container mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-card-foreground mb-2">
                Agreement Discovery
              </h1>
              <p className="text-muted-foreground text-sm font-medium">
                Intrusive examination of your agreements
              </p>
            </div>
            {/* <div className="hidden md:flex items-center gap-4">
              <div className="bg-primary/10 rounded-lg px-4 py-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                  <span className="text-primary text-sm font-medium">
                    AI Assistant Ready
                  </span>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* Main Content Grid - 2 Column Layout */}
      <div className="container mx-auto px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch">
          {/* Left Column - Upload Agreement */}
          <div className="flex flex-col h-full">
            <div className="bg-card rounded-xl border border-border shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full">
              <div className="p-5 flex flex-col h-full">

                <div className="relative flex-1 flex flex-col justify-center">
                  <input
                    type="file"
                    accept=".pdf"
                    multiple
                    onChange={handleInputChange}
                    disabled={uploading}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed z-10"
                    id="contract-upload"
                  />

                  {uploading && (selectedFiles.length > 0 || selectedContract) ? (
                    <div className="border-2 border-primary/20 bg-primary/5 rounded-lg p-6 text-center w-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-3" />
                      <p className="font-medium text-primary mb-1">
                        {selectedFiles.length > 0
                          ? selectedFiles.length === 1
                            ? "Analyzing Agreement"
                            : `Analyzing ${selectedFiles.length} Agreements`
                          : "Loading Agreement"}
                      </p>
                      <p className="text-sm text-muted-foreground mb-3">
                        {selectedFiles.length > 0
                          ? selectedFiles.length === 1
                            ? selectedFiles[0].name
                            : `${selectedFiles.length} files selected`
                          : selectedContract?.title}
                      </p>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {uploadProgress}% complete
                      </p>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-border hover:border-primary/50 rounded-lg p-6 text-center transition-colors group cursor-pointer w-full">
                      <CloudUpload className="h-10 w-10 text-muted-foreground group-hover:text-primary mx-auto mb-3 transition-colors" />
                      <h4 className="font-medium text-card-foreground mb-2">
                        Upload Agreement(s)
                      </h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Select one or multiple PDF files (up to 10MB each)
                      </p>
                      <Button className="w-full" disabled={uploading}>
                        <CloudUpload className="mr-2 h-4 w-4" />
                        Browse Files
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Select Existing Agreement */}
          <div className="flex flex-col h-full">
            <div className="bg-card rounded-xl border border-border shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full">
              <div className="p-5 flex flex-col h-full">


                <div className="relative flex-1 flex flex-col justify-center">
                  {uploading && selectedContract ? (
                    <div className="border-2 border-primary/20 bg-primary/5 rounded-lg p-6 text-center w-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-3" />
                      <p className="font-medium text-primary mb-1">
                        Loading Agreement
                      </p>
                      <p className="text-sm text-muted-foreground mb-3">
                        {selectedContract?.title}
                      </p>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {uploadProgress}% complete
                      </p>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-border hover:border-primary/50 rounded-lg p-6 transition-colors w-full">
                      {/* Header section - only show when no search is active */}
                      {!contractSearch && (
                        <div className="text-center mb-4">
                          <Search className="h-10 w-10 text-muted-foreground group-hover:text-primary mx-auto mb-3 transition-colors" />
                          <h4 className="font-medium text-card-foreground mb-9">
                            Find Existing Agreement
                          </h4>

                        </div>
                      )}

                      {/* Search input - always present to maintain focus */}
                      <div className="space-y-4">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="text"
                            placeholder="Search agreements from repository..."
                            value={contractSearch}
                            onChange={(e) => setContractSearch(e.target.value)}
                            className="pl-10 w-full"
                            disabled={uploading}
                          />
                        </div>

                        {/* Search results - only show when there's a search term */}
                        {contractSearch && (
                          <div className="max-h-[280px] overflow-y-auto">
                            {searchLoading ? (
                              <div className="space-y-2">
                                {[...Array(3)].map((_, i) => (
                                  <div
                                    key={i}
                                    className="flex items-center gap-3 p-3 rounded-lg"
                                  >
                                    <Skeleton className="h-8 w-8 rounded" />
                                    <div className="flex-1 space-y-1">
                                      <Skeleton className="h-4 w-3/4" />
                                      <Skeleton className="h-3 w-1/2" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : searchResults.length > 0 ? (
                              <div className="space-y-1">
                                {searchResults.slice(0, 5).map((contract) => (
                                  <div
                                    key={contract.id}
                                    onClick={() =>
                                      handleContractSelection(contract)
                                    }
                                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted cursor-pointer transition-colors group"
                                  >
                                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                      <FileText className="h-4 w-4 text-primary" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center gap-2 mb-1">
                                        <p className="font-medium m-0 text-card-foreground truncate group-hover:text-primary transition-colors">
                                          {contract.title}
                                        </p>
                                        {contract.overallConfidence && (
                                          <TableConfidenceIndicator
                                            confidence={
                                              contract.overallConfidence
                                            }
                                            field="Overall Extraction"
                                            size="sm"
                                            showPercentage={true}
                                          />
                                        )}
                                      </div>
                                      <p className="text-sm m-0 text-muted-foreground truncate">
                                        {contract.provider || ""}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-6">
                                <FileText className="h-10 w-10 text-muted-foreground/50 mx-auto mb-3" />
                                <p className="text-muted-foreground">
                                  No agreements found
                                </p>
                                <p className="text-sm text-muted-foreground/70">
                                  Try a different search term
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Contracts Section */}
        <div className="mt-8">
          <div className="bg-card rounded-xl border border-border shadow-sm">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-6 px-4">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Clock className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg m-0 font-semibold text-card-foreground">
                    Recently Assessed
                  </h3>

                </div>
              </div>

              {recentContractsLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className="flex items-center gap-4 p-4 rounded-lg border border-border"
                    >
                      <Skeleton className="h-10 w-10 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  ))}
                </div>
              ) : recentContracts.length > 0 ? (
                <div className="space-y-2">
                  {recentContracts.map((contract) => (
                    <div
                      key={contract.id}
                      onClick={() => handleContractSelection(contract)}
                      className="flex items-center gap-4 p-4 rounded-lg border border-border hover:bg-muted cursor-pointer transition-colors group"
                    >
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <FileText className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium m-0 text-card-foreground truncate group-hover:text-primary transition-colors">
                            {contract.title}
                          </p>
                          {contract.overallConfidence && (
                            <TableConfidenceIndicator
                              confidence={contract.overallConfidence}
                              field="Overall Extraction"
                              size="sm"
                              showPercentage={true}
                            />
                          )}
                        </div>
                        <p className="text-sm m-0 text-muted-foreground truncate">
                          {contract.provider || "No provider specified"}
                        </p>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {contract.createdAt
                          ? new Date(contract.createdAt).toLocaleDateString()
                          : ""}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-muted-foreground">
                    No recent documents found
                  </p>
                  <p className="text-sm text-muted-foreground/70">
                    Upload your first document to get started
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Note: Auto-Grouping Confirmation Modal removed since smart grouping is disabled */}
    </div>
  );
}
