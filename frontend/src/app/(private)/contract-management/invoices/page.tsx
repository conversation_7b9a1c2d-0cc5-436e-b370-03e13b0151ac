"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

export default function InvoicesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Invoice Reconciliation
        </h1>
        <p className="text-muted-foreground">
          Reconcile invoices with contracts and purchase orders
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Invoice Reconciliation Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Invoice reconciliation and matching tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
