import Link from "next/link"
import { 
  <PERSON><PERSON><PERSON>, 
  ArrowUpRight, 
  BarChart3, 
  Clock, 
  Key, 
  PlusCircle, 
  RefreshCw, 
  Shield, 
  Users,
  Search,
  Filter,
  SlidersHorizontal
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export default function LicensesPage() {
  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">License Management</h2>
          <p className="text-muted-foreground">
            Manage and track all your software licenses and entitlements.
          </p>
        </div>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          New License
        </Button>
      </div>
      
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Licenses</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">86</div>
            <p className="text-xs text-muted-foreground">
              +3 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">
              Within next 30 days
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Issues</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilization Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78%</div>
            <p className="text-xs text-muted-foreground">
              +5% from last quarter
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* License Management */}
      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle>License Repository</CardTitle>
              <CardDescription>
                View and manage all your software licenses
              </CardDescription>
            </div>
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search licenses..."
                  className="pl-8 w-full md:w-[200px] lg:w-[300px]"
                />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="software">Software</SelectItem>
                  <SelectItem value="saas">SaaS</SelectItem>
                  <SelectItem value="hardware">Hardware</SelectItem>
                  <SelectItem value="service">Service</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="sr-only">Advanced filters</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>License Name</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Expiration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Utilization</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                {
                  name: "Microsoft 365 E3",
                  vendor: "Microsoft",
                  type: "SaaS",
                  expiration: "2024-12-15",
                  status: "Active",
                  utilization: "92%",
                },
                {
                  name: "Adobe Creative Cloud",
                  vendor: "Adobe",
                  type: "Software",
                  expiration: "2024-08-30",
                  status: "Active",
                  utilization: "78%",
                },
                {
                  name: "Salesforce Enterprise",
                  vendor: "Salesforce",
                  type: "SaaS",
                  expiration: "2024-10-01",
                  status: "Active",
                  utilization: "85%",
                },
                {
                  name: "Oracle Database",
                  vendor: "Oracle",
                  type: "Software",
                  expiration: "2024-07-22",
                  status: "Expiring Soon",
                  utilization: "95%",
                },
                {
                  name: "AWS Enterprise Support",
                  vendor: "Amazon",
                  type: "Service",
                  expiration: "2025-01-15",
                  status: "Active",
                  utilization: "100%",
                },
                {
                  name: "VMware vSphere",
                  vendor: "VMware",
                  type: "Software",
                  expiration: "2024-09-10",
                  status: "Compliance Issue",
                  utilization: "110%",
                },
                {
                  name: "Zoom Enterprise",
                  vendor: "Zoom",
                  type: "SaaS",
                  expiration: "2024-11-05",
                  status: "Active",
                  utilization: "65%",
                },
              ].map((license, i) => (
                <TableRow key={i}>
                  <TableCell className="font-medium">{license.name}</TableCell>
                  <TableCell>{license.vendor}</TableCell>
                  <TableCell>{license.type}</TableCell>
                  <TableCell>{new Date(license.expiration).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        license.status === "Active" ? "outline" :
                        license.status === "Expiring Soon" ? "secondary" :
                        "destructive"
                      }
                    >
                      {license.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-full max-w-24 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${
                            parseInt(license.utilization) > 100 
                              ? "bg-destructive" 
                              : parseInt(license.utilization) > 90 
                                ? "bg-amber-500" 
                                : "bg-primary"
                          }`} 
                          style={{ width: `${Math.min(parseInt(license.utilization), 100)}%` }}
                        />
                      </div>
                      <span className="text-xs">{license.utilization}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Link href={`/licenses/${i + 1}`}>
                      <Button variant="ghost" size="icon">
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing 7 of 86 licenses
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm">
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Add New License</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-xs text-muted-foreground">
              Register a new software license or subscription
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/licenses/new">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Create
              </Button>
            </Link>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Compliance Report</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-xs text-muted-foreground">
              Generate a report on license compliance status
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/licenses/compliance">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Generate
              </Button>
            </Link>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">License Templates</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-xs text-muted-foreground">
              Browse and manage your license templates
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/licenses/templates">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Templates
              </Button>
            </Link>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Optimization</CardTitle>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-xs text-muted-foreground">
              View license optimization recommendations
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/licenses/optimization">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Optimize
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
