"use client";

import { useMemo, useState } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  Download,
  Edit,
  FileText,
  MoreHorizontal,
  RefreshCw,
  Shield,
  Users,
  AlertTriangle,
  BarChart3,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePathname } from "next/navigation";

export default function LicenseDetailPage() {
  const pathname = usePathname();

  const licenseId = useMemo(() => {
    const segments = pathname.split("/");
    return segments[segments.length - 1]; // assumes the last segment is the ID
  }, [pathname]);

  const [licenseData] = useState({
    id: licenseId,
    name: "Microsoft 365 E3",
    vendor: "Microsoft",
    type: "SaaS",
    description:
      "Microsoft 365 E3 includes premium Office applications, security and compliance tools, and Windows 10 Enterprise.",
    purchaseDate: "2023-06-15",
    expirationDate: "2024-12-15",
    renewalType: "Auto-renewal",
    cost: "$36,000.00",
    costPeriod: "Annual",
    status: "Active",
    totalLicenses: 150,
    assignedLicenses: 138,
    availableLicenses: 12,
    complianceStatus: "Compliant",
    documents: [
      {
        name: "Microsoft_365_E3_Contract.pdf",
        type: "Contract",
        date: "2023-06-15",
      },
      {
        name: "Microsoft_365_E3_Invoice.pdf",
        type: "Invoice",
        date: "2023-06-15",
      },
      { name: "Microsoft_365_Terms.pdf", type: "Terms", date: "2023-06-15" },
    ],
    history: [
      {
        action: "License Created",
        user: "John Doe",
        date: "2023-06-15",
        notes: "Initial license registration",
      },
      {
        action: "Licenses Added",
        user: "Sarah Johnson",
        date: "2023-09-22",
        notes: "Added 25 additional licenses",
      },
      {
        action: "Compliance Check",
        user: "System",
        date: "2023-12-01",
        notes: "Automated compliance verification - Passed",
      },
      {
        action: "Usage Report Generated",
        user: "Michael Chen",
        date: "2024-03-10",
        notes: "Quarterly usage report",
      },
    ],
    entitlements: [
      {
        name: "Exchange Online",
        description: "Email and calendar",
        included: true,
      },
      {
        name: "SharePoint Online",
        description: "Document management and collaboration",
        included: true,
      },
      {
        name: "Teams",
        description: "Chat, meetings, and calling",
        included: true,
      },
      {
        name: "Office Applications",
        description: "Word, Excel, PowerPoint, etc.",
        included: true,
      },
      {
        name: "Windows 10/11 Enterprise",
        description: "Operating system",
        included: true,
      },
      {
        name: "Advanced Threat Protection",
        description: "Security features",
        included: false,
      },
    ],
    usageData: [
      { month: "Jan", usage: 85 },
      { month: "Feb", usage: 87 },
      { month: "Mar", usage: 90 },
      { month: "Apr", usage: 92 },
      { month: "May", usage: 92 },
      { month: "Jun", usage: 92 },
    ],
  });

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Link href="/licenses">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {licenseData.name}
            </h2>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{licenseData.type}</Badge>
              <span className="text-muted-foreground">
                Vendor: {licenseData.vendor}
              </span>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Renew
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                Export Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <FileText className="mr-2 h-4 w-4" />
                Generate Report
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Shield className="mr-2 h-4 w-4" />
                Compliance Check
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                Archive License
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <div
              className={`rounded-full p-1 ${
                licenseData.status === "Active"
                  ? "bg-green-500/20 text-green-700 dark:text-green-300"
                  : licenseData.status === "Expiring Soon"
                  ? "bg-amber-500/20 text-amber-700 dark:text-amber-300"
                  : "bg-red-500/20 text-red-700 dark:text-red-300"
              }`}
            >
              {licenseData.status === "Active" ? (
                <Check className="h-4 w-4" />
              ) : licenseData.status === "Expiring Soon" ? (
                <Clock className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{licenseData.status}</div>
            <p className="text-xs text-muted-foreground">
              {licenseData.status === "Active"
                ? "In good standing"
                : licenseData.status === "Expiring Soon"
                ? "Renewal needed"
                : "Attention required"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiration</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Date(licenseData.expirationDate).toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {licenseData.renewalType}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">License Usage</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {licenseData.assignedLicenses} / {licenseData.totalLicenses}
            </div>
            <div className="mt-2">
              <div className="flex items-center gap-2">
                <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                  <div
                    className="h-full bg-primary"
                    style={{
                      width: `${
                        (licenseData.assignedLicenses /
                          licenseData.totalLicenses) *
                        100
                      }%`,
                    }}
                  />
                </div>
                <span className="text-xs">
                  {Math.round(
                    (licenseData.assignedLicenses / licenseData.totalLicenses) *
                      100
                  )}
                  %
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Annual Cost</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{licenseData.cost}</div>
            <p className="text-xs text-muted-foreground">
              {licenseData.costPeriod} payment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* License Details Tabs */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="entitlements">Entitlements</TabsTrigger>
          <TabsTrigger value="usage">Usage & Compliance</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Details</CardTitle>
              <CardDescription>
                Complete information about this license
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground">
                      {licenseData.description}
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">
                        Purchase Date
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {new Date(
                          licenseData.purchaseDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">
                        Expiration Date
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {new Date(
                          licenseData.expirationDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Renewal Type</h4>
                      <p className="text-sm text-muted-foreground">
                        {licenseData.renewalType}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Cost</h4>
                      <p className="text-sm text-muted-foreground">
                        {licenseData.cost} ({licenseData.costPeriod})
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">
                      License Allocation
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Total Licenses</span>
                        <span className="font-medium">
                          {licenseData.totalLicenses}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Assigned Licenses</span>
                        <span className="font-medium">
                          {licenseData.assignedLicenses}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Available Licenses</span>
                        <span className="font-medium">
                          {licenseData.availableLicenses}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1">
                      Compliance Status
                    </h4>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          licenseData.complianceStatus === "Compliant"
                            ? "outline"
                            : "destructive"
                        }
                      >
                        {licenseData.complianceStatus}
                      </Badge>
                      {licenseData.complianceStatus === "Compliant" && (
                        <span className="text-xs text-muted-foreground">
                          Last checked: 2 days ago
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="entitlements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Entitlements</CardTitle>
              <CardDescription>
                Features and services included in this license
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Feature</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {licenseData.entitlements.map((entitlement, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        {entitlement.name}
                      </TableCell>
                      <TableCell>{entitlement.description}</TableCell>
                      <TableCell className="text-right">
                        {entitlement.included ? (
                          <Badge variant="outline" className="bg-green-500/10">
                            Included
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted">
                            Not Included
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage & Compliance</CardTitle>
              <CardDescription>
                License usage metrics and compliance information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-8 md:grid-cols-2">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Usage Trend</h3>
                  <div className="h-[200px] flex items-end gap-2">
                    {licenseData.usageData.map((data, i) => (
                      <div
                        key={i}
                        className="flex flex-col items-center gap-2 flex-1"
                      >
                        <div
                          className="w-full bg-primary/20 rounded-t-sm relative"
                          style={{ height: `${data.usage}%` }}
                        >
                          <div
                            className="absolute bottom-0 left-0 w-full bg-primary rounded-t-sm"
                            style={{ height: `${data.usage}%` }}
                          />
                        </div>
                        <span className="text-xs">{data.month}</span>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      6-month usage trend
                    </span>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Detailed Analytics
                    </Button>
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Compliance Status</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 p-4 border rounded-lg">
                      <div
                        className={`rounded-full p-2 ${
                          licenseData.complianceStatus === "Compliant"
                            ? "bg-green-500/20 text-green-700 dark:text-green-300"
                            : "bg-red-500/20 text-red-700 dark:text-red-300"
                        }`}
                      >
                        <Shield className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium">License Compliance</h4>
                        <p className="text-sm text-muted-foreground">
                          {licenseData.complianceStatus === "Compliant"
                            ? "This license is compliant with all terms and conditions."
                            : "This license has compliance issues that need attention."}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Compliance Checks</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm p-2 bg-muted/50 rounded">
                          <span>License Count</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            Passed
                          </span>
                        </div>
                        <div className="flex justify-between text-sm p-2 bg-muted/50 rounded">
                          <span>Usage Rights</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            Passed
                          </span>
                        </div>
                        <div className="flex justify-between text-sm p-2 bg-muted/50 rounded">
                          <span>Geographic Restrictions</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            Passed
                          </span>
                        </div>
                        <div className="flex justify-between text-sm p-2 bg-muted/50 rounded">
                          <span>Renewal Status</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            Passed
                          </span>
                        </div>
                      </div>
                    </div>

                    <Button variant="outline" className="w-full">
                      <Shield className="mr-2 h-4 w-4" />
                      Run Compliance Check
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Documents</CardTitle>
              <CardDescription>
                Contracts, invoices, and related documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {licenseData.documents.map((document, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        {document.name}
                      </TableCell>
                      <TableCell>{document.type}</TableCell>
                      <TableCell>
                        {new Date(document.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="mt-4">
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License History</CardTitle>
              <CardDescription>
                Activity history and audit trail
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {licenseData.history.map((event, i) => (
                  <div key={i} className="flex gap-4">
                    <div className="flex-shrink-0 mt-1">
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{event.action}</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(event.date).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {event.notes}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        By: {event.user}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
