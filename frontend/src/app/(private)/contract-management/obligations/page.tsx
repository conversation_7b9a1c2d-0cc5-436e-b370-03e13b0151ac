"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

export default function ObligationsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Obligations Tracking
        </h1>
        <p className="text-muted-foreground">
          Track and manage contract obligations and commitments
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Obligations Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Contract obligations tracking tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
