"use client";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

export default function FinancialsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Financials & Consumption
        </h1>
        <p className="text-muted-foreground">
          Track financial performance and consumption metrics
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Financial Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Financial tracking and consumption analysis tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
