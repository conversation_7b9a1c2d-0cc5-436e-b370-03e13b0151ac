"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

import { Card, CardContent } from "@/components/ui/card";
import {
  Download,
  FileText,
  ArrowLeft,
  Edit,
  Trash,
  AlertTriangle,
  Building,
  Phone,
  Globe,
  CreditCard,
  Shield,
  FileCheck,
  TrendingUp,
  Clock,
  Target,
  Loader2,
  XCircle,
} from "lucide-react";
import { CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  contractService,
  Contract as BaseContract,
  AgreementType,
  SecurityClassification,
} from "@/services/contractService";
import { format } from "date-fns";
import {
  formatContractClassification,
  formatAgreementType,
} from "@/lib/classification-utils";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { DeleteConfirmationModal } from "@/components/ui/delete-confirmation-modal";
import { DocumentMetadata } from "@/components/contracts/DocumentMetadata";
import { downloadContractDocument } from "@/lib/download-utils";
import { formatCurrency, formatContractValue } from "@/lib/format-utils";
import { ContractStatusBadge } from "@/components/contracts/ContractStatusBadge";

import {
  AggregatedConfidenceScore,
  FieldConfidenceIndicator,
  getFieldConfidence,
} from "@/components/contracts/ConfidenceIndicators";
import {
  shouldDisplayField,
  shouldDisplaySection,
  getMetadataValue as getMetadataValueUtil,
  isValueMeaningful,
  getAttemptedButEmptyFields,
  generateEmptyFieldsTooltip,
  getDisplayValue,
  isCriticalField,
  MetadataFieldKey,
  getMetadataValue,
} from "@/utils/metadata-display";
import { SectionMetadataInfoTooltip } from "@/components/contracts/MetadataInfoTooltip";
import { ClauseAnalysisCard } from "@/components/contracts/ClauseAnalysisCard";
import { TabbedExtractionDisplay } from "@/components/contracts/TabbedExtractionDisplay";

// Define field mappings for each metadata section
const SECTION_FIELD_MAPPINGS: Record<string, MetadataFieldKey[]> = {
  overview: [
    "title",
    "agreement_type",
    "contract_classification",
    "provider",
    "client",
  ],
  financial: [
    "total_amount",
    "annually_amount",
    "contract_value",
    "currency",
    "payment_terms",
    "payment_terms_detailed",
  ],
  dates: [
    "effective_date",
    "end_date",
    "execution_date",
    "renewal_date",
    "termination_date",
  ],
  parties: ["provider", "client"],
  keyTerms: ["contract_classification", "governing_law"],
  sla: ["sla"],
  paymentTerms: ["payment_terms", "payment_terms_detailed"],
  renewalTerms: ["renewal_terms", "renewal_date"],
  legal: [
    "governing_law",
    "dispute_resolution",
    "confidentiality",
    "warranties",
    "amendments",
  ],
  termination: ["termination"],
  liability: ["liability_limits"],
  licensing: ["licensing"],
  indemnification: ["indemnification"],
  dataBreach: ["data_breach"],
  reporting: ["reporting_obligations"],
  governance: ["governance"],
  ipr: ["ipr"],
  rights: ["rights"],
  clauseAnalysis: ["clauses_analysis"],
  // Oracle-specific sections
  oracleOverview: [
    "publisher",
    "reseller",
    "entitled_entity",
    "entitled_entity_country",
    "original_document_name",
    "document_type",
  ],
  oracleProduct: [
    "product_name",
    "raw_product_name",
    "total_quantity",
    "metric",
    "metric_definition",
    "term",
    "level",
    "limitations",
    "included_rights",
  ],
  oracleSupport: [
    "csi",
    "support_contract_number",
    "support_start_date",
    "support_end_date",
    "governing_agreement",
  ],
  oracleFinancial: [
    "license_value",
    "license_value_per_unit",
    "contractual_support_value",
    "support_value_per_year",
    "support_value_per_year_per_unit",
    "oracle_currency",
  ],
  oracleAdditional: ["purchase_date", "index_field", "delta"],
};

// Use the Contract type directly from the service
type LocalContract = BaseContract;

// Enhanced contract metadata interface matching the modal
interface ContractMetadata {
  title?: string;
  contractType?: string;
  parties?: Array<{
    name: string;
    role?: string;
    address?: string;
    contact?: {
      email?: string;
      phone?: string;
    };
  }>;
  dates?: {
    startDate?: string;
    endDate?: string;
    executionDate?: string;
    effectiveDate?: string;
    renewalDate?: string;
    terminationDate?: string;
  };
  value?: {
    amount?: string | number;
    currency?: string;
    totalValue?: string | number;
    annualValue?: string | number;
  };

  // Enhanced metadata from new schema
  contractId?: string;
  provider?: string;
  client?: string;
  vendorName?: string;
  customerName?: string;
  renewalTermsDetailed?: Array<{
    auto_renewal?: string;
    notice_period?: string;
    renewal_term?: string;
    pricing_conditionality?: string;
  }>;
  lineItems?: Array<{
    description?: string;
    amount?: number;
    details?: string[];
  }>;
  liability?: {
    capping?: string;
    exclusions?: string;
  };
  paymentTermsDetailed?: {
    timeline?: string;
    delayed_payment_charges?: string;
    right_to_invoice?: string;
  };
  sla?: {
    availability?: string;
    response_time_p1?: string;
    response_time_p2?: string;
    resolution_time_p1?: string;
    resolution_time_p2?: string;
  };
  termination?: {
    t4c?: string;
    notice_t4c?: string;
  };
  contractValueCategory?: {
    acv?: string;
  };

  // Legacy fields for backward compatibility
  contractValue?: string | number;
  currency?: string;
  effectiveDate?: string;
  endDate?: string;
  startDate?: string;
  executionDate?: string;
  renewalDate?: string;
  terminationDate?: string;
  paymentTerms?:
  | {
    dueDays?: number;
    latePaymentPenalty?: string;
    paymentMethod?: string;
    description?: string;
  }
  | string;
  renewalTerms?:
  | {
    isAutoRenew?: boolean;
    renewalPeriod?: string;
    noticePeriodDays?: number;
    renewalConditions?: string;
  }
  | string;
  confidentiality?: string;
  terminationClauses?: string[];
  governingLaw?: string;
  disputeResolution?: string;
  liabilityLimits?: string;
  warranties?: string[];
  amendments?: string;
  signatures?: Array<{
    name?: string;
    title?: string;
    date?: string;
    party?: string;
  }>;
  analysisResult?: any;
  [key: string]: any;
}

interface ContractData {
  id: string;
  title: string;
  documentId?: string;
  metadata?: ContractMetadata;
  documentUrl?: string;
  createdAt: string;
}

// Helper functions for formatting
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper function to get display name (original filename without extension)
const getDisplayName = (contract: LocalContract): string => {
  // Get original filename from extraction data
  const extraction = (contract as any).extraction;
  const originalFilename = extraction?.fixedFields?.original_filename?.value;

  // Remove file extension from filename
  if (originalFilename) {
    return originalFilename.replace(/\.[^/.]+$/, "");
  }

  // Fallback to contract title
  return contract.title;
};

const formatPercentage = (value: string | number) => {
  const numValue = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(numValue) ? "N/A" : `${numValue}%`;
};

const formatDuration = (value: string) => {
  if (!value) return "N/A";
  // Handle common duration formats
  if (value.includes("hour")) return value;
  if (value.includes("day")) return value;
  if (value.includes("month")) return value;
  if (value.includes("year")) return value;
  return value;
};

// Helper function to detect if this is an Oracle contract
const isOracleContract = (contract: LocalContract): boolean => {
  // Check provider from the root level first (this is where it's actually stored)
  const rootProvider = contract.provider || "";

  // Check provider from metadata (fallback)
  const metadataProvider =
    getMetadataValueUtil(contract.metadata, "provider") || "";

  // Check publisher from metadata (Oracle-specific field)
  const publisher = getMetadataValueUtil(contract.metadata, "publisher") || "";

  // Check counterparty as fallback
  const counterparty = contract.counterparty || "";

  // Combine all possible provider sources
  const allProviders = [rootProvider, metadataProvider, publisher, counterparty]
    .filter(Boolean)
    .map((p) => p.toLowerCase());

  // Check if any provider contains "oracle"
  return allProviders.some((provider) => {
    return (
      provider.includes("oracle corporation") ||
      provider.includes("oracle america") ||
      provider.includes("oracle international") ||
      provider.includes("oracle israel") ||
      provider.includes("oracle emea") ||
      provider.includes("oracle europe") ||
      provider.includes("oracle asia") ||
      provider.includes("oracle japan") ||
      provider.includes("oracle canada") ||
      provider.includes("oracle australia") ||
      provider === "oracle" ||
      (provider.includes("oracle") &&
        (provider.includes("inc") ||
          provider.includes("ltd") ||
          provider.includes("llc") ||
          provider.includes("corp") ||
          provider.includes("limited") ||
          provider.includes("gmbh") ||
          provider.includes("sa") ||
          provider.includes("sas") ||
          provider.includes("bv") ||
          provider.includes("ag"))) ||
      // Match any provider that starts with "oracle " followed by a word
      /^oracle\s+\w+/.test(provider)
    );
  });
};

// Helper function to check if Oracle sections should be displayed
const shouldDisplayOracleSection = (
  contract: LocalContract,
  sectionFields: string[]
): boolean => {
  if (!isOracleContract(contract)) return false;

  // For Oracle contracts, show sections if ANY field was attempted (even if "N/A")
  // Check multiple locations for Oracle fields
  return sectionFields.some((field) => {
    // Check in three-tier extraction data first
    const extraction = (contract as any).extraction;
    if (extraction) {
      // Check Oracle special fields
      if (extraction.specialFields?.oracle?.[field]?.value) {
        return true;
      }

      // Check fixed fields
      if (extraction.fixedFields?.[field]?.value) {
        return true;
      }

      // Check dynamic fields
      if (extraction.dynamicFields?.[field]?.value) {
        return true;
      }
    }

    // Fallback to legacy metadata structure for backward compatibility
    const value = getMetadataValueUtil(contract.metadata, field);
    const autoExtracted = contract.metadata?.autoExtractedFields as any;
    const oracleFieldsValue = autoExtracted?.oracleFields?.[field];
    const rawResult = contract.metadata?.autoExtractedFields?.rawResult;
    const legacyOracleFieldsValue = rawResult?.oracle_fields?.[field];

    return (
      (value !== null && value !== undefined) ||
      (oracleFieldsValue !== null && oracleFieldsValue !== undefined) ||
      (legacyOracleFieldsValue !== null &&
        legacyOracleFieldsValue !== undefined)
    );
  });
};

export default function ContractViewPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const contractId = params.id as string;

  const [contract, setContract] = useState<LocalContract | null>(null);
  const [loading, setLoading] = useState(false);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [documentBlobUrl, setDocumentBlobUrl] = useState<string | null>(null);
  const [documentLoading, setDocumentLoading] = useState(false);
  const [documentError, setDocumentError] = useState<string | null>(null);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Edit mode state for three-tier extraction
  const [isEditMode, setIsEditMode] = useState(false);

  useEffect(() => {
    if (!user && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (user && contractId) {
      fetchContract(contractId);
    }
  }, [user, isLoading, contractId, router]);

  // Load document with authentication when documentUrl changes
  useEffect(() => {
    if (documentUrl && contractId) {
      loadDocument();
    }
    return () => {
      // Cleanup blob URL when component unmounts
      if (documentBlobUrl) {
        URL.revokeObjectURL(documentBlobUrl);
      }
    };
  }, [documentUrl, contractId]);

  const fetchContract = async (id: string) => {
    setLoading(true);
    try {
      const data = await contractService.getContract(id);
      setContract(data);

      // Set document URL
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || "";
      setDocumentUrl(`${baseUrl}/api/contracts/${id}/document`);
    } catch (error) {
      console.error("Error fetching contract:", error);
      toast.error("Failed to load contract details");
    } finally {
      setLoading(false);
    }
  };

  const loadDocument = async () => {
    if (!documentUrl) return;

    setDocumentLoading(true);
    setDocumentError(null);

    try {
      // Get the auth token from localStorage
      const token = localStorage.getItem("access_token");
      if (!token) {
        throw new Error("No authentication token found");
      }

      // Fetch the document with authorization header
      const response = await fetch(documentUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to load document: ${response.status} ${response.statusText}`
        );
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      setDocumentBlobUrl(blobUrl);
    } catch (error) {
      console.error("Error loading document:", error);
      setDocumentError(
        error instanceof Error ? error.message : "Failed to load document"
      );
    } finally {
      setDocumentLoading(false);
    }
  };

  // Helper function to get metadata from three-tier extraction structure
  const getMetadataValue = (path: string): any => {
    if (!contract) return null;

    // First try three-tier extraction data
    const extraction = (contract as any).extraction;
    if (extraction) {
      // Check fixed fields
      const fixedValue = path
        .split(".")
        .reduce((obj: any, key) => obj?.[key], extraction.fixedFields);
      if (
        fixedValue?.value !== undefined &&
        fixedValue.value !== null &&
        fixedValue.value !== "N/A"
      ) {
        return fixedValue.value;
      }

      // Check dynamic fields
      const dynamicValue = path
        .split(".")
        .reduce((obj: any, key) => obj?.[key], extraction.dynamicFields);
      if (
        dynamicValue?.value !== undefined &&
        dynamicValue.value !== null &&
        dynamicValue.value !== "N/A"
      ) {
        return dynamicValue.value;
      }

      // Check special fields (Oracle, Microsoft, SAP)
      const specialFields = extraction.specialFields;
      if (specialFields) {
        for (const vendor of ["oracle", "microsoft", "sap"]) {
          const specialValue = path
            .split(".")
            .reduce((obj: any, key) => obj?.[key], specialFields[vendor]);
          if (
            specialValue?.value !== undefined &&
            specialValue.value !== null &&
            specialValue.value !== "N/A"
          ) {
            return specialValue.value;
          }
        }
      }
    }

    // Fallback to legacy metadata structure for backward compatibility
    if (!contract?.metadata) return null;

    const rawResult = contract.metadata.autoExtractedFields?.rawResult;
    if (rawResult) {
      const value = path
        .split(".")
        .reduce((obj: any, key) => obj?.[key], rawResult);
      if (value !== undefined && value !== null && value !== "N/A")
        return value;
    }

    const autoExtracted = contract.metadata.autoExtractedFields;
    if (autoExtracted) {
      const value = path
        .split(".")
        .reduce((obj: any, key) => obj?.[key], autoExtracted);
      if (value !== undefined && value !== null && value !== "N/A")
        return value;
    }

    const value = path
      .split(".")
      .reduce((obj: any, key) => obj?.[key], contract.metadata as any);
    if (value !== undefined && value !== null && value !== "N/A") return value;

    return null;
  };

  const handleDownloadDocument = async () => {
    if (contractId) {
      try {
        // Use original filename from extraction, then title, then fallback to contract ID
        const originalFilename = contract?.extraction?.fixedFields?.original_filename?.value;
        const filename = originalFilename || contract?.title || `Contract-${contractId}`;
        await downloadContractDocument(contractId, filename);
        toast.success("Document downloaded successfully");
      } catch (error) {
        console.error("Error downloading document:", error);
        toast.error("Failed to download document");
      }
    }
  };

  const handleDeleteContract = () => {
    setDeleteModalOpen(true);
  };

  const confirmDeleteContract = async () => {
    if (!contract) return;

    setIsDeleting(true);
    try {
      await contractService.deleteContract(contract.id);
      toast.success("Contract deleted successfully");
      router.push("/contract-management/contracts");
    } catch (error) {
      console.error("Error deleting contract:", error);
      toast.error("Failed to delete contract");
    } finally {
      setIsDeleting(false);
      setDeleteModalOpen(false);
    }
  };

  const handleEditToggle = () => {
    setIsEditMode(!isEditMode);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Skeleton className="h-10 w-32" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-6 w-32" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-48" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/contract-management/contracts">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Repository
              </Link>
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Contract Not Found</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              The contract you're looking for could not be found. It may have
              been deleted or you may not have permission to view it.
            </p>
            <Button asChild className="mt-4">
              <Link href="/contract-management/contracts">
                Return to Contracts
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/contract-management/contracts">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Repository
            </Link>
          </Button>
        </div>

        <div className="flex gap-2">
          {documentBlobUrl && (
            <Button
              variant="outline"
              onClick={() => window.open(documentBlobUrl, "_blank")}
            >
              <FileText className="mr-2 h-4 w-4" />
              Open in New Tab
            </Button>
          )}
          {documentUrl && (
            <Button variant="outline" onClick={handleDownloadDocument}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          )}

          <Button variant="outline" onClick={handleEditToggle}>
            <Edit className="mr-2 h-4 w-4" />
            {isEditMode ? "Exit Edit Mode" : "Edit Fields"}
          </Button>

          <Button variant="destructive" onClick={handleDeleteContract}>
            <Trash className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Page Title */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">
            {getDisplayName(contract)}
          </h1>
          {contract.status ? (
            <ContractStatusBadge status={contract.status} />
          ) : (
            <ContractStatusBadge status="Unknown" />
          )}
        </div>
        <p className="text-muted-foreground">
          Contract details and documentation
        </p>
      </div>

      {/* Overall Confidence Score */}
      {contract.extraction?.overallConfidence && (
        <div className="mb-6">
          <AggregatedConfidenceScore
            scores={[
              {
                value: contract.extraction.overallConfidence,
                field: "Overall Extraction",
                source: "Three-Tier Analysis",
              },
            ]}
          />
        </div>
      )}

      {/* Contract Details - Tabbed Extraction Display */}
      <div className="flex gap-6 flex-1 min-h-0">
        {/* Left Panel - Tabbed Extraction Display */}
        <div className="flex-1 overflow-y-auto">
          {contract && (
            <TabbedExtractionDisplay
              contractId={contract.id}
              className=""
              isEditMode={isEditMode}
            />
          )}
        </div>

        {/* Right Panel - Document Viewer */}
        <div className="w-1/2 flex flex-col min-h-0">
          <div className="mb-4 flex-shrink-0">
            <h3 className="text-lg font-semibold">Document</h3>
          </div>
          <div className="flex-1 border rounded-lg overflow-hidden min-h-0">
            {documentBlobUrl ? (
              <iframe
                src={documentBlobUrl}
                className="w-full h-full"
                title="Contract Document"
              />
            ) : documentLoading ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p className="text-muted-foreground">Loading document...</p>
                </div>
              </div>
            ) : documentError ? (
              <div className="h-full flex items-center justify-center bg-muted rounded-md">
                <div className="text-center">
                  <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                  <p className="text-red-600 mb-2">Failed to load document</p>
                  <p className="text-muted-foreground text-sm">
                    {documentError}
                  </p>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center bg-muted rounded-md">
                <div className="text-center">
                  <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Document not available
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onConfirm={confirmDeleteContract}
        title="Delete Contract"
        description={`Are you sure you want to delete "${getDisplayName(
          contract
        )}"? This action cannot be undone.`}
        isLoading={isDeleting}
      />
    </div>
  );
}
