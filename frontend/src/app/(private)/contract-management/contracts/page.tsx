/**
 * Contracts Page
 * Main contracts repository and management interface
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  FileText,
  Search,
  ArrowUpDown,
  MoreHorizontal,
  Calendar,
  Tag,
  CheckCircle,
  Clock,
  Upload,
  X,
  Loader2,
  FileSearch,
  Eye,
  Download,
  Share,
  Trash,
  FilterX,
  SlidersHorizontal,
  Folder,
  FolderOpen,
  // Note: Users icon removed since Smart Grouping button is disabled
  FileSpreadsheet,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
// Removed tabs import - grouped functionality moved to stack discovery page
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Contract,
  AgreementType,
  contractService,
} from "@/services/contractService";
// Removed folder service imports - grouped functionality moved to stack discovery page
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { ContractStatusBadge } from "@/components/contracts/ContractStatusBadge";
import { DeleteConfirmationModal } from "@/components/ui/delete-confirmation-modal";

import { DataTable, DataTableColumn } from "@/components/ui/data-table";
// Note: Smart grouping and manual contract grouping functionality removed
// FolderAccordion moved to stack discovery page

import { EntitlementAnalysisModal } from "@/components/contracts/EntitlementAnalysisModal";
import {
  formatCurrency,
  formatContractTerm,
  formatNoticePeriod,
} from "@/lib/format-utils";
import {
  formatContractClassification,
  formatAgreementType,
  getClassificationColor,
  getAgreementTypeColor,
} from "@/lib/classification-utils";
import { cn } from "@/lib/utils";
import { contractExtractionService } from "@/services/contractExtractionService";
import { getContractStatus } from "@/lib/contract-status-utils";

// Interface for extraction data from the API
interface ExtractionData {
  contractId: string;
  fixedFields: {
    agreement_type?: { value: string; confidence: number };
    provider?: { value: string; confidence: number };
    client?: { value: string; confidence: number };
    product?: { value: string; confidence: number };
    total_amount?: { value: string; confidence: number };
    annually_amount?: { value: string; confidence: number };
    start_date?: { value: string; confidence: number };
    end_date?: { value: string; confidence: number };
    contract_id?: { value: string; confidence: number };
    contract_classification?: { value: string; confidence: number };
    // LLM-extracted status and term
    contract_status?: { value: string; confidence: number };
    contract_term?: { value: string; confidence: number };
    // File metadata
    original_filename?: { value: string; confidence: number };
    // Renewal fields
    auto_renewal?: { value: string; confidence: number };
    renewal_notice_period?: { value: string; confidence: number };
    // Calculated fields from backend (derived from auto_renewal + renewal_notice_period)
    safe_auto_renewal?: { value: string; confidence: number }; // "Yes" if no auto-renewal OR auto-renewal with ≥30 days notice
    intervention_opportunity?: { value: string; confidence: number };
  } | null;
  dynamicFields: any;
  specialFields: any;
  overallConfidence: number | null;
  extractionDate: string | null;
  extractionVersion: string | null;
  updatedAt: string;
  // Contract info from the three-tier extraction API
  contractInfo?: {
    title: string;
    description?: string;
    fileName?: string;
    status?: string;
    createdAt?: string;
    updatedAt?: string;
  };
}

// No sample contracts - we'll only use real data from the API

// Format date for display
function formatDate(dateString: string) {
  // Handle invalid or N/A dates
  if (!dateString || dateString === "N/A" || dateString.trim() === "") {
    return "N/A";
  }

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "N/A";
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  } catch {
    return "N/A";
  }
}

// Get status badge variant - uses calculated status with fallback
function getStatusBadge(contract: Contract) {
  return (
    <ContractStatusBadge
      status={contract.status || "Unknown"}
      variant="compact"
    />
  );
}

// Calculate time since update
function getTimeAgo(dateString: string) {
  const now = new Date();
  const updatedDate = new Date(dateString);
  const diffInDays = Math.floor(
    (now.getTime() - updatedDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  if (diffInDays < 7) return `${diffInDays} days ago`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
  return `${Math.floor(diffInDays / 30)} months ago`;
}

// Helper functions for extracting metadata from contracts
function getAutoRenewal(contract: Contract): string {
  // First check three-tier extraction fixed fields (prioritized)
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.auto_renewal?.value) {
    const value = (extraction.fixedFields as any).auto_renewal.value;
    // Fixed fields should only contain "Yes" or "No"
    if (value === "Yes" || value === "No") {
      return value;
    }
    // Legacy support - normalize to Yes/No
    if (value && value.toLowerCase().includes("yes")) {
      return "Yes";
    }
    // Default to "No" for any other value
    return "No";
  }

  // Check the basic isAutoRenew field (fallback)
  if (contract.isAutoRenew !== undefined && contract.isAutoRenew !== null) {
    return contract.isAutoRenew ? "Yes" : "No";
  }

  // Check metadata for detailed renewal terms (legacy fallback)
  const metadata = (contract as any).metadata;
  if (metadata) {
    if (
      metadata.renewalTermsDetailed &&
      Array.isArray(metadata.renewalTermsDetailed)
    ) {
      const renewalTerm = metadata.renewalTermsDetailed[0];
      if (renewalTerm?.auto_renewal) {
        const autoRenewal = renewalTerm.auto_renewal.toLowerCase();
        return autoRenewal.includes("yes") || autoRenewal.includes("automatic")
          ? "Yes"
          : "No";
      }
    }

    // Check legacy autoExtractedFields for renewal terms (fallback)
    if (metadata.autoExtractedFields?.renewalTerms) {
      const renewalTerms = metadata.autoExtractedFields.renewalTerms;
      if (
        typeof renewalTerms === "object" &&
        renewalTerms.isAutoRenew !== undefined
      ) {
        return renewalTerms.isAutoRenew ? "Yes" : "No";
      }
    }
  }

  return "N/A";
}

function getRenewalNoticePeriod(contract: Contract): string {
  // First check three-tier extraction fixed fields (prioritized)
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.renewal_notice_period?.value) {
    const value = (extraction.fixedFields as any).renewal_notice_period.value;
    if (value && value !== "N/A" && value.trim() !== "") {
      // If it's already formatted text, return as is
      if (isNaN(Number(value))) {
        return value;
      }
      // If it's a number, format it as days
      return formatNoticePeriod(Number(value));
    }
  }

  // Check the basic noticePeriodDays field (fallback)
  if (contract.noticePeriodDays && contract.noticePeriodDays > 0) {
    return formatNoticePeriod(contract.noticePeriodDays);
  }

  // Check metadata for detailed renewal terms (legacy fallback)
  const metadata = (contract as any).metadata;
  if (metadata) {
    if (
      metadata.renewalTermsDetailed &&
      Array.isArray(metadata.renewalTermsDetailed)
    ) {
      const renewalTerm = metadata.renewalTermsDetailed[0];
      if (renewalTerm?.notice_period) {
        return formatNoticePeriod(undefined, renewalTerm.notice_period);
      }
    }

    // Check legacy autoExtractedFields for renewal terms (fallback)
    if (metadata.autoExtractedFields?.renewalTerms) {
      const renewalTerms = metadata.autoExtractedFields.renewalTerms;
      if (typeof renewalTerms === "object" && renewalTerms.noticePeriodDays) {
        return formatNoticePeriod(renewalTerms.noticePeriodDays);
      }
    }
  }

  return "N/A";
}

function getContractTerm(contract: Contract): string {
  return formatContractTerm(contract.startDate, contract.endDate);
}

// Helper function to extract contract number from contract metadata
// Uses the same logic as the auto-grouping service
function getContractNumber(contract: Contract): string {
  // First check three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.contract_id?.value) {
    return extraction.fixedFields.contract_id.value;
  }

  // Then check the top-level contractNumber field on the contract object
  if (contract.contractNumber && contract.contractNumber !== "N/A") {
    return contract.contractNumber;
  }

  // If not found at top level, check metadata
  const metadata = (contract as any).metadata;
  if (!metadata) return "N/A";

  // Check various possible fields for contract number in legacy metadata (fallback)
  const autoExtracted = metadata.autoExtractedFields || {};

  // Check metadata fields
  let contractNumber =
    autoExtracted.contractNumber ||
    autoExtracted.contractId ||
    autoExtracted.contract_id ||
    metadata.contractNumber ||
    metadata.contractId ||
    null;

  // If not found at metadata level, check nested structures
  if (!contractNumber || contractNumber === "N/A") {
    // Check rawResult.contract_id
    if (
      autoExtracted.rawResult?.contract_id &&
      autoExtracted.rawResult.contract_id !== "N/A"
    ) {
      contractNumber = autoExtracted.rawResult.contract_id;
    }
  }

  return contractNumber && contractNumber !== "N/A" ? contractNumber : "N/A";
}

// Helper functions for three-tier extraction data
function getThreeTierProvider(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.provider?.value) {
    return extraction.fixedFields.provider.value;
  }

  return "N/A";
}

function getThreeTierContractValue(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.total_amount?.value) {
    return formatCurrency(extraction.fixedFields.total_amount.value);
  }

  // Fallback to existing value logic
  if (
    contract.value &&
    contract.value !== "0" &&
    contract.value !== "N/A" &&
    contract.value.toLowerCase() !== "not specified"
  ) {
    return formatCurrency(contract.value);
  }
  return "N/A";
}

function getThreeTierEffectiveDate(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.start_date?.value) {
    const dateValue = extraction.fixedFields.start_date.value;
    // Only format if it's not "N/A" or empty
    if (dateValue && dateValue !== "N/A" && dateValue.trim() !== "") {
      return formatDate(dateValue);
    }
  }

  // Fallback to existing start date logic
  return contract.startDate ? formatDate(contract.startDate) : "N/A";
}

function getThreeTierExpirationDate(contract: Contract): string {
  // Check if contract has three-tier extraction data
  const extraction = (contract as any).extraction;
  if (extraction?.fixedFields?.end_date?.value) {
    const dateValue = extraction.fixedFields.end_date.value;
    // Only format if it's not "N/A" or empty
    if (dateValue && dateValue !== "N/A" && dateValue.trim() !== "") {
      return formatDate(dateValue);
    }
  }

  // Fallback to existing end date logic
  return contract.endDate ? formatDate(contract.endDate) : "N/A";
}

// Column definitions for the contract table
const getContractColumns = (
  handleViewContract: (contractId: string) => void,
  handleDeleteContract: (contract: Contract) => void
): DataTableColumn<Contract>[] => {
  const columns: DataTableColumn<Contract>[] = [
    // 1. Contract Name (show original filename without extension)
    {
      key: "title",
      header: "Contract Name",
      className:
        "font-light table-cell-wrap w-[280px] min-w-[280px] max-w-[280px]",
      render: (_: any, contract: any) => {
        // Get original filename from extraction fixed fields
        const extraction = (contract as any).extraction;
        const originalFilename =
          extraction?.fixedFields?.original_filename?.value;

        // Remove file extension from filename
        const displayName = originalFilename
          ? originalFilename.replace(/\.[^/.]+$/, "")
          : contract.title;

        return (
          <div className="w-full max-w-[200px] min-w-[200px] overflow-hidden">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => handleViewContract(contract.id)}
                    className="hover:underline flex items-start gap-3 text-left w-full min-h-[3rem] p-1"
                  >
                    <FileText className="h-4 w-4 text-emerald-500 flex-shrink-0 mt-1" />
                    <span className="text-wrap-force leading-tight flex-1 min-w-0 text-sm">
                      {displayName}
                    </span>
                  </button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p className="font-light">{displayName}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <div className="text-xs text-muted-foreground mt-1 text-wrap-force px-1">
              Updated {getTimeAgo(contract.updatedAt)}
            </div>
          </div>
        );
      },
    },

    // 3. Status
    {
      key: "status",
      header: "Status",
      className: "text-center w-[90px]",
      render: (_: any, contract: any) => getStatusBadge(contract),
    },

    // 4. Provider
    {
      key: "provider",
      header: "Provider",
      className: "text-center min-w-[250px]",
      render: (_: any, contract: any) => (
        <span className="text-sm">{getThreeTierProvider(contract)}</span>
      ),
    },

    // 5. Contract ID
    {
      key: "contractNumber",
      header: "Contract ID",
      className: "text-center min-w-[120px]",
      sortable: true,
      accessor: (contract: Contract) => getContractNumber(contract),
      render: (_: any, contract: any) => (
        <span className="text-sm font-light">
          {getContractNumber(contract)}
        </span>
      ),
    },

    // 6. Type (Agreement Type + Grouping Status)
    {
      key: "agreementType",
      header: "Type",
      className: "text-center w-[140px]",
      render: (_: any, contract: any) => {
        const agreementType = formatAgreementType(contract.agreementType);
        const colorClass = getAgreementTypeColor(contract.agreementType);
        // Note: Grouping indicator removed since folders are now virtual

        return (
          <div className="flex flex-col items-center gap-1">
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${colorClass}`}
            >
              {agreementType}
            </span>
          </div>
        );
      },
    },

    // 7. Classification
    {
      key: "classification",
      header: "Classification",
      className: "text-center w-[140px]",
      render: (_: any, contract) => {
        // Get the contract classification from extraction data
        const extraction = (contract as any).extraction;
        const classificationValue =
          extraction?.fixedFields?.contract_classification?.value;

        const classification =
          formatContractClassification(classificationValue);
        const colorClass = getClassificationColor(classificationValue);

        return (
          <span
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${colorClass}`}
          >
            {classification}
          </span>
        );
      },
    },

    // 8. TCV (Total Contract Value)
    {
      key: "tcv",
      header: "TCV",
      className: "text-center  min-w-[100px]",
      render: (_: any, contract) => {
        const value = getThreeTierContractValue(contract);
        return (
          <span
            className={`font-light text-sm whitespace-nowrap ${value === "N/A" ? "text-muted-foreground" : ""
              }`}
          >
            {value}
          </span>
        );
      },
    },

    // 9. Effective Date (from three-tier extraction)
    {
      key: "effectiveDate",
      header: "Effective Date",
      className: "text-center w-[130px]",
      render: (_: any, contract) => {
        const date = getThreeTierEffectiveDate(contract);
        return (
          <span
            className={`text-sm whitespace-nowrap ${date === "N/A" ? "text-muted-foreground" : ""
              }`}
          >
            {date}
          </span>
        );
      },
    },

    // 10. Expiration Date (from three-tier extraction)
    {
      key: "expirationDate",
      header: "Expiration Date",
      className: "text-center w-[130px]",
      render: (_: any, contract) => {
        const date = getThreeTierExpirationDate(contract);
        return (
          <span
            className={`text-sm whitespace-nowrap ${date === "N/A" ? "text-muted-foreground" : ""
              }`}
          >
            {date}
          </span>
        );
      },
    },

    // 11. Contract Term
    {
      key: "contractTerm",
      header: "Contract Term",
      className: "text-center max-w-[100px]",
      render: (_: any, contract) => (
        <span className="text-foreground text-sm whitespace-nowrap font-light">
          {getContractTerm(contract)}
        </span>
      ),
    },

    // 12. Auto Renewal
    {
      key: "autoRenewal",
      header: "Auto Renewal",
      className: "text-center w-[100px]",
      render: (_: any, contract) => {
        const autoRenewal = getAutoRenewal(contract);
        return (
          <span
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap ${autoRenewal === "Yes"
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              : autoRenewal === "No"
                ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
              }`}
          >
            {autoRenewal}
          </span>
        );
      },
    },

    // 13. Renewal Notice
    {
      key: "renewalNotice",
      header: "Renewal Notice",
      className: "text-center max-w-[120px]",
      render: (_: any, contract) => {
        const renewalNotice = getRenewalNoticePeriod(contract);
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="text-foreground max-w-[120px] text-sm font-light block truncate cursor-help">
                  {renewalNotice}
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                className="bg-primary text-primary-foreground"
              >
                <p>{renewalNotice}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },

    // 14. Safe Auto Renewal (display N/A instead of calculated values)
    {
      key: "safeAutoRenewal",
      header: "Safe Auto Renewal",
      className: "text-center min-w-[120px]",
      render: () => {
        // Always display N/A instead of backend-calculated values
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-light whitespace-nowrap bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
            N/A
          </span>
        );
      },
    },

    // 15. Intervention Opportunity (calculated on backend)
    {
      key: "interventionOpportunity",
      header: "Intervention Opportunity",
      className: "text-center w-[110px]",
      render: (_: any, contract) => {
        // Get the backend-calculated intervention opportunity value
        const extraction = (contract as any).extraction;
        const interventionLevel = parseInt(
          extraction?.fixedFields?.intervention_opportunity?.value || "3"
        );

        const getInterventionColor = (level: number) => {
          if (level <= 2)
            return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
          if (level === 3)
            return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
          return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
        };

        return (
          <span
            className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-light ${getInterventionColor(
              interventionLevel
            )}`}
          >
            {interventionLevel}
          </span>
        );
      },
    },

    // 16. Actions
    {
      key: "actions",
      header: "Actions",
      className: "text-center w-[80px]",
      render: (_: any, contract) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => handleViewContract(contract.id)}>
              <Eye className="h-4 w-4 mr-2" />
              View Contract
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Share className="h-4 w-4 mr-2" />
              Share
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => handleDeleteContract(contract)}
              className="text-red-600"
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return columns;
};

export default function ContractsPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [importing, setImporting] = useState(false);
  const [extractions, setExtractions] = useState<ExtractionData[]>([]);
  const [loading, setLoading] = useState(true);
  // Removed folders state - grouped functionality moved to stack discovery page
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  // Removed grouped tab functionality - now only shows files tab
  const [filterStatus, setFilterStatus] = useState<
    "Active" | "Inactive" | "Unknown" | ""
  >("");
  const [filterType, setFilterType] = useState<AgreementType | "all">("all");
  const [filterCounterparty, setFilterCounterparty] = useState("");
  const [filterStartDateFrom, setFilterStartDateFrom] = useState("");
  const [filterStartDateTo, setFilterStartDateTo] = useState("");
  const [filterValueMin, setFilterValueMin] = useState("");
  const [filterValueMax, setFilterValueMax] = useState("");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [contractToDelete, setContractToDelete] = useState<Contract | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  // Note: Remove from folder functionality removed since folders are now virtual

  // Lightweight contract data for selection across pages
  interface SelectedContractData {
    id: string;
    title: string;
    agreementType: string;
    folderId?: string | null;
  }

  // Bulk selection and grouping state
  const [selectedContracts, setSelectedContracts] = useState<
    Map<string, SelectedContractData>
  >(new Map());
  // Note: Group contracts modal state removed since manual grouping is disabled

  // Note: Folder management state removed since folders are now virtual

  // Note: Auto-grouping state removed since smart grouping functionality is disabled

  // Entitlement analysis state
  const [entitlementAnalysisModalOpen, setEntitlementAnalysisModalOpen] =
    useState(false);

  // Export state
  const [exporting, setExporting] = useState(false);

  // Repository statistics state
  const [repositoryStats, setRepositoryStats] = useState<{
    totalContracts: number;
    activeContracts: number;
    inactiveContracts: number;
    unknownStatusContracts: number;
    agreementTypesCount: number;
    recentlyAddedCount: number;
    agreementTypeBreakdown: { [key: string]: number };
  } | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      // Reset to first page when search term changes
      setPage(0);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Reset page when filters change
  useEffect(() => {
    setPage(0);
  }, [
    filterStatus,
    filterType,
    filterCounterparty,
    filterStartDateFrom,
    filterStartDateTo,
    filterValueMin,
    filterValueMax,
  ]);

  const fetchExtractions = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        title: debouncedSearchTerm || undefined,
        agreementType: filterType === "all" ? undefined : filterType,
        // Note: We'll filter by calculated status on the frontend since backend uses ContractStatus
        // Backend filtering by status is disabled for now
      };

      const result = await contractService.getContractsWithExtraction(params);

      // Store all extractions from the current page
      const allExtractionsData = result.extractions || [];

      // For now, just set the extractions data directly
      // TODO: Implement filtering for extraction data if needed
      setExtractions(allExtractionsData);

      // Use the total from the server for proper pagination
      setTotal(result.total || 0);

      // If no extractions are returned, show empty state
      if (!result.extractions || result.extractions.length === 0) {
        console.log("No extractions returned from API");
        setExtractions([]);
        setTotal(result.total || 0); // Keep server total even if current page is empty
      }
    } catch (error) {
      console.error("Error fetching extraction data:", error);
      // Show empty state if API fails
      setExtractions([]);
      setTotal(0);
      toast.error("Failed to load extraction data", {
        description:
          "Please try again later or contact support if the problem persists.",
      });
    } finally {
      setLoading(false);
    }
  }, [
    page,
    rowsPerPage,
    debouncedSearchTerm,
    filterStatus,
    filterType,
    filterCounterparty,
    filterStartDateFrom,
    filterStartDateTo,
    filterValueMin,
    filterValueMax,
  ]);

  // Removed fetchFolders function - grouped functionality moved to stack discovery page

  // Note: Folders tab removed - grouped functionality moved to stack discovery page

  const fetchRepositoryStats = useCallback(async () => {
    setStatsLoading(true);
    try {
      const stats = await contractService.getRepositoryStatistics();
      setRepositoryStats(stats);
    } catch (error) {
      console.error("Error fetching repository statistics:", error);
      // Don't show toast error for stats as it's not critical
      setRepositoryStats(null);
    } finally {
      setStatsLoading(false);
    }
  }, []);

  // Fetch contracts and folders on component mount and when filters change
  useEffect(() => {
    if (!user && !isLoading) {
      router.push("/auth/login");
      return;
    }

    if (user) {
      fetchExtractions();
      // Note: Don't fetch folders on initial load - only when user switches to folders tab
      fetchRepositoryStats();
    }
  }, [
    user,
    isLoading,
    router,
    fetchExtractions,
    fetchRepositoryStats,
  ]);

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleViewContract = (contractId: string) => {
    router.push(`/contract-management/contracts/analysis/${contractId}`);
  };

  const handleDeleteContract = (contract: Contract) => {
    setContractToDelete(contract);
    setDeleteModalOpen(true);
  };

  const confirmDeleteContract = async () => {
    if (!contractToDelete) return;

    setIsDeleting(true);
    try {
      await contractService.deleteContract(contractToDelete.id);
      toast.success("Contract deleted successfully");
      fetchExtractions();
      setDeleteModalOpen(false);
      setContractToDelete(null);
    } catch (error) {
      console.error("Error deleting contract:", error);
      toast.error("Failed to delete contract");
    } finally {
      setIsDeleting(false);
    }
  };

  // Bulk delete functionality
  const [bulkDeleteModalOpen, setBulkDeleteModalOpen] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  const handleBulkDelete = () => {
    if (selectedContracts.size === 0) return;
    setBulkDeleteModalOpen(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedContracts.size === 0) return;

    setIsBulkDeleting(true);
    try {
      const contractIds = Array.from(selectedContracts.keys());
      const result = await contractService.deleteContracts(contractIds);

      if (result.success) {
        if (result.failed === 0) {
          toast.success(
            `Successfully deleted ${result.deleted} contract${result.deleted !== 1 ? "s" : ""
            }`
          );
        } else {
          toast.success(
            `Deleted ${result.deleted} contract${result.deleted !== 1 ? "s" : ""
            }, ${result.failed} failed`
          );
          // Show errors for failed deletions
          result.errors.forEach((error) => {
            toast.error(error);
          });
        }
      } else {
        toast.error("Failed to delete contracts");
        result.errors.forEach((error) => {
          toast.error(error);
        });
      }

      setBulkDeleteModalOpen(false);
      setSelectedContracts(new Map());
      fetchExtractions(); // Refresh the contracts list
    } catch (error) {
      console.error("Error in bulk delete:", error);
      toast.error("Failed to delete contracts");
    } finally {
      setIsBulkDeleting(false);
    }
  };

  // Note: Remove from folder functionality removed since folders are now virtual

  // Bulk selection handlers
  const handleContractSelection = (
    contractId: string | number,
    checked: boolean
  ) => {
    const newSelected = new Map(selectedContracts);
    const id = String(contractId);

    if (checked) {
      // Find the contract in the current page data to get lightweight info
      const contract = getAllContracts().find((c) => c.id === id);
      if (contract) {
        newSelected.set(id, {
          id: contract.id,
          title: contract.title,
          agreementType: contract.agreementType,
        });
      }
    } else {
      newSelected.delete(id);
    }
    setSelectedContracts(newSelected);
  };

  const handleSelectAllContracts = (checked: boolean) => {
    const newSelected = new Map(selectedContracts);

    if (checked) {
      // Add all current page contracts to selection
      getAllContracts().forEach((contract) => {
        newSelected.set(contract.id, {
          id: contract.id,
          title: contract.title,
          agreementType: contract.agreementType,
        });
      });
    } else {
      // Remove all current page contracts from selection
      getAllContracts().forEach((contract) => {
        newSelected.delete(contract.id);
      });
    }

    setSelectedContracts(newSelected);
  };

  const getSelectedContractObjects = (): Contract[] => {
    // Convert the lightweight selected contract data to Contract objects
    // This is sufficient for the GroupContractsModal which only needs id, title, agreementType, and folderId
    return Array.from(selectedContracts.values()).map((contractData) => ({
      ...contractData,
      agreementType: contractData.agreementType as AgreementType,
      folderId: contractData.folderId || undefined,
      // Add minimal required fields that GroupContractsModal expects but doesn't actually use
      description: "",
      contractNumber: "",
      status: "Unknown" as any,
      startDate: "",
      endDate: "",
      renewalType: "",
      renewalDate: "",
      isAutoRenew: false,
      noticePeriodDays: 0,
      version: 1,
      securityClassification: "PUBLIC" as any,
      isEncrypted: false,
      currentVersionId: "",
      createdAt: "",
      updatedAt: "",
      tenantId: "",
      counterparty: "",
      value: "",
    }));
  };

  // Note: Group contracts functionality removed since manual grouping is disabled

  // Helper function to format dates properly
  const formatDateField = (dateValue: string | undefined): string => {
    if (!dateValue || dateValue === "N/A" || dateValue.trim() === "") {
      return "N/A";
    }

    try {
      const date = new Date(dateValue);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "N/A";
      }
      // Format as MM/DD/YYYY
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  // No longer calculating status on frontend - using LLM-extracted values only

  // Convert extraction data to contract-like objects for the table
  const getContractsFromExtractions = (): Contract[] => {
    return extractions.map((extraction) => {
      const fixedFields = extraction.fixedFields || {};

      // Get status from LLM extraction only, no calculation
      const startDate = fixedFields.start_date?.value;
      const endDate = fixedFields.end_date?.value;
      const extractedStatus = fixedFields.contract_status?.value;
      const calculatedStatus = getContractStatus(extractedStatus);

      // Extract auto renewal and notice period from fixed fields
      const autoRenewalValue = (fixedFields as any).auto_renewal?.value;
      // Fixed fields should only contain "Yes" or "No"
      const isAutoRenew = autoRenewalValue === "Yes";

      const noticePeriodValue = (fixedFields as any).renewal_notice_period
        ?.value;
      let noticePeriodDays = 0;
      if (noticePeriodValue && !isNaN(Number(noticePeriodValue))) {
        noticePeriodDays = Number(noticePeriodValue);
      }

      return {
        id: extraction.contractId,
        // Use original filename from contractInfo or fallback to original_filename field
        title:
          (extraction as any).contractInfo?.title ||
          fixedFields.original_filename?.value ||
          fixedFields.contract_id?.value ||
          "N/A",
        description: "",
        contractNumber: fixedFields.contract_id?.value || "N/A",
        agreementType: fixedFields.agreement_type?.value || "N/A",
        status: calculatedStatus,
        startDate: formatDateField(startDate),
        endDate: formatDateField(endDate),
        renewalType: "",
        renewalDate: "N/A",
        isAutoRenew: isAutoRenew,
        noticePeriodDays: noticePeriodDays,
        version: 1,
        securityClassification:
          fixedFields.contract_classification?.value || "PUBLIC",
        isEncrypted: false,
        currentVersionId: "",
        createdAt: extraction.updatedAt || new Date().toISOString(),
        updatedAt: extraction.updatedAt || new Date().toISOString(),
        tenantId: "",
        counterparty: fixedFields.client?.value || "N/A",
        value: fixedFields.total_amount?.value || "N/A",
        provider: fixedFields.provider?.value || "N/A",
        // Add extraction data for helper functions
        extraction: extraction,
      } as Contract & { extraction: ExtractionData };
    });
  };

  // Get all contracts for the Files tab (both grouped and standalone)
  const getAllContracts = (): Contract[] => {
    return getContractsFromExtractions();
  };

  // Note: Folder management handlers removed since folders are now virtual

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      // Convert FileList to array and add to selected files
      const newFiles = Array.from(e.target.files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // Convert FileList to array and add to selected files
      const newFiles = Array.from(e.dataTransfer.files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleImport = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setImporting(true);

      // Show a toast to indicate AI analysis is happening
      const importToastId = toast.loading(
        `AI is analyzing ${selectedFiles.length} contract${selectedFiles.length > 1 ? "s" : ""
        }...`,
        {
          description:
            "This may take a few moments as we extract key information",
          duration: 60000, // Long duration since we'll dismiss it manually
        }
      );

      // Use the batch import endpoint for better performance
      const formData = new FormData();
      selectedFiles.forEach((file) => {
        formData.append("files", file);
      });

      // Track progress for large imports
      let progressInterval: NodeJS.Timeout | null = null;
      let progressCounter = 0;

      if (selectedFiles.length > 3) {
        // For larger imports, show progress updates
        progressInterval = setInterval(() => {
          progressCounter += 1;
          const estimatedTotal = selectedFiles.length * 5; // Rough estimate: 5 seconds per file
          const progress = Math.min(
            95,
            Math.round((progressCounter / estimatedTotal) * 100)
          );

          toast.loading(`Processing contracts (${progress}%)...`, {
            id: importToastId,
            description: `Analyzed ${Math.min(
              progressCounter,
              selectedFiles.length
            )} of ${selectedFiles.length} contracts`,
          });
        }, 1000);
      }

      try {
        // Use the same import API as Discovery page for consistency
        const result = await contractService.importContracts(selectedFiles);

        // Clear the progress interval if it exists
        if (progressInterval) {
          clearInterval(progressInterval);
        }

        // Handle background processing response (202 status)
        if (result.processingStarted && result.jobId) {
          toast.success(
            `Import processing started for ${result.filesReceived} files`,
            {
              id: importToastId, // Replace the loading toast
              description: `Processing in background. Job ID: ${result.jobId}`,
              duration: 8000,
            }
          );

          // Start polling for job status
          const pollJobStatus = async () => {
            if (!result.jobId) return true; // Stop polling if no job ID

            try {
              const jobStatus = await contractService.getImportJobStatus(
                result.jobId
              );

              if (jobStatus.success && jobStatus.job) {
                const job = jobStatus.job;

                if (job.status === "completed") {
                  toast.success(
                    `Import completed: ${job.successfulFiles} successful, ${job.failedFiles} failed`,
                    {
                      description:
                        "All contracts have been processed and analyzed",
                      duration: 5000,
                    }
                  );

                  // Refresh the contracts list
                  fetchExtractions();
                  return true; // Stop polling
                } else if (job.status === "failed") {
                  toast.error(
                    `Import failed: ${job.errors?.[0]?.error || "Unknown error"
                    }`,
                    {
                      description: "Please try uploading the files again",
                      duration: 8000,
                    }
                  );
                  return true; // Stop polling
                } else {
                  // Still processing, show progress
                  const progressText = `Processing: ${job.processedFiles}/${job.totalFiles} files (${job.progressPercentage}%)`;
                  console.log(progressText);
                  return false; // Continue polling
                }
              }
            } catch (error) {
              console.error("Error polling job status:", error);
              return true; // Stop polling on error
            }
            return false;
          };

          // Poll every 3 seconds
          const pollInterval = setInterval(async () => {
            const shouldStop = await pollJobStatus();
            if (shouldStop) {
              clearInterval(pollInterval);
            }
          }, 3000);

          // Stop polling after 10 minutes
          setTimeout(() => {
            clearInterval(pollInterval);
          }, 600000);

          // Close the import dialog for background processing
          setImportDialogOpen(false);
          setSelectedFiles([]);
        } else {
          // Handle immediate response (synchronous processing)
          // Show detailed success message
          if (result.successful > 0) {
            toast.success(
              `Imported and analyzed ${result.successful} contract${result.successful > 1 ? "s" : ""
              } successfully`,
              {
                id: importToastId, // Replace the loading toast
                description:
                  "AI has extracted metadata including parties, values, and dates",
                duration: 5000,
              }
            );

            // If we have contracts in the result, check for auto-grouping opportunities
            if (result.contracts && result.contracts.length > 0) {
              // Close the import dialog
              setImportDialogOpen(false);

              // Reset selected files
              setSelectedFiles([]);

              // Note: Auto-grouping functionality removed - just refresh the list
              fetchExtractions();
            }
          } else {
            // If no successful imports, show error
            toast.error("No contracts were successfully imported", {
              id: importToastId, // Replace the loading toast
              description: "Something went wrong. Please try again",
              duration: 5000,
            });
          }
        }

        // Show errors if any
        if (result.failed > 0) {
          // Extract clean error message from backend
          let errorMessage = "Please check the file formats and try again";
          if (result.errors && result.errors.length > 0) {
            errorMessage = result.errors[0].error;
            // Clean up nested error messages
            if (
              errorMessage.includes(
                "Failed to import contracts from documents: "
              )
            ) {
              errorMessage = errorMessage.replace(
                "Failed to import contracts from documents: ",
                ""
              );
            }
          }

          toast.error(
            `Failed to import ${result.failed} contract${result.failed > 1 ? "s" : ""
            }`,
            {
              description: errorMessage,
              duration: 5000,
            }
          );

          // Log detailed errors to console for debugging
          if (result.errors && result.errors.length > 0) {
            console.error("Import errors:", result.errors);
          }
        }
      } catch (importError) {
        // Clear the progress interval if it exists
        if (progressInterval) {
          clearInterval(progressInterval);
        }

        console.error("Error importing contracts:", importError);

        // Use the error message from backend
        const errorMessage =
          importError instanceof Error
            ? importError.message
            : "Please try again";

        toast.error("Failed to import contracts", {
          id: importToastId, // Replace the loading toast
          description: errorMessage,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("Error preparing import:", error);
      toast.error("Failed to prepare contracts for import", {
        description: "Please try again with different files",
      });
    } finally {
      setImporting(false);
      fetchExtractions(); // Always refresh the list
    }
  };

  // Note: Auto-grouping handlers removed since smart grouping functionality is disabled

  // Note: Manual auto-grouping handler removed since smart grouping functionality is disabled

  // Repository export handler
  const handleExportRepository = async () => {
    try {
      setExporting(true);

      // Show loading toast
      const exportToastId = toast.loading("Preparing repository export...", {
        description: "Gathering contract data for export",
      });

      // Get all contracts data for export
      const contractsData = getAllContracts();

      if (contractsData.length === 0) {
        toast.error("No contracts available to export", {
          id: exportToastId,
        });
        return;
      }

      // Use the repository export service
      const { repositoryExportService } = await import(
        "@/services/repositoryExportService"
      );

      // Prepare the exact same data that's shown in the UI table
      // This matches the column structure from getContractColumns()
      const exportData = contractsData.map((contract) => {
        const extraction = (contract as any).extraction;

        return {
          "Contract Name": contract.title,
          Folder: "Virtual (Provider-based)",
          "Provider/Supplier": getThreeTierProvider(contract),
          "Contract ID": getContractNumber(contract),
          "Agreement Type": formatAgreementType(contract.agreementType),
          "Client/Counterparty": contract.counterparty || "N/A",
          "Contract Value": getThreeTierContractValue(contract),
          "Start Date": getThreeTierEffectiveDate(contract),
          "End Date": getThreeTierExpirationDate(contract),
          Status: contract.status,
          "Auto Renewal": getAutoRenewal(contract),
          "Notice Period": getRenewalNoticePeriod(contract),
          "Contract Term": getContractTerm(contract),
          "Last Updated": formatDate(contract.updatedAt),
          "Overall Confidence": extraction?.overallConfidence
            ? `${Math.round(extraction.overallConfidence * 100)}%`
            : "N/A",
        };
      });

      // Prepare UI table data format for the backend
      const tableData = {
        columns: [
          { header: "Contract Name", key: "contractName" },
          { header: "Folder", key: "folder" },
          { header: "Provider/Supplier", key: "provider" },
          { header: "Contract ID", key: "contractId" },
          { header: "Agreement Type", key: "agreementType" },
          { header: "Client/Counterparty", key: "counterparty" },
          { header: "Contract Value", key: "value" },
          { header: "Start Date", key: "startDate" },
          { header: "End Date", key: "endDate" },
          { header: "Status", key: "status" },
          { header: "Auto Renewal", key: "autoRenewal" },
          { header: "Notice Period", key: "noticePeriod" },
          { header: "Contract Term", key: "contractTerm" },
          { header: "Last Updated", key: "lastUpdated" },
          { header: "Overall Confidence", key: "confidence" },
        ],
        rows: exportData.map((row) => [
          row["Contract Name"],
          row["Folder"],
          row["Provider/Supplier"],
          row["Contract ID"],
          row["Agreement Type"],
          row["Client/Counterparty"],
          row["Contract Value"],
          row["Start Date"],
          row["End Date"],
          row["Status"],
          row["Auto Renewal"],
          row["Notice Period"],
          row["Contract Term"],
          row["Last Updated"],
          row["Overall Confidence"],
        ]),
      };

      // Export using backend API
      await repositoryExportService.exportUITableToExcel(tableData);

      toast.success(`Successfully exported ${contractsData.length} contracts`, {
        id: exportToastId,
        description: "Repository export completed",
        duration: 5000,
      });
    } catch (error) {
      console.error("Error exporting repository:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to export repository";
      toast.error(errorMessage, {
        description: "Please try again later",
      });
    } finally {
      setExporting(false);
    }
  };

  // Show loading state while auth is loading
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login redirect if no user
  if (!user) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col w-full max-w-full overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="flex-shrink-0 px-4 sm:px-6 lg:px-8 w-full max-w-full">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
          <div className="min-w-0 flex-1">
            <h2 className="text-3xl font-bold tracking-tight text-foreground">
              Contracts
            </h2>
            <p className="text-muted-foreground">
              Manage and track all your contracts in one place.
            </p>
          </div>
          <div className="flex gap-2 flex-shrink-0">
            {/* Note: Smart Grouping button removed since smart grouping functionality is disabled */}
            {/* <Button
              onClick={() => setEntitlementAnalysisModalOpen(true)}
              variant="outline"
              disabled={getAllContracts().length === 0}
              className="border-green-600 text-green-600 hover:bg-green-50"
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Entitlement Analysis
            </Button> */}
            <Button
              onClick={() => setImportDialogOpen(true)}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
              <DialogContent className="sm:max-w-2xl max-h-[90vh] flex flex-col">
                <DialogHeader>
                  <DialogTitle>Import & Analyze Contracts</DialogTitle>
                  <DialogDescription>
                    Upload Agreement Documents for AI-powered analysis and
                    comprehensive metadata extraction. Our system will
                    automatically identify key contract details including
                    parties, values, dates, and terms.
                  </DialogDescription>
                </DialogHeader>
                <div className="flex-1 overflow-y-auto px-6 py-4">
                  <div className="space-y-6">
                    <div className="mb-2">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="bg-emerald-100 dark:bg-emerald-900/30 p-1.5 rounded-full">
                          <FileText className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <p className="text-sm font-medium">
                          Upload your contracts
                        </p>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="bg-emerald-100 dark:bg-emerald-900/30 p-1.5 rounded-full">
                          <Search className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <p className="text-sm font-medium">
                          AI analyzes the content
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-emerald-100 dark:bg-emerald-900/30 p-1.5 rounded-full">
                          <Tag className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <p className="text-sm font-medium">
                          Key data is extracted automatically
                        </p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Supports PDF, Word, ODT, HTML, Markdown, CSV, Excel, and
                        text files (up to 25MB each)
                      </p>
                    </div>
                    <div className="grid gap-2">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <div
                          className="border-2 border-dashed border-emerald-200 dark:border-emerald-800 rounded-lg p-6 flex flex-col items-center justify-center gap-2 bg-emerald-50/50 dark:bg-emerald-950/20 hover:bg-emerald-50 dark:hover:bg-emerald-950/30 transition-colors"
                          onDrop={handleDrop}
                          onDragOver={handleDragOver}
                        >
                          <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-2">
                            <Upload className="h-8 w-8 text-emerald-500 dark:text-emerald-400" />
                          </div>
                          <p className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                            Click to upload or drag and drop
                          </p>
                          <p className="text-xs text-emerald-600/70 dark:text-emerald-400/70">
                            Upload multiple files for batch processing
                          </p>
                          <p className="text-xs text-emerald-600/70 dark:text-emerald-400/70 mt-1">
                            PDF, DOCX, DOC, TXT, RTF, ODT, HTML, MD (up to 20
                            files, 25MB each)
                          </p>
                        </div>
                        <input
                          id="file-upload"
                          type="file"
                          className="hidden"
                          accept=".pdf,.docx,.doc,.txt,.rtf,.odt,.html,.htm,.md,.csv,.xlsx,.xls"
                          onChange={handleFileChange}
                          multiple
                        />
                      </label>

                      {selectedFiles.length > 0 && (
                        <div className="mt-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-sm font-medium">
                              Selected Files ({selectedFiles.length})
                            </h4>
                            {selectedFiles.length > 1 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedFiles([])}
                                disabled={importing}
                                className="h-8 text-xs"
                              >
                                Clear All
                              </Button>
                            )}
                          </div>
                          <div className="max-h-64 overflow-y-auto pr-2 border rounded-md bg-muted/20">
                            <div className="p-2 space-y-2">
                              {selectedFiles.map((file, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between gap-3 p-3 bg-background rounded-md border hover:bg-muted/50 transition-colors"
                                >
                                  <div className="flex items-center gap-3 overflow-hidden min-w-0 flex-1">
                                    {file.type.includes("pdf") ? (
                                      <FileText className="h-4 w-4 flex-shrink-0 text-red-500" />
                                    ) : file.type.includes("word") ||
                                      file.name.endsWith(".docx") ||
                                      file.name.endsWith(".doc") ? (
                                      <FileText className="h-4 w-4 flex-shrink-0 text-blue-500" />
                                    ) : file.type.includes("sheet") ||
                                      file.name.endsWith(".xlsx") ||
                                      file.name.endsWith(".xls") ||
                                      file.name.endsWith(".csv") ? (
                                      <FileText className="h-4 w-4 flex-shrink-0 text-green-500" />
                                    ) : (
                                      <FileText className="h-4 w-4 flex-shrink-0 text-gray-500" />
                                    )}
                                    <div className="flex flex-col overflow-hidden min-w-0 flex-1">
                                      <span
                                        className="text-sm font-medium truncate"
                                        title={file.name}
                                      >
                                        {file.name}
                                      </span>
                                      <span className="text-xs text-muted-foreground">
                                        {(file.size / 1024 / 1024).toFixed(2)}{" "}
                                        MB
                                      </span>
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7 flex-shrink-0 hover:bg-destructive/10 hover:text-destructive"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      removeFile(index);
                                    }}
                                    disabled={importing}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* File summary */}
                          <div className="mt-3 space-y-2">
                            <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                              <span>Total files: {selectedFiles.length}</span>
                              <span>
                                Total size:{" "}
                                {(
                                  selectedFiles.reduce(
                                    (acc, file) => acc + file.size,
                                    0
                                  ) /
                                  1024 /
                                  1024
                                ).toFixed(2)}{" "}
                                MB
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {(() => {
                                const fileTypes = selectedFiles.reduce(
                                  (acc, file) => {
                                    const ext =
                                      file.name
                                        .split(".")
                                        .pop()
                                        ?.toLowerCase() || "";
                                    acc[ext] = (acc[ext] || 0) + 1;
                                    return acc;
                                  },
                                  {} as Record<string, number>
                                );

                                return Object.entries(fileTypes).map(
                                  ([ext, count]) => (
                                    <Badge
                                      key={ext}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {ext.toUpperCase()}: {count}
                                    </Badge>
                                  )
                                );
                              })()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setImportDialogOpen(false);
                      setSelectedFiles([]);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    onClick={handleImport}
                    disabled={selectedFiles.length === 0 || importing}
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    {importing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <FileText className="mr-2 h-4 w-4" />
                        Import
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Main Content Area - Responsive */}
      <div className="flex-1 min-h-0 px-4 sm:px-6 lg:px-8 w-full max-w-full overflow-hidden">
        <div className="h-full flex flex-col w-full max-w-full">
          {/* Filters Section */}
          <div className="flex-shrink-0 space-y-4 mb-6 w-full max-w-full">
            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 w-full max-w-full">
              <div className="flex items-center gap-2">
                <h2 className="text-lg font-semibold text-foreground">
                  Contracts ({total})
                </h2>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                <div className="relative flex-1 lg:flex-initial">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search contracts..."
                    className="w-full lg:w-[300px] pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <Button
                  variant="outline"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className={cn(
                    "whitespace-nowrap",
                    (filterType && filterType !== "all") ||
                      filterCounterparty ||
                      filterStartDateFrom ||
                      filterStartDateTo ||
                      filterValueMin ||
                      filterValueMax
                      ? "border-emerald-500 text-emerald-600 bg-emerald-50 dark:bg-emerald-950/20"
                      : ""
                  )}
                >
                  <SlidersHorizontal className="mr-2 h-4 w-4" />
                  Filters
                  {((filterType && filterType !== "all") ||
                    filterCounterparty ||
                    filterStartDateFrom ||
                    filterStartDateTo ||
                    filterValueMin ||
                    filterValueMax) && (
                      <span className="ml-2 bg-emerald-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
                        {
                          [
                            filterType && filterType !== "all"
                              ? filterType
                              : null,
                            filterCounterparty,
                            filterStartDateFrom,
                            filterStartDateTo,
                            filterValueMin,
                            filterValueMax,
                          ].filter(Boolean).length
                        }
                      </span>
                    )}
                </Button>
              </div>
            </div>

            {/* Bulk Selection Actions */}
            {selectedContracts.size > 0 && (
              <div className="flex items-center justify-between p-3 bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                    {selectedContracts.size} contract
                    {selectedContracts.size !== 1 ? "s" : ""} selected
                    {total > rowsPerPage && (
                      <span className="text-xs text-emerald-600 dark:text-emerald-400 ml-1">
                        (across all pages)
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedContracts(new Map())}
                    className="text-emerald-700 border-emerald-300 hover:bg-emerald-100 dark:text-emerald-300 dark:border-emerald-700 dark:hover:bg-emerald-900/30"
                  >
                    Clear Selection
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="text-white bg-red-600 hover:bg-red-700 border-red-600"
                  >
                    Delete Selected
                  </Button>
                  {/* Note: Group Contracts button removed since manual grouping is disabled */}
                </div>
              </div>
            )}

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50 dark:bg-muted/20">
                <div className="space-y-2">
                  <Label htmlFor="filter-type" className="text-sm font-medium">
                    Agreement Type
                  </Label>
                  <Select
                    value={filterType}
                    onValueChange={(value) =>
                      setFilterType(value as AgreementType | "all")
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value={AgreementType.MSA}>MSA</SelectItem>
                      <SelectItem value={AgreementType.NDA}>NDA</SelectItem>
                      <SelectItem value={AgreementType.SOW}>SOW</SelectItem>
                      <SelectItem value={AgreementType.PO}>PO</SelectItem>
                      <SelectItem value={AgreementType.SLA}>SLA</SelectItem>
                      <SelectItem value={AgreementType.DPA}>DPA</SelectItem>
                      <SelectItem value={AgreementType.BAA}>BAA</SelectItem>
                      <SelectItem value={AgreementType.EULA}>EULA</SelectItem>
                      <SelectItem value={AgreementType.OTHER}>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="filter-counterparty"
                    className="text-sm font-medium"
                  >
                    Counterparty
                  </Label>
                  <Input
                    id="filter-counterparty"
                    placeholder="Filter by counterparty..."
                    value={filterCounterparty}
                    onChange={(e) => setFilterCounterparty(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Start Date Range
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      type="date"
                      placeholder="From"
                      value={filterStartDateFrom}
                      onChange={(e) => setFilterStartDateFrom(e.target.value)}
                      className="text-xs"
                    />
                    <Input
                      type="date"
                      placeholder="To"
                      value={filterStartDateTo}
                      onChange={(e) => setFilterStartDateTo(e.target.value)}
                      className="text-xs"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Value Range</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min value"
                      value={filterValueMin}
                      onChange={(e) => setFilterValueMin(e.target.value)}
                    />
                    <Input
                      type="number"
                      placeholder="Max value"
                      value={filterValueMax}
                      onChange={(e) => setFilterValueMax(e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:col-span-2 lg:col-span-4 sm:items-center sm:justify-between">
                  <div className="text-sm text-muted-foreground">
                    Filters are applied automatically as you type
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFilterType("all");
                      setFilterCounterparty("");
                      setFilterStartDateFrom("");
                      setFilterStartDateTo("");
                      setFilterValueMin("");
                      setFilterValueMax("");
                      setSearchTerm("");
                      setFilterStatus("");
                      setPage(0);
                      fetchExtractions();
                    }}
                    className="w-full sm:w-auto"
                  >
                    <FilterX className="mr-2 h-4 w-4" />
                    Clear All
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Table Container - Responsive */}
          <div className="flex-1 min-h-0 w-full max-w-full">
            <Card className="h-full flex flex-col w-full max-w-full overflow-hidden">
              <CardHeader className="px-4 sm:px-6 py-4 flex-shrink-0 border-b">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <CardTitle className="text-lg font-light text-foreground">
                    Contract Repository
                  </CardTitle>
                  <div className="flex gap-2 w-full sm:w-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 sm:flex-none"
                      onClick={handleExportRepository}
                      disabled={
                        loading || exporting || getAllContracts().length === 0
                      }
                    >
                      {exporting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Exporting...
                        </>
                      ) : (
                        <>
                          <Download className="mr-2 h-4 w-4" />
                          Export
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 sm:flex-none"
                    >
                      <ArrowUpDown className="mr-2 h-4 w-4" />
                      Sort
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="flex-1 p-0 min-h-0 w-full max-w-full overflow-hidden">
                {/* Contracts Table - Table Wrapper with Horizontal Scroll */}
                <div className="h-full w-full max-w-full overflow-x-auto overflow-y-hidden">
                  <DataTable
                    data={getContractsFromExtractions()}
                    columns={getContractColumns(
                      handleViewContract,
                      handleDeleteContract
                    )}
                    loading={loading}
                    minWidth="1800px"
                    stickyHeader={true}
                    containerClassName="h-full border-0 rounded-none w-full"
                    emptyMessage="No contracts found"
                    emptyIcon={
                      <FileSearch className="h-10 w-10 text-muted-foreground" />
                    }
                    emptyAction={
                      <Button
                        onClick={() => setImportDialogOpen(true)}
                        className="bg-emerald-600 hover:bg-emerald-700 text-white"
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Import Contracts
                      </Button>
                    }
                    selectedRows={new Set(selectedContracts.keys())}
                    onRowSelect={handleContractSelection}
                    selectAll={
                      getContractsFromExtractions().length > 0 &&
                      getContractsFromExtractions().every((contract) =>
                        selectedContracts.has(contract.id)
                      )
                    }
                    onSelectAll={handleSelectAllContracts}
                  />
                </div>
              </CardContent>

              <CardFooter className="flex-shrink-0 flex flex-col sm:flex-row sm:justify-between gap-4 px-4 sm:px-6 py-4 border-t bg-muted/20">
                <div className="text-sm text-muted-foreground text-center sm:text-left">
                  {total > rowsPerPage ? (
                    <>
                      Showing <strong>{getAllContracts().length}</strong> of{" "}
                      <strong>{total}</strong> contracts on page{" "}
                      <strong>{page + 1}</strong>
                    </>
                  ) : (
                    <>
                      Showing <strong>{total}</strong> contract
                      {total !== 1 ? "s" : ""}
                    </>
                  )}
                </div>
                {total > rowsPerPage && (
                  <div className="flex gap-2 justify-center sm:justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={page === 0 || loading}
                      onClick={() => handleChangePage(null, page - 1)}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-3 text-sm text-muted-foreground">
                      Page {page + 1} of {Math.ceil(total / rowsPerPage)}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={
                        page >= Math.ceil(total / rowsPerPage) - 1 || loading
                      }
                      onClick={() => handleChangePage(null, page + 1)}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardFooter>
            </Card>
          </div>

          {/* Statistics Cards - Responsive */}
          {(repositoryStats && repositoryStats.totalContracts > 0) ||
            statsLoading ? (
            <div className="flex-shrink-0 mt-4">
              <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <Card className="hover:shadow-sm transition-shadow border-emerald-200/50 dark:border-emerald-800/50">
                  <CardHeader className="pb-3 pt-4 px-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-light text-foreground">
                        Active Contracts
                      </CardTitle>
                      <CheckCircle className="h-4 w-4 text-emerald-500" />
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 pb-4">
                    <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                      {statsLoading
                        ? "..."
                        : repositoryStats?.activeContracts || 0}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Currently active
                    </p>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-sm transition-shadow border-blue-200/50 dark:border-blue-800/50">
                  <CardHeader className="pb-3 pt-4 px-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-light text-foreground">
                        Agreement Types
                      </CardTitle>
                      <Tag className="h-4 w-4 text-blue-500" />
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 pb-4">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {statsLoading
                        ? "..."
                        : repositoryStats?.agreementTypesCount || 0}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Different types
                    </p>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-sm transition-shadow border-red-200/50 dark:border-red-800/50">
                  <CardHeader className="pb-3 pt-4 px-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-light text-foreground">
                        Inactive Contracts
                      </CardTitle>
                      <Clock className="h-4 w-4 text-red-500" />
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 pb-4">
                    <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                      {statsLoading
                        ? "..."
                        : repositoryStats?.inactiveContracts || 0}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Expired or pending
                    </p>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-sm transition-shadow border-purple-200/50 dark:border-purple-800/50">
                  <CardHeader className="pb-3 pt-4 px-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-light text-foreground">
                        Recently Added
                      </CardTitle>
                      <Calendar className="h-4 w-4 text-purple-500" />
                    </div>
                  </CardHeader>
                  <CardContent className="px-4 pb-4">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {statsLoading
                        ? "..."
                        : repositoryStats?.recentlyAddedCount || 0}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Last 30 days
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onConfirm={confirmDeleteContract}
        title="Delete Contract"
        description={`Are you sure you want to delete "${contractToDelete?.title}"? This action cannot be undone and will permanently remove the contract and all associated data.`}
        itemName={contractToDelete?.title}
        isLoading={isDeleting}
      />

      {/* Bulk Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={bulkDeleteModalOpen}
        onOpenChange={setBulkDeleteModalOpen}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Contracts"
        description={`Are you sure you want to delete ${selectedContracts.size
          } contract${selectedContracts.size !== 1 ? "s" : ""
          }? This action cannot be undone and will permanently remove all selected contracts and their associated data.`}
        itemName={`${selectedContracts.size} contract${selectedContracts.size !== 1 ? "s" : ""
          }`}
        isLoading={isBulkDeleting}
      />

      {/* Note: Group Contracts Modal removed since manual grouping is disabled */}

      {/* Note: Folder management modals removed since folders are now virtual */}

      {/* Note: Remove from folder modal removed since folders are now virtual */}

      {/* Note: Auto-Grouping Confirmation Modal removed since smart grouping is disabled */}

      {/* Entitlement Analysis Modal */}
      <EntitlementAnalysisModal
        open={entitlementAnalysisModalOpen}
        onOpenChange={setEntitlementAnalysisModalOpen}
      />
    </div>
  );
}
