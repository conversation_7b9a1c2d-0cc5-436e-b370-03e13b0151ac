"use client";

import { useParams, useRouter } from "next/navigation";
import { ContractAnalysisView } from "@/components/contracts/ContractAnalysisView";
import { toast } from "sonner";

export default function ContractAnalysisPage() {
  const params = useParams();
  const router = useRouter();
  const contractId = params.id as string;

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <ContractAnalysisView contractId={contractId} className="h-screen" />
    </div>
  );
}
