"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

export default function ChangesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Change Management
        </h1>
        <p className="text-muted-foreground">
          Manage contract changes and amendments
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Change Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Contract change management tools.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
