"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card";

export default function SupplierPerformancePage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
          Supplier Performance Dashboard
        </h1>
        <p className="text-muted-foreground">
          Monitor and evaluate supplier performance metrics
        </p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Coming soon - Supplier performance tracking and analytics.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
