/**
 * Test page for Contract Hierarchy Timeline functionality
 * Demonstrates the hierarchy visualization with sample data
 */

"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ContractHierarchyTimeline } from "@/components/contracts/ContractHierarchyTimeline";
import { contractHierarchyService, ContractWithHierarchy } from "@/services/contractHierarchyService";
import { GitBranch, TestTube, Loader2 } from "lucide-react";

export default function TestHierarchyPage() {
  const [hierarchyTypes, setHierarchyTypes] = useState<any>(null);
  const [sampleContracts, setSampleContracts] = useState<ContractWithHierarchy[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Sample contract data for demonstration
  const generateSampleContracts = (): ContractWithHierarchy[] => {
    return [
      {
        id: "msa-001",
        title: "Master Services Agreement - TechCorp",
        agreementType: "MSA",
        startDate: "2023-01-15",
        endDate: "2026-01-15",
        value: "USD:5000000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 1,
        parentContracts: [],
        childContracts: ["sow-001", "sow-002"],
        groupId: "TechCorp:MSA-2023-001",
      },
      {
        id: "sow-001",
        title: "SOW - Cloud Infrastructure Services",
        agreementType: "SOW",
        startDate: "2023-03-01",
        endDate: "2024-03-01",
        value: "USD:1200000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 4,
        parentContracts: ["msa-001"],
        childContracts: ["po-001", "po-002"],
        groupId: "TechCorp:MSA-2023-001",
      },
      {
        id: "sow-002",
        title: "SOW - Application Development",
        agreementType: "SOW",
        startDate: "2023-06-01",
        endDate: "2024-12-01",
        value: "USD:800000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 4,
        parentContracts: ["msa-001"],
        childContracts: ["po-003"],
        groupId: "TechCorp:MSA-2023-001",
      },
      {
        id: "po-001",
        title: "PO - Q1 Cloud Services",
        agreementType: "PO",
        startDate: "2023-03-01",
        endDate: "2023-06-01",
        value: "USD:300000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 6,
        parentContracts: ["sow-001"],
        childContracts: [],
        groupId: "TechCorp:MSA-2023-001",
      },
      {
        id: "po-002",
        title: "PO - Q2 Cloud Services",
        agreementType: "PO",
        startDate: "2023-06-01",
        endDate: "2023-09-01",
        value: "USD:300000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 6,
        parentContracts: ["sow-001"],
        childContracts: [],
        groupId: "TechCorp:MSA-2023-001",
      },
      {
        id: "po-003",
        title: "PO - Development Sprint 1",
        agreementType: "PO",
        startDate: "2023-07-01",
        endDate: "2023-10-01",
        value: "USD:200000",
        provider: "TechCorp Solutions",
        hierarchyLevel: 6,
        parentContracts: ["sow-002"],
        childContracts: [],
        groupId: "TechCorp:MSA-2023-001",
      },
    ];
  };

  const loadHierarchyTypes = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const types = await contractHierarchyService.getAllHierarchyTypes();
      setHierarchyTypes(types);
      
      // Generate sample contracts for demonstration
      setSampleContracts(generateSampleContracts());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load hierarchy types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHierarchyTypes();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
            <TestTube className="h-8 w-8" style={{ color: "#09260D" }} />
            Contract Hierarchy Test
          </h1>
          <p className="text-muted-foreground mt-2">
            Testing the contract hierarchy timeline visualization functionality
          </p>
        </div>
        <Button onClick={loadHierarchyTypes} disabled={loading}>
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <GitBranch className="h-4 w-4 mr-2" />
          )}
          Reload Data
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive">
              <strong>Error:</strong> {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hierarchy Types Info */}
      {hierarchyTypes && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Supported Hierarchy Types
              <Badge variant="outline">
                {hierarchyTypes.totalTypes} types
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(hierarchyTypes.hierarchyTypes).map(([type, info]: [string, any]) => (
                <div key={type} className="p-3 border rounded-lg">
                  <div className="font-medium text-sm">{type}</div>
                  <div className="text-xs text-muted-foreground">
                    Level {info.level} • {info.description}
                  </div>
                  {info.parentTypes.length > 0 && (
                    <div className="text-xs text-muted-foreground mt-1">
                      Parents: {info.parentTypes.join(', ')}
                    </div>
                  )}
                  {info.childTypes.length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      Children: {info.childTypes.join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sample Hierarchy Timeline */}
      {sampleContracts.length > 0 && (
        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              Sample Contract Hierarchy Timeline
            </h2>
            <p className="text-muted-foreground">
              Demonstrating MSA → SOW → PO relationships with sample data
            </p>
          </div>
          
          <ContractHierarchyTimeline
            contracts={sampleContracts}
            groupId="TechCorp:MSA-2023-001"
            showConnectors={true}
            className="w-full"
          />
        </div>
      )}

      {/* Contract Details */}
      {sampleContracts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sample Contract Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sampleContracts.map((contract) => (
                <div key={contract.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium">{contract.title}</div>
                    <Badge variant="outline">
                      Level {contract.hierarchyLevel}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Type:</span> {contract.agreementType}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Value:</span> {contract.value}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Start:</span> {contract.startDate}
                    </div>
                    <div>
                      <span className="text-muted-foreground">End:</span> {contract.endDate}
                    </div>
                  </div>
                  {contract.parentContracts && contract.parentContracts.length > 0 && (
                    <div className="mt-2 text-sm">
                      <span className="text-muted-foreground">Parents:</span> {contract.parentContracts.join(', ')}
                    </div>
                  )}
                  {contract.childContracts && contract.childContracts.length > 0 && (
                    <div className="mt-1 text-sm">
                      <span className="text-muted-foreground">Children:</span> {contract.childContracts.join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading hierarchy data...</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
