"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DashboardLayoutService } from '@/services/dashboardLayoutService';

export default function TestDashboardAPI() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testGetLayout = async () => {
    setLoading(true);
    setResult('Testing GET layout...');
    try {
      const layout = await DashboardLayoutService.getLayout();
      setResult(`SUCCESS: ${JSON.stringify(layout, null, 2)}`);
    } catch (error) {
      setResult(`ERROR: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const testSaveLayout = async () => {
    setLoading(true);
    setResult('Testing SAVE layout...');
    try {
      const testLayout = {
        name: 'Test Layout',
        description: 'Test layout description',
        widgets: [
          {
            id: 'test-widget-1',
            type: 'summary-kpi',
            title: 'Test Widget',
            description: 'Test widget description',
            position: { x: 0, y: 0, w: 6, h: 4 },
            config: {},
            visible: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ],
        isDefault: false,
      };
      
      const savedLayout = await DashboardLayoutService.saveLayout(testLayout);
      setResult(`SUCCESS: ${JSON.stringify(savedLayout, null, 2)}`);
    } catch (error) {
      setResult(`ERROR: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Dashboard API Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testGetLayout} disabled={loading}>
              Test GET Layout
            </Button>
            <Button onClick={testSaveLayout} disabled={loading}>
              Test SAVE Layout
            </Button>
          </div>
          
          {result && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Result:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {result}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
