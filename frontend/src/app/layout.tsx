import { Metadata } from "next";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { AppProviders } from "@/providers/AppProviders";

// Import Inter as fallback for Aptos Light
import { Inter } from "next/font/google";

// Configure Inter as fallback for Aptos Light with light weight emphasis
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "MAIT - Contract Lifecycle Management",
    template: "%s | MAIT",
  },
  description: "AI-powered contract management platform",
  icons: {
    icon: "/maitlogo.svg",
    shortcut: "/maitlogo.svg",
    apple: "/maitlogo.svg",
  },
};

// Root layout component
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable} suppressHydrationWarning>
      <head>
        {/* Preload Aptos Light font for better performance */}
        <link
          rel="preload"
          href="/aptos-light.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="min-h-screen bg-white text-custom-green font-light">
        <ErrorBoundary>
          <AppProviders>
            {children}
            <Toaster />
          </AppProviders>
        </ErrorBoundary>
      </body>
    </html>
  );
}
