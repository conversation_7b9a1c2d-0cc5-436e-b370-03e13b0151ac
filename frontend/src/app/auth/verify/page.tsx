"use client";

import * as React from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { CheckCircle2, Mail } from "lucide-react";
import { Suspense } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { apiClient } from "@/lib/api-client";

function VerifyForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isVerified, setIsVerified] = React.useState(false);
  const [email, setEmail] = React.useState<string>("");

  // Get email and token from URL if available
  const token = searchParams.get("token");
  const emailParam = searchParams.get("email");

  React.useEffect(() => {
    if (emailParam) {
      setEmail(emailParam);
    }

    // If token is provided, verify it automatically
    if (token && emailParam) {
      verifyEmail(token, emailParam);
    }
  }, [token, emailParam]);

  async function verifyEmail(verificationToken: string, userEmail: string) {
    setIsLoading(true);

    try {
      // Call the verification API
      await apiClient.post("/api/auth/verify-email", {
        token: verificationToken,
        email: userEmail,
      });

      setIsVerified(true);
      toast.success("Email verified successfully");
    } catch (error) {
      console.error("Verification error:", error);
      toast.error(
        "Failed to verify email. The link may be expired or invalid."
      );
    } finally {
      setIsLoading(false);
    }
  }

  async function resendVerification() {
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    setIsLoading(true);

    try {
      // Call the resend verification API
      await apiClient.post("/api/auth/resend-verification", {
        email,
      });

      toast.success("Verification email sent successfully");
    } catch (error) {
      console.error("Resend verification error:", error);
      toast.error("Failed to resend verification email");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="w-full max-w-md">
      <Card className="border-0 shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            {isVerified ? (
              <div className="h-12 w-12 rounded-full bg-success/20 flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-success" />
              </div>
            ) : (
              <div className="h-12 w-12 rounded-full bg-primary/20 flex items-center justify-center">
                <Mail className="h-6 w-6 text-primary" />
              </div>
            )}
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            {isVerified ? "Email Verified" : "Verify Your Email"}
          </CardTitle>
          <CardDescription className="text-center">
            {isVerified
              ? "Your email has been verified successfully"
              : "Please check your email for a verification link"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isVerified ? (
            <div className="space-y-4">
              <div className="bg-muted p-4 rounded-lg text-center">
                <p className="text-sm text-muted-foreground">
                  Your account has been verified. You can now sign in to access
                  your account.
                </p>
              </div>
              <Button
                className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all"
                onClick={() => router.push("/auth/login")}
              >
                Sign In
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  We've sent a verification link to your email address. Please
                  check your inbox and click the link to verify your account.
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-center">Didn't receive the email?</p>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={resendVerification}
                  disabled={isLoading}
                >
                  {isLoading ? "Sending..." : "Resend Verification Email"}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-sm">
            <Link
              href="/auth/login"
              className="font-medium text-primary hover:underline"
            >
              Back to Sign In
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

export default function VerifyPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full max-w-md">
          <Card className="border-0 shadow-lg">
            <CardHeader className="space-y-1">
              <div className="flex justify-center mb-4">
                <div className="h-12 w-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Mail className="h-6 w-6 text-primary" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-center">
                Verify Your Email
              </CardTitle>
              <CardDescription className="text-center">
                Loading...
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-40 flex items-center justify-center">
                <p>Loading verification form...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      }
    >
      <VerifyForm />
    </Suspense>
  );
}
