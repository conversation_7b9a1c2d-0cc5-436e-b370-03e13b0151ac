"use client";

import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="flex items-center justify-between p-4 md:p-6">
        <Link href="/" className="flex items-center space-x-2">
          <img src="/maitlogo.svg" alt="MAIT Logo" className="h-12 w-auto" />
        </Link>
        <ThemeToggle />
      </header>
      <main className="flex-1 flex items-center justify-center p-4 md:p-8">
        {children}
      </main>
      <footer className="py-6 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} MAIT. All rights reserved.</p>
      </footer>
    </div>
  );
}
