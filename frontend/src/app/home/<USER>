"use client";

import Link from "next/link";
import {
  ArrowRight,
  Users,
  Search,
  Database,
  BarChart3,
  MessageSquare,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";

export default function HomePage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 md:py-32 bg-gradient-to-br from-background via-background to-primary/10">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-0 right-0 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl transform translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl transform -translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/2 w-1/5 h-1/5 bg-accent/10 rounded-full blur-2xl transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
            <div className="flex flex-col justify-center space-y-4">
              {/* <Badge className="w-fit mb-2 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
                AI-Powered B2B2B Platform
              </Badge> */}
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                  Smarter sourcing. Contract intelligence. Optimal renewals.
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  Live insights, all in one platform.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Link href="/auth/register">
                  <Button
                    size="lg"
                    className="w-full min-[400px]:w-auto bg-primary hover:bg-primary/90 transition-all"
                  >
                    Start Free Trial
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full min-[400px]:w-auto border-primary/20 hover:border-primary/40 transition-all"
                  >
                    Schedule a Demo
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative w-full h-full min-h-[350px] bg-gradient-to-br from-primary/15 to-secondary/15 rounded-2xl overflow-hidden shadow-lg">
                <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-3/4 h-3/4 bg-card/90 backdrop-blur-sm rounded-lg shadow-lg p-6 flex flex-col justify-between">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="h-3 w-3 rounded-full bg-primary"></div>
                          <span className="text-sm font-medium">
                            Contract #A-12345
                          </span>
                        </div>
                        <Badge className="bg-success/10 text-success hover:bg-success/20">
                          Active
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="h-2 w-3/4 bg-muted rounded"></div>
                        <div className="h-2 w-1/2 bg-muted rounded"></div>
                        <div className="h-2 w-5/6 bg-muted rounded"></div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">
                          Renewal Date
                        </span>
                        <span className="font-medium">Dec 15, 2023</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">Status</span>
                        <span className="font-medium text-primary">
                          AI-Reviewed
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">
                          Compliance
                        </span>
                        <span className="font-medium text-success">100%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 py-20 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/4 right-1/4 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              {/* <div className="inline-block rounded-lg bg-secondary/10 text-secondary px-3 py-1 text-sm font-medium">
                Key Features
              </div> */}
              <h1 className="font-bold">
                Key Features
              </h1>
              {/* <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Our platform provides a comprehensive set of tools to manage
                your contracts and licenses throughout their lifecycle.
              </p> */}
            </div>
          </div>
          <div className="mx-auto flex max-w-7xl gap-6 py-12 overflow-x-auto">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Users className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">
                  Persona-Based Intelligence
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Insights and actionable intelligence customised specifically
                  to your role, providing procurement leaders, finance
                  directors, IT managers, and other stakeholders with clear,
                  relevant, and impactful information.
                </p>
              </CardContent>
              {/* <CardFooter>
                <Link
                  href="/contract-concierge/personas"
                  className="text-primary dark:text-white hover:text-primary/80 dark:hover:text-white/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter> */}
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Search className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">
                  Agreement Discovery
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Upload your contract and MAIT identifies, analyses, extracts,
                  and benchmarks, surfacing critical clauses, uncovering hidden
                  risks, and highlighting optimisation opportunities.
                </p>
              </CardContent>
              {/* <CardFooter>
                <Link
                  href="/contract-concierge/discovery"
                  className="text-primary dark:text-white hover:text-primary/80 dark:hover:text-white/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter> */}
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <Database className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">
                  Live Repository
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  A centralized database of your contracts and key data,
                  providing real-time access, analysis, and benchmarking
                  capabilities to keep you informed and proactive.
                </p>
              </CardContent>
              {/* <CardFooter>
                <Link
                  href="/contract-management"
                  className="text-primary dark:text-white hover:text-primary/80 dark:hover:text-white/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter> */}
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <BarChart3 className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">
                  Always-On Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Real-time visibility through continuous monitoring and
                  analytics, enabling you to track risks, compliance,
                  performance, and financial insights instantly, at any time.
                </p>
              </CardContent>
              {/* <CardFooter>
                <Link
                  href="/cockpit"
                  className="text-primary dark:text-white hover:text-primary/80 dark:hover:text-white/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter> */}
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-card min-w-[280px]">
              <CardHeader className="pb-2">
                <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 dark:bg-white/20 border border-primary/20">
                  <MessageSquare className="h-6 w-6 text-primary dark:text-white" />
                </div>
                <CardTitle className="text-foreground">
                  Contract Assistant
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Your personal contract management assistant that helps you
                  understand agreements, identify risks, and make informed
                  decisions about your contracts and licenses.
                </p>
              </CardContent>
              {/* <CardFooter>
                <Link
                  href="/contract-concierge/discovery"
                  className="text-primary dark:text-white hover:text-primary/80 dark:hover:text-white/80 inline-flex items-center font-medium transition-colors"
                >
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardFooter> */}
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden bg-gradient-to-br from-primary/10 via-background to-secondary/10">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-1/3 right-1/4 w-1/4 h-1/4 bg-primary/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-1/3 left-1/4 w-1/4 h-1/4 bg-secondary/10 rounded-full blur-2xl"></div>
        </div>

        <div className="container px-4 md:px-6 relative z-10">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="space-y-3 flex flex-col items-center justify-center space-y-2">
              <h1 className="font-bold">
                Ready to Unify Your Contract & License Management?
              </h1>
              <p className="max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Start your 14-day free trial today and experience the power of
                our unified platform with enterprise and platform administration
                capabilities.
              </p>
            </div>
            <div className="flex flex-col gap-3 min-[400px]:flex-row mt-4">
              <Link href="/auth/register">
                <Button
                  size="lg"
                  className="w-full min-[400px]:w-auto bg-primary hover:bg-primary/90 transition-all"
                >
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full min-[400px]:w-auto border-primary/20 hover:border-primary/40 transition-all"
                >
                  Schedule a Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
