/**
 * Widget Type Registry
 * Maps widget types to their React components and metadata
 */

import { WidgetType } from '@/types/dashboard';

export interface WidgetTypeInfo {
  type: WidgetType;
  name: string;
  description: string;
  category: 'priority' | 'portfolio';
  component: string;
  defaultSize: {
    w: number;
    h: number;
  };
  configurable: {
    chartType?: boolean;
    colors?: boolean;
    refreshInterval?: boolean;
    showLegend?: boolean;
    format?: boolean;
    pageSize?: boolean;
  };
}

export const WIDGET_TYPE_REGISTRY: Record<WidgetType, WidgetTypeInfo> = {
  'expiring-contracts': {
    type: 'expiring-contracts',
    name: 'Expiring Contracts',
    description: 'Contracts expiring in the next 30-90 days',
    category: 'priority',
    component: 'ExpiringContractsWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'auto-renewals': {
    type: 'auto-renewals',
    name: 'Auto Renewals',
    description: 'Contracts with automatic renewal clauses',
    category: 'priority',
    component: 'AutoRenewalsWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'auto-renewals-classification': {
    type: 'auto-renewals-classification',
    name: 'Auto Renewals by Classification',
    description: 'Auto renewals broken down by contract classification',
    category: 'priority',
    component: 'AutoRenewalsByClassificationWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'realised-savings': {
    type: 'realised-savings',
    name: 'Realised Savings',
    description: 'Savings already achieved through contract optimization',
    category: 'priority',
    component: 'RealisedSavingsWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      refreshInterval: true,
      format: true
    }
  },
  'potential-savings': {
    type: 'potential-savings',
    name: 'Potential Savings',
    description: 'Potential savings opportunities identified',
    category: 'priority',
    component: 'PotentialSavingsWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      refreshInterval: true,
      format: true
    }
  },
  'key-obligations': {
    type: 'key-obligations',
    name: 'Key Obligations',
    description: 'Important contractual obligations requiring attention',
    category: 'priority',
    component: 'KeyObligationsWidget',
    defaultSize: { w: 4, h: 3 },
    configurable: {
      refreshInterval: true,
      pageSize: true
    }
  },
  'total-agreements': {
    type: 'total-agreements',
    name: 'Total Agreements',
    description: 'Overview of all agreements in the portfolio',
    category: 'portfolio',
    component: 'TotalAgreementsWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'high-value-agreements': {
    type: 'high-value-agreements',
    name: 'High Value Agreements',
    description: 'Agreements categorized by contract value',
    category: 'portfolio',
    component: 'HighValueAgreementsWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'critical-agreements': {
    type: 'critical-agreements',
    name: 'Critical Agreements',
    description: 'Agreements categorized by business criticality',
    category: 'portfolio',
    component: 'CriticalAgreementsWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'expired-agreements': {
    type: 'expired-agreements',
    name: 'Expired Agreements',
    description: 'Agreements that have already expired',
    category: 'portfolio',
    component: 'ExpiredAgreementsWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      refreshInterval: true,
      format: true
    }
  },
  'aging-contracts': {
    type: 'aging-contracts',
    name: 'Aging Contracts',
    description: 'Contracts categorized by age since execution',
    category: 'portfolio',
    component: 'AgingContractsWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'customer-supplier-paper': {
    type: 'customer-supplier-paper',
    name: 'Customer Supplier Paper',
    description: 'Distribution of customer vs supplier agreements',
    category: 'portfolio',
    component: 'CustomerSupplierPaperWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'agreement-types': {
    type: 'agreement-types',
    name: 'Agreement Types',
    description: 'Distribution of different agreement types',
    category: 'portfolio',
    component: 'PortfolioAgreementTypeWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'service-types': {
    type: 'service-types',
    name: 'Service Types',
    description: 'Distribution of different service types',
    category: 'portfolio',
    component: 'ServiceTypeWidget',
    defaultSize: { w: 3, h: 3 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'summary-kpi': {
    type: 'summary-kpi',
    name: 'Contract Summary',
    description: 'Key metrics overview',
    category: 'priority',
    component: 'SummaryWidgets',
    defaultSize: { w: 12, h: 2 },
    configurable: {
      refreshInterval: true
    }
  },
  'spend-by-provider': {
    type: 'spend-by-provider',
    name: 'Spend by Provider',
    description: 'Contract value distribution by provider',
    category: 'portfolio',
    component: 'SpendByProviderWidget',
    defaultSize: { w: 4, h: 5 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  },
  'confidence-distribution': {
    type: 'confidence-distribution',
    name: 'AI Confidence Distribution',
    description: 'Extraction confidence levels',
    category: 'portfolio',
    component: 'ConfidenceDistributionWidget',
    defaultSize: { w: 8, h: 4 },
    configurable: {
      chartType: true,
      refreshInterval: true
    }
  },
  'confidence': {
    type: 'confidence',
    name: 'Average Confidence',
    description: 'Overall AI extraction confidence',
    category: 'portfolio',
    component: 'ConfidenceWidget',
    defaultSize: { w: 4, h: 4 },
    configurable: {
      refreshInterval: true
    }
  },
  'top-contracts': {
    type: 'top-contracts',
    name: 'Top Contracts',
    description: 'Highest value contracts',
    category: 'portfolio',
    component: 'TopContractsWidget',
    defaultSize: { w: 12, h: 6 },
    configurable: {
      pageSize: true,
      refreshInterval: true
    }
  },
  'renewal-timeline': {
    type: 'renewal-timeline',
    name: 'Renewal Timeline',
    description: 'Upcoming renewals',
    category: 'priority',
    component: 'RenewalTimelineWidget',
    defaultSize: { w: 4, h: 4 },
    configurable: {
      refreshInterval: true
    }
  },
  'contract-status': {
    type: 'contract-status',
    name: 'Contract Status Overview',
    description: 'Current status distribution',
    category: 'portfolio',
    component: 'ContractStatusWidget',
    defaultSize: { w: 4, h: 5 },
    configurable: {
      chartType: true,
      refreshInterval: true,
      showLegend: true
    }
  }
};

/**
 * Get widget info by type
 */
export function getWidgetInfo(type: WidgetType): WidgetTypeInfo | undefined {
  return WIDGET_TYPE_REGISTRY[type];
}

/**
 * Get widgets by category
 */
export function getWidgetsByCategory(category: 'priority' | 'portfolio'): WidgetTypeInfo[] {
  return Object.values(WIDGET_TYPE_REGISTRY).filter(widget => widget.category === category);
}

/**
 * Get all widget types
 */
export function getAllWidgetTypes(): WidgetType[] {
  return Object.keys(WIDGET_TYPE_REGISTRY) as WidgetType[];
}

/**
 * Check if widget type exists
 */
export function isValidWidgetType(type: string): type is WidgetType {
  return type in WIDGET_TYPE_REGISTRY;
}
