/**
 * Dashboard Context
 * Manages dashboard state, layouts, and widget configurations
 */

"use client";

import React, { createContext, useContext, useReducer, useEffect } from "react";
import {
  DashboardContextType,
  DashboardLayout,
  DashboardWidget,
  WidgetTemplate,
} from "@/types/dashboard";
import { WIDGET_TEMPLATES } from "@/data/widgetTemplates";
import { DashboardLayoutService } from "@/services/dashboardLayoutService";
import { toast } from "sonner";

// Dashboard state
interface DashboardState {
  currentLayout: DashboardLayout | null;
  layouts: DashboardLayout[];
  widgets: DashboardWidget[];
  selectedWidget: DashboardWidget | null;
  isEditMode: boolean;
  isDragging: boolean;
  widgetTemplates: WidgetTemplate[];
}

// Dashboard actions
type DashboardAction =
  | { type: "SET_EDIT_MODE"; payload: boolean }
  | { type: "SET_DRAGGING"; payload: boolean }
  | { type: "SET_CURRENT_LAYOUT"; payload: DashboardLayout }
  | { type: "SET_LAYOUTS"; payload: DashboardLayout[] }
  | { type: "ADD_WIDGET"; payload: DashboardWidget }
  | { type: "REMOVE_WIDGET"; payload: string }
  | {
    type: "UPDATE_WIDGET";
    payload: { id: string; updates: Partial<DashboardWidget> };
  }
  | { type: "SELECT_WIDGET"; payload: DashboardWidget | null }
  | { type: "SET_WIDGETS"; payload: DashboardWidget[] }
  | { type: "REORDER_WIDGETS"; payload: { oldIndex: number; newIndex: number } }
  | { type: "RESET_LAYOUT" };

// Initial state
const initialState: DashboardState = {
  currentLayout: null,
  layouts: [],
  widgets: [],
  selectedWidget: null,
  isEditMode: false,
  isDragging: false,
  widgetTemplates: WIDGET_TEMPLATES,
};

// Dashboard reducer
function dashboardReducer(
  state: DashboardState,
  action: DashboardAction
): DashboardState {
  switch (action.type) {
    case "SET_EDIT_MODE":
      return {
        ...state,
        isEditMode: action.payload,
        selectedWidget: action.payload ? state.selectedWidget : null,
      };

    case "SET_DRAGGING":
      return { ...state, isDragging: action.payload };

    case "SET_CURRENT_LAYOUT":
      return {
        ...state,
        currentLayout: action.payload,
        widgets: action.payload.widgets,
      };

    case "SET_LAYOUTS":
      return { ...state, layouts: action.payload };

    case "ADD_WIDGET":
      const newWidgets = [...state.widgets, action.payload];
      return {
        ...state,
        widgets: newWidgets,
        currentLayout: state.currentLayout
          ? {
            ...state.currentLayout,
            widgets: newWidgets,
            updatedAt: new Date().toISOString(),
          }
          : null,
      };

    case "REMOVE_WIDGET":
      const filteredWidgets = state.widgets.filter(
        (w) => w.id !== action.payload
      );
      return {
        ...state,
        widgets: filteredWidgets,
        selectedWidget:
          state.selectedWidget?.id === action.payload
            ? null
            : state.selectedWidget,
        currentLayout: state.currentLayout
          ? {
            ...state.currentLayout,
            widgets: filteredWidgets,
            updatedAt: new Date().toISOString(),
          }
          : null,
      };

    case "UPDATE_WIDGET":
      const updatedWidgets = state.widgets.map((widget) =>
        widget.id === action.payload.id
          ? {
            ...widget,
            ...action.payload.updates,
            updatedAt: new Date().toISOString(),
          }
          : widget
      );
      return {
        ...state,
        widgets: updatedWidgets,
        currentLayout: state.currentLayout
          ? {
            ...state.currentLayout,
            widgets: updatedWidgets,
            updatedAt: new Date().toISOString(),
          }
          : null,
      };

    case "SELECT_WIDGET":
      return { ...state, selectedWidget: action.payload };

    case "SET_WIDGETS":
      return { ...state, widgets: action.payload };

    case "REORDER_WIDGETS":
      const reorderedWidgets = [...state.widgets];
      const [movedWidget] = reorderedWidgets.splice(action.payload.oldIndex, 1);
      reorderedWidgets.splice(action.payload.newIndex, 0, movedWidget);
      return {
        ...state,
        widgets: reorderedWidgets,
        currentLayout: state.currentLayout
          ? {
            ...state.currentLayout,
            widgets: reorderedWidgets,
            updatedAt: new Date().toISOString(),
          }
          : null,
      };

    case "RESET_LAYOUT":
      return {
        ...state,
        widgets: getDefaultWidgets(),
        selectedWidget: null,
        currentLayout: state.currentLayout
          ? {
            ...state.currentLayout,
            widgets: getDefaultWidgets(),
            updatedAt: new Date().toISOString(),
          }
          : null,
      };

    default:
      return state;
  }
}

// Create context
const DashboardContext = createContext<DashboardContextType | null>(null);

// Dashboard provider component
export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Initialize with saved or default layout
  useEffect(() => {
    const loadLayout = async () => {
      try {
        console.log("Loading dashboard layout from API...");
        // Try to load saved layout from API
        const savedLayout = await DashboardLayoutService.getLayout();
        console.log("Loaded saved layout:", savedLayout);

        // Convert the API response to our format
        const layoutData = {
          id: savedLayout.id,
          name: savedLayout.name,
          description: savedLayout.description,
          widgets: savedLayout.widgets,
          isDefault: savedLayout.isDefault,
          createdAt: savedLayout.createdAt,
          updatedAt: savedLayout.updatedAt,
        };

        dispatch({ type: "SET_CURRENT_LAYOUT", payload: layoutData });
        dispatch({ type: "SET_LAYOUTS", payload: [layoutData] });

        // Check if the loaded layout has the old structure and needs reset
        const hasOldStructure = layoutData.widgets.some(
          (widget: any) =>
            (widget.widgetType === "confidence" && widget.position.y === 7) ||
            (widget.widgetType === "renewal-timeline" && widget.position.h !== 4) ||
            layoutData.widgets.length < 6
        );

        if (hasOldStructure) {
          console.log("Detected old layout structure, auto-resetting...");
          // Auto-reset to new layout
          setTimeout(() => {
            dispatch({ type: "RESET_LAYOUT" });
            // Save the new layout
            const newLayoutData = {
              name: layoutData.name,
              description: layoutData.description,
              widgets: getDefaultWidgets(),
              isDefault: layoutData.isDefault,
            };

            if (layoutData.id && layoutData.id !== "default") {
              DashboardLayoutService.updateLayout(layoutData.id, newLayoutData)
                .then(() => console.log("Auto-reset layout saved"))
                .catch((err) =>
                  console.error("Error saving auto-reset layout:", err)
                );
            }
          }, 100);
        }
      } catch (error) {
        console.error("Failed to load saved layout:", error);
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        console.log("Error details:", errorMessage);
        console.log("Using default layout");
        // Fall back to default layout
        const defaultLayout = getDefaultLayout();
        dispatch({ type: "SET_CURRENT_LAYOUT", payload: defaultLayout });
        dispatch({ type: "SET_LAYOUTS", payload: [defaultLayout] });
      }
    };

    loadLayout();
  }, []);

  // Context value
  const contextValue: DashboardContextType = {
    // State
    currentLayout: state.currentLayout,
    layouts: state.layouts,
    widgets: state.widgets,
    selectedWidget: state.selectedWidget,
    isEditMode: state.isEditMode,
    isDragging: state.isDragging,
    widgetTemplates: state.widgetTemplates,

    // Actions
    setEditMode: (enabled: boolean) => {
      dispatch({ type: "SET_EDIT_MODE", payload: enabled });
    },

    addWidget: (
      template: WidgetTemplate,
      position?: { x: number; y: number }
    ) => {
      const newWidget: DashboardWidget = {
        id: generateWidgetId(),
        userId: '', // Will be set by backend
        name: template.name,
        description: template.description,
        category: template.category,
        widgetType: template.type,
        isVisible: true,
        position: {
          x: position?.x || 0,
          y: position?.y || 0,
          w: template.defaultSize.w,
          h: template.defaultSize.h,
        },
        configuration: template.defaultConfig,
        order: 0, // Will be set by backend
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: "ADD_WIDGET", payload: newWidget });
    },

    removeWidget: (widgetId: string) => {
      dispatch({ type: "REMOVE_WIDGET", payload: widgetId });
    },

    updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => {
      dispatch({ type: "UPDATE_WIDGET", payload: { id: widgetId, updates } });
    },

    moveWidget: (
      widgetId: string,
      position: { x: number; y: number; w: number; h: number }
    ) => {
      dispatch({
        type: "UPDATE_WIDGET",
        payload: { id: widgetId, updates: { position } },
      });
    },

    selectWidget: (widget: DashboardWidget | null) => {
      dispatch({ type: "SELECT_WIDGET", payload: widget });
    },

    reorderWidgets: (oldIndex: number, newIndex: number) => {
      dispatch({ type: "REORDER_WIDGETS", payload: { oldIndex, newIndex } });
    },

    // Layout actions (simplified for now)
    createLayout: (name: string, description?: string) => {
      // TODO: Implement layout creation
      console.log("Create layout:", name, description);
    },

    deleteLayout: (layoutId: string) => {
      // TODO: Implement layout deletion
      console.log("Delete layout:", layoutId);
    },

    switchLayout: (layoutId: string) => {
      // TODO: Implement layout switching
      console.log("Switch layout:", layoutId);
    },

    saveLayout: async () => {
      if (state.currentLayout) {
        try {
          const layoutData = {
            name: state.currentLayout.name,
            description: state.currentLayout.description,
            widgets: state.widgets,
            isDefault: state.currentLayout.isDefault,
          };

          if (state.currentLayout.id && state.currentLayout.id !== "default") {
            // Update existing layout
            await DashboardLayoutService.updateLayout(
              state.currentLayout.id,
              layoutData
            );
          } else {
            // Create new layout
            const savedLayout = await DashboardLayoutService.saveLayout(
              layoutData
            );
            dispatch({ type: "SET_CURRENT_LAYOUT", payload: savedLayout });
          }

          toast.success("Dashboard layout saved successfully!");
        } catch (error) {
          console.error("Failed to save layout:", error);
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          toast.error(`Failed to save dashboard layout: ${errorMessage}`);
        }
      }
    },

    resetLayout: async () => {
      dispatch({ type: "RESET_LAYOUT" });
      // Also save the reset layout to backend
      try {
        if (state.currentLayout) {
          const layoutData = {
            name: state.currentLayout.name,
            description: state.currentLayout.description,
            widgets: getDefaultWidgets(),
            isDefault: state.currentLayout.isDefault,
          };

          if (state.currentLayout.id && state.currentLayout.id !== "default") {
            await DashboardLayoutService.updateLayout(
              state.currentLayout.id,
              layoutData
            );
          } else {
            await DashboardLayoutService.saveLayout(layoutData);
          }
          toast.success("Dashboard layout reset successfully");
        }
      } catch (error) {
        console.error("Error saving reset layout:", error);
        toast.error("Failed to save reset layout");
      }
    },
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

// Hook to use dashboard context
export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error("useDashboard must be used within a DashboardProvider");
  }
  return context;
};

// Helper functions
function generateWidgetId(): string {
  return `widget_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

function getDefaultLayout(): DashboardLayout {
  return {
    id: "default",
    name: "Default Dashboard",
    description: "Default contract analytics dashboard",
    widgets: getDefaultWidgets(),
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

function getDefaultWidgets(): DashboardWidget[] {
  return [
    // Top row - KPI Summary (compact)
    {
      id: "summary-kpi-1",
      userId: "",
      name: "Contract Summary",
      description: "Key metrics overview",
      category: "priority",
      widgetType: "summary-kpi",
      isVisible: true,
      position: { x: 0, y: 0, w: 12, h: 2 },
      configuration: { showHeader: true, refreshInterval: 300 },
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    // Second row - Charts (optimized sizes)
    {
      id: "spend-provider-1",
      userId: "",
      name: "Contract Value by Provider",
      description: "Top providers by total contract value",
      category: "portfolio",
      widgetType: "spend-by-provider",
      isVisible: true,
      position: { x: 0, y: 2, w: 4, h: 5 },
      configuration: { chartType: "pie", showLegend: false, refreshInterval: 600 },
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "agreement-types-1",
      userId: "",
      name: "Agreement Types Distribution",
      description: "Contract value by agreement type",
      category: "portfolio",
      widgetType: "agreement-types",
      isVisible: true,
      position: { x: 4, y: 2, w: 4, h: 5 },
      configuration: { chartType: "bar", refreshInterval: 600 },
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "contract-status-1",
      userId: "",
      name: "Contract Status Overview",
      description: "Current status distribution",
      category: "portfolio",
      widgetType: "contract-status",
      isVisible: true,
      position: { x: 8, y: 2, w: 4, h: 5 },
      configuration: { chartType: "pie", showLegend: false, refreshInterval: 600 },
      order: 4,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    // Third row - Analytics and Timeline
    {
      id: "confidence-distribution-1",
      userId: "",
      name: "AI Confidence Distribution",
      description: "Extraction confidence levels",
      category: "portfolio",
      widgetType: "confidence-distribution",
      isVisible: true,
      position: { x: 0, y: 7, w: 8, h: 4 },
      configuration: { chartType: "bar", refreshInterval: 600 },
      order: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "renewal-timeline-1",
      userId: "",
      name: "Renewal Timeline",
      description: "Upcoming renewals",
      category: "priority",
      widgetType: "renewal-timeline",
      isVisible: true,
      position: { x: 8, y: 7, w: 4, h: 4 },
      configuration: { refreshInterval: 3600 },
      order: 6,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },

    // Fourth row - Additional KPI
    {
      id: "confidence-1",
      userId: "",
      name: "Avg. Confidence",
      description: "Overall AI extraction confidence",
      category: "portfolio",
      widgetType: "confidence",
      isVisible: true,
      position: { x: 8, y: 11, w: 4, h: 4 },
      configuration: { refreshInterval: 600 },
      order: 7,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    // Bottom row - Table (compact)
    {
      id: "top-contracts-1",
      userId: "",
      name: "Top Contracts",
      description: "Highest value contracts",
      category: "portfolio",
      widgetType: "top-contracts",
      isVisible: true,
      position: { x: 0, y: 15, w: 12, h: 6 },
      configuration: {
        pageSize: 8,
        sortBy: "totalAmount",
        sortOrder: "desc",
        refreshInterval: 600,
      },
      order: 8,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];
}
