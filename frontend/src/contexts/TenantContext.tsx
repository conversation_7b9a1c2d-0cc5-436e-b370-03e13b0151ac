/**
 * Tenant Context
 * Provides tenant information and functions to the application
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { apiClient } from '@/lib/api-client';

// Interface for tenant data
interface Tenant {
  id: string;
  name: string;
  tier: string;
  status: string;
}

// Interface for tenant settings
interface TenantSettings {
  id: string;
  branding: any;
  whiteLabeling: any;
  customDomain: string | null;
  themeSettings: any;
  loginPageSettings: any;
}

// Interface for tenant context
interface TenantContextType {
  currentTenant: Tenant | null;
  tenantSettings: TenantSettings | null;
  isLoading: boolean;
  refreshTenantSettings: () => Promise<void>;
  getThemeColor: (colorName: string, defaultColor?: string) => string;
  getLogo: () => string;
}

// Create context with default values
const TenantContext = createContext<TenantContextType>({
  currentTenant: null,
  tenantSettings: null,
  isLoading: true,
  refreshTenantSettings: async () => {},
  getThemeColor: () => '',
  getLogo: () => '',
});

// Hook to use the tenant context
export const useTenant = () => useContext(TenantContext);

// Provider component
export const TenantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isLoading: authLoading } = useAuth();
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [tenantSettings, setTenantSettings] = useState<TenantSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load tenant data when user changes
  useEffect(() => {
    const loadTenantData = async () => {
      if (authLoading || !user) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Load tenant data
        const tenant = await apiClient.get<Tenant>(`/tenants/${user.tenantId}`);
        setCurrentTenant(tenant);

        // Load tenant settings
        await refreshTenantSettings();
      } catch (error) {
        console.error('Error loading tenant data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTenantData();
  }, [user, authLoading]);

  /**
   * Refreshes tenant settings
   */
  const refreshTenantSettings = async () => {
    if (!user) return;

    try {
      const settings = await apiClient.get<TenantSettings>(`/tenant-settings/${user.tenantId}`);
      setTenantSettings(settings);
    } catch (error) {
      console.error('Error loading tenant settings:', error);
    }
  };

  /**
   * Gets a theme color from tenant settings
   * @param colorName Color name
   * @param defaultColor Default color if not found
   * @returns Color value
   */
  const getThemeColor = (colorName: string, defaultColor: string = '#000000'): string => {
    if (!tenantSettings?.themeSettings?.colors) {
      return defaultColor;
    }

    return tenantSettings.themeSettings.colors[colorName] || defaultColor;
  };

  /**
   * Gets the tenant logo URL
   * @returns Logo URL
   */
  const getLogo = (): string => {
    if (!tenantSettings?.branding?.logoUrl) {
      return '/logo.svg'; // Default logo
    }

    return tenantSettings.branding.logoUrl;
  };

  // Context value
  const value = {
    currentTenant,
    tenantSettings,
    isLoading,
    refreshTenantSettings,
    getThemeColor,
    getLogo,
  };

  return <TenantContext.Provider value={value}>{children}</TenantContext.Provider>;
};
