/**
 * Authentication Context
 * Provides authentication state and functions to the application
 */

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { apiClient } from "@/lib/api-client";
import { jwtDecode } from "jwt-decode";

// Interface for user data
interface User {
  userId: string;
  tenantId: string;
  role: string;
  permissions: string[];
  name?: string;
  email?: string;
  status?: string;
  persona?: string;
  tenantName?: string;
  tenantRole?: string;
  createdAt?: string;
  updatedAt?: string;
  isSuperAdmin?: boolean;
}

// Interface for authentication context
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updatePersona: (persona: string) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isResourceOwner: (createdById: string | null) => boolean;
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  login: async () => { },
  logout: () => { },
  updatePersona: async () => { },
  hasPermission: () => false,
  isResourceOwner: () => false,
});

// Hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component with proper type definitions
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Check for existing token on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem("access_token");
        const userProfileStr = localStorage.getItem("user_profile");
        let userProfile = null;

        if (userProfileStr) {
          try {
            userProfile = JSON.parse(userProfileStr);
          } catch (e) {
            console.error("Error parsing user profile:", e);
          }
        }

        if (token) {
          try {
            // Decode token to get user data
            const decoded = jwtDecode<any>(token);

            // Check if token is expired
            const expiryStr = localStorage.getItem("token_expiry");
            if (expiryStr) {
              const expiry = parseInt(expiryStr, 10);
              if (Date.now() >= expiry) {
                // Token is expired, clear it
                throw new Error("Token expired");
              }
            }

            // Create user object with token data and profile data
            const userData = {
              userId: decoded.userId,
              tenantId: decoded.tenantId,
              role: decoded.role,
              permissions: decoded.permissions || [],
              isSuperAdmin: decoded.isSuperAdmin || false,
              // Add additional user details from the stored profile
              ...(userProfile || {}),
            };

            setUser(userData);

            // Let middleware handle redirects to avoid conflicts
          } catch (tokenError) {
            console.error("Token validation error:", tokenError);
            // Clear invalid tokens
            apiClient.logout();
            setUser(null);
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        // Clear invalid tokens
        apiClient.logout();
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, [router]);

  /**
   * Logs in a user
   * @param email User email
   * @param password User password
   */
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await apiClient.post("/api/auth/login", {
        email,
        password,
      });
      const { accessToken, refreshToken, expiresIn, userProfile } = response;

      // Store tokens and user profile
      apiClient.setTokens(accessToken, refreshToken, expiresIn, userProfile);

      // Decode token to get user data
      const decoded = jwtDecode<any>(accessToken);

      // Create user object with token data and profile data
      const userData = {
        userId: decoded.userId,
        tenantId: decoded.tenantId || "default",
        role: decoded.role || "USER",
        permissions: decoded.permissions || [],
        isSuperAdmin: decoded.isSuperAdmin || false,
        // Add additional user details from the profile
        name: userProfile?.name,
        email: userProfile?.email,
        status: userProfile?.status,
        persona: userProfile?.persona,
        tenantName: userProfile?.tenantName,
        tenantRole: userProfile?.tenantRole,
        createdAt: userProfile?.createdAt,
        updatedAt: userProfile?.updatedAt,
      };

      setUser(userData);

      // Redirect based on user type with a slight delay to ensure state is updated
      setTimeout(() => {
        if (userData.isSuperAdmin || userData.role === 'SUPER_ADMIN') {
          router.push("/super-admin/dashboard");
        } else if (userProfile?.persona) {
          router.push("/contract-concierge/discovery");
        } else {
          router.push("/administration/personas");
        }
      }, 100);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logs out the current user
   */
  const logout = async () => {
    try {
      // Call logout endpoint if available
      if (user) {
        try {
          await apiClient.post("/api/auth/logout");
        } catch (error) {
          console.error("Logout API error:", error);
          // Continue with local logout even if API call fails
        }
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Clear tokens and user data first
      apiClient.logout();
      setUser(null);

      // Add a small delay to ensure cookies are cleared before redirect
      setTimeout(() => {
        // Redirect to home page as preferred by user
        // Use window.location.href for a clean redirect
        window.location.href = "/home";
      }, 100);
    }
  };

  /**
   * Checks if the user has a specific permission
   * @param permission Permission to check
   * @returns Whether the user has the permission
   */
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // Super admin users have all permissions
    if (user.isSuperAdmin || user.role === "SUPER_ADMIN" || user.permissions.includes("super_admin:access")) {
      return true;
    }

    // Admin users have all permissions
    if (user.permissions.includes("admin:access") || user.role === "ADMIN") {
      return true;
    }

    return user.permissions.includes(permission);
  };

  /**
   * Checks if the current user is the owner of a resource
   * @param createdById ID of the user who created the resource
   * @returns Whether the current user is the owner
   */
  const isResourceOwner = (createdById: string | null): boolean => {
    if (!user || !createdById) return false;

    // Super admin users can access all resources
    if (user.isSuperAdmin || user.role === "SUPER_ADMIN" || user.permissions.includes("super_admin:access")) {
      return true;
    }

    // Admin users can access all resources
    if (user.permissions.includes("admin:access") || user.role === "ADMIN") {
      return true;
    }

    return user.userId === createdById;
  };

  /**
   * Updates the user's persona
   * @param persona The persona to set
   */
  const updatePersona = async (persona: string) => {
    try {
      const response = await apiClient.put("/api/auth/persona", { persona });

      // Update the user state with the new persona
      if (user) {
        const updatedUser = { ...user, persona };
        setUser(updatedUser);

        // Update the stored user profile
        const userProfileStr = localStorage.getItem("user_profile");
        if (userProfileStr) {
          try {
            const userProfile = JSON.parse(userProfileStr);
            userProfile.persona = persona;
            localStorage.setItem("user_profile", JSON.stringify(userProfile));
          } catch (e) {
            console.error("Error updating stored user profile:", e);
          }
        }
      }
    } catch (error) {
      console.error("Update persona error:", error);
      throw error;
    }
  };

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    updatePersona,
    hasPermission,
    isResourceOwner,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
