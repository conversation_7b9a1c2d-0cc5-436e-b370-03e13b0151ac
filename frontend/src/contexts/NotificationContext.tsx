/**
 * Notification Context
 * Provides notification state and functions to the application
 */

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from "react";
import { useAuth } from "./AuthContext";
import { apiClient } from "@/lib/api-client";

// Interface for notification data
export interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'CONTRACT' | 'LICENSE' | 'USER' | 'SYSTEM' | 'SECURITY';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  isRead: boolean;
  readAt?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  tenantId: string;
  resourceId?: string;
  resourceType?: string;
  actionUrl?: string;
  metadata?: any;
}

// Interface for notification context
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchNotifications: () => Promise<void>;
  fetchLiveNotifications: (since?: Date) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;

  // Real-time updates
  startPolling: () => void;
  stopPolling: () => void;
}

// Create context with default values
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  fetchNotifications: async () => { },
  fetchLiveNotifications: async () => { },
  markAsRead: async () => { },
  markAllAsRead: async () => { },
  deleteNotification: async () => { },
  startPolling: () => { },
  stopPolling: () => { },
});

// Provider component props
interface NotificationProviderProps {
  children: ReactNode;
}

// Provider component
export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const { user, isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    if (!user?.userId || !isAuthenticated) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get(`/api/notifications/user/${user.userId}`);
      setNotifications(response.notifications || []);

      // Calculate unread count
      const unread = (response.notifications || []).filter((n: Notification) => !n.isRead).length;
      setUnreadCount(unread);
    } catch (err: any) {
      console.error('Error fetching notifications:', err);
      setError(err.response?.data?.error || 'Failed to fetch notifications');
    } finally {
      setIsLoading(false);
    }
  }, [user?.userId, isAuthenticated]);

  // Fetch live notifications (for real-time updates)
  const fetchLiveNotifications = useCallback(async (since?: Date) => {
    if (!user?.userId || !isAuthenticated) return;

    try {
      const params: any = { limit: 10 };
      if (since) {
        params.since = since.toISOString();
      }

      const response = await apiClient.get(`/api/notifications/user/${user.userId}/live?${new URLSearchParams(params).toString()}`);

      const liveData = response;

      if (since && liveData.notifications.length > 0) {
        // Add new notifications to the beginning of the list
        setNotifications(prev => [...liveData.notifications, ...prev]);
      } else {
        // Replace all notifications
        setNotifications(liveData.notifications || []);
      }

      setUnreadCount(liveData.unreadCount || 0);
    } catch (err: any) {
      console.error('Error fetching live notifications:', err);
      setError(err.response?.data?.error || 'Failed to fetch live notifications');
    }
  }, [user?.userId, isAuthenticated]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await apiClient.post(`/api/notifications/${notificationId}/read`);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err: any) {
      console.error('Error marking notification as read:', err);
      setError(err.response?.data?.error || 'Failed to mark notification as read');
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!user?.userId) return;

    try {
      await apiClient.post(`/api/notifications/user/${user.userId}/read-all`);

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          isRead: true,
          readAt: new Date().toISOString()
        }))
      );

      setUnreadCount(0);
    } catch (err: any) {
      console.error('Error marking all notifications as read:', err);
      setError(err.response?.data?.error || 'Failed to mark all notifications as read');
    }
  }, [user?.userId]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await apiClient.delete(`/api/notifications/${notificationId}`);

      // Update local state
      const notification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));

      // Update unread count if the deleted notification was unread
      if (notification && !notification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (err: any) {
      console.error('Error deleting notification:', err);
      setError(err.response?.data?.error || 'Failed to delete notification');
    }
  }, [notifications]);



  // Start polling for new notifications
  const startPolling = useCallback(() => {
    // Don't start if already polling
    if (pollingInterval) return;

    const interval = setInterval(async () => {
      // Simple fetch without complex dependencies
      if (user?.userId && isAuthenticated) {
        try {
          const response = await apiClient.get(`/api/notifications/user/${user.userId}`);
          setNotifications(response.notifications || []);

          // Calculate unread count
          const unread = (response.notifications || []).filter((n: Notification) => !n.isRead).length;
          setUnreadCount(unread);
        } catch (err) {
          console.error('Error polling notifications:', err);
        }
      }
    }, 60000); // Poll every 60 seconds

    setPollingInterval(interval);
  }, [pollingInterval, user?.userId, isAuthenticated]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  }, [pollingInterval]);

  // Initial fetch and setup polling when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.userId) {
      // Initial fetch
      fetchNotifications();

      // Start polling
      startPolling();
    } else {
      // Clear notifications when user is not authenticated
      setNotifications([]);
      setUnreadCount(0);

      // Stop polling
      stopPolling();
    }

    // Cleanup on unmount
    return () => {
      stopPolling();
    };
  }, [isAuthenticated, user?.userId]); // Only depend on auth state and userId

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    fetchLiveNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    startPolling,
    stopPolling,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
