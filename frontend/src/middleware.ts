import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { jwtDecode } from "jwt-decode";

// Define public paths that don't require authentication (for future use)
// const publicPaths = ["/", "/home", "/features", "/pricing", "/about", "/contact", "/auth/login", "/auth/register", "/auth/forgot-password"];

// Define normal user dashboard paths
const normalUserPaths = [
  // Main navigation sections
  "/contract-concierge", // Contract Concierge (discovery, benchmark)
  "/cockpit", // Cockpit (dashboard, ai-insights, analytics)
  "/source-to-contract", // Source to Contract (sourcing, rfx, negotiation, marketplace)
  "/contract-management", // Contract Management (contracts, licenses, compliance, etc.)
  "/administration", // Administration (personas, users, integrations, audit)

  // Legacy/direct routes (for backward compatibility)
  "/discovery",
  "/sourcing",
  "/rfx",
  "/negotiation",
  "/marketplace",
  "/renewals",
  "/profile",

  // Next.js private route group
  "/(private)",
];

// Define super admin paths
const superAdminPaths = [
  "/super-admin",
];

// All dashboard paths (normal user + super admin)
const dashboardPaths = [...normalUserPaths, ...superAdminPaths];

/**
 * Middleware function to handle authentication and role-based redirects
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check path types
  const isDashboardPath = dashboardPaths.some(
    (path) => pathname === path || pathname.startsWith(`${path}/`)
  );
  const isNormalUserPath = normalUserPaths.some(
    (path) => pathname === path || pathname.startsWith(`${path}/`)
  );
  const isSuperAdminPath = superAdminPaths.some(
    (path) => pathname === path || pathname.startsWith(`${path}/`)
  );

  // Get the token and expiry from cookies
  const token = request.cookies.get("access_token")?.value;
  const tokenExpiry = request.cookies.get("token_expiry")?.value;

  // Check if token exists and is not expired
  let isValidToken = false;
  let userRole: string | null = null;
  let isSuperAdmin = false;

  if (token && tokenExpiry) {
    const expiryTime = parseInt(tokenExpiry, 10);
    const currentTime = Date.now();
    isValidToken = currentTime < expiryTime;

    // Decode token to get user role
    if (isValidToken) {
      try {
        const decoded: any = jwtDecode(token);
        userRole = decoded.role;
        isSuperAdmin = decoded.isSuperAdmin || decoded.role === 'SUPER_ADMIN';
      } catch (error) {
        console.error('Token decode error:', error);
        isValidToken = false;
      }
    }
  }

  // Check if this is a logout scenario (no valid token but trying to access home)
  const isLogoutRedirect = !isValidToken && (pathname === "/" || pathname === "/home");

  // If the user is not authenticated and trying to access a dashboard path
  if (!isValidToken && isDashboardPath) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  // Role-based access control for authenticated users
  if (isValidToken && userRole) {
    // Super admin trying to access normal user routes
    if (isSuperAdmin && isNormalUserPath) {
      console.warn(`Super admin blocked from accessing normal user path: ${pathname}`);
      return NextResponse.redirect(new URL("/super-admin/dashboard", request.url));
    }

    // Normal user trying to access super admin routes
    if (!isSuperAdmin && isSuperAdminPath) {
      console.warn(`Normal user blocked from accessing super admin path: ${pathname}`);
      return NextResponse.redirect(new URL("/contract-concierge/discovery", request.url));
    }

    // Redirect authenticated users from public pages to appropriate dashboard
    // But allow logout redirects to home page
    if (!isLogoutRedirect && (pathname.startsWith("/auth/") || pathname === "/" || pathname === "/home")) {
      if (isSuperAdmin) {
        return NextResponse.redirect(new URL("/super-admin/dashboard", request.url));
      } else {
        return NextResponse.redirect(new URL("/contract-concierge/discovery", request.url));
      }
    }
  }

  // Continue with the request for all other cases
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes
     */
    "/((?!_next/static|_next/image|favicon.ico|public|api).*)",
  ],
};
