/**
 * Integrity Service
 * API service for integrity configuration and analysis
 */

import { apiClient } from "@/lib/api-client";

export interface IntegrityClauseValue {
  value: string;
  weightage: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  description: string;
}

export interface IntegrityClause {
  id: string;
  name: string;
  description: string;
  category: string;
  possibleValues: IntegrityClauseValue[];
}

export interface IntegrityConfigurationData {
  configurationName: string;
  description?: string;
  version?: string;
  clauses: IntegrityClause[];
}

export interface IntegrityConfiguration {
  id: string;
  userId: string;
  tenantId: string;
  configurationName: string;
  clauses: IntegrityConfigurationData;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IntegrityClauseResult {
  clauseName: string;
  value: string; // Either the found value or "N/A"
  description: string; // Description of what this value means
  riskScore: number;
  maxScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  confidence: number;
}

export interface IntegrityAnalysisResult {
  configurationUsed: {
    id: string;
    name: string;
    totalClauses: number;
  };
  overallRiskScore: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  maxPossibleScore: number;
  clauses: Record<string, IntegrityClauseResult>;
  risks: DocumentRisk[];
  extractionDate: string;
  processingTimeMs: number;
}

export interface DocumentRisk {
  id: string;
  title: string;
  description: string;
  category: 'Legal' | 'Financial' | 'Operational' | 'Compliance' | 'Security' | 'Commercial';
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  impact: string;
  mitigation: string;
  confidence: number;
}

/**
 * Integrity Service class for managing integrity configurations and analysis
 */
export class IntegrityService {
  /**
   * Get user's integrity configurations
   */
  static async getConfigurations(): Promise<IntegrityConfiguration[]> {
    const response = await apiClient.get<{ success: boolean; configurations: IntegrityConfiguration[] }>(
      "/api/integrity/configurations"
    );
    return response.configurations;
  }

  /**
   * Get user's active integrity configuration
   */
  static async getActiveConfiguration(): Promise<IntegrityConfiguration> {
    const response = await apiClient.get<{ success: boolean; configuration: IntegrityConfiguration }>(
      "/api/integrity/configurations/active"
    );
    return response.configuration;
  }

  /**
   * Create new integrity configuration
   */
  static async createConfiguration(
    configurationData: IntegrityConfigurationData
  ): Promise<IntegrityConfiguration> {
    const response = await apiClient.post<{ success: boolean; configuration: IntegrityConfiguration }>(
      "/api/integrity/configurations",
      configurationData
    );
    return response.configuration;
  }

  /**
   * Update integrity configuration
   */
  static async updateConfiguration(
    configId: string,
    configurationData: IntegrityConfigurationData
  ): Promise<IntegrityConfiguration> {
    const response = await apiClient.put<{ success: boolean; configuration: IntegrityConfiguration }>(
      `/api/integrity/configurations/${configId}`,
      configurationData
    );
    return response.configuration;
  }

  /**
   * Set configuration as active
   */
  static async setActiveConfiguration(configId: string): Promise<IntegrityConfiguration> {
    const response = await apiClient.post<{ success: boolean; configuration: IntegrityConfiguration }>(
      `/api/integrity/configurations/${configId}/activate`
    );
    return response.configuration;
  }

  /**
   * Get default integrity template
   */
  static async getDefaultTemplate(): Promise<IntegrityConfigurationData> {
    const response = await apiClient.get<{ success: boolean; template: IntegrityConfigurationData }>(
      "/api/integrity/template"
    );
    return response.template;
  }

  /**
   * Reset user's configuration to use updated template
   */
  static async resetToTemplate(): Promise<IntegrityConfiguration> {
    const response = await apiClient.post<{ success: boolean; configuration: IntegrityConfiguration; message: string }>(
      "/api/integrity/configurations/reset-to-template"
    );
    return response.configuration;
  }

  /**
   * Generate integrity analysis for a contract
   */
  static async generateIntegrityAnalysis(contractId: string): Promise<IntegrityAnalysisResult> {
    const response = await apiClient.post<{ success: boolean; analysis: IntegrityAnalysisResult }>(
      `/api/contracts/${contractId}/generate-integrity-analysis`,
      {},
      { timeout: 600000 } // 10 minutes for AI analysis
    );
    return response.analysis;
  }

  /**
   * Calculate risk level based on score percentage
   * Higher scores mean higher integrity and lower risk
   */
  static calculateRiskLevel(score: number, maxScore: number): 'Low' | 'Medium' | 'High' {
    const percentage = (score / maxScore) * 100;

    if (percentage >= 70) return 'Low';
    if (percentage >= 40) return 'Medium';
    return 'High';
  }

  /**
   * Get risk level color for UI display
   */
  static getRiskLevelColor(riskLevel: 'Low' | 'Medium' | 'High'): string {
    switch (riskLevel) {
      case 'Low':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'High':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }

  /**
   * Format risk score for display
   */
  static formatRiskScore(score: number, maxScore: number): string {
    const percentage = ((score / maxScore) * 100).toFixed(1);
    return `${score}/${maxScore} (${percentage}%)`;
  }

  /**
   * Validate configuration data
   */
  static validateConfiguration(config: IntegrityConfigurationData): string[] {
    const errors: string[] = [];

    if (!config.configurationName || config.configurationName.trim() === '') {
      errors.push('Configuration name is required');
    }

    if (!config.clauses || config.clauses.length === 0) {
      errors.push('At least one clause is required');
    }

    // Check for duplicate clause IDs
    const clauseIds = config.clauses.map(c => c.id);
    const uniqueIds = new Set(clauseIds);
    if (clauseIds.length !== uniqueIds.size) {
      errors.push('Duplicate clause IDs found');
    }

    // Validate each clause
    config.clauses.forEach((clause, index) => {
      if (!clause.id || !clause.name) {
        errors.push(`Clause ${index + 1}: ID and name are required`);
      }

      if (!clause.possibleValues || clause.possibleValues.length === 0) {
        errors.push(`Clause "${clause.name}": At least one possible value is required`);
      }

      clause.possibleValues.forEach((value, valueIndex) => {
        if (!value.value || typeof value.weightage !== 'number') {
          errors.push(`Clause "${clause.name}", Value ${valueIndex + 1}: Value and weightage are required`);
        }

        if (!['Low', 'Medium', 'High'].includes(value.riskLevel)) {
          errors.push(`Clause "${clause.name}", Value ${valueIndex + 1}: Invalid risk level`);
        }
      });
    });

    return errors;
  }

  /**
   * Calculate maximum possible score for a configuration
   */
  static calculateMaxScore(config: IntegrityConfigurationData): number {
    return config.clauses.reduce((total, clause) => {
      const maxWeightage = Math.max(...clause.possibleValues.map(v => v.weightage));
      return total + maxWeightage;
    }, 0);
  }
}

// Create a singleton instance
export const integrityService = new IntegrityService();
