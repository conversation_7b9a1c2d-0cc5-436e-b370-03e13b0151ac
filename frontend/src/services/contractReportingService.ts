/**
 * Contract Reporting Service
 * Frontend service for managing contract reporting relationships
 */

import { apiClient } from "@/lib/api-client";

export interface ReportingValidationResult {
  isValid: boolean;
  reason?: string;
  suggestedParent?: string;
  isHierarchyWarning?: boolean;
}

export interface ContractSummary {
  id: string;
  title: string;
  agreementType: string;
  provider?: string;
  startDate?: string;
  endDate?: string;
}

export interface ChildContractsResponse {
  parentContractId: string;
  children: ContractSummary[];
}

export interface ReportingChainResponse {
  contractId: string;
  reportingChain: ContractSummary[];
}

export class ContractReportingService {
  /**
   * Updates the reporting relationship for a contract
   */
  static async updateReportingRelationship(
    contractId: string,
    parentContractId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.put(
        `/api/contracts/${contractId}/reporting-to`,
        { parentContractId }
      );
      return response;
    } catch (error) {
      console.error("Error updating reporting relationship:", error);
      throw new Error("Failed to update reporting relationship");
    }
  }

  /**
   * Removes the reporting relationship for a contract
   */
  static async removeReportingRelationship(
    contractId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.delete(
        `/api/contracts/${contractId}/reporting-to`
      );
      return response;
    } catch (error) {
      console.error("Error removing reporting relationship:", error);
      throw new Error("Failed to remove reporting relationship");
    }
  }

  /**
   * Gets contracts that report to a specific contract
   */
  static async getChildContracts(
    contractId: string
  ): Promise<ChildContractsResponse> {
    try {
      const response = await apiClient.get<ChildContractsResponse>(
        `/api/contracts/${contractId}/children`
      );
      return response;
    } catch (error) {
      console.error("Error getting child contracts:", error);
      throw new Error("Failed to get child contracts");
    }
  }

  /**
   * Gets the reporting chain for a contract
   */
  static async getReportingChain(
    contractId: string
  ): Promise<ReportingChainResponse> {
    try {
      const response = await apiClient.get<ReportingChainResponse>(
        `/api/contracts/${contractId}/reporting-chain`
      );
      return response;
    } catch (error) {
      console.error("Error getting reporting chain:", error);
      throw new Error("Failed to get reporting chain");
    }
  }

  /**
   * Validates a potential reporting relationship
   */
  static async validateReportingRelationship(
    contractId: string,
    parentContractId: string,
    allowManualOverride: boolean = false
  ): Promise<ReportingValidationResult> {
    try {
      const response = await apiClient.post<ReportingValidationResult>(
        `/api/contracts/${contractId}/validate-reporting`,
        { parentContractId, allowManualOverride }
      );
      return response;
    } catch (error) {
      console.error("Error validating reporting relationship:", error);
      throw new Error("Failed to validate reporting relationship");
    }
  }

  /**
   * Gets all contracts for autocomplete/selection purposes
   */
  static async getContractsForSelection(): Promise<ContractSummary[]> {
    try {
      const response = await apiClient.get("/api/contracts/autocomplete");

      // Transform the response to match our ContractSummary interface
      return (
        response.contracts?.map((contract: any) => ({
          id: contract.id,
          title: contract.title || contract.contractNumber || "Untitled",
          agreementType: contract.agreementType || "Unknown",
          provider: contract.counterparty,
          startDate: contract.startDate,
          endDate: contract.endDate,
        })) || []
      );
    } catch (error) {
      console.error("Error getting contracts for selection:", error);
      throw new Error("Failed to get contracts for selection");
    }
  }
}
