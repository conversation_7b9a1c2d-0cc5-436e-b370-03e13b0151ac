/**
 * Repository Export Service
 * Handles repository export functionality using backend APIs
 */

import { apiClient } from "@/lib/api-client";

export interface RepositoryExportStats {
  totalContracts: number;
  contractsWithExtraction: number;
  averageConfidence: number;
  agreementTypes: Record<string, number>;
  statusDistribution: Record<string, number>;
  folderDistribution: Record<string, number>;
}

export interface UITableData {
  columns: Array<{
    header: string;
    key: string;
  }>;
  rows: Array<Array<string | number>>;
}

class RepositoryExportService {
  /**
   * Exports repository contracts to Excel format using backend API
   * @returns Promise that resolves when download starts
   */
  async exportToExcel(): Promise<void> {
    try {
      const baseUrl =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000";
      const url = `${baseUrl}/api/repository/export/excel`;

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
      const filename = `Contract_Repository_Export_${timestamp}.xlsx`;

      // Use fetch directly for blob response since apiClient might not handle blobs correctly
      const authToken = localStorage.getItem("access_token");

      if (!authToken) {
        throw new Error("No authentication token found. Please log in again.");
      }

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Export failed with status ${response.status}`
        );
      }

      // Get the blob data
      const blob = await response.blob();

      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error exporting repository to Excel:", error);
      throw error;
    }
  }

  /**
   * Exports repository contracts using UI table format
   * @param tableData UI table data to export
   * @returns Promise that resolves when download starts
   */
  async exportUITableToExcel(tableData: UITableData): Promise<void> {
    try {
      const baseUrl =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000";
      const url = `${baseUrl}/api/repository/export/ui-table`;

      console.log("Exporting repository UI table data:", {
        url,
        columnsCount: tableData.columns.length,
        rowsCount: tableData.rows.length,
      });

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
      const filename = `Contract_Repository_Export_${timestamp}.xlsx`;

      // Use fetch directly for blob response since apiClient might not handle blobs correctly
      const authToken = localStorage.getItem("access_token");

      if (!authToken) {
        throw new Error("No authentication token found. Please log in again.");
      }

      const response = await fetch(url, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ tableData }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Export failed with status ${response.status}`
        );
      }

      // Get the blob data
      const blob = await response.blob();

      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error exporting repository UI table to Excel:", error);
      throw error;
    }
  }

  /**
   * Gets repository export statistics
   * @returns Promise that resolves to export statistics
   */
  async getExportStatistics(): Promise<RepositoryExportStats> {
    try {
      const response = await apiClient.get("/api/repository/export/stats");
      return response.data;
    } catch (error) {
      console.error("Error getting repository export statistics:", error);
      throw error;
    }
  }

  /**
   * Prepares UI table data from contract data for export
   * @param contractsData Array of contract data
   * @returns UI table data format
   */
  prepareUITableData(contractsData: any[]): UITableData {
    const columns = [
      { header: "Contract Name", key: "title" },
      { header: "Contract ID", key: "contractNumber" },
      { header: "Agreement Type", key: "agreementType" },
      { header: "Provider/Supplier", key: "provider" },
      { header: "Client/Counterparty", key: "counterparty" },
      { header: "Contract Value", key: "value" },
      { header: "Start Date", key: "startDate" },
      { header: "End Date", key: "endDate" },
      { header: "Status", key: "status" },
      { header: "Auto Renewal", key: "autoRenewal" },
      { header: "Notice Period", key: "noticePeriod" },
      { header: "Folder", key: "folder" },
      { header: "Last Updated", key: "updatedAt" },
      { header: "Overall Confidence", key: "confidence" },
    ];

    const rows = contractsData.map((contract) => [
      contract.title || "N/A",
      contract.contractNumber || "N/A",
      contract.agreementType || "N/A",
      contract.provider || "N/A",
      contract.counterparty || "N/A",
      contract.value || "N/A",
      contract.startDate || "N/A",
      contract.endDate || "N/A",
      contract.status || "N/A",
      contract.autoRenewal || "N/A",
      contract.noticePeriod || "N/A",
      contract.folderName || "Standalone",
      contract.updatedAt || "N/A",
      contract.confidence || "N/A",
    ]);

    return { columns, rows };
  }
}

// Export singleton instance
export const repositoryExportService = new RepositoryExportService();
