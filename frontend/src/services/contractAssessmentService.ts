/**
 * Contract Assessment Service
 * Handles API calls for contract assessments
 */

import { apiClient } from "@/lib/api-client";

/**
 * Contract assessment data interface
 */
export interface ContractAssessmentData {
  contractId?: string;
  annualContractValue?: number;
  totalContractValue?: number;
  currency?: string;
  terminationForConvenience?: boolean;
  terminationNoticeDays?: number;
  autoRenewal?: boolean;
  licenseItems?: Array<{
    name: string;
    quantity: number;
    unitPrice: number;
    discount?: number;
  }>;
  geographicLimitations?: string;
  customerDefinition?: string;
  consumptionReporting?: boolean;
  auditRequirements?: string;
  activeUsersPercentage?: number;
  featureUtilization?: number;
  usageFrequency?: string;
  volumeChangeForecast?: string;
  additionalProducts?: string;
  redundantProducts?: string;
  downgradePotential?: boolean;
  preferredContractLength?: string;
  paymentFlexibility?: boolean;
  vendorSwitchWillingness?: boolean;
  satisfactionRating?: number;
  impactRating?: number;
  isNicheOffering?: boolean;
}

/**
 * Contract Assessment Service
 */
const contractAssessmentService = {
  /**
   * Creates or updates a contract assessment
   * @param contractId Contract ID
   * @param data Assessment data
   * @returns Created or updated assessment
   */
  async createOrUpdateAssessment(
    contractId: string,
    data: ContractAssessmentData
  ) {
    try {
      return await apiClient.post(
        `/api/contracts/${contractId}/assessment`,
        data
      );
    } catch (error) {
      console.error("Error creating/updating contract assessment:", error);
      throw error;
    }
  },

  /**
   * Gets a contract assessment
   * @param contractId Contract ID
   * @returns Contract assessment or null if not found
   */
  async getAssessment(contractId: string) {
    try {
      return await apiClient.get(`/api/contracts/${contractId}/assessment`);
    } catch (error) {
      console.error("Error getting contract assessment:", error);
      throw error;
    }
  },
};

export default contractAssessmentService;
