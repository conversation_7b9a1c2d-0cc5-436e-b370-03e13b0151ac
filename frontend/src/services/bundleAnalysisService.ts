/**
 * Bundle Analysis Service
 * Handles API calls for bundle interconnection analysis
 */

import { apiClient } from "@/lib/api-client";


export interface BundleAnalysisResponse {
  exists: boolean;
  folderId: string;
  folderName: string;
  analysis?: BundleAnalysisData;
  contractIds?: string[];
  extractionDate?: string;
  processingTimeMs?: number;
  overallConfidence?: number;
  modelUsed?: string;
  createdAt?: string;
  updatedAt?: string;
  message?: string;
}

export interface BundleAnalysisData {
  contract_groups: ContractGroup[];
  standalone_documents: StandaloneDocument[];
  overall_analysis: OverallAnalysis;
}

export interface OverallAnalysis {
  total_bundles: number;
  standalone_documents: number;
  high_risk_bundles: number;
  confidence_score: number;
  analysis_date: string;
  folder_name: string;
  processing_date?: string;
  model_used?: string;
  contract_count?: number;
}

export interface ContractGroup {
  group_id: string;
  documents: GroupDocument[];
  hierarchy: DocumentHierarchy;
  comprehensive_clause_analysis: ComprehensiveClauseAnalysis;
  business_implications: BusinessImplications;
}

export interface GroupDocument {
  name: string;
  agreementType: string;
  is_primary?: boolean;
  relationship_type?: 'explicit' | 'inferred';
  linked_by?: string[];
  hierarchy_level?: number;
  confidence?: number;
}

export interface DocumentHierarchy {
  primary_document: string;
  document_chain: string[];
}

export interface ComprehensiveClauseAnalysis {
  "Use rights & restrictions": CategoryClauseAnalysis;
  "General": CategoryClauseAnalysis;
  "Legal terms": CategoryClauseAnalysis;
  "Commercial terms": CategoryClauseAnalysis;
  "Data protection": CategoryClauseAnalysis;
  "Others": CategoryClauseAnalysis;
}

export interface CategoryClauseAnalysis {
  category_risk_assessment: RiskLevel;
  [clauseName: string]: ClauseAnalysis | RiskLevel;
}

export interface ClauseAnalysis {
  [documentName: string]: ClauseValue | any;
  precedence_order?: string[];
  analysis?: string;
}

export interface ClauseValue {
  status: 'SAME' | 'CONFLICT' | 'SUPERSEDING' | 'UNIQUE' | 'MISSING';
  value: string;
  confidence: number;
}



export interface RiskLevel {
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
  conflicts: number;
  total_clauses: number;
}

export interface BusinessImplications {
  critical_conflicts: string[];
  missing_clauses: string[];
  recommendations: string[];
}

export interface StandaloneDocument {
  name: string;
  agreementType: string;
  linked_group_id: string | null;
  is_orphan: boolean;
}

export interface GenerateBundleAnalysisResponse {
  success: boolean;
  folderId: string;
  folderName: string;
  analysis: BundleAnalysisData;
  contractIds: string[];
  extractionDate: string;
  processingTimeMs: number;
  overallConfidence?: number;
  message: string;
}

class BundleAnalysisService {
  /**
   * Get bundle analysis for a folder if it exists
   */
  async getBundleAnalysis(folderId: string): Promise<BundleAnalysisResponse> {
    try {
      const response = await apiClient.get<BundleAnalysisResponse>(`/api/bundle-analysis/${folderId}`);
      return response;
    } catch (error: any) {
      console.error('Error getting bundle analysis:', error);
      throw new Error(error.response?.data?.error || 'Failed to get bundle analysis');
    }
  }

  /**
   * Generate new bundle analysis for a folder
   */
  async generateBundleAnalysis(folderId: string): Promise<GenerateBundleAnalysisResponse> {
    try {
      const response = await apiClient.post<GenerateBundleAnalysisResponse>(
        `/api/bundle-analysis/${folderId}`,
        {},
        { timeout: 600000 } // 10 minutes for AI analysis
      );
      return response;
    } catch (error: any) {
      console.error('Error generating bundle analysis:', error);
      throw new Error(error.response?.data?.error || 'Failed to generate bundle analysis');
    }
  }

  /**
   * Format confidence score as percentage
   */
  formatConfidence(confidence?: number): string {
    if (confidence === undefined || confidence === null) return 'N/A';
    return `${Math.round(confidence * 100)}%`;
  }

  /**
   * Get confidence color based on score
   */
  getConfidenceColor(confidence?: number): string {
    if (confidence === undefined || confidence === null) return 'text-muted-foreground';
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.5) return 'text-yellow-600';
    return 'text-red-600';
  }

  /**
   * Format processing time
   */
  formatProcessingTime(timeMs?: number): string {
    if (!timeMs) return 'N/A';
    if (timeMs < 1000) return `${timeMs}ms`;
    return `${(timeMs / 1000).toFixed(1)}s`;
  }

  /**
   * Get document type badge color
   */
  getDocumentTypeBadgeColor(type: string): string {
    const typeColors: Record<string, string> = {
      'Master Agreement': 'bg-blue-100 text-blue-800',
      'SOW': 'bg-green-100 text-green-800',
      'Statement of Work': 'bg-green-100 text-green-800',
      'Invoice': 'bg-purple-100 text-purple-800',
      'Purchase Order': 'bg-orange-100 text-orange-800',
      'Amendment': 'bg-yellow-100 text-yellow-800',
      'Schedule': 'bg-indigo-100 text-indigo-800',
      'Appendix': 'bg-indigo-100 text-indigo-800',
      'NDA': 'bg-red-100 text-red-800',
      'SLA': 'bg-cyan-100 text-cyan-800',
      'T&Cs': 'bg-gray-100 text-gray-800',
    };
    return typeColors[type] || 'bg-gray-100 text-gray-800';
  }

  /**
   * Get link type badge color
   */
  getLinkTypeBadgeColor(linkType?: string): string {
    if (linkType === 'firm') return 'bg-green-100 text-green-800';
    if (linkType === 'inferred') return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  }
}

export const bundleAnalysisService = new BundleAnalysisService();
