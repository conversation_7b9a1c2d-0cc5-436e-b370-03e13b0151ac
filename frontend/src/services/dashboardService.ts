/**
 * Dashboard Service
 * Handles API calls for dashboard data and analytics
 */

import { apiClient } from "@/lib/api-client";

export interface DashboardSummary {
  totalContracts: number;
  activeContracts: number;
  upcomingRenewals: number;
  totalValue: number;
  totalValueCurrency: string;
  averageConfidence: number;
}

export interface SpendAnalysisData {
  spendByProvider: Array<{ name: string; value: number }>;
  spendByAgreementType: Array<{ name: string; value: number }>;
}

export interface RenewalTimelineData {
  [key: string]: number; // e.g., "30days": 5, "60days": 12
}

export interface ConfidenceDistribution {
  high: { count: number; percentage: number };
  medium: { count: number; percentage: number };
  low: { count: number; percentage: number };
  total: number;
}

export interface TopContract {
  id: string;
  contractId: string;
  provider: string;
  product: string;
  totalAmount: number;
  endDate: string | null;
  agreementType: string;
  extractionDate: string;
}

export interface ActiveContractsPerSupplier {
  name: string;
  activeContracts: number;
}

export interface ContractDetail {
  contractId: string;
  contractName: string;
  endDate: string;
  totalTCV: number;
}

export interface ExpiringContractsData {
  period: string;
  count: number;
  totalTCV: number;
  currency: string;
  contracts: ContractDetail[];
}

export interface AutoRenewalsData {
  period: string;
  safe: {
    count: number;
    totalTCV: number;
    contracts: ContractDetail[];
  };
  notSafe: {
    count: number;
    totalTCV: number;
    contracts: ContractDetail[];
  };
  currency: string;
}

export interface AutoRenewalsByClassification {
  name: string;
  count: number;
  totalTCV: number;
  currency: string;
  contracts: ContractDetail[];
}

export interface PortfolioOverviewData {
  totalAgreements: {
    active: { count: number; totalTCV: number };
    inactive: { count: number; totalTCV: number };
    total: { count: number; totalTCV: number };
  };
  highValueAgreements: {
    high: { count: number; totalTCV: number };
    medium: { count: number; totalTCV: number };
    low: { count: number; totalTCV: number };
  };
  criticalAgreements: Array<{
    name: string;
    count: number;
    totalTCV: number;
  }>;
  expiredAgreements: Array<{
    name: string;
    count: number;
    totalTCV: number;
  }>;
  agingContracts: {
    olderThan4Y: { count: number; totalTCV: number };
    approaching4Y: { count: number; totalTCV: number };
  };
  customerSupplierPaper: {
    customer: { count: number; totalTCV: number };
    supplier: { count: number; totalTCV: number };
  };
  agreementType: Array<{
    name: string;
    count: number;
    totalTCV: number;
  }>;
  serviceType: Array<{
    name: string;
    count: number;
    totalTCV: number;
  }>;
  currency: string;
}

export class DashboardService {
  /**
   * Get dashboard summary statistics
   */
  static async getSummary(): Promise<DashboardSummary> {
    return await apiClient.get<DashboardSummary>("/api/dashboard/summary");
  }

  /**
   * Get spend analysis data
   */
  static async getSpendAnalysis(): Promise<SpendAnalysisData> {
    return await apiClient.get<SpendAnalysisData>(
      "/api/dashboard/spend-analysis"
    );
  }

  /**
   * Get renewal timeline data
   */
  static async getRenewalTimeline(): Promise<RenewalTimelineData> {
    return await apiClient.get<RenewalTimelineData>(
      "/api/dashboard/renewal-timeline"
    );
  }

  /**
   * Get confidence distribution data
   */
  static async getConfidenceDistribution(): Promise<ConfidenceDistribution> {
    return await apiClient.get<ConfidenceDistribution>(
      "/api/dashboard/confidence-distribution"
    );
  }

  /**
   * Get top contracts by value
   */
  static async getTopContracts(limit: number = 10): Promise<TopContract[]> {
    return await apiClient.get<TopContract[]>(
      `/api/dashboard/top-contracts?limit=${limit}`
    );
  }

  /**
   * Get contracts by status (active/inactive)
   */
  static async getContractsByStatus(status: 'active' | 'inactive', limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/contracts-by-status?status=${status}&limit=${limit}`
    );
  }

  /**
   * Get contracts by aging category
   */
  static async getContractsByAging(agingCategory: 'olderThan4Y' | 'approaching4Y', limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/contracts-by-aging?agingCategory=${agingCategory}&limit=${limit}`
    );
  }

  /**
   * Get critical contracts by service type
   */
  static async getCriticalContractsByServiceType(serviceType: string, limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/critical-contracts-by-service-type?serviceType=${encodeURIComponent(serviceType)}&limit=${limit}`
    );
  }

  /**
   * Get contracts by criticality level
   */
  static async getContractsByCriticality(criticality: 'critical' | 'important' | 'standard', limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/contracts-by-criticality?criticality=${criticality}&limit=${limit}`
    );
  }

  /**
   * Get contracts by agreement type
   */
  static async getContractsByType(type: string, limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/contracts-by-type?type=${encodeURIComponent(type)}&limit=${limit}`
    );
  }

  /**
   * Get expired contracts by service type
   */
  static async getExpiredContractsByServiceType(serviceType: string, limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/expired-contracts-by-service-type?serviceType=${encodeURIComponent(serviceType)}&limit=${limit}`
    );
  }

  /**
   * Get contracts by service type
   */
  static async getContractsByServiceType(serviceType: string, limit: number = 10): Promise<ContractDetail[]> {
    return await apiClient.get<ContractDetail[]>(
      `/api/dashboard/contracts-by-service-type?serviceType=${encodeURIComponent(serviceType)}&limit=${limit}`
    );
  }

  /**
   * Get active contracts per supplier
   */
  static async getActiveContractsPerSupplier(): Promise<
    ActiveContractsPerSupplier[]
  > {
    console.log("Fetching active contracts per supplier...");
    try {
      const result = await apiClient.get<ActiveContractsPerSupplier[]>(
        "/api/dashboard/active-contracts-per-supplier"
      );
      console.log("Active contracts per supplier result:", result);
      return result;
    } catch (error) {
      console.error("Error fetching active contracts per supplier:", error);
      throw error;
    }
  }

  /**
   * Get expiring contracts data
   */
  static async getExpiringContracts(): Promise<ExpiringContractsData[]> {
    return await apiClient.get<ExpiringContractsData[]>(
      "/api/dashboard/expiring-contracts"
    );
  }

  /**
   * Get auto-renewals data
   */
  static async getAutoRenewals(): Promise<AutoRenewalsData[]> {
    return await apiClient.get<AutoRenewalsData[]>(
      "/api/dashboard/auto-renewals"
    );
  }

  /**
   * Get auto-renewals by classification data
   */
  static async getAutoRenewalsByClassification(): Promise<
    AutoRenewalsByClassification[]
  > {
    return await apiClient.get<AutoRenewalsByClassification[]>(
      "/api/dashboard/auto-renewals-by-classification"
    );
  }

  /**
   * Get portfolio overview data for all 8 charts
   */
  static async getPortfolioOverview(): Promise<PortfolioOverviewData> {
    return await apiClient.get<PortfolioOverviewData>(
      "/api/dashboard/portfolio-overview"
    );
  }
}

/**
 * Utility functions for dashboard data processing
 */
export class DashboardUtils {
  /**
   * Format currency value
   */
  static formatCurrency(value: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  /**
   * Format large numbers with K, M, B suffixes
   */
  static formatLargeNumber(value: number): string {
    if (value >= 1000000000) {
      return (value / 1000000000).toFixed(1) + "B";
    } else if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + "M";
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + "K";
    }
    return value.toString();
  }

  /**
   * Calculate percentage change
   */
  static calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * Get confidence color based on percentage
   */
  static getConfidenceColor(confidence: number): string {
    if (confidence === -1) return "text-blue-600";
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.5) return "text-yellow-600";
    return "text-red-600";
  }

  /**
   * Get confidence badge variant
   */
  static getConfidenceBadgeVariant(
    confidence: number
  ): "default" | "secondary" | "destructive" {
    if (confidence >= 0.8) return "default";
    if (confidence >= 0.5) return "secondary";
    return "destructive";
  }

  /**
   * Format confidence percentage
   */
  static formatConfidence(confidence: number): string {
    if (confidence === -1) {
      return "Manual";
    }
    return `${Math.round(confidence * 100)}%`;
  }

  /**
   * Calculate days until date
   */
  static getDaysUntil(dateString: string): number {
    const targetDate = new Date(dateString);
    const today = new Date();
    const diffTime = targetDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get status based on end date
   */
  static getContractStatus(
    endDate: string | null
  ): "Active" | "Expired" | "Expiring Soon" | "Unknown" {
    if (!endDate) return "Unknown";

    const daysUntil = this.getDaysUntil(endDate);

    if (daysUntil < 0) return "Expired";
    if (daysUntil <= 30) return "Expiring Soon";
    return "Active";
  }

  /**
   * Get status badge variant
   */
  static getStatusBadgeVariant(
    status: string
  ): "default" | "secondary" | "destructive" | "outline" {
    switch (status) {
      case "Active":
        return "default";
      case "Expiring Soon":
        return "secondary";
      case "Expired":
        return "destructive";
      default:
        return "outline";
    }
  }

  /**
   * Generate chart colors for data visualization
   */
  static generateChartColors(count: number): string[] {
    const baseColors = [
      "#10B981", // green
      "#3B82F6", // blue
      "#F59E0B", // amber
      "#EF4444", // red
      "#8B5CF6", // violet
      "#06B6D4", // cyan
      "#F97316", // orange
      "#84CC16", // lime
      "#EC4899", // pink
      "#6B7280", // gray
    ];

    const colors = [];
    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
  }

  /**
   * Sort data for charts
   */
  static sortChartData<T extends { value: number }>(
    data: T[],
    ascending: boolean = false
  ): T[] {
    return [...data].sort((a, b) =>
      ascending ? a.value - b.value : b.value - a.value
    );
  }

  /**
   * Limit chart data and group others
   */
  static limitChartData<T extends { name: string; value: number }>(
    data: T[],
    limit: number = 10
  ): T[] {
    if (data.length <= limit) return data;

    const sorted = this.sortChartData(data);
    const top = sorted.slice(0, limit - 1);
    const others = sorted.slice(limit - 1);

    const othersSum = others.reduce((sum, item) => sum + item.value, 0);

    return [...top, { name: "Others", value: othersSum } as T];
  }
}
