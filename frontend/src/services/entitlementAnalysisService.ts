/**
 * Entitlement Analysis Service
 * Handles API calls for entitlement analysis functionality
 */

import { apiClient } from "@/lib/api-client";

export interface Provider {
  name: string;
}

export interface ContractForAnalysis {
  id: string;
  title: string;
  fileName: string;
  hasAnalysisFields: boolean;
  provider: string;
  canExtractFields?: boolean; // Has document for extraction
  lastExtractionAttempt?: string;
  extractionStatus?: 'never_attempted' | 'in_progress' | 'completed' | 'failed';
}

export interface AnalysisData {
  contractId: string;
  contractName: string;
  fileName: string;
  _purchasingItemIndex?: number;
  _isMultiItem?: boolean;
  [key: string]: any; // Analysis fields with confidence data
}

export interface AnalysisFieldMetadata {
  value: any;
  confidence: number;
  isContractLevel: boolean;
}

export interface AnalysisResponse {
  data: AnalysisData[];
  fields: string[];
  total: number;
}

export interface ExtractionJobRequest {
  contractIds: string[];
  forceReExtraction?: boolean;
}

export interface ExtractionJobResponse {
  jobId: string;
  contractsQueued: number;
  skipped: number;
  estimatedTimeSeconds: number;
  statusEndpoint: string;
  message: string;
}

export interface ExtractionJobStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: {
    total: number;
    completed: number;
    failed: number;
    current?: string;
  };
  results: {
    contractId: string;
    status: 'success' | 'failed';
    error?: string;
    fieldsExtracted?: number;
  }[];
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
}

export interface UpdateFieldRequest {
  contractId: string;
  fieldKey: string;
  newValue: string;
  isContractLevel: boolean;
  purchasingItemIndex?: number;
}

export interface UpdateFieldResponse {
  success: boolean;
  message: string;
}

export class EntitlementAnalysisService {
  /**
   * Gets all unique providers/suppliers
   * @returns List of providers
   */
  async getProviders(): Promise<string[]> {
    try {
      const response = await apiClient.get(
        "/api/entitlement-analysis/providers"
      );
      return response.providers || [];
    } catch (error) {
      console.error("Error fetching providers:", error);
      throw error;
    }
  }

  /**
   * Gets contracts by provider
   * @param provider Provider name (first word)
   * @returns List of contracts for the provider
   */
  async getContractsByProvider(
    provider: string
  ): Promise<ContractForAnalysis[]> {
    try {
      const response = await apiClient.get(
        `/api/entitlement-analysis/contracts/${encodeURIComponent(provider)}`
      );
      return response.contracts || [];
    } catch (error) {
      console.error(
        `Error fetching contracts for provider ${provider}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Gets entitlement analysis data for selected contracts
   * @param contractIds Array of contract IDs
   * @returns Analysis data
   */
  async getAnalysisData(contractIds: string[]): Promise<AnalysisResponse> {
    try {
      const response = await apiClient.post("/api/entitlement-analysis/data", {
        contractIds,
      });
      return response;
    } catch (error) {
      console.error("Error fetching analysis data:", error);
      throw error;
    }
  }

  /**
   * Exports entitlement analysis data
   * @param contractIds Array of contract IDs
   * @param format Export format (default: xlsx)
   * @returns Blob for download
   */
  async exportAnalysisData(
    contractIds: string[],
    format: string = "xlsx"
  ): Promise<Blob> {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/entitlement-analysis/export`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
          body: JSON.stringify({
            contractIds,
            format,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      console.error("Error exporting analysis data:", error);
      throw error;
    }
  }

  /**
   * Updates an analysis field value
   * @param request Update field request
   * @returns Update response
   */
  async updateAnalysisField(
    request: UpdateFieldRequest
  ): Promise<UpdateFieldResponse> {
    try {
      const response = await apiClient.put(
        "/api/entitlement-analysis/update-field",
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating analysis field:", error);
      throw error;
    }
  }

  /**
   * Triggers analysis fields extraction for existing contracts
   * @param request Extraction job request
   * @returns Extraction job response
   */
  async extractAnalysisFields(
    request: ExtractionJobRequest
  ): Promise<ExtractionJobResponse> {
    try {
      const response = await apiClient.post(
        "/api/entitlement-analysis/extract-fields",
        request,
        { timeout: 600000 } // 10 minutes for AI extraction
      );
      return response;
    } catch (error) {
      console.error("Error starting analysis fields extraction:", error);
      throw error;
    }
  }

  /**
   * Extracts analysis fields for a single contract using OCR text from database
   * @param contractId Contract ID
   * @returns Extraction result
   */
  async extractAnalysisFieldsWithDocument(contractId: string): Promise<{
    success: boolean;
    analysisFields: any;
    message: string;
  }> {
    try {
      // Use the standard endpoint that automatically uses OCR text from database
      const response = await this.extractAnalysisFields({
        contractIds: [contractId],
        forceReExtraction: true
      });

      // Return a compatible response format
      return {
        success: true,
        analysisFields: {}, // The actual fields will be available after job completion
        message: `Analysis fields extraction job started with ID: ${response.jobId}`
      };
    } catch (error) {
      console.error("Error extracting analysis fields:", error);
      throw error;
    }
  }

  /**
   * Gets extraction job status
   * @param jobId Job ID to check
   * @returns Job status
   */
  async getExtractionStatus(jobId: string): Promise<ExtractionJobStatus> {
    try {
      const response = await apiClient.get(
        `/api/entitlement-analysis/extraction-status/${jobId}`
      );
      return response;
    } catch (error) {
      console.error("Error getting extraction status:", error);
      throw error;
    }
  }

  /**
   * Downloads the exported file
   * @param blob File blob
   * @param filename Filename for download
   */
  downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

// Export singleton instance
export const entitlementAnalysisService = new EntitlementAnalysisService();
