/**
 * Super Admin Service
 * API service for super admin functionality
 */

import { apiClient } from '@/lib/api-client';

export interface PlatformOverview {
  totals: {
    tenants: number;
    users: number;
    contracts: number;
  };
  active: {
    tenants: number;
    users: number;
  };
  recent: {
    tenants: number;
    users: number;
    contracts: number;
  };
  timestamp: string;
}

export interface TenantInfo {
  id: string;
  name: string;
  tier: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  userCount: number;
  contractCount: number;
}

export interface UserInfo {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  createdAt: string;
  tenantUsers: Array<{
    tenant: {
      id: string;
      name: string;
      tier: string;
    };
    tenantRole: string;
    isActive: boolean;
  }>;
}

export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  database: {
    connections: number;
    maxConnections: number;
    queryTime: number;
  };
  storage: {
    used: number;
    total: number;
    percentage: number;
  };
}

export interface TenantAnalytics {
  tenantId: string;
  tenantName: string;
  metrics: {
    totalUsers: number;
    activeUsers: number;
    totalContracts: number;
    contractsThisMonth: number;
    storageUsed: number;
    lastActivity: string;
  };
}

class SuperAdminService {
  private baseUrl = '/api/super-admin';

  /**
   * Test super admin authentication
   */
  async testAuth(): Promise<any> {
    return apiClient.get(`${this.baseUrl}/test`);
  }

  /**
   * Get platform overview statistics
   */
  async getPlatformOverview(): Promise<PlatformOverview> {
    return apiClient.get(`${this.baseUrl}/platform/overview`);
  }

  /**
   * Get all tenants with basic information
   */
  async getAllTenants(): Promise<{ tenants: TenantInfo[]; total: number }> {
    return apiClient.get(`${this.baseUrl}/tenants`);
  }

  /**
   * Get all users across all tenants
   */
  async getAllUsers(): Promise<{ users: UserInfo[]; total: number }> {
    return apiClient.get(`${this.baseUrl}/users`);
  }

  /**
   * Get system metrics and performance data
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve({
      cpu: {
        usage: 45.2,
        cores: 8,
      },
      memory: {
        used: 12.5,
        total: 32,
        percentage: 39.1,
      },
      database: {
        connections: 25,
        maxConnections: 100,
        queryTime: 12.5,
      },
      storage: {
        used: 156.7,
        total: 500,
        percentage: 31.3,
      },
    });
  }

  /**
   * Get analytics for all tenants
   */
  async getTenantAnalytics(): Promise<TenantAnalytics[]> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve([
      {
        tenantId: '1',
        tenantName: 'Acme Corporation',
        metrics: {
          totalUsers: 25,
          activeUsers: 18,
          totalContracts: 156,
          contractsThisMonth: 12,
          storageUsed: 2.3,
          lastActivity: '2025-07-07T10:30:00Z',
        },
      },
      {
        tenantId: '2',
        tenantName: 'TechStart Inc',
        metrics: {
          totalUsers: 8,
          activeUsers: 6,
          totalContracts: 43,
          contractsThisMonth: 5,
          storageUsed: 0.8,
          lastActivity: '2025-07-07T09:15:00Z',
        },
      },
    ]);
  }

  /**
   * Get platform usage trends
   */
  async getUsageTrends(period: '7d' | '30d' | '90d' = '30d'): Promise<any> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve({
      period,
      data: [
        { date: '2025-06-07', users: 120, contracts: 45, tenants: 8 },
        { date: '2025-06-14', users: 135, contracts: 52, tenants: 9 },
        { date: '2025-06-21', users: 142, contracts: 58, tenants: 10 },
        { date: '2025-06-28', users: 158, contracts: 63, tenants: 11 },
        { date: '2025-07-05', users: 165, contracts: 71, tenants: 12 },
      ],
    });
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(limit: number = 50): Promise<any[]> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve([
      {
        id: '1',
        timestamp: '2025-07-07T10:30:00Z',
        userId: 'admin-1',
        action: 'USER_CREATED',
        resource: 'user:<EMAIL>',
        tenantId: 'tenant-1',
        details: 'Created new user account',
      },
      {
        id: '2',
        timestamp: '2025-07-07T10:25:00Z',
        userId: 'admin-1',
        action: 'TENANT_UPDATED',
        resource: 'tenant:acme-corp',
        tenantId: 'tenant-1',
        details: 'Updated tenant settings',
      },
    ]);
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<any> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve({
      status: 'healthy',
      services: {
        database: { status: 'healthy', responseTime: 12 },
        redis: { status: 'healthy', responseTime: 3 },
        storage: { status: 'healthy', responseTime: 8 },
        email: { status: 'healthy', responseTime: 45 },
      },
      uptime: '99.9%',
      lastCheck: new Date().toISOString(),
    });
  }

  /**
   * Update tenant status
   */
  async updateTenantStatus(tenantId: string, status: string): Promise<void> {
    return apiClient.put(`${this.baseUrl}/tenants/${tenantId}/status`, { status });
  }

  /**
   * Update user status
   */
  async updateUserStatus(userId: string, status: string): Promise<void> {
    return apiClient.put(`${this.baseUrl}/users/${userId}/status`, { status });
  }

  /**
   * Get platform configuration
   */
  async getPlatformConfig(): Promise<any> {
    // Mock data for now - replace with actual API call when backend is ready
    return Promise.resolve({
      features: {
        multiTenant: true,
        aiExtraction: true,
        analytics: true,
        notifications: true,
      },
      limits: {
        maxTenantsPerPlan: {
          basic: 1,
          pro: 5,
          enterprise: 50,
        },
        maxUsersPerTenant: {
          basic: 10,
          pro: 100,
          enterprise: 1000,
        },
      },
      security: {
        passwordPolicy: {
          minLength: 8,
          requireSpecialChars: true,
          requireNumbers: true,
        },
        sessionTimeout: 3600,
        maxLoginAttempts: 5,
      },
    });
  }
}

export const superAdminService = new SuperAdminService();
