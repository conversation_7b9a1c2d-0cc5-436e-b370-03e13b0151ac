/**
 * Contract Hierarchy Service
 * Handles API calls for contract hierarchy functionality
 */

import { apiClient } from "@/lib/api-client";

export interface ContractHierarchyInfo {
  level: number;
  parentTypes: string[];
  childTypes: string[];
  description: string;
}

export interface ContractWithHierarchy {
  id: string;
  title: string;
  agreementType: string;
  startDate?: string;
  endDate?: string;
  value?: string;
  provider?: string;
  hierarchyLevel?: number;
  parentContracts?: string[];
  childContracts?: string[];
  groupId?: string;
}

export interface HierarchyGroupResponse {
  groupId: string;
  contracts: ContractWithHierarchy[];
  totalContracts: number;
}

export interface HierarchyInfoResponse {
  agreementType: string;
  hierarchyInfo: ContractHierarchyInfo;
}

export interface HierarchyTypesResponse {
  hierarchyTypes: Record<string, ContractHierarchyInfo>;
  totalTypes: number;
}

export interface CanBeParentResponse {
  parentType: string;
  childType: string;
  canBeParent: boolean;
}

export class ContractHierarchyService {
  /**
   * Gets contracts with hierarchy information for a specific group
   * @param groupId Group identifier (e.g., "SupplierName:ContractNumber")
   * @returns Promise<HierarchyGroupResponse>
   */
  async getContractsWithHierarchy(
    groupId: string
  ): Promise<HierarchyGroupResponse> {
    try {
      const response = await apiClient.get<HierarchyGroupResponse>(
        `/api/contracts/hierarchy/${encodeURIComponent(groupId)}`
      );
      return response;
    } catch (error) {
      console.error("Error fetching contracts with hierarchy:", error);
      throw new Error("Failed to fetch contract hierarchy data");
    }
  }

  /**
   * Gets hierarchy information for a specific agreement type
   * @param agreementType Agreement type (e.g., "MSA", "SOW", "PO")
   * @returns Promise<HierarchyInfoResponse>
   */
  async getHierarchyInfo(
    agreementType: string
  ): Promise<HierarchyInfoResponse> {
    try {
      const response = await apiClient.get<HierarchyInfoResponse>(
        `/api/contracts/hierarchy-info/${encodeURIComponent(agreementType)}`
      );
      return response;
    } catch (error) {
      console.error("Error fetching hierarchy info:", error);
      throw new Error("Failed to fetch hierarchy information");
    }
  }

  /**
   * Gets all supported hierarchy types
   * @returns Promise<HierarchyTypesResponse>
   */
  async getAllHierarchyTypes(): Promise<HierarchyTypesResponse> {
    try {
      const response = await apiClient.get<HierarchyTypesResponse>(
        "/api/contracts/hierarchy-types"
      );
      return response;
    } catch (error) {
      console.error("Error fetching hierarchy types:", error);
      throw new Error("Failed to fetch hierarchy types");
    }
  }

  /**
   * Checks if one contract type can be a parent of another
   * @param parentType Parent agreement type
   * @param childType Child agreement type
   * @returns Promise<CanBeParentResponse>
   */
  async canBeParent(
    parentType: string,
    childType: string
  ): Promise<CanBeParentResponse> {
    try {
      const response = await apiClient.get<CanBeParentResponse>(
        `/api/contracts/can-be-parent/${encodeURIComponent(
          parentType
        )}/${encodeURIComponent(childType)}`
      );
      return response;
    } catch (error) {
      console.error("Error checking parent-child relationship:", error);
      throw new Error("Failed to check parent-child relationship");
    }
  }

  /**
   * Gets hierarchy level for a specific agreement type
   * @param agreementType Agreement type
   * @returns Promise<number | null>
   */
  async getHierarchyLevel(agreementType: string): Promise<number | null> {
    try {
      const hierarchyInfo = await this.getHierarchyInfo(agreementType);
      return hierarchyInfo.hierarchyInfo.level;
    } catch (error) {
      // If hierarchy info is not found, return null
      return null;
    }
  }

  /**
   * Validates if a contract hierarchy relationship is valid
   * @param parentType Parent agreement type
   * @param childType Child agreement type
   * @returns Promise<boolean>
   */
  async validateHierarchyRelationship(
    parentType: string,
    childType: string
  ): Promise<boolean> {
    try {
      const result = await this.canBeParent(parentType, childType);
      return result.canBeParent;
    } catch (error) {
      console.error("Error validating hierarchy relationship:", error);
      return false;
    }
  }

  /**
   * Gets hierarchy description for display purposes
   * @param agreementType Agreement type
   * @returns Promise<string>
   */
  async getHierarchyDescription(agreementType: string): Promise<string> {
    try {
      const hierarchyInfo = await this.getHierarchyInfo(agreementType);
      return hierarchyInfo.hierarchyInfo.description;
    } catch (error) {
      return `${agreementType} document`;
    }
  }

  /**
   * Groups contracts by their hierarchy relationships
   * @param contracts Array of contracts with hierarchy information
   * @returns Grouped contracts by hierarchy level
   */
  groupContractsByHierarchy(
    contracts: ContractWithHierarchy[]
  ): Record<number, ContractWithHierarchy[]> {
    return contracts.reduce((groups, contract) => {
      const level = contract.hierarchyLevel || 1;
      if (!groups[level]) {
        groups[level] = [];
      }
      groups[level].push(contract);
      return groups;
    }, {} as Record<number, ContractWithHierarchy[]>);
  }

  /**
   * Finds root contracts (contracts without parents) in a group
   * @param contracts Array of contracts with hierarchy information
   * @returns Array of root contracts
   */
  findRootContracts(
    contracts: ContractWithHierarchy[]
  ): ContractWithHierarchy[] {
    return contracts.filter(
      (contract) =>
        !contract.parentContracts || contract.parentContracts.length === 0
    );
  }

  /**
   * Finds child contracts for a specific parent contract
   * @param parentId Parent contract ID
   * @param contracts Array of contracts with hierarchy information
   * @returns Array of child contracts
   */
  findChildContracts(
    parentId: string,
    contracts: ContractWithHierarchy[]
  ): ContractWithHierarchy[] {
    return contracts.filter(
      (contract) =>
        contract.parentContracts && contract.parentContracts.includes(parentId)
    );
  }

  /**
   * Builds a tree structure from flat contract hierarchy data
   * @param contracts Array of contracts with hierarchy information
   * @returns Tree structure with parent-child relationships
   */
  buildHierarchyTree(
    contracts: ContractWithHierarchy[]
  ): ContractHierarchyTree[] {
    const contractMap = new Map(contracts.map((c) => [c.id, c]));
    const roots: ContractHierarchyTree[] = [];

    contracts.forEach((contract) => {
      if (!contract.parentContracts || contract.parentContracts.length === 0) {
        // This is a root contract
        roots.push({
          contract,
          children: this.buildChildrenTree(contract.id, contracts, contractMap),
        });
      }
    });

    return roots;
  }

  /**
   * Recursively builds children tree for a contract
   * @param parentId Parent contract ID
   * @param allContracts All contracts in the group
   * @param contractMap Map for quick contract lookup
   * @returns Array of child contract trees
   */
  private buildChildrenTree(
    parentId: string,
    allContracts: ContractWithHierarchy[],
    contractMap: Map<string, ContractWithHierarchy>
  ): ContractHierarchyTree[] {
    const children = this.findChildContracts(parentId, allContracts);

    return children.map((child) => ({
      contract: child,
      children: this.buildChildrenTree(child.id, allContracts, contractMap),
    }));
  }

  /**
   * Calculates timeline bounds for a group of contracts
   * @param contracts Array of contracts
   * @returns Timeline bounds with start and end dates
   */
  calculateTimelineBounds(
    contracts: ContractWithHierarchy[]
  ): { start: Date; end: Date } | null {
    const dates = contracts
      .flatMap((c) => [c.startDate, c.endDate])
      .filter(Boolean)
      .map((d) => new Date(d!));

    if (dates.length === 0) return null;

    return {
      start: new Date(Math.min(...dates.map((d) => d.getTime()))),
      end: new Date(Math.max(...dates.map((d) => d.getTime()))),
    };
  }
}

export interface ContractHierarchyTree {
  contract: ContractWithHierarchy;
  children: ContractHierarchyTree[];
}

// Export singleton instance
export const contractHierarchyService = new ContractHierarchyService();
