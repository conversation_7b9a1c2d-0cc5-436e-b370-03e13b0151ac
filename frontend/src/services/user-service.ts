/**
 * User Service
 * Provides API functions for user management
 */

import { apiClient } from "@/lib/api-client";

export interface User {
  id: string;
  name: string;
  email: string;
  status: string;
  systemRole: string;
  tenantRole: string;
  role?: {
    id: string;
    name: string;
    description?: string;
  };
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  createdBy?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserInput {
  name: string;
  email: string;
  password: string;
  roleId?: string;
}

export interface UpdateUserInput {
  name?: string;
  email?: string;
  status?: string;
  roleId?: string;
}

export interface CreateRoleInput {
  name: string;
  description?: string;
  permissions: string[];
  isDefault: boolean;
}

export interface UpdateRoleInput {
  name?: string;
  description?: string;
  permissions?: string[];
  isDefault?: boolean;
}

/**
 * User Service class for managing users and roles
 */
export class UserService {
  /**
   * Get all users in the tenant
   * @returns Array of users
   */
  static async getUsers(): Promise<User[]> {
    return apiClient.get<User[]>("/api/users");
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns User
   */
  static async getUser(id: string): Promise<User> {
    return apiClient.get<User>(`/api/users/${id}`);
  }

  /**
   * Create a new user
   * @param data User data
   * @returns Created user
   */
  static async createUser(data: CreateUserInput): Promise<User> {
    return apiClient.post<User>("/api/users", data);
  }

  /**
   * Update a user
   * @param id User ID
   * @param data User data
   * @returns Updated user
   */
  static async updateUser(id: string, data: UpdateUserInput): Promise<User> {
    return apiClient.put<User>(`/api/users/${id}`, data);
  }

  /**
   * Delete a user
   * @param id User ID
   */
  static async deleteUser(id: string): Promise<void> {
    return apiClient.delete(`/api/users/${id}`);
  }

  /**
   * Get all roles in the tenant
   * @returns Array of roles
   */
  static async getRoles(): Promise<Role[]> {
    return apiClient.get<Role[]>("/api/roles");
  }

  /**
   * Get a role by ID
   * @param id Role ID
   * @returns Role
   */
  static async getRole(id: string): Promise<Role> {
    return apiClient.get<Role>(`/api/roles/${id}`);
  }

  /**
   * Create a new role
   * @param data Role data
   * @returns Created role
   */
  static async createRole(data: CreateRoleInput): Promise<Role> {
    return apiClient.post<Role>("/api/roles", data);
  }

  /**
   * Update a role
   * @param id Role ID
   * @param data Role data
   * @returns Updated role
   */
  static async updateRole(id: string, data: UpdateRoleInput): Promise<Role> {
    return apiClient.put<Role>(`/api/roles/${id}`, data);
  }

  /**
   * Delete a role
   * @param id Role ID
   */
  static async deleteRole(id: string): Promise<void> {
    return apiClient.delete(`/api/roles/${id}`);
  }
}
