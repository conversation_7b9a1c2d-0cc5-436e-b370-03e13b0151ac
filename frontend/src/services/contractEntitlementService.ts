/**
 * Contract Entitlement Service
 * Handles API calls for individual contract entitlements
 */

import { apiClient } from "@/lib/api-client";

export interface ContractValueCard {
  value: string;
  currency: string;
}

export interface ContractTermCard {
  term: string;
}

export interface RenewalNoticeCard {
  noticePeriod: string;
  autoRenewal: string;
}

export interface DiscountCard {
  discountLevel: string;
  discountPercentage: string;
  discountFieldKey: string;
}

export interface SKUItem {
  [key: string]: string;
}

export interface UseRightsLegal {
  limitations: string;
  includedRights: string;
  metricDefinition: string;
  governingAgreement: string;
  supportTerms: string;
  entitledEntity: string;
  // Special fields from mapping.json
  territorialScope: string;
  thirdPartyUsage: string;
  licenceMobility: string;
  ipOwnership: string;
  rampedPricingConcession: string;
  warrantyDisclaimer: string;
}

export interface CommercialEntitlements {
  totalContractValue: string;
  supplierDiscountLevel: string;
}

export interface ContractDetails {
  contractTerm: string;
  fixedPricing: string;
  trueUpOrder: string;
  invoiceSchedule: string;
}

export interface YearWisePurchasing {
  [year: string]: SKUItem[];
}

export interface ContractEntitlements {
  contractValue: ContractValueCard;
  contractTerm: ContractTermCard;
  renewalNotice: RenewalNoticeCard;
  discount: DiscountCard;
  skuBreakdown: SKUItem[];
  yearWisePurchasing: YearWisePurchasing;
  useRightsLegal: UseRightsLegal;
}

export class ContractEntitlementService {
  /**
   * Get entitlements for a specific contract
   */
  async getContractEntitlements(contractId: string): Promise<ContractEntitlements> {
    try {
      const response = await apiClient.get(`/api/contracts/${contractId}/entitlements`);
      return response;
    } catch (error) {
      console.error("Error fetching contract entitlements:", error);
      throw new Error("Failed to fetch contract entitlements");
    }
  }
}

// Export singleton instance
export const contractEntitlementService = new ContractEntitlementService();
