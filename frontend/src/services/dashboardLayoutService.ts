/**
 * Dashboard Layout Service
 * API service for managing dashboard layouts
 */

import { apiClient } from "@/lib/api-client";
import { DashboardLayout, DashboardWidget, GroupedWidgets } from "@/types/dashboard";

export interface DashboardLayoutResponse {
  id: string;
  userId: string;
  name: string;
  description?: string;
  widgets: any[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SaveLayoutRequest {
  name?: string;
  description?: string;
  widgets: any[];
  isDefault?: boolean;
}

export class DashboardLayoutService {
  /**
   * Get user's dashboard layout
   */
  static async getLayout(): Promise<DashboardLayoutResponse> {
    const response = await apiClient.get("/api/dashboard/layout");
    return response.data;
  }

  /**
   * Save user's dashboard layout
   */
  static async saveLayout(
    data: SaveLayoutRequest
  ): Promise<DashboardLayoutResponse> {
    const response = await apiClient.post("/api/dashboard/layout", data);
    return response.data;
  }

  /**
   * Update user's dashboard layout
   */
  static async updateLayout(
    layoutId: string,
    data: Partial<SaveLayoutRequest>
  ): Promise<DashboardLayoutResponse> {
    const response = await apiClient.put(
      `/api/dashboard/layout/${layoutId}`,
      data
    );
    return response.data;
  }

  /**
   * Delete user's dashboard layout
   */
  static async deleteLayout(layoutId: string): Promise<{ message: string }> {
    const response = await apiClient.delete(
      `/api/dashboard/layout/${layoutId}`
    );
    return response.data;
  }

  /**
   * Get user's dashboard widgets grouped by category
   */
  static async getWidgets(): Promise<GroupedWidgets> {
    try {
      // Add timestamp to prevent caching
      const timestamp = Date.now();

      // Call both category endpoints in parallel
      const [priorityResponse, portfolioResponse] = await Promise.all([
        apiClient.get(`/api/dashboard/widgets/category/priority?t=${timestamp}`),
        apiClient.get(`/api/dashboard/widgets/category/portfolio?t=${timestamp}`)
      ]);

      const groupedWidgets: GroupedWidgets = {
        priority: priorityResponse || [],
        portfolio: portfolioResponse || []
      };

      return groupedWidgets;
    } catch (error: any) {
      console.error('DashboardLayoutService: Error in getWidgets:', error);
      throw error;
    }
  }

  /**
   * Get widgets by category
   */
  static async getWidgetsByCategory(category: 'priority' | 'portfolio'): Promise<DashboardWidget[]> {
    const response = await apiClient.get(`/api/dashboard/widgets/category/${category}`);
    return response.data;
  }

  /**
   * Update widget visibility
   */
  static async updateWidgetVisibility(widgetId: string, isVisible: boolean): Promise<DashboardWidget> {
    const response = await apiClient.put(`/api/dashboard/widgets/${widgetId}/visibility`, {
      isVisible
    });
    return response.data;
  }

  /**
   * Update widget configuration
   */
  static async updateWidgetConfiguration(widgetId: string, configuration: any): Promise<DashboardWidget> {
    const response = await apiClient.put(`/api/dashboard/widgets/${widgetId}/configuration`, {
      configuration
    });
    return response.data;
  }

  /**
   * Bulk update widgets
   */
  static async bulkUpdateWidgets(updates: Array<{ id: string; data: Partial<DashboardWidget> }>): Promise<DashboardWidget[]> {
    const response = await apiClient.put("/api/dashboard/widgets/bulk", {
      updates
    });
    return response.data;
  }

  /**
   * Reset user widgets to defaults
   */
  static async resetWidgets(): Promise<GroupedWidgets> {
    const response = await apiClient.post("/api/dashboard/widgets/reset");
    return response.data;
  }
}
