/**
 * Folder Service
 * Provides API methods for folder management
 */

import { apiClient } from "@/lib/api-client";

// Folder interface
export interface Folder {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  createdById?: string;
}

// Folder with contract count interface
export interface FolderWithContractCount {
  folder: Folder;
  contractCount: number;
  contracts?: any[]; // Contract data from API
}

// Folder search params interface
export interface FolderSearchParams {
  name?: string;
  page?: number;
  limit?: number;
}

// Folder list result interface
export interface FolderListResult {
  folders: FolderWithContractCount[];
  total: number;
  page: number;
  limit: number;
}

// Create folder data interface
export interface CreateFolderData {
  name: string;
}

// Update folder data interface
export interface UpdateFolderData {
  name: string;
}

// Move contracts to folder data interface
export interface MoveContractsToFolderData {
  contractIds: string[];
  folderId: string | null;
}

// Folder service class
export class FolderService {
  /**
   * Gets all folders with optional search parameters
   * @param params Search parameters
   * @returns List of folders with contract counts and total count
   */
  async getFolders(params?: FolderSearchParams): Promise<FolderListResult> {
    try {
      const queryParams = new URLSearchParams();

      // Add search parameters to query
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const query = queryParams.toString() ? `?${queryParams.toString()}` : "";
      return await apiClient.get(`/api/folders${query}`);
    } catch (error) {
      console.error("Error fetching folders:", error);
      throw error;
    }
  }

  /**
   * Gets all providers and their contracts grouped as virtual folders
   * @returns List of provider-based virtual folders with contract counts
   */
  async getFoldersByProviders(): Promise<FolderListResult> {
    try {
      return await apiClient.get("/api/folders/by-providers");
    } catch (error) {
      console.error("Error fetching folders by providers:", error);
      throw error;
    }
  }

  /**
   * Gets folders for autocomplete (lightweight)
   * @param search Optional search term
   * @returns Array of folders with minimal data
   */
  async getFoldersForAutocomplete(search?: string): Promise<Folder[]> {
    try {
      const url = search
        ? `/api/folders/autocomplete?search=${encodeURIComponent(search)}`
        : "/api/folders/autocomplete";

      const response = await apiClient.get(url);
      return response.folders || [];
    } catch (error) {
      console.error("Error fetching folders for autocomplete:", error);
      throw error;
    }
  }

  /**
   * Gets a folder by ID
   * @param id Folder ID
   * @returns Folder data
   */
  async getFolder(id: string): Promise<Folder> {
    try {
      return await apiClient.get(`/api/folders/${id}`);
    } catch (error) {
      console.error(`Error fetching folder ${id}:`, error);
      throw error;
    }
  }

  /**
   * Creates a new folder
   * @param folderData Folder creation data
   * @returns Created folder
   */
  async createFolder(folderData: CreateFolderData): Promise<Folder> {
    try {
      return await apiClient.post("/api/folders", folderData);
    } catch (error) {
      console.error("Error creating folder:", error);
      throw error;
    }
  }

  /**
   * Updates an existing folder
   * @param id Folder ID
   * @param folderData Update data
   * @returns Updated folder
   */
  async updateFolder(
    id: string,
    folderData: UpdateFolderData
  ): Promise<Folder> {
    try {
      return await apiClient.put(`/api/folders/${id}`, folderData);
    } catch (error) {
      console.error(`Error updating folder ${id}:`, error);
      throw error;
    }
  }

  /**
   * Deletes a folder
   * @param id Folder ID
   * @returns Success message
   */
  async deleteFolder(id: string): Promise<{ message: string }> {
    try {
      return await apiClient.delete(`/api/folders/${id}`);
    } catch (error) {
      console.error(`Error deleting folder ${id}:`, error);
      throw error;
    }
  }

  /**
   * Moves contracts to a folder
   * @param data Move contracts data
   * @returns Success message with moved count
   */
  async moveContractsToFolder(data: MoveContractsToFolderData): Promise<{
    message: string;
    movedCount: number;
  }> {
    try {
      return await apiClient.post("/api/folders/move-contracts", data);
    } catch (error) {
      console.error("Error moving contracts to folder:", error);
      throw error;
    }
  }

  /**
   * Creates a folder if it doesn't exist, or returns existing folder
   * @param name Folder name
   * @returns Folder (existing or newly created)
   */
  async createOrGetFolder(name: string): Promise<Folder> {
    try {
      // First try to find existing folder
      const folders = await this.getFoldersForAutocomplete(name);
      const existingFolder = folders.find(
        (folder) => folder.name.toLowerCase() === name.toLowerCase()
      );

      if (existingFolder) {
        return existingFolder;
      }

      // Create new folder if not found
      return await this.createFolder({ name });
    } catch (error) {
      console.error("Error creating or getting folder:", error);
      throw error;
    }
  }
}

// Create a singleton instance
export const folderService = new FolderService();
