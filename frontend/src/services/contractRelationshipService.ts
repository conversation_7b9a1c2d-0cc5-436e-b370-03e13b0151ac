/**
 * Contract Relationship Service
 * Frontend service for fetching contract relationship data
 */

import { apiClient } from "@/lib/api-client";
import {
  SupplierContractRelationshipsResponse,
  ContractRelationshipCriteria,
} from "@/types/contract-relationships";

export class ContractRelationshipService {
  /**
   * Fetches contract relationships for a specific supplier
   */
  static async getSupplierContractRelationships(
    supplierName: string,
    criteria?: Partial<ContractRelationshipCriteria>
  ): Promise<SupplierContractRelationshipsResponse> {
    try {
      // Build query parameters from criteria
      const queryParams = new URLSearchParams();

      if (criteria) {
        Object.entries(criteria).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const queryString = queryParams.toString();
      const url = `/api/suppliers/${encodeURIComponent(
        supplierName
      )}/contract-relationships${queryString ? `?${queryString}` : ""}`;

      const response =
        await apiClient.get<SupplierContractRelationshipsResponse>(url);
      return response;
    } catch (error) {
      console.error("Error fetching supplier contract relationships:", error);
      throw new Error("Failed to fetch contract relationships");
    }
  }

  /**
   * Gets available suppliers for relationship analysis
   */
  static async getAvailableSuppliers(): Promise<string[]> {
    try {
      // This would typically come from a dedicated endpoint
      // For now, we'll use the existing contracts endpoint to get unique suppliers
      const response = await apiClient.get(
        "/api/contracts/with-extraction?limit=1000"
      );

      if (response?.contracts) {
        const suppliers = new Set<string>();
        response.contracts.forEach((contract: any) => {
          const supplier =
            contract.extraction?.fixedFields?.supplier?.value ||
            contract.extraction?.fixedFields?.counterparty?.value;
          if (supplier && typeof supplier === "string") {
            suppliers.add(supplier);
          }
        });
        return Array.from(suppliers).sort();
      }

      return [];
    } catch (error) {
      console.error("Error fetching available suppliers:", error);
      return [];
    }
  }

  /**
   * Validates supplier name exists
   */
  static async validateSupplier(supplierName: string): Promise<boolean> {
    try {
      const suppliers = await this.getAvailableSuppliers();
      return suppliers.includes(supplierName);
    } catch (error) {
      console.error("Error validating supplier:", error);
      return false;
    }
  }

  /**
   * Deletes a manual reporting relationship
   */
  static async deleteRelationship(
    contractId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.delete(
        `/api/contracts/${contractId}/reporting-to`
      );
      return response;
    } catch (error) {
      console.error("Error deleting relationship:", error);
      throw new Error("Failed to delete relationship");
    }
  }
}
