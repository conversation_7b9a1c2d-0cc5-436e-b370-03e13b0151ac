/**
 * Contract Extraction Service
 * Handles API calls for three-tier contract extraction data
 */

import { apiClient } from "@/lib/api-client";
import { ContractStatus } from "./contractService";

export interface FieldValue {
  value: string;
  confidence: number;
}

export interface DynamicField {
  value: string;
  description: string;
  confidence: number;
}

export interface FixedFields {
  agreement_type: FieldValue;
  provider: FieldValue;
  client: FieldValue;
  product: FieldValue;
  total_amount: FieldValue;
  annually_amount: FieldValue; // Year-by-year breakdown of contract value
  start_date: FieldValue;
  end_date: FieldValue;
  contract_id: FieldValue;
  contract_classification: FieldValue;
  // Contract status and term (LLM-extracted)
  contract_status: FieldValue; // Active/Inactive/Unknown - extracted by LLM
  contract_term: FieldValue; // Duration extracted by LLM or calculated from dates
  // Renewal fields
  auto_renewal: FieldValue;
  renewal_notice_period: FieldValue;
  // Document relationships
  relationships: FieldValue;
  // File metadata
  original_filename: FieldValue;
}

export interface DynamicFields {
  [key: string]: DynamicField;
}

export interface DynamicCategoryFields {
  [fieldName: string]: DynamicField;
}

export interface DynamicCategoricalFields {
  "Use rights & restrictions"?: DynamicCategoryFields;
  "General"?: DynamicCategoryFields;
  "Legal terms"?: DynamicCategoryFields;
  "Commercial terms"?: DynamicCategoryFields;
  "Data protection"?: DynamicCategoryFields;
  "Others"?: DynamicCategoryFields;
}

export interface CategoryFields {
  [fieldName: string]: FieldValue;
}

export interface SupplierCategoricalFields {
  "Use rights & restrictions"?: CategoryFields;
  General?: CategoryFields;
  "Legal terms"?: CategoryFields;
  "Commercial terms"?: CategoryFields;
  "Data protection"?: CategoryFields;
  Others?: CategoryFields;
}

export interface SpecialFields {
  oracle?: SupplierCategoricalFields;
  microsoft?: SupplierCategoricalFields;
  sap?: SupplierCategoricalFields;
  "red-hat"?: SupplierCategoricalFields;
  salesforce?: SupplierCategoricalFields;
  servicenow?: SupplierCategoricalFields;
  [supplierName: string]: SupplierCategoricalFields | undefined;
}

export interface DocumentSummary {
  value: string;
  confidence: number;
  extractionDate: string;
  processingTimeMs?: number;
}

export interface ComplianceClause {
  present: "Yes" | "No" | "Unclear";
  summary: string;
  complianceRisk: "Low" | "Medium" | "High";
}

export interface DORACompliance {
  criticalFunctions: ComplianceClause;
  subcontracting: ComplianceClause;
  auditRights: ComplianceClause;
  terminationRights: ComplianceClause;
  exitStrategy: ComplianceClause;
  incidentNotification: ComplianceClause;
  dataLocation: ComplianceClause;
  businessContinuity: ComplianceClause;
  securityMeasures: ComplianceClause;
  regulatoryCooperation: ComplianceClause;
  liability: ComplianceClause;
  serviceLevelAgreements: ComplianceClause;
}

export interface ComplianceAnalysis {
  dora?: DORACompliance;
  esg?: any; // Will be defined when ESG compliance is implemented
  extractionDate?: string;
  processingTimeMs?: number;
  overallRisk?: "Low" | "Medium" | "High";
}

export interface AnalysisFields {
  [key: string]: FieldValue | string | number | undefined;
  extractionDate?: string;
  processingTimeMs?: number;
}

export interface BasicContractInfo {
  id: string;
  title: string;
  description?: string;
  fileName?: string;
  status?: ContractStatus;
  createdAt?: string;
  updatedAt?: string;
}

export interface ContractExtractionData {
  id: string;
  contractId: string;
  tenantId: string;
  fixedFields: FixedFields;
  dynamicFields: DynamicCategoricalFields; // Always categorized format
  specialFields: SpecialFields;
  documentSummary?: DocumentSummary;
  complianceAnalysis?: ComplianceAnalysis;
  integrityAnalysis?: any;
  analysisFields?: AnalysisFields;
  extractionDate: string;
  extractionVersion: string;
  overallConfidence?: number;
  processingTimeMs?: number;
  modelUsed?: string;
  folderId?: string; // Auto-assigned folder based on extraction data
  createdAt: string;
  updatedAt: string;
  contractInfo?: BasicContractInfo;
}

export interface ExtractionStats {
  totalExtractions: number;
  averageConfidence: number;
  highConfidenceCount: number;
  lowConfidenceCount: number;
  latestExtractionDate: string | null;
}

export interface CreateExtractionParams {
  documentBuffer: string; // base64 encoded
  fileName: string;
}

export interface UpdateExtractionParams {
  fixedFields?: FixedFields;
  dynamicFields?: DynamicFields;
  specialFields?: SpecialFields;
}

export class ContractExtractionService {
  /**
   * Gets three-tier extraction data for a contract
   * @param contractId Contract ID
   * @returns Extraction data or null if not found
   */
  async getContractExtraction(
    contractId: string
  ): Promise<ContractExtractionData | null> {
    try {
      const response = await apiClient.get(
        `/api/contracts/${contractId}/extraction`
      );
      return response;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      console.error(
        `Error fetching extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Creates three-tier extraction for a contract
   * @param contractId Contract ID
   * @param params Creation parameters
   * @returns Created extraction data
   */
  async createContractExtraction(
    contractId: string,
    params: CreateExtractionParams
  ): Promise<ContractExtractionData> {
    try {
      const response = await apiClient.post(
        `/api/contracts/${contractId}/extraction`,
        params
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error creating extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Updates three-tier extraction for a contract
   * @param contractId Contract ID
   * @param params Update parameters
   * @returns Updated extraction data
   */
  async updateContractExtraction(
    contractId: string,
    params: UpdateExtractionParams
  ): Promise<ContractExtractionData> {
    try {
      const response = await apiClient.put(
        `/api/contracts/${contractId}/extraction`,
        params
      );
      return response;
    } catch (error) {
      console.error(
        `Error updating extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Partially updates specific fields in three-tier extraction data
   * @param contractId Contract ID
   * @param data Field updates data
   * @returns Updated extraction data
   */
  async patchContractExtraction(
    contractId: string,
    data: {
      fieldUpdates: Array<{
        fieldType: string;
        fieldKey: string;
        newKey: string;
        newValue: string;
        description?: string;
        vendor?: string;
      }>;
    }
  ): Promise<ContractExtractionData> {
    try {
      const response = await apiClient.patch(
        `/api/contracts/${contractId}/extraction`,
        data
      );
      return response;
    } catch (error) {
      console.error(
        `Error patching extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Deletes three-tier extraction for a contract
   * @param contractId Contract ID
   */
  async deleteContractExtraction(contractId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/contracts/${contractId}/extraction`);
    } catch (error) {
      console.error(
        `Error deleting extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Refreshes contract analysis by clearing all existing data and re-running complete analysis workflow
   * @param contractId Contract ID
   * @returns Refreshed extraction data
   */
  async refreshContractExtraction(
    contractId: string
  ): Promise<ContractExtractionData> {
    try {
      const response = await apiClient.post(
        `/api/contracts/${contractId}/extraction/refresh`,
        {},
        { timeout: 600000 } // 10 minutes for AI processing
      );
      return response;
    } catch (error) {
      console.error(
        `Error refreshing extraction for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Gets extraction statistics for the current tenant
   * @returns Extraction statistics
   */
  async getExtractionStats(): Promise<ExtractionStats> {
    try {
      const response = await apiClient.get("/api/extractions/stats");
      return response;
    } catch (error) {
      console.error("Error fetching extraction stats:", error);
      throw error;
    }
  }

  /**
   * Gets extractions by confidence threshold
   * @param minConfidence Minimum confidence (0-1)
   * @param maxConfidence Maximum confidence (0-1, optional)
   * @returns Extractions matching criteria
   */
  async getExtractionsByConfidence(
    minConfidence: number,
    maxConfidence?: number
  ): Promise<{ extractions: ContractExtractionData[]; total: number }> {
    try {
      const params = new URLSearchParams();
      params.append("minConfidence", minConfidence.toString());
      if (maxConfidence !== undefined) {
        params.append("maxConfidence", maxConfidence.toString());
      }

      const response = await apiClient.get(
        `/api/extractions/by-confidence?${params.toString()}`
      );
      return response;
    } catch (error) {
      console.error("Error fetching extractions by confidence:", error);
      throw error;
    }
  }

  /**
   * Checks if extraction exists for a contract
   * @param contractId Contract ID
   * @returns True if extraction exists
   */
  async hasExtraction(contractId: string): Promise<boolean> {
    try {
      const extraction = await this.getContractExtraction(contractId);
      return extraction !== null;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const contractExtractionService = new ContractExtractionService();
