/**
 * Contract Service
 * Provides API methods for contract management
 */

import { apiClient } from "@/lib/api-client";

// Contract status - calculated dynamically based on dates
export type ContractStatus = "Active" | "Inactive" | "Unknown";

// Contract classification enum
export enum ContractClassification {
  SW_SAAS = "SW_SAAS",
  IAAS = "IAAS",
  PAAS = "PAAS",
  PROFESSIONAL_SERVICES = "PROFESSIONAL_SERVICES",
  MANAGED_SERVICES = "MANAGED_SERVICES",
  HARDWARE = "HARDWARE",
  RESELLER = "RESELLER",
  NETWORK = "NETWORK",
  OTHER = "OTHER",
}

// Agreement type enum
export enum AgreementType {
  MSA = "MSA",
  NDA = "NDA",
  SOW = "SOW",
  PO = "PO",
  SLA = "SLA",
  DPA = "DPA",
  BAA = "BAA",
  EULA = "EULA",
  LOI = "LOI",
  MOA = "MOA",
  MOU = "MOU",
  JV = "JV",
  CA = "CA",
  LPA = "LPA",
  SSA = "SSA",
  ESA = "ESA",
  PSA = "PSA",
  TOS = "TOS",
  DUA = "DUA",
  OEM = "OEM",
  RFP = "RFP",
  RFQ = "RFQ",
  BPA = "BPA",
  PPA = "PPA",
  LSA = "LSA",
  ISA = "ISA",
  SPA = "SPA",
  APA = "APA",
  TPA = "TPA",
  IP = "IP",
  RSA = "RSA",
  VARA = "VARA",
  DDA = "DDA",
  TSA = "TSA",
  IA = "IA",
  INVOICE = "INVOICE",
  SCHEDULE = "SCHEDULE",
  ORDER = "ORDER",
  OTHER = "OTHER",
}

// Security classification enum
export enum SecurityClassification {
  PUBLIC = "PUBLIC",
  CONFIDENTIAL = "CONFIDENTIAL",
  RESTRICTED = "RESTRICTED",
  HIGHLY_RESTRICTED = "HIGHLY_RESTRICTED",
}

// Contract interface
export interface Contract {
  id: string;
  title: string;
  description?: string;
  contractNumber?: string;
  agreementType: AgreementType; // Use agreementType as the primary type field
  classification?: ContractClassification;
  status: ContractStatus;
  startDate?: string;
  endDate?: string;
  renewalType?: string;
  renewalDate?: string;
  isAutoRenew: boolean;
  noticePeriodDays?: number;
  version: number;
  securityClassification: SecurityClassification;
  isEncrypted: boolean;
  currentVersionId?: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  createdById?: string;
  createdByName?: string;
  counterparty?: string; // Added for UI display
  value?: string; // Added for UI display
  provider?: string; // Enhanced provider from metadata (Oracle contracts, etc.)
  assessmentCompleted?: boolean; // Added to track assessment status
  metadata?: ContractMetadata; // Contract metadata with AI-extracted fields
  extraction?: ContractExtraction; // Three-tier extraction data
}

// Contract metadata interface
export interface ContractMetadata {
  id: string;
  contractId: string;
  effectiveDate?: string;
  executionDate?: string;
  totalValue?: number;
  currency?: string;
  paymentTerms?: string;
  autoExtractedFields?: Record<string, any>;
  customMetadata?: Record<string, any>;
  encryptedFields?: string;
  lastExtractedAt?: string;
  aiGeneratedSummary?: string;
  aiGeneratedTabularSummary?: Record<string, any>[];
  createdAt: string;
  updatedAt: string;
}

// Contract search params interface
export interface ContractSearchParams {
  title?: string;
  agreementType?: AgreementType;
  status?: ContractStatus;
  provider?: string;
  securityClassification?: SecurityClassification;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  renewalMonths?: string; // "3", "6", "12" for contracts renewing in next X months
  page?: number;
  limit?: number;
}

// Contract creation params interface
export interface ContractCreateParams {
  title: string;
  description?: string | null;
  contractNumber?: string | null;
  agreementType: AgreementType;
  classification?: ContractClassification | null;
  status?: ContractStatus;
  startDate?: string | null;
  endDate?: string | null;
  renewalType?: string | null;
  renewalDate?: string | null;
  isAutoRenew?: boolean;
  noticePeriodDays?: number | null;
  securityClassification?: SecurityClassification;
  isEncrypted?: boolean;
  counterparty?: string | null;
  value?: string | null;
}

// Import result interface
export interface ImportResult {
  successful: number;
  failed: number;
  errors?: Array<{
    file?: string;
    row?: number;
    error: string;
    data?: any;
  }>;
  contracts?: Array<{
    id: string;
    title: string;
    status: ContractStatus;
    metadata?: any;
  }>;
  // Background processing properties
  processingStarted?: boolean;
  jobId?: string;
  filesReceived?: number;
  estimatedTimeSeconds?: number;
  batchInfo?: {
    totalFiles: number;
    concurrencyLimit: number;
    estimatedBatches: number;
  };
  statusEndpoint?: string;
}

// Contract extraction interfaces
export interface ContractExtraction {
  id: string;
  contractId: string;
  tenantId: string;
  fixedFields: {
    agreement_type: { value: string; confidence: number };
    provider: { value: string; confidence: number };
    client: { value: string; confidence: number };
    product: { value: string; confidence: number };
    total_amount: { value: string; confidence: number };
    annually_amount: { value: string; confidence: number };
    start_date: { value: string; confidence: number };
    end_date: { value: string; confidence: number };
    contract_id: { value: string; confidence: number };
    contract_classification: { value: string; confidence: number };
    contract_status: { value: string; confidence: number };
    contract_term: { value: string; confidence: number };
    auto_renewal: { value: string; confidence: number };
    renewal_notice_period: { value: string; confidence: number };
    relationships: { value: string; confidence: number };
    original_filename: { value: string; confidence: number };
    safe_auto_renewal?: { value: string; confidence: number };
    intervention_opportunity?: { value: string; confidence: number };
  };
  dynamicFields: {
    [key: string]: { value: string; description: string; confidence: number };
  };
  specialFields: {
    [supplierName: string]: {
      [key: string]: { value: string; confidence: number };
    };
  };
  documentSummary?: {
    value: string;
    confidence: number;
    extractionDate: string;
    processingTimeMs?: number;
  };
  analysisFields?: {
    [key: string]: { value: string; confidence: number };
  };
  overallConfidence?: number;
  extractionDate: string;
  extractionVersion: string;
  processingTimeMs?: number;
  modelUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContractWithExtraction extends Contract {
  extraction: ContractExtraction | undefined;
}

// Contract service class
export class ContractService {
  /**
   * Gets all contracts with extraction data for enhanced Repository view
   * @param params Search parameters
   * @returns List of extraction data and total count
   */
  async getContractsWithExtraction(
    params?: ContractSearchParams
  ): Promise<{ extractions: any[]; total: number }> {
    try {
      const queryParams = new URLSearchParams();

      // Add search parameters to query
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const query = queryParams.toString() ? `?${queryParams.toString()}` : "";
      return await apiClient.get(`/api/contracts/with-extraction${query}`);
    } catch (error) {
      console.error("Error fetching contracts with extraction:", error);
      throw error;
    }
  }

  /**
   * Gets repository statistics for all contracts
   * @returns Repository statistics including counts by status, agreement types, etc.
   */
  async getRepositoryStatistics(): Promise<{
    totalContracts: number;
    activeContracts: number;
    inactiveContracts: number;
    unknownStatusContracts: number;
    agreementTypesCount: number;
    recentlyAddedCount: number;
    agreementTypeBreakdown: { [key: string]: number };
  }> {
    try {
      return await apiClient.get("/api/contracts/repository-statistics");
    } catch (error) {
      console.error("Error fetching repository statistics:", error);
      throw error;
    }
  }

  /**
   * Gets all contracts with optional search parameters
   * @param params Search parameters
   * @returns List of contracts and total count
   */
  async getContracts(
    params?: ContractSearchParams
  ): Promise<{ contracts: Contract[]; total: number }> {
    try {
      const queryParams = new URLSearchParams();

      // Add search parameters to query
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }

      const query = queryParams.toString() ? `?${queryParams.toString()}` : "";
      // Use the existing API endpoint for now
      return await apiClient.get(`/api/contracts${query}`);
    } catch (error) {
      console.error("Error fetching contracts:", error);
      throw error;
    }
  }

  /**
   * Helper method to transform extraction data to Contract format
   * @param extractionData Extraction data from the API
   * @returns Contract object
   */
  private transformExtractionToContract(extractionData: any): Contract {
    const fixedFields = extractionData.fixedFields || {};
    const contractInfo = extractionData.contractInfo || {};

    // Extract values from fixed fields with fallbacks
    const agreementType = fixedFields.agreement_type?.value || "OTHER";
    const provider = fixedFields.provider?.value || "";
    const client = fixedFields.client?.value || "";
    const totalAmount = fixedFields.total_amount?.value || "";
    const startDate = fixedFields.start_date?.value || null;
    const endDate = fixedFields.end_date?.value || null;
    const contractId = fixedFields.contract_id?.value || "";
    const autoRenewal = fixedFields.auto_renewal?.value === "Yes";
    const originalFilename = fixedFields.original_filename?.value || "";

    // Use only LLM-extracted status, no calculation fallback
    const extractedStatus = fixedFields.contract_status?.value;
    let status = "Unknown";

    if (
      extractedStatus &&
      (extractedStatus === "Active" ||
        extractedStatus === "Inactive" ||
        extractedStatus === "Unknown")
    ) {
      status = extractedStatus;
    }
    // No fallback calculation - if LLM didn't extract status, it remains "Unknown"

    return {
      id: extractionData.contractId,
      title:
        originalFilename ||
        contractInfo.title ||
        `Contract ${extractionData.contractId}`,
      description: contractInfo.description || "",
      agreementType: agreementType as any,
      status: status as any,
      value: totalAmount,
      provider: provider,
      counterparty: client,
      contractNumber: contractId,
      startDate: startDate,
      endDate: endDate,
      isAutoRenew: autoRenewal,
      version: 1,
      securityClassification: "CONFIDENTIAL" as any,
      isEncrypted: true,
      tenantId: extractionData.tenantId || "",
      createdAt: extractionData.extractionDate || new Date().toISOString(),
      updatedAt: extractionData.updatedAt || new Date().toISOString(),
      // Include extraction data for components that need it
      extraction: {
        id: extractionData.id,
        contractId: extractionData.contractId,
        tenantId: extractionData.tenantId,
        fixedFields: extractionData.fixedFields,
        dynamicFields: extractionData.dynamicFields,
        specialFields: extractionData.specialFields,
        documentSummary: extractionData.documentSummary,
        analysisFields: extractionData.analysisFields,
        extractionDate: extractionData.extractionDate,
        extractionVersion: extractionData.extractionVersion || "1.0",
        overallConfidence: extractionData.overallConfidence,
        processingTimeMs: extractionData.processingTimeMs,
        modelUsed: extractionData.modelUsed,
        folderId: extractionData.folderId,
        createdAt: extractionData.createdAt,
        updatedAt: extractionData.updatedAt,
      },
    } as Contract;
  }

  /**
   * Gets a contract by ID using the new extraction API
   * @param id Contract ID
   * @returns Contract data with extraction information
   */
  async getContract(id: string): Promise<Contract> {
    try {
      // Get the contract extraction data from the new API endpoint
      const extractionData = await apiClient.get(
        `/api/contracts/${id}/extraction`
      );

      if (!extractionData || !extractionData.contractInfo) {
        throw new Error("Contract extraction data not found");
      }

      // Use the helper method to transform the data
      return this.transformExtractionToContract(extractionData);
    } catch (error) {
      console.error(`Error fetching contract ${id}:`, error);
      throw error;
    }
  }

  /**
   * Analyzes a Agreement Document using AI to extract metadata
   * @param id Contract ID
   * @returns Contract with AI-analyzed metadata
   */
  async analyzeContractWithAI(id: string): Promise<Contract> {
    try {
      // Get the contract data first
      const contract = await this.getContract(id);

      // Get the Agreement Document
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || "";
      const documentUrl = `${baseUrl}/api/contracts/${id}/document`;

      try {
        // Get the auth token from localStorage (using the correct key)
        const token = localStorage.getItem("access_token");
        if (!token) {
          console.error("No authentication token found");
          return contract;
        }

        // Fetch the document with authorization header
        const response = await fetch(documentUrl, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch document: ${response.status} ${response.statusText}`
          );
        }

        const blob = await response.blob();

        // Create a File object from the blob
        const file = new File([blob], contract.title || "contract.pdf", {
          type: blob.type || "application/pdf",
        });

        // Create form data for the file
        const formData = new FormData();
        formData.append("files", file);

        // First, upload the document to Gemini
        const uploadUrl = `${baseUrl}/api/gemini/upload`;
        const uploadResponse = await fetch(uploadUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }).then((res) => res.json());

        // Check if upload was successful
        if (
          uploadResponse.documentIds &&
          uploadResponse.documentIds.length > 0
        ) {
          const documentId = uploadResponse.documentIds[0];

          // Now use the Gemini chat API to extract metadata
          const chatUrl = `${baseUrl}/api/gemini/chat`;
          const prompt =
            "Extract key metadata from this Agreement Document as a JSON object. Include contract type, contract value, currency, effective date, end date, parties involved (with their roles), and any other important metadata. Format the response as a valid JSON object with key-value pairs.";

          const chatResponse = await fetch(chatUrl, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              message: prompt,
              documentIds: [documentId],
              context: "/contracts",
              conversationHistory: [],
            }),
          }).then((res) => res.json());

          // Extract metadata from the chat response
          if (chatResponse && chatResponse.message) {
            // Try to extract JSON from the response
            try {
              // Look for JSON object in the response
              const jsonMatch = chatResponse.message.match(/\{[\s\S]*\}/);
              let metadata = {};

              if (jsonMatch) {
                metadata = JSON.parse(jsonMatch[0]);
              } else {
                // If no JSON found, use the whole message as a fallback
                metadata = { analysis: chatResponse.message };
              }

              // Use this as our analysis response
              const analysisResponse = {
                success: true,
                metadata: metadata,
                documentId: documentId,
              };

              // If analysis was successful, merge the metadata with the contract
              if (analysisResponse.success && analysisResponse.metadata) {
                // Get existing metadata or create an empty object
                const existingMetadata = (contract as any).metadata || {};

                // Merge the AI-extracted metadata with existing metadata
                (contract as any).metadata = {
                  ...existingMetadata,
                  ...analysisResponse.metadata,
                };

                // Update the contract metadata in the database
                if (analysisResponse.metadata) {
                  try {
                    await this.updateContractMetadata(
                      id,
                      analysisResponse.metadata
                    );
                  } catch (metadataError) {
                    console.error(
                      "Error updating contract metadata:",
                      metadataError
                    );
                    // Continue even if metadata update fails
                  }
                }
              }
            } catch (jsonError) {
              console.error(
                "Error parsing JSON from Gemini response:",
                jsonError
              );
            }
          }
        }
      } catch (analysisError) {
        console.error("Error analyzing Agreement Document:", analysisError);
        // Continue even if analysis fails - we'll just return the contract without AI metadata
      }

      return contract;
    } catch (error) {
      console.error(`Error analyzing contract ${id}:`, error);
      throw error;
    }
  }

  /**
   * Updates contract metadata with AI-extracted information
   * @param contractId Contract ID
   * @param metadata AI-extracted metadata
   */
  private async updateContractMetadata(
    contractId: string,
    metadata: any
  ): Promise<void> {
    try {
      await apiClient.post(`/api/contracts/${contractId}/metadata`, {
        autoExtractedFields: metadata,
      });
    } catch (error) {
      console.error(
        `Error updating metadata for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Creates a new contract
   * @param data Contract data
   * @returns Created contract
   */
  async createContract(data: ContractCreateParams): Promise<Contract> {
    try {
      return await apiClient.post("/api/contracts", data);
    } catch (error) {
      console.error("Error creating contract:", error);
      throw error;
    }
  }

  /**
   * Updates an existing contract
   * @param id Contract ID
   * @param data Contract data
   * @returns Updated contract
   */
  async updateContract(
    id: string,
    data: ContractCreateParams
  ): Promise<Contract> {
    try {
      // Ensure all required fields have valid values
      const validatedData = {
        ...data,
        // Make sure status is one of the valid status values
        status:
          data.status && ["Active", "Inactive", "Unknown"].includes(data.status)
            ? data.status
            : "Unknown",
        // Ensure these fields are never undefined
        description: data.description || "",
        contractNumber: data.contractNumber || "",
        classification: data.classification || null,
        agreementType: data.agreementType || null,
        startDate: data.startDate || null,
        endDate: data.endDate || null,
        renewalType: data.renewalType || null,
        renewalDate: data.renewalDate || null,
        isAutoRenew: data.isAutoRenew || false,
        noticePeriodDays: data.noticePeriodDays || null,
        isEncrypted: data.isEncrypted || false,
        counterparty: data.counterparty || "",
        value: data.value || "",
      };

      return await apiClient.put(`/api/contracts/${id}`, validatedData);
    } catch (error) {
      console.error(`Error updating contract ${id}:`, error);
      throw error;
    }
  }

  /**
   * Deletes a contract
   * @param id Contract ID
   * @returns Success status
   */
  async deleteContract(id: string): Promise<{ success: boolean }> {
    try {
      return await apiClient.delete(`/api/contracts/${id}`);
    } catch (error) {
      console.error(`Error deleting contract ${id}:`, error);
      throw error;
    }
  }

  /**
   * Deletes multiple contracts
   * @param ids Array of contract IDs to delete
   * @returns Bulk delete result with success/failure counts
   */
  async deleteContracts(ids: string[]): Promise<{
    success: boolean;
    deleted: number;
    failed: number;
    errors: string[];
  }> {
    try {
      const results = await Promise.allSettled(
        ids.map((id) => this.deleteContract(id))
      );

      let deleted = 0;
      let failed = 0;
      const errors: string[] = [];

      results.forEach((result, index) => {
        if (result.status === "fulfilled" && result.value.success) {
          deleted++;
        } else {
          failed++;
          const error =
            result.status === "rejected"
              ? result.reason?.message || "Unknown error"
              : "Delete operation failed";
          errors.push(`Contract ${ids[index]}: ${error}`);
        }
      });

      return {
        success: deleted > 0,
        deleted,
        failed,
        errors,
      };
    } catch (error) {
      console.error("Error in bulk delete operation:", error);
      throw error;
    }
  }

  /**
   * Gets multiple contracts by IDs for comparison using three-tier extraction API
   * @param ids Array of contract IDs
   * @returns Array of contracts with three-tier extraction data
   */
  async getContractsForComparison(ids: string[]): Promise<Contract[]> {
    try {
      // Use the three-tier extraction API for each contract
      const contracts = await Promise.all(
        ids.map(async (id) => {
          try {
            // Get extraction data directly from the three-tier API
            const extractionData = await apiClient.get(
              `/api/contracts/${id}/extraction`
            );

            if (!extractionData || !extractionData.contractInfo) {
              throw new Error(`Contract extraction data not found for ${id}`);
            }

            // Transform extraction data to Contract format (same as in getContract)
            return this.transformExtractionToContract(extractionData);
          } catch (error) {
            console.error(
              `Error fetching contract ${id} for comparison:`,
              error
            );
            // Return a minimal contract object if extraction fails
            return {
              id,
              title: `Contract ${id}`,
              description: "Error loading contract data",
              agreementType: "OTHER" as any,
              status: "Unknown" as any,
              value: "",
              version: 1,
              securityClassification: "CONFIDENTIAL" as any,
              isEncrypted: true,
              tenantId: "",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            } as Contract;
          }
        })
      );
      return contracts.filter((contract) => contract.id); // Filter out any invalid contracts
    } catch (error) {
      console.error("Error fetching contracts for comparison:", error);
      throw error;
    }
  }

  /**
   * Imports contracts from multiple files
   * @param files Files to import
   * @returns Import result with count of successful and failed imports
   */
  async importContracts(files: File[]): Promise<ImportResult> {
    try {
      const formData = new FormData();

      // Append each file to the form data
      files.forEach((file) => {
        formData.append("files", file);
      });


      return await apiClient.post("/api/contracts/import", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 600000, // 10 minutes for AI processing
      });
    } catch (error) {
      console.error("Error importing contracts:", error);
      throw error;
    }
  }

  /**
   * Gets the status of an import job
   * @param jobId Job ID to check
   * @returns Job status information
   */
  async getImportJobStatus(jobId: string): Promise<any> {
    try {
      return await apiClient.get(`/api/contracts/import/status/${jobId}`);
    } catch (error) {
      console.error("Error getting import job status:", error);
      throw error;
    }
  }

  /**
   * Uploads a Agreement Document and extracts metadata using AI
   * @param file PDF document to upload
   * @returns Uploaded contract with AI-extracted metadata
   */
  async uploadContractWithAI(file: File): Promise<Contract> {
    try {
      // Use the existing import endpoint for now
      const formData = new FormData();
      formData.append("files", file);

      const result = await apiClient.post("/api/contracts/import", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 600000, // 10 minutes for AI processing
      });

      // Check for validation errors in the result
      if (result.failed > 0 && result.errors && result.errors.length > 0) {
        // Extract clean error message from backend
        let errorMessage = result.errors[0].error;
        if (
          errorMessage.includes("Failed to import contracts from documents: ")
        ) {
          errorMessage = errorMessage.replace(
            "Failed to import contracts from documents: ",
            ""
          );
        }
        throw new Error(errorMessage);
      }

      // Get the first contract from the import result
      if (
        result.successful > 0 &&
        result.contracts &&
        result.contracts.length > 0
      ) {
        return result.contracts[0];
      }

      throw new Error("No contracts were imported");
    } catch (error) {
      console.error("Error uploading contract:", error);
      throw error;
    }
  }

  /**
   * Gets contract metadata
   * @param contractId Contract ID
   * @returns Contract metadata
   */
  async getContractMetadata(contractId: string): Promise<ContractMetadata> {
    try {
      return await apiClient.get(`/api/contracts/${contractId}/metadata`);
    } catch (error) {
      console.error(
        `Error fetching metadata for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Gets contracts for autocomplete (lightweight)
   * @param search Optional search term
   * @returns Array of contracts with minimal data
   */
  async getContractsForAutocomplete(search?: string): Promise<Contract[]> {
    try {
      const url = search
        ? `/api/contracts/autocomplete?search=${encodeURIComponent(search)}`
        : "/api/contracts/autocomplete";

      const response = await apiClient.get(url);
      return response.contracts || [];
    } catch (error) {
      console.error("Error fetching contracts for autocomplete:", error);
      throw error;
    }
  }

  /**
   * Gets all contract IDs for smart grouping analysis
   * @returns Array of all contract IDs in the system
   */
  async getAllContractIdsForGrouping(): Promise<string[]> {
    try {
      // Fetch all contracts with a high limit to get all contracts
      // We only need IDs for grouping analysis
      const response = await apiClient.get("/api/contracts?limit=10000&page=1");
      return response.contracts?.map((contract: Contract) => contract.id) || [];
    } catch (error) {
      console.error("Error fetching all contract IDs for grouping:", error);
      throw error;
    }
  }

  /**
   * Generates AI summary for a contract
   * @param contractId Contract ID
   * @returns Generated summaries (both narrative and tabular)
   */
  async generateContractSummary(contractId: string): Promise<{
    success: boolean;
    summary: string;
    tabularSummary?: Record<string, any>[];
    message: string;
  }> {
    try {
      const response = await apiClient.post(
        `/api/contracts/${contractId}/generate-summary`,
        {},
        { timeout: 600000 } // 10 minutes for AI processing
      );
      return response;
    } catch (error) {
      console.error(
        `Error generating summary for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Generates DORA compliance analysis for a contract using OCR text from database
   * @param contractId Contract ID
   * @returns Generated compliance analysis
   */
  async generateDORACompliance(contractId: string): Promise<{
    success: boolean;
    compliance: any;
    message: string;
  }> {
    try {
      // Use the standard endpoint that automatically uses OCR text from database
      const response = await apiClient.post(
        `/api/contracts/${contractId}/generate-dora-compliance`,
        {},
        { timeout: 600000 } // 10 minutes for AI processing
      );
      return response;
    } catch (error) {
      console.error(
        `Error generating DORA compliance for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Generates integrity analysis for a contract
   * @param contractId Contract ID
   * @returns Generated integrity analysis
   */
  async generateIntegrityAnalysis(contractId: string): Promise<{
    success: boolean;
    analysis: any;
    message: string;
  }> {
    try {
      const response = await apiClient.post(
        `/api/contracts/${contractId}/generate-integrity-analysis`,
        {},
        { timeout: 600000 } // 10 minutes for AI processing
      );
      return response;
    } catch (error) {
      console.error(
        `Error generating integrity analysis for contract ${contractId}:`,
        error
      );
      throw error;
    }
  }
}

// Create a singleton instance
export const contractService = new ContractService();
