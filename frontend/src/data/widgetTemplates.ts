/**
 * Widget Templates Catalog
 * Defines available widget types and their configurations
 */

import { WidgetTemplate } from "@/types/dashboard";

export const WIDGET_TEMPLATES: WidgetTemplate[] = [
  // Overview Widgets
  {
    type: "summary-kpi",
    name: "Contract Summary",
    description:
      "Key metrics overview including total contracts, active contracts, and portfolio value",
    icon: "BarChart3",
    category: "overview",
    defaultSize: { w: 12, h: 2 },
    defaultConfig: {
      showHeader: true,
      showDescription: true,
      refreshInterval: 300,
    },
    configSchema: {
      fields: [
        {
          key: "refreshInterval",
          label: "Refresh Interval (seconds)",
          type: "number",
          default: 300,
          validation: { min: 30, max: 3600 },
        },
        {
          key: "showTrend",
          label: "Show Trend Indicators",
          type: "boolean",
          default: true,
        },
      ],
    },
  },

  // Analytics Widgets
  {
    type: "spend-by-provider",
    name: "Spend by Provider",
    description: "Pie chart showing contract value distribution by provider",
    icon: "PieChart",
    category: "analytics",
    defaultSize: { w: 4, h: 5 },
    defaultConfig: {
      chartType: "pie",
      showLegend: true,
      showTooltip: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "chartType",
          label: "Chart Type",
          type: "select",
          default: "pie",
          options: [
            { label: "Pie Chart", value: "pie" },
            { label: "Donut Chart", value: "donut" },
            { label: "Bar Chart", value: "bar" },
          ],
        },
        {
          key: "showLegend",
          label: "Show Legend",
          type: "boolean",
          default: true,
        },
        {
          key: "colors",
          label: "Color Scheme",
          type: "select",
          default: "default",
          options: [
            { label: "Default", value: "default" },
            { label: "Blue Theme", value: "blue" },
            { label: "Green Theme", value: "green" },
            { label: "Custom", value: "custom" },
          ],
        },
      ],
    },
  },

  {
    type: "agreement-types",
    name: "Agreement Types",
    description: "Bar chart showing contract distribution by agreement type",
    icon: "BarChart",
    category: "analytics",
    defaultSize: { w: 4, h: 5 },
    defaultConfig: {
      chartType: "bar",
      showTooltip: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "chartType",
          label: "Chart Type",
          type: "select",
          default: "bar",
          options: [
            { label: "Bar Chart", value: "bar" },
            { label: "Horizontal Bar", value: "horizontalBar" },
            { label: "Pie Chart", value: "pie" },
          ],
        },
      ],
    },
  },

  {
    type: "confidence-distribution",
    name: "AI Confidence Distribution",
    description: "Shows distribution of AI extraction confidence scores",
    icon: "TrendingUp",
    category: "analytics",
    defaultSize: { w: 6, h: 4 },
    defaultConfig: {
      chartType: "bar",
      showTooltip: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "confidenceThreshold",
          label: "Minimum Confidence Threshold",
          type: "number",
          default: 0,
          validation: { min: 0, max: 1 },
        },
      ],
    },
  },

  {
    type: "confidence",
    name: "Average Confidence",
    description: "Shows overall AI extraction confidence score",
    icon: "Target",
    category: "overview",
    defaultSize: { w: 4, h: 5 },
    defaultConfig: {
      showHeader: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "refreshInterval",
          label: "Refresh Interval (seconds)",
          type: "number",
          default: 600,
          validation: { min: 30, max: 3600 },
        },
      ],
    },
  },

  // Contract Widgets
  {
    type: "top-contracts",
    name: "Top Contracts",
    description: "Table showing highest value contracts",
    icon: "Table",
    category: "contracts",
    defaultSize: { w: 12, h: 6 },
    defaultConfig: {
      pageSize: 10,
      sortBy: "totalAmount",
      sortOrder: "desc",
      showHeader: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "pageSize",
          label: "Items per Page",
          type: "select",
          default: 10,
          options: [
            { label: "5", value: 5 },
            { label: "10", value: 10 },
            { label: "20", value: 20 },
            { label: "50", value: 50 },
          ],
        },
        {
          key: "sortBy",
          label: "Sort By",
          type: "select",
          default: "totalAmount",
          options: [
            { label: "Contract Value", value: "totalAmount" },
            { label: "Provider", value: "provider" },
            { label: "End Date", value: "endDate" },
            { label: "Confidence", value: "confidence" },
          ],
        },
        {
          key: "columns",
          label: "Visible Columns",
          type: "multiselect",
          default: ["provider", "product", "totalAmount", "endDate"],
          options: [
            { label: "Provider", value: "provider" },
            { label: "Product", value: "product" },
            { label: "Contract Value", value: "totalAmount" },
            { label: "End Date", value: "endDate" },
            { label: "Agreement Type", value: "agreementType" },
            { label: "Confidence", value: "confidence" },
          ],
        },
      ],
    },
  },

  {
    type: "renewal-timeline",
    name: "Renewal Timeline",
    description: "Shows contracts expiring in different time periods",
    icon: "Calendar",
    category: "contracts",
    defaultSize: { w: 4, h: 4 },
    defaultConfig: {
      showHeader: true,
      refreshInterval: 3600,
    },
    configSchema: {
      fields: [
        {
          key: "timeframes",
          label: "Time Periods",
          type: "multiselect",
          default: ["30days", "60days", "90days", "180days"],
          options: [
            { label: "30 Days", value: "30days" },
            { label: "60 Days", value: "60days" },
            { label: "90 Days", value: "90days" },
            { label: "180 Days", value: "180days" },
            { label: "1 Year", value: "365days" },
          ],
        },
      ],
    },
  },

  // Financial Widgets
  {
    type: "contract-status",
    name: "Contract Status Overview",
    description:
      "Shows breakdown of contract statuses (Active, Expired, Expiring)",
    icon: "Activity",
    category: "financial",
    defaultSize: { w: 4, h: 5 },
    defaultConfig: {
      chartType: "donut",
      showLegend: true,
      refreshInterval: 600,
    },
    configSchema: {
      fields: [
        {
          key: "chartType",
          label: "Chart Type",
          type: "select",
          default: "donut",
          options: [
            { label: "Donut Chart", value: "donut" },
            { label: "Pie Chart", value: "pie" },
            { label: "Bar Chart", value: "bar" },
          ],
        },
      ],
    },
  },
];

export const getWidgetTemplate = (type: string): WidgetTemplate | undefined => {
  return WIDGET_TEMPLATES.find((template) => template.type === type);
};

export const getWidgetsByCategory = (category: string): WidgetTemplate[] => {
  return WIDGET_TEMPLATES.filter((template) => template.category === category);
};

export const getAllCategories = (): string[] => {
  return Array.from(
    new Set(WIDGET_TEMPLATES.map((template) => template.category))
  );
};
