/**
 * Utility functions for clause analysis display and formatting
 */

export interface ClauseAnalysis {
  category: string;
  title: string;
  summary: string;
  keyPoints: string[];
  riskLevel: "Low" | "Medium" | "High";
  originalText: string;
}

/**
 * Extracts clause analysis from contract metadata
 */
export function extractClauseAnalysis(metadata: any): ClauseAnalysis[] {
  if (!metadata) return [];

  // Try multiple possible locations for clause analysis data
  const clausesAnalysis = 
    metadata?.autoExtractedFields?.clauses_analysis || 
    metadata?.clauses_analysis || 
    metadata?.autoExtractedFields?.rawResult?.clauses_analysis ||
    [];

  if (!Array.isArray(clausesAnalysis)) {
    return [];
  }

  return clausesAnalysis.filter((clause: any) => 
    clause && 
    typeof clause === 'object' && 
    clause.category && 
    clause.title
  );
}

/**
 * Groups clauses by category
 */
export function groupClausesByCategory(clauses: ClauseAnalysis[]): Record<string, ClauseAnalysis[]> {
  return clauses.reduce((groups, clause) => {
    const category = clause.category || 'Others';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(clause);
    return groups;
  }, {} as Record<string, ClauseAnalysis[]>);
}

/**
 * Gets clause statistics
 */
export function getClauseStatistics(clauses: ClauseAnalysis[]): {
  total: number;
  byRiskLevel: Record<string, number>;
  byCategory: Record<string, number>;
} {
  const stats = {
    total: clauses.length,
    byRiskLevel: { Low: 0, Medium: 0, High: 0 },
    byCategory: {} as Record<string, number>
  };

  clauses.forEach(clause => {
    // Count by risk level
    if (clause.riskLevel && stats.byRiskLevel[clause.riskLevel] !== undefined) {
      stats.byRiskLevel[clause.riskLevel]++;
    }

    // Count by category
    const category = clause.category || 'Others';
    stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
  });

  return stats;
}

/**
 * Filters clauses by risk level
 */
export function filterClausesByRiskLevel(
  clauses: ClauseAnalysis[], 
  riskLevel: "Low" | "Medium" | "High"
): ClauseAnalysis[] {
  return clauses.filter(clause => clause.riskLevel === riskLevel);
}

/**
 * Filters clauses by category
 */
export function filterClausesByCategory(
  clauses: ClauseAnalysis[], 
  category: string
): ClauseAnalysis[] {
  return clauses.filter(clause => clause.category === category);
}

/**
 * Gets the highest risk level from a list of clauses
 */
export function getHighestRiskLevel(clauses: ClauseAnalysis[]): "Low" | "Medium" | "High" | null {
  if (clauses.length === 0) return null;

  const riskLevels = clauses.map(clause => clause.riskLevel).filter(Boolean);
  
  if (riskLevels.includes("High")) return "High";
  if (riskLevels.includes("Medium")) return "Medium";
  if (riskLevels.includes("Low")) return "Low";
  
  return null;
}

/**
 * Formats clause category for display
 */
export function formatClauseCategory(category: string): string {
  const categoryMap: Record<string, string> = {
    'Payment': 'Payment Terms',
    'Termination': 'Termination Clauses',
    'Liability': 'Liability & Damages',
    'Confidentiality': 'Confidentiality & NDAs',
    'Intellectual Property': 'Intellectual Property',
    'Dispute Resolution': 'Dispute Resolution',
    'Force Majeure': 'Force Majeure',
    'Warranties': 'Warranties & Guarantees',
    'Assignment': 'Assignment & Transfer',
    'Notices': 'Notices & Communications',
    'Others': 'Other Clauses'
  };

  return categoryMap[category] || category;
}

/**
 * Checks if clause analysis data is available and meaningful
 */
export function hasClauseAnalysis(metadata: any): boolean {
  const clauses = extractClauseAnalysis(metadata);
  return clauses.length > 0;
}

/**
 * Gets a summary of clause analysis for quick overview
 */
export function getClauseAnalysisSummary(metadata: any): {
  totalClauses: number;
  highRiskCount: number;
  categories: string[];
  hasAnalysis: boolean;
} {
  const clauses = extractClauseAnalysis(metadata);
  const stats = getClauseStatistics(clauses);
  
  return {
    totalClauses: stats.total,
    highRiskCount: stats.byRiskLevel.High || 0,
    categories: Object.keys(stats.byCategory),
    hasAnalysis: clauses.length > 0
  };
}
