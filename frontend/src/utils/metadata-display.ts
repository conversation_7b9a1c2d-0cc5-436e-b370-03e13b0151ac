/**
 * Utility functions for smart metadata field display
 * Handles showing/hiding fields and generating tooltips for attempted but empty fields
 */

// Define all possible metadata fields that can be extracted by AI
export const ALL_METADATA_FIELDS = {
  // Basic contract information
  title: "Contract Title",
  agreement_type: "Agreement Type",
  contract_classification: "Contract Classification",
  provider: "Provider",
  client: "Client",
  contract_id: "Contract ID",
  contract_status: "Contract Status",
  contract_term: "Contract Term",

  // Dates
  effective_date: "Effective Date",
  start_date: "Start Date",
  end_date: "End Date",
  execution_date: "Execution Date",
  renewal_date: "Renewal Date",
  termination_date: "Termination Date",

  // Financial information
  total_amount: "Total Amount",
  annually_amount: "Annual Amount",
  contract_value: "Contract Value",
  currency: "Currency",
  payment_terms: "Payment Terms",
  payment_terms_detailed: "Detailed Payment Terms",

  // Legal and compliance
  governing_law: "Governing Law",
  dispute_resolution: "Dispute Resolution",
  liability_limits: "Liability Limits",
  warranties: "Warranties",
  indemnification: "Indemnification",

  // Contract terms
  renewal_terms: "Renewal Terms",
  termination: "Termination Clauses",
  confidentiality: "Confidentiality",
  amendments: "Amendments",
  signatures: "Signatures",

  // Service-specific
  sla: "Service Level Agreement",
  licensing: "Licensing Terms",
  rights: "Rights and Permissions",
  data_privacy: "Data Privacy",
  service_credits: "Service Credits",

  // Operational
  governance: "Governance",
  reporting_obligations: "Reporting Obligations",
  ipr: "Intellectual Property Rights",
  data_breach: "Data Breach Provisions",
  true_up_down: "True-up/True-down",
  clauses_analysis: "Clause Analysis",

  // Oracle-specific fields (29 required fields)
  publisher: "Publisher",
  reseller: "Reseller",
  entitled_entity: "Entitled Entity",
  entitled_entity_country: "Entitled Entity Country",
  product_name: "Product Name",
  raw_product_name: "Raw Product Name",
  total_quantity: "Total Quantity",
  metric: "Metric",
  metric_definition: "Metric Definition",
  term: "Term",
  level: "Level",
  limitations: "Limitations",
  included_rights: "Included Rights",
  csi: "CSI",
  purchase_date: "Purchase Date",
  governing_agreement: "Governing Agreement",
  support_contract_number: "Support Contract Number",
  support_start_date: "Support Start Date",
  support_end_date: "Support End Date",
  original_document_name: "Original Document Name",
  document_type: "Document Type",
  license_value: "License Value",
  license_value_per_unit: "License Value per Unit",
  contractual_support_value: "Contractual Support Value",
  support_value_per_year: "Support Value per Year",
  support_value_per_year_per_unit: "Support Value per Year per Unit",
  oracle_currency: "Oracle Currency",
  index_field: "Index",
  delta: "Delta",

  // Additional Oracle fields for compatibility
  product_version: "Product Version",
  license_type: "License Type",
  license_metric: "License Metric",
  authorized_users: "Authorized Users",
  named_users: "Named Users",
  processor_license: "Processor License",
  support_level: "Support Level",
  support_expiration: "Support Expiration",
  maintenance_level: "Maintenance Level",
  maintenance_expiration: "Maintenance Expiration",
  cloud_service: "Cloud Service",
  cloud_service_level: "Cloud Service Level",
  cloud_credits: "Cloud Credits",
  cloud_expiration: "Cloud Expiration",
  partner_level: "Partner Level",
  partner_benefits: "Partner Benefits",
  certification_requirements: "Certification Requirements",
  training_requirements: "Training Requirements",
  geographic_scope: "Geographic Scope",
  usage_restrictions: "Usage Restrictions",
  audit_rights: "Audit Rights",
  compliance_requirements: "Compliance Requirements",
  data_residency: "Data Residency",
  security_requirements: "Security Requirements",
  backup_requirements: "Backup Requirements",
  disaster_recovery: "Disaster Recovery",
  performance_metrics: "Performance Metrics",
  reporting_requirements: "Reporting Requirements",
} as const;

export type MetadataFieldKey = keyof typeof ALL_METADATA_FIELDS;

/**
 * Checks if a metadata value is meaningful and should be displayed
 * Returns false for "N/A", "not defined", null, undefined, empty strings, etc.
 */
export function isValueMeaningful(value: any): boolean {
  if (value === null || value === undefined) {
    return false;
  }

  if (typeof value === "string") {
    const trimmed = value.trim().toLowerCase();
    return (
      trimmed !== "" &&
      trimmed !== "not defined" &&
      trimmed !== "n/a" &&
      trimmed !== "unknown" &&
      trimmed !== "not specified" &&
      trimmed !== "null" &&
      trimmed !== "undefined"
    );
  }

  if (Array.isArray(value)) {
    return value.length > 0 && value.some((item) => isValueMeaningful(item));
  }

  if (typeof value === "object") {
    return Object.values(value).some((val) => isValueMeaningful(val));
  }

  return true;
}

/**
 * Checks if a value was attempted by AI but returned as "N/A" or similar
 */
export function isAttemptedButEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return false; // Not attempted
  }

  if (typeof value === "string") {
    const trimmed = value.trim().toLowerCase();
    return (
      trimmed === "n/a" ||
      trimmed === "not defined" ||
      trimmed === "unknown" ||
      trimmed === "not specified" ||
      trimmed === "not available" ||
      trimmed === "none" ||
      trimmed === "null" ||
      trimmed === "undefined"
    );
  }

  return false;
}

/**
 * Gets the value from metadata using three-tier extraction structure
 */
export function getMetadataValue(metadata: any, fieldKey: string): any {
  if (!metadata) return undefined;

  // First check if this is a contract with three-tier extraction data
  const extraction = (metadata as any).extraction;
  if (extraction) {
    // Check fixed fields first
    if (extraction.fixedFields?.[fieldKey]?.value !== undefined) {
      return extraction.fixedFields[fieldKey].value;
    }

    // Check dynamic fields
    if (extraction.dynamicFields?.[fieldKey]?.value !== undefined) {
      return extraction.dynamicFields[fieldKey].value;
    }

    // Check special fields (Oracle, Microsoft, SAP)
    if (extraction.specialFields) {
      for (const vendor of ["oracle", "microsoft", "sap"]) {
        if (extraction.specialFields[vendor]?.[fieldKey]?.value !== undefined) {
          return extraction.specialFields[vendor][fieldKey].value;
        }
      }
    }
  }

  // Fallback to legacy metadata structure for backward compatibility
  // Check at root level first (new flattened structure)
  if (metadata[fieldKey] !== undefined) {
    return metadata[fieldKey];
  }

  // Check in nested oracleFields structure for Oracle-specific fields
  if (metadata.oracleFields && metadata.oracleFields[fieldKey] !== undefined) {
    return metadata.oracleFields[fieldKey];
  }

  // Check in autoExtractedFields
  if (metadata.autoExtractedFields) {
    if (metadata.autoExtractedFields[fieldKey] !== undefined) {
      return metadata.autoExtractedFields[fieldKey];
    }

    // Check in rawResult within autoExtractedFields
    if (
      metadata.autoExtractedFields.rawResult &&
      metadata.autoExtractedFields.rawResult[fieldKey] !== undefined
    ) {
      return metadata.autoExtractedFields.rawResult[fieldKey];
    }

    // Check in oracle_fields within rawResult (most common location for Oracle fields)
    if (
      metadata.autoExtractedFields.rawResult &&
      metadata.autoExtractedFields.rawResult.oracle_fields &&
      metadata.autoExtractedFields.rawResult.oracle_fields[fieldKey] !==
      undefined
    ) {
      return metadata.autoExtractedFields.rawResult.oracle_fields[fieldKey];
    }

    // Handle special field mappings for common fields
    const specialMappings = getSpecialFieldMappings(
      metadata.autoExtractedFields,
      fieldKey
    );
    if (specialMappings !== undefined) {
      return specialMappings;
    }
  }

  // Check in customMetadata for backward compatibility
  if (
    metadata.customMetadata &&
    metadata.customMetadata[fieldKey] !== undefined
  ) {
    return metadata.customMetadata[fieldKey];
  }

  return undefined;
}

/**
 * Handles special field mappings for common contract fields
 */
function getSpecialFieldMappings(
  autoExtractedFields: any,
  fieldKey: string
): any {
  if (!autoExtractedFields) return undefined;

  // Handle common field mappings
  switch (fieldKey) {
    case "title":
      return autoExtractedFields.title || autoExtractedFields.contractTitle;

    case "agreement_type":
      return (
        autoExtractedFields.agreement_type ||
        autoExtractedFields.contractType ||
        autoExtractedFields.rawResult?.agreement_type
      );

    case "contract_value":
    case "total_amount":
      return (
        autoExtractedFields.contractValue ||
        autoExtractedFields.total_amount ||
        autoExtractedFields.value?.amount ||
        autoExtractedFields.value?.totalValue ||
        autoExtractedFields.rawResult?.contract_value
      );

    case "currency":
      return (
        autoExtractedFields.currency ||
        autoExtractedFields.value?.currency ||
        autoExtractedFields.rawResult?.currency
      );

    case "effective_date":
      return (
        autoExtractedFields.effectiveDate ||
        autoExtractedFields.dates?.effectiveDate ||
        autoExtractedFields.rawResult?.effective_date
      );

    case "end_date":
      return (
        autoExtractedFields.endDate ||
        autoExtractedFields.dates?.endDate ||
        autoExtractedFields.rawResult?.end_date
      );

    case "execution_date":
      return (
        autoExtractedFields.executionDate ||
        autoExtractedFields.dates?.executionDate ||
        autoExtractedFields.rawResult?.execution_date
      );

    case "provider":
      // Check parties array for provider role
      if (
        autoExtractedFields.parties &&
        Array.isArray(autoExtractedFields.parties)
      ) {
        const provider = autoExtractedFields.parties.find(
          (p: any) =>
            p.role?.toLowerCase().includes("provider") ||
            p.role?.toLowerCase().includes("vendor") ||
            p.role?.toLowerCase().includes("supplier")
        );
        if (provider) return provider.name;
        // If no specific role, try first party
        if (autoExtractedFields.parties[0])
          return autoExtractedFields.parties[0].name;
      }
      return (
        autoExtractedFields.provider || autoExtractedFields.rawResult?.provider
      );

    case "client":
      // Check parties array for client role
      if (
        autoExtractedFields.parties &&
        Array.isArray(autoExtractedFields.parties)
      ) {
        const client = autoExtractedFields.parties.find(
          (p: any) =>
            p.role?.toLowerCase().includes("client") ||
            p.role?.toLowerCase().includes("customer") ||
            p.role?.toLowerCase().includes("buyer")
        );
        if (client) return client.name;
        // If no specific role, try second party
        if (autoExtractedFields.parties[1])
          return autoExtractedFields.parties[1].name;
      }
      return (
        autoExtractedFields.client || autoExtractedFields.rawResult?.client
      );

    case "payment_terms":
      return (
        autoExtractedFields.paymentTerms ||
        autoExtractedFields.payment_terms ||
        autoExtractedFields.rawResult?.payment_terms
      );

    case "governing_law":
      return (
        autoExtractedFields.governingLaw ||
        autoExtractedFields.governing_law ||
        autoExtractedFields.rawResult?.governing_law
      );

    case "dispute_resolution":
      return (
        autoExtractedFields.disputeResolution ||
        autoExtractedFields.dispute_resolution ||
        autoExtractedFields.rawResult?.dispute_resolution
      );

    case "liability_limits":
      return (
        autoExtractedFields.liabilityLimits ||
        autoExtractedFields.liability_limits ||
        autoExtractedFields.liability?.capping ||
        autoExtractedFields.rawResult?.liability?.capping
      );

    case "termination":
      return (
        autoExtractedFields.termination ||
        autoExtractedFields.terminationClauses ||
        autoExtractedFields.rawResult?.termination
      );

    case "renewal_terms":
      return (
        autoExtractedFields.renewalTerms ||
        autoExtractedFields.renewal_terms ||
        autoExtractedFields.rawResult?.renewal_terms
      );

    case "sla":
      return (
        autoExtractedFields.sla ||
        autoExtractedFields.serviceLevel ||
        autoExtractedFields.rawResult?.sla
      );

    default:
      return undefined;
  }
}

/**
 * Gets all fields that have meaningful values
 */
export function getFieldsWithValues(metadata: any): MetadataFieldKey[] {
  const fieldsWithValues: MetadataFieldKey[] = [];

  Object.keys(ALL_METADATA_FIELDS).forEach((fieldKey) => {
    const value = getMetadataValue(metadata, fieldKey);
    if (isValueMeaningful(value)) {
      fieldsWithValues.push(fieldKey as MetadataFieldKey);
    }
  });

  return fieldsWithValues;
}

/**
 * Checks if a field was attempted by the AI (has confidence score or explicit value)
 */
export function wasFieldAttempted(metadata: any, fieldKey: string): boolean {
  // Check if there's a confidence score for this field
  const confidenceScore = getFieldConfidenceScore(metadata, fieldKey);
  if (confidenceScore !== undefined) {
    return true;
  }

  // Check if the field exists in the metadata (even if empty/N/A)
  const value = getMetadataValue(metadata, fieldKey);
  if (value !== undefined) {
    return true;
  }

  // Check if field exists in confidence_scores object (indicates AI attempted it)
  if (metadata?.confidence_scores) {
    const possibleKeys = [
      `${fieldKey}_score`,
      fieldKey,
      `${fieldKey}_confidence`,
    ];

    for (const key of possibleKeys) {
      if (metadata.confidence_scores[key] !== undefined) {
        return true;
      }
    }
  }

  // Check in autoExtractedFields confidence_scores
  if (metadata?.autoExtractedFields?.confidence_scores) {
    const possibleKeys = [
      `${fieldKey}_score`,
      fieldKey,
      `${fieldKey}_confidence`,
    ];

    for (const key of possibleKeys) {
      if (metadata.autoExtractedFields.confidence_scores[key] !== undefined) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Gets all fields that were attempted but have no meaningful values
 * These are fields that exist in the metadata but contain "N/A", "not defined", etc.
 * OR fields that have confidence scores but no meaningful values
 */
export function getAttemptedButEmptyFields(metadata: any): MetadataFieldKey[] {
  const attemptedButEmpty: MetadataFieldKey[] = [];

  Object.keys(ALL_METADATA_FIELDS).forEach((fieldKey) => {
    const value = getMetadataValue(metadata, fieldKey);
    const wasAttempted = wasFieldAttempted(metadata, fieldKey);

    // Field was attempted if:
    // 1. It has a confidence score, OR
    // 2. It exists and is marked as "N/A" or similar, OR
    // 3. It was attempted but has no meaningful value
    if (
      wasAttempted &&
      (!isValueMeaningful(value) || isAttemptedButEmpty(value))
    ) {
      attemptedButEmpty.push(fieldKey as MetadataFieldKey);
    }
  });

  return attemptedButEmpty;
}

/**
 * Generates tooltip content for attempted but empty fields
 */
export function generateEmptyFieldsTooltip(
  emptyFields: MetadataFieldKey[]
): string {
  if (emptyFields.length === 0) {
    return "All attempted fields have been successfully extracted.";
  }

  const fieldNames = emptyFields
    .map((field) => ALL_METADATA_FIELDS[field])
    .join(", ");

  return `The following fields were attempted but returned N/A: ${fieldNames}`;
}

/**
 * Gets confidence score for a specific field from three-tier extraction
 */
export function getFieldConfidenceScore(
  metadata: any,
  fieldKey: string
): number | undefined {
  // First check three-tier extraction data
  const extraction = (metadata as any).extraction;
  if (extraction) {
    // Check fixed fields confidence
    if (extraction.fixedFields?.[fieldKey]?.confidence !== undefined) {
      return extraction.fixedFields[fieldKey].confidence;
    }

    // Check dynamic fields confidence
    if (extraction.dynamicFields?.[fieldKey]?.confidence !== undefined) {
      return extraction.dynamicFields[fieldKey].confidence;
    }

    // Check special fields confidence (Oracle, Microsoft, SAP)
    if (extraction.specialFields) {
      for (const vendor of ["oracle", "microsoft", "sap"]) {
        if (
          extraction.specialFields[vendor]?.[fieldKey]?.confidence !== undefined
        ) {
          return extraction.specialFields[vendor][fieldKey].confidence;
        }
      }
    }
  }

  // Fallback to legacy confidence score structure
  const possibleKeys = [
    `${fieldKey}_score`,
    fieldKey,
    `${fieldKey}_confidence`,
  ];

  // Check at root level confidence_scores
  if (metadata?.confidence_scores) {
    for (const key of possibleKeys) {
      if (metadata.confidence_scores[key] !== undefined) {
        return metadata.confidence_scores[key];
      }
    }
  }

  // Check in autoExtractedFields confidence_scores
  if (metadata?.autoExtractedFields?.confidence_scores) {
    for (const key of possibleKeys) {
      if (metadata.autoExtractedFields.confidence_scores[key] !== undefined) {
        return metadata.autoExtractedFields.confidence_scores[key];
      }
    }
  }

  // Check in rawResult confidence_scores
  if (metadata?.autoExtractedFields?.rawResult?.confidence_scores) {
    for (const key of possibleKeys) {
      if (
        metadata.autoExtractedFields.rawResult.confidence_scores[key] !==
        undefined
      ) {
        return metadata.autoExtractedFields.rawResult.confidence_scores[key];
      }
    }
  }

  return undefined;
}

/**
 * Categorizes fields by their confidence levels
 */
export function categorizeFieldsByConfidence(metadata: any): {
  high: MetadataFieldKey[];
  medium: MetadataFieldKey[];
  low: MetadataFieldKey[];
} {
  const fieldsWithValues = getFieldsWithValues(metadata);
  const categorized = {
    high: [] as MetadataFieldKey[],
    medium: [] as MetadataFieldKey[],
    low: [] as MetadataFieldKey[],
  };

  fieldsWithValues.forEach((field) => {
    const confidence = getFieldConfidenceScore(metadata, field);
    if (confidence !== undefined) {
      if (confidence >= 0.8) {
        categorized.high.push(field);
      } else if (confidence >= 0.5) {
        categorized.medium.push(field);
      } else {
        categorized.low.push(field);
      }
    } else {
      // Default to medium if no confidence score
      categorized.medium.push(field);
    }
  });

  return categorized;
}

/**
 * Checks if a field should be displayed based on its value and confidence
 */
export function shouldDisplayField(metadata: any, fieldKey: string): boolean {
  const value = getMetadataValue(metadata, fieldKey);
  return isValueMeaningful(value);
}

/**
 * Critical fields that should always be shown, even if empty (display "N/A")
 */
export const CRITICAL_FIELDS: MetadataFieldKey[] = [
  "contract_value",
  "total_amount",
  "effective_date",
  "end_date",
  "provider",
  "client",
];

/**
 * Checks if a field is critical and should always be displayed
 */
export function isCriticalField(fieldKey: string): boolean {
  return CRITICAL_FIELDS.includes(fieldKey as MetadataFieldKey);
}

/**
 * Checks if a section should be displayed based on its fields
 * A section is displayed if it has at least one meaningful value OR contains critical fields
 */
export function shouldDisplaySection(
  metadata: any,
  sectionFields: MetadataFieldKey[]
): boolean {
  // Check if any field in the section has meaningful values
  const hasValues = sectionFields.some((field) =>
    shouldDisplayField(metadata, field)
  );

  // Check if section contains critical fields (these should always be shown)
  const hasCriticalFields = sectionFields.some((field) =>
    isCriticalField(field)
  );

  return hasValues || hasCriticalFields;
}

/**
 * Gets the display value for a field, returning "N/A" for critical empty fields
 */
export function getDisplayValue(
  metadata: any,
  fieldKey: string,
  fallback: string = ""
): string {
  const value = getMetadataValue(metadata, fieldKey);

  if (isValueMeaningful(value)) {
    if (typeof value === "string") {
      return value;
    } else if (typeof value === "object" && value !== null) {
      if (Array.isArray(value)) {
        return formatArrayValue(value, fieldKey);
      } else {
        return formatObjectValue(value, fieldKey);
      }
    } else {
      return String(value);
    }
  }

  // For critical fields, show "N/A" instead of hiding
  if (isCriticalField(fieldKey)) {
    return "N/A";
  }

  return fallback;
}

/**
 * Formats array values for display
 */
function formatArrayValue(value: any[], fieldKey: string): string {
  if (!Array.isArray(value) || value.length === 0) {
    return "N/A";
  }

  // Handle specific field types
  switch (fieldKey) {
    case "parties":
      return value
        .filter((party) => party && (party.name || party.role))
        .map((party) => {
          if (party.name && party.role) {
            return `${party.name} (${party.role})`;
          }
          return party.name || party.role || "Unknown party";
        })
        .join(", ");

    case "signatures":
      return value
        .filter((sig) => sig && (sig.name || sig.title))
        .map((sig) => {
          const parts = [];
          if (sig.name) parts.push(sig.name);
          if (sig.title) parts.push(`(${sig.title})`);
          if (sig.date) parts.push(`[${sig.date}]`);
          return parts.join(" ");
        })
        .join(", ");

    case "warranties":
    case "termination_clauses":
    case "amendments":
      return value
        .filter((item) => isValueMeaningful(item))
        .map((item) => (typeof item === "string" ? item : String(item)))
        .join("; ");

    default:
      // Generic array handling
      return value
        .filter((item) => isValueMeaningful(item))
        .map((item) => {
          if (typeof item === "object" && item !== null) {
            return formatObjectValue(item, fieldKey);
          }
          return String(item);
        })
        .join(", ");
  }
}

/**
 * Formats object values for display
 */
function formatObjectValue(value: any, fieldKey: string): string {
  if (!value || typeof value !== "object") {
    return "N/A";
  }

  // Handle specific field types
  switch (fieldKey) {
    case "liability":
    case "liability_limits":
      const liabilityParts = [];
      if (value.capping) liabilityParts.push(`Capping: ${value.capping}`);
      if (value.exclusions)
        liabilityParts.push(`Exclusions: ${value.exclusions}`);
      if (value.limits) liabilityParts.push(`Limits: ${value.limits}`);
      return liabilityParts.length > 0 ? liabilityParts.join("; ") : "N/A";

    case "sla":
      const slaParts = [];
      if (value.availability)
        slaParts.push(`Availability: ${value.availability}`);
      if (value.response_time_p1)
        slaParts.push(`Response P1: ${value.response_time_p1}`);
      if (value.response_time_p2)
        slaParts.push(`Response P2: ${value.response_time_p2}`);
      if (value.resolution_time_p1)
        slaParts.push(`Resolution P1: ${value.resolution_time_p1}`);
      if (value.resolution_time_p2)
        slaParts.push(`Resolution P2: ${value.resolution_time_p2}`);
      return slaParts.length > 0 ? slaParts.join("; ") : "N/A";

    case "payment_terms_detailed":
      const paymentParts = [];
      if (value.timeline) paymentParts.push(`Timeline: ${value.timeline}`);
      if (value.delayed_payment_charges)
        paymentParts.push(`Late charges: ${value.delayed_payment_charges}`);
      if (value.right_to_invoice)
        paymentParts.push(`Invoice rights: ${value.right_to_invoice}`);
      return paymentParts.length > 0 ? paymentParts.join("; ") : "N/A";

    case "termination":
      const terminationParts = [];
      if (value.t4c) terminationParts.push(`T4C: ${value.t4c}`);
      if (value.notice_t4c)
        terminationParts.push(`Notice: ${value.notice_t4c}`);
      if (value.conditions)
        terminationParts.push(`Conditions: ${value.conditions}`);
      return terminationParts.length > 0 ? terminationParts.join("; ") : "N/A";

    case "renewal_terms":
      const renewalParts = [];
      if (value.auto_renewal)
        renewalParts.push(`Auto-renewal: ${value.auto_renewal}`);
      if (value.notice_period)
        renewalParts.push(`Notice period: ${value.notice_period}`);
      if (value.renewal_term) renewalParts.push(`Term: ${value.renewal_term}`);
      if (value.pricing_conditionality)
        renewalParts.push(`Pricing: ${value.pricing_conditionality}`);
      return renewalParts.length > 0 ? renewalParts.join("; ") : "N/A";

    case "value":
    case "contract_value":
      if (value.amount && value.currency) {
        return `${value.currency}:${value.amount}`;
      }
      if (value.totalValue) return String(value.totalValue);
      if (value.amount) return String(value.amount);
      break;

    // Oracle-specific field formatting
    case "license_value":
    case "license_value_per_unit":
    case "contractual_support_value":
    case "support_value_per_year":
    case "support_value_per_year_per_unit":
      if (typeof value === "object" && value !== null) {
        if (value.amount && value.currency) {
          return `${value.currency} ${value.amount}`;
        }
        if (value.value && value.currency) {
          return `${value.currency} ${value.value}`;
        }
      }
      return String(value);

    case "oracle_currency":
      if (typeof value === "object" && value !== null) {
        return value.code || value.symbol || String(value);
      }
      return String(value);

    case "purchase_date":
    case "support_start_date":
    case "support_end_date":
      if (typeof value === "object" && value !== null) {
        return value.date || value.timestamp || String(value);
      }
      return String(value);

    case "product_name":
    case "raw_product_name":
      if (typeof value === "object" && value !== null) {
        const parts = [];
        if (value.name) parts.push(value.name);
        if (value.version) parts.push(`v${value.version}`);
        if (value.edition) parts.push(`(${value.edition})`);
        return parts.length > 0 ? parts.join(" ") : String(value);
      }
      return String(value);

    case "metric":
    case "metric_definition":
      if (typeof value === "object" && value !== null) {
        const parts = [];
        if (value.type) parts.push(`Type: ${value.type}`);
        if (value.unit) parts.push(`Unit: ${value.unit}`);
        if (value.description) parts.push(`Description: ${value.description}`);
        return parts.length > 0 ? parts.join("; ") : String(value);
      }
      return String(value);

    case "limitations":
    case "included_rights":
      if (typeof value === "object" && value !== null) {
        if (Array.isArray(value.items)) {
          return value.items.join("; ");
        }
        if (value.description) return value.description;
        const meaningfulValues = Object.values(value)
          .filter((v) => isValueMeaningful(v))
          .map((v) => String(v));
        return meaningfulValues.join("; ");
      }
      return String(value);

    default:
      // Generic object handling - extract meaningful values
      const meaningfulValues = Object.entries(value)
        .filter(([_, v]) => isValueMeaningful(v))
        .map(([key, v]) => {
          // For nested objects, format them recursively
          if (typeof v === "object" && v !== null) {
            if (Array.isArray(v)) {
              return `${key}: ${formatArrayValue(v, key)}`;
            } else {
              return `${key}: ${formatObjectValue(v, key)}`;
            }
          }
          return `${key}: ${String(v)}`;
        });

      return meaningfulValues.length > 0 ? meaningfulValues.join("; ") : "N/A";
  }

  return "N/A";
}

/**
 * Gets all fields across all sections that were attempted but returned empty
 * This is used for the global info tooltip
 */
export function getAllAttemptedButEmptyFields(metadata: any): {
  fields: MetadataFieldKey[];
  totalAttempted: number;
  totalSuccessful: number;
  totalPossible: number;
} {
  const attemptedButEmpty = getAttemptedButEmptyFields(metadata);
  const fieldsWithValues = getFieldsWithValues(metadata);
  const totalPossibleFields = Object.keys(ALL_METADATA_FIELDS).length;

  // Count all fields that were attempted (either successful or failed)
  const allAttemptedFields = new Set<string>();

  // Add successful fields
  fieldsWithValues.forEach((field) => allAttemptedFields.add(field));

  // Add attempted but empty fields
  attemptedButEmpty.forEach((field) => allAttemptedFields.add(field));

  // Also check confidence scores to find additional attempted fields
  if (metadata?.confidence_scores) {
    Object.keys(metadata.confidence_scores).forEach((key) => {
      // Remove '_score' suffix to get field name
      const fieldName = key.replace(/_score$/, "").replace(/_confidence$/, "");
      if (ALL_METADATA_FIELDS[fieldName as MetadataFieldKey]) {
        allAttemptedFields.add(fieldName);
      }
    });
  }

  if (metadata?.autoExtractedFields?.confidence_scores) {
    Object.keys(metadata.autoExtractedFields.confidence_scores).forEach(
      (key) => {
        // Remove '_score' suffix to get field name
        const fieldName = key
          .replace(/_score$/, "")
          .replace(/_confidence$/, "");
        if (ALL_METADATA_FIELDS[fieldName as MetadataFieldKey]) {
          allAttemptedFields.add(fieldName);
        }
      }
    );
  }

  return {
    fields: attemptedButEmpty,
    totalAttempted: allAttemptedFields.size,
    totalSuccessful: fieldsWithValues.length,
    totalPossible: totalPossibleFields,
  };
}
