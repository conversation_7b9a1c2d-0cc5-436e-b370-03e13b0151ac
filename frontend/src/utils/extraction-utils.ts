/**
 * Extraction Utilities
 * Helper functions for working with three-tier extraction data
 */

export interface FieldValue {
  value: string;
  confidence: number;
}

export interface FlattenedField {
  key: string;
  value: string;
  confidence: number;
  vendor?: string;
  fieldType: "fixed" | "dynamic" | "special";
  displayName: string;
}

/**
 * Flattens specialFields from nested vendor structure to a flat array
 * @param specialFields The nested special fields object
 * @returns Array of flattened field objects
 */
export function flattenSpecialFields(specialFields: {
  [vendor: string]: { [key: string]: FieldValue };
}): FlattenedField[] {
  const flattened: FlattenedField[] = [];

  Object.entries(specialFields || {}).forEach(([vendor, fields]) => {
    Object.entries(fields || {}).forEach(([fieldKey, fieldValue]) => {
      // Skip fields with very low confidence or N/A values
      if (fieldValue.confidence < 0.3 && fieldValue.value === "N/A") {
        return;
      }

      flattened.push({
        key: `${vendor}_${fieldKey}`,
        value: fieldValue.value,
        confidence: fieldValue.confidence,
        vendor,
        fieldType: "special",
        displayName: formatSpecialFieldName(fieldKey, vendor),
      });
    });
  });

  return flattened;
}

/**
 * Flattens fixedFields to a flat array
 * @param fixedFields The fixed fields object
 * @returns Array of flattened field objects
 */
export function flattenFixedFields(fixedFields: {
  [key: string]: FieldValue;
}): FlattenedField[] {
  const flattened: FlattenedField[] = [];

  Object.entries(fixedFields || {}).forEach(([fieldKey, fieldValue]) => {
    // Skip fields with very low confidence or N/A values
    if (fieldValue.confidence < 0.3 && fieldValue.value === "N/A") {
      return;
    }

    flattened.push({
      key: fieldKey,
      value: fieldValue.value,
      confidence: fieldValue.confidence,
      fieldType: "fixed",
      displayName: formatFixedFieldName(fieldKey),
    });
  });

  return flattened;
}

/**
 * Gets all extraction fields as a flat array for table display
 * @param extraction The extraction data
 * @returns Array of all flattened fields
 */
export function getAllExtractionFields(extraction: {
  fixedFields?: { [key: string]: FieldValue };
  specialFields?: { [vendor: string]: { [key: string]: FieldValue } };
}): FlattenedField[] {
  const fixedFields = flattenFixedFields(extraction.fixedFields || {});
  const specialFields = flattenSpecialFields(extraction.specialFields || {});

  return [...fixedFields, ...specialFields];
}

/**
 * Formats a fixed field name for display
 * @param fieldKey The field key
 * @returns Formatted display name
 */
export function formatFixedFieldName(fieldKey: string): string {
  return fieldKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
}

/**
 * Formats a special field name for display
 * @param fieldKey The field key
 * @param vendor The vendor name
 * @returns Formatted display name
 */
export function formatSpecialFieldName(
  fieldKey: string,
  vendor: string
): string {
  const vendorName = vendor.charAt(0).toUpperCase() + vendor.slice(1);
  const fieldName = fieldKey
    .replace(/_/g, " ")
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return `${fieldName} (${vendorName})`;
}

/**
 * Gets a specific field value from extraction data
 * @param extraction The extraction data
 * @param fieldPath The field path (e.g., "fixedFields.provider" or "specialFields.microsoft.waiver")
 * @returns The field value or null if not found
 */
export function getExtractionFieldValue(
  extraction: {
    fixedFields?: { [key: string]: FieldValue };
    specialFields?: { [vendor: string]: { [key: string]: FieldValue } };
  },
  fieldPath: string
): string | null {
  if (fieldPath.startsWith("fixedFields.")) {
    const field = fieldPath.replace("fixedFields.", "");
    return extraction.fixedFields?.[field]?.value || null;
  } else if (fieldPath.startsWith("specialFields.")) {
    const parts = fieldPath.replace("specialFields.", "").split(".");
    if (parts.length >= 2) {
      const vendor = parts[0];
      const field = parts[1];
      return extraction.specialFields?.[vendor]?.[field]?.value || null;
    }
  }

  return null;
}

/**
 * Gets confidence score for a specific field
 * @param extraction The extraction data
 * @param fieldPath The field path
 * @returns The confidence score or 0 if not found
 */
export function getExtractionFieldConfidence(
  extraction: {
    fixedFields?: { [key: string]: FieldValue };
    specialFields?: { [vendor: string]: { [key: string]: FieldValue } };
  },
  fieldPath: string
): number {
  if (fieldPath.startsWith("fixedFields.")) {
    const field = fieldPath.replace("fixedFields.", "");
    return extraction.fixedFields?.[field]?.confidence || 0;
  } else if (fieldPath.startsWith("specialFields.")) {
    const parts = fieldPath.replace("specialFields.", "").split(".");
    if (parts.length >= 2) {
      const vendor = parts[0];
      const field = parts[1];
      return extraction.specialFields?.[vendor]?.[field]?.confidence || 0;
    }
  }

  return 0;
}

/**
 * Filters fields by confidence threshold
 * @param fields Array of flattened fields
 * @param minConfidence Minimum confidence threshold (0-1)
 * @returns Filtered array of fields
 */
export function filterFieldsByConfidence(
  fields: FlattenedField[],
  minConfidence: number = 0.3
): FlattenedField[] {
  return fields.filter((field) => field.confidence >= minConfidence);
}

/**
 * Groups fields by vendor for special fields
 * @param fields Array of flattened fields
 * @returns Object with vendor as key and fields as value
 */
export function groupSpecialFieldsByVendor(fields: FlattenedField[]): {
  [vendor: string]: FlattenedField[];
} {
  const grouped: { [vendor: string]: FlattenedField[] } = {};

  fields
    .filter((field) => field.fieldType === "special" && field.vendor)
    .forEach((field) => {
      const vendor = field.vendor!;
      if (!grouped[vendor]) {
        grouped[vendor] = [];
      }
      grouped[vendor].push(field);
    });

  return grouped;
}

/**
 * Creates a simple key-value object from extraction data for easy table display
 * @param extraction The extraction data
 * @param includeConfidence Whether to include confidence scores in the output
 * @returns Simple key-value object
 */
export function extractionToKeyValue(
  extraction: {
    fixedFields?: { [key: string]: FieldValue };
    specialFields?: { [vendor: string]: { [key: string]: FieldValue } };
  },
  includeConfidence: boolean = false
): { [key: string]: string } {
  const result: { [key: string]: string } = {};

  // Add fixed fields
  Object.entries(extraction.fixedFields || {}).forEach(([key, field]) => {
    if (
      field.confidence >= 0.3 ||
      field.confidence === -1 ||
      field.value !== "N/A"
    ) {
      const displayKey = formatFixedFieldName(key);
      result[displayKey] = includeConfidence
        ? `${field.value} (${
            field.confidence === -1
              ? "Manual"
              : Math.round(field.confidence * 100) + "%"
          })`
        : field.value;
    }
  });

  // Add special fields
  Object.entries(extraction.specialFields || {}).forEach(([vendor, fields]) => {
    Object.entries(fields).forEach(([key, field]) => {
      if (
        field.confidence >= 0.3 ||
        field.confidence === -1 ||
        field.value !== "N/A"
      ) {
        const displayKey = formatSpecialFieldName(key, vendor);
        result[displayKey] = includeConfidence
          ? `${field.value} (${
              field.confidence === -1
                ? "Manual"
                : Math.round(field.confidence * 100) + "%"
            })`
          : field.value;
      }
    });
  });

  return result;
}
