/**
 * Dashboard Types
 * Type definitions for customizable dashboard system
 */

export interface DashboardWidget {
  id: string;
  userId: string;
  name: string;
  description?: string;
  category: WidgetCategory;
  widgetType: string;
  isVisible: boolean;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  configuration: WidgetConfig;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GroupedWidgets {
  priority: DashboardWidget[];
  portfolio: DashboardWidget[];
}

export type WidgetType =
  | "expiring-contracts"
  | "auto-renewals"
  | "auto-renewals-classification"
  | "realised-savings"
  | "potential-savings"
  | "key-obligations"
  | "total-agreements"
  | "high-value-agreements"
  | "critical-agreements"
  | "expired-agreements"
  | "aging-contracts"
  | "customer-supplier-paper"
  | "agreement-types"
  | "service-types"
  | "summary-kpi"
  | "spend-by-provider"
  | "confidence-distribution"
  | "confidence"
  | "top-contracts"
  | "renewal-timeline"
  | "contract-status";

export interface WidgetConfig {
  // Common config
  refreshInterval?: number; // in seconds
  showHeader?: boolean;
  showDescription?: boolean;

  // Chart-specific config
  chartType?: "pie" | "bar" | "line" | "area" | "donut";
  showLegend?: boolean;
  showTooltip?: boolean;
  colors?: string[];

  // Table-specific config
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  columns?: string[];

  // KPI-specific config
  format?: "currency" | "number" | "percentage";
  showTrend?: boolean;

  // Data filtering
  filters?: {
    dateRange?: {
      start: string;
      end: string;
    };
    providers?: string[];
    agreementTypes?: string[];
    confidenceThreshold?: number;
  };
}

export interface WidgetTemplate {
  type: WidgetType;
  name: string;
  description: string;
  icon: string;
  category: WidgetCategory;
  defaultSize: {
    w: number;
    h: number;
  };
  defaultConfig: WidgetConfig;
  configSchema: WidgetConfigSchema;
}

export type WidgetCategory =
  | "priority"
  | "portfolio"
  | "overview"
  | "analytics"
  | "contracts"
  | "financial";

export interface WidgetConfigSchema {
  fields: ConfigField[];
}

export interface ConfigField {
  key: string;
  label: string;
  type:
  | "text"
  | "number"
  | "select"
  | "multiselect"
  | "boolean"
  | "color"
  | "daterange";
  required?: boolean;
  default?: any;
  options?: { label: string; value: any }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface DashboardContextType {
  // Current layout
  currentLayout: DashboardLayout | null;
  layouts: DashboardLayout[];

  // Widget management
  widgets: DashboardWidget[];
  selectedWidget: DashboardWidget | null;

  // Layout management
  isEditMode: boolean;
  isDragging: boolean;

  // Actions
  setEditMode: (enabled: boolean) => void;
  addWidget: (
    template: WidgetTemplate,
    position?: { x: number; y: number }
  ) => void;
  removeWidget: (widgetId: string) => void;
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => void;
  moveWidget: (
    widgetId: string,
    position: { x: number; y: number; w: number; h: number }
  ) => void;
  selectWidget: (widget: DashboardWidget | null) => void;
  reorderWidgets: (oldIndex: number, newIndex: number) => void;

  // Layout actions
  createLayout: (name: string, description?: string) => void;
  deleteLayout: (layoutId: string) => void;
  switchLayout: (layoutId: string) => void;
  saveLayout: () => Promise<void>;
  resetLayout: () => Promise<void>;

  // Widget templates
  widgetTemplates: WidgetTemplate[];
}

// Grid layout configuration
export interface GridLayoutConfig {
  cols: number;
  rowHeight: number;
  margin: [number, number];
  containerPadding: [number, number];
  breakpoints: {
    lg: number;
    md: number;
    sm: number;
    xs: number;
    xxs: number;
  };
  colsBreakpoints: {
    lg: number;
    md: number;
    sm: number;
    xs: number;
    xxs: number;
  };
}

export const DEFAULT_GRID_CONFIG: GridLayoutConfig = {
  cols: 12,
  rowHeight: 60,
  margin: [16, 16],
  containerPadding: [16, 16],
  breakpoints: {
    lg: 1200,
    md: 996,
    sm: 768,
    xs: 480,
    xxs: 0,
  },
  colsBreakpoints: {
    lg: 12,
    md: 10,
    sm: 6,
    xs: 4,
    xxs: 2,
  },
};
