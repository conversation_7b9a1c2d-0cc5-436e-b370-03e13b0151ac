/**
 * Types for contract relationship visualization
 */

export interface ContractRelationshipCriteria {
  contractIdMatching: boolean;
  startDateProximity: boolean;
  executionDateCorrelation: boolean;
  signatureDateAlignment: boolean;
  signatureNameMatching: boolean;
  referencedDocuments: boolean;
  agreementTypeHierarchy: boolean;
}

export interface ContractRelationship {
  fromContractId: string;
  toContractId: string;
  relationshipType:
    | "HIERARCHY"
    | "REFERENCE"
    | "TEMPORAL"
    | "SIGNATURE"
    | "CONTRACT_ID"
    | "MANUAL";
  strength: number; // 0-1 confidence score
  criteria: string[]; // Which criteria matched
  details?: string; // Additional details about the relationship
}

export interface ContractNode {
  id: string;
  title: string;
  agreementType: string;
  provider?: string;
  startDate?: Date;
  endDate?: Date;
  executionDate?: Date;
  signatureDate?: Date;
  signatoryName?: string;
  contractNumber?: string;
  referencedDocuments?: string[];
  hierarchyLevel?: number;
  value?: string;
  status?: string;
}

export interface ContractRelationshipAnalysis {
  nodes: ContractNode[];
  relationships: ContractRelationship[];
  clusters: ContractCluster[];
}

export interface ContractCluster {
  id: string;
  name: string;
  contractIds: string[];
  clusterType: "HIERARCHY" | "TEMPORAL" | "REFERENCE" | "MIXED";
  strength: number;
}

export interface SupplierContractRelationshipsResponse {
  success: boolean;
  supplier: string;
  totalContracts: number;
  totalRelationships: number;
  totalClusters: number;
  analysis: ContractRelationshipAnalysis;
}

// React Flow specific types
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    contract: ContractNode;
    isSelected?: boolean;
    onSelect?: (nodeId: string) => void;
  };
  style?: React.CSSProperties;
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: {
    relationship: ContractRelationship;
  };
  style?: React.CSSProperties;
  markerEnd?: {
    type: string;
    color?: string;
  };
  label?: string;
  labelStyle?: React.CSSProperties;
}

export interface ContractRelationshipVisualizationProps {
  supplierName: string;
  isOpen: boolean;
  onClose: () => void;
  criteria?: Partial<ContractRelationshipCriteria>;
}
