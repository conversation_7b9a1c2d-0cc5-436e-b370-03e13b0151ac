/**
 * Classification Utilities
 * Provides utility functions for formatting contract classifications and agreement types
 */

import {
  ContractClassification,
  AgreementType,
} from "@/services/contractService";

/**
 * Formats contract classification for display
 * @param classification Contract classification enum value
 * @returns Formatted display string
 */
export function formatContractClassification(
  classification?: ContractClassification | string
): string {
  if (!classification) return "N/A";

  switch (classification) {
    case ContractClassification.SW_SAAS:
      return "SW/SaaS";
    case ContractClassification.IAAS:
      return "IaaS";
    case ContractClassification.PAAS:
      return "PaaS";
    case ContractClassification.PROFESSIONAL_SERVICES:
      return "Professional Services";
    case ContractClassification.MANAGED_SERVICES:
      return "Managed Services";
    case ContractClassification.HARDWARE:
      return "Hardware";
    case ContractClassification.RESELLER:
      return "Reseller";
    case ContractClassification.NETWORK:
      return "Network";
    case ContractClassification.OTHER:
      return "Other";
    default:
      return classification.toString();
  }
}

/**
 * Formats agreement type for display (abbreviations only)
 * @param agreementType Agreement type enum value
 * @returns Formatted display string (abbreviation)
 */
export function formatAgreementType(
  agreementType?: AgreementType | string
): string {
  if (!agreementType) return "N/A";

  switch (agreementType) {
    case AgreementType.MSA:
      return "MSA";
    case AgreementType.NDA:
      return "NDA";
    case AgreementType.SOW:
      return "SOW";
    case AgreementType.PO:
      return "PO";
    case AgreementType.SLA:
      return "SLA";
    case AgreementType.DPA:
      return "DPA";
    case AgreementType.BAA:
      return "BAA";
    case AgreementType.EULA:
      return "EULA";
    case AgreementType.LOI:
      return "LOI";
    case AgreementType.MOA:
      return "MOA";
    case AgreementType.MOU:
      return "MOU";
    case AgreementType.JV:
      return "JV";
    case AgreementType.CA:
      return "CA";
    case AgreementType.LPA:
      return "LPA";
    case AgreementType.SSA:
      return "SSA";
    case AgreementType.ESA:
      return "ESA";
    case AgreementType.PSA:
      return "PSA";
    case AgreementType.TOS:
      return "TOS";
    case AgreementType.DUA:
      return "DUA";
    case AgreementType.OEM:
      return "OEM";
    case AgreementType.RFP:
      return "RFP";
    case AgreementType.RFQ:
      return "RFQ";
    case AgreementType.BPA:
      return "BPA";
    case AgreementType.PPA:
      return "PPA";
    case AgreementType.LSA:
      return "LSA";
    case AgreementType.ISA:
      return "ISA";
    case AgreementType.SPA:
      return "SPA";
    case AgreementType.APA:
      return "APA";
    case AgreementType.TPA:
      return "TPA";
    case AgreementType.IP:
      return "IP";
    case AgreementType.RSA:
      return "RSA";
    case AgreementType.VARA:
      return "VARA";
    case AgreementType.DDA:
      return "DDA";
    case AgreementType.TSA:
      return "TSA";
    case AgreementType.IA:
      return "IA";
    case AgreementType.INVOICE:
      return "INVOICE";
    case AgreementType.SCHEDULE:
      return "SCHEDULE";
    case AgreementType.ORDER:
      return "ORDER";
    case AgreementType.OTHER:
      return "Other";
    default:
      return agreementType.toString();
  }
}

/**
 * Gets the color class for contract classification badges
 * @param classification Contract classification enum value
 * @returns CSS color class
 */
export function getClassificationColor(
  classification?: ContractClassification | string
): string {
  if (!classification)
    return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";

  // Convert to string and normalize for comparison
  const normalizedClassification = classification.toString().toUpperCase();

  switch (normalizedClassification) {
    case "SW_SAAS":
    case "SW/SAAS":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "IAAS":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    case "PAAS":
      return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";
    case "PROFESSIONAL_SERVICES":
    case "PROFESSIONAL SERVICES":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "MANAGED_SERVICES":
    case "MANAGED SERVICES":
      return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";
    case "HARDWARE":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    case "RESELLER":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "NETWORK":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    case "OTHER":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
}

/**
 * Gets the color class for agreement type badges
 * @param agreementType Agreement type enum value
 * @returns CSS color class
 */
export function getAgreementTypeColor(
  agreementType?: AgreementType | string
): string {
  if (!agreementType)
    return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";

  // Convert to string and normalize for comparison
  const normalizedType = agreementType.toString().toUpperCase();

  switch (normalizedType) {
    case "MSA":
    case "PSA":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "NDA":
    case "CA":
    case "DPA":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "SOW":
    case "PO":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "SLA":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    case "EULA":
    case "TOS":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    case "INVOICE":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "SCHEDULE":
      return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";
    case "ORDER":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
}
