/**
 * Tests for format utility functions
 */

import {
  formatContractTerm,
  formatNoticePeriod,
  formatContractValue,
  parseContractValue,
} from "../format-utils";

describe("formatContractTerm", () => {
  it("should format contract term correctly for years", () => {
    const startDate = "2023-01-01";
    const endDate = "2025-01-01";
    const result = formatContractTerm(startDate, endDate);
    expect(result).toBe("2 years");
  });

  it("should format contract term correctly for months", () => {
    const startDate = "2023-01-01";
    const endDate = "2023-07-01";
    const result = formatContractTerm(startDate, endDate);
    expect(result).toBe("6 months");
  });

  it("should format contract term correctly for days", () => {
    const startDate = "2023-01-01";
    const endDate = "2023-01-15";
    const result = formatContractTerm(startDate, endDate);
    expect(result).toBe("14 days");
  });

  it("should return N/A for missing dates", () => {
    expect(formatContractTerm(null, "2023-01-01")).toBe("N/A");
    expect(formatContractTerm("2023-01-01", null)).toBe("N/A");
    expect(formatContractTerm(null, null)).toBe("N/A");
  });

  it("should return N/A for invalid dates", () => {
    expect(formatContractTerm("invalid", "2023-01-01")).toBe("N/A");
    expect(formatContractTerm("2023-01-01", "invalid")).toBe("N/A");
  });
});

describe("formatNoticePeriod", () => {
  it("should format notice period in days", () => {
    expect(formatNoticePeriod(30)).toBe("30 days");
    expect(formatNoticePeriod(1)).toBe("1 day");
  });

  it("should format notice period in months", () => {
    expect(formatNoticePeriod(60)).toBe("2 months");
    expect(formatNoticePeriod(90)).toBe("3 months");
  });

  it("should format notice period in years", () => {
    expect(formatNoticePeriod(365)).toBe("1 year");
    expect(formatNoticePeriod(730)).toBe("2 years");
  });

  it("should prefer text over numeric value", () => {
    const result = formatNoticePeriod(30, "60 days notice required");
    expect(result).toBe("60 days notice required");
  });

  it("should return N/A for invalid input", () => {
    expect(formatNoticePeriod(0)).toBe("N/A");
    expect(formatNoticePeriod(-1)).toBe("N/A");
    expect(formatNoticePeriod(undefined)).toBe("N/A");
  });
});

describe("parseContractValue", () => {
  it("should parse combined format 'CURRENCY:AMOUNT'", () => {
    const result = parseContractValue("EUR:114239.58");
    expect(result.currency).toBe("EUR");
    expect(result.amount).toBe(114239.58);
  });

  it("should parse USD format", () => {
    const result = parseContractValue("USD:6600.00");
    expect(result.currency).toBe("USD");
    expect(result.amount).toBe(6600.0);
  });

  it("should handle legacy numeric format", () => {
    const result = parseContractValue("314");
    expect(result.currency).toBe("USD");
    expect(result.amount).toBe(314);
  });

  it("should handle invalid currency codes", () => {
    const result = parseContractValue("INVALID:1000");
    expect(result.currency).toBe("USD");
    expect(result.amount).toBe(1000);
  });

  it("should return default for empty values", () => {
    expect(parseContractValue("")).toEqual({ currency: "USD", amount: 0 });
    expect(parseContractValue(undefined)).toEqual({
      currency: "USD",
      amount: 0,
    });
  });
});

describe("formatContractValue", () => {
  it("should format EUR values in compact mode", () => {
    const result = formatContractValue("EUR:114239.58", undefined, true);
    expect(result).toMatch(/€114K|€114,240/); // Allow for different compact formatting
  });

  it("should format USD values in compact mode", () => {
    const result = formatContractValue("USD:6600.00", undefined, true);
    expect(result).toMatch(/\$7K|\$6,600/); // Allow for different compact formatting
  });

  it("should format small values without compact notation", () => {
    const result = formatContractValue("USD:314", undefined, true);
    expect(result).toMatch(/\$314/);
  });

  it("should handle legacy format", () => {
    const result = formatContractValue("314", undefined, true);
    expect(result).toMatch(/\$314/);
  });

  it("should return N/A for invalid values", () => {
    expect(formatContractValue("")).toBe("N/A");
    expect(formatContractValue(undefined)).toBe("N/A");
    expect(formatContractValue("N/A")).toBe("N/A");
  });

  it("should format large values with M suffix in compact mode", () => {
    const result = formatContractValue("EUR:2500000", undefined, true);
    expect(result).toMatch(/€3M|€2,500,000/); // Allow for different compact formatting
  });
});
