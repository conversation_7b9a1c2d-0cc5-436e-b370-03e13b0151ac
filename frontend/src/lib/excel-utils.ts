/**
 * Excel Utilities
 * Helper functions for Excel file generation and manipulation
 */

import * as XLSX from "xlsx";

export interface ExcelSheetData {
  name: string;
  data: Record<string, any>[];
}

/**
 * Creates an Excel workbook from multiple sheets of data
 * @param sheets Array of sheet data
 * @returns Excel workbook
 */
export function createWorkbook(sheets: ExcelSheetData[]): XLSX.WorkBook {
  const workbook = XLSX.utils.book_new();

  sheets.forEach((sheet) => {
    const worksheet = XLSX.utils.json_to_sheet(sheet.data);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
  });

  return workbook;
}

/**
 * Downloads an Excel file from a workbook
 * @param workbook Excel workbook
 * @param filename Filename for download
 */
export function downloadWorkbook(
  workbook: XLSX.WorkBook,
  filename: string
): void {
  // Generate Excel buffer
  const excelBuffer = XLSX.write(workbook, { type: "array", bookType: "xlsx" });

  // Create blob and download
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}

/**
 * Creates an Excel file from contract data and downloads it
 * @param contractData Array of contract data
 * @param filename Filename for download
 */
export function exportContractsToExcel(
  contractData: Record<string, any>[],
  filename: string
): void {
  const sheets: ExcelSheetData[] = [
    {
      name: "Contracts",
      data: contractData,
    },
  ];

  const workbook = createWorkbook(sheets);
  downloadWorkbook(workbook, filename);
}

/**
 * Formats contract data for Excel export
 * @param contracts Array of contracts
 * @returns Formatted data for Excel
 */
export function formatContractsForExcel(
  contracts: any[]
): Record<string, any>[] {
  return contracts.map((contract) => ({
    "Contract ID": contract.id,
    "Contract Title": contract.title,
    "Agreement Type": contract.agreementType || "N/A",
    Status: contract.status || "Unknown",
    "Start Date": contract.startDate
      ? new Date(contract.startDate).toLocaleDateString()
      : "N/A",
    "End Date": contract.endDate
      ? new Date(contract.endDate).toLocaleDateString()
      : "N/A",
    "Contract Value": contract.value || "N/A",
    Provider: contract.provider || "N/A",
    Counterparty: contract.counterparty || "N/A",
    "Contract Number": contract.contractNumber || "N/A",
    Description: contract.description || "N/A",
  }));
}

/**
 * Validates Excel file format
 * @param file File to validate
 * @returns True if valid Excel file
 */
export function isValidExcelFile(file: File): boolean {
  const validTypes = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
    "application/vnd.ms-excel", // .xls
  ];

  return validTypes.includes(file.type);
}

/**
 * Reads Excel file and returns data
 * @param file Excel file
 * @returns Promise with sheet data
 */
export function readExcelFile(file: File): Promise<Record<string, any>[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });

        // Get first sheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as Record<
          string,
          any
        >[];
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => reject(new Error("Failed to read file"));
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Gets Excel file information
 * @param file Excel file
 * @returns Promise with file info
 */
export function getExcelFileInfo(file: File): Promise<{
  sheets: string[];
  totalRows: number;
}> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });

        let totalRows = 0;
        workbook.SheetNames.forEach((sheetName) => {
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          totalRows += jsonData.length;
        });

        resolve({
          sheets: workbook.SheetNames,
          totalRows,
        });
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => reject(new Error("Failed to read file"));
    reader.readAsArrayBuffer(file);
  });
}
