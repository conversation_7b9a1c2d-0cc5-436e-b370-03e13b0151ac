/**
 * Frontend Currency Conversion Utilities
 * Mirrors the backend CurrencyConversionService for consistent conversion
 */

export interface CurrencyConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  targetCurrency: string;
  exchangeRate: number;
}

export class CurrencyConverter {
  private static instance: CurrencyConverter;
  private baseCurrency = "USD";

  // Exchange rates (same as backend fallback rates)
  private fallbackRates: Record<string, number> = {
    "EUR": 0.85,
    "GBP": 0.73,
    "JPY": 110.0,
    "CAD": 1.25,
    "AUD": 1.35,
    "CHF": 0.92,
    "CNY": 6.45,
    "INR": 74.5,
    "BRL": 5.2,
    "MXN": 20.1,
    "SGD": 1.35,
    "HKD": 7.8,
    "NOK": 8.5,
    "SEK": 8.7,
    "DKK": 6.3,
    "PLN": 3.9,
    "CZK": 21.5,
    "HUF": 295.0,
    "RON": 4.1,
    "BGN": 1.66,
    "HRK": 6.4,
    "RUB": 73.5,
    "TRY": 8.5,
    "ZAR": 14.2,
    "KRW": 1180.0,
    "THB": 31.5,
    "MYR": 4.1,
    "IDR": 14250.0,
    "PHP": 49.5,
    "VND": 23000.0
  };

  private constructor() { }

  public static getInstance(): CurrencyConverter {
    if (!CurrencyConverter.instance) {
      CurrencyConverter.instance = new CurrencyConverter();
    }
    return CurrencyConverter.instance;
  }

  /**
   * Parse currency value in format "CURRENCY:AMOUNT"
   */
  public parseCurrencyValue(currencyValue: string | null | undefined): {
    currency: string;
    amount: number;
  } {
    if (!currencyValue || typeof currencyValue !== "string") {
      return { currency: "USD", amount: 0 };
    }

    // Handle format like "USD:58926.00" or "PHP:8855748.00"
    if (currencyValue.includes(":")) {
      const parts = currencyValue.split(":");
      if (parts.length === 2) {
        const currency = parts[0].toUpperCase();
        const amount = parseFloat(parts[1]);
        return {
          currency: currency || "USD",
          amount: isNaN(amount) ? 0 : amount
        };
      }
    }

    // Handle direct number format (assume USD)
    const amount = parseFloat(currencyValue);
    return {
      currency: "USD",
      amount: isNaN(amount) ? 0 : amount
    };
  }

  /**
   * Convert amount from one currency to another
   */
  public convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string = this.baseCurrency
  ): CurrencyConversionResult {
    const from = fromCurrency.toUpperCase();
    const to = toCurrency.toUpperCase();

    // If same currency, no conversion needed
    if (from === to) {
      return {
        originalAmount: amount,
        originalCurrency: from,
        convertedAmount: amount,
        targetCurrency: to,
        exchangeRate: 1.0
      };
    }

    // Get exchange rate
    const exchangeRate = this.getExchangeRate(from, to);
    const convertedAmount = amount * exchangeRate;

    return {
      originalAmount: amount,
      originalCurrency: from,
      convertedAmount: convertedAmount,
      targetCurrency: to,
      exchangeRate: exchangeRate
    };
  }

  /**
   * Convert currency value string to USD
   */
  public convertToUSD(currencyValue: string | null | undefined): number {
    const parsed = this.parseCurrencyValue(currencyValue);
    if (parsed.amount === 0) return 0;

    const conversion = this.convertCurrency(parsed.amount, parsed.currency, "USD");
    return conversion.convertedAmount;
  }

  /**
   * Get exchange rate between two currencies
   */
  private getExchangeRate(from: string, to: string): number {
    if (from === to) return 1.0;

    // Direct conversion to USD
    if (to === "USD" && this.fallbackRates[from]) {
      return 1 / this.fallbackRates[from];
    }

    // Direct conversion from USD
    if (from === "USD" && this.fallbackRates[to]) {
      return this.fallbackRates[to];
    }

    // Cross-currency conversion via USD
    if (this.fallbackRates[from] && this.fallbackRates[to]) {
      return this.fallbackRates[to] / this.fallbackRates[from];
    }

    // Default fallback
    console.warn(`No exchange rate available for ${from} to ${to}, using 1.0`);
    return 1.0;
  }

  /**
   * Format converted amount as USD currency
   */
  public formatAsUSD(currencyValue: string | null | undefined): string {
    const convertedAmount = this.convertToUSD(currencyValue);

    if (convertedAmount === 0) return "N/A";

    try {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(convertedAmount);
    } catch (error) {
      return `$${convertedAmount.toLocaleString("en-US", {
        maximumFractionDigits: 0,
      })}`;
    }
  }
}

// Export singleton instance
export const currencyConverter = CurrencyConverter.getInstance();
