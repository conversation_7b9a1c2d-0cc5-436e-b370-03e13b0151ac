/**
 * Secure API Client
 * Provides a secure way to interact with the backend API
 */

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from "axios";
import { toast } from "sonner";

// Auth token storage keys
const ACCESS_TOKEN_KEY = "access_token";
const REFRESH_TOKEN_KEY = "refresh_token";
const TOKEN_EXPIRY_KEY = "token_expiry";

/**
 * API client for secure communication with the backend
 */
export class ApiClient {
  private axiosInstance: AxiosInstance;
  private refreshPromise: Promise<boolean> | null = null;

  constructor(
    baseURL: string = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"
  ) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds for regular operations
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Add request interceptor for authentication
    this.axiosInstance.interceptors.request.use(
      this.handleRequestAuth,
      this.handleRequestError
    );

    // Add response interceptor for token refresh
    this.axiosInstance.interceptors.response.use(
      this.handleResponse,
      this.handleResponseError
    );
  }

  /**
   * Handles authentication for requests
   */
  private handleRequestAuth = async (
    config: InternalAxiosRequestConfig
  ): Promise<InternalAxiosRequestConfig> => {
    const token = this.getAccessToken();

    if (token) {
      config.headers.set("Authorization", `Bearer ${token}`);
    }

    return config;
  };

  /**
   * Handles request errors
   */
  private handleRequestError = (error: any): Promise<any> => {
    console.error("Request error:", error);
    return Promise.reject(error);
  };

  /**
   * Handles successful responses
   */
  private handleResponse = (response: AxiosResponse): AxiosResponse => {
    return response;
  };

  /**
   * Handles response errors, including token refresh
   */
  private handleResponseError = async (error: AxiosError): Promise<any> => {
    const originalRequest = error.config as any;

    // Check if error is due to expired token
    if (error.response?.status === 401 && !originalRequest._retry) {
      // If we're already refreshing, wait for that to complete
      if (this.refreshPromise) {
        await this.refreshPromise;
        // Retry the original request
        return this.axiosInstance(originalRequest);
      }

      // Mark request as retried
      originalRequest._retry = true;

      // Try to refresh the token
      this.refreshPromise = this.refreshToken();

      try {
        const refreshed = await this.refreshPromise;
        this.refreshPromise = null;

        if (refreshed) {
          // Retry the original request with new token
          return this.axiosInstance(originalRequest);
        } else {
          // If refresh failed, logout but don't redirect
          // Let the AuthContext handle the redirect
          this.logout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        this.refreshPromise = null;
        // If refresh failed, logout but don't redirect
        // Let the AuthContext handle the redirect
        this.logout();
        return Promise.reject(error);
      }
    }

    // Handle other errors
    if (error.response) {
      // Server responded with an error status
      const errorData = error.response.data as {
        error?: string;
        message?: string;
        details?: string;
      };

      // Get the most specific error message available
      const errorMessage =
        errorData.message || errorData.error || "An error occurred";

      // Log detailed error information for debugging
      console.error("API Error Response:", {
        status: error.response.status,
        url: originalRequest?.url,
        message: errorMessage,
        details: errorData.details || "No additional details",
      });

      // Show appropriate error message based on status code
      if (error.response.status === 413) {
        toast.error(
          "The document is too large to process. Please try with a smaller document."
        );
      } else if (
        error.response.status === 404 &&
        originalRequest?.url?.includes("/gemini")
      ) {
        toast.error(
          "Document not found. Please check if the document still exists."
        );
      } else if (
        error.response.status === 504 ||
        errorMessage.includes("timeout")
      ) {
        toast.error(
          "The request timed out. This might be due to large documents or server load."
        );
      } else {
        toast.error(errorMessage);
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error("API No Response:", {
        url: originalRequest?.url,
        method: originalRequest?.method,
        timeout: originalRequest?.timeout,
      });

      // Check if it's a timeout error
      if (
        error.code === "ECONNABORTED" ||
        (error.message && error.message.includes("timeout"))
      ) {
        toast.error(
          "Request timed out. Document processing may take longer than expected."
        );
      } else {
        toast.error("No response from server. Please check your connection.");
      }
    } else {
      // Something else happened
      console.error("API Request Error:", error);
      toast.error("An error occurred. Please try again.");
    }

    return Promise.reject(error);
  };

  /**
   * Refreshes the access token
   * @returns Whether the refresh was successful
   */
  private async refreshToken(): Promise<boolean> {
    const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);

    if (!refreshToken) {
      return false;
    }

    try {
      const response = await axios.post(
        `${this.axiosInstance.defaults.baseURL}/api/auth/refresh-token`,
        { refreshToken }
      );

      const {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn,
      } = response.data;

      this.setTokens(accessToken, newRefreshToken, expiresIn);

      return true;
    } catch (error) {
      console.error("Token refresh failed:", error);
      return false;
    }
  }

  /**
   * Gets the current access token
   * @returns Access token or null if not found
   */
  private getAccessToken(): string | null {
    // Check if token is expired
    const expiryStr = localStorage.getItem(TOKEN_EXPIRY_KEY);

    if (expiryStr) {
      const expiry = parseInt(expiryStr, 10);

      if (Date.now() >= expiry) {
        // Token is expired, try to refresh
        this.refreshToken();
        return null;
      }
    }

    return localStorage.getItem(ACCESS_TOKEN_KEY);
  }

  /**
   * Sets authentication tokens
   * @param accessToken Access token
   * @param refreshToken Refresh token
   * @param expiresIn Expiration time in seconds
   * @param userProfile User profile data
   */
  public setTokens(
    accessToken: string,
    refreshToken: string,
    expiresIn: number,
    userProfile?: any
  ): void {
    localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);

    // Calculate expiry time
    const expiryTime = Date.now() + expiresIn * 1000;
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());

    // Store user profile if provided
    if (userProfile) {
      localStorage.setItem("user_profile", JSON.stringify(userProfile));
    }

    // Also set cookies for middleware access
    document.cookie = `access_token=${accessToken}; path=/; max-age=${expiresIn}; SameSite=Lax`;
    document.cookie = `token_expiry=${expiryTime}; path=/; max-age=${expiresIn}; SameSite=Lax`;
  }

  /**
   * Logs the user out
   */
  public logout(): void {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
    localStorage.removeItem("user_profile");

    // Clear all authentication-related cookies more thoroughly
    const cookiesToClear = ["access_token", "token_expiry", "refresh_token"];
    cookiesToClear.forEach(cookieName => {
      // Clear for current path
      document.cookie = `${cookieName}=; path=/; max-age=0; SameSite=Lax`;
      // Clear for root domain
      document.cookie = `${cookieName}=; path=/; max-age=0; SameSite=Lax; domain=${window.location.hostname}`;
      // Clear without domain
      document.cookie = `${cookieName}=; path=/; max-age=0; SameSite=Lax; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    });
  }

  /**
   * Checks if the user is authenticated
   * @returns Whether the user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  /**
   * Makes a GET request
   * @param url URL to request
   * @param config Request configuration
   * @returns Response data
   */
  public async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.get<T>(url, config);
    return response.data;
  }

  /**
   * Makes a POST request
   * @param url URL to request
   * @param data Request data
   * @param config Request configuration
   * @returns Response data
   */
  public async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.post<T>(url, data, config);
    return response.data;
  }


  /**
   * Makes a PUT request
   * @param url URL to request
   * @param data Request data
   * @param config Request configuration
   * @returns Response data
   */
  public async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.put<T>(url, data, config);
    return response.data;
  }

  /**
   * Makes a PATCH request
   * @param url URL to request
   * @param data Request data
   * @param config Request configuration
   * @returns Response data
   */
  public async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.patch<T>(url, data, config);
    return response.data;
  }

  /**
   * Makes a DELETE request
   * @param url URL to request
   * @param config Request configuration
   * @returns Response data
   */
  public async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.axiosInstance.delete<T>(url, config);
    return response.data;
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();
