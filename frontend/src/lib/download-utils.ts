/**
 * Download utilities for handling authenticated file downloads
 */

// Auth token storage keys (matching api-client.ts)
const ACCESS_TOKEN_KEY = "access_token";
const TOKEN_EXPIRY_KEY = "token_expiry";

/**
 * Gets the current access token, checking for expiry
 * @returns Access token or null if not found or expired
 */
const getValidAccessToken = (): string | null => {
  // Check if token is expired
  const expiryStr = localStorage.getItem(TOKEN_EXPIRY_KEY);

  if (expiryStr) {
    const expiry = parseInt(expiryStr, 10);

    if (Date.now() >= expiry) {
      // Token is expired
      return null;
    }
  }

  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

/**
 * Downloads a file from an authenticated endpoint
 * @param url The URL to download from
 * @param filename Optional filename for the download
 */
export const downloadAuthenticatedFile = async (
  url: string,
  filename?: string
): Promise<void> => {
  try {
    // Get the auth token from localStorage (using the correct key)
    const token = getValidAccessToken();
    if (!token) {
      throw new Error(
        "No valid authentication token found. Please log in again."
      );
    }

    // Fetch the file with authorization header
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to download file: ${response.status} ${response.statusText}`
      );
    }

    // Get the blob from the response
    const blob = await response.blob();

    // Get filename from Content-Disposition header or use provided filename
    let downloadFilename = filename;
    if (!downloadFilename) {
      const contentDisposition = response.headers.get("Content-Disposition");
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          downloadFilename = filenameMatch[1];
        }
      }
    }

    // Create a download link and trigger the download
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.download = downloadFilename || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error("Error downloading file:", error);
    throw error;
  }
};

/**
 * Downloads a Agreement Document
 * @param contractId The contract ID
 * @param filename Optional filename for the download
 */
export const downloadContractDocument = async (
  contractId: string,
  filename?: string
): Promise<void> => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000";
  const url = `${baseUrl}/api/contracts/${contractId}/download`;
  return downloadAuthenticatedFile(url, filename);
};
