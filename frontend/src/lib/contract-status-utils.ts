/**
 * Contract Status Utilities
 * Provides consistent status calculation logic across the application
 */

import { ContractStatus } from "@/services/contractService";

/**
 * Gets contract status from LLM extraction only
 * @param extractedStatus - Status extracted by LLM from contract document
 * @returns Contract status from LLM or "Unknown" if not available
 */
export function getContractStatus(
  extractedStatus?: string | null
): ContractStatus {
  // Only use LLM-extracted status if available and valid
  if (extractedStatus && typeof extractedStatus === "string") {
    const normalizedStatus = extractedStatus.trim();
    if (
      normalizedStatus === "Active" ||
      normalizedStatus === "Inactive" ||
      normalizedStatus === "Unknown"
    ) {
      return normalizedStatus as ContractStatus;
    }
  }

  // Return "Unknown" if LLM status is not available or invalid
  return "Unknown";
}

/**
 * Calculates contract status based on start and end dates (fallback method)
 * @param startDate - Contract start date (ISO string or Date)
 * @param endDate - Contract end date (ISO string or Date)
 * @returns Calculated contract status
 */
export function calculateContractStatus(
  startDate: string | Date | undefined | null,
  endDate: string | Date | undefined | null
): ContractStatus {
  // If either start date or end date is missing, status is unknown
  if (!startDate || !endDate) {
    return "Unknown";
  }

  // Handle string values that might be "N/A" or empty
  if (
    (typeof startDate === "string" &&
      (startDate === "N/A" || startDate.trim() === "")) ||
    (typeof endDate === "string" &&
      (endDate === "N/A" || endDate.trim() === ""))
  ) {
    return "Unknown";
  }

  try {
    const currentDate = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Check if dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return "Unknown";
    }

    // Set time to start of day for accurate date comparison
    currentDate.setHours(0, 0, 0, 0);
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    // Active: current date is between start and end date (inclusive)
    if (currentDate >= start && currentDate <= end) {
      return "Active";
    }

    // Inactive: current date is outside the contract period
    return "Inactive";
  } catch {
    return "Unknown";
  }
}

/**
 * Checks if a contract is currently active based on LLM extraction only
 * @param extractedStatus - Status extracted by LLM from contract document
 * @returns True if contract is active, false otherwise
 */
export function isContractActive(extractedStatus?: string | null): boolean {
  return getContractStatus(extractedStatus) === "Active";
}

/**
 * Checks if a contract is expired (end date has passed)
 * @param endDate - Contract end date
 * @returns True if contract is expired, false otherwise
 */
export function isContractExpired(
  endDate: string | Date | undefined | null
): boolean {
  if (!endDate) return false;

  try {
    const currentDate = new Date();
    const end = new Date(endDate);

    if (isNaN(end.getTime())) return false;

    // Set time to start of day for accurate date comparison
    currentDate.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    return currentDate > end;
  } catch {
    return false;
  }
}

/**
 * Checks if a contract is expiring soon (within specified days)
 * @param endDate - Contract end date
 * @param daysThreshold - Number of days to consider as "soon" (default: 30)
 * @returns True if contract is expiring soon, false otherwise
 */
export function isContractExpiringSoon(
  endDate: string | Date | undefined | null,
  daysThreshold: number = 30
): boolean {
  if (!endDate) return false;

  try {
    const currentDate = new Date();
    const end = new Date(endDate);

    if (isNaN(end.getTime())) return false;

    // Set time to start of day for accurate date comparison
    currentDate.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    // Calculate threshold date
    const thresholdDate = new Date(currentDate);
    thresholdDate.setDate(thresholdDate.getDate() + daysThreshold);

    // Contract is expiring soon if end date is within threshold and not yet expired
    return end <= thresholdDate && end > currentDate;
  } catch {
    return false;
  }
}

/**
 * Gets a human-readable status description
 * @param status - Contract status
 * @returns Human-readable description
 */
export function getStatusDescription(status: ContractStatus): string {
  switch (status) {
    case "Active":
      return "Contract is currently active and in effect";
    case "Inactive":
      return "Contract is not currently active (expired or not yet started)";
    case "Unknown":
      return "Contract status cannot be determined (missing dates)";
    default:
      return "Unknown status";
  }
}
