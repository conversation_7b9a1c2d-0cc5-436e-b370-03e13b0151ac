/**
 * Parses combined value format "CURRENCY:AMOUNT" or handles legacy formats
 */
export function parseContractValue(value: string | number | undefined): {
  currency: string;
  amount: number;
} {
  if (!value && value !== 0) {
    return { currency: "USD", amount: 0 };
  }

  const stringValue = typeof value === "string" ? value : String(value);

  // Check if it's the new combined format "CURRENCY:AMOUNT"
  if (stringValue.includes(":")) {
    const [currency, amount] = stringValue.split(":");
    const numValue = parseFloat(amount.replace(/[^0-9.-]/g, ""));
    return {
      currency: currency && /^[A-Z]{3}$/.test(currency) ? currency : "USD",
      amount: isNaN(numValue) ? 0 : numValue,
    };
  }

  // Legacy format: extract numeric value and assume USD
  const numValue = parseFloat(stringValue.replace(/[^0-9.-]/g, ""));
  return {
    currency: "USD",
    amount: isNaN(numValue) ? 0 : numValue,
  };
}

/**
 * Formats a currency value with proper symbol based on currency code
 */
export function formatCurrency(
  value: string | number | undefined,
  currency?: string
): string {
  // Check for null/undefined/empty values first
  if (!value && value !== 0) return "N/A";

  // Check for explicit "N/A" or "Not specified" values
  if (
    typeof value === "string" &&
    (value === "N/A" || value.toLowerCase() === "not specified")
  ) {
    return "N/A";
  }

  // Parse the value to get currency and amount
  const parsed = parseContractValue(value);

  // If the original value was null/undefined but parseContractValue returned amount: 0,
  // it means there was no meaningful value, so return N/A
  if (
    (!value && value !== 0) ||
    (parsed.amount === 0 && (!value || value === "0"))
  ) {
    return "N/A";
  }

  // Use provided currency or parsed currency
  const finalCurrency =
    currency && /^[A-Z]{3}$/.test(currency) && currency !== "N/A"
      ? currency
      : parsed.currency;

  try {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: finalCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(parsed.amount);
  } catch (error) {
    // Fallback if currency formatting fails
    return `${finalCurrency} ${parsed.amount.toLocaleString("en-US", {
      maximumFractionDigits: 0,
    })}`;
  }
}

/**
 * Formats a contract value for display with proper currency symbol
 */
export function formatContractValue(
  value: string | number | undefined,
  currency?: string,
  compact: boolean = false
): string {
  // Check for null/undefined/empty values first
  if (!value && value !== 0) return "N/A";

  // Check for explicit "N/A" or "Not specified" values
  if (
    typeof value === "string" &&
    (value === "N/A" || value.toLowerCase() === "not specified")
  ) {
    return "N/A";
  }

  // Parse the value to get currency and amount
  const parsed = parseContractValue(value);

  // If the original value was null/undefined but parseContractValue returned amount: 0,
  // it means there was no meaningful value, so return N/A
  if (
    (!value && value !== 0) ||
    (parsed.amount === 0 && (!value || value === "0"))
  ) {
    return "N/A";
  }

  // Use provided currency or parsed currency
  const finalCurrency =
    currency && /^[A-Z]{3}$/.test(currency) && currency !== "N/A"
      ? currency
      : parsed.currency;

  if (isNaN(parsed.amount)) return "N/A";

  // For compact display (like in cards/lists)
  if (compact) {
    try {
      const formatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: finalCurrency,
        notation: "compact",
        maximumFractionDigits: 0,
      });
      return formatter.format(parsed.amount);
    } catch (error) {
      // Fallback for compact display
      if (parsed.amount >= 1000000) {
        return `${finalCurrency} ${Math.round(parsed.amount / 1000000)}M`;
      } else if (parsed.amount >= 1000) {
        return `${finalCurrency} ${Math.round(parsed.amount / 1000)}K`;
      } else {
        return `${finalCurrency} ${parsed.amount.toLocaleString("en-US", {
          maximumFractionDigits: 0,
        })}`;
      }
    }
  }

  // For full display
  return formatCurrency(value, currency);
}

/**
 * Gets contract term from LLM extraction only
 * @param extractedTerm - Term extracted by LLM from contract document
 * @returns Contract term from LLM or "N/A" if not available
 */
export function getContractTerm(extractedTerm?: string | null): string {
  // Only use LLM-extracted term if available and meaningful
  if (extractedTerm && typeof extractedTerm === "string") {
    const normalizedTerm = extractedTerm.trim();
    if (
      normalizedTerm !== "N/A" &&
      normalizedTerm !== "" &&
      normalizedTerm !== "Not specified"
    ) {
      return normalizedTerm;
    }
  }

  // Return "N/A" if LLM term is not available or not meaningful
  return "N/A";
}

/**
 * Calculates and formats contract term duration between start and end dates (fallback method)
 */
export function formatContractTerm(
  startDate: string | null | undefined,
  endDate: string | null | undefined
): string {
  if (!startDate || !endDate) return "N/A";

  try {
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return "N/A";
    if (end <= start) return "N/A";

    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Calculate total months (including years converted to months)
    const totalMonths = Math.floor(diffDays / 30);

    if (totalMonths > 0) {
      return `${totalMonths} month${totalMonths > 1 ? "s" : ""}`;
    } else {
      return `${diffDays} day${diffDays > 1 ? "s" : ""}`;
    }
  } catch (error) {
    return "N/A";
  }
}

/**
 * Formats renewal notice period from days to human readable format
 */
export function formatNoticePeriod(
  noticePeriodDays: number | string | undefined,
  noticePeriodText?: string
): string {
  // If we have text from AI analysis, prefer that
  if (noticePeriodText && typeof noticePeriodText === "string") {
    return noticePeriodText;
  }

  if (!noticePeriodDays) return "N/A";

  const days =
    typeof noticePeriodDays === "string"
      ? parseInt(noticePeriodDays, 10)
      : noticePeriodDays;

  if (isNaN(days) || days <= 0) return "N/A";

  if (days >= 365) {
    const years = Math.floor(days / 365);
    return `${years} year${years > 1 ? "s" : ""}`;
  } else if (days >= 30) {
    const months = Math.floor(days / 30);
    return `${months} month${months > 1 ? "s" : ""}`;
  } else {
    return `${days} day${days > 1 ? "s" : ""}`;
  }
}

/**
 * Formats a date to relative time (e.g., "2 hours ago", "3 days ago")
 * or absolute format for older dates
 */
export function formatRelativeDate(
  dateString: string | undefined | null
): string {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "N/A";

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    // For very recent times
    if (diffSeconds < 60) {
      return "Just now";
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks} week${weeks !== 1 ? "s" : ""} ago`;
    } else {
      // For older dates, show absolute format
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  } catch (error) {
    return "N/A";
  }
}

/**
 * Formats a date to absolute format (e.g., "Jan 15, 2024")
 */
export function formatAbsoluteDate(
  dateString: string | undefined | null
): string {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "N/A";

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch (error) {
    return "N/A";
  }
}
