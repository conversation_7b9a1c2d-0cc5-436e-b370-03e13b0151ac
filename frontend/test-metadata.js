// Test script to verify metadata display functions
const { getMetadataValue, getDisplayValue, shouldDisplayField } = require('./src/utils/metadata-display.ts');

// Mock contract metadata structure similar to what the API returns
const mockContractMetadata = {
  id: "test-metadata-id",
  contractId: "test-contract-id",
  autoExtractedFields: {
    title: "Test Service Agreement",
    agreement_type: "MSA",
    contractValue: "USD:50000",
    currency: "USD",
    effectiveDate: "2024-01-01",
    endDate: "2024-12-31",
    parties: [
      {
        name: "Acme Corp",
        role: "provider"
      },
      {
        name: "Client Inc",
        role: "client"
      }
    ],
    paymentTerms: "Net 30 days",
    governingLaw: "New York State Law",
    disputeResolution: "Arbitration",
    rawResult: {
      contract_value: "50000",
      effective_date: "2024-01-01",
      end_date: "2024-12-31"
    }
  },
  confidence_scores: {
    title_score: 0.95,
    agreement_type_score: 0.88,
    contract_value_score: 0.92,
    effective_date_score: 0.85
  }
};

console.log("Testing metadata display functions...");

// Test getMetadataValue function
console.log("\n=== Testing getMetadataValue ===");
console.log("Title:", getMetadataValue(mockContractMetadata, "title"));
console.log("Agreement Type:", getMetadataValue(mockContractMetadata, "agreement_type"));
console.log("Contract Value:", getMetadataValue(mockContractMetadata, "contract_value"));
console.log("Provider:", getMetadataValue(mockContractMetadata, "provider"));
console.log("Client:", getMetadataValue(mockContractMetadata, "client"));
console.log("Effective Date:", getMetadataValue(mockContractMetadata, "effective_date"));

// Test getDisplayValue function
console.log("\n=== Testing getDisplayValue ===");
console.log("Title Display:", getDisplayValue(mockContractMetadata, "title"));
console.log("Agreement Type Display:", getDisplayValue(mockContractMetadata, "agreement_type"));
console.log("Contract Value Display:", getDisplayValue(mockContractMetadata, "contract_value"));
console.log("Provider Display:", getDisplayValue(mockContractMetadata, "provider"));
console.log("Client Display:", getDisplayValue(mockContractMetadata, "client"));

// Test shouldDisplayField function
console.log("\n=== Testing shouldDisplayField ===");
console.log("Should display title:", shouldDisplayField(mockContractMetadata, "title"));
console.log("Should display agreement_type:", shouldDisplayField(mockContractMetadata, "agreement_type"));
console.log("Should display contract_value:", shouldDisplayField(mockContractMetadata, "contract_value"));
console.log("Should display provider:", shouldDisplayField(mockContractMetadata, "provider"));
console.log("Should display non_existent_field:", shouldDisplayField(mockContractMetadata, "non_existent_field"));

console.log("\nTest completed!");
