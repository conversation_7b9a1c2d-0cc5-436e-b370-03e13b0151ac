# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app

# Accept build arguments
ARG NEXT_PUBLIC_API_URL
ARG NODE_ENV=production

# Set environment variables
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL

# Copy package files first for better caching
COPY package.json ./
RUN npm install

# Copy configuration files first
COPY postcss.config.js tailwind.config.js tsconfig.json next.config.js components.json next-env.d.ts ./

# Copy the rest of the application code
COPY . .

# Build the app
RUN npm run build

# Expose the port the app will run on
EXPOSE 3000

# Start the production server
CMD ["npm", "start"]
