# Aptio - AI-Native Contract and License Management Platform

Aptio is a comprehensive B2B2B SaaS platform for contract and license lifecycle management, featuring advanced AI capabilities throughout the entire workflow. Our platform transforms how organizations create, manage, analyze, and optimize their contracts and software licenses.

## 🔍 Overview

Aptio helps businesses of all sizes streamline their contract and license management processes with intelligent automation, powerful analytics, and secure multi-tenant architecture. By combining cutting-edge AI with domain expertise, <PERSON>pt<PERSON> reduces risks, cuts costs, and improves compliance across your organization.

### Who It's For

- **Legal Teams**: Streamline contract creation, review, and approval processes
- **IT Departments**: Optimize software license usage and ensure compliance
- **Finance Teams**: Track contract costs and identify savings opportunities
- **Procurement**: Manage vendor relationships and contract renewals
- **Executives**: Gain insights into contractual obligations and risks

## 💼 Business Value

Aptio delivers significant business value across multiple dimensions:

- **Risk Reduction**: Identify contractual risks early and ensure compliance with obligations
- **Cost Savings**: Optimize license usage and avoid unnecessary purchases or renewals
- **Time Efficiency**: Automate manual processes and reduce time spent on contract management
- **Improved Compliance**: Stay compliant with regulatory requirements and contractual obligations
- **Better Decision Making**: Gain insights from AI-powered analytics to make informed decisions
- **Scalability**: Handle growing contract and license volumes without proportional resource increases

## 🌟 Features

### Core Features

- **Contract Management**: Create, store, track, and manage contracts throughout their lifecycle
- **License Management**: Track, optimize, and ensure compliance for software and other licenses
- **Multi-Tenant Architecture**: Securely manage multiple organizations with proper isolation
- **White-Labeling**: Customizable branding and theming for different tenants
- **Audit & Compliance**: Track all contract and license activities for compliance and auditing

### AI-Native Capabilities

- **AI-Powered Document Processing**: Automatic classification, information extraction, and analysis
- **Intelligent Agreement Analysis**: Identify key terms, obligations, risks, and generate summaries
- **License Optimization**: AI-driven recommendations for license optimization and cost savings
- **RAG and Chat**: Document-aware chat interface and semantic search across all documents
- **AI Analytics**: Automated insights, trend detection, and business intelligence
- **Personalized Onboarding**: AI-guided user onboarding and contextual assistance

## 🏗️ Architecture

This project follows a modular monolith architecture with Domain-Driven Design (DDD) principles, designed for scalability and future microservices extraction:

- **Frontend**: Next.js, React, TailwindCSS, Tanstack Query, Auth.js
- **Backend**: Express.js, Prisma ORM, PostgreSQL with pgvector, Redis, BullMQ
- **AI Components**: OpenAI, LangChain, Vector embeddings, RAG pipeline
- **Infrastructure**: Docker, Docker Compose, Nginx, Prometheus, Grafana

The platform consists of the following containerized components:

- **Backend API**: Node.js application with Express
- **Frontend**: Next.js application
- **Database**: PostgreSQL with pgvector extension for vector embeddings
- **Cache**: Redis for caching and message broker
- **Reverse Proxy**: Nginx for routing and SSL termination
- **Monitoring**: Prometheus and Grafana (optional)

## 📂 Project Structure

```
.
├── backend/                 # Express.js backend
│   ├── src/
│   │   ├── api/             # API endpoints (REST, GraphQL, Webhooks)
│   │   ├── core/            # Domain entities and interfaces
│   │   ├── infrastructure/  # Cross-cutting concerns
│   │   └── modules/         # Bounded contexts implementations
│   ├── prisma/              # Database schema and migrations
│   └── tests/               # Backend tests
├── frontend/                # Next.js frontend
│   ├── public/              # Static assets
│   └── src/
│       ├── app/             # Next.js App Router
│       ├── components/      # UI components
│       ├── lib/             # Utilities and helpers
│       └── styles/          # Global styles
├── docker-compose.yml       # Docker compose configuration
└── Dockerfile               # Multi-stage Docker build
```

## 🚀 Getting Started

### Prerequisites

- Docker and Docker Compose
- 4+ CPU cores
- 8+ GB RAM
- 20+ GB disk space
- OpenAI API key

### Development Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/aptio.git
   cd aptio
   ```

2. Set up environment variables:

   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your configuration, including:

   - `OPENAI_API_KEY`: Your OpenAI API key
   - `JWT_SECRET` and `JWT_REFRESH_SECRET`: Secure random strings for JWT authentication
   - `ENCRYPTION_KEY`: 32-byte hex string for data encryption

3. Start the development environment:

   ```bash
   # Make the deployment script executable
   chmod +x deploy.sh

   # Deploy with development profile
   NODE_ENV=development ./deploy.sh
   ```

4. The application will be available at:
   - Frontend: http://localhost:3001
   - Backend API: http://localhost:3000
   - PgAdmin: http://localhost:5050 (username: <EMAIL>, password: admin)

### Production Deployment

1. Configure environment variables for production:

   ```bash
   # Edit .env file
   NODE_ENV=production
   ```

2. Deploy the platform:

   ```bash
   ./deploy.sh
   ```

3. To stop and clean up:

   ```bash
   # Stop containers
   ./cleanup.sh

   # Remove volumes (WARNING: This will delete all data)
   ./cleanup.sh --volumes

   # Remove everything including Docker images
   ./cleanup.sh --all
   ```

## 📚 Documentation

Our comprehensive documentation is organized into user and developer sections:

### User Documentation

- [User Guide](docs/user/README.md) - Complete user documentation
- [Getting Started](docs/user/getting-started.md) - Guide for new users
- [Contract Management](docs/user/contract-management.md) - Managing contracts
- [License Management](docs/user/license-management.md) - Managing licenses
- [AI Features](docs/user/ai-features.md) - Using AI-powered features

### Developer Documentation

- [Developer Guide](docs/developer/README.md) - Complete developer documentation
- [Architecture Overview](docs/developer/architecture.md) - System architecture
- [Development Setup](docs/developer/setup.md) - Setting up the development environment
- [Database Design](docs/developer/database.md) - Database schema and design
- [AI Integration](docs/developer/ai-integration.md) - AI implementation details
- [Security Implementation](docs/developer/security.md) - Security architecture
- [Deployment Guide](docs/developer/deployment.md) - Deployment instructions

### Project Planning

- [Implementation Plan](plan.md) - Detailed implementation plan with frontend and backend tasks
- [Delivery Timeline](docs/delivery-timeline.md) - 16-week delivery timeline
- [Feature Roadmap](docs/feature-roadmap.md) - Feature dependencies and milestones
- [Weekly Sprint Plan](docs/weekly-sprint-plan.md) - Detailed weekly sprint planning
- [Critical Analysis](docs/critical-analysis.md) - Analysis and recommendations

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests
npm run test:backend

# Run frontend tests
npm run test:frontend
```

## 🛠️ Deployment

Aptio can be deployed using Docker for both development and production environments. For detailed deployment instructions, see the [Deployment Guide](docs/developer/deployment.md).

## 🤝 Customer Success & Support

At Aptio, we're committed to your success:

- **Dedicated Support**: Our support team is available to help you with any questions or issues
- **Implementation Services**: Professional services to ensure successful implementation
- **Training**: Comprehensive training programs for administrators and end users
- **Regular Updates**: Continuous improvements and new features based on customer feedback
- **Community**: Join our community of users to share best practices and insights

For support, contact <NAME_EMAIL> or visit our [Support Portal](https://support.aptio.com).

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
