---
description: Repository Information Overview
alwaysApply: true
---

# Aptio Information

## Summary
Aptio is a comprehensive B2B2B SaaS platform for contract and license lifecycle management with advanced AI capabilities. The platform helps businesses streamline contract and license management processes with intelligent automation, analytics, and a secure multi-tenant architecture.

## Structure
- **backend/**: Express.js backend with API endpoints, domain entities, and infrastructure
- **frontend/**: Next.js frontend application with React components and app router
- **nginx/**: Configuration for reverse proxy and SSL termination
- **prometheus/**: Monitoring configuration
- **docs/**: Comprehensive documentation for users and developers
- **.zencoder/**: Configuration for code generation tools

## Language & Runtime
**Languages**: TypeScript, JavaScript
**Node Version**: ^18.x (inferred from dependencies)
**Frontend Framework**: Next.js 15.3.1
**Backend Framework**: Express.js 5.1.0
**Build System**: npm/pnpm scripts
**Package Manager**: npm/pnpm

## Dependencies

### Backend Dependencies
**Main Dependencies**:
- Express.js 5.1.0 - Web framework
- Prisma ORM 6.6.0 - Database ORM
- OpenAI 4.96.0 - AI integration
- LangChain 0.3.24 - AI framework
- PostgreSQL with pgvector - Vector database
- BullMQ 5.51.1 - Job queue
- Redis - Caching and message broker

**Development Dependencies**:
- TypeScript 5.8.3
- Jest 29.7.0 - Testing framework
- ts-node-dev - Development server

### Frontend Dependencies
**Main Dependencies**:
- Next.js 15.3.1 - React framework
- React 18.2.0
- TailwindCSS 3.4.17 - CSS framework
- Radix UI - UI component library
- Tanstack Query 5.74.7 - Data fetching
- Next-Auth 5.0.0-beta.5 - Authentication

**Development Dependencies**:
- Jest 29.7.0 - Testing framework
- Testing Library - Testing utilities
- ESLint 9.25.1 - Linting

## Build & Installation
```bash
# Install dependencies
npm run setup

# Development mode
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run tests
npm test
```

## Docker
**Docker Compose**: docker-compose.yml
**Images**:
- Backend: multistrat-backend:latest
- Frontend: multistrat-frontend:latest
- Database: ankane/pgvector:latest

**Configuration**:
- Backend runs on port 5000
- Frontend runs on port 3002
- PostgreSQL with pgvector extension for vector embeddings
- Redis for caching and message broker

## Testing
**Framework**: Jest
**Test Location**: 
- Backend: backend/tests/
- Frontend: frontend/src/tests/

**Run Command**:
```bash
# Run all tests
npm test

# Run backend tests
npm run test:backend

# Run frontend tests
npm run test:frontend
```

## Database
**Type**: PostgreSQL with pgvector extension
**ORM**: Prisma 6.6.0
**Schema**: backend/prisma/schema.prisma
**Models**: User, Tenant, Contract, License, and more

## AI Integration
**Libraries**: OpenAI, LangChain, AI SDK
**Features**: 
- Document processing
- Agreement analysis
- License optimization
- RAG and semantic search