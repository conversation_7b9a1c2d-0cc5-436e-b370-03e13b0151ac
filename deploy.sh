#!/bin/bash
# Deployment script for Aptio platform

# Exit on error
set -e

# Load environment variables
if [ -f .env ]; then
  export $(cat .env | grep -v '^#' | xargs)
else
  echo "Error: .env file not found"
  echo "Please create a .env file based on .env.example"
  exit 1
fi

# Check required environment variables
required_vars=(
  "NODE_ENV"
  "POSTGRES_PASSWORD"
  "JWT_SECRET"
  "JWT_REFRESH_SECRET"
  "ENCRYPTION_KEY"
  "OPENAI_API_KEY"
)

for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "Error: Required environment variable $var is not set"
    exit 1
  fi
done

# Create required directories
mkdir -p nginx/ssl
mkdir -p nginx/www
mkdir -p db/migrations
mkdir -p prometheus
mkdir -p grafana/provisioning

# Generate self-signed SSL certificate for development
if [ ! -f nginx/ssl/server.crt ] || [ ! -f nginx/ssl/server.key ]; then
  echo "Generating self-signed SSL certificate..."
  openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/server.key -out nginx/ssl/server.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
fi

# Build and start the services
echo "Building and starting services..."

# Determine deployment mode
if [ "$NODE_ENV" = "production" ]; then
  echo "Deploying in production mode..."
  docker-compose build
  docker-compose up -d
else
  echo "Deploying in development mode..."
  docker-compose build
  docker-compose up -d --profile dev
fi

# Wait for database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "Running database migrations..."
docker-compose exec backend npx prisma migrate deploy

# Generate Prisma client
echo "Generating Prisma client..."
docker-compose exec backend npx prisma generate

# Seed the database if needed
if [ "$SEED_DATABASE" = "true" ]; then
  echo "Seeding the database..."
  docker-compose exec backend npm run seed
fi

echo "Deployment completed successfully!"
echo "Backend API: http://localhost:3000"
echo "Frontend: http://localhost:3001"
echo "PgAdmin: http://localhost:5050"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000"

# Show running containers
docker-compose ps
