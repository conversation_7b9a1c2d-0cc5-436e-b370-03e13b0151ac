# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
.next
out
.output
.nuxt

# Testing
coverage

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment variables
.env
.env.local
.env.docker
.env.development.local
.env.test.local
.env.production.local

# Cache
.vercel
.turbo
.eslintcache
.vite
.cache
.parcel-cache

# Turborepo
.turbo

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs
*.log

# Database
*.sqlite
*.db

# Uploads/generated files in development
uploads
public/uploads