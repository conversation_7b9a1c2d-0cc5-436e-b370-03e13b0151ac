#!/bin/bash
# Cleanup script for Aptio platform

# Exit on error
set -e

# Show help message
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "Usage: ./cleanup.sh [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  --volumes    Remove volumes (WARNING: This will delete all data)"
  echo "  --images     Remove Docker images"
  echo "  --all        Remove everything (volumes and images)"
  echo "  --help, -h   Show this help message"
  exit 0
fi

# Stop containers
echo "Stopping containers..."
docker-compose down

# Remove volumes if requested
if [ "$1" = "--volumes" ] || [ "$1" = "--all" ]; then
  echo "Removing volumes..."
  docker-compose down -v
fi

# Remove images if requested
if [ "$1" = "--images" ] || [ "$1" = "--all" ]; then
  echo "Removing images..."
  docker rmi aptio-backend:latest aptio-frontend:latest
fi

echo "Cleanup completed!"
